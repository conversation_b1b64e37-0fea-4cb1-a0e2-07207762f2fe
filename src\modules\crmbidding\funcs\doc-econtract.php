<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2017 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Thu, 02 Feb 2017 08:48:59 GMT
 */

if (!defined('NV_IS_MOD_CRMBIDDING')) {
    die('Stop!!!');
}

if (!defined('NV_IS_USER')) {
    $redirect = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name, true);
    Header("Location: " . NV_BASE_SITEURL . "index.php?" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($redirect));
    die();
}
if (!isset($array_op[1]) || !is_numeric($array_op[1])) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}

/**
 * Lấy dữ liệu từ version mới nhất hoặc dữ liệu version được chọn
 * /{id-contract}/{version} - Xem hợp đồng theo version
 * /{id-contract}/preview - Xem trước hợp đồng
 */
$type = '';
if (isset($array_op[2]) && (is_numeric($array_op[2]) || $array_op[2] == 'preview')) {
    if ($array_op[2] == 'preview') {
        $cache_file = 'econtract_' . $array_op[1] . '_' . NV_LANG_DATA . '.cache';
        if (($cache_data = $nv_Cache->getItem($module_name, $cache_file)) != false) {
            $type = 'preview';
            $data_array = json_decode($cache_data, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $row = $data_array;
                if (empty($row['id'])) {
                    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
                }
                $check_exisit_econtract = $db->query('SELECT id, status FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id = ' . $row['id'] . ' AND customer_id = ' . $user_info['userid'])->fetch();
                if (empty($check_exisit_econtract)) {
                    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
                }
                $row['status'] = $check_exisit_econtract['status'];
            }
        }
    } else {
        $version_row = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions WHERE econtract_id=' . $array_op[1] . ' AND id=' . $array_op[2])->fetch();
        $row = empty($version_row) && empty($version_row['contract_data']) ? [] : json_decode($version_row['contract_data'], true);
        if (isset($row['id']) && $row['id'] > 0) {
            $check_econtract = $db->query('SELECT id, status FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $row['id']  . ' AND customer_id=' . $user_info['userid'])->fetch();
            if (empty($check_econtract)) {
                nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
            }
            $row['status'] = $check_econtract['status'];
        } else {
            nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
        }
    }
} else {
    $row = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $array_op[1]  . ' AND customer_id=' . $user_info['userid'])->fetch();
}
if (empty($row)) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}
$row['contract_data'] = !empty($row['contract_data']) ? json_decode($row['contract_data'], true) : [];

if ($array_op[2] == 'prevew' && isset($_GET['save']) && $_GET['save'] == 1) {
    $changed_data = $setParts = [];
    // Lấy thông tin từ file cache
    $cache_file = 'econtract_' . $row['id'] . '_' . NV_LANG_DATA . '.cache';
    $cache_data = $nv_Cache->getItem($module_name, $cache_file);
    if ($cache_data == false) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['update-econtract'] . '/' . $row['id']);
    }
    $row_new = json_decode($cache_data, true);
    // Kiểm thông tin
    $error = validate_econtract_row($row_new);

    $row_old = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $row_new['id'] . ' AND customer_id=' . $user_info['userid'])->fetch();
    if (empty($row_old) || !empty($error)) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['update-econtract'] . '/' . $row['id']);
    }

    if (empty($error)) {
        $query = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET ';

        $fieldsToUpdate = ['c_name', 'tax_code', 'representative', 'jobtitle', 'phone', 'email', 'c_address', 'receiver', 'receiver_phone', 'receiver_address', 'customer_type', 'authorization_letter', 'receiver', 'receiver_phone', 'receiver_address', 'cccd'];
        foreach ($fieldsToUpdate as $field) {
            $newValue = $row_new[$field];
            if ($newValue != $row_old[$field]) {
                $setParts[] = $field . '=' . ($field == 'customer_type' ? $newValue : $db->quote($newValue));
                $changed_data[$field] = [
                    'old' => $row_old[$field],
                    'new' => $newValue
                ];
            }
        }
        if (!empty($setParts)) {
            $setParts[] = 'status=' . EContractStatus::Incomplete->value;
            $setParts[] = 'stage=' . EContractStage::Negotiating->value;
            $setParts[] = 'stage_next=' . EContractStage::HSTDTSignatureRequired->value;
            $setParts[] = 'customer_signed=0'; // Khách chưa ký
            $setParts[] = 'hstdt_signed=0'; // HSTDT chưa ký

            $query .= implode(',', $setParts) . ' WHERE id=' . $row_new['id'];

            $exc = $db->prepare($query)->execute();
            if ($exc) {
                // Xử lý thay đổi version
                $max_code_ver = $db->query('SELECT MAX(version) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions WHERE econtract_id=' . $row_old['id'])->fetchColumn() ?: 0;
                $new_version = $max_code_ver + 1;
                // Kiểm tra contract_data
                if (empty($row['contract_data'])) {
                    $row['contract_data'] = json_encode([
                        'data_vip' => $data_vip,
                        'content' => '',
                        'total_service' => $data_total['total_service'],
                        'promotion' => $data_total['promotion'],
                        'total_payment' => $data_total['total_payment'],
                    ], JSON_UNESCAPED_UNICODE);
                }
                $new_version_data = [
                    'id' => $row_old['id'],
                    'current_version' => $row_old['current_version'],
                    'tax_code' => $row_new['tax_code'],
                    'c_name' => $row_new['c_name'],
                    'representative' => $row_new['representative'],
                    'jobtitle' => $row_new['jobtitle'],
                    'c_address' => $row_new['c_address'],
                    'phone' => $row_new['phone'],
                    'email' => $row_new['email'],
                    'authorization_letter' => $row_new['authorization_letter'],
                    'receiver' => $row_new['receiver'],
                    'receiver_phone' => $row_new['receiver_phone'],
                    'receiver_address' => $row_new['receiver_address'],
                    'cccd' => $row_new['cccd'],
                    'customer_type' => $row_new['customer_type'],
                    'contract_no' => $row_old['contract_no'],
                    'contract_path' => '',
                    'status' => $row_old['status'],
                    'term_changed' => $row_old['term_changed'],
                    'term_changed_notes' => $row_old['term_changed_notes'],
                    'stage' => $row_old['stage'],
                    'stage_next' => $row_old['stage_next'],
                    'uploader_id' => $row_old['uploader_id'],
                    'customer_id' => $row_old['customer_id'],
                    'created_at' => $row_old['created_at'],
                    'updated_at' => NV_CURRENTTIME,
                    'contract_data' => $row_old['contract_data'],
                ];
                // Cập nhật version
                create_version_econtract([
                    'econtract_id' => $row_new['id'],
                    'user_id' => $user_info['userid'],
                    'version' => $new_version,
                    'pdf_path' => $row_old['contract_path'],
                    'contract_data' => json_encode($new_version_data),
                ]);
                // Lưu log thay đổi
                create_log_econtract([
                    'econtract_id' => $row_new['id'],
                    'version_id' => $new_version,
                    'action' => 1,
                    'user_id' => $user_info['userid'],
                    'action_desc_vi' => '<strong>Khách hàng</strong> cập nhật thông tin hợp đồng',
                    'action_desc_en' => '<strong>Customers</strong> update contract information',
                    'changed_data' => json_encode($changed_data, JSON_UNESCAPED_UNICODE),
                    'log_visible' => 1,
                    'created_at' => NV_CURRENTTIME
                ]);
                nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['doc-econtract'] . '/' . $row['id']);
            }
        }
    }
    // Sau khi lưu thông tin thì sẽ xóa file cache-preview
    $nv_Cache->delItem($module_name, $cache_file);
}

// Lấy thông tin đơn hàng
$econtract_order = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id=' . $row['id'])->fetchAll();
if (empty($econtract_order)) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}

$list_order = implode(', ', array_map(function ($order) {
    return sprintf('BDH%010s', $order['order_id']);
}, $econtract_order));

// #3140 Fix lỗi nội dung đơn hàng
$order_ids = array_column($econtract_order, 'order_id');
if (!empty($order_ids)) {
    $where = [];
    $where['AND'] = [
        ['IN' => ['id' => '(' . implode(',', $order_ids) . ')']]
    ];
    $api_dtinfo = new NukeViet\Api\DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
    $api_dtinfo->setModule('bidding')
        ->setLang('vi')
        ->setAction('ListBiddingOrder')
        ->setData([
            'where' => $where,
            'show_customs_log' => 1
        ]);
    $result = $api_dtinfo->execute();
    $error = $api_dtinfo->getError();
    if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
        $data_vip = [];
        foreach ($result['data'] as $_order) {
            $_total += $_order['money'];
            $_discount += $_order['discount'];
            $_total_end += $_order['total'];

            if (!empty($_order['customs_log'])) {
                foreach ($_order['customs_log'] as $custom_vip) {
                    $data_vip[] = [$custom_vip['vip_name'], $custom_vip['numbers_year']];
                    $custom_vip['title'] = $global_arr_vip[$custom_vip['vip']] . (($custom_vip['prefix_lang'] ?? 0) == 0 ? ' (Tiếng Việt)' : ' (Tiếng Anh)');
                    $custom_vip['vip_price_format'] = nv_currency_format($custom_vip['vip_price'] * $custom_vip['numbers_year']);
                    $custom_vips[] = $custom_vip;
                }
            }
        }

        $row['contract_data'] = [
            'data_vip' => convert_data_vip($data_vip),
            'content' => '',
            'total_service' => $_total,
            'promotion' => $_discount,
            'total_payment' => $_total_end,
        ];
    }
}

$page_title = $nv_Lang->getModule('doc_econtract');
$key_words = $module_info['keywords'];
$array_mod_title[] = array(
    'title' => $nv_Lang->getModule('doc_econtract'),
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op, true)
);
$contract_data =  $row['contract_data'];

$array_data = [
    'id' => $row['id'],
    'contract_no' => $row['contract_no'],
    'c_name' => mb_strtoupper($row['c_name'], 'UTF-8'),
    'representative' => $row['representative'], // Người đại diện
    'jobtitle' => $row['jobtitle'], // Chức vụ
    'authorization_letter' => empty($row['authorization_letter']) ? '' : $nv_Lang->getModule('customer_authorization_letter', $row['authorization_letter']),
    'c_address' => $row['c_address'],
    'tax_code' => $row['tax_code'],
    'cccd' => $row['cccd'],
    'vip' =>  $contract_data['data_vip']['vip'] ?? '',
    'phone' => $row['phone'],
    'email' => $row['email'],
    'money' => empty($contract_data['total_payment']) ? '0 VNĐ' : str_replace('đ', ' VNĐ', nv_currency_format($contract_data['total_payment'])),
    'money_text' => numberToWords($contract_data['total_payment'] ?? 0),
    'content' => $list_order,
    'contract_time' => $contract_data['data_vip']['year'] ?? '',
    'detail_contract_time' => $nv_Lang->getModule('detail_contract_time', nv_date('dd/mm/yyy', $contract_data['data_vip'][1]['from_time'] ?? ''), nv_date('dd/mm/yyy', $contract_data['data_vip'][1]['to_time'] ?? '')),
    'create_time' => $nv_Lang->getModule('create_time', date('d'), date('m'), date('Y')),
    'customer_type' => $row['customer_type'],
    'contract_path' => $row['contract_path'],
    'status' => $row['status'],
    'type_econtract' => $row['type_econtract'],
    'bank_account' => $row['bank_account']
];

$array_data_sell = [
    'represent' => 'Phạm Đức Tiến',
    'position' => 'Giám đốc',
    'address' => 'Tầng 6, tòa nhà hỗn hợp Sông Đà, Số 131 Đường Trần Phú, Phường Văn Quán, Quận Hà Đông, Thành phố Hà Nội, Việt Nam.',
    'phone' => '024.8888.4288',
    'hotline' => '0904.634.288',
    'tax_code' => '**********',
    'account_no' => '**********',
    'in' => 'Ngân hàng TMCP Ngoại Thương Việt Nam - Chi nhánh VCB Tây HN - PGD Nam Thanh Xuân.',
    'email' => '<EMAIL>',
    'website' => 'www.dauthau.asia'
];

// Danh sách link
$array_data_link['link_term_conditions'] = '<a href="https://dauthau.asia/siteterms/terms-and-conditions.html">https://dauthau.asia/siteterms/terms-and-conditions.html</a>';
$array_data_link['link_service'] = '<a href="https://dauthau.asia/siteterms/service-commitment.html">https://dauthau.asia/siteterms/service-commitment.html</a>';
$array_data_link['link_privacy'] = '<a href="https://dauthau.asia/siteterms/privacy.html">https://dauthau.asia/siteterms/privacy.html</a>';
$array_data_link['link_copyright'] = '<a href="https://dauthau.asia/siteterms/copyright.html">https://dauthau.asia/siteterms/copyright.html</a>';
$array_data_link['link_siteterms'] = '<a href="https://dauthau.asia/siteterms/yeu-cau-he-thong-cua-phan-mem-dauthau-info.html">https://dauthau.asia/siteterms/yeu-cau-he-thong-cua-phan-mem-dauthau-info.html</a>';
$array_data_link['link_support'] = '<a href="https://support.dauthau.net/vi/supportticket/add/">https://support.dauthau.net/vi/supportticket/add/</a>';
$array_data_link['link_cloudflare'] = '<a href="https://developers.cloudflare.com/ssl/ssl-tls/browser-compatibility">https://developers.cloudflare.com/ssl/ssl-tls/browser-compatibility</a>';
$array_data_link['link_huong_dan_truy_cap'] = '<a href="https://dauthau.asia/news/tin-tuc/huong-dan-khach-vip-truy-cap-dauthau-info-tu-dia-chi-ip-nuoc-ngoai-863.html">tại đây</a>';
$array_data_link['link_nukeviet'] = '<a href="https://www.nukeviet.vn/">www.nukeviet.vn</a>';
$array_data_link['link_msc'] = '<a href="http://muasamcong.mpi.gov.vn">http://muasamcong.mpi.gov.vn</a>';
$array_data_link['link_tra_cuu'] = '<a href="http://tracuunnt.gdt.gov.vn">http://tracuunnt.gdt.gov.vn</a>';
$array_data_link['link_bang_gia'] = '<a href="https://dauthau.asia/page/bang-gia.html">https://dauthau.asia/page/bang-gia.html</a>';
$array_data_link['link_bo_luat_dan_su'] = '<a href="https://thuvienphapluat.vn/van-ban/Quyen-dan-su/Bo-luat-dan-su-2015-296215.aspx">Bộ luật dân sự</a>';
$array_data_link['link_dang_ky_kinh_doanh'] = '<a href="www.dangkykinhdoanh.gov.vn">www.dangkykinhdoanh.gov.vn</a>';
$array_data_link['link_yc_khong_su_dung_phan_mem'] = '<a href="https://dauthau.asia/news/blog/dauthau-info-ngung-quet-du-lieu-366.html">yêu cầu từ Bộ KH&ĐT về việc không được sử dụng phần mềm để tiếp cận</a>';
$array_data_link['link_info_tiet_lo_bi_mat_cong_nghe'] = '<a href="https://dauthau.asia/news/tu-lieu-cho-nha-thau/dauthau-info-tiet-lo-bi-mat-cong-nghe-nhap-lieu-tu-dong-498.html">truy cập thủ công trước khi được xử lý tự động</a>';
// Danh sách liên hệ
$array_contact = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
];
$contents = nv_theme_doc_econtract($array_data, $array_data_sell, $array_data_link, $array_contact, $type);

if (isset($_GET['pdf']) && $_GET['pdf'] == 1) {
    if (!empty($row['contract_path'])) {
        $file_path = NV_ROOTDIR . '/' . $row['contract_path'];
        if (file_exists($file_path)) {
            header('Content-Description: File Transfer');
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . basename($file_path) . '"');
            header('Expires: 0');
            header('Cache-Control: must-revalidate');
            header('Pragma: public');
            header('Content-Length: ' . filesize($file_path));
            flush(); // Flush system output buffer
            readfile($file_path);
            header('Location: ' . $_SERVER['HTTP_REFERER']);
            exit;
        }
    } else {
        require_once NV_ROOTDIR . '/vendor/autoload.php';
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_top' => 20,
            'margin_bottom' => 20,
            'default_font' => 'times',  // Thay đổi font mặc định thành Times New Roman
            'default_font_size' => 13,  // Kích thước font mặc định
            'debug' => true
        ]);
        // Chỉ lấy phần nội dung trong contract-wrapper
        $dom = new DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($contents, 'HTML-ENTITIES', 'UTF-8'), LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $xpath = new DOMXPath($dom);
        $contractWrapper = $xpath->query("//div[contains(@class, 'contract-wrapper')]");
        if ($contractWrapper->length > 0) {
            $contents = $dom->saveHTML($contractWrapper->item(0));
        }
        // CSS cho PDF
        $stylesheet = '
            h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            .contract-terms {
                margin: 0;
                font-size: 16px;
            }
            p {
                font-size: 16px;
                margin-top: 5px;
                margin-bottom: 5px;
            }
            ul {
                margin-top: 0;
                padding-top: 0;
            }
            .contract-wrapper {
                padding: 20px;
                font-size: 14px;
                line-height: 1.5;
            }
            .header {
                margin-bottom: 10px;
            }
            .header h4 {
                font-weight: 400;
                font-size: 16px;
            }

            .contract-title {
                margin-bottom: 5px;
            }
            .contract-title h4 {
                font-weight: 400;
                font-size: 16px;
            }
            .contract-basis ul {
                font-style: italic;
                list-style-type: disc;
                padding-left: 20px;
            }
            .seller h5 {
                margin-top: 15px;
            }
            .contract-terms {
                margin-top: 15px;
            }
            .contract-parties {
                margin: 0;
            }
            .signature-space {
                height: 100px;
            }
            .contract-parties .table td {
                font-size: 16px;
                padding: 1px 4px;
                border: none;
                vertical-align: top;
            }
            .underline {
                border-bottom: 1px solid black;
            }
            .contract-wrapper {
                max-height: calc(100vh - 190px);
                overflow-y: auto;
            }
            .contract-wrapper .text-highlight {
                color: black;
            }
            .text-center {
                text-align: center;
            }
            .term {
                text-align: justify;
            }
        ';
        // Thêm CSS
        $mpdf->WriteHTML($stylesheet, \Mpdf\HTMLParserMode::HEADER_CSS);

        // Thiết lập số trang ở footer
        $mpdf->defaultfooterline = false;
        $mpdf->AddPage('', '', '', '', 'off');
        $footer = '<div style="text-align: right; font-size: 12pt; font-family: \'Times New Roman\', sans-serif; color: grey;">
            {PAGENO}
           </div>';
        $mpdf->setFooter($footer);

        // Thêm nội dung
        $mpdf->WriteHTML($contents);
        // Xuất file
        $filename = $array_data['contract_no'] . '.pdf';
        $mpdf->Output($filename, 'D');
        exit();
    }
}

include NV_ROOTDIR . '/includes/header.php';
echo $contents;
include NV_ROOTDIR . '/includes/footer.php';
