<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */
if (!defined('NV_IS_FILE_ADMIN')) die('Stop!!!');

$error = '';
$table_bank_info = $db_config['prefix'] . '_' . $module_name . '_bank_info';
if ($nv_Request->isset_request('id', 'GET')) {
    $is_update = true;
    $id_bank = $nv_Request->get_int('id', 'GET', 0);
    if ($id_bank == 0) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    }
} else {
    $is_update = false;
}
// Xử lý khi thêm mới tài khoản / cập nhật
if ($nv_Request->isset_request('submit', 'POST')) {
    $array_insert_bank_info = array();
    $array_insert_bank_info['title_vi'] = $nv_Request->get_title('title_vi', 'post', '');
    $array_insert_bank_info['title_en'] = $nv_Request->get_title('title_en', 'post', '');
    $array_insert_bank_info['account_holder_vi'] = $nv_Request->get_title('account_holder_vi', 'post', '');
    $array_insert_bank_info['account_holder_en'] = $nv_Request->get_title('account_holder_en', 'post', '');
    $array_insert_bank_info['account_number'] = $nv_Request->get_title('account_number', 'post', '');
    $array_insert_bank_info['bank_name_vi'] = $nv_Request->get_title('bank_name_vi', 'post', '');
    $array_insert_bank_info['bank_name_en'] = $nv_Request->get_title('bank_name_en', 'post', '');
    $array_insert_bank_info['bank_shortname_vi'] = $nv_Request->get_title('bank_shortname_vi', 'post', '');
    $array_insert_bank_info['bank_shortname_en'] = $nv_Request->get_title('bank_shortname_en', 'post', '');
    $array_insert_bank_info['description_content_vi'] = $nv_Request->get_title('description_content_vi', 'post', '');
    $array_insert_bank_info['description_content_en'] = $nv_Request->get_title('description_content_en', 'post', '');
    $array_insert_bank_info['note_vi'] = $nv_Request->get_title('note_vi', 'POST', '');
    $array_insert_bank_info['note_en'] = $nv_Request->get_title('note_en', 'POST', '');

    if (empty($array_insert_bank_info['title_vi'])) {
        $error .= $nv_Lang->getModule('error_title_bank') . ' (' . $nv_Lang->getModule('lang_vi') . ')' . '<br>';
    }
    if (empty($array_insert_bank_info['title_en'])) {
        $error .= $nv_Lang->getModule('error_title_bank') . ' (' . $nv_Lang->getModule('lang_en') . ')' . '<br>';
    }

    if (empty($array_insert_bank_info['account_holder_vi'])) {
        $error .= $nv_Lang->getModule('error_account_holder')  . ' (' . $nv_Lang->getModule('lang_vi') . ')' . '<br>';
    }
    if (empty($array_insert_bank_info['account_holder_en'])) {
        $error .= $nv_Lang->getModule('error_account_holder') . ' (' . $nv_Lang->getModule('lang_en') . ')'  . '<br>';
    }
    if (empty($array_insert_bank_info['account_number'])) {
        $error .= $nv_Lang->getModule('error_account_number') . '<br>';
    }
    if (empty($array_insert_bank_info['bank_name_vi'])) {
        $error .= $nv_Lang->getModule('error_bank_name')  . ' (' . $nv_Lang->getModule('lang_vi') . ')' .  '<br>';
    }
    if (empty($array_insert_bank_info['bank_name_en'])) {
        $error .= $nv_Lang->getModule('error_bank_name')  . ' (' . $nv_Lang->getModule('lang_en') . ')' .  '<br>';
    }
    if (empty($array_insert_bank_info['bank_shortname_vi'])) {
        $error .= $nv_Lang->getModule('error_bank_shortname')  . ' (' . $nv_Lang->getModule('lang_vi') . ')' .  '<br>';
    }
    if (empty($array_insert_bank_info['bank_shortname_en'])) {
        $error .= $nv_Lang->getModule('error_bank_shortname')  . ' (' . $nv_Lang->getModule('lang_en') . ')' .  '<br>';
    }

    if ($error == '') {
        // Kiểm tra có update hay không
        if ($nv_Request->isset_request('id', 'GET')) {
            $db->update_array($array_insert_bank_info, $table_bank_info, ['id=' . $id_bank]);
        } else {
            $db->insert_array($array_insert_bank_info, $table_bank_info);
        }
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    }
}

// Cập nhật status hiển thị ngoài site hay không
if ($nv_Request->isset_request('action', 'POST', '')) {
    if ($nv_Request->isset_request('id', 'POST')) {
        if ($nv_Request->get_title('action') == 'change_status') {
            $id = $nv_Request->get_int('id', 'POST', 0);
            if ($id != 0) {
                $query = 'UPDATE ' . $table_bank_info . ' SET status=status^1 WHERE id = ' . $id;
                $db->query($query);
                nv_jsonOutput([
                    'res' => 'success',
                    'status' => $nv_Lang->getModule('status_success'),
                    'message' => $nv_Lang->getModule('message_success'),
                    'color' => '#2ecc71',
                    'icon' => '<i class="fa fa-check"></i>'
                ]);
            }
        }
    }
    nv_jsonOutput([
        'res' => 'error',
        'status' => $nv_Lang->getModule('status_error'),
        'message' => $nv_Lang->getModule('message_error'),
        'color' => '#eb4034',
        'icon' => '<i class="fa fa-exclamation-triangle"></i>'
    ]);
}

// Lấy thông tin các tài khoản đang có
$db->sqlreset()
->select('*')
->from($db_config['prefix'] . '_' .$module_name . "_bank_info");
$bank_info = $db->query($db->sql())->fetchAll();
foreach($bank_info as $i => $bank_item) {
    $bank_info[$i]['title'] = $bank_item['title_' . NV_LANG_DATA];
    $bank_info[$i]['bank_name'] = $bank_item['bank_name_' . NV_LANG_DATA];
    $bank_info[$i]['bank_shortname'] = $bank_item['bank_shortname_' . NV_LANG_DATA];
    $bank_info[$i]['description_content'] = $bank_item['description_content_' . NV_LANG_DATA];
    $bank_info[$i]['note'] = $bank_item['note_' . NV_LANG_DATA];
}

$page_title = $nv_Lang->getModule('config_bank_info');
$array_config = $module_config[$module_name];

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('OP', $op);
$xtpl->assign('DATA', $array_config);

// Parse các thông tin ngân hàng đã có
$stt = 1;
if (!$is_update) {
    foreach ($bank_info as $key => $val) {
        $val['stt'] = $stt++;
        if ($val['status'] == 1) {
            $val['status'] = 'checked';
        } else {
            $val['status'] = '';
        }
        $val['link_update'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&id=' . $val['id'];
        $xtpl->assign('loop', $val);
        $xtpl->parse('main.bank_info.loop');
    }
    $xtpl->parse('main.bank_info');
} else {
    foreach ($bank_info as $key => $val) {
        if ($val['id'] == $id_bank) {
            $xtpl->assign('bank_info_update', $val);
        }
    }
}

if (!empty($error)) {
    $xtpl->assign('ERROR', $error);
    $xtpl->parse('main.error');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
