<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6ca42708-7d08-408c-ae38-670273b7e257" name="Changes" comment="Tạo bộ lọc Leads/Cơ hội theo : ghi chú mới nhât dauthau.info#3456">
      <change beforePath="$PROJECT_DIR$/src/modules/crmbidding/admin/leads_info.php" beforeDir="false" afterPath="$PROJECT_DIR$/src/modules/crmbidding/admin/leads_info.php" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/modules/crmbidding/admin/opportunities_info.php" beforeDir="false" afterPath="$PROJECT_DIR$/src/modules/crmbidding/admin/opportunities_info.php" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerConfigs">
    <option name="configs">
      <option value="$PROJECT_DIR$/src/includes/composer.json" />
      <option value="$PROJECT_DIR$/src/composer.json" />
    </option>
  </component>
  <component name="ComposerSettings" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="Git.Merge.Settings">
    <option name="BRANCH" value="master" />
  </component>
  <component name="Git.Settings">
    <favorite-branches>
      <branch-storage>
        <map>
          <entry type="REMOTE">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$" source="origin/detached" />
              </list>
            </value>
          </entry>
        </map>
      </branch-storage>
    </favorite-branches>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="projectConfigVersion" value="5" />
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="dev_hai_3456" />
                    <option name="lastUsedInstant" value="1748967140" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1748967077" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="dev_hai_2744" />
                    <option name="lastUsedInstant" value="1727852717" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="D:\xampp\php\php.exe">
    <include_path>
      <path value="$PROJECT_DIR$/src/includes/vendor/smarty/smarty" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/src/includes/vendor/composer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/src/includes/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/src/includes/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/src/includes/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/src/includes/vendor/vinades/pclzip" />
      <path value="$PROJECT_DIR$/src/includes/vendor/vinades/nukeviet" />
      <path value="$PROJECT_DIR$/src/includes/vendor/tecnickcom/tc-lib-color" />
      <path value="$PROJECT_DIR$/src/includes/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/src/includes/vendor/zaloplatform/zalo-php-sdk" />
      <path value="$PROJECT_DIR$/src/includes/vendor/tecnickcom/tc-lib-barcode" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpmailer/phpmailer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/league/oauth2-client" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/src/vendor/desarrolla2/cache" />
      <path value="$PROJECT_DIR$/src/vendor/johngrogg/ics-parser" />
      <path value="$PROJECT_DIR$/src/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/src/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/src/vendor/composer" />
      <path value="$PROJECT_DIR$/src/vendor/mtdowling/jmespath.php" />
      <path value="$PROJECT_DIR$/src/vendor/aws/aws-sdk-php-resources" />
      <path value="$PROJECT_DIR$/src/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/src/vendor/aws/aws-crt-php" />
      <path value="$PROJECT_DIR$/src/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/vendor/aws/aws-sdk-php" />
      <path value="$PROJECT_DIR$/src/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/src/vendor/elastic/transport" />
      <path value="$PROJECT_DIR$/src/vendor/maennchen/zipstream-php" />
      <path value="$PROJECT_DIR$/src/vendor/php-http/discovery" />
      <path value="$PROJECT_DIR$/src/vendor/paypal/PayPal" />
      <path value="$PROJECT_DIR$/src/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/src/vendor/php-http/promise" />
      <path value="$PROJECT_DIR$/src/vendor/elasticsearch/elasticsearch" />
      <path value="$PROJECT_DIR$/src/vendor/php-http/httplug" />
      <path value="$PROJECT_DIR$/src/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/src/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/src/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/src/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/src/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/src/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/src/vendor/jasny/validation-result" />
      <path value="$PROJECT_DIR$/src/vendor/jasny/sso" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/src/vendor/ezyang/htmlpurifier" />
    </include_path>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2mVmZdu78IlVWtKRdzEaM9w10aj" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev__hai__3456&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/webroot/www/dauthau/api.dauthau.info&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.sourceCode.HTML&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\webroot\www\dauthau\id.dauthau.net\vinades" />
      <recent name="D:\webroot\www\dauthau\id.dauthau.net\src\modules\wallet\funcs" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-PS-242.23726.107" />
        <option value="bundled-php-predefined-a98d8de5180a-90914f2295cb-com.jetbrains.php.sharedIndexes-PS-242.23726.107" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="6ca42708-7d08-408c-ae38-670273b7e257" name="Changes" comment="" />
      <created>1727170175127</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1727170175127</updated>
      <workItem from="1727170176302" duration="18137000" />
      <workItem from="1727252720736" duration="28673000" />
      <workItem from="1727506282427" duration="6456000" />
      <workItem from="1727662306757" duration="21319000" />
      <workItem from="1727794241715" duration="138000" />
      <workItem from="1727794406037" duration="4701000" />
      <workItem from="1727838685675" duration="10993000" />
      <workItem from="1727921270779" duration="456000" />
      <workItem from="1727921741769" duration="5530000" />
      <workItem from="1727928116874" duration="54000" />
      <workItem from="1727928181274" duration="4652000" />
      <workItem from="1728033652520" duration="2661000" />
      <workItem from="1728117132486" duration="226000" />
      <workItem from="1728544741483" duration="2714000" />
      <workItem from="1741060933348" duration="10049000" />
      <workItem from="1741140695796" duration="2867000" />
      <workItem from="1741359953556" duration="1130000" />
      <workItem from="1741528132590" duration="582000" />
      <workItem from="1741665309925" duration="10032000" />
      <workItem from="1741685195174" duration="1678000" />
      <workItem from="1741753334154" duration="1182000" />
      <workItem from="1743843360420" duration="3190000" />
      <workItem from="1743950033216" duration="88000" />
      <workItem from="1744081791004" duration="579000" />
      <workItem from="1744119983931" duration="65000" />
      <workItem from="1744259190191" duration="2144000" />
      <workItem from="1744338048600" duration="20000" />
      <workItem from="1744431000956" duration="16000" />
      <workItem from="1744601551182" duration="780000" />
      <workItem from="1744643927584" duration="948000" />
      <workItem from="1745425025478" duration="2146000" />
      <workItem from="1745832576135" duration="257000" />
      <workItem from="1746586652549" duration="1254000" />
      <workItem from="1746671526228" duration="61000" />
      <workItem from="1746671694944" duration="1854000" />
      <workItem from="1748945146915" duration="3039000" />
      <workItem from="1748964038263" duration="6290000" />
      <workItem from="1749018134747" duration="1364000" />
    </task>
    <task id="LOCAL-00002" summary="Thêm các ô chọn địa chỉ">
      <option name="closed" value="true" />
      <created>1727237854392</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1727237854392</updated>
    </task>
    <task id="LOCAL-00003" summary="Xử lí issue dauthau.info#2744">
      <option name="closed" value="true" />
      <created>1727256061134</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1727256061134</updated>
    </task>
    <task id="LOCAL-00004" summary="Xử lí issue dauthau.info#2744">
      <option name="closed" value="true" />
      <created>1727259910094</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1727259910094</updated>
    </task>
    <task id="LOCAL-00005" summary="Fix lỗi psr12">
      <option name="closed" value="true" />
      <created>1727260023021</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1727260023021</updated>
    </task>
    <task id="LOCAL-00006" summary="Xử lí issue dauthau#2744">
      <option name="closed" value="true" />
      <created>1727335871342</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1727335871342</updated>
    </task>
    <task id="LOCAL-00007" summary="Xử lí issue dauthau#2744">
      <option name="closed" value="true" />
      <created>1727344190135</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1727344190135</updated>
    </task>
    <task id="LOCAL-00008" summary="Xử lí issue dauthau#2744">
      <option name="closed" value="true" />
      <created>1727345749815</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1727345749815</updated>
    </task>
    <task id="LOCAL-00009" summary="Xử lí issue dauthau#2744">
      <option name="closed" value="true" />
      <created>1727422666951</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1727422666951</updated>
    </task>
    <task id="LOCAL-00010" summary="Xử lí issue dauthau#2744">
      <option name="closed" value="true" />
      <created>1727425521487</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1727425521487</updated>
    </task>
    <task id="LOCAL-00011" summary="Xử lí issue dauthau#2744">
      <option name="closed" value="true" />
      <created>1727425776060</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1727425776060</updated>
    </task>
    <task id="LOCAL-00012" summary="Xử lí issue dauthau#2744">
      <option name="closed" value="true" />
      <created>1727425893216</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1727425893216</updated>
    </task>
    <task id="LOCAL-00013" summary="Xử lí issue dauthau#2744">
      <option name="closed" value="true" />
      <created>1727426436142</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1727426436142</updated>
    </task>
    <task id="LOCAL-00014" summary="Xử lí issue dauthau#2744">
      <option name="closed" value="true" />
      <created>1727517226674</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1727517226674</updated>
    </task>
    <task id="LOCAL-00015" summary="Sửa lỗi psr12">
      <option name="closed" value="true" />
      <created>1727523392292</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1727523392292</updated>
    </task>
    <task id="LOCAL-00016" summary="Sửa lỗi conflit">
      <option name="closed" value="true" />
      <created>1727663039781</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1727663039781</updated>
    </task>
    <task id="LOCAL-00017" summary="Sửa lỗi conflit">
      <option name="closed" value="true" />
      <created>1727663840238</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1727663840238</updated>
    </task>
    <task id="LOCAL-00018" summary="Sửa lỗi conflit">
      <option name="closed" value="true" />
      <created>1727663994734</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1727663994734</updated>
    </task>
    <task id="LOCAL-00019" summary="Sửa lỗi format code">
      <option name="closed" value="true" />
      <created>1727667005602</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1727667005602</updated>
    </task>
    <task id="LOCAL-00020" summary="Sửa lỗi format code">
      <option name="closed" value="true" />
      <created>1727667258609</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1727667258609</updated>
    </task>
    <task id="LOCAL-00021" summary="Sửa lỗi format code">
      <option name="closed" value="true" />
      <created>1727670185193</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1727670185193</updated>
    </task>
    <task id="LOCAL-00022" summary="Sửa lỗi format code">
      <option name="closed" value="true" />
      <created>1727670996922</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1727670996922</updated>
    </task>
    <task id="LOCAL-00023" summary="Sửa lỗi format code">
      <option name="closed" value="true" />
      <created>1727671214206</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1727671214206</updated>
    </task>
    <task id="LOCAL-00024" summary="Sửa lỗi format code">
      <option name="closed" value="true" />
      <created>1727671399102</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1727671399102</updated>
    </task>
    <task id="LOCAL-00025" summary="Xử lí issue dauthau.info#2744">
      <option name="closed" value="true" />
      <created>1727798770711</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1727798770711</updated>
    </task>
    <task id="LOCAL-00026" summary="Xử lí issue dauthau.info#2744">
      <option name="closed" value="true" />
      <created>1727839341168</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1727839341168</updated>
    </task>
    <task id="LOCAL-00027" summary="Xử lí issue dauthau.info#2744">
      <option name="closed" value="true" />
      <created>1727840029095</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1727840029095</updated>
    </task>
    <task id="LOCAL-00028" summary="Xử lí issue dauthau.info#2744">
      <option name="closed" value="true" />
      <created>1727840761691</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1727840761691</updated>
    </task>
    <task id="LOCAL-00029" summary="Sửa lỗi psr12">
      <option name="closed" value="true" />
      <created>1727841734160</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1727841734160</updated>
    </task>
    <task id="LOCAL-00030" summary="Xử lí issue dauthau.info#2744">
      <option name="closed" value="true" />
      <created>1727844702053</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1727844702053</updated>
    </task>
    <task id="LOCAL-00031" summary="Xử lí issue dauthau.info#2744">
      <option name="closed" value="true" />
      <created>1727846392990</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1727846392990</updated>
    </task>
    <task id="LOCAL-00032" summary="Xử lí issue dauthau.info#2744">
      <option name="closed" value="true" />
      <created>1727852500882</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1727852500882</updated>
    </task>
    <task id="LOCAL-00033" summary="Xử lí issue dauthau.info#2744">
      <option name="closed" value="true" />
      <created>1727853064924</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1727853064924</updated>
    </task>
    <task id="LOCAL-00034" summary="Xử lí giao diện mobile">
      <option name="closed" value="true" />
      <created>1727928316265</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1727928316265</updated>
    </task>
    <task id="LOCAL-00035" summary="Xử lí giao diện mobile">
      <option name="closed" value="true" />
      <created>1727928650029</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1727928650029</updated>
    </task>
    <task id="LOCAL-00036" summary="Xử lí giao diện mobile">
      <option name="closed" value="true" />
      <created>1727928717206</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1727928717206</updated>
    </task>
    <task id="LOCAL-00037" summary="Xử lí giao diện mobile">
      <option name="closed" value="true" />
      <created>1727929439725</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1727929439725</updated>
    </task>
    <task id="LOCAL-00038" summary="Xử lí giao diện mobile">
      <option name="closed" value="true" />
      <created>1727929666169</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1727929666169</updated>
    </task>
    <task id="LOCAL-00039" summary="Xử lí giao diện mobile">
      <option name="closed" value="true" />
      <created>1727929951636</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1727929951636</updated>
    </task>
    <task id="LOCAL-00040" summary="Dùng SCSS cho location">
      <option name="closed" value="true" />
      <created>1727930343505</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1727930343505</updated>
    </task>
    <task id="LOCAL-00041" summary="Xử lí lại vị trí hiển thị địa chỉ">
      <option name="closed" value="true" />
      <created>1728546150869</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1728546150869</updated>
    </task>
    <task id="LOCAL-00042" summary="Xử lí lại vị trí hiển thị địa chỉ">
      <option name="closed" value="true" />
      <created>1728546287960</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1728546287960</updated>
    </task>
    <task id="LOCAL-00043" summary="Sửa lỗi tìm kiếm">
      <option name="closed" value="true" />
      <created>1741083670045</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1741083670045</updated>
    </task>
    <task id="LOCAL-00044" summary="Dùng sort array">
      <option name="closed" value="true" />
      <created>1741084233893</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1741084233893</updated>
    </task>
    <task id="LOCAL-00045" summary="Sửa điều kiện search ví tiền và số dư">
      <option name="closed" value="true" />
      <created>1741669132991</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1741669132991</updated>
    </task>
    <task id="LOCAL-00046" summary="Chỉnh sửa giao diện tạo mới yêu cầu">
      <option name="closed" value="true" />
      <created>1741682983291</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1741682983291</updated>
    </task>
    <task id="LOCAL-00047" summary="Chỉnh sửa giao diện tạo mới yêu cầu">
      <option name="closed" value="true" />
      <created>1741683406620</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1741683406620</updated>
    </task>
    <task id="LOCAL-00048" summary="Chỉnh sửa giao diện tạo mới yêu cầu">
      <option name="closed" value="true" />
      <created>1741686091553</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1741686091553</updated>
    </task>
    <task id="LOCAL-00049" summary="Chỉnh sửa nhận diện tự động dauthau.info#3340">
      <option name="closed" value="true" />
      <created>1745426640972</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1745426640972</updated>
    </task>
    <task id="LOCAL-00050" summary="Tạo bộ lọc Leads/Cơ hội theo : ghi chú mới nhât dauthau.info#3456">
      <option name="closed" value="true" />
      <created>1748971799568</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1748971799570</updated>
    </task>
    <option name="localTasksCounter" value="51" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="Thêm các ô chọn địa chỉ" />
    <MESSAGE value="Fix lỗi psr12" />
    <MESSAGE value="Xử lí issue dauthau#2744" />
    <MESSAGE value="Sửa lỗi conflit" />
    <MESSAGE value="Sửa lỗi format code" />
    <MESSAGE value="Sửa lỗi psr12" />
    <MESSAGE value="Xử lí issue dauthau.info#2744" />
    <MESSAGE value="Xử lí giao diện mobile" />
    <MESSAGE value="Dùng SCSS cho location" />
    <MESSAGE value="Xử lí lại vị trí hiển thị địa chỉ" />
    <MESSAGE value="Sửa lỗi tìm kiếm" />
    <MESSAGE value="Dùng sort array" />
    <MESSAGE value="Sửa điều kiện search ví tiền và số dư" />
    <MESSAGE value="Chỉnh sửa giao diện tạo mới yêu cầu" />
    <MESSAGE value="Chỉnh sửa nhận diện tự động dauthau.info#3340" />
    <MESSAGE value="Tạo bộ lọc Leads/Cơ hội theo : ghi chú mới nhât dauthau.info#3456" />
    <option name="LAST_COMMIT_MESSAGE" value="Tạo bộ lọc Leads/Cơ hội theo : ghi chú mới nhât dauthau.info#3456" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>