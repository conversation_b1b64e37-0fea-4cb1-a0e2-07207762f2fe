<?php

use NukeViet\Api\DoApi;
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$id_user = intval($nv_Request->get_int('user_id', 'get', 0));
$user_info = [];
if ($id_user > 0) {
    // lấy thông tin user theo id bao gồm toàn bộ user chứ k phải mỗi khách vip trong customer
    $sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . " as tb1 INNER JOIN " . NV_USERS_GLOBALTABLE . "_info as tb2 ON tb1.userid=tb2.userid WHERE tb1.userid=" . $id_user;
    $result = $db->query($sql);
    $user_info = $result->fetch();
    $result->closeCursor();
}

if (empty($user_info)) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=manage_customer');
}

$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;user_id=' . $id_user;

// thông tin sale chăm sóc
$carrier_info = $db->query('SELECT * FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $user_info['saleid'])->fetch();

// lấy cấu hình module bidding dauthau.info
$params = [
    'name' => 'bidding',
    'site' => 'dauthau.info'
];
$result_api = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'ListModuleConfig', $params);
$bidding_dauthau_config = $vip_is_filter = [];

if ($result_api['status'] == 'success') {
    foreach ($result_api['data'] as $k => $v) {
        $bidding_dauthau_config[$k] = $v;

        if (substr($k, -7) == 'filters') {
            $vip_is_filter[] = str_replace('vip', '', $k);
        }
    }
}

 // Trạng thái
$arr_status = [
    $nv_Lang->getModule('history_payment_no'),
    $nv_Lang->getModule('history_payment_send'),
    $nv_Lang->getModule('history_payment_check'),
    $nv_Lang->getModule('history_payment_cancel'),
    $nv_Lang->getModule('history_payment_yes')
];

// lấy danh sách vip dauthau.info
$arr_where = [];
$arr_where['AND'][] = [
    '=' => [
        'user_id' => $id_user
    ]
];
$param = array(
    'where' => $arr_where
);
$result = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'ListBiddingCustoms', $param, 'bidding');
$array_vip_dauthauinfo = [];
if ($result_api['status'] == 'success' and !empty($result['data'])) {
    $array_vip_dauthauinfo = $result['data'];
}

// ajax các thông tin
// đơn hàng dauthau.info
if ($nv_Request->isset_request('apiName', 'post') && $nv_Request->get_title('apiName', 'post', '') == 'listOrder') {
    $arr_where = [];
    $arr_where['AND'][] = [
        '=' => [
            'userid' => $id_user
        ]
    ];
    $array_order = [];
    $array_order = [
        'add_time' => 'DESC'
    ];

    $infoAPI = array(
        'where' => $arr_where,
        'order' => $array_order
    );
    $contents = '';
    $result = nv_call_api(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET, 'ListBiddingOrder', $infoAPI, 'bidding');
    if ($result['status'] == 'success' and !empty($result['data'])) {
        $arrIdOrder = array_column($result['data'], 'order_id');
        // Log của đơn hàng
        $array_logs = [];
        if (!empty($arrIdOrder)) {
            $infoAPI = [];
            $arr_where = [];
            $arr_where['AND'][] = [
                'IN' => [
                    'order_id' => "(" . implode(',', $arrIdOrder) . ")"
                ]
            ];
            $infoAPI = array(
                'where' => $arr_where,
                'array_select' => 'order_id, vip, type_export, numbers_year'
            );
            $result_log = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'ListBiddingCustomsLog', $infoAPI, 'bidding');
            if (!empty($result_log['data'])) {
                foreach ($result_log['data'] as $key => $value) {
                    $array_logs[$value['order_id']][] = $value;
                }
            }

            $stt = 1;
            $array_order_dtinfo = [];
            foreach ($result['data'] as $key => $value) {
                $value['stt'] = $stt++;
                if (isset($arr_status_order[$value['status']])) {
                    $value['name_status'] = $arr_status_order[$value['status']];
                }

                if ($value['status'] == 4) {
                    $value['class_status'] = 'fieldset__order--success';
                    $value['status_bg'] = 'price--status--success';
                } else {
                    $value['class_status'] = 'fieldset__order--danger';
                    $value['status_bg'] = 'price--status--danger';
                    $xtpl->parse('main.view_order.loop.show_bell');
                }

                if ($value['is_expired'] == 1) {
                    $value['is_expired'] = $nv_Lang->getModule('not_expired');
                    $value['class_expired'] = 'not_is_expired';
                } else {
                    $value['is_expired'] = $nv_Lang->getModule('yes_expired');
                    $value['class_expired'] = 'is_expired';
                }
                if (isset($arr_source_money[$value['source_money']])) {
                    $value['name_source_money'] = $arr_source_money[$value['source_money']];
                }

                $value['money'] = number_format($value['money'], 0, ',', '.') . CURRENCY_UNIT;
                $value['discount'] = number_format($value['discount'], 0, ',', '.') . CURRENCY_UNIT;
                $value['price_reduce'] = number_format($value['price_reduce'], 0, ',', '.') . CURRENCY_UNIT;
                $value['source_money'] = number_format($value['source_money'], 0, ',', '.') . CURRENCY_UNIT;
                $value['total_end'] = number_format($value['total_end'], 0, ',', '.') . CURRENCY_UNIT;
                $value['total'] = number_format($value['total'], 0, ',', '.') . CURRENCY_UNIT;
                $value['taxes_fees'] = number_format($value['taxes_fees'], 0, ',', '.') . CURRENCY_UNIT;
                $value['add_time'] = nv_date('d/m/Y H:i:s', $value['add_time']);
                $value['edit_time'] = nv_date('d/m/Y H:i:s', $value['edit_time']);
                $value['static_time'] = nv_date('d/m/Y H:i:s', $value['static_time']);
                $value['link_order'] = $view['order'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding' . '&amp;' . NV_OP_VARIABLE . '=payment&amp;vieworderid=' . $value['id'] . '&amp;userid=' . $value['userid'] . '&showheader=1';

                $value['name_userid'] = nv_show_name_user($user_info['first_name'], $user_info['last_name'], $user_info['username']);
                if (isset($arrUser[$value['admin_id']])) {
                    $value['name_admin_id'] = nv_show_name_user($all_array_user_id_users[$value['admin_id']]['first_name'], $all_array_user_id_users[$value['admin_id']]['last_name'], $all_array_user_id_users[$value['admin_id']]['username']);
                }

                if (isset($arrUser[$value['caregiver_id']])) {
                    $value['name_caregiver_id'] = nv_show_name_user($all_array_user_id_users[$value['caregiver_id']]['first_name'], $all_array_user_id_users[$value['caregiver_id']]['last_name'], $all_array_user_id_users[$value['caregiver_id']]['username']);
                }

                if (isset($arrUser[$value['promo_userid']])) {
                    $value['name_caregiver_id'] = nv_show_name_user($all_array_user_id_users[$value['promo_userid']]['first_name'], $all_array_user_id_users[$value['promo_userid']]['last_name'], $all_array_user_id_users[$value['promo_userid']]['username']);
                }

                if (isset($arrUser[$value['add_adminid']])) {
                    $value['name_add_adminid'] = nv_show_name_user($all_array_user_id_users[$value['add_adminid']]['first_name'], $all_array_user_id_users[$value['add_adminid']]['last_name'], $all_array_user_id_users[$value['add_adminid']]['username']);
                }

                if (isset($arrUser[$value['affiliate_userid']])) {
                    $value['name_affiliate_userid'] = nv_show_name_user($all_array_user_id_users[$value['affiliate_userid']]['first_name'], $all_array_user_id_users[$value['affiliate_userid']]['last_name'], $all_array_user_id_users[$value['affiliate_userid']]['username']);
                }
                $value['title'] = [];
                if (isset($array_logs[$value['id']])) {
                    foreach ($array_logs[$value['id']] as $log) {
                        if ($log['vip'] == 88) {
                            // VIP X1 không tính theo năm
                            if ($log['type_export'] == 1) {
                                $value['title'][] = 'gói <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . '</span>: ' . $nv_Lang->getModule('vip88_export_old');
                            } elseif ($log['type_export'] == 2) {
                                $value['title'][] = 'gói <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . '</span>: ' . $nv_Lang->getModule('vip88_export_now_month');
                            } else {
                                $value['title'][] = 'gói <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . '</span>: ' . $nv_Lang->getModule('vip88_export_now_year');
                            }
                        } else if ($log['vip'] == 55) {
                            if (!empty($log['type_export'])) {
                                $value['title'][] = 'gói <span class="btn_order__vip">' . ($log['type_export'] == 1 ? $nv_Lang->getModule('vip55_year_lang') : $nv_Lang->getModule('vip55_month_lang')) . '</span>';
                            }
                        } else {
                            if ($log['numbers_year'] < 1) {
                                switch ($log['numbers_year']) {
                                    case '0.08':
                                        $value['title'][] = 'gói: <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . ' </span> với thời hạn ' . $nv_Lang->getModule('key_numer_year_' . $log['numbers_year']);
                                        break;

                                    case '0.25':
                                        $value['title'][] = 'gói: <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . ' </span> với thời hạn ' . $nv_Lang->getModule('key_numer_year_' . $log['numbers_year']);
                                        break;

                                    case '0.5':
                                        $value['title'][] = 'gói: <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . ' </span> với thời hạn ' . $nv_Lang->getModule('key_numer_year_' . $log['numbers_year']);
                                        break;

                                    case '0.75':
                                        $value['title'][] = 'gói: <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . ' </span> với thời hạn ' . $nv_Lang->getModule('key_numer_year_' . $log['numbers_year']);
                                        break;
                                }
                            } else {
                                // Các gói còn lại tính theo năm
                                $value['title'][] = $log['numbers_year'] . ' năm gói <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . '</span>';
                            }
                        }
                    }
                }

                if (empty($value['title'])) {
                    $value['title'] = 'Giao dịch chưa rõ nội dung';
                } else {
                    $value['title'] = '<a href="' . $value['link_order'] . '" data-toggle="tooltip" title="Chi tiết gói ' . $nv_Lang->getModule('vip' . $log['vip']) . '"> Thanh toán ' . implode(', ', $value['title']) . '</a>';
                }
                $array_order_dtinfo[] = $value;
            }

            $tpl = new \NukeViet\Template\NVSmarty();
            $tpl->setTemplateDir(get_module_tpl_dir('detail_customer_order_dtinfo.tpl'));
            $tpl->assign('LANG', $nv_Lang);
            $tpl->assign('MODULE_NAME', $module_name);
            $tpl->assign('MODULE_DATA', $module_data);
            $tpl->assign('OP', $op);
            $tpl->assign('ARRAY_ORDER_DTINFO', $array_order_dtinfo);
            $contents = $tpl->fetch('detail_customer_order_dtinfo.tpl');

            nv_jsonOutput([
                'status' => 'success',
                'data' => $contents
            ]);
        }
        nv_jsonOutput([
            'status' => 'success',
            'mess' => 'Empty data'
        ]);
    } else {
        nv_jsonOutput([
            'status' => 'fail',
            'error' => $nv_Lang->getModule('err_list_order'),
            'mess' => $error,
            'result' => $result
        ]);
    }

    exit();
}

// danh sách profile dauthau.net
if (defined('API_DAUTHAUNET_URL') and $nv_Request->isset_request('apiName', 'post') && $nv_Request->get_title('apiName', 'post', '') == 'list_profile') {
    $where_api = [];
    $per_page = 20;
    $page = $nv_Request->get_int('page', 'post', 1);
    $fields = [
        'id',
        'profile_type',
        'prof_name',
        'prof_code',
        'prof_alias',
        'info_phone',
        'info_email',
        'status',
        'fee_status',
        'fee_expired',
        'active_id',
        'add_time'
    ];
    $where_api['AND'][] = [
        '=' => [
            'userid' => $id_user
        ]
    ];

    $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
    $api->setModule('')
        ->setLang('vi')
        ->setAction('ListProfile')
        ->setData([
            'page' => $page,
            'per_page' => $per_page,
            'fields' => $fields,
            'where' => $where_api
        ]);
    $result = $api->execute();
    $error = $api->getError();

    if (empty($error) and $result['status'] == 'success') {
        if (!empty($result['data'])) {
            $stt = $per_page * ($page - 1);
            $array_profile_dtnet = [];
            foreach ($result['data'] as $key => $value) {
                $stt++;
                $value['stt'] = $stt;
                $value['add_time'] = nv_date('d/m/Y', $value['add_time']);
                if ($value['status'] == 3 or $value['status'] == 4 or $value['status'] == 5) {

                    $value['status_txt'] = '<a href="' . URL_DTNET_ADMIN . 'index.php?language=vi&nv=dn&op=detail&active_id=' . $value['active_id'] . '">' . $nv_Lang->getModule('active_' . $value['status']) . '</a>';
                } else if ($value['status'] == 10 || $value['status'] == 11 || $value['status'] == 12) {
                    $value['status_txt'] = $nv_Lang->getModule('venture_' . $value['status']);
                } else {
                    // đã xác thực hoặc cần xác thực lại
                    $value['status_txt'] = $nv_Lang->getModule('active_' . $value['status']);
                }

                $value['prof_code_view'] = $value['info_phone_view'] = $value['info_email_view'] = '';
                if (!empty($value['prof_code'])) {
                    $value['prof_code_view'] = '<br/> MST: ' . $value['prof_code'];
                }
                if (!empty($value['info_phone'])) {
                    $value['info_phone_view'] = '<br/> SĐT: ' . $value['info_phone'];
                }
                if (!empty($value['info_email'])) {
                    $value['info_email_view'] = '<br/> Email: ' . $value['info_email'];
                }

                $value['fee_status_parse'] = $value['status_fee'] > 0 ? $nv_Lang->getModule('fee_status1') : $nv_Lang->getModule('fee_status0');
                $value['fee_expired_parse'] = nv_date('d/m/Y', $value['fee_expired']);
                $value['link_view'] = 'https://dauthau.net/vi/dn/' . $value['prof_alias'];

                $array_profile_dtnet[] = $value;
            }
            // phân trang
            $generate_page = nv_generate_page($base_url, $result['total'], $per_page, $page);

            $tpl = new \NukeViet\Template\NVSmarty();
            $tpl->setTemplateDir(get_module_tpl_dir('detail_customer_list_profile.tpl'));
            $tpl->assign('LANG', $nv_Lang);
            $tpl->assign('MODULE_NAME', $module_name);
            $tpl->assign('MODULE_DATA', $module_data);
            $tpl->assign('OP', $op);
            $tpl->assign('ARRAY_PROFILE_DTNET', $array_profile_dtnet);
            $tpl->assign('NV_GENERATE_PAGE', $generate_page);
            $contents = $tpl->fetch('detail_customer_list_profile.tpl');
            // trả về dữ liệu
            nv_jsonOutput([
                'status' => 'success',
                'mess' => 'Success',
                'data' => $contents
            ]);
        }
        nv_jsonOutput([
            'status' => 'success',
            'mess' => 'Empty data'
        ]);
    } else {
        nv_jsonOutput([
            'status' => 'fail',
            'error' => $nv_Lang->getModule('err_list_profile_dtnet'),
            'mess' => $error,
            'result' => $result
        ]);
    }
}

// danh sách vip dauthau.net
if (defined('API_DAUTHAUNET_URL') and $nv_Request->isset_request('apiName', 'post') && $nv_Request->get_title('apiName', 'post', '') == 'list_vip_dtnet') {
    $where_api = [];
    $per_page = 20;
    $page = $nv_Request->get_int('page', 'post', 1);
    $fields = [
        'id',
        'profile_id',
        'vip',
        'from_time',
        'end_time',
        'sum_viptime',
        'status',
        'name',
        'tax',
        'email_bill',
        'contact_phone',
        'contact_to',
        'address_org',
        'admin_id'
    ];

    $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
    $api->setModule('')
        ->setLang('vi')
        ->setAction('ListVip')
        ->setData([
            'page' => $page,
            'per_page' => $per_page,
            'fields' => $fields,
            'where' => $where_api,
            'userid' => $id_user
        ]);
    $result = $api->execute();
    $error = $api->getError();

    if (empty($error) and $result['status'] == 'success') {
        if (!empty($result['data'])) {

            $stt = $per_page * ($page - 1);
            $array_vip_dtnet = [];
            foreach ($result['data'] as $key => $value) {
                $stt++;
                $value['stt'] = $stt;
                $value['from_time'] = nv_date('d/m/Y', $value['from_time']);
                $value['end_time'] = nv_date('d/m/Y', $value['end_time']);
                $value['sum_viptime'] = nv_convertfromSec($value['sum_viptime']);
                $value['class_status'] = ($value['status'] == 1) ? 'label label-primary' : 'label label-danger';
                $value['status'] = ($value['status'] == 1) ? $nv_Lang->getModule('hieuluc') : $nv_Lang->getModule('hethan');
                $value['email'] = $value['email_bill'];
                $value['link_vip'] = URL_DTNET_ADMIN . 'index.php?language=vi&nv=dn&op=cus_info&showheader=1&id=' . $value['id'];
                $value['vip_title'] = $nv_Lang->getModule($value['vip']);
                $value['name_admin_id'] = isset($all_array_user_id_users[$value['admin_id']]) ? $all_array_user_id_users[$value['admin_id']]['username'] : 'N/A';
                $array_vip_dtnet[] = $value;
            }

            // phân trang
            $generate_page = nv_generate_page($base_url, $result['total'], $per_page, $page);

            $tpl = new \NukeViet\Template\NVSmarty();
            $tpl->setTemplateDir(get_module_tpl_dir('detail_customer_list_vip_dtnet.tpl'));
            $tpl->assign('LANG', $nv_Lang);
            $tpl->assign('MODULE_NAME', $module_name);
            $tpl->assign('MODULE_DATA', $module_data);
            $tpl->assign('OP', $op);
            $tpl->assign('ARRAY_VIP_DTNET', $array_vip_dtnet);
            $tpl->assign('NV_GENERATE_PAGE', $generate_page);
            $contents = $tpl->fetch('detail_customer_list_vip_dtnet.tpl');
            // trả về dữ liệu
            nv_jsonOutput([
                'status' => 'success',
                'mess' => 'Success',
                'data' => $contents
            ]);
        }
        nv_jsonOutput([
            'status' => 'success',
            'mess' => 'Empty data'
        ]);
    } else {
        nv_jsonOutput([
            'status' => 'fail',
            'error' => $nv_Lang->getModule('err_list_profile_dtnet'),
            'mess' => $error,
            'result' => $result
        ]);
    }
}

// danh sách order dauthau.net
if (defined('API_DAUTHAUNET_URL') and $nv_Request->isset_request('apiName', 'post') && $nv_Request->get_title('apiName', 'post', '') == 'list_order_dtnet') {
    $where_api = [];
    $per_page = 20;
    $page = $nv_Request->get_int('page', 'post', 1);
    $fields = [
        'id',
        'profile_id',
        'userid',
        'admin_id',
        'caregiver_id',
        'affiliate_userid',
        'promo_userid',
        'promo_code',
        'promo_type',
        'promo_value',
        'promo_id',
        'money',
        'discount',
        'price_reduce',
        'total_end',
        'total',
        'discount_update',
        'total_update',
        'note',
        'status',
        'is_expired',
        'add_time',
        'static_time',
        'source_money',
        'add_adminid'
    ];

    $where_api['AND'][] = [
        '=' => [
            'userid' => $id_user
        ]
    ];
    $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
    $api->setModule('')
        ->setLang('vi')
        ->setAction('ListPayment')
        ->setData([
            'page' => $page,
            'per_page' => $per_page,
            'fields' => $fields,
            'where' => $where_api
        ]);
    $result = $api->execute();
    $error = $api->getError();

    if (empty($error) and $result['status'] == 'success') {
        if (!empty($result['data'])) {
            $where_api = [];
            $fields = [
                'pay_id',
                'vip',
                'vip_title',
                'numbers_year'
            ];
            foreach ($result['data'] as $key => $value) {
                $where_api['OR'][] = [
                    '=' => [
                        'pay_id' => $value['id']
                    ]
                ];
            }

            $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
            $api->setModule('')
                ->setLang('vi')
                ->setAction('ListPayLog')
                ->setData([
                    'page' => 1,
                    'per_page' => $per_page,
                    'fields' => $fields,
                    'where' => $where_api
                ]);
            $result_log = $api->execute();
            $error_log = $api->getError();

            if (empty($error_log) and $result_log['status'] == 'success') {
                if ($result_log['total'] > 0) {
                    foreach ($result_log['data'] as $key => $value) {
                        $array_logs[$value['pay_id']] = $value;
                    }
                }
            }
            $stt = $per_page * ($page - 1);
            $array_order_dtnet = [];
            foreach ($array_payment_dtnet as $key => $value) {
                $stt++;
                $value['stt'] = $stt;
                if (isset($arr_status[$value['status']])) {
                    $value['name_status'] = $arr_status[$value['status']];
                }

                if ($value['status'] == 4) {
                    $value['class_status'] = 'fieldset__order--success';
                    $value['status_bg'] = 'price--status--success';
                } else {
                    $value['class_status'] = 'fieldset__order--danger';
                    $value['status_bg'] = 'price--status--danger';
                    $xtpl->parse('main.view_order.loop.show_bell');
                }

                if ($value['is_expired'] == 1) {
                    $value['is_expired'] = $nv_Lang->getModule('not_expired');
                    $value['class_expired'] = 'not_is_expired';
                } else {
                    $value['is_expired'] = $nv_Lang->getModule('yes_expired');
                    $value['class_expired'] = 'is_expired';
                }
                if (isset($arr_source_money[$value['source_money']])) {
                    $value['name_source_money'] = $arr_source_money[$value['source_money']];
                }

                $value['money'] = number_format($value['money'], 0, ',', '.') . CURRENCY_UNIT;
                $value['discount'] = number_format($value['discount'], 0, ',', '.') . CURRENCY_UNIT;
                $value['price_reduce'] = number_format($value['price_reduce'], 0, ',', '.') . CURRENCY_UNIT;
                $value['source_money'] = number_format($value['source_money'], 0, ',', '.') . CURRENCY_UNIT;
                $value['total_end'] = number_format($value['total_end'], 0, ',', '.') . CURRENCY_UNIT;
                $value['total'] = number_format($value['total'], 0, ',', '.') . CURRENCY_UNIT;
                $value['taxes_fees'] = number_format($value['taxes_fees'], 0, ',', '.') . CURRENCY_UNIT;
                $value['add_time'] = nv_date('d/m/Y H:i:s', $value['add_time']);
                $value['edit_time'] = nv_date('d/m/Y H:i:s', $value['edit_time']);
                $value['static_time'] = nv_date('d/m/Y H:i:s', $value['static_time']);
                $value['link_order'] = $view['order'] = URL_DTNET_ADMIN . 'index.php?language=vi&nv=dn&op=payment&vieworderid=' . $value['id'];
                $value['name_userid'] = $user_info['username'];

                if (isset($all_array_user_id_users[$value['admin_id']])) {
                    $value['name_admin_id'] = $all_array_user_id_users[$value['admin_id']]['username'];
                }

                if (isset($all_array_user_id_users[$value['caregiver_id']])) {
                    $value['name_caregiver_id'] = $all_array_user_id_users[$value['caregiver_id']]['username'];
                }

                if (isset($all_array_user_id_users[$value['promo_userid']])) {
                    $value['name_promo_userid'] = $all_array_user_id_users[$value['promo_userid']]['username'];
                }

                if (isset($all_array_user_id_users[$value['add_adminid']])) {
                    $value['name_add_adminid'] = $all_array_user_id_users[$value['add_adminid']]['username'];
                }

                if (isset($all_array_user_id_users[$value['affiliate_userid']])) {
                    $value['name_affiliate_userid'] = $all_array_user_id_users[$value['affiliate_userid']]['username'];
                }
                $value['title'] = [];
                if (isset($array_logs[$value['id']])) {
                    if (!empty($array_logs[$value['id']]['vip'])) {
                        $value['title'] = $array_logs[$value['id']]['numbers_year'] . ' năm gói <span class="btn_order__vip">' . $array_logs[$value['id']]['vip_title'] . '</span>';
                    } else {
                        $value['title'] = $array_logs[$value['id']]['numbers_year'] . ' năm gia hạn hồ sơ';
                    }
                }

                if (empty($value['title'])) {
                    $value['title'] = 'Giao dịch chưa rõ nội dung';
                } else {
                    $value['title'] = '<a href="' . $value['link_order'] . '" data-toggle="tooltip" title="Chi tiết gói ' . $array_logs[$value['id']]['vip_title'] . '"> Thanh toán ' . $value['title'] . '</a>';
                }
                $array_order_dtnet[] = $value;
            }

            // phân trang
            $generate_page = nv_generate_page($base_url, $result['total'], $per_page, $page);

            $tpl = new \NukeViet\Template\NVSmarty();
            $tpl->setTemplateDir(get_module_tpl_dir('detail_customer_list_payment_dtnet.tpl'));
            $tpl->assign('LANG', $nv_Lang);
            $tpl->assign('MODULE_NAME', $module_name);
            $tpl->assign('MODULE_DATA', $module_data);
            $tpl->assign('OP', $op);
            $tpl->assign('ARRAY_ORDER_DTNET', $array_order_dtnet);
            $tpl->assign('NV_GENERATE_PAGE', $generate_page);
            $contents = $tpl->fetch('detail_customer_list_payment_dtnet.tpl');
            // trả về dữ liệu
            nv_jsonOutput([
                'status' => 'success',
                'mess' => 'Success',
                'data' => $contents
            ]);
        }
        nv_jsonOutput([
            'status' => 'success',
            'mess' => 'Empty data'
        ]);
    } else {
        nv_jsonOutput([
            'status' => 'fail',
            'error' => $nv_Lang->getModule('err_list_profile_dtnet'),
            'mess' => $error,
            'result' => $result
        ]);
    }
}

// danh sách ticket
if ($nv_Request->isset_request('apiName', 'post') && $nv_Request->get_title('apiName', 'post', '') == 'ListTicket') {
    $where_api = [];
    $where_api['AND'][] = [
        '=' => [
            'customer_id' => $id_user
        ]
    ];

    $perpage = 20;
    $page = $nv_Request->get_int('page', 'post', 1);

    $params = [
        'page' => $page,
        'perpage' => $perpage,
        'where' => $where_api
    ];

    // GỌI API
    $ListAllTicket = nv_local_api('ListAllTicket', $params, $admin_info['username'], 'supportticket');
    $result = json_decode($ListAllTicket, true);

    if ($result['status'] == 'success') {
        if (!empty($result['data'])) {

            $stt = $perpage * ($page - 1);
            $array_ticket = [];
            foreach ($result['data'] as $key => $value) {
                $stt++;
                $value['stt'] = $stt;
                $value['add_time'] = nv_date('H:i d/m/Y', $value['add_time']);
                $value['edit_time'] = $value['edit_time'] > 0 ? nv_date('H:i d/m/Y', $value['edit_time']) : '';
                if (!empty($value['assignee_to'])) {
                    $assignee_fullname = [];
                    foreach ($value['assignee'] as $assignee) {
                        $assignee_fullname[] = nv_show_name_user($assignee['first_name'], $assignee['last_name'], $assignee['userid']);
                    }
                    $value['assignee'] = implode(', ', $assignee_fullname);
                } else {
                    $value['assignee'] = '';
                }
                if (!empty($value['label_ids'])) {
                    $label_title = [];
                    foreach ($value['label'] as $label) {
                        $label_title[] = $label['title_' . NV_LANG_DATA];
                    }
                    $value['label'] = implode(', ', $label_title);
                } else {
                    $value['label'] = '';
                }
                $array_ticket[] = $value;
            }

            // phân trang
            $generate_page = nv_generate_page($base_url, $result['total'], $perpage, $page);

            $tpl = new \NukeViet\Template\NVSmarty();
            $tpl->setTemplateDir(get_module_tpl_dir('detail_customer_list_ticket.tpl'));
            $tpl->assign('LANG', $nv_Lang);
            $tpl->assign('MODULE_NAME', $module_name);
            $tpl->assign('MODULE_DATA', $module_data);
            $tpl->assign('OP', $op);
            $tpl->assign('ARRAY_TICKET', $array_ticket);
            $tpl->assign('NV_GENERATE_PAGE', $generate_page);
            $contents = $tpl->fetch('detail_customer_list_ticket.tpl');

            // trả về dữ liệu
            nv_jsonOutput([
                'status' => 'success',
                'data' => $contents,
            ]);
        }
        nv_jsonOutput([
            'status' => 'success',
            'mess' => 'Empty data'
        ]);
    } else {
        nv_jsonOutput([
            'status' => 'fail',
            'mess' => $nv_Lang->getModule('err_list_ticket'),
            'error' => $error,
            'result' => $result
        ]);
    }
}

// comment support
if ($nv_Request->isset_request('apiName', 'post') && $nv_Request->get_title('apiName', 'post', '') == 'ListSupport') {
    $query_comment = $db->query("SELECT * FROM " . NV_PREFIXLANG . '_' . $module_data . "_comment WHERE sourceid = " . $id_user . " AND source = 3 ORDER BY timecreate DESC");
    $stt = 1;
    $array_comment = [];
    $arr_note = array(
        1 => $nv_Lang->getModule('payment'),
        2 => $nv_Lang->getModule('customs'),
        3 => $nv_Lang->getModule('chitietkh'),
        4 => $nv_Lang->getModule('customs_net')
    );

    while ($row = $query_comment->fetch()) {
        $row['stt'] = $stt;
        $row['timecreate'] = nv_date('H:i d/m/Y', $row['timecreate']);
        $row['update_time'] = $row['update_time'] > 0 ? nv_date('H:i d/m/Y', $row['update_time']) : '';
        $row['name_admin_id'] = isset($all_array_user_id_users[$row['post_id']]) ? $all_array_user_id_users[$row['post_id']]['username'] : 'N/A';

        $title_vip = '';
        if ($row['siteid'] == 1) {
            $title_vip = $row['vip'] == 55 ? ($row['type_export'] == 1 ? $nv_Lang->getModule('vip55_year') : $nv_Lang->getModule('vip55_month')) : $nv_Lang->getModule('vip' . $row['vip']);
        } elseif ($row['siteid'] == 2) {
            $title_vip = $nv_Lang->getModule($value['vip']);
        }
        $row['vip_title'] = $title_vip;
        $array_comment[] = $row;
        $stt++;
    }

    $tpl = new \NukeViet\Template\NVSmarty();
    $tpl->setTemplateDir(get_module_tpl_dir('detail_customer_history_support.tpl'));
    $tpl->assign('LANG', $nv_Lang);
    $tpl->assign('MODULE_NAME', $module_name);
    $tpl->assign('MODULE_DATA', $module_data);
    $tpl->assign('OP', $op);
    $tpl->assign('ARRAY_COMMENT', $array_comment);
    $contents = $tpl->fetch('detail_customer_history_support.tpl');

    // trả về dữ liệu
    nv_jsonOutput([
        'status' => 'success',
        'data' => $contents,
    ]);
}

// Lưu comment Sale
$comment_vip = $nv_Request->get_title('vip', 'post', '');
if ($nv_Request->isset_request('action', 'post') && $nv_Request->get_title('action', 'post', '') == 'saveComment') {
    $id = intval($nv_Request->get_int('id', 'post', 0));
    $arr_vip = explode('_', $nv_Request->get_title('vip', 'post', 0));
    $content = $nv_Request->get_textarea('content', '', NV_ALLOWED_HTML_TAGS);
    $content = nv_nl2br($content);
    $arr_vip[0] = isset($arr_vip[0]) ? intval($arr_vip[0]) : 0;
    $arr_vip[1] = isset($arr_vip[1]) ? intval($arr_vip[1]) : 0;
    $arr_vip[2] = isset($arr_vip[2]) ? $arr_vip[2] : 0;

    $_vip = $arr_vip[0];
    $_type_export = $arr_vip[1];
    $siteid = $arr_vip[2];

    // lưu tập trung tại bảng nv4_vi_crmbidding_comment trong đó phân loại sourceid = 3 là từ trang qlkh bao gồm cả user và vip,
    // siteid là id của site, nếu không có thì để 0, site =1 là vip bên dauthau.info, site = 2 là user bên dauthau.net
    if ($id > 0) {
        $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_comment SET note = :note, update_time=:update_time, vip=:vip, type_export=:type_export, siteid=:siteid WHERE id = ' . $id);
        $stmt->bindParam(':note', $content, PDO::PARAM_STR);
        $stmt->bindParam(':update_time', $time, PDO::PARAM_INT);
        $stmt->bindParam(':vip', $vip[0], PDO::PARAM_INT);
        $stmt->bindParam(':type_export', $type_export, PDO::PARAM_INT);
        $stmt->bindParam(':siteid', $siteid, PDO::PARAM_INT);

        $exc = $stmt->execute();
        if ($exc) {
            nv_jsonOutput([
                'res' => $nv_Lang->getModule('tb'),
                'mess' => $nv_Lang->getModule('update_comment_success'),
                'status' => 'success'
            ]);
        }
    } else {
        $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_comment (source, sourceid, post_id, timecreate, note, vip, type_export, siteid) VALUES (3, :sourceid, :post_id, ' . NV_CURRENTTIME . ', :note, :vip, :type_export, :siteid)');
        $stmt->bindParam(':sourceid', $id_user, PDO::PARAM_INT);
        $stmt->bindParam(':post_id', $admin_info['userid'], PDO::PARAM_INT);
        $stmt->bindParam(':note', $content, PDO::PARAM_STR);
        $stmt->bindParam(':vip', $_vip, PDO::PARAM_STR);
        $stmt->bindParam(':type_export', $_type_export, PDO::PARAM_INT);
        $stmt->bindParam(':siteid', $siteid, type: PDO::PARAM_INT);

        $exc = $stmt->execute();
        if ($exc) {
            nv_jsonOutput([
                'res' => $nv_Lang->getModule('tb'),
                'mess' => $nv_Lang->getModule('success_comment'),
                'status' => 'success'
            ]);
        }
    }
    nv_jsonOutput([
        'res' => $nv_Lang->getModule('tb'),
        'mess' => $nv_Lang->getModule('error_comment'),
        'status' => 'error'
    ]);
}

// Thêm/sửa liên kết MXH
if ($nv_Request->isset_request('profileUrl', 'post') && $nv_Request->isset_request('platform', 'post')) {
    $editId = $nv_Request->get_int('editId', 'post', 0);
    $profileName = $nv_Request->get_title('profileName', 'post', '');
    $profileUrl = $nv_Request->get_title('profileUrl', 'post', '');
    $platform = $nv_Request->get_title('platform', 'post', '');

    if (empty(SocialNetwork::tryFrom($platform)?->value)) {
        nv_jsonOutput([
            'status' => 'error',
            'code' => 1001,
            'mess' => $nv_Lang->getModule('social_error_platform'),
        ]);
    }

    $profileName = trim(strip_tags($profileName));
    if (empty($profileName)) {
        nv_jsonOutput([
            'status' => 'error',
            'code' => 1002,
            'mess' => $nv_Lang->getModule('social_error_name'),
        ]);
    }

    if (!nv_is_url($profileUrl)) {
        nv_jsonOutput([
            'status' => 'error',
            'code' => 1003,
            'mess' => $nv_Lang->getModule('social_error_url'),
        ]);
    }

    if ($editId > 0) {
        $profile = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_user_socials WHERE userid=' . $id_user . ' AND id=' . $editId)->fetch();
        if (empty($profile)) {
            nv_jsonOutput([
                'status' => 'error',
                'code' => 1004,
                'mess' => 'Error!',
            ]);
        } else {
            $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_user_socials SET platform=:platform, profile_name=:profile_name, profile_url=:profile_url, edit_time=:edit_time WHERE id=:id');
            $stmt->bindParam(':platform', $platform, PDO::PARAM_STR);
            $stmt->bindParam(':profile_name', $profileName, PDO::PARAM_STR);
            $stmt->bindParam(':profile_url', $profileUrl, PDO::PARAM_STR);
            $stmt->bindValue(':edit_time', NV_CURRENTTIME, PDO::PARAM_INT);
            $stmt->bindParam(':id', $editId, PDO::PARAM_INT);
            $exc = $stmt->execute();
            if ($exc) {
                nv_insert_logs(NV_LANG_DATA, $module_name, 'Update social profile', json_encode(['userid' => $id_user, 'profile_name' => $profileName, 'profile_url' => $profileUrl]), $admin_info['userid']);
                nv_jsonOutput([
                    'mess' => $nv_Lang->getModule('social_success_update'),
                    'data' => [
                        'platform' => SocialNetwork::tryFrom($platform)?->getLabel(),
                    ],
                    'status' => 'success'
                ]);
            }
        }
    } else {
        $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_user_socials (userid, platform, profile_name, profile_url , add_by, add_time) VALUES (:userid, :platform, :profile_name, :profile_url, :add_by, :add_time)');
        $stmt->bindParam(':userid', $id_user, PDO::PARAM_INT);
        $stmt->bindParam(':platform', $platform, PDO::PARAM_STR);
        $stmt->bindParam(':profile_name', $profileName, PDO::PARAM_STR);
        $stmt->bindParam(':profile_url', $profileUrl, PDO::PARAM_STR);
        $stmt->bindParam(':add_by', $admin_info['userid'], PDO::PARAM_INT);
        $stmt->bindValue(':add_time', NV_CURRENTTIME, PDO::PARAM_INT);
        $exc = $stmt->execute();
        if ($exc) {
            $newId = $db->lastInsertId();
            nv_insert_logs(NV_LANG_DATA, $module_name, 'Insert social profile', json_encode(['userid' => $id_user, 'profile_name' => $profileName, 'profile_url' => $profileUrl]), $admin_info['userid']);
            nv_jsonOutput([
                'mess' => $nv_Lang->getModule('social_success_insert'),
                'data' => [
                    'id' => $newId,
                    'platform' => SocialNetwork::tryFrom($platform)?->getLabel(),
                ],
                'status' => 'success'
            ]);
        }
    }

    nv_jsonOutput([
        'status' => 'error',
        'code' => 1005,
        'mess' => 'Error!',
    ]);
}

// Xóa social profile
if ($nv_Request->isset_request('profileIdSelected', 'post')) {
    $profileId = $nv_Request->get_int('profileIdSelected', 'post', 0);
    $profile = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_user_socials WHERE userid=' . $id_user . ' AND id=' . $profileId)->fetch();
    if (empty($profile)) {
        nv_jsonOutput([
            'status' => 'error',
            'code' => 2001,
            'mess' => 'Error!',
        ]);
    } else {
        $stmt = $db->prepare('DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_user_socials WHERE userid=:userid AND id=:id');
        $stmt->bindParam(':userid', $id_user, PDO::PARAM_INT);
        $stmt->bindParam(':id', $profileId, PDO::PARAM_INT);
        $exc = $stmt->execute();
        if ($exc) {
            nv_insert_logs(NV_LANG_DATA, $module_name, 'Delete social profile', json_encode(['userid' => $id_user, 'profile_id' => $profileId]), $admin_info['userid']);
            nv_jsonOutput([
                'mess' => $nv_Lang->getModule('social_success_delete'),
                'status' => 'success'
            ]);
        }
    }
}

// timeline
if ($nv_Request->isset_request('apiName', 'post') && $nv_Request->get_title('apiName', 'post', '') == 'listHtkh') {

    $_tmp_phone = $user_info['phone'] != '' ? $user_info['phone'] : '';
    if (preg_match('/(\d{9})$/', $user_info['phone'], $m)) {
        $_tmp_phone = $m[0];
    }
    $where = [];
    if (!empty($user_info['email'])) {
        $where[] = "email = " . $db->quote($user_info['email']) . "";
    }
    if (!empty($user_info['phone'])) {
        $where[] = "phone LIKE " . $db->quote('%' . $_tmp_phone . '');
    }

    $array_timline = [];
    // telepro
    $sql = "SELECT id,name,phone,email,timecall, status, note, thoigian_nghetuvan FROM " . NV_PREFIXLANG . "_crmbidding_telepro";
    if (!empty($where)) {
        $sql .= " WHERE " . implode(' OR ', $where) . " ORDER BY id DESC";
        $result = $db->query($sql);
        while ($_row = $result->fetch()) {
            $_row_tmp = [];
            $_row_tmp['type'] = 'call';
            $_row_tmp['time'] = $_row['timecall'];
            $_row_tmp['title'] = nv_date('H:i d/m/Y', $_row['timecall']) . " - Telepro - " . $_row['phone'];
            $_row_tmp['body'] = '<p>' . $_row['note'] . ' ' . $_row['status'] . '</p>';
            $_row_tmp['body'] .= '<p><strong>' . $nv_Lang->getModule('thoigian_nghetuvan') . ': </strong>' . $_row['thoigian_nghetuvan'] . '</p>';
            $href = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=telepro&amp;id=' . $_row['id'];
            $_row_tmp['body'] .= '<a href="' . $href . '" target="_blank">' . $nv_Lang->getModule('view_detai') . '</a>';
            insertTimeline($_row['timecall'], $_row_tmp, $array_timline);
        }
    }

    // chatgpt
    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_crmbidding_chatgpt_users WHERE userid = " . $id_user;
    $result = $db->query($sql);
    $chatGptUsers = [];
    while ($_row = $result->fetch()) {
        $chatGptUsers[$_row['id']] = $_row['last_activity'];
    }
    $chatGptIds = array_keys($chatGptUsers);
    if (!empty($chatGptIds)) {
        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_crmbidding_chatgpt WHERE crmbidding_chatgpt_id IN (" . implode(',', $chatGptIds) . ")";
        $result = $db->query($sql);
        while ($_row = $result->fetch()) {
            $lastActivity = $chatGptUsers[$_row['crmbidding_chatgpt_id']];
            $_row_tmp = [];
            $_row_tmp['type'] = 'chat';
            $_row_tmp['time'] = $lastActivity;
            $_row_tmp['title'] = nv_date('H:i d/m/Y', $lastActivity) . " - " . $nv_Lang->getModule('use_chatgpt');
            $_row_tmp['not_display'] = 1;

            $_row_tmp['body'] = '<p><strong>' . $nv_Lang->getModule('user_question') . '</strong>: ' . $_row['message_user'] . '</p>';
            $_row_tmp['body'] .= '<p><strong>' . $nv_Lang->getModule('ai_answer') . '</strong>: ' . $_row['message_ai'] . '</p>';
            $href = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=chatgpt_detail&amp;id=' . $_row['crmbidding_chatgpt_id'];
            $_row_tmp['body'] .= '<a href="' . $href . '" target="_blank">' . $nv_Lang->getModule('view_detai') . '</a>';
            insertTimeline($lastActivity, $_row_tmp, $array_timline);
        }
    }

    // mobifone
    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_crmbidding_mobiphone";
    if (!empty($user_info['phone'])) {
        $sql .= " WHERE sdt = " . $db->quote($user_info['phone']);
        $result = $db->query($sql);
        while ($_row = $result->fetch()) {
            $_row_tmp = [];
            $_row_tmp['type'] = 'call';
            $_row_tmp['time'] = $_row['thoi_gian_bat_dau'];
            $_row_tmp['title'] = nv_date('H:i d/m/Y', $_row['thoi_gian_bat_dau']) . " - Tổng đài Mobifone - " . $_row['sdt'];
            $_row_tmp['body'] = '<p>' . ($_row['loai_cuoc_goi'] == 1 ? 'Khách gọi đến' : 'Sale gọi đi') . ':';
            $_row_tmp['body'] .= "từ " . nv_date('H:i d/m/Y', $_row['thoi_gian_bat_dau']) . ' đến ' . nv_date('H:i d/m/Y', $_row['thoi_gian_ket_thuc']) . '.</p>';
            $_row_tmp['body'] .= '<p>' . 'Trạng thái cuộc gọi: ' . ($_row['trang_thai_cuoc_goi'] == 1 ? 'Cuộc gọi gặp' : 'Cuộc gọi nhỡ') . '</p>';
            $_row_tmp['body'] .= '<p>' . $nv_Lang->getModule('kq_khao_sat') . ':' . $_row['kq_khao_sat'] . '</p>';
            $href = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=mobiphone&amp;id=' . $_row['call_id'];
            $_row_tmp['body'] .= '<a href="' . $href . '" target="_blank">' . $nv_Lang->getModule('view_detai') . '</a>';
            insertTimeline($_row['thoi_gian_bat_dau'], $_row_tmp, $array_timline);
        }
    }

    // đăng ký thành viên
    $sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . " WHERE userid = " . $id_user;
    $result = $db->query($sql);
    while ($_row = $result->fetch()) {
        $_row_tmp = [];
        $_row_tmp['type'] = 'user';
        $_row_tmp['time'] = $_row['regdate'];
        $_row_tmp['title'] = nv_date('H:i d/m/Y', $_row['regdate']) . " - Đăng ký thành viên";

        if ($_row['active_obj'] == 'SYSTEM') {
            $_row_tmp['body'] = 'Hệ thống tự kích hoạt';
        } elseif ($_row['active_obj'] == 'EMAIL') {
            $_row_tmp['body'] = 'Kích hoạt qua email';
        } elseif (preg_match('/^OAUTH\:(.*?)$/', $_row['active_obj'], $m)) {
            $_row_tmp['body'] = sprintf('Kích hoạt qua Oauth %s', $m[1]);
        } else {
            $_row_tmp['body'] = 'Admin kích hoạt';
        }
        if ($_row['email_verification_time'] > 0) {
            $_row_tmp['body'] .= sprintf(' - Xác minh email lúc %s', nv_date('H:i d/m/Y', $_row['email_verification_time']));
        }
        // kiểm tra xem có qua affiliate k
        $affilicate = $db->query('SELECT tb2.username FROM ' . $db_config['prefix'] . '_elink_affiliate_set as tb1 INNER JOIN ' . NV_USERS_GLOBALTABLE . ' as tb2 ON tb1.pri_uid = tb2.userid WHERE tb1.pre_uid =' . $_row['userid'])->fetch();
        if (!empty($affilicate)) {
            $_row_tmp['body'] = '<br/> Đăng ký tài khoản qua affiliate của tài khoản: ' . $affilicate['username'];
        }
        insertTimeline($_row['regdate'], $_row_tmp, $array_timline);
    }

    // các đơn hàng khác
    $arr_where = [];
    $arr_where['AND'][] = [
        '=' => [
            'userid' => $id_user
        ]
    ];

    $infoAPI = array(
        'where' => $arr_where
    );

    $result = nv_call_api(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET, 'ListBiddingOrder', $infoAPI, 'bidding');
    $result_other = [];
    if ($result['status'] == 'success' and !empty($result['data'])) {
        $result_other = $result['data'];
        foreach ($result_other as $key => $array_order_other) {
            $_row_tmp = [];
            $_row_tmp['type'] = 'order';
            $_row_tmp['time'] = $array_order_other['add_time'];
            $_row_tmp['title'] = nv_date('H:i d/m/Y', $array_order_other['add_time']) . " - Đăng ký đơn hàng" . ($array_order_other['status'] == 4 ? '-Đã thanh toán' : '-Chưa thanh toán');
            $_row_tmp['body'] = '';

            $arr_where = [];
            $arr_where['AND'][] = [
                '=' => [
                    'order_id' => $array_order_other['id']
                ]
            ];

            $infoAPI = array(
                'where' => $arr_where
            );

            $resultCustomsLog = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'ListBiddingCustomsLog', $infoAPI, 'bidding');
            if (isset($resultCustomsLog['data'])) {
                foreach ($resultCustomsLog['data'] as $key => $row) {
                    $tmp_body = '<p>' . empty($row['is_renewal']) ? '- Đăng ký mới ' : '- Gia hạn ';
                    $tmp_body .= $row['numbers_year'] . ' năm ';
                    if ($row['vip'] == 11) {
                        $tmp_body .= 'VIP 1 Quốc tế';
                    } else if ($row['vip'] == 21) {
                        $tmp_body .= 'VIP 2 Quốc tế';
                    } else if ($row['vip'] == 99) {
                        $tmp_body .= 'VIEWEB';
                    } else if ($row['vip'] == 88) {
                        $tmp_body .= $row['type_export'] == 1 ? 'Gói X1 quá khứ' : 'Gói X1 hiện tại';
                    } else if ($row['vip'] == 77) {
                        $tmp_body .= 'Gói T0';
                    } else {
                        $tmp_body .= 'VIP' . $row['vip'];
                    }
                    $_row_tmp['body'] .= $tmp_body . ' </p>';
                }
            }

            if ($array_order_other['affiliate_userid'] > 0) {
                $ck = false;
                if ($array_order_other['promo_code'] != '' and $array_order_other['add_adminid'] == 0) {
                    $_row_tmp['body'] .= '<p>- Đăng ký qua mã giảm giá: ' . $array_order_other['promo_code'] . '</p>';
                } else {
                    $ck = true;
                    $_row_tmp['body'] .= '<p>- Đăng ký qua link affiliate';
                }
                $affilicate = $db->query('SELECT username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid =' . $array_order_other['affiliate_userid'])->fetch();
                if (!empty($affilicate)) {
                    $_row_tmp['body'] .= ' của tài khoản: ' . $affilicate['username'];
                }
                if ($ck) {
                    $_row_tmp['body'] .= '</p>';
                }
            }

            $href = $value['link_order'] = $view['order'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding' . '&amp;' . NV_OP_VARIABLE . '=payment&amp;vieworderid=' . $array_order_other['id'] . '&amp;userid=' . $array_order_other['userid'] . '&showheader=1';
            $_row_tmp['body'] .= '<a href="' . $href . '" target="_blank">' . $nv_Lang->getModule('view_detai') . '</a>';
            insertTimeline($array_order_other['add_time'], $_row_tmp, $array_timline);
        }
    }

    // click banner
    $bnclk_where = [];
    $bnclk_where['AND'][] = [
        '=' => [
            'c.userid' => $id_user
        ]
    ];
    $bnclk_params = [
        'where' => $bnclk_where
    ];

    $api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
    $api->setModule('bannersdt')
        ->setLang('vi')
        ->setAction('ListUsersClickBanners')
        ->setData($bnclk_params);
    $result = $api->execute();

    if ($result['status'] == 'success') {
        $history = $result['data'][$id_user]['click_history'];

        foreach ($history as $click_time => $row) {
            $_row_tmp = [
                'title' => nv_date('H:i d/m/Y', $click_time) . ' - ' . 'Click vào banner - ' . $row['title'],
                'type' => 'bannersdt_click',
                'time' => $click_time,
                'body' => '<p>Click vào banner ' . $row['title'] . ' trên trang ' . $row['click_ref'] . '</p><img style="max-width: 100%" src="' . URL_DTINFO . 'uploads/bannersdt/' . $row['file_name'] . '" />'
            ];
            insertTimeline($click_time, $_row_tmp, $array_timline);
        }
    }

    // Hiển thị email chết, search theo từng marketing
    $array_email = [];
    $array_sub_email = [];
    $array_email[] = trim($user_info['email']);

    foreach ($array_vip_dauthauinfo as $row) {
        if ($row['email'] != '') {
            $array_email[] = trim($row['email']);
        }
        if ($row['sub_email'] != '') {
            $array_sub_email = explode(',', $row['sub_email']);
            $array_sub_email = array_map('trim', $array_sub_email);
        }
    }

    $array_email = array_merge($array_sub_email, $array_email);

    $list_dead_mail_marketing = get_dead_email_from_marketing($array_email);
    foreach ($list_dead_mail_marketing as $dead_mail_marketing) {
        $_row_tmp = [
            'title' => nv_date('H:i d/m/Y', $dead_mail_marketing['date_added']) . ' - ' . $nv_Lang->getModule('emaildie') . ' (Marketing)',
            'type' => 'emaildie',
            'time' => $dead_mail_marketing['date_added'],
            'body' => '<strong>' . $dead_mail_marketing['email'] . '</strong>' . ':</strong> ' . nv_htmlspecialchars($dead_mail_marketing['comments'])
        ];
        insertTimeline($dead_mail_marketing['date_added'], $_row_tmp, $array_timline);
    }

    foreach ($ListBiddingCusomsEmailDie['data'] as $dead_email_dauthauinfo) {
        $_row_tmp = [
            'title' => nv_date('H:i d/m/Y', $dead_email_dauthauinfo['time_die']) . ' - ' . $nv_Lang->getModule('emaildie') . ' (ElasticSearch)',
            'type' => 'emaildie',
            'time' => $dead_email_dauthauinfo['time_die'],
            'body' => '<strong>' . $dead_email_dauthauinfo['email'] . '</strong>' . ':</strong> ' . nv_htmlspecialchars($dead_email_dauthauinfo['comments'])
        ];
        insertTimeline($dead_email_dauthauinfo['time_die'], $_row_tmp, $array_timline);
    }

    // mail marketing
    if (defined('MARKETING_API_CRM_KEY')) {
        $num = 0;
        foreach ($array_email as $email) {
            $num++;
            if ($email != '') {
                $page = 1;
                $run = 1;
                while ($run == 1 and (microtime(true) - NV_START_TIME < 10)) {
                    $run = 0;
                    // Lấy data email được gửi tới KH
                    $request = [
                        // Tham số bắt buộc
                        'apikey' => MARKETING_API_CRM_KEY,
                        'apisecret' => MARKETING_API_CRM_SECRET,
                        'action' => 'GetEmailSentOfEmail',
                        'module' => 'marketing',

                        // Tham số tùy chọn
                        'page' => $page,
                        'per_page' => 100,
                        'email' => $email,
                        'language' => 'vi'
                    ];
                    $NV_Http = new NukeViet\Http\Http($global_config, NV_TEMP_DIR);
                    $NV_Http->reset();
                    $args = [
                        'headers' => [
                            'Referer' => NV_MY_DOMAIN
                        ],
                        'body' => $request,
                        'timeout' => 10,
                        'sslverify' => false,
                        'decompress' => false
                    ];
                    $responsive = $NV_Http->post(MARKETING_API_URL, $args);
                    if (is_array($responsive) and empty(NukeViet\Http\Http::$error)) {
                        $email_marketing = !empty($responsive['body']) ? json_decode($responsive['body'], true) : [];
                        if (!empty($email_marketing)) {
                            if ($email_marketing['status'] == 'success') {
                                foreach ($email_marketing['rows'] as $item) {
                                    if ($item['is_read'] == 1) {
                                        $_row_tmp = [];
                                        $_row_tmp['type'] = 'mail';
                                        $_row_tmp['time'] = $item['date_read'];
                                        $_row_tmp['title'] = nv_date('H:i d/m/Y', $item['date_read']) . " - Đọc emailmarketing qua: " . $email;
                                        $_row_tmp['body'] = '';
                                        $_row_tmp['body'] .= 'Sự kiện: ' . $item['event'];
                                        $_row_tmp['body'] .= '<br/> Chiến dịch: ' . $item['campaign'];
                                        $_row_tmp['body'] .= '<br/> Tiêu đề mail: ' . $item['subject'];

                                        if (!empty($item['tracking_urls'])) {
                                            $item['urls'] = '';
                                            foreach ($item['tracking_urls'] as $link) {
                                                if ($link['hits'] > 0) {
                                                    $item['urls'] .= '<a href="' . $link['url'] . '">' . $link['url'] . '</a> - ' . $link['hits'] . ' click </br>';
                                                } else {
                                                    $_row_tmp['not_display'] = 1;
                                                }
                                            }
                                        }
                                        if ($item['urls'] != '') {
                                            $_row_tmp['body'] .= '<br/> Link đã click:<br/> ' . $item['urls'];
                                        }
                                        insertTimeline($item['date_read'], $_row_tmp, $array_timline);
                                    }
                                }
                            }

                            if (intval($email_marketing['pages']) > $page) {
                                $page++;
                                $run = 1;
                            }
                        }
                    } else {
                        trigger_error(print_r($NV_Http, true));
                    }
                }
            }
        }
    }

    // leads liên quan
    $where = [];
    if (!empty($user_info['email'])) {
        $where['OR'][] = [
            '=' => [
                'email' => $user_info['email']
            ]
        ];

        $where['OR'][] = [
            'FIND_IN_SET' => [
                'sub_email' => $user_info['email']
            ]
        ];
    }
    if (!empty($row['phone'])) {
        $where['OR'][] = [
            '=' => [
                'phone_search' => $_tmp_phone
            ]
        ];

        $where['OR'][] = [
            'FIND_IN_SET' => [
                'sub_phone_search' => $_tmp_phone
            ]
        ];
    }
    foreach ($array_vip_dauthauinfo as $row) {
        if (!empty($row['address_bill'])) {
            $where['OR'][] = [
                '=' => [
                    'email' => $row['address_bill']
                ]
            ];

            $where['OR'][] = [
                'FIND_IN_SET' => [
                    'sub_email' => $row['address_bill']
                ]
            ];
        }
        if (!empty($row['sub_email'])) {
            $_sub_email = explode(',', $row['sub_email']);
            foreach ($_sub_email as $key => $value) {
                $where['OR'][] = [
                    '=' => [
                        'email' => $value
                    ]
                ];

                $where['OR'][] = [
                    'FIND_IN_SET' => [
                        'sub_email' => $value
                    ]
                ];
            }
        }

        if (!empty($row['contact_phone'])) {
            $_tmp_phone2 = $row['contact_phone'];
            if (preg_match('/(\d{9})$/', $row['contact_phone'], $m)) {
                $_tmp_phone2 = $m[0];
            }
            $where['OR'][] = [
                '=' => [
                    'phone_search' => $_tmp_phone2
                ]
            ];

            $where['OR'][] = [
                'FIND_IN_SET' => [
                    'sub_phone_search' => $_tmp_phone2
                ]
            ];
        }
    }

    if (!empty($where)) {
        $params = [
            'page' => 1,
            'perpage' => 50,
            'where' => $where
        ];

        // GỌI API
        $List = nv_local_api('ListAllLeads', $params, $admin_info['username'], 'crmbidding');
        $ListAllLeads = json_decode($List, true);
        if (isset($ListAllLeads['data'])) {
            foreach ($ListAllLeads['data'] as $_row) {
                if (empty($_row['timecreate'])) {
                    $_row['timecreate'] = $_row['updatetime'];
                }
                $href = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $_row['id'] . '&amp;showheader=1';
                $_row_tmp = [];
                $_row_tmp['type'] = 'leads';
                $_row_tmp['time'] = $_row['timecreate'];
                $_row_tmp['title'] = nv_date('H:i d/m/Y', $_row['timecreate']) . " - Leads " . $array_groups_leads[$_row['source_leads']]['title'] . " - " . '<a href="' . $href . '" target="_blank">' . $_row['name'] . '</a>';
                $_row_tmp['body'] = '<p>- Trạng thái: ' . $nv_Lang->getModule('status' . $_row['status']) . '</p>';
                $_row_tmp['body'] .= '<p>- Cập nhật lần cuối: ' . nv_date('H:i d/m/Y', $_row['updatetime']) . '</p>';
                if (!empty($array_admin_id[$_row['caregiver_id']])) {
                    $_row_tmp['body'] .= '<p>- Người chăm sóc: ' . $array_admin_id[$_row['caregiver_id']]['username'] . '</p>';
                }

                if ($_row['status'] == 2) {
                    $_row_tmp['not_display'] = 0;
                } else {
                    $_row_tmp['not_display'] = 1;
                }

                insertTimeline($_row_tmp['time'], $_row_tmp, $array_timline);
            }
        }

        // GỌI API
        $List = nv_local_api('ListAllOpportunities', $params, $admin_info['username'], 'crmbidding');
        $ListAllOpportunities = json_decode($List, true);
        if (isset($ListAllOpportunities['data'])) {
            foreach ($ListAllOpportunities['data'] as $_row) {
                $_row_tmp = [];
                $href = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=opportunities_info&amp;id=' . $_row['id'] . '&amp;showheader=1';
                $_row_tmp['type'] = 'opportunities';
                $_row_tmp['time'] = $_row['timecreate'];
                $_row_tmp['title'] = nv_date('H:i d/m/Y', $_row['timecreate']) . " - Opportunities - " . '<a href="' . $href . '" target="_blank">' . $_row['name'] . '</a>';
                $_row_tmp['body'] = '<p>- Trạng thái: ' . $nv_Lang->getModule('status_opportunities' . $_row['status']) . '</p>';
                $_row_tmp['body'] .= '<p>- Cập nhật lần cuối: ' . nv_date('H:i d/m/Y', $_row['updatetime']) . '</p>';
                if (!empty($array_admin_id[$_row['caregiver_id']])) {
                    $_row_tmp['body'] .= '<p>- Người chăm sóc: ' . $array_admin_id[$_row['caregiver_id']]['username'] . '</p>';
                }

                $_row_tmp['not_display'] = 1;
                if ($_row['orderid'] != '') {
                    // API hóa ListBiddingCustomsLog
                    $infoAPI = [];
                    $where = [];
                    $where['AND'][] = [
                        'IN' => [
                            'order_id' => '(' . $_row['orderid'] . ')'
                        ]
                    ];

                    $infoAPI = [
                        'where' => $where
                    ];

                    $APIListBiddingCustomsLog = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'ListBiddingCustomsLog', $infoAPI, 'bidding');
                    if (isset($APIListBiddingCustomsLog['data'])) {
                        foreach ($APIListBiddingCustomsLog['data'] as $_order) {
                            $_row_tmp['body'] .= '<br/>- Đã tạo đơn hàng: VIP ' . $_order['vip'] . ' lúc:' . nv_date('H:i d/m/Y', $_order['addtime']);
                            $_row_tmp['not_display'] = 0;
                        }
                    }
                }
                insertTimeline($_row_tmp['time'], $_row_tmp, $array_timline);
            }
        }
    }

    // Timeline nhận mail sale gửi đến <EMAIL>
    $where = [];
    if (!empty($user_info['email'])) {
        $where[] = "FIND_IN_SET(" . $db->quote($user_info['email']) . ", send_to)";
    }
    foreach ($array_vip_dauthauinfo as $row) {
        if (!empty($row['address_bill'])) {
            $where[] = "FIND_IN_SET(" . $db->quote($row['address_bill']) . ", send_to)";
        }
        if (!empty($row['sub_email'])) {
            $_sub_email = explode(',', $row['sub_email']);
            foreach ($_sub_email as $key => $value) {
                $where[] = "FIND_IN_SET(" . $db->quote($value) . ", send_to)";
            }
        }
    }
    if (!empty($where)) {
        $sql = "SELECT email_id, mail_date, subject, sort_content, from_host, from_name, from_address
        FROM " . NV_PREFIXLANG . "_crmbidding_emails WHERE (" . implode(' OR ', $where) . ")";
        $result = $db->query($sql);
        while ($_row = $result->fetch()) {
            $_row_tmp = [];
            $_row_tmp['type'] = 'mail';
            $_row_tmp['time'] = $_row['mail_date'];
            $_row_tmp['title'] = nv_date('H:i d/m/Y', $_row['mail_date']) . " - " . $nv_Lang->getModule('timeline_recieve_email') . ' ';
            if (isset($array_admin_mails[$_row['from_address']])) {
                $_row_tmp['title'] .= $array_admin_mails[$_row['from_address']]['full_name'];
            } else {
                $_row_tmp['title'] .= $_row['from_address'];
                if (!empty($_row['from_name'])) {
                    $_row_tmp['title'] .= ' (' . $_row['from_name'] . ')';
                }
            }
            $href = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=imapdetail&amp;id=' . $_row['email_id'];

            $_body = [];
            $_body[] = '<b>' . $nv_Lang->getModule('timeline_mailsubject') . '</b>: ' . (empty($_row['subject']) ? 'No subject' : $_row['subject']);
            if (!empty($_row['sort_content'])) {
                $_body[] = '<b>' . $nv_Lang->getModule('timeline_mailscontent') . '</b>: ' . $_row['sort_content'];
            }
            if (!empty($_row['from_host'])) {
                $_body[] = '<b>' . $nv_Lang->getModule('timeline_mailfhost') . '</b>: ' . $_row['from_host'];
            }
            if (!empty($_row['from_name'])) {
                $_body[] = '<b>' . $nv_Lang->getModule('timeline_mailfname') . '</b>: ' . $_row['from_name'];
            }
            if (!empty($_row['from_address'])) {
                $_body[] = '<b>' . $nv_Lang->getModule('timeline_mailfaddress') . '</b>: ' . $_row['from_address'];
            }
            $_body[] = '<a href="' . $href . '" target="_blank">' . $nv_Lang->getModule('view_detai') . '</a>';
            $_row_tmp['body'] = implode('<br />', $_body);
            $_row_tmp['not_display'] = 1;

            insertTimeline($_row['mail_date'], $_row_tmp, $array_timline);
        }
    }

    $i = 1;
    $sole = 0;
    $arr_check = [];
    $dem = 1;

    if (!empty($array_timline)) {
        // show timeline
        krsort($array_timline, 1); // sắp xếp theo thứ tự giảm dần của thời gian

        $_array_timeline = [];
        foreach ($array_timline as $timline_i) {
            foreach ($timline_i as $timline) {
                $sole += empty($timline['not_display']) ? 1 : 0;
                $timline['class_li'] = '';
                $timline['id'] = $i;
                if ($sole % 2 == 0) {
                    $timline['class_li'] = 'timeline-inverted';
                }
                $timline['class'] = 'primary';
                if ($timline['type'] == 'call') {
                    $timline['icon'] = '<i class="fa fa-phone"></i>';
                } else if ($timline['type'] == 'user') {
                    $timline['class'] = 'info';
                    $timline['icon'] = '<i class="fa fa-user"></i>';
                } else if ($timline['type'] == 'order') {
                    $timline['icon'] = '<i class="fa fa-shopping-cart"></i>';
                    $timline['class'] = 'success';
                } else if ($timline['type'] == 'mail') {
                    $timline['icon'] = '<i class="fa fa-envelope-open"></i>';
                    $timline['class'] = 'warning';
                } else if ($timline['type'] == 'leads') {
                    $timline['icon'] = '<i class="fa fa-cube"></i>';
                    $timline['class'] = 'primary';
                } else if ($timline['type'] == 'opportunities') {
                    $timline['icon'] = '<i class="fa fa-cubes"></i>';
                    $timline['class'] = 'primary';
                } else if ($timline['type'] == 'emaildie') {
                    $timline['icon'] = '<i class="fa fa-envelope"></i>';
                    $timline['class'] = 'danger';
                } else if ($timline['type'] == 'chat') {
                    $timline['icon'] = '<i class="fa fa-comments-o"></i>';
                    $timline['class'] = 'warning';
                } else if ($timline['type'] == 'bannersdt_click') {
                    $timline['icon'] = '<i class="fa fa-picture-o"></i>';
                    $timline['class'] = 'warning';
                }

                $timline['display'] = (!empty($timline['not_display']) and $timline['not_display'] == 1) ? 'display: none;' : '';
                if (isset($timline['not_display']) && $timline['not_display'] == 1) {
                    $x = $timline['id'];
                    $arr_check[$timline['id']] = $timline;
                    if (count($arr_check) == 1) {
                        $timline['class_hiden_id'] = 'hide_not_important' . $dem;
                        $timline['class_hiden'] = 'hidentime';
                    }
                    $timline['class_hide'] = 'hide_not_important' . $dem;
                }

                if (isset($x) && ($x + 1 == $i)) {
                    $x = 1;
                    $arr_check = [];
                    $dem++;
                }

                $_array_timeline[] = $timline;
                $i++;
            }
        }

        $tpl = new \NukeViet\Template\NVSmarty();
        $tpl->setTemplateDir(get_module_tpl_dir('detail_customer_htkh.tpl'));
        $tpl->assign('LANG', $nv_Lang);
        $tpl->assign('MODULE_NAME', $module_name);
        $tpl->assign('MODULE_DATA', $module_data);
        $tpl->assign('OP', $op);
        $tpl->assign('ARRAY_TIMELINE', $_array_timeline);
        $contents = $tpl->fetch('detail_customer_htkh.tpl');
    }

    // trả về dữ liệu
    nv_jsonOutput([
        'status' => 'success',
        'data' => $contents,
    ]);
}

// danh sách bộ lọc
$infoAPI = [];
$infoAPI = array(
    'user_id' => $id_user
);
$ListBiddingFilter = nv_call_api(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET, 'ListBiddingFilter', $infoAPI, 'bidding');

// Danh sách đơn hàng chung
$sql = "SELECT * FROM " . NV_PREFIXLANG . "_bidding_orders_general WHERE userid=" . $id_user;
$query = $db->query($sql);
$static_order = $arr_orders = [];
$static_order['money'] = 0;
$static_order['money_pay'] = 0;
$static_order['price_reduce'] = 0;
$static_order['taxes_fees'] = 0;
$static_order['discount'] = 0;
$static_order['total_end'] = 0;
while ($row = $query->fetch()) {
    $row['order_date'] = nv_date('d/m/Y H:i:s', $row['order_date']);
    $row['total'] = number_format($row['total'], 0, ',', '.');
    $row['status'] = $arr_status[$row['status']];
    $arr_orders[] = $row;

    $static_order['money'] += $row['money'];
    $static_order['money_pay'] += $row['total'];
    $static_order['price_reduce'] += $row['price_reduce'];
    $static_order['taxes_fees'] += $row['taxes_fees'];
    $static_order['discount'] += $row['discount'];
    $static_order['total_end'] += $row['total_end'];
}

$tpl = new \NukeViet\Template\NVSmarty();
$tpl->setTemplateDir(get_module_tpl_dir('detail_customer_new.tpl'));
$tpl->assign('LANG', $nv_Lang);
$tpl->assign('MODULE_NAME', $module_name);
$tpl->assign('MODULE_DATA', $module_data);
$tpl->assign('OP', $op);

// thông tin thành viên
$user_info['fullname'] = nv_show_name_user($user_info['first_name'], $user_info['last_name'], $user_info['username']);
$user_info['birthday'] = ($user_info['birthday'] != 0) ? nv_date('d/m/Y', $user_info['birthday']) : 'N/A';
$user_info['phone'] = ($user_info['phone'] != 0) ? $user_info['phone'] : 'N/A';
$user_info['mst'] = ($user_info['mst'] != 0) ? $user_info['mst'] : 'N/A';
$tpl->assign('USER', $user_info);

// lấy thông tin ví tiền
$result = $db->query("SELECT * FROM " . $db_config['prefix'] . "_wallet_money WHERE userid=" . $id_user . " AND status=1 AND money_unit='VND'");
$wallet = $result->fetch();
$tpl->assign('MONEY', $wallet);

// lấy thông tin điểm
$result = $db->query("SELECT * FROM " . $db_config['prefix'] . "_points_customs WHERE userid=" . $id_user . " AND status=1");
$points = $result->fetch();
$tpl->assign('POINT', $points);

// Danh sách mạng xã hội
$socials = SocialNetwork::cases();
$tpl->assign('SOCIALS', $socials);
$user_social_query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_user_socials WHERE userid = ' . $id_user);
$count_profile = $user_social_query->rowCount();
$tpl->assign('DISPLAY_PROFILE', $count_profile > 0 ? 'table' : 'none');
$arr_profile = [];
while ($profile = $user_social_query->fetch()) {
    $profile['platform_label'] = SocialNetwork::from($profile['platform'])?->getLabel();
    $arr_profile[] = $profile;
}
$tpl->assign('ARR_PROFILE', $arr_profile);

// các cảnh báo email die
$infoAPI = array(
    'user_id' => $id_user,
    'group_by' => 'email'
);
$ListBiddingCusomsEmailDie = nv_call_api(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET, 'ListBiddingCustomsEmailDie', $infoAPI, 'bidding');
$ListBiddingCusomsEmailDie['data'] = $ListBiddingCusomsEmailDie['data'] ?? [];
$tpl->assign('EMAIL_DIE', $ListBiddingCusomsEmailDie['data']);

// Kiểm tra Khách VIP chưa tạo bộ lọc sau 1 tháng từ khi kích hoạt => Hiện cảnh báo cho Sale
// 2592000 => 1 tháng
$arr_warning_filter = [];
foreach ($array_vip_dauthauinfo as $k => $v) {
    if ($v['end_time'] >= NV_CURRENTTIME and in_array($v['vip'], $vip_is_filter)) {
        // Sau 1 tháng khách hàng chưa có bộ lọc hiện cảnh báo cho sale sau
        if (($v['from_time'] + 2592000) < NV_CURRENTTIME and $v['filter_titles'] == '') {
            $arr_warning_filter[] = $nv_Lang->getModule('vip' . $v['vip']);
        }
    }
}

sort($arr_warning_filter, SORT_NATURAL | SORT_FLAG_CASE);
$fullname_admin = nv_show_name_user($carrier_info['first_name'], $carrier_info['last_name'], $carrier_info['userid']);
if (defined('NV_IS_MODADMIN')) {
    $checss = md5($admin_info['username'] . '_' . $admin_info['level']);
} else {
    $checss = md5($id_user);
}

$link_filter = rtrim(URL_DTINFO, '/') . nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=filters&amp;userid=' . $id_user . '&amp;check_crm=1&amp;checkss=' . $checss, true);

$waring_filter = '';
if (!empty($arr_warning_filter)) {
    $waring_filter = sprintf($nv_Lang->getModule('waring_filter'), implode(', ', $arr_warning_filter), $fullname_admin, $link_filter);
}
$tpl->assign('WARING_FILTER', $waring_filter);

// Các bộ lọc k tìm được kết quả
$waring_filter_email = '';
if (isset($ListBiddingFilter['data'])) {
    $arr_warning_filter_mai = [];
    foreach ($ListBiddingFilter['data'] as $k => $v) {
        if ($v['stat_totalresult'] == 0 and $v['addtime'] + 2592000 < NV_CURRENTTIME) {
            $arr_warning_filter_mail[] = "<a href=" . $link_filter . ">" . $v['title'] . '</a> (từ ngày ' . date('d/m/Y', $v['addtime']) . ' đến nay)';
        }
    }
    if (!empty($arr_warning_filter_mail)) {
        $waring_filter_email = sprintf($nv_Lang->getModule('waring_filter_email'), implode(', ', $arr_warning_filter_mail), $fullname_admin);
    }
}
$tpl->assign('WARING_FILTER_EMAIL', $waring_filter_email);

$array_vip_all = [];
// danh sách gói dịch vụ dauthau.info
$array_vip_show = [];
$number = 1;
foreach ($array_vip_dauthauinfo as $key => $value) {
    $value['stt'] = $number;
    $value['title_vip'] = $value['vip'] == 55 ? ($value['type_export'] == 1 ? $nv_Lang->getModule('vip55_year') : $nv_Lang->getModule('vip55_month')) : $nv_Lang->getModule('vip' . $value['vip']);
    $value['class_status'] = ($value['status'] == 1) ? 'label label-primary' : 'label label-danger';
    $value['status'] = ($value['status'] == 1) ? $nv_Lang->getModule('hieuluc') : $nv_Lang->getModule('hethan');
    $value['fullname'] = $user_info['fullname'];
    $value['from_time'] = nv_date('d/m/Y H:i:s', $value['from_time']);
    $value['end_time'] = nv_date('d/m/Y H:i:s', $value['end_time']);
    $value['deal_time'] = nv_date('d/m/Y H:i:s', $value['deal_time']);
    $value['deal_price'] = number_format($value['deal_price'], 0, ',', '.') . CURRENCY_UNIT;
    $value['link_vip'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding' . '&amp;' . NV_OP_VARIABLE . '=cus_info&amp;id=' . $value['user_id'] . '-' . $value['vip'];
    $value['sum_viptime'] = nv_convertfromSec($value['sum_viptime']);
    if (isset($all_array_user_id_users[$value['admin_id']])) {
        $value['name_admin_id'] = nv_show_name_user($all_array_user_id_users[$value['admin_id']]['first_name'], $all_array_user_id_users[$value['admin_id']]['last_name'], $all_array_user_id_users[$value['admin_id']]['username']);
    }
    $array_vip_show[$key] = $value;
    $array_vip_all[$value['vip'] . '_' . $value['type_export'] . '_1']['key'] = $value['vip'] . '_' . $value['type_export'] . '_1';
    $array_vip_all[$value['vip'] . '_' . $value['type_export'] . '_1']['title'] = $value['vip'] == 55 ? ($value['type_export'] == 1 ? $nv_Lang->getModule('vip55_year') : $nv_Lang->getModule('vip55_month')) : $nv_Lang->getModule('vip' . $value['vip']);
    $array_vip_all[$value['vip'] . '_' . $value['type_export'] . '_1']['selected'] = ($value['vip'] . '_' . $value['type_export'] . '_1' == $comment_vip) ? 'selected="selected"' : '';
    $number++;
}
$tpl->assign('ARR_VIP_SHOW', $array_vip_show);

// show đơn hàng
$tpl->assign('ARR_ORDERS', $arr_orders);
$tpl->assign('STATIC_ORDER', $static_order);

$tpl->assign('ARRAY_VIP_ALL', $array_vip_all);

$contents = $tpl->fetch('detail_customer_new.tpl');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
