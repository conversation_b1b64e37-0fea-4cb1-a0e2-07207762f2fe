<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<div class="econtract-page">
    <!-- BEGIN: allow_add -->
    <a href="{CREATE_CONTRACT_URL}" class="btn btn-success">
        <em class="fa fa-plus"></em> {LANG.create_new_contract}
    </a>
    <!-- END: allow_add -->
    <a href="{DONE_URL}" class="btn btn-default pull-right">
        <em class="fa fa-star"></em> {LANG.econtract_done}
    </a>
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-group well" style="margin-top: 15px;">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" />
        <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" />
        <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
        <div class="row">
            <div class="col-md-20">
                <div class="form-group col-md-12">
                    <input class="form-control" type="text" value="{Q}" name="q" maxlength="255" placeholder="{LANG.econtract_search_placeholder_key}" />
                </div>
                <div class="form-group col-md-6">
                    <select class="form-control" multiple="multiple" name="vip_id[]">
                        <!-- BEGIN: search_vip -->
                        <option value="{VIP.id}" {VIP.selected}>{VIP.title}</option>
                        <!-- END: search_vip -->
                    </select>
                </div>
                <div class="form-group col-md-6">
                    <select class="form-control" multiple="multiple" name="uploader_id[]">
                        <!-- BEGIN: search_uploader_id -->
                        <option value="{UPLOADER_ID.userid}" {UPLOADER_ID.selected}>{UPLOADER_ID.title}</option>
                        <!-- END: search_uploader_id -->
                    </select>
                </div>
                <div class="form-group col-md-6 mt-1">
                    <select class="form-control" multiple="multiple" name="status[]">
                        <!-- BEGIN: search_status -->
                        <option value="{STATUS.id}" {STATUS.selected}>{STATUS.title}</option>
                        <!-- END: search_status -->
                    </select>
                </div>
                <div class="form-group col-md-6 mt-1">
                    <select class="form-control" multiple="multiple" name="stage[]">
                        <!-- BEGIN: search_stage -->
                        <option value="{STAGE.id}" {STAGE.selected}>{STAGE.title}</option>
                        <!-- END: search_stage -->
                    </select>
                </div>
                <div class="form-group col-md-6 mt-1">
                    <select class="form-control" name="term_changed">
                        <!-- BEGIN: search_term_changed -->
                        <option value="{TERM_CHANGED.id}" {TERM_CHANGED.selected}>{TERM_CHANGED.title}</option>
                        <!-- END: search_term_changed -->
                    </select>
                </div>
                <div class="form-group col-md-6 mt-1">
                    <select class="form-control" name="hard_copy">
                        <!-- BEGIN: search_hard_copy -->
                        <option value="{HARD_COPY.id}" {HARD_COPY.selected}>{HARD_COPY.title}</option>
                        <!-- END: search_hard_copy -->
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <input class="btn btn-primary" type="submit" value="{LANG.search_submit}" />
                    <a href="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&{NV_NAME_VARIABLE}={MODULE_NAME}&{NV_OP_VARIABLE}={OP}" class="btn btn-default">{LANG.clear_search}</a>
                </div>
            </div>
        </div>
    </form>

    <div class="table-responsive mt-3" style="margin-top: 15px;">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th class="mw100 text-center">{LANG.contract_no}</th>
                    <th class="w200 text-center">{LANG.updated_time}</th>
                    <th class="w150 text-center">{LANG.contract_attachment}</th>
                    <th class="w150 text-center">Giai đoạn</th>
                    <th class="w150 text-center">Tình trạng</th>
                    <th class="w100 text-center text-nowrap">{LANG.action}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop_row -->
                <tr>
                    <td>
                        <a <!-- BEGIN: action --> href="{ROW_LINK.url_detail}" <!-- END: action -->><strong>{ROW.contract_no}</strong></a>
                        <br>
                        {ROW.c_name}
                    </td>
                    <td class="text-center">{ROW.updated_at}</td>
                    <td class="text-center">
                        <a href="{ROW.url_download_contract}" data-toggle="tooltip" title="{LANG.click_download_attachment}" download="{ROW.contract_attachment}">
                            <em class="fa fa-paperclip"></em>
                            <span>{LANG.contract_attachment}</span>
                        </a>
                    </td>
                    <td class="text-center">{ROW.stage_label}</td>
                    <td class="text-center">
                        {ROW.status_label}
                        <!-- BEGIN: show_label_term_changed -->
                        <div class="text-danger"><em class="fa fa-info-circle"></em> Điều khoản hợp đồng đã thay đổi</div>
                        <!-- END: show_label_term_changed -->
                        <!-- BEGIN: show_label_order_cancel -->
                        <div class="text-danger"><em class="fa fa-info-circle"></em> {ORDER_CANCEL_LABEL}</div>
                        <!-- END: show_label_order_cancel -->
                        <!-- BEGIN: show_label_merged_contract -->
                        <div class="text-danger"><em class="fa fa-info-circle"></em> Hợp đồng đã được gộp chung</div>
                        <!-- END: show_label_merged_contract -->
                    </td>
                    <td class="text-center list-actions">
                        <!-- BEGIN: action_detail -->
                        <a href="{ROW_LINK.url_detail}" class="btn btn-xs btn-success"><em class="fa fa-file-pdf-o"></em> {LANG.view_detail}</a>
                        <a href="{ROW_LINK.url_preview}" class="btn btn-xs btn-default"><em class="fa fa-eye"></em> Xem trước HĐ</a>
                        <a href="{ROW_LINK.url_download}" class="btn btn-xs btn-warning"><em class="fa fa-cloud-download"></em> Tải xuống HĐ</a>
                        <a href="{ROW_LINK.url_upload}" class="btn btn-xs btn-danger"><em class="fa fa-cloud-upload"></em> Tải lên HĐ đã ký</a>
                        <a href="#" class="btn btn-xs btn-info" id="btn_sign_{ROW_ID}" data-id="{ROW_ID}"><em class="fa fa-paper-plane"></em> Trình lãnh đạo ký</a>
                        <!-- END: action_detail -->
                    </td>
                </tr>
                <!-- END: loop_row -->
            </tbody>

            <tfoot>
                <tr>
                    <td colspan="2"><em>{LANG.total_num_econtract}: <strong>{NUM_ITEMS}</strong></em></td>
                    <td class="text-right" colspan="5">
                        <!-- BEGIN: generate_page -->
                        {NV_GENERATE_PAGE}
                        <!-- END: generate_page -->
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>

    <!-- BEGIN: allow_add_modal -->
    <div class="modal fade" id="modalUploadAttachment" tabindex="-1" role="dialog" aria-labelledby="modalUploadAttachmentLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <form id="frmUploadAttachment" enctype="multipart/form-data">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" onclick="modalHide();" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h3 class="modal-title" id="modalUploadAttachmentLabel"><em class="fa fa-upload"></em> <strong>{LANG.upload_new_contract}</strong></h3>
                    </div>
                    <div class="modal-body">
                        <div class="form-group row">
                            <label class="col-md-6 text-right">{LANG.econtract_taxcode} <span class="text-danger">*</span></label>
                            <div class="col-md-18 input-group" style="padding-left: 5px;padding-right: 5px;">
                                <input type="text" name="tax_code" id="inpTaxCode" class="form-control">
                                <span class="input-group-btn">
                                    <button class="btn btn-info" type="button" id="btnGetDataCompany" data-type="taxcode" data-toggle="tooltip" data-placement="top" title="{LANG.get_company_info}">
                                        <em class="fa fa-undo fa-fix">&nbsp;</em>
                                    </button>
                                </span>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-6 text-right">{LANG.customer_name} <span class="text-danger">*</span></label>
                            <div class="col-md-18">
                                <input type="text" name="c_name" id="inpCName" class="form-control">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-6 text-right">{LANG.company_address} <span class="text-danger">*</span></label>
                            <div class="col-md-18">
                                <input type="text" name="c_address" id="inpCAddress" class="form-control">
                            </div>
                        </div>
                        <h4 class="text-center"><strong>{LANG.choose_order}</strong> <span class="text-danger">*</span></h4>
                        <div class="form-group row">
                            <div class="col-md-24">
                                <div id="tblOrders" class="hide" style="max-height: 300px;overflow-y: scroll;">
                                    <table class="table table-striped table-bordered">
                                        <thead>
                                            <tr>
                                                <th class="w50"></th>
                                                <th class="text-center w100">{LANG.order_id}</th>
                                                <th class="text-center w100">{LANG.vip}</th>
                                                <th>{LANG.fee_info}</th>
                                                <th class="text-center">{LANG.edit_time}</th>
                                                <th class="text-center w180">{LANG.econtract_no_if}</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                                <p id="txtOrders" class="text-center" style="margin-bottom: 0;"><small class="text-warning"><em>{LANG.choose_order_helptext}</em></small></p>
                            </div>
                        </div>
                        <hr>
                        <div class="form-group row">
                            <label class="col-md-6 text-right">{LANG.econtract_no} <span class="text-danger">*</span></label>
                            <div class="col-md-18">
                                <input type="text" name="contract_no" class="form-control">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-6 text-right">{LANG.econtract_choose_file} <span class="text-danger">*</span></label>
                            <div class="col-md-18">
                                <input type="file" name="contract_path" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer" style="text-align: center;">
                        <button type="button" class="btn btn-default" data-toggle="reset">{LANG.btn_reset}</button>
                        <button type="submit" class="btn btn-primary"><em class="fa fa-upload"></em> {LANG.do_upload}</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- END: allow_add_modal -->
    <div class="modal fade" id="modalEditContract" tabindex="-1" role="dialog" aria-labelledby="modalEditContractLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" onclick="modalHide();" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h3 class="modal-title" id="modalEditContractLabel"><em class="fa fa-edit"></em> <strong>{LANG.edit_econtract}: <span id="txtEditId">N/A</span></strong></h3>
                </div>
                <div class="modal-body"></div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modalViewContract" tabindex="-1" role="dialog" aria-labelledby="modalViewContractLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" onclick="modalHide();" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h3 class="modal-title" id="modalViewContractLabel"><em class="fa fa-file-o"></em> <strong>{LANG.view_econtract}: <span id="txtViewId">N/A</span></strong></h3>
                </div>
                <div class="modal-body"></div>
            </div>
        </div>
    </div>
</div>
<script>
    function getParamValue(param) {
        let url = new URL(window.location.href);
        return url.searchParams.get(param);
    }

    function addOrUpdateParam(param, value) {
        let url = new URL(window.location.href);
        url.searchParams.set(param, value); // Thêm hoặc cập nhật tham số
        window.history.replaceState({}, '', url.toString());
    }

    function removeParam(param) {
        let url = new URL(window.location.href);
        url.searchParams.delete(param); // Xóa tham số
        window.history.replaceState({}, '', url.toString());
    }

    function showModalView(id) {
        $('#modalEditContract').modal('hide');
        // Gọi ajax lấy thông tin theo id để show ra view
        $.ajax({
            url: '{OP_BASE_URL}&view_detail=1&econtract_id=' + id,
            method: 'GET',
            dataType: 'JSON',
            success: (res) => {
                removeParam('edit_id');
                addOrUpdateParam('view_id', id);
                $('#txtViewId').text('#' + id);
                if (res.status == 'success') {
                    $('#modalViewContract .modal-body').html(res.html);
                } else {
                    $('#modalViewContract .modal-body').html('<div class="text-center alert alert-danger">' + res.message + '</div>');
                }
                $('#modalViewContract').modal('show');
            },
            error: (err) => {
                console.log(err);
                alert('{LANG.error_unknown}');
            }
        });
        return false;
    }

    function showModalEdit(id) {
        $('#modalViewContract').modal('hide');
        // Gọi ajax lấy thông tin theo id để fill lên form edit
        $.ajax({
            url: '{OP_BASE_URL}&edit_form=1&econtract_id=' + id,
            method: 'GET',
            dataType: 'JSON',
            success: (res) => {
                removeParam('view_id');
                addOrUpdateParam('edit_id', id);
                $('#txtEditId').text('#' + id);
                if (res.status == 'success') {
                    $('#modalEditContract .modal-body').html(res.html);
                } else {
                    $('#modalEditContract .modal-body').html('<div class="text-center alert alert-danger">' + res.message + '</div>');
                }
                $('#modalEditContract').modal('show');
            },
            error: (err) => {
                console.log(err);
                alert('{LANG.error_unknown}');
            }
        });
        return false;
    }

    $(() => {
        $('select[name="vip_id[]"]').select2({
            placeholder: '{LANG.select_vip_package}',
        });
        $('select[name="uploader_id[]"]').select2({
            placeholder: '{LANG.select_uploader}',
        });
        $('select[name="status[]"]').select2({
            placeholder: '{LANG.select_status}',
        });
        $('select[name="stage[]"]').select2({
            placeholder: '{LANG.select_stage}',
        });

        if (getParamValue('edit_id')) {
            showModalEdit(getParamValue('edit_id'));
        }

        if (getParamValue('view_id')) {
            showModalView(getParamValue('view_id'));
        }

        $('#modalEditContract').on('hidden.bs.modal', function () {
            removeParam('edit_id');
            $('#txtEditId').text('N/A');
            $('body').removeAttr("style");
        });

        $('#modalViewContract').on('hidden.bs.modal', function () {
            removeParam('view_id');
            $('#txtViewId').text('N/A');
            $('body').removeAttr("style");
        });

        $('#modalUploadAttachment button[data-toggle="reset"]').on('click', (e) => {
            e.preventDefault();
            $('#frmUploadAttachment')[0].reset();
            $('#tblOrders').addClass('hide');
            $('#txtOrders').removeClass('hide');
        });

        var inProcessing = false;

        // Lấy thông tin công ty
        $('#btnGetDataCompany').on('click', () => {
            if (!inProcessing) {
                let tax_code = $('#inpTaxCode').val();
                if (tax_code != '') {
                    inProcessing = true;
                    $('#btnGetDataCompany').attr('disabled', 'disabled');
                    $('#btnGetDataCompany > em').addClass('wait');
                    $('#inpTaxCode').attr('disabled', 'disabled');
                    $.ajax({
                        url: '{AJAX_GETCOMINFO}' + tax_code,
                        method: 'GET',
                        dataType: 'JSON',
                        success: (res) => {
                            inProcessing = false;
                            $('#btnGetDataCompany').removeAttr('disabled');
                            $('#btnGetDataCompany > em').removeClass('wait');
                            $('#inpTaxCode').removeAttr('disabled');
                            if (res.status == 'success') {
                                $('#inpCName').val(res.data.c_name);
                                $('#inpCAddress').val(res.data.c_address);

                                // Hiện danh sách đơn hàng
                                let html_table_orders = '';
                                if (res.data.orders) {
                                    res.data.orders.forEach((order, i) => {
                                        html_table_orders += `<tr>
                                            <td class="text-center">
                                                <input type="checkbox" name="order_ids[]" value="` + order.id + `"/>
                                            </td>
                                            <td class="text-center">` + order.order_code + `</td>
                                            <td class="text-center">` + order.vip_titles + `</td>
                                            <td>
                                                <ul>
                                                    <li>{LANG.total_price}: <strong>` + order.money + `</strong></li>
                                                    <li>{LANG.duoc_giam}: <strong>` + order.discount + `</strong></li>
                                                    <li>{LANG.total_payment}: <strong>` + order.total + `</strong></li>
                                                    <li>{LANG.chiet_khau_rieng}: <strong>` + order.price_reduce + `</strong></li>
                                                    <li>{LANG.total_real_receive}: <strong>` + order.total_end + `</strong></li>
                                                </ul>
                                            </td>
                                            <td class="text-center">` + order.edit_time + `</td>
                                            <td class="text-center">` + (order.econtract_no != '' ? order.econtract_no : ``) + `</td>
                                        </tr>`;
                                    });
                                    $('#tblOrders tbody').html(html_table_orders);
                                    $('#tblOrders').removeClass('hide');
                                    $('#txtOrders').addClass('hide');
                                }
                            } else {
                                alert(res.message);
                                $('#inpCName').val('');
                                $('#inpCAddress').val('');
                                $('#tblOrders tbody').html('');
                                $('#tblOrders').addClass('hide');
                                $('#txtOrders').removeClass('hide');
                            }
                        },
                        error: (err) => {
                            console.log(err);
                            inProcessing = false;
                            $('#btnGetDataCompany').removeAttr('disabled');
                            $('#btnGetDataCompany > em').removeClass('wait');
                            $('#inpTaxCode').removeAttr('disabled');
                            alert('{LANG.error_unknown}');
                        }
                    });
                } else {
                    alert('{LANG.error_empty_taxcode}');
                }
            }
            return false;
        });

        // Bắt đầu tải form thông tin hợp đồng lên
        $('#frmUploadAttachment').on('submit', () => {
            if (!inProcessing) {
                inProcessing = true;
                let frmData = new FormData($('#frmUploadAttachment')[0]);
                $.ajax({
                    url: '{AJAX_SUBMIT_UPLOAD}',
                    method: 'POST',
                    data: frmData,
                    processData: false,
                    contentType: false,
                    success: (res) => {
                        inProcessing = false;
                        if (res.status == 'success') {
                            $('#frmUploadAttachment')[0].reset();
                            $('#tblOrders tbody').html('');
                            $('#tblOrders').addClass('hide');
                            $('#txtOrders').removeClass('hide');
                            alert(res.message);
                            window.location.reload();

                        } else if (res.status == 'error') {
                            alert(res.message);
                        }
                    },
                    error: (err) => {
                        console.log(err);
                        inProcessing = false;
                        alert('{LANG.error_unknown_zip}');
                    }
                });
            }
            return false;
        });

        // Gữi mail lãnh đạo ký kết hợp đồng
        $('a[id^="btn_sign_"]').on('click', function () {
            event.preventDefault();
            var contractId = $(this).data('id');
            if (!contractId) {
                alert('Không có hợp đồng để ký!');
                return;
            }
            $.ajax({
                url: '{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&{NV_NAME_VARIABLE}={MODULE_NAME}&{NV_OP_VARIABLE}={OP}',
                type: 'POST',
                data: {
                    sign_contract: '{TOKEN}',
                    contract_id: contractId
                },
                success: function (response) {
                    if (response.status == 'success') {
                        alert(response.message);
                        $('#btn_sign').addClass('disabled');
                    } else {
                        alert(response.message);
                    }
                },
                error: function () {
                    alert('Đã xảy ra lỗi khi gửi yêu cầu!');
                }
            });
        });
    });
</script>
<!-- END: main -->

<!-- BEGIN: view_detail -->
<div class="row">
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <colgroup>
                <col class="w150">
                <col>
            </colgroup>
            <tr>
                <td><strong>{LANG.econtract_taxcode}</strong></td>
                <td>{VIEW_ECONTRACT.tax_code}</td>
            </tr>
            <tr>
                <td><strong>{LANG.company_name}</strong></td>
                <td>{VIEW_ECONTRACT.c_name}</td>
            </tr>
            <tr>
                <td><strong>{LANG.company_address}</strong></td>
                <td>{VIEW_ECONTRACT.c_address}</td>
            </tr>
            <tr>
                <td><strong>{LANG.econtract_no}</strong></td>
                <td>{VIEW_ECONTRACT.contract_no} &nbsp;&nbsp;<a href="{VIEW_ECONTRACT.econtract_link}" class="label label-xs label-info" target="_blank">{LANG.view} <em class="fa fa-external-link"></em></a></td>
            </tr>
            <tr>
                <td><strong>{LANG.uploader_fullname}</strong></td>
                <td>
                    <a href="{VIEW_ECONTRACT.uploader_link}">{VIEW_ECONTRACT.uploader}</a>
                </td>
            </tr>
            <tr>
                <td><strong>{LANG.updated_time}</strong></td>
                <td>{VIEW_ECONTRACT.updated_at}</td>
            </tr>
        </table>
    </div>
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th class="text-center" colspan="4"><strong>{LANG.list_orders}</strong></th>
                </tr>
                <tr>
                    <th class="text-center w150">{LANG.order_id}</th>
                    <th class="text-center">{LANG.vip}</th>
                    <th class="text-center w150">{LANG.order_user}</th>
                    <th class="text-center w100"></th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop_order -->
                <tr>
                    <td class="text-center">{ORDER_ROW.order_code}</td>
                    <td class="text-center">{ORDER_ROW.vips}</td>
                    <td class="text-center">{ORDER_ROW.username}</td>
                    <td class="text-center">
                        <a href="{ORDER_ROW.link}" class="btn btn-xs btn-info" target="_blank">{LANG.view} <em class="fa fa-external-link"></em></a>
                    </td>
                </tr>
                <!-- END: loop_order -->
            </tbody>
        </table>
    </div>
</div>
<!-- BEGIN: show_btn_edit -->
<hr>
<div class="text-center">
    <a href="javascript:;" onclick="showModalEdit('{VIEW_ECONTRACT.id}');" class="btn btn-warning"><em class="fa fa-edit"></em> {LANG.edit}</a>
</div>
<!-- END: show_btn_edit -->
<!-- END: view_detail -->

<!-- BEGIN: edit_detail -->
<form id="frmEditContract" enctype="multipart/form-data">
    <input type="hidden" name="contract_id" value="{EDIT_ECONTRACT.id}">
    <div class="form-group row">
        <label class="col-md-6 text-right">{LANG.econtract_taxcode} <span class="text-danger">*</span></label>
        <div class="col-md-18" style="padding-left: 5px;padding-right: 5px;">
            <input type="text" value="{EDIT_ECONTRACT.tax_code}" name="tax_code" id="inpTaxCode" class="form-control" readonly>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-6 text-right">{LANG.company_name} <span class="text-danger">*</span></label>
        <div class="col-md-18">
            <input type="text" value="{EDIT_ECONTRACT.c_name}" name="c_name" id="inpCName" class="form-control">
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-6 text-right">{LANG.company_address} <span class="text-danger">*</span></label>
        <div class="col-md-18">
            <input type="text" value="{EDIT_ECONTRACT.c_address}" name="c_address" id="inpCAddress" class="form-control">
        </div>
    </div>
    <h4 class="text-center"><strong>{LANG.choose_order}</strong> <span class="text-danger">*</span></h4>
    <div class="form-group row">
        <div class="col-md-24">
            <div id="tblOrders" style="max-height: 300px;overflow-y: scroll;">
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th class="w50"></th>
                            <th class="text-center w100">{LANG.order_id}</th>
                            <th class="text-center w100">{LANG.vip}</th>
                            <th>{LANG.fee_info}</th>
                            <th class="text-center">{LANG.edit_time}</th>
                            <th class="text-center w180">{LANG.econtract_no_if}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- BEGIN: order_loop -->
                        <tr>
                            <td class="text-center">
                                <input type="checkbox" name="order_ids[]" value="{CUSTOMER_ORDER.id}" {CUSTOMER_ORDER.check} />
                            </td>
                            <td class="text-center">{CUSTOMER_ORDER.order_code}</td>
                            <td class="text-center">{CUSTOMER_ORDER.vip_titles}</td>
                            <td>
                                <ul>
                                    <li>{LANG.total_price}: <strong>{CUSTOMER_ORDER.money}</strong></li>
                                    <li>{LANG.duoc_giam}: <strong>{CUSTOMER_ORDER.discount}</strong></li>
                                    <li>{LANG.total_payment}: <strong>{CUSTOMER_ORDER.total}</strong></li>
                                    <li>{LANG.chiet_khau_rieng}: <strong>{CUSTOMER_ORDER.price_reduce}</strong></li>
                                    <li>{LANG.total_real_receive}: <strong>{CUSTOMER_ORDER.total_end}</strong></li>
                                </ul>
                            </td>
                            <td class="text-center">{CUSTOMER_ORDER.edit_time}</td>
                            <td class="text-center">{CUSTOMER_ORDER.econtract_no}</td>
                        </tr>
                        <!-- END: order_loop -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <hr>
    <div class="form-group row">
        <label class="col-md-6 text-right">{LANG.econtract_no} <span class="text-danger">*</span></label>
        <div class="col-md-18">
            <input type="text" value="{EDIT_ECONTRACT.contract_no}" name="contract_no" class="form-control">
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-6 text-right">
            {LANG.econtract_choose_file}
            <br><small class="text-info"><em>({LANG.if_not_update})</em></small>
        </label>
        <div class="col-md-18">
            <a class="btn btn-sm btn-info" href="{EDIT_ECONTRACT.url_download_contract}" data-toggle="tooltip" title="{LANG.click_download_attachment}" download="{EDIT_ECONTRACT.contract_attachment}">
                <em class="fa fa-file-zip-o"></em>
                <span><em>{LANG.uploaded_name}:</em> <strong>{EDIT_ECONTRACT.contract_attachment}</strong></span>
            </a>
            <input type="file" name="contract_path" class="form-control">
        </div>
    </div>
    <div class="modal-footer" style="text-align: center;">
        <button type="submit" class="btn btn-primary"><em class="fa fa-save"></em> {LANG.save}</button>
    </div>
</form>

<script>
    $(() => {
        var inProcessing = false;

        $('[data-toggle="tooltip"]').tooltip();
        // Bắt đầu tải form thông tin hợp đồng lên
        $('#frmEditContract').on('submit', (e) => {
            e.preventDefault();
            if (!inProcessing) {
                $('#frmEditContract button[type="submit"]').text('{LANG.in_progress}').attr('disabled', 'disabled');
                inProcessing = true;
                let frmData = new FormData($('#frmEditContract')[0]);
                $.ajax({
                    url: '{AJAX_SUBMIT_EDIT}',
                    method: 'POST',
                    data: frmData,
                    processData: false,
                    contentType: false,
                    success: (res) => {
                        $('#frmEditContract button[type="submit"]').html('<em class="fa fa-save"></em> {LANG.save}').removeAttr('disabled');
                        inProcessing = false;
                        alert(res.message);
                        if (res.status == 'success') {
                            window.location.href = '{OP_BASE_URL}';
                        }
                    },
                    error: (err) => {
                        console.log(err);
                        inProcessing = false;
                        $('#frmEditContract button[type="submit"]').html('<em class="fa fa-save"></em> {LANG.save}').removeAttr('disabled');
                        alert('{LANG.error_unknown_zip}');
                    }
                });
            }
            return false;
        });
    });
</script>
<!-- END: edit_detail -->