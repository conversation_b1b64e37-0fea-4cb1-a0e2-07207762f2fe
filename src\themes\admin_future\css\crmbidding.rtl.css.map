{"version": 3, "sources": ["../../../../scss/admin_future/crmbidding.scss", "crmbidding.css"], "names": [], "mappings": "AAAA;;;;;;;EAAA;AAkBA;EACI,WAAA;ACTJ;;ADYA;EACI,YAAA;ACTJ;;ADYA;EACI,YAAA;ACTJ;;ADYA;EACI,kBAAA;ACTJ;;ADYA;EACI,oBAAA;ACTJ;;ADWA;EACI,oBAAA;ACRJ;;ADUA;EACI,yBAAA;ACPJ;;ADUA;EACI,gBAAA;EACA,oBAAA;EACA,kBAAA;ACPJ;;ADUA;EACI,MAAA;EACA,SAAA;EACA,kBAAA;EACA,YAAA;EACA,UAAA;EACA,yBAAA;EACA,UAAA;EACA,oBAAA;ACPJ;;ADUA;EACI,yBAAA;EACA,kBAAA;EACA,gBAAA;ACPJ;;ADUA;;EAEI,YAAA;EACA,cAAA;ACPJ;;ADUA;EACI,WAAA;ACPJ;;ADUA;;EAEI,YAAA;EACA,cAAA;ACPJ;;ADUA;EACI,WAAA;ACPJ;;ADUA;EACI,UAAA;EACA,YAAA;EACA,mDAAA;EACA,kBAAA;EACA,aAAA;EACA,kBAAA;EACA,oDAAA;EACA,4CAAA;EACA,6CAAA;EACA,iBAAA;EACA,kBAAA;ACPJ;;ADUA;;EAEI,iBAAA;ACPJ;;ADUA;EACI,iCAAA;ACPJ;;ADUA;EACI,kBAAA;ACPJ;;ADUA;EACI,mBAAA;ACPJ;;ADUA;EACI,kBAAA;EACA,SAAA;EACA,WAAA;EACA,qBAAA;EACA,kCAAA;EACA,0DAAA;EACA,sDAAA;EACA,qCAAA;EACA,YAAA;ACPJ;;ADUA;EACI,kBAAA;EACA,SAAA;EACA,WAAA;EACA,qBAAA;EACA,kCAAA;EACA,0DAAA;EACA,sDAAA;EACA,qCAAA;EACA,YAAA;ACPJ;;ADUA;EACI,WAAA;EACA,WAAA;EACA,YAAA;EACA,iBAAA;EACA,gBAAA;EACA,kBAAA;EACA,kBAAA;EACA,SAAA;EACA,UAAA;EACA,mBAAA;EACA,yBAAA;EACA,YAAA;EACA,2BAAA;EACA,4BAAA;EACA,8BAAA;EACA,+BAAA;ACPJ;;ADUA;EACI,WAAA;ACPJ;;ADUA;EACI,qBAAA;EACA,uBAAA;EACA,YAAA;EACA,UAAA;ACPJ;;ADUA;EACI,qBAAA;EACA,uBAAA;EACA,YAAA;EACA,UAAA;ACPJ;;ADUA;EACI,oCAAA;ACPJ;;ADUA;EACI,oCAAA;ACPJ;;ADUA;EACI,oCAAA;ACPJ;;ADUA;EACI,oCAAA;ACPJ;;ADUA;EACI,oCAAA;ACPJ;;ADUA;EACI,aAAA;EACA,cAAA;ACPJ;;ADUA;;EAEI,gBAAA;ACPJ;;ADUA;EACI,eAAA;ACPJ;;ADUA;EACI,uCAAA;EACA,yBAAA;EACA,6BAAA;EACA,4BAAA;ACPJ;;ADUA;EACI,cAAA;EACA,yBAAA;EACA,qBAAA;EACA,eAAA;ACPJ;;ADUA;EACI,kBAAA;EACA,+BAAA;EAEA,uBAAA;EACA,wBAAA;ACRJ;;ADWA;EACI,yBAAA;EAAA,sBAAA;EAAA,iBAAA;ACRJ;;ADWA;;EAEI,oDAAA;EACA,yBAAA;ACRJ;;ADWA;;EAEI,UAAA;EACA,yBAAA;ACRJ;;ADWA;;EAEI,yBAAA;ACRJ;;ADWA;EACI,gBAAA;EACA,eAAA;ACRJ;;ADWA;EACI,gBAAA;EACA,qBAAA;EACA,yBAAA;EAAA,sBAAA;EAAA,iBAAA;ACRJ;;ADWA;EACI,YAAA;EACA,4BAAA;ACRJ;;ADWA;EACI,2BAAA;EACA,eAAA;EACA,yBAAA;EAAA,sBAAA;EAAA,iBAAA;EACA,gBAAA;EACA,eAAA;EACA,kBAAA;EACA,WAAA;EACA,aAAA;EACA,uBAAA;ACRJ;;ADWA;EACI,aAAA;EACA,0BAAA;EACA,WAAA;ACRJ;;ADWA;EACI,kBAAA;ACRJ;;ADWA;EACI,aAAA;EACA,eAAA;EACA,mBAAA;EACA,kBAAA;ACRJ;;ADWA;EACI,aAAA;ACRJ;;ADWA;EACI,cAAA;EACA,6BAAA;ACRJ;;ADWA;EACI,6CAAA;ACRJ;;ADWA;EACI,eAAA;EACA,iBAAA;EACA,eAAA;ACRJ;;ADWA;EACI,kBAAA;EACA,qBAAA;EACA,yBAAA;EACA,uBAAA;EACA,gBAAA;EACA,wCAAA;EACA,YAAA;EACA,kBAAA;EACA,oBAAA;EACA,mBAAA;EACA,wBAAA;ACRJ;;ADWA;EACI,gBAAA;ACRJ;;ADWA;EACI,WAAA;EACA,kBAAA;EACA,MAAA;EACA,YAAA;EACA,UAAA;EACA,YAAA;EACA,uBAAA;EACA,oCAAA;ACRJ;;ADWA;EACI,WAAA;EACA,kBAAA;EACA,MAAA;EACA,YAAA;EACA,WAAA;EACA,YAAA;EACA,uFAAA;EACA,gBAAA;ACRJ;;ADWA;EACI,WAAA;EACA,YAAA;ACRJ;;ADWA;EACI,iBAAA;EACA,kBAAA;EACA,kBAAA;ACRJ;;ADWA;;EAEI,oDAAA;EACA,yBAAA;ACRJ;;ADWA;;EAEI,UAAA;EACA,yBAAA;ACRJ;;ADWA;;EAEI,yBAAA;ACRJ;;ADWA;EACI,kBAAA;EACA,mDAAA;EACA,mBAAA;EACA,kBAAA;EACA,0BAAA;EACA,2BAAA;ACRJ;;ADWA;EACI,kBAAA;EACA,SAAA;EACA,WAAA;EACA,YAAA;EACA,WAAA;EACA,mBAAA;EACA,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,iBAAA;EACA,kBAAA;EACA,wCAAA;ACRJ;;ADWA;EACI,kBAAA;EACA,UAAA;EACA,UAAA;EACA,wCAAA;EACA,wBAAA;EACA,WAAA;ACRJ", "file": "crmbidding.rtl.css", "sourcesContent": ["/**\n * NukeViet Content Management System\n * @version 4.x\n * <AUTHOR> <<EMAIL>>\n * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved\n * @license GNU/GPL version 2 or any later version\n * @see https://github.com/nukeviet The NukeViet CMS GitHub project\n */\n\n// Define variable\n@import \"../functions\";\n@import \"variables\";\n@import \"variables-dark\";\n@import \"../../node_modules/bootstrap/scss/functions\";\n@import \"../../node_modules/bootstrap/scss/variables\";\n\n//@import \"../../src/themes/admin_default/css/crmbidding.css\";\n\n.w50 {\n    width: 50px;\n}\n\n.w160 {\n    width: 160px;\n}\n\n.w120 {\n    width: 120px;\n}\n\n.link_manager {\n    margin-bottom: 2px;\n}\n\n.btn__downleft i {\n    transition: all 0.3s;\n}\n.iconDown {\n    transform: rotate(0);\n}\n.textinfo {\n    overflow-wrap: break-word;\n}\n\n.timeline {\n    list-style: none;\n    padding: 20px 0 20px;\n    position: relative;\n}\n\n.timeline:before {\n    top: 0;\n    bottom: 0;\n    position: absolute;\n    content: \" \";\n    width: 3px;\n    background-color: #eeeeee;\n    left: 50%;\n    margin-left: -1.5px;\n}\n\n.timeline>li {\n    /* margin-bottom: 20px; */\n    position: relative;\n    transition: 0.5s;\n}\n\n.timeline>li:before,\n.timeline>li:after {\n    content: \" \";\n    display: table;\n}\n\n.timeline>li:after {\n    clear: both;\n}\n\n.timeline>li:before,\n.timeline>li:after {\n    content: \" \";\n    display: table;\n}\n\n.timeline>li:after {\n    clear: both;\n}\n\n.timeline>li>.timeline-panel {\n    width: 50%;\n    float: left;\n    border: 1px solid #d4d4d421;\n    border-radius: 2px;\n    padding: 20px;\n    position: relative;\n    /* -webkit-box-shadow: 0 1px 6px rgb(0 0 0 / 18%); */\n    /* box-shadow: 0 1px 6px rgb(0 0 0 / 18%); */\n    background: #e6e9f147;\n    border-right: none;\n    border-left: none;\n}\n\n.timeline>li.timeline-inverted+li:not(.timeline-inverted),\n.timeline>li:not(.timeline-inverted)+li.timeline-inverted {\n    margin-top: -16px;\n}\n\n.timeline>li.not_display {\n    /* margin-top: 10px !important; */\n}\n\n.timeline>li:not(.timeline-inverted) {\n    padding-right: 90px;\n}\n\n.timeline>li.timeline-inverted {\n    padding-left: 90px;\n}\n\n.timeline>li>.timeline-panel:before {\n    position: absolute;\n    top: 26px;\n    right: -15px;\n    display: inline-block;\n    border-top: 15px solid transparent;\n    border-left: 15px solid #d4d4d421;\n    border-right: 0 solid #d4d4d421;\n    border-bottom: 15px solid transparent;\n    content: \" \";\n}\n\n.timeline>li>.timeline-panel:after {\n    position: absolute;\n    top: 27px;\n    right: -14px;\n    display: inline-block;\n    border-top: 14px solid transparent;\n    border-left: 14px solid #d4d4d421;\n    border-right: 0 solid #d4d4d421;\n    border-bottom: 14px solid transparent;\n    content: \" \";\n}\n\n.timeline>li>.timeline-badge {\n    color: #fff;\n    width: 40px;\n    height: 40px;\n    line-height: 40px;\n    font-size: 1.4em;\n    text-align: center;\n    position: absolute;\n    top: 16px;\n    left: 51%;\n    margin-left: -30px;\n    background-color: #999999;\n    z-index: 100;\n    border-top-right-radius: 50%;\n    border-top-left-radius: 50%;\n    border-bottom-right-radius: 50%;\n    border-bottom-left-radius: 50%;\n}\n\n.timeline>li.timeline-inverted>.timeline-panel {\n    float: right;\n}\n\n.timeline>li.timeline-inverted>.timeline-panel:before {\n    border-left-width: 0;\n    border-right-width: 15px;\n    left: -15px;\n    right: auto;\n}\n\n.timeline>li.timeline-inverted>.timeline-panel:after {\n    border-left-width: 0;\n    border-right-width: 14px;\n    left: -14px;\n    right: auto;\n}\n\n.timeline-badge.primary {\n    background-color: #2e6da4 !important;\n}\n\n.timeline-badge.success {\n    background-color: #3f903f !important;\n}\n\n.timeline-badge.warning {\n    background-color: #f0ad4e !important;\n}\n\n.timeline-badge.danger {\n    background-color: #d9534f !important;\n}\n\n.timeline-badge.info {\n    background-color: #5bc0de !important;\n}\n\n.timeline-title {\n    margin-top: 0;\n    color: inherit;\n}\n\n.timeline-body>p,\n.timeline-body>ul {\n    margin-bottom: 0;\n}\n\n.timeline-body>p+p {\n    margin-top: 5px;\n}\n\n.panel__htkh {\n    /* box-shadow: 0px 4px 10px 3px #ccc; */\n    border: 1px solid #d9d9d9;\n    border-top-left-radius: 10px;\n    border-top-right-radius: 10px;\n}\n\n.panel__htkh .panel-heading {\n    color: #424242;\n    background-color: #dde4f5;\n    border-color: #dde4f5;\n    cursor: pointer;\n}\n\n#info {\n    /* padding: 9px; */\n    /* border: 1px solid #ffa8a8; */\n\n    /* max-height: 700px; */\n    /* overflow-y: scroll; */\n}\n\n.timeline-title {\n    user-select: none;\n}\n\n#info::-webkit-scrollbar-track,\n.info_product::-webkit-scrollbar-track {\n    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);\n    background-color: #f5f5f5;\n}\n\n#info::-webkit-scrollbar,\n.info_product::-webkit-scrollbar {\n    width: 3px;\n    background-color: #f5f5f5;\n}\n\n#info::-webkit-scrollbar-thumb,\n.info_product::-webkit-scrollbar-thumb {\n    background-color: #f79633;\n}\n\n#info .header {\n    overflow: hidden;\n    padding: 20px 0;\n}\n\n#info .show_log {\n    margin-bottom: 0;\n    display: inline-block;\n    user-select: none;\n}\n\n#info .show_log a span {\n    padding: 8px;\n    /* background: whitesmoke; */\n}\n\n.hidentime {\n    /* top: -16px !important; */\n    cursor: pointer;\n    user-select: none;\n    margin-top: 10px;\n    font-size: 15px;\n    text-align: center;\n    width: 100%;\n    display: flex;\n    justify-content: center;\n}\n\n.li__time_custom {\n    z-index: 9999;\n    padding-right: 0 !important;\n    width: 100%;\n}\n\n.li__time_custom:nth-child(1) i {\n    margin-bottom: 5px;\n}\n\n.li__time_custom i {\n    padding: 10px;\n    font-size: 18px;\n    background: #ebebeb;\n    border-radius: 50%;\n}\n\n.not_display1 {\n    display: none;\n}\n\n.timeline .showtimeline {\n    display: block;\n    animation: 0.6s fadeIn linear;\n}\n\n.timeline .showtimeline .timeline-panel {\n    background: #fdf3f37d;\n}\n\n.timeline-title .fa-ellipsis-h {\n    font-size: 19px;\n    margin-left: 8px;\n    margin-top: 6px;\n}\n\n.btn--social {\n    position: relative;\n    text-decoration: none;\n    text-transform: uppercase;\n    font-family: sans-serif;\n    overflow: hidden;\n    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);\n    border: none;\n    margin-bottom: 5px;\n    display: inline-flex;\n    align-items: center;\n    outline: none !important;\n}\n\n.btn--social i {\n    margin-right: 4px;\n}\n\n.btn--social:before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -15px;\n    width: 50%;\n    height: 100%;\n    transform: skew(46deg);\n    background: rgba(255, 255, 255, .2);\n}\n\n.btn--social:after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n    transition: 0.5s;\n}\n\n.btn--social:hover:after {\n    left: 100%;\n    border: none;\n}\n\n.support {\n    max-height: 500px;\n    overflow-x: hidden;\n    padding: 17px 20px;\n}\n\n.support::-webkit-scrollbar-track,\n.info_product::-webkit-scrollbar-track {\n    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);\n    background-color: #f5f5f5;\n}\n\n.support::-webkit-scrollbar,\n.info_product::-webkit-scrollbar {\n    width: 3px;\n    background-color: #f5f5f5;\n}\n\n.support::-webkit-scrollbar-thumb,\n.info_product::-webkit-scrollbar-thumb {\n    background-color: #f79633;\n}\n\n.row__support {\n    padding: 16px 10px;\n    border: 1px solid #cccccc73;\n    margin-bottom: 20px;\n    position: relative;\n    transition: transform 0.6s;\n    animation: fadeIn linear 1s;\n}\n\n.support__stt {\n    position: absolute;\n    top: -6px;\n    left: -6px;\n    height: 20px;\n    width: 20px;\n    background: #fdf500;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-weight: bold;\n    border-radius: 2px;\n    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);\n}\n\n.support__action_edit {\n    position: absolute;\n    top: -11px;\n    right: -3px;\n    box-shadow: 0 5px 5px rgb(0 0 0 / 20%);\n    padding: 4px 0px 4px 5px;\n    width: auto;\n}\n\n", "/**\n * NukeViet Content Management System\n * @version 4.x\n * <AUTHOR> <<EMAIL>>\n * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved\n * @license GNU/GPL version 2 or any later version\n * @see https://github.com/nukeviet The NukeViet CMS GitHub project\n */\n.w50 {\n  width: 50px;\n}\n\n.w160 {\n  width: 160px;\n}\n\n.w120 {\n  width: 120px;\n}\n\n.link_manager {\n  margin-bottom: 2px;\n}\n\n.btn__downleft i {\n  transition: all 0.3s;\n}\n\n.iconDown {\n  transform: rotate(0);\n}\n\n.textinfo {\n  overflow-wrap: break-word;\n}\n\n.timeline {\n  list-style: none;\n  padding: 20px 0 20px;\n  position: relative;\n}\n\n.timeline:before {\n  top: 0;\n  bottom: 0;\n  position: absolute;\n  content: \" \";\n  width: 3px;\n  background-color: #eeeeee;\n  left: 50%;\n  margin-left: -1.5px;\n}\n\n.timeline > li {\n  /* margin-bottom: 20px; */\n  position: relative;\n  transition: 0.5s;\n}\n\n.timeline > li:before,\n.timeline > li:after {\n  content: \" \";\n  display: table;\n}\n\n.timeline > li:after {\n  clear: both;\n}\n\n.timeline > li:before,\n.timeline > li:after {\n  content: \" \";\n  display: table;\n}\n\n.timeline > li:after {\n  clear: both;\n}\n\n.timeline > li > .timeline-panel {\n  width: 50%;\n  float: left;\n  border: 1px solid rgba(212, 212, 212, 0.1294117647);\n  border-radius: 2px;\n  padding: 20px;\n  position: relative;\n  /* -webkit-box-shadow: 0 1px 6px rgb(0 0 0 / 18%); */\n  /* box-shadow: 0 1px 6px rgb(0 0 0 / 18%); */\n  background: rgba(230, 233, 241, 0.2784313725);\n  border-right: none;\n  border-left: none;\n}\n\n.timeline > li.timeline-inverted + li:not(.timeline-inverted),\n.timeline > li:not(.timeline-inverted) + li.timeline-inverted {\n  margin-top: -16px;\n}\n\n.timeline > li.not_display {\n  /* margin-top: 10px !important; */\n}\n\n.timeline > li:not(.timeline-inverted) {\n  padding-right: 90px;\n}\n\n.timeline > li.timeline-inverted {\n  padding-left: 90px;\n}\n\n.timeline > li > .timeline-panel:before {\n  position: absolute;\n  top: 26px;\n  right: -15px;\n  display: inline-block;\n  border-top: 15px solid transparent;\n  border-left: 15px solid rgba(212, 212, 212, 0.1294117647);\n  border-right: 0 solid rgba(212, 212, 212, 0.1294117647);\n  border-bottom: 15px solid transparent;\n  content: \" \";\n}\n\n.timeline > li > .timeline-panel:after {\n  position: absolute;\n  top: 27px;\n  right: -14px;\n  display: inline-block;\n  border-top: 14px solid transparent;\n  border-left: 14px solid rgba(212, 212, 212, 0.1294117647);\n  border-right: 0 solid rgba(212, 212, 212, 0.1294117647);\n  border-bottom: 14px solid transparent;\n  content: \" \";\n}\n\n.timeline > li > .timeline-badge {\n  color: #fff;\n  width: 40px;\n  height: 40px;\n  line-height: 40px;\n  font-size: 1.4em;\n  text-align: center;\n  position: absolute;\n  top: 16px;\n  left: 51%;\n  margin-left: -30px;\n  background-color: #999999;\n  z-index: 100;\n  border-top-right-radius: 50%;\n  border-top-left-radius: 50%;\n  border-bottom-right-radius: 50%;\n  border-bottom-left-radius: 50%;\n}\n\n.timeline > li.timeline-inverted > .timeline-panel {\n  float: right;\n}\n\n.timeline > li.timeline-inverted > .timeline-panel:before {\n  border-left-width: 0;\n  border-right-width: 15px;\n  left: -15px;\n  right: auto;\n}\n\n.timeline > li.timeline-inverted > .timeline-panel:after {\n  border-left-width: 0;\n  border-right-width: 14px;\n  left: -14px;\n  right: auto;\n}\n\n.timeline-badge.primary {\n  background-color: #2e6da4 !important;\n}\n\n.timeline-badge.success {\n  background-color: #3f903f !important;\n}\n\n.timeline-badge.warning {\n  background-color: #f0ad4e !important;\n}\n\n.timeline-badge.danger {\n  background-color: #d9534f !important;\n}\n\n.timeline-badge.info {\n  background-color: #5bc0de !important;\n}\n\n.timeline-title {\n  margin-top: 0;\n  color: inherit;\n}\n\n.timeline-body > p,\n.timeline-body > ul {\n  margin-bottom: 0;\n}\n\n.timeline-body > p + p {\n  margin-top: 5px;\n}\n\n.panel__htkh {\n  /* box-shadow: 0px 4px 10px 3px #ccc; */\n  border: 1px solid #d9d9d9;\n  border-top-left-radius: 10px;\n  border-top-right-radius: 10px;\n}\n\n.panel__htkh .panel-heading {\n  color: #424242;\n  background-color: #dde4f5;\n  border-color: #dde4f5;\n  cursor: pointer;\n}\n\n#info {\n  /* padding: 9px; */\n  /* border: 1px solid #ffa8a8; */\n  /* max-height: 700px; */\n  /* overflow-y: scroll; */\n}\n\n.timeline-title {\n  user-select: none;\n}\n\n#info::-webkit-scrollbar-track,\n.info_product::-webkit-scrollbar-track {\n  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);\n  background-color: #f5f5f5;\n}\n\n#info::-webkit-scrollbar,\n.info_product::-webkit-scrollbar {\n  width: 3px;\n  background-color: #f5f5f5;\n}\n\n#info::-webkit-scrollbar-thumb,\n.info_product::-webkit-scrollbar-thumb {\n  background-color: #f79633;\n}\n\n#info .header {\n  overflow: hidden;\n  padding: 20px 0;\n}\n\n#info .show_log {\n  margin-bottom: 0;\n  display: inline-block;\n  user-select: none;\n}\n\n#info .show_log a span {\n  padding: 8px;\n  /* background: whitesmoke; */\n}\n\n.hidentime {\n  /* top: -16px !important; */\n  cursor: pointer;\n  user-select: none;\n  margin-top: 10px;\n  font-size: 15px;\n  text-align: center;\n  width: 100%;\n  display: flex;\n  justify-content: center;\n}\n\n.li__time_custom {\n  z-index: 9999;\n  padding-right: 0 !important;\n  width: 100%;\n}\n\n.li__time_custom:nth-child(1) i {\n  margin-bottom: 5px;\n}\n\n.li__time_custom i {\n  padding: 10px;\n  font-size: 18px;\n  background: #ebebeb;\n  border-radius: 50%;\n}\n\n.not_display1 {\n  display: none;\n}\n\n.timeline .showtimeline {\n  display: block;\n  animation: 0.6s fadeIn linear;\n}\n\n.timeline .showtimeline .timeline-panel {\n  background: rgba(253, 243, 243, 0.4901960784);\n}\n\n.timeline-title .fa-ellipsis-h {\n  font-size: 19px;\n  margin-left: 8px;\n  margin-top: 6px;\n}\n\n.btn--social {\n  position: relative;\n  text-decoration: none;\n  text-transform: uppercase;\n  font-family: sans-serif;\n  overflow: hidden;\n  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);\n  border: none;\n  margin-bottom: 5px;\n  display: inline-flex;\n  align-items: center;\n  outline: none !important;\n}\n\n.btn--social i {\n  margin-right: 4px;\n}\n\n.btn--social:before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: -15px;\n  width: 50%;\n  height: 100%;\n  transform: skew(46deg);\n  background: rgba(255, 255, 255, 0.2);\n}\n\n.btn--social:after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n  transition: 0.5s;\n}\n\n.btn--social:hover:after {\n  left: 100%;\n  border: none;\n}\n\n.support {\n  max-height: 500px;\n  overflow-x: hidden;\n  padding: 17px 20px;\n}\n\n.support::-webkit-scrollbar-track,\n.info_product::-webkit-scrollbar-track {\n  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);\n  background-color: #f5f5f5;\n}\n\n.support::-webkit-scrollbar,\n.info_product::-webkit-scrollbar {\n  width: 3px;\n  background-color: #f5f5f5;\n}\n\n.support::-webkit-scrollbar-thumb,\n.info_product::-webkit-scrollbar-thumb {\n  background-color: #f79633;\n}\n\n.row__support {\n  padding: 16px 10px;\n  border: 1px solid rgba(204, 204, 204, 0.4509803922);\n  margin-bottom: 20px;\n  position: relative;\n  transition: transform 0.6s;\n  animation: fadeIn linear 1s;\n}\n\n.support__stt {\n  position: absolute;\n  top: -6px;\n  left: -6px;\n  height: 20px;\n  width: 20px;\n  background: #fdf500;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  border-radius: 2px;\n  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);\n}\n\n.support__action_edit {\n  position: absolute;\n  top: -11px;\n  right: -3px;\n  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);\n  padding: 4px 0px 4px 5px;\n  width: auto;\n}\n\n/*# sourceMappingURL=crmbidding.css.map */\n"]}