<?php

/**
 *
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

$page_title = $nv_Lang->getModule('static_groups_lead');

$array_search = [];
$curent = nv_date('d/m/Y', NV_CURRENTTIME);
$curent_from = '01/' . nv_date('m/Y', NV_CURRENTTIME);
$array_search['time_from'] = $nv_Request->get_title('time_from', 'post,get', $curent_from);
$array_search['time_to'] = $nv_Request->get_title('time_to', 'post,get', $curent);
$array_search['admin_id'] = $nv_Request->get_int('admin_id', 'post,get', -1);
$array_search['type'] = $nv_Request->get_int('type', 'post,get', 1);
$array_search['method'] = $nv_Request->get_int('method', 'post,get', 1);

$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=';
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users';
$result = $db->query($sql);
$array_groups_users = [];
while ($row = $result->fetch()) {
    $row['config'] = json_decode($row['config'], true);
    $array_groups_users[$row['userid']] = $row;
}

/**
 * điều hành chung xem tất
 * Sales nào dc cấu hình xem tất thì dc xem còn lại chỉ xem của mình
 * trường hợp trưởng nhóm thì xem thêm các thành viên của nhóm, $arr_admin_view
 */
$arr_admin_view = $arr_admin_view_tmp = [];
if ($data_config['view_static'] == 2 and !defined('NV_IS_SPADMIN')) {
    if (isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 0) {
        if ($array_groups_users[$admin_info['userid']]['is_leader'] == 1) {
            $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $array_groups_users[$admin_info['userid']]['group_id'] . ' AND userid != ' . $admin_info['userid'];
            $_result = $db->query($_sql);
            while ($_row_groups_users = $_result->fetch()) {
                $arr_admin_view[$_row_groups_users['userid']] = $_row_groups_users['userid'];
            }
            $arr_admin_view[$admin_info['userid']] = $admin_info['userid'];
            $arr_admin_view_tmp = $arr_admin_view;
            if ($array_search['admin_id'] != 0) {
                $arr_admin_view = [];
            }
        } else {
            $array_search['admin_id'] = $admin_info['userid'];
        }
    }
}

// Xuất danh sách admin ra
$sql = 'SELECT userid, first_name, last_name, username, tb2.is_suspend FROM ' . NV_USERS_GLOBALTABLE . ' tb1 INNER JOIN ' . $db_config['prefix'] . '_authors tb2 ON tb1.userid = tb2.admin_id WHERE tb1.userid IN (SELECT admin_id FROM ' . NV_AUTHORS_GLOBALTABLE . ') AND tb1.active = 1 AND tb2.is_suspend = 0';
if ($data_config['view_static'] == 2 and !defined('NV_IS_SPADMIN')) {
    if (isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 0) {
        if (!empty($arr_admin_view_tmp)) {
            $sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arr_admin_view_tmp) . ') ';
        } else {
            $sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $admin_info['userid'];
        }
    }
}
$array_admin = $array_admin_id = [];
$result = $db->query($sql);
while ($_user_info = $result->fetch()) {
    $array_admin[$_user_info['userid']] = $_user_info;
    $array_admin_id[$_user_info['userid']] = $_user_info['userid'];
}

$array_admin_except = [];
$result_admin = $db->query('SELECT admin_id FROM nv4_authors');
while ($_admin_info = $result_admin->fetch()) {
    $array_admin_except[$_admin_info['admin_id']] = $_admin_info['admin_id'];
}

// các tài khoản test mà k muốn thống kê đưa vào đây, lấy theo nhóm test https://id.dauthau.net/qtdtvnds/index.php?language=vi&nv=users&op=groups&userlist=19
$sql = 'SELECT userid FROM ' . NV_GROUPS_GLOBALTABLE . '_users WHERE group_id = 19';
$result = $db->query($sql);
while ($_gr_user_info = $result->fetch()) {
    $array_admin_except[$_gr_user_info['userid']] = $_gr_user_info['userid'];
}

if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_from'], $m)) {
    $sfrom = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
} else {
    $sfrom = mktime(0, 0, 0, date('n'), 1, date('Y'));
}
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_to'], $m)) {
    $sto = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
} else {
    $sto = mktime(23, 59, 59, date('n'), date('j'), date('Y'));
}

// các nguồn leads
$sql = "SELECT * FROM nv4_vi_crmbidding_groups_leads WHERE active=1 ORDER BY weight ASC";
$array_groups_leads = $nv_Cache->db($sql, 'id', $module_data);

// thêm loại khách tự đăng kí
$array_groups_leads[0]['id'] = 0;
$array_groups_leads[0]['title'] = $nv_Lang->getModule('groups_lead0');

if ($array_search['type'] == 1) {
    // Xuôi: Tính từ leads làm điểm bắt đầu, các dữ liệu khác tính lần lượt theo
    foreach ($array_groups_leads as $groups_leads) {
        $groups_leads_id = $groups_leads['id'];
        $array_leads_all = $arr_vip = $array_oppotunities = $array_order = $array_order_id = [];
        if ($groups_leads_id > 0) {
            $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_leads WHERE active=1 AND timecreate >= ' . $sfrom . ' AND timecreate <=' . $sto . ' AND source_leads = ' . $groups_leads_id;
            if (!empty($arr_admin_view)) {
                $sql .= ' AND caregiver_id IN (' . implode(',', $arr_admin_view) . ') ';
            } else if ($array_search['admin_id'] > 0) {
                $sql .= ' AND caregiver_id = ' . $array_search['admin_id'];
            }
            $result = $db->query($sql);
            while ($row = $result->fetch()) {
                $array_leads_all[$row['id']] = $row['id'];
            }
            if (!empty($array_leads_all)) {
                $_sql = 'SELECT id, orderid FROM ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities WHERE active=1 AND leadsid IN (' . implode(',', $array_leads_all) . ') ';
                $_result = $db->query($_sql);

                while ($_row = $_result->fetch()) {
                    $_row['orderid'] = explode(',', $_row['orderid']);
                    foreach ($_row['orderid'] as $orderid) {
                        if ($orderid > 0) {
                            $array_order_id[$orderid] = $orderid;
                        }
                    }
                    $array_oppotunities[$_row['id']] = $_row['id'];
                }
            }
        } else {
            $_sql = 'SELECT id, orderid FROM ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities WHERE active=1 AND is_system = 1 AND timecreate >= ' . $sfrom . ' AND timecreate <=' . $sto . '';

            if (!empty($arr_admin_view)) {
                $_sql .= ' AND caregiver_id IN (' . implode(',', $arr_admin_view) . ') ';
            } else if ($array_search['admin_id'] > 0) {
                $_sql .= ' AND caregiver_id = ' . $array_search['admin_id'];
            }
            $_result = $db->query($_sql);
            while ($_row = $_result->fetch()) {
                $_row['orderid'] = explode(',', $_row['orderid']);
                foreach ($_row['orderid'] as $orderid) {
                    if ($orderid > 0) {
                        $array_order_id[$orderid] = $orderid;
                    }
                }

                $array_oppotunities[$_row['id']] = $_row['id'];
            }
        }

        $arr_money = [];
        $arr_money['money'] = 0;
        $arr_money['discount'] = 0;
        $arr_money['total'] = 0;
        $arr_money['price_reduce'] = 0;
        $arr_money['total_end'] = 0;
        $arr_money['discount_excess'] = 0;
        if (!empty($array_order_id)) {

            $where = [];
            $where['AND'][] = [
                'IN' => [
                    'id' => '(' . implode(',', $array_order_id) . ')'
                ]
            ];

            $tmp_orders_dauthauinfo = fetchAllOrders($where);
            $tmp_orders_dauthaunet = fetchAllOrdersFromDauThauNet($where);
            $tmp_orders = array_merge($tmp_orders_dauthauinfo, $tmp_orders_dauthaunet);

            foreach ($tmp_orders as $_row) {
                $array_order[$_row['id']] = $_row['id'];
                if ($_row['status'] == 4) {
                    $arr_money['money'] += $_row['money'];
                    $arr_money['discount'] += $_row['discount'];
                    $arr_money['total'] += $_row['total'];
                    $arr_money['price_reduce'] += $_row['price_reduce'];
                    $arr_money['total_end'] += $_row['total_end'];
                    $arr_money['discount_excess'] += $_row['discount_excess'] ?? 0;
                }
            }
            unset($tmp_orders_dauthauinfo);
            unset($tmp_orders_dauthaunet);
            unset($tmp_orders);

            if (!empty($array_order)) {

                $where_CustomsLog = [];
                $where_CustomsLog['AND'][] = [
                    '=' => [
                        'status' => 1
                    ]
                ];
                $where_CustomsLog['AND'][] = [
                    'IN' => [
                        'order_id' => '(' . implode(',', $array_order) . ')'
                    ]
                ];

                $api_CustomsLog = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
                $api_CustomsLog->setModule('bidding')
                    ->setLang('vi')
                    ->setAction('ListBiddingCustomsLog')
                    ->setData([
                    'where' => $where_CustomsLog
                ]);
                $result_CustomsLog = $api_CustomsLog->execute();
                if ($result_CustomsLog['status'] == 'success') {
                    foreach ($result_CustomsLog['data'] as $_row) {
                        $arr_vip[$_row['order_id']] = $_row['id'];
                    }
                }
                // $_sql = 'SELECT id, vip, order_id FROM ' . NV_PREFIXLANG . '_bidding_customs_log WHERE status = 1 AND order_id IN (' . implode(',', $array_order) . ') ';
            }
        }

        $array_groups_leads[$groups_leads_id]['num_leads'] = sizeof($array_leads_all);
        $array_groups_leads[$groups_leads_id]['num_opportunities'] = sizeof($array_oppotunities);
        $array_groups_leads[$groups_leads_id]['num_order'] = sizeof($array_order);
        $array_groups_leads[$groups_leads_id]['order_curent'] = $array_order;
        $array_groups_leads[$groups_leads_id]['num_vip'] = sizeof($arr_vip);
        $array_groups_leads[$groups_leads_id]['vip_curent'] = $arr_vip;
        $array_groups_leads[$groups_leads_id]['money'] = $arr_money['money'];
        $array_groups_leads[$groups_leads_id]['discount'] = $arr_money['discount'];
        $array_groups_leads[$groups_leads_id]['total'] = $arr_money['total'];
        $array_groups_leads[$groups_leads_id]['price_reduce'] = $arr_money['price_reduce'];
        $array_groups_leads[$groups_leads_id]['total_end'] = $arr_money['total_end'];
        $array_groups_leads[$groups_leads_id]['discount_excess'] = $arr_money['discount_excess'];
    }
} else {
    /*
     * Ngược: bao gồm
     * - Các leads dc tạo trong tháng
     * - Các leads trước đó, nhưng tạo ra cơ hội, đơn hàng trong tháng này
     */
    $array_leads_current = $array_order_curent = $array_oppo_current_lead_id = $array_oppo_current = [];
    // các đơn hàng trong tháng
    $where = [];
    $where['AND'][] = [
        'NOT IN' => [
            'userid' => '(' . implode(',', $array_admin_except) . ')'
        ]
    ];
    $where['AND'][] = [
        '>=' => [
            'add_time' => $sfrom
        ]
    ];
    $where['AND'][] = [
        '<=' => [
            'add_time' => $sto
        ]
    ];
    $where['AND'][] = [
        '=' => [
            'is_expired' => 0
        ]
    ];

    if (!empty($arr_admin_view)) {
        $where['AND'][] = [
            'IN' => [
                'caregiver_id' => '(' . implode(',', $arr_admin_view) . ')'
            ]
        ];
    } else if ($array_search['admin_id'] > 0) {
        $where['AND'][] = [
            '=' => [
                'caregiver_id' => $array_search['admin_id']
            ]
        ];
    }

    $tmp_orders_dauthauinfo = fetchAllOrders($where);
    $tmp_orders_dauthaunet = fetchAllOrdersFromDauThauNet($where);
    $tmp_orders = array_merge($tmp_orders_dauthauinfo, $tmp_orders_dauthaunet);

    foreach ($tmp_orders as $row) {
        $array_order_curent[$row['id']] = $row['id'];
        $array_groups_leads[$row['source_leads']]['order_curent'][$row['id']] = $row['id'];
    }
    unset($tmp_orders_dauthauinfo);
    unset($tmp_orders_dauthaunet);
    unset($tmp_orders);

    // các đơn hàng vip đã thanh toán trong tháng
    $where = [];
    $where['AND'][] = [
        'NOT IN' => [
            'userid' => '(' . implode(',', $array_admin_except) . ')'
        ]
    ];
    $where['AND'][] = [
        '>=' => [
            'static_time' => $sfrom
        ]
    ];
    $where['AND'][] = [
        '<=' => [
            'static_time' => $sto
        ]
    ];
    $where['AND'][] = [
        '=' => [
            'status' => 4
        ]
    ];
    if (!empty($arr_admin_view)) {
        $where['AND'][] = [
            'IN' => [
                'caregiver_id' => '(' . implode(',', $arr_admin_view) . ')'
            ]
        ];
    } else if ($array_search['admin_id'] > 0) {
        $where['AND'][] = [
            '=' => [
                'caregiver_id' => $array_search['admin_id']
            ]
        ];
    }

    $array_vip_curent = [];
    $tmp_orders_dauthauinfo = fetchAllOrders($where);
    $tmp_orders_dauthaunet = fetchAllOrdersFromDauThauNet($where);
    $tmp_orders = array_merge($tmp_orders_dauthauinfo, $tmp_orders_dauthaunet);

    foreach ($tmp_orders as $row) {
        $array_vip_curent[$row['id']] = $row['id'];
        $array_groups_leads[$row['source_leads']]['vip_curent'][$row['id']] = $row['id'];

        if (!isset($array_groups_leads[$row['source_leads']]['money'])) {
            $array_groups_leads[$row['source_leads']]['money'] = $row['money'];
            $array_groups_leads[$row['source_leads']]['discount'] = $row['discount'];
            $array_groups_leads[$row['source_leads']]['total'] = $row['total'];
            $array_groups_leads[$row['source_leads']]['price_reduce'] = $row['price_reduce'];
            $array_groups_leads[$row['source_leads']]['total_end'] = $row['total_end'];
            $array_groups_leads[$row['source_leads']]['discount_excess'] = $row['discount_excess'];
        } else {
            $array_groups_leads[$row['source_leads']]['money'] += $row['money'];
            $array_groups_leads[$row['source_leads']]['discount'] += $row['discount'];
            $array_groups_leads[$row['source_leads']]['total'] += $row['total'];
            $array_groups_leads[$row['source_leads']]['price_reduce'] += $row['price_reduce'];
            $array_groups_leads[$row['source_leads']]['total_end'] += $row['total_end'];
            $array_groups_leads[$row['source_leads']]['discount_excess'] += $row['discount_excess'];
        }
    }

    unset($tmp_orders_dauthauinfo);
    unset($tmp_orders_dauthaunet);
    unset($tmp_orders);

    $array_order_prev = $array_oppo_prev = [];
    if (!empty($array_order_curent) and !empty($array_vip_curent)) {
        // các đơn hàng tháng trước:là các đơn k phải tạo từ tháng này, và thuộc các đơn đã thanh toán trong tháng này
        $where = [];
        $where['AND'][] = [
            'NOT IN' => [
                'id' => '(' . implode(',', $array_order_curent) . ')'
            ]
        ];

        $where['AND'][] = [
            'IN' => [
                'id' => '(' . implode(',', $array_vip_curent) . ')'
            ]
        ];

        $tmp_orders = fetchAllOrders($where);
        foreach ($tmp_orders as $row) {
            $array_order_prev[$row['id']] = $row['id'];
            $array_groups_leads[$row['source_leads']]['order_prev'][$row['id']] = $row['id'];
        }
        unset($tmp_orders);
    }
    // Các leads dc tạo trong tháng
    $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_leads WHERE active=1 AND timecreate >= ' . $sfrom . ' AND timecreate <=' . $sto . ' AND user_id NOT IN (' . implode(',', $array_admin_except) . ')';
    if (!empty($arr_admin_view)) {
        $sql .= ' AND caregiver_id IN (' . implode(',', $arr_admin_view) . ') ';
    } else if ($array_search['admin_id'] > 0) {
        $sql .= ' AND caregiver_id = ' . $array_search['admin_id'];
    }

    $result = $db->query($sql);

    while ($row = $result->fetch()) {
        $array_leads_current[$row['id']] = $row['id'];
        $array_groups_leads[$row['source_leads']]['leadscurent'][$row['id']] = $row['id'];
    }

    // Các opp dc tạo trong tháng
    $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities WHERE active=1 AND timecreate >= ' . $sfrom . ' AND timecreate <=' . $sto . ' AND user_id NOT IN (' . implode(',', $array_admin_except) . ')';
    if (!empty($arr_admin_view)) {
        $sql .= ' AND caregiver_id IN (' . implode(',', $arr_admin_view) . ') ';
    } else if ($array_search['admin_id'] > 0) {
        $sql .= ' AND caregiver_id = ' . $array_search['admin_id'];
    }

    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        $array_oppo_current[$row['id']] = $row['id'];
        $array_oppo_current_lead_id[$row['leadsid']] = $row['leadsid'];
        $array_groups_leads[$row['source_leads']]['oppocurent'][$row['id']] = $row['id'];

        if ($row['source_leads'] != 0) {
            $array_groups_leads[$row['source_leads']]['oppocurent'][$row['id']] = $row['id'];

            // các cơ hội mà trùng với đơn hàng của khách tự đăng ký mà có nguồn lead khác nguồn tự đăng ký thì ghi thêm cơ hội của nguồn khách tự đăng ký
            $row['orderid'] = explode(',', $row['orderid']);
            foreach ($row['orderid'] as $value) {
                if ((!empty($array_groups_leads[0]['order_curent']) and in_array($value, $array_groups_leads[0]['order_curent'])) or (!empty($array_groups_leads[0]['order_prev']) and in_array($value, $array_groups_leads[0]['order_prev']))) {
                    $array_groups_leads[0]['oppocurent'][$row['id']] = $row['id'];
                }
            }
        } else {
            $array_groups_leads[$row['source_leads']]['oppocurent'][$row['id']] = $row['id'];
        }
    }

    // Các opp dc tạo từ tháng trước: k phải cơ hội dc tạo trong tháng này, và là các cơ hội của đơn hàng dc tạo trong tháng này + đơn hàng tháng trc đó
    $array_order = array_merge($array_order_curent, $array_order_prev);
    if (!empty($array_order) and !empty($array_oppo_current)) {
        $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities WHERE active=1 AND id NOT IN (' . implode(',', $array_oppo_current) . ') AND CONCAT(",", `orderid`, ",") REGEXP ",(' . implode('|', $array_order) . '),"';
        $result = $db->query($sql);
        while ($row = $result->fetch()) {
            $array_oppo_prev[$row['id']] = $row['id'];
            $array_oppo_current_lead_id[$row['leadsid']] = $row['leadsid'];

            if ($row['source_leads'] != 0) {
                $array_groups_leads[$row['source_leads']]['oppo_prev'][$row['id']] = $row['id'];

                // các cơ hội mà trùng với đơn hàng của khách tự đăng ký mà có nguồn lead khác nguồn tự đăng ký thì ghi thêm cơ hội của nguồn khách tự đăng ký
                $row['orderid'] = explode(',', $row['orderid']);
                foreach ($row['orderid'] as $value) {
                    if ((!empty($array_groups_leads[0]['order_curent']) and in_array($value, $array_groups_leads[0]['order_curent'])) or (!empty($array_groups_leads[0]['order_prev']) and in_array($value, $array_groups_leads[0]['order_prev']))) {
                        $array_groups_leads[0]['oppo_prev'][$row['id']] = $row['id'];
                    }
                }
            } else {
                $array_groups_leads[$row['source_leads']]['oppo_prev'][$row['id']] = $row['id'];
            }
        }
    }

    // leads dc tạo từ tháng trước của cơ hội trên
    if (!empty($array_leads_current) and !empty($array_oppo_current_lead_id)) {
        $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_leads WHERE active=1 AND id NOT IN (' . implode(',', $array_leads_current) . ')  AND id IN (' . implode(',', $array_oppo_current_lead_id) . ')';
        $result = $db->query($sql);
        $array_leads_prev = [];
        while ($row = $result->fetch()) {
            $array_leads_prev[$row['id']] = $row['id'];
            $array_groups_leads[$row['source_leads']]['leads_prev'][$row['id']] = $row['id'];
        }
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('ARRAY_SEARCH', $array_search);

$i = 1;
$static_groups = [];
$static_groups['leads'] = 0;
$static_groups['opportunities'] = 0;
$static_groups['order'] = 0;
$static_groups['vip'] = 0;
$static_groups['money'] = 0;
$static_groups['discount'] = 0;
$static_groups['total'] = 0;
$static_groups['price_reduce'] = 0;
$static_groups['total_end'] = 0;
$static_groups['discount_excess'] = 0;

foreach ($array_groups_leads as $groups_leads_id => $groups_leads) {
    $groups_leads['stt'] = $i;
    $groups_leads['link_leads'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads&amp;showheader=0&amp;time_from=' . $array_search['time_from'] . '&amp;time_to=' . $array_search['time_to'] . '&amp;group_leads=' . $groups_leads_id . '&amp;view_leads=1&amp;search_time_type=1&caregiver_id=' . $array_search['admin_id'];
    $groups_leads['link_opportunities'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=opportunities&amp;showheader=0&amp;time_from=' . $array_search['time_from'] . '&amp;time_to=' . $array_search['time_to'] . '&amp;view_leads=1&amp;group_leads=' . $groups_leads_id . '&amp;search_time_type=1&caregiver_id=' . $array_search['admin_id'];

    $order_curent = !empty($groups_leads['order_curent']) ? implode(',', $groups_leads['order_curent']) : '';
    $groups_leads['link_order'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;showheader=0&amp;view_leads=1&amp;orderid_static=' . $order_curent;

    $vip_curent_key = !empty($groups_leads['vip_curent']) ? array_keys($groups_leads['vip_curent']) : [];
    $vip_curent_key = !empty($vip_curent_key) ? implode(',', $vip_curent_key) : '';

    $groups_leads['link_vip'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;showheader=0&amp;view_leads=1&amp;orderid_static=' . $vip_curent_key;

    if ($array_search['type'] == 2) { // ngược
        $groups_leads['leadscurent'] = !empty($groups_leads['leadscurent']) ? sizeof($groups_leads['leadscurent']) : 0;
        $groups_leads['leads_prev'] = !empty($groups_leads['leads_prev']) ? sizeof($groups_leads['leads_prev']) : 0;
        $groups_leads['oppocurent'] = !empty($groups_leads['oppocurent']) ? sizeof($groups_leads['oppocurent']) : 0;
        $groups_leads['oppo_prev'] = !empty($groups_leads['oppo_prev']) ? sizeof($groups_leads['oppo_prev']) : 0;
        $groups_leads['order_curent'] = !empty($groups_leads['order_curent']) ? sizeof($groups_leads['order_curent']) : 0;
        $groups_leads['order_prev'] = !empty($groups_leads['order_prev']) ? sizeof($groups_leads['order_prev']) : 0;
        $groups_leads['vip_curent'] = !empty($groups_leads['vip_curent']) ? sizeof($groups_leads['vip_curent']) : 0;

        $groups_leads['num_leads'] = $groups_leads['leadscurent'];
        $groups_leads['num_opportunities'] = $groups_leads['oppocurent'];
        $groups_leads['num_order'] = $groups_leads['order_curent'];
        $groups_leads['num_vip'] = $groups_leads['vip_curent'];

        $static_groups['leads'] += ($groups_leads['leadscurent'] + $groups_leads['leads_prev']);
        $static_groups['opportunities'] += ($groups_leads['oppocurent'] + $groups_leads['oppo_prev']);
        $static_groups['order'] += ($groups_leads['order_curent'] + $groups_leads['order_prev']);
        $static_groups['vip'] += $groups_leads['num_vip'];

        $groups_leads['money'] = isset($groups_leads['money']) ? $groups_leads['money'] : 0;
        $groups_leads['discount'] = isset($groups_leads['discount']) ? $groups_leads['discount'] : 0;
        $groups_leads['total'] = isset($groups_leads['total']) ? $groups_leads['total'] : 0;
        $groups_leads['price_reduce'] = isset($groups_leads['price_reduce']) ? $groups_leads['price_reduce'] : 0;
        $groups_leads['total_end'] = isset($groups_leads['total_end']) ? $groups_leads['total_end'] : 0;
        $groups_leads['discount_excess'] = isset($groups_leads['discount_excess']) ? $groups_leads['discount_excess'] : 0;

        $static_groups['money'] += $groups_leads['money'];
        $static_groups['discount'] += $groups_leads['discount'];
        $static_groups['total'] += $groups_leads['total'];
        $static_groups['price_reduce'] += $groups_leads['price_reduce'];
        $static_groups['total_end'] += $groups_leads['total_end'];
        $static_groups['discount_excess'] += $groups_leads['discount_excess'];

        $groups_leads['money'] = number_format($groups_leads['money']);
        $groups_leads['discount'] = number_format($groups_leads['discount']);
        $groups_leads['total'] = number_format($groups_leads['total']);
        $groups_leads['price_reduce'] = number_format($groups_leads['price_reduce']);
        $groups_leads['total_end'] = number_format($groups_leads['total_end']);
        $groups_leads['discount_excess'] = number_format($groups_leads['discount_excess']);

        if ($array_search['method'] == 2) { // theo chu kì: chia cho lead
            $lead = $groups_leads['leadscurent'] + $groups_leads['leads_prev'];
            $groups_leads['rate_leads'] = $lead != 0 ? number_format(($groups_leads['num_opportunities'] / $lead * 100), 2) : 0;
            if ($groups_leads_id == 0) {
                $oppo = $groups_leads['oppocurent'] + $groups_leads['oppo_prev'];
                $groups_leads['rate_order'] = $oppo != 0 ? number_format(($groups_leads['num_order'] / $oppo * 100), 2) : 0;
                $groups_leads['rate_vip'] = $oppo != 0 ? number_format(($groups_leads['num_vip'] / $oppo * 100), 2) : 0;
            } else {
                $groups_leads['rate_order'] = $lead != 0 ? number_format(($groups_leads['num_order'] / $lead * 100), 2) : 0;
                $groups_leads['rate_vip'] = $lead != 0 ? number_format(($groups_leads['num_vip'] / $lead * 100), 2) : 0;
            }
        } else { // theo phân đoạn
            $groups_leads['rate_leads'] = $groups_leads['num_leads'] != 0 ? number_format(($groups_leads['num_opportunities'] / ($groups_leads['leadscurent'] + $groups_leads['leads_prev']) * 100), 2) : 0;
            $groups_leads['rate_order'] = $groups_leads['num_opportunities'] != 0 ? number_format(($groups_leads['num_order'] / ($groups_leads['oppocurent'] + $groups_leads['oppo_prev']) * 100), 2) : 0;
            $groups_leads['rate_vip'] = $groups_leads['num_order'] != 0 ? number_format(($groups_leads['num_vip'] / ($groups_leads['order_curent'] + $groups_leads['order_prev']) * 100), 2) : 0;
        }
    } else { // xuôi
        $static_groups['leads'] += $groups_leads['num_leads'];
        $static_groups['opportunities'] += $groups_leads['num_opportunities'];
        $static_groups['order'] += $groups_leads['num_order'];
        $static_groups['vip'] += $groups_leads['num_vip'];

        $static_groups['money'] += $groups_leads['money'];
        $static_groups['discount'] += $groups_leads['discount'];
        $static_groups['total'] += $groups_leads['total'];
        $static_groups['price_reduce'] += $groups_leads['price_reduce'];
        $static_groups['total_end'] += $groups_leads['total_end'];
        $static_groups['discount_excess'] += $groups_leads['discount_excess'];

        $groups_leads['money'] = number_format($groups_leads['money']);
        $groups_leads['discount'] = number_format($groups_leads['discount']);
        $groups_leads['total'] = number_format($groups_leads['total']);
        $groups_leads['price_reduce'] = number_format($groups_leads['price_reduce']);
        $groups_leads['total_end'] = number_format($groups_leads['total_end']);
        $groups_leads['discount_excess'] = number_format($groups_leads['discount_excess']);

        if ($array_search['method'] == 2) { // theo chu kì: chia cho lead
            if ($groups_leads_id == 0) {
                $groups_leads['rate_leads'] = $groups_leads['num_leads'] != 0 ? number_format(($groups_leads['num_opportunities'] / $groups_leads['num_leads'] * 100), 2) : 0;
                $groups_leads['rate_order'] = $groups_leads['num_opportunities'] != 0 ? number_format(($groups_leads['num_order'] / $groups_leads['num_opportunities'] * 100), 2) : 0;
                $groups_leads['rate_vip'] = $groups_leads['num_opportunities'] != 0 ? number_format(($groups_leads['num_vip'] / $groups_leads['num_opportunities'] * 100), 2) : 0;
            } else {
                $groups_leads['rate_leads'] = $groups_leads['num_leads'] != 0 ? number_format(($groups_leads['num_opportunities'] / $groups_leads['num_leads'] * 100), 2) : 0;
                $groups_leads['rate_order'] = $groups_leads['num_leads'] != 0 ? number_format(($groups_leads['num_order'] / $groups_leads['num_leads'] * 100), 2) : 0;
                $groups_leads['rate_vip'] = $groups_leads['num_leads'] != 0 ? number_format(($groups_leads['num_vip'] / $groups_leads['num_leads'] * 100), 2) : 0;
            }
        } else { // theo từng phân đoạn
            $groups_leads['rate_leads'] = $groups_leads['num_leads'] != 0 ? number_format(($groups_leads['num_opportunities'] / $groups_leads['num_leads'] * 100), 2) : 0;
            $groups_leads['rate_order'] = $groups_leads['num_opportunities'] != 0 ? number_format(($groups_leads['num_order'] / $groups_leads['num_opportunities'] * 100), 2) : 0;
            $groups_leads['rate_vip'] = $groups_leads['num_order'] != 0 ? number_format(($groups_leads['num_vip'] / $groups_leads['num_order'] * 100), 2) : 0;
        }
    }

    $xtpl->assign('GROUPS_LEADS', $groups_leads);
    $xtpl->parse('main.loop_groupsleads');
    $i++;
}
if ($array_search['method'] == 2) { // theo chu kì: chia cho lead
    $static_groups['rate_leads'] = $static_groups['leads'] != 0 ? number_format(($static_groups['opportunities'] / $static_groups['leads'] * 100), 2) : 0;
    $static_groups['rate_order'] = $static_groups['leads'] != 0 ? number_format(($static_groups['order'] / $static_groups['leads'] * 100), 2) : 0;
    $static_groups['rate_vip'] = $static_groups['leads'] != 0 ? number_format(($static_groups['vip'] / $static_groups['leads'] * 100), 2) : 0;
} else {
    $static_groups['rate_leads'] = $static_groups['leads'] != 0 ? number_format(($static_groups['opportunities'] / $static_groups['leads'] * 100), 2) : 0;
    $static_groups['rate_order'] = $static_groups['opportunities'] != 0 ? number_format(($static_groups['order'] / $static_groups['opportunities'] * 100), 2) : 0;
    $static_groups['rate_vip'] = $static_groups['order'] != 0 ? number_format(($static_groups['vip'] / $static_groups['order'] * 100), 2) : 0;
}

$static_groups['money'] = number_format($static_groups['money']);
$static_groups['discount'] = number_format($static_groups['discount']);
$static_groups['total'] = number_format($static_groups['total']);
$static_groups['price_reduce'] = number_format($static_groups['price_reduce']);
$static_groups['total_end'] = number_format($static_groups['total_end']);
$static_groups['discount_excess'] = number_format($static_groups['discount_excess']);

$xtpl->assign('STATIC_GROUPS', $static_groups);

foreach ($array_admin as $_user_info) {
    $xtpl->assign('OPTION', array(
        'key' => $_user_info['userid'],
        'title' => nv_show_name_user($_user_info['first_name'], $_user_info['last_name'], $_user_info['username']),
        'selected' => $array_search['admin_id'] == $_user_info['userid'] ? ' selected="selected"' : ''
    ));
    $xtpl->parse('main.loop_admin');
}

if ($array_search['admin_id'] == 0) {
    $xtpl->assign('marketing_selected', ' selected="selected"');
}

$xtpl->assign('METHOD' . $array_search['method'], ' selected="selected"');
$xtpl->assign('TYPE' . $array_search['type'], ' selected="selected"');

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
