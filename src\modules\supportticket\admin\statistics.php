<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('statistics');

$template = get_tpl_dir([$global_config['module_theme'], $global_config['admin_theme']], 'admin_default', '/modules/' . $module_file . '/statistics.tpl');
$tpl = new \NukeViet\Template\NVSmarty();
$tpl->registerPlugin('modifier', 'nformat', 'nv_number_format');
$tpl->registerPlugin('modifier', 'dformat', 'nv_datetime_format');
$tpl->setTemplateDir(NV_ROOTDIR . '/themes/' . $template . '/modules/' . $module_file);
$tpl->assign('LANG', $nv_Lang);
$tpl->assign('MODULE_NAME', $module_name);
$tpl->assign('OP', $op);

$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
$invalid_answer_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=invalid_answers&amp;type=tool';
$accept_refund_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=refunds&amp;type=done&amp;refund_status=' . RefundStatus::Accept->value;
$answers_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=answers';
$array_search = [];

$curent_from = date('d-m-Y', strtotime('first day of this month'));
$curent_to = date('d-m-Y', strtotime('now'));
$array_search['time_from'] = $nv_Request->get_title('time_from', 'get', $curent_from);
$array_search['time_to'] = $nv_Request->get_title('time_to', 'get', $curent_to);
$array_search['u_time_from'] = nv_d2u_get($array_search['time_from']);
$array_search['u_time_to'] = nv_d2u_get($array_search['time_to'], 23, 59, 59);
$array_search['ticket_id'] = $nv_Request->get_int('ticket_id', 'get', 0);
$array_search['type'] = $nv_Request->get_int('type', 'get', 0);

$statusDone = CommentStatus::Done->value;
$statusInvalid = CommentStatus::Invalid->value;
$statusRefund = CommentStatus::Refund->value;
$paymentDone = PaymentStatus::Done->value;
$refundAccept = RefundStatus::Accept->value;
$commentTypeAI = CommentType::AI->value;
$commentTypeExpert = CommentType::Expert->value;
$commentTypeCustomer = CommentType::Customer->value;

if (!empty($array_search['time_from'])) {
    $base_url .= '&amp;time_from=' . urlencode($array_search['time_from']);
    $invalid_answer_url .= '&amp;time_from=' . urlencode($array_search['time_from']);
    $accept_refund_url .= '&amp;time_from=' . urlencode($array_search['time_from']);
    $answers_url .= '&amp;time_from=' . urlencode($array_search['time_from']);
}
if (!empty($array_search['time_to'])) {
    $base_url .= '&amp;time_to=' . urlencode($array_search['time_to']);
    $invalid_answer_url .= '&amp;time_to=' . urlencode($array_search['time_to']);
    $accept_refund_url .= '&amp;time_to=' . urlencode($array_search['time_to']);
    $answers_url .= '&amp;time_to=' . urlencode($array_search['time_to']);
}
if ($array_search['ticket_id'] > 0) {
    $base_url .= '&amp;ticket_id=' . $array_search['ticket_id'];
    $answers_url .= '&amp;ticket_id=' . $array_search['ticket_id'];
} else {
    $array_search['ticket_id'] = '';
}
if ($array_search['type'] > 0) {
    $base_url .= '&amp;type=' . $array_search['type'];
}
if ($array_search['type'] == 0 || $array_search['type'] == 2) {
    $answers_url .= '&amp;type=' . $array_search['type'];
}

$config_commission = isset($module_config[$module_name]['commission']) ? $module_config[$module_name]['commission'] * 0.01 : 0;
$where_ticket_arr = $where_ticket_ai_arr = $where_ticket_expert_arr = [];
$where_question_arr = $where_question_ai_arr = $where_question_expert_arr = [];
$where_answer_arr = $where_answer_ai_arr = $where_answer_expert_arr = [];
$where_ticket_arr[] = 'is_paid=1';
$where_question_arr[] = 'is_paid=1';
$where_answer_arr[] = 'is_paid=1';
$real_data = 'delete_time=0';
$test_data = 'delete_time>0';

if (!empty($array_search['u_time_from'])) {
    $where_ticket_arr[] = 'add_time>=' . $array_search['u_time_from'];
    $where_question_arr[] = 'ticket_add_time>=' . $array_search['u_time_from'];
    $where_answer_arr[] = 'ticket_add_time>=' . $array_search['u_time_from'];
}
if (!empty($array_search['u_time_to'])) {
    $where_ticket_arr[] = 'add_time<=' . $array_search['u_time_to'];
    $where_question_arr[] = 'ticket_add_time<=' . $array_search['u_time_to'];
    $where_answer_arr[] = 'ticket_add_time<=' . $array_search['u_time_to'];
}
if ($array_search['ticket_id'] > 0) {
    $where_ticket_arr[] = 'id='. $array_search['ticket_id'];
    $where_question_arr[] = 'ticket_id='. $array_search['ticket_id'];
    $where_answer_arr[] = 'ticket_id='. $array_search['ticket_id'];
}
if ($array_search['type'] == 1) {
    $where_ticket_ai_arr[] = 'ask_ai=1';
    $where_question_ai_arr[] = 'comment_type=' . $commentTypeCustomer . ' AND point_offer=0';
    $where_answer_ai_arr[] = 'comment_type=' . $commentTypeAI;
} elseif ($array_search['type'] == 2) {
    $where_ticket_expert_arr[] = 'ask_expert=1';
    $where_question_expert_arr[] = 'comment_type=' . $commentTypeCustomer . ' AND point_offer>0';
    $where_answer_expert_arr[] = 'comment_type=' . $commentTypeExpert;
} else {
    $where_ticket_ai_arr[] = 'ask_ai=1';
    $where_ticket_expert_arr[] = 'ask_expert=1';
    $where_question_ai_arr[] = 'comment_type=' . $commentTypeCustomer . ' AND point_offer=0';
    $where_question_expert_arr[] = 'comment_type=' . $commentTypeCustomer . ' AND point_offer>0';
    $where_answer_ai_arr[] = 'comment_type=' . $commentTypeAI;
    $where_answer_expert_arr[] = 'comment_type=' . $commentTypeExpert;
}

$where_ticket_ai_merge = array_merge($where_ticket_arr, $where_ticket_ai_arr);
$where_ticket_ai_str = implode(' AND ', $where_ticket_ai_merge);
$where_ticket_expert_merge = array_merge($where_ticket_arr, $where_ticket_expert_arr);
$where_ticket_expert_str = implode(' AND ', $where_ticket_expert_merge);

$where_question_ai_merge = array_merge($where_question_arr, $where_question_ai_arr);
$where_question_ai_str = implode(' AND ', $where_question_ai_merge);
$where_question_expert_merge = array_merge($where_question_arr, $where_question_expert_arr);
$where_question_expert_str = implode(' AND ', $where_question_expert_merge);

$where_answer_ai_merge = array_merge($where_answer_arr, $where_answer_ai_arr);
$where_answer_ai_str = implode(' AND ', $where_answer_ai_merge);
$where_answer_expert_merge = array_merge($where_answer_arr, $where_answer_expert_arr);
$where_answer_expert_str = implode(' AND ', $where_answer_expert_merge);

$total_ticket_ai = 0;
$total_question_ai = 0;
$total_answer_ai = 0;
$empty_answer_ai = 0;
$invalid_answer_ai = 0;
$request_refund_ai = 0;
$accept_refund_ai = 0;
$done_answer_ai = 0;
$point_paid_ai = 0;
$number_rating_ai = 0;
$total_word_ai = 0;
$total_rating_ai = 0;
$avg_rating_ai = 0;
$total_ticket_test_ai = 0;
$total_question_test_ai = 0;
$total_word_test_ai = 0;
$avg_reply_range_time_ai = 0;

if ($array_search['type'] == 0 || $array_search['type'] == 1) {
    $answer_ai_sql = "
        SELECT
            SUM(CASE WHEN content IS NOT NULL THEN 1 ELSE 0 END) AS total_answer_ai,
            SUM(CASE WHEN content IS NULL THEN 1 ELSE 0 END) AS empty_answer_ai,
            SUM(CASE WHEN status = $statusInvalid THEN 1 ELSE 0 END) AS invalid_answer_ai,
            SUM(CASE WHEN refund_status > 0 THEN 1 ELSE 0 END) AS request_refund_ai,
            SUM(CASE WHEN refund_status = $refundAccept THEN 1 ELSE 0 END) AS accept_refund_ai,
            SUM(CASE WHEN status = $statusDone AND payment_status = $paymentDone THEN 1 ELSE 0 END) AS done_answer_ai,
            SUM(CASE WHEN status = $statusDone AND point_final > 0 AND payment_status = $paymentDone THEN point_final ELSE 0 END) AS point_paid_ai,
            SUM(CASE WHEN count_word > 0 THEN count_word ELSE 0 END) AS total_word_ai,
            SUM(CASE WHEN rating_number > 0 THEN 1 ELSE 0 END) AS number_rating_ai,
            SUM(CASE WHEN rating_number > 0 THEN rating_number ELSE 0 END) AS total_rating_ai,
            SUM(reply_range_time) AS total_reply_range_time_ai
        FROM " . TB_TICKET_LOG . "
        WHERE $where_answer_ai_str AND $real_data
    ";
    $answer_data_ai = $db->query($answer_ai_sql)->fetch();

    $total_answer_ai = (int)$answer_data_ai['total_answer_ai'];
    $empty_answer_ai = (int)$answer_data_ai['empty_answer_ai'];
    $invalid_answer_ai = (int)$answer_data_ai['invalid_answer_ai'];
    $request_refund_ai = (int)$answer_data_ai['request_refund_ai'];
    $accept_refund_ai = (int)$answer_data_ai['accept_refund_ai'];
    $done_answer_ai = (int)$answer_data_ai['done_answer_ai'];
    $point_paid_ai = (int)$answer_data_ai['point_paid_ai'];
    $total_word_ai = (int)$answer_data_ai['total_word_ai'];
    $number_rating_ai = (int)$answer_data_ai['number_rating_ai'];
    $total_rating_ai = (int)$answer_data_ai['total_rating_ai'];
    $total_reply_range_time_ai = (int)$answer_data_ai['total_reply_range_time_ai'];
    $avg_rating_ai = $number_rating_ai > 0 ? round($total_rating_ai / $number_rating_ai, 2) : 0;
    $avg_reply_range_time_ai = $total_answer_ai > 0 ? round($total_reply_range_time_ai / $total_answer_ai, 2) : 0;

    $total_ticket_ai = $db->query("SELECT COUNT(*) FROM " . TB_TICKET_ROW . " WHERE $where_ticket_ai_str AND $real_data")->fetchColumn();
    $total_question_ai = $db->query("SELECT COUNT(*) FROM " . TB_TICKET_LOG . " WHERE $where_question_ai_str AND $real_data")->fetchColumn();

    $total_ticket_test_ai = $db->query("SELECT COUNT(*) FROM " . TB_TICKET_ROW . " WHERE $where_ticket_ai_str AND $test_data")->fetchColumn();
    $total_question_test_ai = $db->query("SELECT COUNT(*) FROM " . TB_TICKET_LOG . " WHERE $where_question_ai_str AND $test_data")->fetchColumn();
    $total_word_test_ai = $db->query("SELECT COALESCE(SUM(count_word), 0) FROM " . TB_TICKET_LOG . " WHERE $where_answer_ai_str AND count_word > 0 AND $test_data")->fetchColumn();
}

$total_ticket_expert = 0;
$total_question_expert = 0;
$total_answer_expert = 0;
$empty_answer_expert = 0;
$invalid_answer_expert = 0;
$request_refund_expert = 0;
$accept_refund_expert = 0;
$done_answer_expert = 0;
$point_paid_expert = 0;
$number_rating_expert = 0;
$reply_expert = 0;
$number_rating_expert = 0;
$total_rating_expert = 0;
$avg_rating_expert = 0;
$avg_reply_range_time_expert = 0;

if ($array_search['type'] == 0 || $array_search['type'] == 2) {
    $answer_expert_sql = "
        SELECT
            COUNT(*) AS total_answer_expert,
            SUM(CASE WHEN refund_status > 0 THEN 1 ELSE 0 END) AS request_refund_expert,
            SUM(CASE WHEN refund_status = $refundAccept THEN 1 ELSE 0 END) AS accept_refund_expert,
            SUM(CASE WHEN status = $statusDone AND payment_status = $paymentDone THEN 1 ELSE 0 END) AS done_answer_expert,
            SUM(CASE WHEN status = $statusDone AND point_final > 0 AND payment_status = $paymentDone THEN point_final ELSE 0 END) AS point_paid_expert,
            COUNT(DISTINCT CASE WHEN reply_userid > 0 THEN reply_userid END) AS reply_expert,
            SUM(CASE WHEN rating_number > 0 THEN 1 ELSE 0 END) AS number_rating_expert,
            SUM(CASE WHEN rating_number > 0 THEN rating_number ELSE 0 END) AS total_rating_expert,
            SUM(reply_range_time) AS total_reply_range_time_expert
        FROM " . TB_TICKET_LOG . "
        WHERE $where_answer_expert_str AND $real_data
    ";
    $answer_data_expert = $db->query($answer_expert_sql)->fetch();

    $total_answer_expert = (int)$answer_data_expert['total_answer_expert'];
    $request_refund_expert = (int)$answer_data_expert['request_refund_expert'];
    $accept_refund_expert = (int)$answer_data_expert['accept_refund_expert'];
    $done_answer_expert = (int)$answer_data_expert['done_answer_expert'];
    $point_paid_expert = (int)$answer_data_expert['point_paid_expert'];
    $reply_expert = (int)$answer_data_expert['reply_expert'];
    $number_rating_expert = (int)$answer_data_expert['number_rating_expert'];
    $total_rating_expert = (int)$answer_data_expert['total_rating_expert'];
    $total_reply_range_time_expert = (int)$answer_data_expert['total_reply_range_time_expert'];
    $avg_rating_expert = $number_rating_expert > 0 ? round($total_rating_expert / $number_rating_expert, 2) : 0;
    $avg_reply_range_time_expert = $total_answer_expert > 0 ? round($total_reply_range_time_expert / $total_answer_expert, 2) : 0;

    $total_ticket_expert = $db->query("SELECT COUNT(*) FROM " . TB_TICKET_ROW . " WHERE $where_ticket_expert_str AND $real_data")->fetchColumn();
    $total_question_expert = $db->query("SELECT COUNT(*) FROM " . TB_TICKET_LOG . " WHERE $where_question_expert_str AND $real_data")->fetchColumn();
    $empty_answer_expert = $total_ticket_expert + $total_question_expert - $total_answer_expert;

    // Thống kê chi tiết theo từng chuyên gia
    $expert_stats = [];
    $sql = "
        SELECT 
            reply_userid,
            COUNT(*) AS total_answer,
            SUM(CASE WHEN refund_status = $refundAccept THEN 1 ELSE 0 END) AS accept_refund,
            SUM(CASE WHEN refund_status = $refundAccept THEN point_refund ELSE 0 END) AS point_refund,
            SUM(CASE WHEN status = $statusDone AND payment_status = $paymentDone THEN 1 ELSE 0 END) AS done_answer,
            SUM(CASE WHEN status = $statusDone AND point_final > 0 AND payment_status = $paymentDone THEN point_final ELSE 0 END) AS point_paid,
            SUM(CASE WHEN rating_number > 0 THEN 1 ELSE 0 END) AS number_rating,
            SUM(CASE WHEN rating_number > 0 THEN rating_number ELSE 0 END) AS total_rating
        FROM " . TB_TICKET_LOG . "
        WHERE $where_answer_expert_str AND reply_userid > 0 AND $real_data
        GROUP BY reply_userid
    ";
    $query = $db->query($sql);
    $stt = 0;
    while ($row = $query->fetch()) {
        ++$stt;
        $expert_id = $row['reply_userid'];
        $expert_fullname = get_user_info($expert_id)['fullname'];
        $expert_answers_url = $answers_url . '&amp;expert=' . $expert_id;
        $total_answer = (int)$row['total_answer'];
        $done_answer = (int)$row['done_answer'];
        $point_paid = (int)$row['point_paid'];
        $accept_refund = (int)$row['accept_refund'];
        $point_refund = (int)$row['point_refund'];
        $number_rating = (int)$row['number_rating'];
        $total_rating = (int)$row['total_rating'];
        $avg_rating = $number_rating > 0 ? round($total_rating / $number_rating, 2) : 0;
        $expert_stats[$expert_id] = [
            'stt' => $stt,
            'fullname' => $expert_fullname,
            'expert_answers_url' => $expert_answers_url,
            'total_answer' => nv_number_format($total_answer),
            'done_answer' => nv_number_format($done_answer),
            'point_paid' => nv_number_format($point_paid),
            'accept_refund' => nv_number_format($accept_refund),
            'point_refund' => nv_number_format($point_refund),
            'revenue' => nv_number_format($point_paid * 1000),
            'commission' => nv_number_format($point_paid * 1000 * $config_commission),
            'number_rating' => nv_number_format($number_rating),
            'avg_rating' => $avg_rating,
        ];
    }
    $tpl->assign('EXPERT_STATS', $expert_stats);
}

$statistics = [
    'total_question_ai' => nv_number_format($total_ticket_ai + $total_question_ai),
    'total_question_expert' => nv_number_format($total_ticket_expert + $total_question_expert),
    'total_question' => nv_number_format($total_ticket_ai + $total_question_ai + $total_ticket_expert + $total_question_expert),
    'total_answer_ai' => nv_number_format($total_answer_ai),
    'total_answer_expert' => nv_number_format($total_answer_expert),
    'total_answer' => nv_number_format($total_answer_ai + $total_answer_expert),
    'empty_answer_ai' => nv_number_format($empty_answer_ai),
    'empty_answer_expert' => nv_number_format($empty_answer_expert),
    'empty_answer' => nv_number_format($empty_answer_ai + $empty_answer_expert),
    'invalid_answer_ai' => nv_number_format($invalid_answer_ai),
    'request_refund_ai' => nv_number_format($request_refund_ai),
    'request_refund_expert' => nv_number_format($request_refund_expert),
    'request_refund' => nv_number_format($request_refund_ai + $request_refund_expert),
    'accept_refund_ai' => nv_number_format($accept_refund_ai),
    'accept_refund_expert' => nv_number_format($accept_refund_expert),
    'accept_refund' => nv_number_format($accept_refund_ai + $accept_refund_expert),
    'done_answer_ai' => nv_number_format($done_answer_ai),
    'done_answer_expert' => nv_number_format($done_answer_expert),
    'done_answer' => nv_number_format($done_answer_ai + $done_answer_expert),
    'point_paid_ai' => nv_number_format($point_paid_ai),
    'point_paid_expert' => nv_number_format($point_paid_expert),
    'point_paid' => nv_number_format($point_paid_ai + $point_paid_expert),
    'revenue_ai' => nv_number_format($point_paid_ai * 1000),
    'revenue_expert' => nv_number_format($point_paid_expert * 1000),
    'revenue' => nv_number_format(($point_paid_ai + $point_paid_expert) * 1000),
    'commission_expert' => nv_number_format($point_paid_expert * 1000 * $config_commission),
    'reply_expert' => nv_number_format($reply_expert),
    'total_word_ai' => nv_number_format($total_word_ai),
    'request_refund_rate_ai' => $total_answer_ai > 0 ? round(($request_refund_ai / $total_answer_ai) * 100, 2) : 0,
    'request_refund_rate_expert' => $total_answer_expert > 0 ? round(($request_refund_expert / $total_answer_expert) * 100, 2) : 0,
    'request_refund_rate' => ($total_answer_ai + $total_answer_expert) > 0 ? round((($request_refund_ai + $request_refund_expert) / ($total_answer_ai + $total_answer_expert)) * 100, 2) : 0,
    'accept_refund_rate_ai' => $request_refund_ai > 0 ? round(($accept_refund_ai / $request_refund_ai) * 100, 2) : 0,
    'accept_refund_rate_expert' => $request_refund_expert > 0 ? round(($accept_refund_expert / $request_refund_expert) * 100, 2) : 0,
    'accept_refund_rate' => ($request_refund_ai + $request_refund_expert) > 0 ? round((($accept_refund_ai + $accept_refund_expert) / ($request_refund_ai + $request_refund_expert)) * 100, 2) : 0,
    'number_rating_ai' => nv_number_format($number_rating_ai),
    'number_rating_expert' => nv_number_format($number_rating_expert),
    'number_rating' => nv_number_format($number_rating_ai + $number_rating_expert),
    'avg_rating_ai' => $avg_rating_ai,
    'avg_rating_expert' => $avg_rating_expert,
    'avg_rating' => ($number_rating_ai + $number_rating_expert) > 0 ? round(($total_rating_ai + $total_rating_expert) / ($number_rating_ai + $number_rating_expert), 2) : 0,
    'total_question_test_ai' => nv_number_format($total_ticket_test_ai + $total_question_test_ai),
    'total_word_test_ai' => nv_number_format($total_word_test_ai),
    'avg_reply_range_time_ai' => reply_range_time($avg_reply_range_time_ai),
    'avg_reply_range_time_expert' => reply_range_time($avg_reply_range_time_expert),
];

$filter_url = [
    'invalid_answer_url' => $invalid_answer_url,
    'accept_refund_url' => $accept_refund_url,
];

$tpl->assign('DATA', $statistics);
$tpl->assign('SEARCH', $array_search);
$tpl->assign('TOKEN', NV_CHECK_SESSION);
$tpl->assign('FILTER_URL', $filter_url);
$tpl->assign('COMMISSION', $config_commission * 100);
$tpl->assign('TYPES', [
    0 => [
            'selected' => $array_search['type'] == 0 ? 'selected' : '',
            'label' => $nv_Lang->getModule('all'),
        ],
    1 => [
            'selected' => $array_search['type'] == 1 ? 'selected' : '',
            'label' => $nv_Lang->getModule('role_ai'),
        ],
    2 => [
            'selected' => $array_search['type'] == 2 ? 'selected' : '',
            'label' => $nv_Lang->getModule('role_expert'),
        ]
]);

$paid_cat_ids = array_column($array_paid_cats, 'cat_id');
$query_cat = $db->query('SELECT userid FROM ' . $db_config['prefix'] . '_' . $module_data . '_cat_admins WHERE cat_id IN (' . implode(',', $paid_cat_ids) . ')');
$experts = [];
$admin_ids = [];
while ($cat = $query_cat->fetch()) {
    $admin_ids[] = $cat['userid'];
}
$admin_ids = array_unique($admin_ids);
foreach ($admin_ids as $id) {
    if(isset($array_admin_listall[$id])) {
        $array_admin_listall[$id]['selected'] = $array_search['expert'] == $id ? 'selected' : '';
        $array_admin_listall[$id]['fullname'] = nv_show_name_user($array_admin_listall[$id]['first_name'], $array_admin_listall[$id]['last_name'], $array_admin_listall[$id]['username']);
        $experts[] = $array_admin_listall[$id];
    }
}

$tpl->assign('EXPERTS', $experts);

$contents = $tpl->fetch('statistics.tpl');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
