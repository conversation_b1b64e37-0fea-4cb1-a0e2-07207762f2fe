<?php

/**
 * T<PERSON>h năng quản lý hóa đơn điện tử
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2024 Hu<PERSON><PERSON>. All rights reserved
 * @createdAt Mon, 15 Apr 2024 11:55:00
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

// Lấy phân loại nhóm của user
$my_group_type = 'sale';
$sql = 'SELECT t1.* FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups as t1 LEFT JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users as t2 ON t1.group_id=t2.group_id WHERE t2.userid = ' . $admin_info['userid'];
$_my_groups = $db->query($sql)->fetchAll();
if ($_my_groups) {
    foreach ($_my_groups as $_my_group) {
        $_my_group['config'] = unserialize($_my_group['config']);
        if (
            $my_group_type == 'sale' &&
            isset($_my_group['config']['type']) &&
            !empty($_my_group['config']['type']) &&
            $_my_group['config']['type'] != 'sale'
        ) {
            $my_group_type = $_my_group['config']['type'];
        }
    }
}

$admin_config = [];
// Lấy danh sách user mà mình đang quản lý (chung team)
$filter_uploader_ids = [];
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE userid = ' . $admin_info['userid'];
$result = $db->query($sql);
while ($row_groups_users = $result->fetch()) {
    $_admin_config = json_decode($row_groups_users['config'], true) ?? [];
    $admin_config = array_merge($admin_config, $_admin_config);
    if ($row_groups_users['is_leader'] == 1) {
        $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $row_groups_users['group_id'] . ' AND userid != ' . $admin_info['userid'];
        $_result = $db->query($_sql);
        while ($_row_groups_users = $_result->fetch()) {
            $filter_uploader_ids[$_row_groups_users['userid']] = $_row_groups_users['userid'];
        }
    }
}
$is_manage_einvoice = isset($admin_config['manage_einvoice']) && ($admin_config['manage_einvoice'] == 1); // Phân quyền: Quản lý TẤT CẢ hóa đơn
if (defined('NV_IS_SPADMIN') || $is_manage_einvoice) {
    $filter_uploader_ids[$admin_info['userid']] = $admin_info['userid'];
}

// TODO: Bắt đầu tải form thông tin hóa đơn lên
if ($nv_Request->isset_request('submit', 'post,get')) {
    $error = '';
    $data_upload = [];
    $data_upload['einvoice_no'] = $nv_Request->get_title('einvoice_no', 'post', '');
    $data_upload['tax_code'] = $nv_Request->get_title('tax_code', 'post', '');
    $data_upload['c_email'] = $nv_Request->get_title('c_email', 'post', '');
    $data_upload['c_phone'] = $nv_Request->get_title('c_phone', 'post', '');
    $data_upload['c_name'] = $nv_Request->get_title('c_name', 'post', '');
    $data_upload['c_address'] = $nv_Request->get_title('c_address', 'post', '');
    $data_upload['order_ids'] = $nv_Request->get_typed_array('order_ids', 'post', 'int', []);
    $data_upload['vip_list'] = $nv_Request->get_array('vip_titles', 'post', []);

    $formatted_invoice_no = formatInvoiceNumber($data_upload['einvoice_no']);
    if (empty($formatted_invoice_no)) {
        $error = $nv_Lang->getModule('error_invalid_einvoice_no');
    } else {
        $data_upload['einvoice_no'] = $formatted_invoice_no;
    }

    // ? Validate dữ liệu
    if (empty($data_upload['tax_code']) && empty($data_upload['c_email']) && empty($data_upload['c_phone'])) {
        $error = $nv_Lang->getModule('error_tax_code_email_phone');
    } elseif (empty($data_upload['c_name'])) {
        $error = $nv_Lang->getModule('error_c_name');
    } elseif (empty($data_upload['c_address'])) {
        $error = $nv_Lang->getModule('error_c_address');
    } elseif (!count($data_upload['order_ids'])) {
        $error = $nv_Lang->getModule('error_order_ids');
    } elseif (empty($data_upload['einvoice_no'])) {
        $error = $nv_Lang->getModule('error_einvoice_no');
    } elseif ($db->query('SELECT COUNT(*) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices WHERE einvoice_no=' . $db->quote($data_upload['einvoice_no']))->fetchColumn()) {
        $error = $nv_Lang->getModule('error_existed_einvoice_no');
    } elseif (empty($_FILES['einvoice_path']['tmp_name'])) {
        $error = $nv_Lang->getModule('error_choose_file_einvoice');
    }

    // Kiểm tra tồn tại hóa đơn và gói VIP đã thêm
    if (empty($error)) {
        // Lấy danh sách gói VIP
        $einvoice_vip_ids = [
            'vi' => [],
            'en' => [],
        ];
        $order_vip_ids = [];
        $order_user_ids = [];
        $order_prefix_langs = [];
        $where = [];
        $where['AND'] = [
            ['IN' => ['order_id' => '(' . implode(',', $data_upload['order_ids']) . ')']]
        ];
        $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
        $api_dtinfo->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingCustomsLog')
            ->setData([
                'array_select' => ['vip', 'order_id', 'user_id', 'prefix_lang'],
                'where' => $where
            ]);
        $result = $api_dtinfo->execute();
        $error = $api_dtinfo->getError();
        if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
            $bidding_customs_logs = $result['data'];
            if (is_array($bidding_customs_logs) && $result['total']) {
                foreach ($bidding_customs_logs as $bidding_customs_log) {
                    if ($bidding_customs_log['prefix_lang'] == 0) {
                        $einvoice_vip_ids['vi'][] = $bidding_customs_log['vip'];
                        $order_vip_ids[$bidding_customs_log['order_id']]['vi'][] = $bidding_customs_log['vip'];
                    } elseif ($bidding_customs_log['prefix_lang'] == 1) {
                        $einvoice_vip_ids['en'][] = $bidding_customs_log['vip'];
                        $order_vip_ids[$bidding_customs_log['order_id']]['en'][] = $bidding_customs_log['vip'];
                    }
                    $order_user_ids[$bidding_customs_log['order_id']] = intval($bidding_customs_log['user_id']);
                }
            }
        }

        foreach ($data_upload['order_ids'] as $order_id) {
            $_vips_vi = ($data_upload['vip_list'][$order_id]['vi'] ?: $order_vip_ids[$order_id]['vi']) ?? [];
            $_vips_en = ($data_upload['vip_list'][$order_id]['en'] ?: $order_vip_ids[$order_id]['en']) ?? [];

            $existed_order = $db->query('SELECT einvoice_id, order_id, vips_vi, vips_en FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders WHERE order_id=' . $order_id)->fetchAll();

            if ($existed_order) {
                $existed_vips_vi = [];
                $existed_vips_en = [];
                $einvoice_ids = [];

                foreach ($existed_order as $eo) {
                    if (!empty($eo['vips_vi'])) {
                        $existed_vips_vi = array_merge($existed_vips_vi, explode(',', $eo['vips_vi']));
                    }
                    if (!empty($eo['vips_en'])) {
                        $existed_vips_en = array_merge($existed_vips_en, explode(',', $eo['vips_en']));
                    }
                    $einvoice_ids[] = $eo['einvoice_id'];
                }

                $existed_vips_vi = array_unique(array_filter($existed_vips_vi));
                $existed_vips_en = array_unique(array_filter($existed_vips_en));

                // Kiểm tra Tiếng Việt: nếu có bất kỳ gói nào đã tồn tại
                $intersect_vips_vi = array_intersect($_vips_vi, $existed_vips_vi);
                if (!empty($intersect_vips_vi)) {
                    $duplicate_titles = array_map(function ($vip_key) use ($global_arr_vip) {
                        return $global_arr_vip[$vip_key] ?? $vip_key;
                    }, $intersect_vips_vi);

                    $duplicate_titles_str = implode(', ', $duplicate_titles);
                    $check_einvoice = $db->query('SELECT einvoice_no FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices WHERE id=' . intval($einvoice_ids[0]))->fetchColumn();
                    $error .= sprintf($nv_Lang->getModule('existed_orders_by_einvoice'), $duplicate_titles_str, $order_id, $check_einvoice) . "\n";
                }

                // Kiểm tra Tiếng Anh: nếu có bất kỳ gói nào đã tồn tại
                $intersect_vips_en = array_intersect($_vips_en, $existed_vips_en);
                if (!empty($intersect_vips_en)) {
                    $duplicate_titles = array_map(function ($vip_key) use ($global_arr_vip) {
                        return $global_arr_vip[$vip_key] ?? $vip_key;
                    }, $intersect_vips_en);

                    $duplicate_titles_str = implode(', ', $duplicate_titles);
                    $check_einvoice = $db->query('SELECT einvoice_no FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices WHERE id=' . intval($einvoice_ids[0]))->fetchColumn();
                    $error .= sprintf($nv_Lang->getModule('existed_orders_by_einvoice'), $duplicate_titles_str, $order_id, $check_einvoice) . "\n";
                }
            }
        }
    }

    // Lấy thông tin người chăm sóc
    if (empty($error)) {
        // Lấy ID Sale chăm sóc đơn hàng
        $caregiver_ids = [];
        $where = [];
        $where['AND'] = [
            ['IN' => ['id' => '(' . implode(',', $data_upload['order_ids']) . ')']]
        ];
        $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
        $api_dtinfo->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingOrder')
            ->setData([
                'array_select' => ['id', 'caregiver_id'],
                'where' => $where
            ]);
        $result = $api_dtinfo->execute();
        $error = $api_dtinfo->getError();
        if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
            foreach ($result['data'] as $_order) {
                $caregiver_ids[$_order['id']] = intval($_order['caregiver_id']);
            }
        }
    }

    // Kiểm tra và lưu file
    if (empty($error)) {
        // Khởi tạo thư mục upload
        if (!is_dir(NV_UPLOADS_REAL_DIR . '/einvoices')) {
            nv_mkdir(NV_UPLOADS_REAL_DIR, 'einvoices');
        }
        if (!is_dir(NV_UPLOADS_REAL_DIR . '/einvoices/' . strtolower(change_alias($admin_info['username'])))) {
            nv_mkdir(NV_UPLOADS_REAL_DIR . '/einvoices', strtolower(change_alias($admin_info['username'])));
        }
        $path_to_upload_einvoice = NV_UPLOADS_REAL_DIR . '/einvoices/' . strtolower(change_alias($admin_info['username']));

        // Lưu tệp
        $upload = new NukeViet\Files\Upload();
        $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
        // Lưu hóa đơn
        $upload_einvoice_info = $upload->save_file($_FILES['einvoice_path'], $path_to_upload_einvoice, false, $global_config['nv_auto_resize']);
        if (!empty($upload_einvoice_info['error'])) {
            $error = $upload_einvoice_info['error'];
        } elseif (!in_array($upload_einvoice_info['ext'], ['pdf', 'doc', 'docx', 'xml', 'zip'])) {
            $error = $nv_Lang->getModule('error_file_type');
        }
    }

    // Xử lý lưu thông tin
    if (empty($error)) {
        // Lấy thông tin từng vip theo order_id
        $_array_vip = [
            'vi' => [],
            'en' => []
        ];

        foreach ($data_upload['order_ids'] as $_order_id) {
            foreach (['vi', 'en'] as $lang) {
                foreach ($data_upload['vip_list'][$_order_id][$lang] as $_vip) {
                    $_array_vip[$lang][] = $_vip;
                }
            }
        }

        $data_upload['einvoice_path'] = NV_UPLOADS_DIR . '/einvoices/' . strtolower(change_alias($admin_info['username'])) . '/' . str_replace(['/', '\\'], '-', $data_upload['einvoice_no']) . '-' . $upload_einvoice_info['basename'];
        rename($upload_einvoice_info['name'], $path_to_upload_einvoice . '/' . str_replace(['/', '\\'], '-', $data_upload['einvoice_no']) . '-' . $upload_einvoice_info['basename']);

        // TODO: LƯU VÀO CSDL
        // ? Lưu bảng _einvoices
        $arr_vip_ids['vi'] = $_array_vip['vi'] ?: $einvoice_vip_ids['vi'];
        $arr_vip_ids['en'] = $_array_vip['en'] ?: $einvoice_vip_ids['en'];

        $sql = 'INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices (
                    tax_code, email, phone, c_name, c_address, einvoice_no, einvoice_path, uploader_id, vips_vi, vips_en, created_at, updated_at
                    ) VALUES (
                        :tax_code, :email, :phone, :c_name, :c_address, :einvoice_no, :einvoice_path,
                    ' . $admin_info['userid'] . ',
                    ' . $db->quote(implode(',', $arr_vip_ids['vi'])) . ',
                    ' . $db->quote(implode(',', $arr_vip_ids['en'])) . ',
                    ' . NV_CURRENTTIME . ',
                    ' . NV_CURRENTTIME . ')';

        $data_insert = [];
        $data_insert['einvoice_no'] = trim($data_upload['einvoice_no']);
        $data_insert['tax_code'] = trim($data_upload['tax_code']);
        $data_insert['email'] = trim($data_upload['c_email']);
        $data_insert['phone'] = trim($data_upload['c_phone']);
        $data_insert['c_name'] = trim($data_upload['c_name']);
        $data_insert['c_address'] = trim($data_upload['c_address']);
        $data_insert['einvoice_path'] = trim($data_upload['einvoice_path']);

        $einvoice_id = $db->insert_id($sql, 'id', $data_insert);
        // ? Lưu bảng _einvoice_orders
        if ($einvoice_id) {
            foreach ($data_upload['order_ids'] as $order_id) {
                $_o_user_id = 0;
                $_o_user_name = 'N/A';
                if (isset($order_user_ids[$order_id]) && $order_user_ids[$order_id] > 0) {
                    $o_user = $db->query('SELECT username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $order_user_ids[$order_id])->fetch();
                    $_o_user_id = $order_user_ids[$order_id];
                    $_o_user_name = $o_user['username'];
                }
                $_caregiver_id = $caregiver_ids[$order_id] ?? 0;
                /**
                 * Nếu như không chọn gói vip trong đơn hàng thì mặc định chọn hết
                 */
                $arr_vips_vi = ($data_upload['vip_list'][$order_id]['vi'] ?? $order_vip_ids[$order_id]['vi']) ?? [];
                $arr_vips_en = ($data_upload['vip_list'][$order_id]['en'] ?? $order_vip_ids[$order_id]['en']) ?? [];
                $sql = 'INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders (
                            einvoice_id, caregiver_id, order_id, site_id, user_id, username, vips_vi, vips_en
                        ) VALUES (
                            ' . $einvoice_id . ',
                            ' . $_caregiver_id . ',
                            ' . $order_id . ',
                            1,
                            ' . $_o_user_id . ',
                            ' . $db->quote($_o_user_name) . ',
                            ' . $db->quote(implode(',', $arr_vips_vi)) . ',
                            ' . $db->quote(implode(',', $arr_vips_en)) . '
                        )';

                $db->insert_id($sql, 'id');

                // Lưu log đơn hàng
                if (!empty($data_insert['einvoice_no'])) {
                    $logApi = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                    // Cập nhật số hóa đơn điện tử vào đơn hàng
                    $logApi->setModule('bidding')
                        ->setLang('vi')
                        ->setAction('UpdateBiddingOrder')
                        ->setData([
                            'biddingorder_id' => $order_id,
                            'admin_id' => $admin_info['admin_id'],
                            'data' => [
                                'invoice_number' => $data_insert['einvoice_no']
                            ]
                        ])->execute();

                    // Ghi log hóa đơn
                    $logApi->setModule('bidding')
                        ->setLang('vi')
                        ->setAction('CreateBiddingAllLogs')
                        ->setData([
                            'userid' => $admin_info['admin_id'],
                            'log_area' => 1,
                            'log_key' => 'LOG_CHANGE_ORDER_EINVOICE',
                            'log_time' => NV_CURRENTTIME,
                            'log_data' => [
                                [$nv_Lang->getModule('log_update_einvoice_success'), $nv_Lang->getModule('new')],
                                [$nv_Lang->getModule('einvoice_no') . ': ', $data_insert['einvoice_no']]
                            ],
                            'order_id' => $order_id
                        ])->execute();
                }
            }

            nv_jsonOutput([
                'status' => 'success',
                'message' => $nv_Lang->getModule('einvoice_save_success')
            ]);
        } else {
            $error = $nv_Lang->getModule('error_unknown');
        }
    }

    nv_jsonOutput([
        'status' => 'error',
        'message' => $error
    ]);
}

// TODO: Xóa hóa đơn
if ($nv_Request->isset_request('delete_id', 'get') and $nv_Request->isset_request('delete_checkss', 'get')) {
    $id = $nv_Request->get_int('delete_id', 'get');
    $delete_checkss = $nv_Request->get_string('delete_checkss', 'get');
    if ($id > 0 and $delete_checkss == md5($id . NV_CACHE_PREFIX . $client_info['session_id'])) {
        $einvoice_delete = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices WHERE id=' . $id)->fetch();
        if ($einvoice_delete) {
            $_order_ids = $db->query('SELECT order_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders WHERE einvoice_id=' . $einvoice_delete['id'])->fetchAll();

            // Xóa danh sách đơn hàng thuộc einvoice này
            $db->query('DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data  . '_einvoice_orders WHERE einvoice_id=' . $einvoice_delete['id']);
            // Xóa hóa đơn đính kèm
            nv_deletefile(NV_ROOTDIR . '/' . $einvoice_delete['einvoice_path']);
            // Xóa row
            $db->query('DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices WHERE id=' . $einvoice_delete['id']);

            // Lưu log đơn hàng
            foreach ($_order_ids as $_order) {
                $logApi = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                // Cập nhật số hóa đơn điện tử vào đơn hàng
                $logApi->setModule('bidding')
                    ->setLang('vi')
                    ->setAction('UpdateBiddingOrder')
                    ->setData([
                        'biddingorder_id' => $_order['order_id'],
                        'admin_id' => $admin_info['admin_id'],
                        'data' => [
                            'invoice_number' => ''
                        ]
                    ])->execute();

                $logApi->setModule('bidding')
                    ->setLang('vi')
                    ->setAction('CreateBiddingAllLogs')
                    ->setData([
                        'userid' => $admin_info['admin_id'],
                        'log_area' => 1,
                        'log_key' => 'LOG_CHANGE_ORDER_EINVOICE',
                        'log_time' => NV_CURRENTTIME,
                        'log_data' => [
                            [$nv_Lang->getModule('log_delete_einvoice_success'), $einvoice_delete['einvoice_no']]
                        ],
                        'order_id' => $_order['order_id']
                    ])->execute();
            }
        }
    }
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
}

// TODO: Call API: Lấy thông tin từ MST
if ($nv_Request->get_int('getcominfo', 'post,get', 0)) {
    $customer_info = [
        'mst' => '',
        'email' => '',
        'phone' => '',
        'c_name' => '',
        'c_address' => '',
        'orders' => []
    ];
    $typecheck = $nv_Request->get_title('typecheck', 'post,get');
    $val2check = $nv_Request->get_title('val2check', 'post,get');

    $where = [];
    $error = '';
    $is_found_com = false;
    if (!empty($val2check)) {
        $customer_info['mst'] = $typecheck == 'taxcode' ? $val2check : '';
        $customer_info['email'] = $typecheck == 'email' ? $val2check : '';
        $customer_info['phone'] = $typecheck == 'phone' ? $val2check : '';
        /**
         * TODO: LẤY THÔNG TIN CÔNG TY / KHÁCH HÀNG
         * 1. Lấy từ CSDL nhà thầu (businesslistings trên dauthau.asia)
         * 2. (nếu không có, hoặc chưa đủ thông tin) => Lấy từ API doanh nghiệp (dauthau.net)
         * 3. Lấy thông tin từ cơ hội kinh doanh
         */
        // ? 1. Lấy từ CSDL nhà thầu (businesslistings trên dauthau.asia)
        $where = [];
        $error_dtasia = '';
        $_typecheck = $typecheck == 'taxcode' ? 'code' : $typecheck;
        $where['AND'] = [
            ['=' => [$_typecheck  => $val2check]]
        ];
        $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
        $api_dtinfo->setModule('businesslistings')
            ->setLang('vi')
            ->setAction('ListAllBusinessListings')
            ->setData([
                'where' => $where
            ]);
        $result_dtasia = $api_dtinfo->execute();
        $error_dtasia = $api_dtinfo->getError();
        if (empty($error_dtasia) && $result_dtasia['status'] == 'success' && !empty($result_dtasia['data'])) {
            $is_found_com = true;
            $data_dtinfo = array_values($result_dtasia['data']);
            $customer_info['c_name'] = (isset($data_dtinfo[0]['companyname']) && !empty($data_dtinfo[0]['companyname']) && empty($customer_info['c_name'])) ? $data_dtinfo[0]['companyname'] : $customer_info['c_name'];
            $customer_info['c_address'] = (isset($data_dtinfo[0]['issue_invoice_addr']) && !empty($data_dtinfo[0]['issue_invoice_addr']) && empty($customer_info['c_address'])) ? $data_dtinfo[0]['issue_invoice_addr'] : $customer_info['c_address'];
            $customer_info['email'] = (isset($data_dtinfo[0]['invoice_email']) && !empty($data_dtinfo[0]['invoice_email']) && empty($customer_info['email'])) ? $data_dtinfo[0]['invoice_email'] : $customer_info['email'];
            $customer_info['phone'] = (isset($data_dtinfo[0]['invoice_phone']) && !empty($data_dtinfo[0]['invoice_phone']) && empty($customer_info['phone'])) ? $data_dtinfo[0]['invoice_phone'] : $customer_info['phone'];
            $customer_info['mst'] = (isset($data_dtinfo[0]['code']) && !empty($data_dtinfo[0]['code']) && empty($customer_info['phone'])) ? $data_dtinfo[0]['code'] : $customer_info['mst'];
        }

        // ? 2. (nếu không có, hoặc chưa đủ thông tin) => Lấy từ API doanh nghiệp (dauthau.net) | Nếu nhập MST thì mới check
        if ($typecheck == 'taxcode' && (empty($customer_info['c_name']) || empty($customer_info['c_address']) || empty($customer_info['mst']) || empty($customer_info['phone']) || empty($customer_info['email']))) {
            $error_dtnet = '';
            $api_dtnet = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
            $api_dtnet->setModule('')
                ->setLang('vi')
                ->setAction('GetBidsProfile')
                ->setData([
                    'prof_code' => $val2check
                ]);
            $result_dtnet = $api_dtnet->execute();
            $error_dtnet = $api_dtnet->getError();
            if (empty($error_dtnet) && $result_dtnet['status'] == 'success' && !empty($result_dtnet['profile_info'])) {
                $is_found_com = true;
                $customer_info['c_name'] = (isset($result_dtnet['profile_info']['profile']['prof_name']) && !empty($result_dtnet['profile_info']['profile']['prof_name']) && empty($customer_info['c_name'])) ? $result_dtnet['profile_info']['profile']['prof_name'] : $customer_info['c_name'];
                $customer_info['c_address'] = (isset($result_dtnet['profile_info']['profile']['represent_address']) && !empty($result_dtnet['profile_info']['profile']['represent_address']) && empty($customer_info['c_address'])) ? $result_dtnet['profile_info']['profile']['represent_address'] : $customer_info['c_address'];
                $customer_info['email'] = (isset($result_dtnet['profile_info']['profile']['info_email']) && !empty($result_dtnet['profile_info']['profile']['info_email']) && empty($customer_info['email'])) ? $result_dtnet['profile_info']['profile']['info_email'] : $customer_info['email'];
                $customer_info['phone'] = (isset($result_dtnet['profile_info']['profile']['info_phone']) && !empty($result_dtnet['profile_info']['profile']['info_phone']) && empty($customer_info['phone'])) ? $result_dtnet['profile_info']['profile']['info_phone'] : $customer_info['phone'];
            }
        }

        // ? 3. Lấy thông tin từ cơ hội kinh doanh
        if (empty($customer_info['c_name']) || empty($customer_info['c_address']) || empty($customer_info['mst']) || empty($customer_info['phone']) || empty($customer_info['email'])) {
            $_typecheck = $typecheck == 'taxcode' ? 'tax' : $typecheck;
            $opportunities_info = $db->query('SELECT company_name, address_company, phone, email, tax FROM ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities WHERE ' . trim($_typecheck) . '=' . $db->quote($val2check))->fetch();
            if ($opportunities_info) {
                $is_found_com = true;
                $customer_info['c_name'] = (!empty($opportunities_info['company_name']) && empty($customer_info['c_name'])) ? $opportunities_info['company_name'] : $customer_info['c_name'];
                $customer_info['c_address'] = (!empty($opportunities_info['address_company']) && empty($customer_info['c_address'])) ? $opportunities_info['address_company'] : $customer_info['c_address'];
                $customer_info['email'] = (!empty($opportunities_info['email']) && empty($customer_info['email'])) ? $opportunities_info['email'] : $customer_info['email'];
                $customer_info['phone'] = (!empty($opportunities_info['phone']) && empty($customer_info['phone'])) ? $opportunities_info['phone'] : $customer_info['phone'];
                $customer_info['mst'] = (!empty($opportunities_info['tax']) && empty($customer_info['mst'])) ? $opportunities_info['tax'] : $customer_info['mst'];
            }
        }

        /**
         * TODO: Lấy thông tin đơn hàng
         * 1. Viết API mới, lấy danh sách đơn hàng theo MST => (api.dauthau.asia)
         * 2. Gọi API từ đây để lấy
         */
        $where = [];
        $_typecheck = $typecheck == 'taxcode' ? 'tax' : $typecheck;
        $where['AND'] = [
            ['=' => [$_typecheck => $val2check]]
        ];
        $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
        $api_dtinfo->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingOrdersByLog')
            ->setData([
                'where' => $where
            ]);
        $result_order = $api_dtinfo->execute();
        $error_order = $api_dtinfo->getError();

        if (empty($error_order) && $result_order['status'] == 'success' && !empty($result_order['data'])) {
            // nv_jsonOutput($result_order['data']);
            foreach ($result_order['data'] as $_order) {
                if (($_order['caregiver_id'] == 0 && (defined('NV_IS_SPADMIN') || $is_manage_einvoice)) || isset($filter_uploader_ids[strval($_order['caregiver_id'])]) || defined('NV_IS_SPADMIN') || $is_manage_einvoice) {
                    // ? Lấy thông tin tên KH, địa chỉ từ đơn hàng (nếu vẫn chưa đủ thông tin)
                    $customer_info['c_name'] = (isset($_order['c_name']) && !empty($_order['c_name']) && empty($customer_info['c_name'])) ? $_order['c_name'] : $customer_info['c_name'];
                    $customer_info['c_address'] = (isset($_order['c_address']) && !empty($_order['c_address']) && empty($customer_info['c_address'])) ? $_order['c_address'] : $customer_info['c_address'];
                    $customer_info['email'] = (isset($_order['email']) && !empty($_order['email']) && empty($customer_info['email'])) ? $_order['email'] : $customer_info['email'];
                    $customer_info['phone'] = (isset($_order['phone']) && !empty($_order['phone']) && empty($customer_info['phone'])) ? $_order['phone'] : $customer_info['phone'];
                    $customer_info['mst'] = (isset($_order['tax']) && !empty($_order['tax']) && empty($customer_info['mst'])) ? $_order['tax'] : $customer_info['mst'];

                    // Thông tin đơn hàng
                    $customer_order = [];
                    $customer_order['id'] = $_order['id'];
                    $customer_order['money'] = number_format($_order['money']) . ' đ';
                    $customer_order['discount'] = number_format($_order['discount']) . ' đ';
                    $customer_order['total'] = number_format($_order['total']) . ' đ';
                    $customer_order['price_reduce'] = number_format($_order['price_reduce']) . ' đ';
                    $customer_order['total_end'] = number_format($_order['total_end']) . ' đ';
                    $customer_order['lang'] = $_order['prefix_lang'] == 0 ? 'vi' : 'en';
                    $_vip_titles = [];
                    foreach ($_order['vip'] as $_vip) {
                        $type_label = '';
                        $_vip_parts = explode('_', $_vip);
                        $_key_vip = $_vip;
                        if (count($_vip_parts) === 2) {
                            $_vip = $_vip_parts[0];
                            $type = $_vip_parts[1];

                            if ($type == '1') {
                                $type_label = ' QK';
                            } elseif ($type == '3') {
                                $type_label = ' HT';
                            }
                        }
                        $_vip_titles[$_key_vip] = ($global_arr_vip[$_vip] ?? $_vip) . $type_label;
                    }
                    $customer_order['vip_titles'] = $_vip_titles;
                    // Lấy username của đơn hàng
                    $customer_order['username'] = $db->query('SELECT username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $_order['userid'])->fetchColumn() ?: '';
                    $customer_order['edit_time'] = !empty($_order['edit_time']) ? nv_date('H:i:s, d/m/Y', $_order['edit_time']) : '-';
                    $customer_order['order_code'] = '<a href="' . URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $_order['id'] . '" target="_blank"><strong class="label label-success">' .  sprintf('BDH%010s', $_order['id']) . '</strong><br><small>(' . $customer_order['username'] . ')</small></a>';
                    // Check đơn hàng có hóa đơn tải lên chưa?
                    $_existed_einvoice = $db->query('SELECT t2.einvoice_no FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders t1 RIGHT JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices t2 ON t1.einvoice_id=t2.id WHERE order_id=' . $_order['id'])->fetchColumn();
                    $customer_order['invoice_number'] = $_existed_einvoice ?: '';
                    $customer_info['orders'][] = $customer_order;
                }
            }
        }

        // ? Nếu chưa có thông tin thì lấy thông tin từ hóa đơn đã thêm trước đó => Xử lý sau

        if (!$is_found_com && !count($customer_info['orders'])) {
            $error = $nv_Lang->getModule('no_result');
        } elseif (!count($customer_info['orders'])) {
            $error = sprintf($nv_Lang->getModule('no_result_orders'), $val2check);
        } else {
            nv_jsonOutput([
                'status' => 'success',
                'data' => $customer_info
            ]);
        }
    } else {
        $error = $nv_Lang->getModule('error_no_val2check');
    }

    if (!empty($error)) {
        nv_jsonOutput([
            'status' => 'error',
            'message' => $error
        ]);
    } else {
        nv_jsonOutput([
            'status' => 'error',
            'message' => $nv_Lang->getModule('error_getcominfo')
        ]);
    }
}

// TODO: Lấy HTML xem chi tiết Hóa đơn điện tử
if ($nv_Request->isset_request('view_detail', 'get')) {
    $view_id = $nv_Request->get_int('einvoice_id', 'get,post', 0);
    $error_view = '';
    $view_einvoice = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices WHERE id=' . $view_id)->fetch();
    if ($view_einvoice) {
        // Check thử nếu là sale thì có đang quản lý đơn hàng nào có hóa đơn này không
        $is_caregiver_order = $db->query('SELECT COUNT(*) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders WHERE einvoice_id=' . $view_id . ' AND caregiver_id=' . $admin_info['userid'])->fetchColumn();
        // Kiểm tra có được quyền Xem không
        if (defined('NV_IS_SPADMIN') || $is_manage_einvoice || $my_group_type == 'marketing' || isset($filter_uploader_ids[$view_einvoice['uploader_id']]) || $is_caregiver_order) {
            $xtpl = new XTemplate('einvoice.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
            $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

            $view_einvoice['einvoice_link'] = NV_MAIN_DOMAIN . '/' . $view_einvoice['einvoice_path'];
            $view_einvoice['uploader'] = 'N/A';
            if (isset($all_array_user_id_users[$view_einvoice['uploader_id']])) {
                $uploader = $all_array_user_id_users[$view_einvoice['uploader_id']];
                $view_einvoice['uploader'] = nv_show_name_user($uploader['first_name'], $uploader['last_name'], $uploader['userid']) . ' (' . $uploader['username'] . ')';
            }
            $view_einvoice['uploader_link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&uploader_id=' . $view_einvoice['uploader_id'];
            $view_einvoice['updated_at'] = nv_date('d/m/Y H:i', $view_einvoice['updated_at']);

            $xtpl->assign('VIEW_EINVOICE', $view_einvoice);

            if (!empty($view_einvoice['tax_code'])) {
                $xtpl->parse('view_detail.tax_code');
            }
            if (!empty($view_einvoice['phone'])) {
                $xtpl->parse('view_detail.phone');
            }
            if (!empty($view_einvoice['email'])) {
                $xtpl->parse('view_detail.email');
            }

            $order_rows = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders WHERE einvoice_id=' . $view_einvoice['id'])->fetchAll();
            if ($order_rows) {
                foreach ($order_rows as $order_row) {
                    $order_row['link'] = URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $order_row['order_id'];
                    $order_vips = [];
                    $text_order_vips = [];
                    if (!empty($order_row['vips_vi'])) {
                        $order_vips = array_merge($order_vips, explode(',', $order_row['vips_vi']));
                    }
                    if (!empty($order_row['vips_en'])) {
                        $order_vips = array_merge($order_vips, explode(',', $order_row['vips_en']));
                    }
                    $order_vips = array_unique($order_vips);
                    if (count($order_vips)) {
                        foreach ($order_vips as $order_vip) {
                            $text_order_vips[] = $global_arr_vip[$order_vip];
                        }
                    }
                    $order_row['vips'] = implode('; ', $text_order_vips);
                    $order_row['order_code'] = '<a href="' . URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $order_row['order_id'] . '" target="_blank"><strong class="label label-success">' .  sprintf('BDH%010s', $order_row['order_id']) . '</strong></a>';
                    $xtpl->assign('ORDER_ROW', $order_row);
                    $xtpl->parse('view_detail.loop_order');
                }
            }

            if (defined('NV_IS_SPADMIN') || $is_manage_einvoice) {
                $xtpl->parse('view_detail.show_btn_edit');
            }

            $xtpl->parse('view_detail');
            $res_html = $xtpl->text('view_detail');

            if (empty($error_view)) {
                nv_jsonOutput([
                    'status' => 'success',
                    'html' => $res_html
                ]);
            }
        } else {
            $error_view = $nv_Lang->getModule('no_permission');
        }
    } else {
        $error_view = $nv_Lang->getModule('no_result');
    }

    nv_jsonOutput([
        'status' => 'error',
        'message' => $error_view
    ]);
}

// TODO: Lấy HTML Sửa Hóa đơn điện tử
if ($nv_Request->isset_request('edit_form', 'get')) {
    $view_id = $nv_Request->get_int('einvoice_id', 'get,post', 0);
    $error_view = '';
    $edit_einvoice = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices WHERE id=' . $view_id)->fetch();
    if ($edit_einvoice) {
        // Kiểm tra có được quyền Sửa không
        $vip_order_row = [];
        if (defined('NV_IS_SPADMIN') || $is_manage_einvoice) {
            // Lấy DS đơn hàng đã chọn
            $order_rows = $db->query('SELECT order_id, vips_vi, vips_en FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders WHERE einvoice_id=' . $edit_einvoice['id'])->fetchAll();
            if ($order_rows) {
                foreach ($order_rows as $_order_row) {
                    $order_rows[$_order_row['order_id']] = $_order_row['order_id'];
                    if (!empty($_order_row['vips_vi'])) {
                        $vip_order_row[$_order_row['order_id']]['vi'] = explode(',', $_order_row['vips_vi']);
                    }
                    if (!empty($_order_row['vips_en'])) {
                        $vip_order_row[$_order_row['order_id']]['en'] = explode(',', $_order_row['vips_en']);
                    }
                }
            } else {
                $order_rows = [];
                $vip_order_row = [];
            }

            $xtpl = new XTemplate('einvoice.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
            $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
            $xtpl->assign('OP_BASE_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
            $xtpl->assign('AJAX_SUBMIT_EDIT', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&submit_edit=1');

            $arr_filename_einvoice = explode('/', $edit_einvoice['einvoice_path']);
            $edit_einvoice['einvoice_attachment'] = end($arr_filename_einvoice);
            $edit_einvoice['url_download_einvoice'] = NV_MAIN_DOMAIN . '/' . $edit_einvoice['einvoice_path'];

            $xtpl->assign('EDIT_EINVOICE', $edit_einvoice);

            /**
             * Lấy DS đơn hàng
             */
            $val2check = '';
            $typecheck = 'tax';
            if (!empty($edit_einvoice['tax_code'])) {
                $val2check = $edit_einvoice['tax_code'];
                $typecheck = 'tax';
            } elseif (!empty($edit_einvoice['email'])) {
                $val2check = $edit_einvoice['email'];
                $typecheck = 'email';
            } elseif (!empty($edit_einvoice['phone'])) {
                $val2check = $edit_einvoice['phone'];
                $typecheck = 'phone';
            }

            $where = [];
            $where['AND'] = [
                ['=' => [$typecheck => $val2check]]
            ];
            $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
            $api_dtinfo->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListBiddingOrdersByLog')
                ->setData([
                    'where' => $where
                ]);
            $result_order = $api_dtinfo->execute();
            $error_order = $api_dtinfo->getError();

            if (empty($error_order) && $result_order['status'] == 'success' && !empty($result_order['data'])) {
                foreach ($result_order['data'] as $_order) {
                    if (defined('NV_IS_SPADMIN') || $is_manage_einvoice) {
                        // Thông tin đơn hàng
                        $customer_order = [];
                        $customer_order['id'] = $_order['id'];
                        $customer_order['money'] = number_format($_order['money']) . ' đ';
                        $customer_order['discount'] = number_format($_order['discount']) . ' đ';
                        $customer_order['total'] = number_format($_order['total']) . ' đ';
                        $customer_order['price_reduce'] = number_format($_order['price_reduce']) . ' đ';
                        $customer_order['total_end'] = number_format($_order['total_end']) . ' đ';
                        $customer_order['vip_titles'] = '';
                        $customer_order['lang'] = $_order['prefix_lang'] == 0 ? 'vi' : 'en';
                        $_vip_titles = [];
                        foreach ($_order['vip'] as $_vip_raw) {
                            $type_label = '';
                            $_vip_parts = explode('_', $_vip_raw);

                            if (count($_vip_parts) === 2) {
                                $_vip = $_vip_parts[0];
                                $type = $_vip_parts[1];

                                if ($type == '1') {
                                    $type_label = ' QK';
                                } elseif ($type == '3') {
                                    $type_label = ' HT';
                                }
                            } else {
                                $_vip = $_vip_raw;
                            }

                            $_vip_titles = [
                                'order_id' => $_order['id'],
                                'key' => $_vip_raw,
                                'title' => ($global_arr_vip[$_vip] ?? $_vip) . $type_label,
                                'is_checked' => in_array($_vip_raw, $vip_order_row[$_order['id']][$customer_order['lang']] ?? []) ? 'checked="checked"' : '',
                                'is_disabled' => !isset($order_rows[$_order['id']]) ? 'disabled="disabled"' : '',
                            ];
                            $xtpl->assign('CUSTOMER_ORDER_VIP', $_vip_titles);
                            $xtpl->parse('edit_detail.order_loop.loop_vip');
                        }
                        // Lấy username của đơn hàng
                        $customer_order['username'] = $db->query('SELECT username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $_order['userid'])->fetchColumn() ?: '';
                        $customer_order['edit_time'] = !empty($_order['edit_time']) ? nv_date('H:i:s, d/m/Y', $_order['edit_time']) : '-';
                        $customer_order['order_code'] = '<a href="' . URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $_order['id'] . '" target="_blank"><strong class="label label-success">' .  sprintf('BDH%010s', $_order['id']) . '</strong><br><small>(' . $customer_order['username'] . ')</small></a>';
                        // Check đơn hàng có hóa đơn tải lên chưa?
                        $_existed_einvoice = $db->query('SELECT t2.einvoice_no FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders t1 RIGHT JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices t2 ON t1.einvoice_id=t2.id WHERE order_id=' . $_order['id'])->fetchColumn();
                        $customer_order['invoice_number'] = $_existed_einvoice ?: '';
                        $customer_order['check'] = isset($order_rows[$_order['id']]) ? 'checked="checked"' : (!empty($customer_order['invoice_number']) ? 'disabled="disabled"' : '');
                        $xtpl->assign('CUSTOMER_ORDER', $customer_order);
                        $xtpl->parse('edit_detail.order_loop');
                    }
                }
            }

            $xtpl->parse('edit_detail');

            $res_html = $xtpl->text('edit_detail');

            if (empty($error_view)) {
                nv_jsonOutput([
                    'status' => 'success',
                    'html' => $res_html
                ]);
            }
        } else {
            $error_view = $nv_Lang->getModule('no_permission');
        }
    } else {
        $error_view = $nv_Lang->getModule('no_result');
    }

    nv_jsonOutput([
        'status' => 'error',
        'message' => $error_view
    ]);
}

// TODO: Bắt đầu xử lý Submit Sửa Hóa đơn điện tử
if ($nv_Request->isset_request('submit_edit', 'post,get')) {
    $error_edit = '';
    $data_upload = [];
    $data_upload['einvoice_id'] = $nv_Request->get_int('einvoice_id', 'post', 0);
    $edit_einvoice = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices WHERE id=' . $data_upload['einvoice_id'])->fetch();
    if ($edit_einvoice) {
        $data_upload['einvoice_no'] = $nv_Request->get_title('einvoice_no', 'post', '');
        $data_upload['c_name'] = $nv_Request->get_title('c_name', 'post', '');
        $data_upload['c_address'] = $nv_Request->get_title('c_address', 'post', '');
        $data_upload['order_ids'] = $nv_Request->get_typed_array('order_ids', 'post', 'int', []);
        $data_upload['vip_list'] = $nv_Request->get_array('vip_titles', 'post', []);

        $formatted_invoice_no = formatInvoiceNumber($data_upload['einvoice_no']);
        if (empty($formatted_invoice_no)) {
            $error = $nv_Lang->getModule('error_invalid_einvoice_no');
        } else {
            $data_upload['einvoice_no'] = $formatted_invoice_no;
        }

        // ? Validate dữ liệu
        if (empty($data_upload['c_name'])) {
            $error_edit = $nv_Lang->getModule('error_c_name');
        } elseif (empty($data_upload['c_address'])) {
            $error_edit = $nv_Lang->getModule('error_c_address');
        } elseif (!count($data_upload['order_ids'])) {
            $error_edit = $nv_Lang->getModule('error_order_ids');
        } elseif (empty($data_upload['einvoice_no'])) {
            $error_edit = $nv_Lang->getModule('error_einvoice_no');
        } elseif ($db->query('SELECT COUNT(*) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices WHERE einvoice_no=' . $db->quote($data_upload['einvoice_no']) . ' AND id<>' . $edit_einvoice['id'])->fetchColumn()) {
            $error_edit = $nv_Lang->getModule('error_existed_einvoice_no');
        }

        // Lấy thông tin đơn hàng đã chọn
        if (empty($error_edit)) {
            $val2check = '';
            $typecheck = 'tax';
            if (!empty($edit_einvoice['mst'])) {
                $val2check = $edit_einvoice['mst'];
                $typecheck = 'tax';
            } elseif (!empty($edit_einvoice['email'])) {
                $val2check = $edit_einvoice['email'];
                $typecheck = 'email';
            } elseif (!empty($edit_einvoice['phone'])) {
                $val2check = $edit_einvoice['phone'];
                $typecheck = 'phone';
            }
            // Lấy danh sách gói VIP
            $einvoice_vip_ids = [
                'vi' => [],
                'en' => [],
            ];
            $order_vip_ids = [];
            $order_user_ids = [];
            $order_prefix_langs = [];
            $where = [];
            $where['AND'] = [
                // ['=' => [$typecheck => $val2check]],
                ['IN' => ['order_id' => '(' . implode(',', $data_upload['order_ids']) . ')']]
            ];
            $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
            $api_dtinfo->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListBiddingCustomsLog')
                ->setData([
                    'array_select' => ['vip', 'order_id', 'user_id', 'prefix_lang'],
                    'where' => $where
                ]);
            $result = $api_dtinfo->execute();
            $error_edit = $api_dtinfo->getError();
            if (empty($error_edit) and $result['status'] == 'success' and !empty($result['data'])) {
                $bidding_customs_logs = $result['data'];
                if (is_array($bidding_customs_logs) && $result['total']) {
                    foreach ($bidding_customs_logs as $bidding_customs_log) {
                        if ($bidding_customs_log['prefix_lang'] == 0) {
                            $einvoice_vip_ids['vi'][] = $bidding_customs_log['vip'];
                            $order_vip_ids[$bidding_customs_log['order_id']]['vi'][] = $bidding_customs_log['vip'];
                        } elseif ($bidding_customs_log['prefix_lang'] == 1) {
                            $einvoice_vip_ids['en'][] = $bidding_customs_log['vip'];
                            $order_vip_ids[$bidding_customs_log['order_id']]['en'][] = $bidding_customs_log['vip'];
                        }
                        $order_user_ids[$bidding_customs_log['order_id']] = intval($bidding_customs_log['user_id']);
                    }
                }
            }
        }

        // Kiểm tra đơn hàng có hóa đơn tải lên chưa?
        if (empty($error_edit)) {
            foreach ($data_upload['order_ids'] as $order_id) {
                $_vips_vi = ($data_upload['vip_list'][$order_id]['vi'] ?: $order_vip_ids[$order_id]['vi']) ?? [];
                $_vips_en = ($data_upload['vip_list'][$order_id]['en'] ?: $order_vip_ids[$order_id]['en']) ?? [];

                // ID hóa đơn đang sửa
                $current_einvoice_id = (int)$data_upload['einvoice_id'];

                $existed_order = $db->query('SELECT einvoice_id, order_id, vips_vi, vips_en FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders WHERE order_id=' . $order_id)->fetchAll();

                if ($existed_order) {
                    $conflict_vips_vi = [];
                    $conflict_vips_en = [];

                    foreach ($existed_order as $eo) {
                        $eo_einvoice_id = (int)$eo['einvoice_id'];
                        if ($eo_einvoice_id !== $current_einvoice_id) {
                            // Chỉ kiểm tra các hóa đơn khác
                            if (!empty($eo['vips_vi'])) {
                                $existed_vips_vi = explode(',', $eo['vips_vi']);
                                $conflict_vips_vi = array_merge($conflict_vips_vi, array_intersect($_vips_vi, $existed_vips_vi));
                            }

                            if (!empty($eo['vips_en'])) {
                                $existed_vips_en = explode(',', $eo['vips_en']);
                                $conflict_vips_en = array_merge($conflict_vips_en, array_intersect($_vips_en, $existed_vips_en));
                            }
                        }
                    }

                    // Lọc trùng và rỗng
                    $conflict_vips_vi = array_unique(array_filter($conflict_vips_vi));
                    $conflict_vips_en = array_unique(array_filter($conflict_vips_en));

                    // Thông báo lỗi nếu có gói đã nằm trong hóa đơn khác
                    if (!empty($conflict_vips_vi)) {
                        $duplicate_titles = array_map(function ($vip_key) use ($global_arr_vip) {
                            return $global_arr_vip[$vip_key] ?? $vip_key;
                        }, $conflict_vips_vi);

                        $duplicate_titles_str = implode(', ', $duplicate_titles);

                        // Lấy số hóa đơn đầu tiên (nếu muốn hiển thị, có thể lấy riêng từng cái)
                        $check_einvoice = $db->query('SELECT einvoice_no FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices WHERE id=' . intval($eo_einvoice_id))->fetchColumn();
                        $error_edit .= sprintf($nv_Lang->getModule('existed_orders_by_einvoice'), $duplicate_titles_str, $order_id, $check_einvoice) . "\n";
                    }

                    if (!empty($conflict_vips_en)) {
                        $duplicate_titles = array_map(function ($vip_key) use ($global_arr_vip) {
                            return $global_arr_vip[$vip_key] ?? $vip_key;
                        }, $conflict_vips_en);

                        $duplicate_titles_str = implode(', ', $duplicate_titles);
                        $check_einvoice = $db->query('SELECT einvoice_no FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices WHERE id=' . intval($eo_einvoice_id))->fetchColumn();
                        $error_edit .= sprintf($nv_Lang->getModule('existed_orders_by_einvoice'), $duplicate_titles_str, $order_id, $check_einvoice) . "\n";
                    }
                }
            }
        }

        // Xử lý lưu thông tin
        if (empty($error_edit)) {
            // Lấy thông tin từng vip theo order_id
            $_array_vip = [
                'vi' => [],
                'en' => []
            ];

            foreach ($data_upload['order_ids'] as $_order_id) {
                foreach (['vi', 'en'] as $lang) {
                    foreach ($data_upload['vip_list'][$_order_id][$lang] as $_vip) {
                        $_array_vip[$lang][] = $_vip;
                    }
                }
            }

            // Lấy ID Sale chăm sóc đơn hàng
            $caregiver_ids = [];
            $where = [];
            $where['AND'] = [
                ['IN' => ['id' => '(' . implode(',', $data_upload['order_ids']) . ')']]
            ];
            $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
            $api_dtinfo->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListBiddingOrder')
                ->setData([
                    'array_select' => ['id', 'caregiver_id'],
                    'where' => $where
                ]);
            $result = $api_dtinfo->execute();
            $error = $api_dtinfo->getError();
            if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
                foreach ($result['data'] as $_order) {
                    $caregiver_ids[$_order['id']] = intval($_order['caregiver_id']);
                }
            }

            if (empty($error_edit)) {
                // Lưu tệp
                $upload = new NukeViet\Files\Upload();
                $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
                if (!empty($_FILES['einvoice_path']['tmp_name'])) {
                    // Lưu hóa đơn (nếu có)
                    if (!is_dir(NV_UPLOADS_REAL_DIR . '/einvoices')) {
                        nv_mkdir(NV_UPLOADS_REAL_DIR, 'einvoices');
                    }
                    if (!is_dir(NV_UPLOADS_REAL_DIR . '/einvoices/' . strtolower(change_alias($admin_info['username'])))) {
                        nv_mkdir(NV_UPLOADS_REAL_DIR . '/einvoices', strtolower(change_alias($admin_info['username'])));
                    }
                    $path_to_upload_einvoice = NV_UPLOADS_REAL_DIR . '/einvoices/' . strtolower(change_alias($admin_info['username']));
                    $upload_einvoice_info = $upload->save_file($_FILES['einvoice_path'], $path_to_upload_einvoice, false, $global_config['nv_auto_resize']);
                    if (!empty($upload_einvoice_info['error'])) {
                        $error_edit = $upload_einvoice_info['error'];
                    } elseif (!in_array($upload_einvoice_info['ext'], ['pdf', 'doc', 'docx', 'xml', 'zip'])) {
                        $error_edit = $nv_Lang->getModule('error_file_type');
                    }
                    if (empty($error_edit) && !empty($edit_einvoice['einvoice_path'])) {
                        // Xóa file cũ đi
                        nv_deletefile(NV_ROOTDIR . '/' . $edit_einvoice['einvoice_path']);
                    }
                }

                if (empty($error_edit)) {
                    if (isset($upload_einvoice_info) && !empty($upload_einvoice_info['name'])) {
                        $data_upload['einvoice_path'] = NV_UPLOADS_DIR . '/einvoices/' . strtolower(change_alias($admin_info['username'])) . '/' . str_replace(['/', '\\'], '-', $data_upload['einvoice_no']) . '-' . $upload_einvoice_info['basename'];
                        rename($upload_einvoice_info['name'], $path_to_upload_einvoice . '/' . str_replace(['/', '\\'], '-', $data_upload['einvoice_no']) . '-' . $upload_einvoice_info['basename']);
                    } else {
                        $data_upload['einvoice_path'] = $edit_einvoice['einvoice_path'];
                    }

                    // TODO: LƯU VÀO CSDL
                    /**
                     * ? Lưu bảng _einvoices
                     * Ưu tiên lấy thông tin từ form upload
                     */
                    $einvoice_vip_vi = $_array_vip['vi'] ?: $einvoice_vip_ids['vi'];
                    $einvoice_vip_en = $_array_vip['en'] ?: $einvoice_vip_ids['en'];
                    $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices SET
                            c_name=:c_name,
                            c_address=:c_address,
                            einvoice_no=:einvoice_no,
                            einvoice_path=:einvoice_path,
                            uploader_id=' . $admin_info['userid'] . ',
                            vips_vi=' . $db->quote(implode(',', $einvoice_vip_vi)) . ',
                            vips_en=' . $db->quote(implode(',', $einvoice_vip_en)) . ',
                            updated_at=' . NV_CURRENTTIME . '
                        WHERE id=' . $edit_einvoice['id'];

                    $sth = $db->prepare($sql);
                    $sth->bindParam(':c_name', $data_upload['c_name'], PDO::PARAM_STR);
                    $sth->bindParam(':c_address', $data_upload['c_address'], PDO::PARAM_STR);
                    $sth->bindParam(':einvoice_no', $data_upload['einvoice_no'], PDO::PARAM_STR);
                    $sth->bindParam(':einvoice_path', $data_upload['einvoice_path'], PDO::PARAM_STR);
                    $sth->execute();

                    // Lấy DS đơn hàng đã chọn trước đó
                    $old_order_rows = [];
                    $update_order_rows = []; // Lưu DS đơn hàng sẽ cập nhật số hóa đơn mới
                    $_old_order_rows = $db->query('SELECT order_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders WHERE einvoice_id=' . $edit_einvoice['id'])->fetchAll();
                    if ($_old_order_rows) {
                        foreach ($_old_order_rows as $_order_row) {
                            $old_order_rows[$_order_row['order_id']] = $_order_row['order_id'];
                        }
                    }

                    // ? Lưu bảng _einvoice_orders
                    foreach ($data_upload['order_ids'] as $order_id) {
                        /**
                         * Nếu chưa có thì thêm mới
                         * Nếu như không chọn gói vip trong đơn hàng thì mặc định chọn hết
                         */
                        $arr_vips_vi = ($data_upload['vip_list'][$order_id]['vi'] ?: $order_vip_ids[$order_id]['vi']) ?? [];
                        $arr_vips_en = ($data_upload['vip_list'][$order_id]['en'] ?: $order_vip_ids[$order_id]['en']) ?? [];
                        if (!isset($old_order_rows[$order_id])) {
                            $_o_user_id = 0;
                            $_o_user_name = 'N/A';
                            if (isset($order_user_ids[$order_id]) && $order_user_ids[$order_id] > 0) {
                                $o_user = $db->query('SELECT username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $order_user_ids[$order_id])->fetch();
                                $_o_user_id = $order_user_ids[$order_id];
                                $_o_user_name = $o_user['username'];
                            }
                            $_caregiver_id = $caregiver_ids[$order_id] ?? 0;
                            $sql = 'INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders (
                                    einvoice_id, caregiver_id, order_id, site_id, user_id, username, vips_vi, vips_en
                                ) VALUES (
                                    ' . $edit_einvoice['id'] . ',
                                    ' . $_caregiver_id . ',
                                    ' . $order_id . ',
                                    1,
                                    ' . $_o_user_id . ',
                                    ' . $db->quote($_o_user_name) . ',
                                    ' . $db->quote(implode(',', $arr_vips_vi)) . ',
                                    ' . $db->quote(implode(',', $arr_vips_en)) . '
                                )';
                            $db->insert_id($sql, 'id');
                            // Lưu log đơn hàng
                            $logApi = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                            // Cập nhật số hóa đơn điện tử vào đơn hàng
                            $logApi->setModule('bidding')
                                ->setLang('vi')
                                ->setAction('UpdateBiddingOrder')
                                ->setData([
                                    'biddingorder_id' => $order_id,
                                    'admin_id' => $admin_info['admin_id'],
                                    'data' => [
                                        'invoice_number' => $data_upload['einvoice_no']
                                    ]
                                ])->execute();

                            // Ghi log hóa đơn
                            $logApi->setModule('bidding')
                                ->setLang('vi')
                                ->setAction('CreateBiddingAllLogs')
                                ->setData([
                                    'userid' => $admin_info['admin_id'],
                                    'log_area' => 1,
                                    'log_key' => 'LOG_CHANGE_ORDER_EINVOICE',
                                    'log_time' => NV_CURRENTTIME,
                                    'log_data' => [
                                        [$nv_Lang->getModule('log_update_einvoice_success'), $nv_Lang->getModule('new')],
                                        [$nv_Lang->getModule('einvoice_no') . ': ', $data_upload['einvoice_no']]
                                    ],
                                    'order_id' => $order_id
                                ])->execute();
                        } else {
                            unset($old_order_rows[$order_id]);
                            $update_order_rows[] = $order_id;
                            $sql_update_einvoice_order = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders SET
                                    vips_vi=' . $db->quote(implode(',', $arr_vips_vi)) . ',
                                    vips_en=' . $db->quote(implode(',', $arr_vips_en)) . '
                                WHERE order_id=' . $order_id . ' AND einvoice_id=' . $edit_einvoice['id'];
                            $db->query($sql_update_einvoice_order);
                        }
                    }

                    // Xóa các đơn hàng cũ (nếu không chọn)
                    $order2dels = array_values($old_order_rows);
                    if ($order2dels) {
                        foreach ($order2dels as $order2del) {
                            $db->query('DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders WHERE order_id=' . $order2del);
                            $logApi = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                            // Cập nhật số hóa đơn điện tử vào đơn hàng
                            $logApi->setModule('bidding')
                                ->setLang('vi')
                                ->setAction('UpdateBiddingOrder')
                                ->setData([
                                    'biddingorder_id' => $order2del,
                                    'admin_id' => $admin_info['admin_id'],
                                    'data' => [
                                        'invoice_number' => ''
                                    ]
                                ])->execute();

                            $logApi->setModule('bidding')
                                ->setLang('vi')
                                ->setAction('CreateBiddingAllLogs')
                                ->setData([
                                    'userid' => $admin_info['admin_id'],
                                    'log_area' => 1,
                                    'log_key' => 'LOG_CHANGE_ORDER_EINVOICE',
                                    'log_time' => NV_CURRENTTIME,
                                    'log_data' => [
                                        [$nv_Lang->getModule('log_delete_einvoice_success'), $edit_einvoice['einvoice_no']]
                                    ],
                                    'order_id' => $order2del
                                ])->execute();
                        }
                    }

                    // Cập nhật lại số hóa đơn mới cho các đơn hàng
                    if (count($update_order_rows) && $edit_einvoice['einvoice_no'] != $data_upload['einvoice_no']) {
                        foreach ($update_order_rows as $update_order_id) {
                            $logApi = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                            $logApi->setModule('bidding')
                                ->setLang('vi')
                                ->setAction('UpdateBiddingOrder')
                                ->setData([
                                    'biddingorder_id' => $update_order_id,
                                    'admin_id' => $admin_info['admin_id'],
                                    'data' => [
                                        'invoice_number' => $data_upload['einvoice_no']
                                    ]
                                ])->execute();

                            $logApi->setModule('bidding')
                                ->setLang('vi')
                                ->setAction('CreateBiddingAllLogs')
                                ->setData([
                                    'userid' => $admin_info['admin_id'],
                                    'log_area' => 1,
                                    'log_key' => 'LOG_CHANGE_ORDER_EINVOICE',
                                    'log_time' => NV_CURRENTTIME,
                                    'log_data' => [
                                        [$nv_Lang->getModule('log_update_einvoice_success'), $nv_Lang->getModule('new')],
                                        [$nv_Lang->getModule('einvoice_no') . ': ', $data_upload['einvoice_no']]
                                    ],
                                    'order_id' => $update_order_id
                                ])->execute();
                        }
                    }

                    nv_jsonOutput([
                        'status' => 'success',
                        'message' => $nv_Lang->getModule('einvoice_update_success')
                    ]);
                }
            }
        }
    } else {
        $error_edit = $nv_Lang->getModule('no_result');
    }

    nv_jsonOutput([
        'status' => 'error',
        'message' => $error_edit
    ]);
}

// TODO: HIỂN THỊ GIAO DIỆN TRANG DANH SÁCH HÓA ĐƠN ĐIỆN TỬ
$page_title = $nv_Lang->getModule('einvoice');

$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op;
$per_page = 20;
$page = $nv_Request->get_int('page', 'post,get', 1);

$array_search = array();
$array_search['q'] = $nv_Request->get_title('q', 'post,get', '');
$array_search['vip_id'] = $nv_Request->get_typed_array('vip_id', 'post,get', 'int', []);
$array_search['uploader_id'] = $nv_Request->get_typed_array('uploader_id', 'post,get', 'int', []);

// Lấy danh sách hóa đơn điện tử để hiển thị
$einvoice_rows = [];
$where = [];

if (!empty($array_search['q'])) {
    $where_q = [];
    // Tìm theo username
    $username_einvoice_ids = [];
    $_einvoice_with_usernames = $db->query('SELECT einvoice_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders WHERE username LIKE ' . $db->quote('%' . $array_search['q'] . '%'))->fetchAll();
    if ($_einvoice_with_usernames && count($_einvoice_with_usernames)) {
        foreach ($_einvoice_with_usernames as $_einvoice_with_username) {
            $username_einvoice_ids[] = $_einvoice_with_username['einvoice_id'];
        }
    }
    $username_einvoice_ids = array_unique($username_einvoice_ids);
    if (count($username_einvoice_ids)) {
        $where_q[] = 't1.id IN (' . implode(',', $username_einvoice_ids) . ')';
    }
    // Tìm theo MST
    $where_q[] = 't1.tax_code LIKE ' . $db->quote($array_search['q'] . '%');
    // Tìm theo SĐT
    $where_q[] = 't1.phone LIKE ' . $db->quote($array_search['q'] . '%');
    // Tìm theo Email
    $where_q[] = 't1.email LIKE ' . $db->quote($array_search['q'] . '%');
    // Tìm theo Số hóa đơn
    $where_q[] = 't1.einvoice_no LIKE ' . $db->quote('%' . $array_search['q'] . '%');
    // Tìm theo Tên công ty
    $where_q[] = 't1.c_name LIKE ' . $db->quote('%' . $array_search['q'] . '%');

    $where[] = '(' . implode(' OR ', $where_q) . ')';
}

if (!empty($array_search['vip_id'])) {
    $where_vip = [];
    foreach ($array_search['vip_id'] as $svid) {
        $where_vip[] = 'FIND_IN_SET(' . $svid . ', t1.vips_vi)';
        $where_vip[] = 'FIND_IN_SET(' . $svid . ', t1.vips_en)';
    }
    $where[] = '(' . implode(' OR ', $where_vip) . ')';
}

if (!empty($array_search['uploader_id'])) {
    if (!(defined('NV_IS_SPADMIN') || $my_group_type == 'marketing' || $is_manage_einvoice)) {
        foreach ($array_search['uploader_id'] as $_isuid => $_suid) {
            if (!in_array($_suid, array_values($filter_uploader_ids))) {
                unset($array_search['uploader_id'][$_isuid]);
            }
        }
        if (!empty($array_search['uploader_id'])) {
            $where[] = 't1.uploader_id IN (' . implode(',', $array_search['uploader_id']) . ')';
        } else {
            $where[] = 't1.uploader_id IN (' . implode(',', array_values($filter_uploader_ids)) . ')';
        }
    } else {
        $where[] = 't1.uploader_id IN (' . implode(',', $array_search['uploader_id']) . ')';
    }
} elseif (!(defined('NV_IS_SPADMIN') || $my_group_type == 'marketing' || $is_manage_einvoice)) {
    $where[] = 't2.caregiver_id = ' . $admin_info['userid'];
}

// Lấy tổng số lượng rows
$db->sqlreset()
    ->select('COUNT(DISTINCT t1.id)')
    ->join('INNER JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders t2 ON t1.id = t2.einvoice_id')
    ->from(NV_PREFIXLANG . '_' . $module_data . '_einvoices t1');

if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}
$sth = $db->prepare($db->sql());
$sth->execute();
$num_items = $sth->fetchColumn();

$db->select('DISTINCT t1.*')
    ->from(NV_PREFIXLANG . '_' . $module_data . '_einvoices t1')
    ->join('INNER JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders t2 ON t1.id = t2.einvoice_id')
    ->order('t1.created_at DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);

$sth = $db->prepare($db->sql());
$sth->execute();

$xtpl = new XTemplate('einvoice.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('Q', $array_search['q']);
$xtpl->assign('OP_BASE_URL', $base_url);
$xtpl->assign('AJAX_GETCOMINFO', $base_url . '&getcominfo=1');
$xtpl->assign('AJAX_SUBMIT_UPLOAD', $base_url . '&submit=1');
$xtpl->assign('AJAX_SUBMIT_UPLOAD_EINVOICE', $base_url . '&submit_einvoice=1');
$xtpl->assign('NUM_ITEMS', $num_items);

// Nếu Marketing/Sale thì ẩn nút Tải lên Hóa đơn
if ($is_manage_einvoice || defined('NV_IS_SPADMIN')) {
    $xtpl->parse('main.allow_add');
    $xtpl->parse('main.allow_add_modal');
}
// Hiển thị danh sách chọn gói VIP tìm kiếm
foreach ($global_arr_vip as $vip_id => $vip_title) {
    $xtpl->assign('VIP', [
        'id' => $vip_id,
        'title' => $vip_title,
        'selected' => in_array($vip_id, $array_search['vip_id']) ? 'selected="selected"' : ''
    ]);
    $xtpl->parse('main.search_vip');
}

// Hiển thị danh sách người tải lên
if (defined('NV_IS_SPADMIN') || $my_group_type == 'marketing' || $is_manage_einvoice) {
    foreach ($all_array_user_id_users as $value) {
        $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
        $value['selected'] = in_array($value['userid'], $array_search['uploader_id']) ? 'selected="selected"' : '';
        $value['title'] = $value['username'] . ' (' . $fullname . ')';
        $xtpl->assign('UPLOADER_ID', $value);
        $xtpl->assign('COL_SEARCH', '12');
        $xtpl->parse('main.search_uploader.search_uploader_id');
    }
    $xtpl->parse('main.search_uploader');
} else {
    $xtpl->assign('COL_SEARCH', '18');
}

$stt = ($page - 1) * $per_page;
while ($row = $sth->fetch()) {
    $row['orders'] = '';
    $row['vips'] = '';

    // Xử lý file hóa đơn
    $einvoice_files = json_decode($row['einvoice_path'], true);
    if (is_array($einvoice_files)) {
        foreach ($einvoice_files as $file) {
            $row['einvoice_files'][] = [
                'url_download_einvoice' => NV_MAIN_DOMAIN . '/' . $file['path'],
                'einvoice_attachment' => $file['filename'],
            ];
            $xtpl->parse('main.loop_row.muti_file.loop');
        }
        $xtpl->parse('main.loop_row.muti_file');
    } else {
        $arr_filename_einvoice = explode('/', $row['einvoice_path']);
        $row['einvoice_attachment'] = end($arr_filename_einvoice);
        $row['url_download_einvoice'] = NV_MAIN_DOMAIN . '/' . $row['einvoice_path'];
        $xtpl->parse('main.loop_row.only_file');
    }

    $order_rows = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders WHERE einvoice_id=' . $row['id'])->fetchAll();
    $order_vips = [];
    if ($order_rows) {
        foreach ($order_rows as $order_row) {
            $row['orders'] .= '<a href="' . URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $order_row['order_id'] . '" target="_blank"><strong class="label label-success">' .  sprintf('BDH%010s', $order_row['order_id']) . '</strong> <small>(' . $order_row['username'] . ')</small></a><br>';
            if (!empty($order_row['vips_vi'])) {
                $order_vips = array_merge($order_vips, explode(',', $order_row['vips_vi']));
            }
            if (!empty($order_row['vips_en'])) {
                $order_vips = array_merge($order_vips, explode(',', $order_row['vips_en']));
            }
        }
    }
    $order_vips = array_unique($order_vips);
    if (count($order_vips)) {
        foreach ($order_vips as $order_vip) {
            $type_label = '';
            $_vip_parts = explode('_', $order_vip);

            if (count($_vip_parts) === 2) {
                $_vip = $_vip_parts[0];
                $type = $_vip_parts[1];

                if ($type == '1') {
                    $type_label = ' QK';
                } elseif ($type == '3') {
                    $type_label = ' HT';
                }
            } else {
                $_vip = $order_vip;
            }
            $row['vips'] .= ' ' . ($global_arr_vip[$_vip] ?? $_vip) . $type_label . ';';
        }
        $row['vips'] = trim($row['vips'], ';');
    }

    $row['del_url'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;delete_id=' . $row['id'] . '&amp;delete_checkss=' . md5($row['id'] . NV_CACHE_PREFIX . $client_info['session_id']);

    $row['stt'] = ++$stt;
    // Lấy tên người tải lên (thường là sale)
    $row['uploader'] = 'N/A';
    if (isset($all_array_user_id_users[$row['uploader_id']])) {
        $uploader = $all_array_user_id_users[$row['uploader_id']];
        $row['uploader'] = nv_show_name_user($uploader['first_name'], $uploader['last_name'], $uploader['userid']);
    }
    $row['updated_at'] = nv_date('d/m/Y H:i', $row['updated_at']);

    $xtpl->assign('ROW', $row);
    if (defined('NV_IS_SPADMIN') || $is_manage_einvoice) {
        $xtpl->parse('main.loop_row.allow_del_einvoice');
    }
    if (defined('NV_IS_SPADMIN') || $is_manage_einvoice) {
        $xtpl->parse('main.loop_row.allow_edit_einvoice');
    }
    if (!empty($row['tax_code'])) {
        $xtpl->parse('main.loop_row.tax_code');
    }
    if (!empty($row['phone']) || !empty($row['email'])) {
        $xtpl->parse('main.loop_row.phone_email');
    }
    $xtpl->parse('main.loop_row');
}

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
if (!empty($generate_page)) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
