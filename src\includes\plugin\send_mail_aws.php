<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_MAINFILE')) {
    exit('Stop!!!');
}

if (!function_exists('aws_sendmail')) {
    /**
     * aws_sendmail()
     *
     * @param array $sm_parameters
     * @param array $config
     * @return string|boolean|boolean|mixed
     */
    function aws_sendmail($sm_parameters, $config = [])
    {
        global $global_config;
        if (empty($config)) {
            $config = $global_config;
        }

        // https://docs.aws.amazon.com/sdk-for-php/v3/developer-guide/guide_configuration.html#credentials
        $credentials = new Aws\Credentials\Credentials('********************', 'gsKOc6PaAyiQc4c33lzzWrn+XLeTffcoBuIJ029r');
        $SesClient = new Aws\Ses\SesClient([
            'credentials' => $credentials,
            'version' => '2010-12-01',
            'region' => 'ap-southeast-1',
            'debug' => false,
            'use_aws_shared_config_files' => false
        ]);

        // Replace <EMAIL> with your "From" address. This address must be verified with Amazon SES.
        $from_name = base64_encode($sm_parameters['from_name']);
        $verified_sender_email = "=?utf-8?B?$from_name?= <" . $config['site_email'] . ">";

        $sm_parameters['message'] = str_replace('cid:sitelogo', NV_MY_DOMAIN . NV_BASE_SITEURL . $config['site_logo'], $sm_parameters['message']);

        try {
            // https://docs.aws.amazon.com/aws-sdk-php/v3/api/api-email-2010-12-01.html#sendemail
            $parameters = [
                'Destination' => [
                    'BccAddresses' => array_keys($sm_parameters['bcc']),
                    'CcAddresses' => array_keys($sm_parameters['cc']),
                    'ToAddresses' => $sm_parameters['to']
                ],
                'ReplyToAddresses' => array_keys($sm_parameters['reply']),
                'Source' => $verified_sender_email,
                'Message' => [
                    'Body' => [
                        'Html' => [
                            'Charset' => 'UTF-8',
                            'Data' => $sm_parameters['message']
                        ],
                        'Text' => [
                            'Charset' => 'UTF-8',
                            'Data' => strip_tags($sm_parameters['message'])
                        ]
                    ],
                    'Subject' => [
                        'Charset' => 'UTF-8',
                        'Data' => nv_unhtmlspecialchars($sm_parameters['subject'])
                    ]
                ], // If you aren't using a configuration set, comment or delete the following line
                'ConfigurationSetName' => 'DauThauNet'
            ];

            $result = $SesClient->sendEmail($parameters);

            $messageid = $result['MessageId'];

            if ($messageid != '') {
                return (!empty($sm_parameters['testmode']) ? '' : true);
            } else {
                return (!empty($sm_parameters['testmode']) ? 'Send error!!!' : false);
            }
        } catch (Aws\Exception\AwsException $e) {
            trigger_error($e->getMessage());
            return (!empty($sm_parameters['testmode']) ? $e->getMessage() : false);
        } catch (exception $e) {
            trigger_error($e->getMessage());
            return (!empty($sm_parameters['testmode']) ? $e->getMessage() : false);
        }

        return (!empty($sm_parameters['testmode']) ? 'Unknown error' : false);
    }
}

nv_add_hook($module_name, 'sector1', $priority, function () {
    global $global_config;

    $global_config['other_sendmail_method'] = 'aws_sendmail';
});
