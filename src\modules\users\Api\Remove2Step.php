<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\users\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

/**
 * <AUTHOR>
 * @desc Gửi yêu cầu gỡ xác thực 2 bước của thành viên
 */
class Remove2Step implements IApi
{
    private $result;

    /**
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_GOD;
    }

    /**
     * @return string
     */
    public static function getCat()
    {
        return 'user';
    }

    /**
     * {@inheritDoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     * {@inheritDoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $nv_Request, $db, $db_config, $nv_Cache, $global_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        $admin_id = Api::getAdminId();

        // Lấy config users
        $global_users_config = [];
        $cacheFile = NV_LANG_DATA . '_' . $module_data . '_config_' . NV_CACHE_PREFIX . '.cache';
        $cacheTTL = 3600;
        if (($cache = $nv_Cache->getItem($module_name, $cacheFile, $cacheTTL)) != false) {
            $global_users_config = unserialize($cache);
        } else {
            $sql = 'SELECT config, content FROM ' . $db_config['prefix'] . '_' . $module_data . '_config';
            $result = $db->query($sql);
            while ($row = $result->fetch()) {
                $global_users_config[$row['config']] = $row['content'];
            }
            $cache = serialize($global_users_config);
            $nv_Cache->setItem($module_name, $cacheFile, $cache, $cacheTTL);
        }

        $uid = $nv_Request->get_absint('userid', 'post', 0);
        if (empty($uid)) {
            return $this->result->setCode('0001')->setMessage('Invalid data')->getResult();
        }

        $row = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . ' WHERE userid=' . $uid)->fetch();
        if (empty($row)) {
            return $this->result->setCode('0001')->setMessage('User not exists')->getResult();
        }

        if (empty($global_config['remove_2step_method']) or empty($row['question'])) {
            $db->query('UPDATE ' . $db_config['prefix'] . '_' . $module_data . ' SET active2step=2 WHERE userid=' . $uid);
            // Thêm thông báo vào hệ thống
            $access_admin = unserialize($global_users_config['access_admin']);
            if (isset($access_admin['access_editus'])) {
                for ($i = 1; $i <= 3; ++$i) {
                    if (!empty($access_admin['access_editus'][$i])) {
                        $admin_view_allowed = $i == 3 ? 0 : $i;
                        nv_insert_notification($module_name, 'remove_2step_request', [
                            'title' => $row['username'],
                            'uid' => $row['userid']
                        ], $uid, 0, 0, 1, $admin_view_allowed, 1);
                    }
                }
            }

            $info = $nv_Lang->getModule('remove_2step_send');
        } else {
            $db->query('DELETE FROM ' . $db_config['prefix'] . '_' . $module_data . '_backupcodes WHERE userid=' . $uid);
            $db->query('UPDATE ' . $db_config['prefix'] . '_' . $module_data . " SET active2step=0, secretkey='', last_update=" . NV_CURRENTTIME . ' WHERE userid=' . $uid);

            $greeting = greeting_for_user_create($row['username'], $row['first_name'], $row['last_name'], $row['gender']);
            $message = $nv_Lang->getModule('remove_2step_content', $greeting, $global_config['site_name']);
            @nv_sendmail_async([
                $global_config['site_name'],
                $global_config['site_email']
            ], $row['email'], $nv_Lang->getModule('remove_2step_subject'), $message);

            $info = $nv_Lang->getModule('remove_2step_success');
        }

        $this->result->set('info', $info);
        return $this->result->setSuccess()->getResult();
    }
}
