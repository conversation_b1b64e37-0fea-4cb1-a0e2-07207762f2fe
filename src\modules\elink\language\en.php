<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

$lang_translator['author'] = 'VINADES.,JSC <<EMAIL>>';
$lang_translator['createdate'] = '04/03/2010, 15:22';
$lang_translator['copyright'] = '@Copyright (C) 2012 VINADES.,JSC. All rights reserved';
$lang_translator['info'] = '';
$lang_translator['langtype'] = 'lang_module';

$lang_module['close'] = 'Close';
$lang_module['select_action'] = 'Selection action';
$lang_module['affiliate'] = 'Affiliate Marketing';
$lang_module['promotion'] = 'Promotion Code';
$lang_module['affiliate_disabled'] = 'Affiliate Marketing is disabled for your account';
$lang_module['promotion_disabled'] = 'Promotion Code is disabled for your account';
$lang_module['your_affiliate'] = 'Your Affiliate Code';
$lang_module['access_manager_page'] = 'Go to management page';
$lang_module['affiliate_visitors'] = 'Number of visitors';
$lang_module['affiliate_users'] = 'Number of members introduced';
$lang_module['promotion_number'] = 'Number of Promotion Code created';
$lang_module['promotion_used'] = 'Number of uses';
$lang_module['affiliate_manager'] = 'Manage Affiliate Marketing';
$lang_module['affiliate_links'] = 'Linked statistics';
$lang_module['affiliate_links1'] = 'Destination link statistics';
$lang_module['affiliate_links2'] = 'Source link statistics';
$lang_module['affiliate_stat_res'] = 'Total number of records';
$lang_module['creat_link'] = 'Create link';
$lang_module['copy'] = 'Copy';
$lang_module['your_link_is'] = 'Your link is';
$lang_module['stat_15_affiliate_users'] = '15 newest members introduced';
$lang_module['stat_15_affiliate_links'] = '15 most recently visited destination links';
$lang_module['stat_15_affiliate_links_ref'] = '15 most recently accessed source links';
$lang_module['stat_10u_no'] = 'No members have been successfully introduced';
$lang_module['stat_10link_no'] = 'No link has been accessed';
$lang_module['stat_10link_no_ref'] = 'No referal link has been accessed yet';
$lang_module['view_detail'] = 'Detail';
$lang_module['affiliate_allusers'] = 'Members introduced';
$lang_module['nolimit'] = 'Unlimited';
$lang_module['all'] = 'All';
$lang_module['year'] = 'Year';
$lang_module['noyetuse'] = 'Not used yet';
$lang_module['used'] = 'Used';
$lang_module['view'] = 'View';
$lang_module['aff_tool1'] = 'Please attach this code to any link of';
$lang_module['aff_tool2'] = 'to introduce others. The rules for creating links are as follows';
$lang_module['aff_tool3'] = 'The tool below helps you get a link with a valid Affiliate code. Copy the link into the box below and click the create link button.';
$lang_module['aff_copied'] = 'Link copied';
$lang_module['aff_error_link1'] = 'This link is not valid, please check again';
$lang_module['aff_error_link2'] = 'Please enter the link of the website';
$lang_module['aff_no_promo'] = 'You have not created any promo code yet. Click here to create a new one';
$lang_module['aff_num_promo1'] = 'You created';
$lang_module['aff_num_promo2'] = 'promo code but have not selected the default promo code. Click here to view, edit to choose default promo code';
$lang_module['aff_default_promo1'] = 'The default promo code for your Affiliate Marketing is';
$lang_module['aff_default_promo2'] = 'Click here to edit this code';
$lang_module['aff_uname'] = 'Full name';
$lang_module['aff_email'] = 'Email';
$lang_module['aff_phone'] = 'Phone';
$lang_module['affl_site'] = 'All sites';
$lang_module['affl_c0'] = 'Affiliate Link + Promotion';
$lang_module['affl_c1'] = 'Affiliate Link';
$lang_module['affl_c2'] = 'Promotion Link';
$lang_module['affl_t0'] = 'Target + Referral link';
$lang_module['affl_t1'] = 'Target link';
$lang_module['affl_t2'] = 'Referral link';
$lang_module['vip1'] = 'VIP 1';
$lang_module['vip2'] = 'VIP 2';
$lang_module['vip3'] = 'VIP 3';
$lang_module['vip31'] = 'VIP 3 Plus';
$lang_module['vip32'] = 'PLP Report';
$lang_module['vip7'] = 'VIP 7';
$lang_module['vip11'] = 'VIP 1 International';
$lang_module['vip21'] = 'VIP 2 International';
$lang_module['vip5'] = 'VIP 5';
$lang_module['vip4'] = 'VIP 4';
$lang_module['vip99'] = 'VIEWEB';
$lang_module['vip19'] = 'PRO 1';
$lang_module['vip88'] = 'X1';
$lang_module['vip89'] = 'X2';
$lang_module['vip66'] = 'API VIP';
$lang_module['vip68'] = 'API PRO';
$lang_module['vip100'] = 'T100';
$lang_module['vip101'] = 'X4';
$lang_module['vip77'] = 'T0';
$lang_module['vip69'] = 'SIEUVIP';
$lang_module['vip6'] = 'VIP 6';
$lang_module['vip55'] = 'TDT';
$lang_module['bvieweb'] = 'B-VIEWEB';
$lang_module['bvip1'] = 'B-PRO 1';
$lang_module['bvip2'] = 'B-PRO 2';
$lang_module['bpro'] = 'B-VIP';
$lang_module['abasic'] = 'A-BASIC';
$lang_module['avip1'] = 'A-PRO 1';
$lang_module['apro1'] = 'A-PRO 2';
$lang_module['apro2'] = 'A-VIP';
$lang_module['x3'] = 'X3';
$lang_module['promotion_manager'] = 'Promotion Code manager';
$lang_module['promotion_creat'] = 'Create Promotion Code';
$lang_module['promotion_edit'] = 'Edit Promotion Code';
$lang_module['promotion_code'] = 'Promotion Code';
$lang_module['promotion_auto'] = 'Create auto';
$lang_module['promotion_type'] = 'Promotion type';
$lang_module['promotion_type0'] = 'Discounted &#37; Invoices (Coupon, Maximum <span class="text-danger">%s&#37;</span>)';
$lang_module['promotion_type1'] = 'Discounted by fixed amount (Voucher, Maximum <span class="text-danger">%s&nbsp;VNĐ</span>)';
$lang_module['promotion_typev0'] = 'Discounted according to &#37; invoices (Coupon)';
$lang_module['promotion_typev1'] = 'Discounted by fixed amount (Voucher)';
$lang_module['promotion_value'] = 'Promotion value';
$lang_module['promotion_svalue'] = 'Promition value';
$lang_module['promotion_value_help'] = '% or VND corresponding to the promotional style above';
$lang_module['promotion_use_number'] = 'Promotion used';
$lang_module['promotion_use_number1'] = 'Number of times used';
$lang_module['promotion_use_number_help'] = 'To leave or drop 0 is unlimited number of times the discounted code';
$lang_module['promotion_use_user'] = 'Number of people used';
$lang_module['promotion_use_user1'] = 'Number of people used';
$lang_module['promotion_use_user_help'] = 'To leave or drop 0 is unlimited number of people who use discount code';
$lang_module['promotion_use_peruser'] = 'The number of times used for each person';
$lang_module['promotion_use_peruser1'] = 'Users / people';
$lang_module['promotion_use_peruser_help'] = '0 is unlimited number of discounted use times, and must be less than or equal to the total number of times.';
$lang_module['promotion_use_limityear'] = 'Number of years of payment of discounted code';
$lang_module['promotion_use_limityear1'] = 'Number of years of payment';
$lang_module['promotion_use_limityear_help'] = 'If you choose more than 1 value please select enough about between it. For example, select 1 year 3 years, select both years. If not system will notify the users understanding. Note: When selecting over 1 year, this promotion code will not be applied to the order of the X1 or VIEWEB';
$lang_module['promotion_apply_lang'] = 'Apply to language';
$lang_module['promotion_apply_lang_info'] = 'This value applies to DauThau.info packages, DauThau.Net packages always applies to all languages';
$lang_module['promotion_use_type'] = 'The code is applied to';
$lang_module['promotion_use_typev1'] = 'Apply to';
$lang_module['promotion_use_type0'] = 'Registered for use for the first time (previously not registered)';
$lang_module['promotion_use_type1'] = 'Extender of the service';
$lang_module['promotion_use_type2'] = 'New members registered account (within 24h)';
$lang_module['promotion_use_type_help'] = 'If not selected, the code will be applied to all';
$lang_module['promotion_start_time'] = 'The day begin';
$lang_module['promotion_end_time'] = 'The end date';
$lang_module['promotion_time_help'] = 'Leave empty if no control is on';
$lang_module['promotion_vip_apply'] = 'Apply for service pack';
$lang_module['promotion_vip_apply1'] = 'Apply for the package';
$lang_module['promotion_vip_apply_help'] = 'If not selected, all packages will be applied';
$lang_module['promotion_vip_include'] = 'Apply only when registering with the following packages';
$lang_module['promotion_vip_includev1'] = 'Only when registered with';
$lang_module['promotion_is_affiliate'] = 'Use as default for Affiliate Code';
$lang_module['promotion_is_affiliatev1'] = 'Affiliate Code';
$lang_module['promotion_is_affiliate0'] = 'No';
$lang_module['promotion_is_affiliate1'] = 'Yes';
$lang_module['promotion_max'] = 'maximum';
$lang_module['promotion_error_nocode'] = 'Error: You have not entered promotion code';
$lang_module['promotion_error_rule'] = 'Error: Invalid promotion code. Promotion code is only available and has a minimum of% s characters, up to 50 characters. The shorter the easier the guess that the longer the longer, the more difficulty enter';
$lang_module['promotion_error_exists'] = 'Error: This promotion code has been used, enter the other code';
$lang_module['promotion_error_typeallowed'] = 'Error: Invalid promotion type';
$lang_module['promotion_error_value_min'] = 'Error: Promotional value must be greater than 0';
$lang_module['promotion_error_value_max'] = 'Error: Promotional value exceeds the limit';
$lang_module['promotion_error_use_value'] = 'Error: The number used times per person exceeds the total number of times';
$lang_module['promotion_error_time'] = 'Error: The end date must be later or by date';
$lang_module['promotion_view_user'] = 'View user';
$lang_module['promotion_view_user1'] = 'User code statistical statistical statistical';
$lang_module['promotion_no_userused'] = 'This code has no users yet';
$lang_module['promotion_view_orders'] = 'Use of the use';
$lang_module['promotion_view_links'] = 'Link statistics';
$lang_module['promotion_view_orders1'] = 'Statistics use code';
$lang_module['promotion_no_orders'] = 'This promotion code has not been used';
$lang_module['promotion_top_user'] = 'List of 5 customers who just used your Promotion Code';
$lang_module['promotion_top_user_no'] = 'No customer promotion Promotion';
$lang_module['promotion_top_order'] = 'List of 5 latest orders using your Promotion Code';
$lang_module['promotion_top_order_no'] = 'There are no orders used promotion code or attached to your referral cod';
$lang_module['promotion_all_users'] = 'All customers use Promotion Code';
$lang_module['promotion_all_users1'] = 'All customers';
$lang_module['promotion_all_orders'] = 'All orders';
$lang_module['promotion_all_orders1'] = 'All orders';
$lang_module['promotion_by_acctount'] = 'Your account is made up of <strong class="text-danger">%s</strong> Promotion code';
$lang_module['promotion_total_price_orders'] = 'Total value of the order is created';
$lang_module['promotion_total_price_orders1'] = 'Includes unpaid orders';
$lang_module['promotion_total_paid_orders'] = 'Total value of guest invoices';
$lang_module['promotion_total_receive_orders'] = 'Total amount of money';
$lang_module['promotion_total_receive_orders1'] = 'For orders to pay';
$lang_module['promotion_total_waiting_orders'] = 'Total amount of waiting for the';
$lang_module['promotion_total_waiting_orders1'] = 'For unpaid orders';
$lang_module['promotion_othernote'] = 'Notes displayed to customers';
$lang_module['promotion_empty'] = 'You still don\'t have any Promotion Code. Click here to create a new one';
$lang_module['promotion_attract'] = 'Attach to the link';
$lang_module['promotion_default'] = 'is default promo code for Affiliate Code';
$lang_module['promotion_tool1'] = 'Tool to attach promo codes to links';
$lang_module['promotion_tool2'] = 'The rules for creating links are as follows';
$lang_module['promotion_tool3'] = 'The tool below helps you get the link with a valid promo code. Please copy the link in the box below and click the create link button';
$lang_module['bidding_order_status0'] = 'Wait for the browse';
$lang_module['bidding_order_status1'] = 'Payment already paid';
$lang_module['bidding_order_status2'] = 'Payment, is holding down';
$lang_module['bidding_order_status3'] = 'Refunded';
$lang_module['bidding_order_status4'] = 'Payment, money received';
$lang_module['bidding_order_status5'] = 'Cancelled';
$lang_module['bidding_order_status6'] = 'Canceled by admin';
$lang_module['stat_sale'] = 'Sale stat';
$lang_module['stat_sale_type'] = 'Introduction type';
$lang_module['stat_update_since'] = '';
$lang_module['stat_update_note'] = 'Data is automatically refreshed every 1 hour. Click here to update again';
$lang_module['sorder_error'] = 'Error';
$lang_module['sorder_name'] = 'Name (On order)';
$lang_module['sorder_code'] = 'Promo Code';
$lang_module['sorder_vip'] = 'VIP Package';
$lang_module['sorder_receive_orders'] = 'Amount received';
$lang_module['sorder_amount'] = 'Amount';
$lang_module['sorder_reg_date'] = 'Regdate';
$lang_module['sorder_status'] = 'Status';
$lang_module['sorder_all'] = 'Total';
$lang_module['sorder_discount'] = 'Discount';
$lang_module['sorder_ovallue'] = 'Order amount';
$lang_module['STT'] = '#';
$lang_module['last_access'] = 'Last access';
$lang_module['total_hits'] = 'Total hits';
$lang_module['link'] = 'Link';
$lang_module['api_affiliate'] = 'Affiliate';
$lang_module['api_affiliate_CheckAffiliateCode'] = 'Kiểm tra mã affiliate (CheckAffiliateCode)';
$lang_module['api_affiliate_GetAffiliateIntroducer'] = 'Lấy người giới thiệu (GetAffiliateIntroducer)';
$lang_module['api_affiliate_CheckIsOfficialCollaborator'] = 'Kiểm tra có phải CTV chính thức không (CheckIsOfficialCollaborator)';
$lang_module['api_promotion'] = 'Mã khuyến mãi';
$lang_module['api_promotion_GetPromotionCode'] = 'Lấy mã khuyến mãi (GetPromotionCode)';
$lang_module['api_promotion_UpdatePromotionCode'] = 'Cập nhật mã khuyến mãi (UpdatePromotionCode)';
$lang_module['api_promotion_UpdatePromotionCodeUse'] = 'Cập nhật thông tin sử dụng mã khuyến mãi (UpdatePromotionCodeUse)';
$lang_module['api_promotion_CreatPromotionCodeUse'] = 'Tạo lượt sử dụng mã khuyến mãi (CreatPromotionCodeUse)';
$lang_module['api_promotion_GetPromotionCodeFromIntroducer'] = 'Lấy mã khuyến mãi mặc định (GetPromotionCodeFromIntroducer)';
$lang_module['api_stat'] = 'Thống kê';
$lang_module['api_stat_UpdateAffiliateStatistics'] = 'Cập nhật thống kê affiliate (UpdateAffiliateStatistics)';
$lang_module['api_stat_UpdatePromotionStatistics'] = 'Cập nhật thống kê mã khuyến mãi (UpdatePromotionStatistics)';
$lang_module['api_stat_SetStatisticsLink'] = 'Ghi thống kê Referral, Target link (SetStatisticsLink)';
$lang_module['apierror_uidcode'] = 'Mã Affiliate không hợp lệ';
$lang_module['apierror_procode'] = 'Mã khuyến mãi không hợp lệ';
$lang_module['apierror_procode_exists'] = 'Mã khuyến mãi không tồn tại';
$lang_module['apierror_site_exists'] = 'Site không tồn tại';
$lang_module['apierror_no_stat'] = 'Không chỉ định thống kê điều gì';
$lang_module['apierror_uidcode1'] = 'Mã Affiliate không tồn tại';
$lang_module['apierror_selfurl_empty'] = 'URL đích không có';
$lang_module['apierror_referer_empty'] = 'URL nguồn không có';
$lang_module['apierror_promouse_exists'] = 'Lượt sử dụng này không tồn tại';
$lang_module['apierror_update_datar'] = 'Chưa chỉ ra dữ liệu cập nhật';
$lang_module['apierror_prouse_count'] = 'Quá số lượt sử dụng được phép';
$lang_module['apierror_u'] = 'Chưa chỉ định thành viên';
$lang_module['main'] = 'Affiliate Marketing, Promotion Code';
$lang_module['config'] = 'Thiết lập';
$lang_module['config_g'] = 'Thiết lập chung';
$lang_module['config_group'] = 'Thiết lập theo nhóm';
$lang_module['config_affiliate_code_groups'] = 'Nhóm được dùng Affiliate Code';
$lang_module['config_promotion_code_length'] = 'Số ký tự tối thiểu Promotion Code';
$lang_module['config_label'] = 'Nội dung';
$lang_module['config_promo_enable'] = 'Kích hoạt Promotion Code';
$lang_module['config_promo_number'] = 'Số Promotion Code được sử dụng';
$lang_module['config_promo_coupon'] = 'Cho phép Coupon';
$lang_module['config_promo_voucher'] = 'Cho phép Voucher';
$lang_module['config_promo_couponmax'] = 'Coupon tối đa (%)';
$lang_module['config_promo_vouchermax'] = 'Voucher tối đa (VNĐ)';
$lang_module['config_promotion_use_limityear'] = 'Số năm thanh toán';
$lang_module['config_promotion_use_limityear_help'] = 'Số năm thanh toán được áp dụng mã giảm giá. Để thành viên chọn khi tạo mã giảm giá ngoài site. Nhập mỗi năm cách nhau bởi dấu phảy';
$lang_module['config_default_notice'] = 'Nội dung thông báo mặc định ở block';
$lang_module['config_group_id_collaborator'] = 'Nhóm cộng tác viên chính thức';
$lang_module['config_discount_freelance'] = 'Chiết khấu tối đa mỗi đơn hàng đối với cộng tác viên tự do (%)';
$lang_module['config_discount_official'] = 'Chiết khấu tối đa mỗi đơn hàng đối với cộng tác viên chính thức (%)';
$lang_module['config_group_id_collaborator_note'] = 'Lưu ý: Các điều hành chung, quản trị tối cao cũng thêm vào nhóm chọn bên trên mới nhận là CTV chính thức';
$lang_module['config_max_freelance_promo_value'] = 'Số tiền giảm giá tối đa của CTV tự do (VNĐ - Dùng trong thống kê)';
$lang_module['config_max_official_promo_value'] = 'Số tiền giảm giá tối đa của CTV chính thức (VNĐ - Dùng trong thống kê)';
$lang_module['affiliate_admin'] = 'Q.Lý người giới thiệu';
$lang_module['affiliate_full'] = 'Quản lý người giới thiệu';
$lang_module['affiliate_add'] = 'Thêm';
$lang_module['sync'] = 'Đồng bộ dữ liệu';
$lang_module['sync_g1'] = 'Chức năng này sẽ';
$lang_module['sync_g2'] = 'Cập nhật lại dữ liệu thành viên được giới thiệu qua Affiliate Code hoặc Pronotion Code';
$lang_module['sync_g3'] = 'Xóa các mã giảm giá bị mất thành viên tạo';
$lang_module['sync_g4'] = 'Xóa các lượt sử dụng mã giảm giá đã bị xóa mã giảm giá';
$lang_module['sync_res_g1'] = 'Thực hiện thành công, dưới đây là thông tin';
$lang_module['sync_res_g2'] = 'Xóa thành viên giới thiệu';
$lang_module['sync_res_g3'] = 'Xóa thành viên được giới thiệu';
$lang_module['sync_res_g4'] = 'Xóa mã giảm giá bị mất thành viên';
$lang_module['sync_res_g5'] = 'Xóa thông tin sử dụng của mã giảm giá không tồn tại';
