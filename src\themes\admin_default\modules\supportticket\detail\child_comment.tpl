<div id="child_comment">
    <!-- BEGIN: loop_child -->
        <div class="panel-body {CHILD_COMMENT.display_id}">
            <div class="row">
                <div class="col-lg-21">
                    <div class="text-left"><strong>{CHILD_COMMENT.customer_question}</strong> <span class="time">{CHILD_COMMENT.comment_time}</span></div>
                </div>
                <div class="col-lg-3">
                    <span style="white-space: nowrap;">{CHILD_COMMENT.response_point}</span>
                </div>
            </div>
            <div class="row">
                <div class="col-md-24">
                    <div class="comment-content bodytext">
                        <div class="content">{CHILD_COMMENT.content}</div>
                        {CHILD_COMMENT.customer_review}
                        {FILE "detail/detail_attach.tpl"}
                    </div>
                </div>
            </div>
        </div>
        <!-- BEGIN: grandchild -->
            <!-- BEGIN: loop -->
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-21">
                            <div class="text-left">
                                <strong>{GRANDCHILD_COMMENT.reply_from}</strong>
                                <span class="time">{GRANDCHILD_COMMENT.comment_time}</span>
                                <p class="text-danger">{GRANDCHILD_COMMENT.comment_invalid}</p>
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <span style="white-space: nowrap;">{GRANDCHILD_COMMENT.response_point}</span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-24">
                            <div class="comment-content bodytext">
                                <div class="content" id="{GRANDCHILD_COMMENT.display_id}">{GRANDCHILD_COMMENT.content}</div>
                                {GRANDCHILD_COMMENT.customer_review}
                            </div>
                        </div>
                    </div>
                </div>
                <!-- BEGIN: great_grandchild-->
                <!-- BEGIN: loop -->
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-24">
                            <div class="text-left"><strong>{GREAT_GRANDCHILD_COMMENT.reply_from}</strong> <span class="time">{GREAT_GRANDCHILD_COMMENT.comment_time}</span></div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-24">
                            <div class="comment-content bodytext">
                                <div class="content" id="{GRANDCHILD_COMMENT.display_id}">{GREAT_GRANDCHILD_COMMENT.content}</div>
                                {GREAT_GRANDCHILD_COMMENT.customer_review}
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END: loop -->
                <!-- END:great_grandchild -->
            <!-- END: loop -->
        <!-- END: grandchild -->
    <!-- END: loop_child -->
    <!-- BEGIN: expert_reply_comment -->
    <div class="col-md-24 expert_reply_comment">
        <button type="button" class="btn btn-primary expert-comment" data-comment-id="{QUESTION_EXPERT_ID}" data-comment-type="{COMMENT_PARENT_TYPE}">
            <i class="fa fa-pencil" aria-hidden="true"></i> {LANG.send_reply_comment}
        </button>
    </div>
    <!-- END: expert_reply_comment -->
    <!-- BEGIN: expert_grandchild_reply_comment -->
    <div class="col-md-24 expert_additional">
        <button type="button" class="btn btn-primary expert-additional-comment" data-comment-id="{QUESTION_EXPERT_ID}" data-comment-type="{COMMENT_PARENT_TYPE}">
            <i class="fa fa-pencil" aria-hidden="true"></i> {LANG.send_additional_comment}
        </button>
    </div>
    <!-- END: expert_grandchild_reply_comment -->
</div>
<script type="text/javascript">
    $(document).ready(function() {
        $("[data-fancybox]").fancybox({});
        // Xử lý hiển thị form comment
        $('.expert-comment, .expert-additional-comment').on('click', function () {
            var commentId = $(this).data('comment-id');
            var commentType = $(this).data('comment-type');
            var commentFormToken = $('#detail_token').val();
            var actionUrl = $('#detail_paid_form_action').attr('action');
            var parentDiv = $(this).closest('#child_comment');
            var targetClass = $(this).hasClass('expert-comment') ? '.expert_reply_comment' : '.expert_additional';

            $.ajax({
                url: actionUrl,
                method: 'POST',
                data: { comment_parent_id: commentId, render_form_expert_comment: commentFormToken, comment_parent_type: commentType},
                success: function(response) {
                    if (response.success) {
                        parentDiv.find(targetClass).html(response.data.html);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Form submission failed:', error);
                }
            });
        });
    });
</script>
