<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Apr 20, 2010 10:47:41 AM
 */

if (!defined('NV_IS_MOD_ELINK')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('affiliate_allusers');
$description = $keywords = 'no';

if (!defined('NV_IS_USER')) {
    $link_redirect = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    nv_redirect_location($link_redirect);
}

$array = [];
$array['affiliate_enabled'] = nv_user_in_groups($global_array_config['affiliate_code_groups']);

if (!$array['affiliate_enabled']) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}

$array_mod_title[] = array(
    'catid' => 0,
    'title' => $nv_Lang->getModule('affiliate_manager'),
    'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=affiliate'
);
$array_mod_title[] = array(
    'catid' => 0,
    'title' => $page_title,
    'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op
);

$array['affiliate_code'] = $user_info['username'];

$page = 1;
$per_page = 20;
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
if (isset($array_op[1])) {
    if (preg_match('/^page\-([0-9]+)$/i', $array_op[1], $m)) {
        $page = intval($m[1]);
    } else {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    }
    if ($page > 99999999 or $page < 1) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    }
}
if (isset($array_op[2])) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
}

$db->sqlreset()->from($db_config['prefix'] . "_" . $module_data . "_affiliate_set tb1, " . NV_USERS_GLOBALTABLE . " tb2");
$db->where("tb1.pre_uid=tb2.userid AND tb1.pri_uid=" . $user_info['userid']);
$db->select('COUNT(tb2.userid)');

$num_items = $db->query($db->sql())->fetchColumn();

$db->order('tb2.regdate DESC')->limit($per_page)->offset(($page - 1) * $per_page);
$db->select('tb2.userid, tb2.email, tb2.first_name, tb2.last_name, tb2.regdate');

$result = $db->query($db->sql());

$array['data'] = [];
$array_userids = [];
while ($row = $result->fetch()) {
    $array['data'][$row['userid']] = $row;
    $array_userids[$row['userid']] = $row['userid'];
}

// Lấy số điện thoại các thành viên trên
$array_user_phone = [];
$array_user_vip = [];
if (!empty($array_userids)) {
    $sql = "SELECT userid, phone FROM " . NV_USERS_GLOBALTABLE . "_info WHERE userid IN(" . implode(',', $array_userids) . ")";
    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        $array_user_phone[$row['userid']] = $row['phone'];
    }

    // Lấy các gói VIP
    /*
    $sql = "SELECT user_id, vip, from_time, end_time, status FROM " . NV_BIDDING_TABLE . "_customs WHERE user_id IN(" . implode(',', $array_userids) . ") AND (status=1 OR status=2)";
    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        if (!isset($array_user_vip[$row['user_id']][$row['vip']]) or $array_user_vip[$row['user_id']][$row['vip']]['status'] > $row['status']) {
            $array_user_vip[$row['user_id']][$row['vip']] = $row;
        }
    }
    */
}

if ($page > 1 and empty($array['data'])) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
}
if ($page > 1) {
    $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . number_format($page, 0, ',', '.');
}

$generate_page = nv_alias_page($page_title, $base_url, $num_items, $per_page, $page);
$contents = nv_elink_theme_affiliate_users($array, $generate_page, $num_items, $array_user_phone, $array_user_vip);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
