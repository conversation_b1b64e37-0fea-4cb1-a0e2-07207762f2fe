<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */
namespace NukeViet\Api;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    exit('Stop!!!');
}

/**
 *
 * @package NukeViet\Api
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2010-2021 VINADES.,JSC. All rights reserved
 * @version 4.5.00
 * @access public
 */
class ErrorLog implements IApi
{

    private $result;

    /**
     * getAdminLev()
     *
     * @return int
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     * getCat()
     *
     * @return string
     */
    public static function getCat()
    {
        return 'webtools';
    }

    /**
     * setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     * execute()
     *
     * @return mixed
     */
    public function execute()
    {
        global $nv_Request;

        $server_name = $nv_Request->get_string('server_name', 'post');
        $file_name = $nv_Request->get_string('file_name', 'post');
        $file_content = $nv_Request->get_string('file_content', 'post', false, false, false);
        $file_content = gzuncompress($file_content);
        if (!empty($file_content) and file_put_contents(NV_ROOTDIR . '/data/errorlog/' . $server_name . '_' . $file_name, $file_content, FILE_APPEND)) {
            $this->result->setSuccess();
        } else {
            $this->result->setError()
                ->setCode('1000')
                ->setMessage('Error Write File');
        }

        return $this->result->getResult();
    }
}
