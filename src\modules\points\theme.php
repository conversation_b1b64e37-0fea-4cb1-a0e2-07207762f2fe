<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 16 Jan 2018 07:25:26 GMT
 */
if (!defined('NV_IS_MOD_POINTS'))
    die('Stop!!!');

/**
 * nv_theme_info()
 *
 * @param mixed $text
 * @param string $type
 * @param string $url
 * @param integer $time
 * @return
 */
function nv_theme_info($text, $type = 'info', $url = '', $time = 2)
{
    global $global_config, $module_name, $module_config, $module_info, $nv_Lang;

    $xtpl = new XTemplate("info.tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('TYPE', $type);

    if (!empty($text)) {
        $xtpl->assign('TEXT', $text);

        if (!empty($url)) {
            $xtpl->assign('URL', $url);
            $xtpl->assign('TIME', $time);
            $xtpl->parse('main.text.url');
        }
        $xtpl->parse('main.text');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}


function nv_theme_point($customs_points, $user_money, $config_point, $arrray_log, $generate_page, $price_list, $error_message, $arrray_point_expired, $generate_page_ex, $tab_active, $array_search, $_error)
{
    global $module_name, $module_file, $module_info, $op, $user_info, $client_info, $module_config, $nv_Lang, $page1;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('OP', $op);
    $xtpl->assign('CHECKSESS', md5($user_info['userid'] . NV_CACHE_PREFIX . $client_info['session_id']));
    $xtpl->assign('USER', $user_info);
    if (isset($user_money['money_current'])) {
        $user_money['money_total'] = nv_number_format((float)$user_money['money_current']);
    }
    $xtpl->assign('MONEY', $user_money);
    $xtpl->assign('POINTS_CONFIG', $module_config[$module_name]);
    $xtpl->assign('LINK', nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op, true));
    $xtpl->assign('HASH', [
        'muadiem' => NV_LANG_DATA == 'vi' ? 'muadiem' : 'buy-point',
        'tangdiem' => NV_LANG_DATA == 'vi' ? 'tangdiem' : 'reward-point',
        'thuongdiem' => NV_LANG_DATA == 'vi' ? 'thuongdiem' : 'bonus-point',
        'banggia' => NV_LANG_DATA == 'vi' ? 'banggia' : 'points-exchange'
    ]);
    $xtpl->assign('ARRAY_SEARCH', $array_search);
    if ($_error != '') {
        $xtpl->assign('ERROR', $_error);
        $xtpl->parse('main.error');
    }

    $customs_points['point_total_format'] = nv_number_format(floor($customs_points['point_total']));
    $xtpl->assign('POINT', $customs_points);
    $xtpl->assign('LIMIT_POINTS', $module_config['points']['limit_give_points']);
    $xtpl->assign('RATIO_POINTS', $module_config['points']['ratio_give_points']);
    $xtpl->assign('PAGE1', $page1);
    $tab_1 = $tab_2 = $tab_content_1 = $tab_content_2 = '';
    if ($tab_active == 2) {
        $tab_2 = 'active';
        $tab_content_2 = 'in active';
    } else {
        $tab_1 = 'active';
        $tab_content_1 = 'in active';
    }

    $xtpl->assign('TAB_1', $tab_1);
    $xtpl->assign('TAB_2', $tab_2);
    $xtpl->assign('TAB_CONTENT_1', $tab_content_1);
    $xtpl->assign('TAB_CONTENT_2', $tab_content_2);

    if (!empty($module_config['points']['use_give_points'])) {
        $xtpl->parse('main.give_points');
    }
    foreach ($config_point as $key => $value) {
        $xtpl->assign('VALUE', $key);
        $xtpl->assign('EXCHANGE', number_format($value) . ' VND = ' . number_format($key) . ' ' . $nv_Lang->getModule('points'));
        $xtpl->parse('main.exchange');
    }

    foreach ($arrray_log as $value) {
        $value['created_time'] = nv_date('H:i:s d/m/Y', $value['created_time']);
        $value['point_total'] = (($value['status'] == 1) ? '+ ' : '- ') . nv_number_format($value['point_total']);
        $xtpl->assign('LOG', $value);
        $xtpl->parse('main.log');
    }
    foreach ($arrray_point_expired as $value) {
        $value['expired_time'] = nv_date('H:i:s d/m/Y', $value['expired']);
        $value['point_total'] = (($value['status'] == 1) ? '+ ' : '- ') . nv_number_format($value['point_total']);
        $xtpl->assign('EXPIRED', $value);
        $xtpl->parse('main.point_expired');
    }

    if ($generate_page) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
    }

    if ($generate_page_ex) {
        $xtpl->assign('NV_GENERATE_PAGE_EX', $generate_page_ex);
        $xtpl->parse('main.generate_page_ex');
    }

    // Bảng giá site DauThau.info
    if (!empty($error_message[1])) {
        $xtpl->assign('PTABLE1_ERROR', $error_message[1]);
        $xtpl->parse('main.ptable1_error');
    } else {
        $grouped_list1 = [];
        foreach ($price_list[1] as $price_item) {
            $group = isset($price_item['group']) ? $price_item['group'] : '';
            $title = $price_item['title'];
            $link = $price_item['link'];
            $values = is_array($price_item['value']) ? $price_item['value'] : ['' => $price_item['value']];

            if (!isset($grouped_list1[$group])) {
                $grouped_list1[$group] = [];
            }

            if (!isset($grouped_list1[$group][$title])) {
                $grouped_list1[$group][$title] = [];
            }

            foreach ($values as $key => $value) {
                $grouped_list1[$group][$title][] = [
                    'link' => $link,
                    'detail' => $key,
                    'value' => $value
                ];
            }
        }
        foreach ($grouped_list1 as $key => $grouped_list) {
            $xtpl->assign('GROUP', $key);
            foreach ($grouped_list as $title => $items) {
                $rowspan = count($items);
                $is_first = true;

                foreach ($items as $item) {
                    $xtpl->assign('ITEM_DETAIL', $item['detail']);
                    $xtpl->assign('ITEM_VALUE', $item['value']);

                    if ($rowspan > 1 && $is_first) {
                        $xtpl->assign('ROWSPAN', $rowspan);
                        $xtpl->assign('LINK_BANGGIA', $item['link']);
                        $xtpl->assign('TITLE', $title);
                        $xtpl->parse('main.ptable1.group.loop_row.rowspan_item');
                        $is_first = false;
                    } elseif ($rowspan > 1) {
                        $xtpl->parse('main.ptable1.group.loop_row.no_rowspan_item');
                    } else {
                        $xtpl->assign('LINK_BANGGIA', $item['link']);
                        $xtpl->assign('TITLE', $title);
                        $xtpl->parse('main.ptable1.group.loop_row.single_item');
                    }

                    $xtpl->parse('main.ptable1.group.loop_row');
                }
            }
            $xtpl->parse('main.ptable1.group');

        }

        $xtpl->parse('main.ptable1');
    }

    // Bảng giá site DauThau.Net
    if (!empty($error_message[2])) {
        $xtpl->assign('PTABLE2_ERROR', $error_message[2]);
        $xtpl->parse('main.ptable2_error');
    } else {
        foreach ($price_list[2] as $price_item) {
            $xtpl->assign('ITEM', $price_item);
            $xtpl->parse('main.ptable2.loop');
        }
        $xtpl->parse('main.ptable2');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_ajax_expired_point($arrray_point_expired, $generate_page_ex, $tab_active, $page1, $per_page)
{
    global $module_name, $module_file, $module_info, $op;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('OP', $op);
    $xtpl->assign('PAGE1', $page1);
    $tab_1 = $tab_2 = $tab_content_1 = $tab_content_2 = '';
    if ($tab_active == 2) {
        $tab_2 = 'active';
        $tab_content_2 = 'in active';
    } else {
        $tab_1 = 'active';
        $tab_content_1 = 'in active';
    }

    $xtpl->assign('TAB_1', $tab_1);
    $xtpl->assign('TAB_2', $tab_2);
    $xtpl->assign('TAB_CONTENT_1', $tab_content_1);
    $xtpl->assign('TAB_CONTENT_2', $tab_content_2);

    foreach ($arrray_point_expired as $value) {
        $value['expired_time'] = nv_date('H:i:s d/m/Y', $value['expired']);
        $value['point_total'] = (($value['status'] == 1) ? '+ ' : '- ') . number_format($value['point_total']);
        $xtpl->assign('EXPIRED', $value);
        $xtpl->parse('ajax.point_expired');
    }

    if ($generate_page_ex) {
        $xtpl->assign('NV_GENERATE_PAGE_EX', $generate_page_ex);
        $xtpl->parse('ajax.generate_page_ex');
    }

    $xtpl->parse('ajax');
    return $xtpl->text('ajax');
}
