<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GroupsUsersSSO implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'UsersSSO';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db;

        $sql = 'SELECT * FROM ' . NV_USERS_GLOBALTABLE . '_groups';
        $_query = $db->query($sql);
        $group_lists = [];
        while ($_row = $_query->fetch()) {
            $group_lists[$_row['group_id']] = $_row;
            $group_lists[$_row['group_id']]['groups_detail'] = $db->query("SELECT * FROM " . NV_USERS_GLOBALTABLE . "_groups_detail WHERE group_id = " . $_row['group_id'])->fetchAll();
            $group_lists[$_row['group_id']]['groups_users'] = $db->query("SELECT * FROM " . NV_USERS_GLOBALTABLE . "_groups_users WHERE group_id = " . $_row['group_id'])->fetchAll();
        }

        if (!empty($group_lists)) {
            $this->result->set('data', $group_lists);
            $this->result->setSuccess();
        } else {
            $this->result->setError();
            $this->result->setMessage('No data');
        }
        return $this->result->getResult();
    }
}