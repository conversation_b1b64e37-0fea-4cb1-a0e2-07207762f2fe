<!-- BEGIN: table_orders -->
<div class="alert alert-info">
    <strong><PERSON><PERSON> chọn nhiều đơn hàng (gộp chung đơn hàng vào hợp đồng):</strong>
    <ul style="padding-left: 20px;">
        <li><PERSON><PERSON><PERSON> bạn đang <strong><PERSON><PERSON><PERSON> hợp đồng mới</strong>, thì các đơn hàng (kể cả đã có hợp đồng) sẽ được gộp chung vào hợp đồng mới.</li>
        <li>Nếu bạn đang <strong>Sửa hợp đồng</strong>, th<PERSON> các đơ<PERSON> hàng (kể cả đã có hợp đồng) sẽ được gộp chung vào <strong>hợp đồng đang sửa</strong> hiện tại.</li>
    </ul>
</div>
<div class="table-responsive">
    <table class="table table-bordered mb-0">
        <thead>
            <tr>
                <th></th>
                <th class="text-center">M<PERSON> đơn hàng</th>
                <th>G<PERSON><PERSON> VIP</th>
                <th>Thông tin chi phí</th>
                <th class="text-center">Số hợp đồng</th>
            </tr>
        </thead>
        <tbody>
            <!-- BEGIN: loop_item -->
            <tr data-order-id="{ORDER.id}">
                <td class="text-center">
                    <input type="checkbox" value="{ORDER.id}" name="checked_order_ids[]" class="form-check" style="margin-right: 0;">
                </td>
                <td class="text-center"><strong>{ORDER.code}</strong></td>
                <td>{ORDER.vips_title}</td>
                <td>
                    <ul style="padding-left: 20px;">
                        <li>{LANG.total_price}: <strong>{ORDER.money}</strong></li>
                        <li>{LANG.duoc_giam}: <strong>{ORDER.discount}</strong></li>
                        <li>{LANG.total_payment}: <strong>{ORDER.total}</strong></li>
                        <li>{LANG.chiet_khau_rieng}: <strong>{ORDER.price_reduce}</strong></li>
                        <li>{LANG.total_real_receive}: <strong>{ORDER.total_end}</strong></li>
                    </ul>
                </td>
                <td class="text-center">{ORDER.contract_no}</td>
            </tr>
            <!-- END: loop_item -->
        </tbody>
    </table>
</div>
<!-- END: table_orders -->

<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<link type="text/css" href="{NV_BASE_SITEURL}themes/{TEMPLATE}/css/econtract.css" rel="stylesheet">

<!-- BEGIN: error -->
<div class="col-md-24">
    <div class="alert alert-danger">{ERROR}</div>
</div>
<!-- END: error -->
<form method="post" action="{ACTION_FORM_URL}" enctype="multipart/form-data">
    <div class="row" id="view-econtract-detail">
        <div class="col-md-17">
            <!-- BEGIN: show_action_buttons -->
            <div class="row">
                <div class="col-md-24">
                    <div class="top-action-buttons">
                        <a href="{BACK_URL}" class="btn btn-sm btn-default"><em class="fa fa-backward"></em> <span> Quay lại danh sách</span></a>
                        <a href="{DETAIL_URL}" class="btn btn-sm btn-success"><em class="fa fa-file-pdf-o"></em> <span>Xem chi tiết</span></a>
                        <a href="javascript:;" id="btn_sign_content" class="btn btn-sm btn-primary"><em class="fa fa-paper-plane"></em> Trình lãnh đạo ký</a>
                        <a href="{UPLOAD_URL}" class="btn btn-sm btn-danger"><em class="fa fa-cloud-upload"></em> Tải lên hợp đồng đã ký</a>
                        <a href="{DOWNLOAD_URL}" class="btn btn-sm btn-warning"><em class="fa fa-cloud-download"></em> Tải phiên bản đang xem</a>
                        <a href="#" id="btn-preview" class="btn btn-sm btn-default pull-right"><em class="fa fa-eye"></em> {LANG.preview}</a>
                    </div>
                </div>
            </div>
            <!-- END: show_action_buttons -->
            <div class="section mb-4">
                <h2 class="section-title">Thông tin Bên A trên hợp đồng</h2>
                <div class="form-check">
                    <input type="checkbox" name="customer_type" value="0" class="form-check-input" id="isIndividual" {ECONTRACT.customer_type_checked}>
                    <label class="form-check-label" for="isIndividual">Khách hàng cá nhân</label>
                </div>

                <div class="row econtract-form-info two-cols mt-5" id="thongtinbenA">
                    <div class="col-md-12" id="field-customer-name">
                        <div class="row">
                            <label class="control-label col-md-7">Tên công ty: <span class="required">*</span></label>
                            <div class="col-md-17">
                                <input type="text" name="c_name" class="form-control required-0 required-1" value="{ECONTRACT.c_name}" placeholder="Tên công ty">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12" id="field-tax-code">
                        <div class="row">
                            <label class="control-label col-md-7">MST: <span class="required">*</span></label>
                            <div class="col-md-17">
                                <input type="text" name="tax_code" class="form-control required-1" value="{ECONTRACT.tax_code}" placeholder="MST">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12" id="field-cccd">
                        <div class="row">
                            <label class="control-label col-md-7">CCCD: <span class="required">*</span></label>
                            <div class="col-md-17">
                                <input type="text" name="cccd" class="form-control required-0" value="{ECONTRACT.cccd}" placeholder="CCCD">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12" id="field-representative">
                        <div class="row">
                            <label class="control-label col-md-7">Người đại diện: <span class="required">*</span></label>
                            <div class="col-md-17">
                                <input type="text" name="representative" class="form-control required-1" value="{ECONTRACT.representative}" placeholder="Người đại diện">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12" id="field-jobtitle">
                        <div class="row">
                            <label class="control-label col-md-7">Chức vụ: <span class="required">*</span></label>
                            <div class="col-md-17">
                                <input type="text" name="jobtitle" class="form-control required-1" value="{ECONTRACT.jobtitle}" placeholder="Chức vụ">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="row">
                            <label class="control-label col-md-7">Số điện thoại: <span class="required">*</span></label>
                            <div class="col-md-17">
                                <input type="text" name="phone" class="form-control required-0 required-1" value="{ECONTRACT.phone}" placeholder="Số điện thoại">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="row">
                            <label class="control-label col-md-7">Email: <span class="required">*</span></label>
                            <div class="col-md-17">
                                <input type="text" name="email" class="form-control required-0 required-1" value="{ECONTRACT.email}" placeholder="Email">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-24">
                        <div class="row">
                            <label class="control-label col-md-3">Địa chỉ: <span class="required">*</span></label>
                            <div class="col-md-21" style="padding-left: 17px;">
                                <input type="text" name="c_address" class="form-control required-0 required-1" value="{ECONTRACT.c_address}" placeholder="Địa chỉ">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-24">
                        <div class="row">
                            <label class="control-label col-md-3">Tài khoản số:</label>
                            <div class="col-md-21" style="padding-left: 17px;">
                                <input type="text" name="bank_account" class="form-control required-0 required-1" value="{ECONTRACT.bank_account}" placeholder="Tài khoản ngân hàng">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-24 form-check">
                        <div class="row">
                            <label class="control-label col-md-3">
                                <input type="checkbox" class="form-check-input" id="checkUyQuyen" {ECONTRACT.authorization_letter_checked}>
                                Theo giấy uỷ quyền
                            </label>
                            <div id="box-uy-quyen" class="col-md-21" style="padding-left: 17px;">
                                <input type="text" class="form-control" name="authorization_letter" value="{ECONTRACT.authorization_letter}" placeholder="Ví dụ: Theo giấy uỷ quyền số 123/UQ/ABC">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Thông tin người nhận -->
            <div class="section mb-4">
                <h2 class="section-title">
                    Thông tin người nhận hợp đồng
                    <div class="help-text">Hãy điền thông tin người nhận hợp đồng nếu khách yêu cầu bản cứng</div>
                </h2>
                <div class="row econtract-form-info-2">
                    <div class="col-md-12">
                        <div class="row">
                            <label class="control-label col-md-10">Họ tên người nhận</label>
                            <div class="col-md-14">
                                <input type="text" name="receiver" class="form-control" value="{ECONTRACT.receiver}" placeholder="Họ tên người nhận">
                            </div>
                        </div>
                        <div class="row">
                            <label class="control-label col-md-10">SĐT người nhận</label>
                            <div class="col-md-14">
                                <input type="text" name="receiver_phone" class="form-control" value="{ECONTRACT.receiver_phone}" placeholder="SĐT người nhận">
                            </div>
                        </div>
                        <div class="row">
                            <label class="control-label col-md-10">Địa chỉ người nhận</label>
                            <div class="col-md-14">
                                <input type="text" name="receiver_address" class="form-control" value="{ECONTRACT.receiver_address}" placeholder="Địa chỉ người nhận">
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="section mb-4">
                <h2 class="section-title">Thông tin danh sách các sản phẩm, dịch vụ</h2>
                <!-- BEGIN: no_order_inlist -->
                <div class="alert alert-info" id="alert-no-data-vips">Chưa có dữ liệu</div>
                <!-- END: no_order_inlist -->
                <!-- BEGIN: order_inlist -->
                <div id="box-info-vips" style="display: none;">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Gói dịch vụ</th>
                                <th>Giá trên hợp đồng</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- BEGIN: loop_item -->
                            <tr>
                                <td>{ORDER_ITEM.title}</td>
                                <td>{ORDER_ITEM.vip_price_format}</td>
                            </tr>
                            <!-- END: loop_item -->
                        </tbody>
                    </table>
                    <div class="sum-total-orders">
                        <div class="row">
                            <div class="col-md-10 text-right"><strong>Tổng dịch vụ</strong></div>
                            <div class="col-md-14" id="price-info-total">{TOTAL}</div>
                        </div>
                        <div class="row">
                            <div class="col-md-10 text-right"><strong>Được giảm</strong></div>
                            <div class="col-md-14" id="price-info-discount">{DISCOUNT}</div>
                        </div>
                        <div class="row">
                            <div class="col-md-10 text-right"><strong>Tổng thanh toán</strong></div>
                            <div class="col-md-14"><strong id="price-info-total-payment">{TOTAL_END}</strong></div>
                        </div>
                    </div>
                </div>
                <!-- END: order_inlist -->
            </div>
            <!-- BEGIN: show_box_term_changed -->
            <div class="section">
                <h2 class="section-title">Hợp đồng đã thay đổi điều khoản</h2>
                <div class="form-check">
                    <input class="form-check-input" name="term_changed" value="1" type="checkbox" id="contractTermChanged" {ECONTRACT.term_changed_checked}>
                    <label class="form-check-label" for="contractTermChanged">
                        Hợp đồng đã thay đổi điều khoản
                    </label>
                </div>
                <div id="box-term-changed-upload">
                    <div id="term-changed-drop-area">
                        <div class="upload-box ml-2" id="drop-zone">
                            <div class="upload-placeholder">
                                <i class="fa fa-cloud-upload"></i>
                                <p>Kéo và Thả File của bạn vào đây</p>
                                <p>Hoặc</p>
                                <button class="btn btn-info" type="button" id="select-file-button">Chọn File</button>
                            </div>
                        </div>
                        <input type="file" class="d-none hidden" id="contract-file" name="contract_path">
                        <div id="file-list"></div>
                    </div>

                    <div id="file-list"></div>

                    <div class="form-group">
                        <label for="noteContractTermChanged"><strong>Ghi chú thay đổi nội dung hợp đồng</strong></label>
                        <textarea name="term_changed_notes" class="form-control" id="noteContractTermChanged" rows="3" placeholder="Nhập nội dung">{ECONTRACT.term_changed_notes}</textarea>
                    </div>
                </div>
            </div>
            <!-- END: show_box_term_changed -->
            <div class="bottom-action-buttons">
                <input type="hidden" name="save" value="1">
                <button type="submit" class="btn btn-info"><em class="fa fa-save"></em> {SUBMIT_TEXT}</button>
            </div>
        </div>
        <div class="col-md-7">
            <div class="right-sidebar">
                <!-- BEGIN: show_econtract_code -->
                <div class="box-info-item">
                    <h3 class="sidebar-title">
                        Số hợp đồng - Phiên bản
                        <!-- BEGIN: show_action_choose_version -->
                        <a href="#" class="btn btn-xs btn-default pull-right"><em class="fa fa-check"></em> Chọn làm phiên bản chính thức</a>
                        <!-- END: show_action_choose_version -->
                    </h3>
                    <div class="box-content">
                        {ECONTRACT.contract_no} - {ECONTRACT.version_data.code}
                        <!-- BEGIN: show_label_current_version -->
                        <span class="label label-success label-current-version">Ph.bản chính thức</span>
                        <!-- END: show_label_current_version -->
                    </div>
                    <div class="box-content text-muted">
                        {ECONTRACT.type_econtract}
                    </div>
                </div>
                <!-- END: show_econtract_code -->
                <div class="box-info-item">
                    <h3 class="sidebar-title">Sale phụ trách</h3>
                    <div class="box-content" id="sale-name">{ECONTRACT.sale_name}</div>
                </div>
                <div class="box-info-item">
                    <h3 class="sidebar-title">Thông tin khách hàng
                        <button type="button" class="btn btn-xs btn-default pull-right" data-toggle="seluser" data-area="customer_id">
                            <i class="fa fa-user" aria-hidden="true"></i> Chọn khách hàng
                        </button>
                    </h3>
                    <div class="box-content">
                        <input type="hidden" value="{ECONTRACT.customer_id}" id="old_customer_id">
                        <input type="hidden" value="{ECONTRACT.customer_id}" name="customer_id" id="customer_id">
                        <ul id="show-cus-info">
                            <li><strong>Tài khoản:</strong> <span id="cus-username">{ECONTRACT.username}</span></li>
                            <li><strong>Họ tên:</strong> <span id="cus-fullname">{ECONTRACT.fullname}</span></li>
                        </ul>
                    </div>
                </div>
                <div class="box-info-item">
                    <h3 class="sidebar-title">Danh sách đơn hàng <a href="javascript:;" onclick="changeCustomerId(true);" class="btn btn-xs btn-default pull-right"><i class="fa fa-shopping-cart" aria-hidden="true"></i> Chọn đơn hàng</a></h3>
                    <div class="box-content">
                        <ul class="list-order-label" id="choosedOrders">
                            <!-- BEGIN: loop_order -->
                            <li class="label-order-title btn-group">
                                <a href="{ORDER.url_detail}" class="btn btn-xs btn-success" target="_blank">
                                    {ORDER.code} <i class="fa fa-external-link" aria-hidden="true"></i>
                                    <input type="hidden" name="order_ids[]" value="{ORDER.id}">
                                </a>
                                <a href="javascript:;" onclick="removeOrder(this);return false;" data-order-id="{ORDER.id}" class="btn btn-xs btn-danger"><i class="fa fa-times" aria-hidden="true"></i></a>
                            </li>
                            <!-- END: loop_order -->
                        </ul>
                    </div>
                </div>
                <!-- BEGIN: sidebar -->
                <div class="box-info-item">
                    <h3 class="sidebar-title">Trạng thái hợp đồng</h3>
                    <div class="box-content">
                        {ECONTRACT.status_label}
                        <!-- BEGIN: show_label_term_changed -->
                        <div class="text-warning"><em class="fa fa-info-circle"></em> Điều khoản hợp đồng đã thay đổi</div>
                        <!-- END: show_label_term_changed -->
                    </div>
                </div>
                <div class="box-info-item">
                    <h3 class="sidebar-title">Giai đoạn hiện tại</h3>
                    <div class="box-content">
                        {ECONTRACT.stage_label}
                    </div>
                </div>
                <div class="box-info-item">
                    <h3 class="sidebar-title">Giai đoạn tiếp theo</h3>
                    <div class="box-content">
                        {ECONTRACT.stage_next_label}
                    </div>
                </div>
                <div class="box-info-item">
                    <h3 class="sidebar-title">Thời gian cập nhật</h3>
                    <div class="box-content">{ECONTRACT.updated_at}</div>
                </div>
                <!-- BEGIN: show_version -->
                <div class="box-info-item">
                    <h3 class="sidebar-title">Phiên bản</h3>
                    <div class="box-content">
                        <ul class="list-versions">
                            <!-- BEGIN: loop_version -->
                            <li class="version-item {VERSION.is_current_class} {VERSION.is_viewing_class}">
                                <a href="{VERSION.link}">
                                    <h4><em class="fa fa-file-pdf-o"></em> {VERSION.code} {VERSION.is_current_label} {VERSION.is_viewing_label}</h4>
                                    <p class="text-muted"><em><em class="fa fa-clock-o"></em> {VERSION.created_at}</em> <span class="label label-info">{VERSION.status_label}</span></p>
                                    <p class="text-muted">
                                        <a href="{VERSION.link_file}" class="text-muted">
                                            <em class="fa fa-paperclip"></em> Hợp đồng đính kèm
                                        </a>
                                    </p>
                                </a>
                            </li>
                            <!-- END: loop_version -->
                        </ul>
                    </div>
                </div>
                <!-- END: show_version -->
                <!-- BEGIN: show_logs -->
                <div class="box-info-item">
                    <h3 class="sidebar-title">Nhật ký hệ thống</h3>
                    <div class="box-content">
                        <ul class="list-logs">
                            <!-- BEGIN: loop_log -->
                            <li class="log-item">
                                <div class="text-muted"><strong>{LOG.fullname}</strong> - <em>{LOG.created_at}</em></div>
                                <div class="log-title">
                                    {LOG.action_desc}
                                    <!-- BEGIN: detail_log_label -->
                                    &nbsp; <a data-toggle="collapse" href="#logOther{LOG.id}" aria-expanded="false" aria-controls="logOther{LOG.id}"><i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
                                    <!-- END: detail_log_label -->
                                </div>
                                <!-- BEGIN: detail_log -->
                                <div class="collapse" id="logOther{LOG.id}">
                                    <ul class="logotherlists">
                                        <!-- BEGIN: loop -->
                                        {CHANGED_KEY}
                                        <ul>
                                            <li>{LANG.old}: {CHANGED_DATA.old}</li>
                                            <li>{LANG.new}: {CHANGED_DATA.new}</li>
                                        </ul>
                                        <!-- END: loop -->
                                    </ul>
                                </div>
                                <!-- END: detail_log -->
                            </li>
                            <!-- END: loop_log -->
                        </ul>
                    </div>
                </div>
                <!-- END: show_logs -->
                <!-- END: sidebar -->
            </div>
        </div>
    </div>
</form>

<div class="modal fade" id="modalChooseOrders" tabindex="-1" role="dialog" aria-labelledby="modalChooseOrdersLabel" data-backdrop="static">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h3 class="modal-title"><em class="fa fa-shopping-cart"></em> <strong>CHỌN ĐƠN HÀNG</strong></h3>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">Vui lòng chọn khách hàng trước</div>
            </div>
            <div class="modal-footer" style="text-align: center;">
                <button type="button" class="btn btn-success disabled" onclick="completedChoooseOrders();return false;"><em class="fa fa-check"></em> Chọn xong đơn hàng</button>
            </div>
        </div>
    </div>
</div>
<!-- BEGIN: show_script_term_changed -->
<script type="text/javascript">
    var selectedFiles = [];
    const MAX_FILE_SIZE = {MAX_FILE_ECONTRACT_SIZE}; // 10MB
    const ALLOWED_FILE_TYPES = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/zip', 'application/x-zip-compressed'];
    const defaultFilePath = '{ECONTRACT.contract_path}';
    const isVietnamese = true; // Thiết lập ngôn ngữ

    document.getElementById('select-file-button').addEventListener('click', function () {
        document.getElementById('contract-file').click();
    });

    document.getElementById('contract-file').addEventListener('change', handleFileSelect);
    document.getElementById('drop-zone').addEventListener('dragover', handleDragOver);
    document.getElementById('drop-zone').addEventListener('dragleave', handleDragLeave);
    document.getElementById('drop-zone').addEventListener('drop', handleDrop);

    document.querySelector('form').addEventListener('submit', prepareSubmit);

    function handleFileSelect(event) {
        var files = event.target.files;
        processFile(files[0]);
        updateFileList();
    }

    function handleDragOver(event) {
        event.preventDefault();
        document.getElementById('drop-zone').classList.add('dragging');
    }

    function handleDragLeave() {
        document.getElementById('drop-zone').classList.remove('dragging');
    }

    function handleDrop(event) {
        event.preventDefault();
        document.getElementById('drop-zone').classList.remove('dragging');
        var files = event.dataTransfer.files;
        processFile(files[0]);
        updateFileList();
        var inputFile = document.getElementById('contract-file');
        var dataTransfer = new DataTransfer();
        // Thêm tệp vừa thả vào DataTransfer
        dataTransfer.items.add(files[0]);
        inputFile.files = dataTransfer.files;
    }

    function processFile(file) {
        if (!file) return;

        if (!ALLOWED_FILE_TYPES.includes(file.type)) {
            alert(isVietnamese
                ? `File không hợp lệ. Vui lòng chọn file có định dạng pdf, doc, docx hoặc zip.`
                : `File is not valid. Please select a file in pdf, doc, docx, or zip format.`);
        } else if (file.size > MAX_FILE_SIZE) {
            alert(isVietnamese
                ? `File quá lớn. Vui lòng chọn file dưới ${MAX_FILE_SIZE / 1024 / 1024} MB.`
                : `File is too large. Please select a file under ${MAX_FILE_SIZE / 1024 / 1024} MB.`);
        } else {
            selectedFiles = [file]; // Thay thế file cũ bằng file mới
        }
    }

    function updateFileList() {
        var fileList = document.getElementById('file-list');
        fileList.innerHTML = '';

        if (selectedFiles.length > 0) {
            // Hiển thị file đã chọn
            var fileItem = document.createElement('div');
            fileItem.classList.add('file-item');
            fileItem.innerHTML = `
            <span>${selectedFiles[0].name}</span>
            <button type="button" class="btn btn-danger btn-sm ml-1" onclick="removeFile()">x</button>
        `;
            fileList.appendChild(fileItem);
        } else if (defaultFilePath) {
            // Hiển thị file mặc định
            var fileItem = document.createElement('div');
            fileItem.classList.add('file-item');
            fileItem.innerHTML = `
            <span>` + defaultFilePath + `</span>
        `;
            fileList.appendChild(fileItem);
        }
    }

    function removeFile() {
        if (confirm(isVietnamese ? 'Bạn có muốn xóa file đã chọn?' : 'Do you want to remove the selected file?')) {
            selectedFiles = [];
            updateFileList();
        }
    }

    function prepareSubmit(event) {
        var inputFile = document.getElementById('contract-file');
        var dataTransfer = new DataTransfer();

        if (selectedFiles.length > 0) {
            dataTransfer.items.add(selectedFiles[0]);
        }

        inputFile.files = dataTransfer.files;
    }

    $(document).ready(function () {
        updateFileList();
    });
</script>
<!-- END: show_script_term_changed -->

<script type="text/javascript">
    var orderIds = JSON.parse('[{ARRAY_ORDER_IDS}]');
    const econtract_id = parseInt('{ECONTRACT.id}');

    function showHideKHCN() {
        if ($('#isIndividual').is(':checked')) {
            $('#checkUyQuyen').closest('.form-check').hide();
            $('#field-tax-code').hide();
            $('#field-representative').hide();
            $('#field-jobtitle').hide();
            $('#field-cccd').show();
            $('#field-customer-name .control-label').html('Tên khách hàng: <span class="required">*</span>');
            $('#field-customer-name input').attr('placeholder', 'Tên khách hàng');

        } else {
            $('#checkUyQuyen').closest('.form-check').show();
            $('#field-tax-code').show();
            $('#field-representative').show();
            $('#field-jobtitle').show();
            $('#field-cccd').hide();
            $('#field-customer-name .control-label').html('Tên công ty: <span class="required">*</span>');
            $('#field-customer-name input').attr('placeholder', 'Tên công ty');
        }
    }
    function showHideTermChanged() {
        if ($('#contractTermChanged').is(':checked')) {
            $('#box-term-changed-upload').show();
        } else {
            $('#box-term-changed-upload').hide();
        }
    }
    function showHideUyQuyen() {
        if ($('#checkUyQuyen').is(':checked')) {
            $('#box-uy-quyen').show();
        } else {
            $('#box-uy-quyen').hide();
        }
    }
    function changeCustomerId(show_modal = false) {
        if ($('#customer_id').val() == '' || $('#customer_id').val() == 0) {
            $('#show-cus-info').hide();
            if (show_modal) {
                alert('Hãy chọn khách hàng trước');
            }
        } else {
            // Nếu $('#old_customer_id').val() != $('#customer_id').val(), thì xóa danh sách đơn hàng đã chọn
            if ($('#old_customer_id').val() != $('#customer_id').val()) {
                orderIds = [];
                $('#choosedOrders').html('');
                $('#old_customer_id').val($('#customer_id').val());
            }

            // Gọi API lấy thông tin user, hiện vào 2 trường. Đồng thời load danh sách đơn hàng của khách này
            $.ajax({
                url: '{URL_LOAD_DATA_CUSTOMER}&userid=' + $('#customer_id').val(),
                method: 'GET',
                dataType: 'json',
                success: function (res) {
                    if (res.status) {
                        $('#cus-username').text(res.data.username);
                        $('#cus-fullname').text(res.data.fullname);
                        $('#show-cus-info').show();
                        $('#modalChooseOrders .modal-body').html(res.data.modal_html);
                        // Checked orders
                        $('#modalChooseOrders table tbody tr input').each((i, e) => {
                            let id = parseInt($(e).val());
                            if (orderIds.includes(id)) {
                                $(e).prop('checked', 'checked');
                            }
                        });
                        $('#modalChooseOrders .modal-footer button').removeClass('disabled');
                        if (show_modal) {
                            $('#modalChooseOrders').modal('show');
                        }
                    } else {
                        $('#show-cus-info').hide();
                        alert(res.message);
                    }
                    return false;
                },
                error: function (err) {
                    $('#show-cus-info').hide();
                    alert('Đã xảy ra lỗi, vui lòng thử lại!');
                    console.log(err);
                    return false;
                }
            });
        }
    }
    $(document).ready(function () {
        showHideKHCN();
        showHideTermChanged();
        showHideUyQuyen();
        changeCustomerId();
        loadOrdersData();
        $('#customer_id').change(function () {
            changeCustomerId(true);
        });
        $('#isIndividual').change(function () {
            showHideKHCN();
        });
        $('#contractTermChanged').change(function () {
            showHideTermChanged();
        });
        $('#checkUyQuyen').change(function () {
            showHideUyQuyen();
        });

        // Bắt sự kiện click nút Tạo/Lưu
        $('button[type="submit"]').click(function (e) {
            e.preventDefault();

            const checkbox = $('#contractTermChanged');
            const textarea = $('#noteContractTermChanged');

            if (checkbox.is(':checked') && checkbox.val() === '1') {
                if (textarea.val().trim() === '') {
                    alert('Vui lòng nhập nội dung vào ghi chú!');
                    textarea.focus();
                    return false;
                }
            }

            var companyFields = $('.required-1');
            var personalFields = $('.required-0');
            var isValid = true;

            if ($('#isIndividual').is(':checked')) {
                personalFields.each(function () {
                    if (!$(this).val()) {
                        isValid = false;
                    }
                });
            } else {
                companyFields.each(function () {
                    if (!$(this).val()) {
                        isValid = false;
                    }
                });
            }

            if (isValid || (!isValid && confirm('Cảnh báo nội dung hợp đồng chưa hoàn tất. Bạn có thực sự muốn tiếp tục không?'))) {
                $('form')[0].submit();
            }
        });

        $('#btn_sign_content').on('click', function() {
            if (!econtract_id) {
                alert('Không có hợp đồng để ký!');
                return;
            }
            $.ajax({
                url: '{FORM_ACTION}',
                type: 'POST',
                data: {
                    sign_contract: '{TOKEN}',
                    contract_id: econtract_id
                },
                success: function(response) {
                    if (response.status) {
                        alert(response.message);
                        $('#btn_sign_content').addClass('disabled');
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('Đã xảy ra lỗi khi gửi yêu cầu!');
                }
            });
        });

        // Xử lý xem trước
        $('#btn-preview').on('click', function(e) {
            e.preventDefault();
            var formData = new FormData($('form')[0]);
            formData.append('previewEcontract', '1');
            formData.delete('save');
            $.ajax({
                url: $('form').attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.status) {
                        window.location.href = response.redirect;
                    } else {
                        alert(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    alert('Có lỗi xảy ra: ' + error);
                }
            });
        });
    });

    $('[data-toggle="seluser"]').on('click', function (e) {
        e.preventDefault();
        var url = script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=users&' + nv_fc_variable;
        url += '=getuserid&area=' + $(this).data('area');
        nv_open_browse(url, "seluser", 850, 420, "resizable=no,scrollbars=no,toolbar=no,location=no,status=no");
    });

    function completedChoooseOrders() {
        orderIds = [];
        if ($('#modalChooseOrders table tbody tr').length) {
            $('#modalChooseOrders table tbody tr input:checked').each((i, e) => {
                let id = parseInt($(e).val());
                if (!orderIds.includes(id)) {
                    orderIds.push(id);
                }
            });
        }
        $('#modalChooseOrders').modal('hide');
        loadOrdersData();
    }

    function loadOrdersData(rebuild_label_orders = true) {
        if (orderIds.length == 0) {
            $('#sale-name').text('');
            $('#choosedOrders').html('');
            $('#alert-no-data-vips').show();
            $('#box-info-vips').hide();
        } else {
            $.ajax({
                url: script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=crmbidding&' + nv_fc_variable + '=econtract_content&load_order_data=1',
                data: { ids: orderIds, id: econtract_id },
                method: 'GET',
                dataType: 'json',
                success: function (res) {
                    console.log(res);
                    if (res.status) {
                        $('#sale-name').text(res.data.sale_name);
                        $('#price-info-total').text(res.data.price_info.total);
                        $('#price-info-discount').text(res.data.price_info.discount);
                        $('#price-info-total-payment').text(res.data.price_info.total_payment);
                        $('#alert-no-data-vips').hide();
                        $('#box-info-vips').show();

                        let table_vips_html = '';
                        res.data.vips.forEach(v => {
                            table_vips_html += `<tr>
                                    <td>`+ v.title + `</td>
                                    <td>`+ v.price_format + `</td>
                                </tr>`;
                        });
                        $('#box-info-vips table tbody').html(table_vips_html);

                        if (rebuild_label_orders) {
                            console.log(res.data.orders);
                            let html = '';
                            res.data.orders.forEach(v => {
                                html += `<li class="label-order-title btn-group">
                                    <a href="` + v.link + `" class="btn btn-xs btn-success" target="_blank">
                                        `+ v.code + ` <i class="fa fa-external-link" aria-hidden="true"></i>
                                        <input type="hidden" name="order_ids[]" value="`+ v.id + `">
                                    </a>
                                    <a href="javascript:;" onclick="removeOrder(this);return false;" data-order-id="`+ v.id + `" class="btn btn-xs btn-danger"><i class="fa fa-times" aria-hidden="true"></i></a>
                                </li>`;
                            });
                            $('#choosedOrders').html(html);
                        }
                    } else {
                        alert(res.message);
                        orderIds = [];
                        loadOrdersData();
                    }
                    return false;
                },
                error: function (err) {
                    $('#show-cus-info').hide();
                    alert('Đã xảy ra lỗi, vui lòng thử lại!');
                    console.log(err);
                    return false;
                }
            });
        }
    }

    function removeOrder(el) {
        let id = parseInt($(el).data('order-id'));
        if (orderIds.includes(id)) {
            orderIds = orderIds.filter(orderId => orderId !== id); // Xóa id khỏi array
        }
        $(el).closest('li').remove();
        loadOrdersData(false);
    }
</script>
<!-- END: main -->
