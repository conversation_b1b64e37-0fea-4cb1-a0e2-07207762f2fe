<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Module\crmbidding\Log;
use NukeViet\Module\crmbidding\User;
use NukeViet\Api\DoApi;

$user_extfield_keys = [
    'marketing_types',
    'custom_types'
];

$error = $row = [];
$showheader = $nv_Request->get_int('showheader', 'post,get', 1);
$id = $nv_Request->get_int('id', 'post,get', 0);
$comentid = $nv_Request->get_int('comentid', 'post,get', 0);
$action = $nv_Request->get_title('action', 'get', '');
$is_open = $nv_Request->get_int('is_open', 'post,get', 0);

if (!empty($id)) {
    $params = [
        'opportunitiesid' => $id
    ];
    $row = nv_local_api('GetDetailOpportunities', $params, $admin_info['username'], 'crmbidding');
    $row = json_decode($row, true);
    $row = $row['data'];
    $params = [];
}

if (empty($row)) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=opportunities&showheader=' . $showheader);
}

// Kiểm tra hot lead
$check_current_oppotunities = check_current_oppotunities($row);
$check_hot_lead = $check_current_oppotunities['hotting'];

//Khách hàng tiềm năng
$potential_customer = false;
if ($row['customs_id'] != '' || $row['orderid'] != '' || $row['orderid_dtnet'] != '') {
    $potential_customer = true;
}
// Kiểm tra xem thông của lead hiện tại có đang nóng ở nơi khác không
// Nếu lead hiện tại có đơn hàng hay gói vip thì ưu tiên lead hiện tại
// Nếu lead hiện tại và lead nóng khác cùng 1 người chăm thì ưu tiên bằng nhau
$first_time = NV_CURRENTTIME;
$another_hot_lead = check_hot_leads($row['phone'], $row['email'], $row['sub_phone'], $row['sub_email'], $row['id'], 2);
if (isset($another_hot_lead)) {
    $has_another_hot_lead = true;
    if ($another_hot_lead['status'] == 1 && $row['caregiver_id'] != $another_hot_lead['data']['caregiver_id'] && !$potential_customer) {
        $first_time = $row['first_time'];
    }
} else {
    $has_another_hot_lead = false;
}

// Lấy thông tin mở rộng của cơ hội nếu là thành viên
$row['user_fields'] = [];
$row['user'] = [];
if (!empty($row['user_id'])) {
    $ext_uinfo = $db->query('SELECT * FROM ' . NV_USERS_GLOBALTABLE . '_info WHERE userid = ' . $row['user_id'])->fetch();
    $row['user'] = $db->query('SELECT userid, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $row['user_id'])->fetch();
} else {
    $ext_uinfo = [];
}
foreach ($user_extfield_keys as $key) {
    $row['user_fields'][$key] = !empty($ext_uinfo[$key]) ? array_map('intval', explode(',', $ext_uinfo[$key])) : [];
    $row[$key] = $row['user_fields'][$key];
}
if (empty($row['user'])) {
    $row['user'] = [
        'userid' => 0,
        'username' => ''
    ];
}

$caregiver_id_leads = $mobiphone_record = array();
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE userid = ' . $admin_info['userid'];
$result = $db->query($sql);
$view_lead_messages = 0;
while ($row_groups_users = $result->fetch()) {
    $admin_config = json_decode($row_groups_users['config'], true);
    // Nếu có cấu hình có lead đó thì xem có đang bật cấu hình xem nguồn lead messages k?
    $view_lead_messages = $admin_config['sale_view_messages'] ?? 0;

    if ($row_groups_users['is_leader'] == 1) {
        $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $row_groups_users['group_id'] . ' AND userid != ' . $admin_info['userid'];
        $_result = $db->query($_sql);
        while ($_row_groups_users = $_result->fetch()) {
            $caregiver_id_leads[$_row_groups_users['userid']] = $_row_groups_users['userid'];
        }
    }
}
$caregiver_id_leads[$admin_info['userid']] = $admin_info['userid'];

// Kiểm tra đơn hàng để hiển thị link chi tiết khách hàng
if ($row['user_id'] != 0 && $row['caregiver_id'] != 0) {
    $arr_where['AND'][] = [
        '=' => [
            'user_id' => $row['user_id']
        ]
    ];

    $params['where'] = $arr_where;

    $api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
    $api->setModule('bidding')
        ->setLang('vi')
        ->setAction('ListBiddingCustoms')
        ->setData($params);
    $result_custom = $api->execute();
    $err = $api->getError();
    if (!empty($err)) {
        $error[] = $err;
    }
    if (isset($result_custom['data'])) {
        $result_custom['data']['caregiver_id'] = $row['caregiver_id'];
    }
}

if ($nv_Request->isset_request('accept', 'post')) {
    if ($admin_info['userid'] == $row['caregiver_id']) {
        if ($row['status'] == 4) {
            $data = [
                'status' => 1
            ];
        }
        $_params_update = [
            'opportunitiesid' => $id,
            'admin_id' => $admin_info['userid'],
            'data' => $data
        ];
        $status_update = nv_local_api('UpdateOpportunities', $_params_update, $admin_info['username'], 'crmbidding');
        $status_update = json_decode($status_update, true);
        $data = $_params_update = [];
    } else if ($row['caregiver_id'] > 0 and $admin_info['userid'] != $row['caregiver_id']) {
        die('Cơ hội đã có sale nhận.');
    } else if ($row['caregiver_id'] == 0) {
        $data = [
            'caregiver_id' => $admin_info['userid']
        ];
        if ($row['status'] == 4 or $row['status'] == 0) {
            $data['status'] = 1;
        }
        $where_api = [];
        $where_api['AND'][] = [
            '=' => [
                'caregiver_id' => 0
            ]
        ];
        $_params_update = [
            'opportunitiesid' => $id,
            'admin_id' => $admin_info['userid'],
            'data' => $data,
            'where' => $where_api
        ];

        try {
            $db->beginTransaction();
            lead_with_locking($row, 2);
            $status_update = nv_local_api('UpdateOpportunities', $_params_update, $admin_info['username'], 'crmbidding');
            $status_update = json_decode($status_update, true);
            add_log_3710(2, $id, 'Xác nhận chăm sóc cơ hội');
            $db->commit();
        } catch (PDOException $e) {
            $db->rollBack();
            trigger_error($e->getMessage());
        }

        if ($status_update['code'] != "0000") {
            $error = $status_update['message'];
            die($error);
        } else {
            if ($row['orderid'] > 0) {
                $data = $_params_update = [];
                // trường hợp đơn hàng thu hồi, k có người chăm sóc ở đơn, khi nhận ở cơ hội thì cập nhật vào đơn hàng luôn
                $infoAPI = [];
                $infoAPI = [
                    'biddingorder_id' => $row['orderid'],
                    'admin_id' => $admin_info['admin_id'],
                    'data' => [
                        'caregiver_id' => $admin_info['userid']
                    ]
                ];

                // check giá trị đơn hàng nếu đơn 0đ thì cho sale đó chốt đơn ở order và chăm sóc ở customs luôn
                $param = [
                    'id' => $row['orderid']
                ];
                $BiddingOrderResponse = CallAPI($param, 'GetBiddingOrder', 'bidding', API_API_URL, API_API_KEY, API_API_SECRET);
                if (isset($BiddingOrderResponse['data'])) {
                    $BiddingOrder = $BiddingOrderResponse['data'];
                    if ($BiddingOrder['total'] == 0) {
                        $infoAPI['data']['admin_id'] = $admin_info['admin_id'];

                        // từ đơn hàng, lấy customs_log-> lấy custom và update
                        $arr_where = [];
                        $arr_where['AND'][] = [
                            '=' => [
                                'order_id' => $row['orderid']
                            ]
                        ];
                        $params = [];
                        $params['where'] = $arr_where;
                        $ListBiddingCustomsLog = CallAPI($params, 'ListBiddingCustomsLog', 'bidding', API_API_URL, API_API_KEY, API_API_SECRET);
                        if (isset($ListBiddingCustomsLog['data'])) {
                            foreach ($ListBiddingCustomsLog['data'] as $CustomsLog) {
                                $arr_where = [];
                                $arr_where['AND'][] = [
                                    '=' => [
                                        'user_id' => $CustomsLog['user_id']
                                    ]
                                ];
                                $arr_where['AND'][] = [
                                    '=' => [
                                        'vip' => $CustomsLog['vip']
                                    ]
                                ];
                                $params = [];
                                $params['where'] = $arr_where;
                                $ListBiddingCustoms = CallAPI($params, 'ListBiddingCustoms', 'bidding', API_API_URL, API_API_KEY, API_API_SECRET);
                                if (isset($ListBiddingCustoms['data'])) {
                                    foreach ($ListBiddingCustoms['data'] as $customs) {
                                        $params = [];
                                        $params = [
                                            'biddingcustom_id' => $customs['id'],
                                            'admin_id' => $admin_info['admin_id'],
                                            'data' => [
                                                'admin_id' => $admin_info['admin_id']
                                            ]
                                        ];
                                        $UpdateBiddingCustom = CallAPI($params, 'UpdateBiddingCustom');
                                    }
                                }
                            }
                        }
                    }
                }
                $UpdateBiddingOrder = CallAPI($infoAPI, 'UpdateBiddingOrder');

                // ghi log order khi chưa có người chăm sóc
                $log_data[] = [
                    nv_show_name_user($array_user_id_users[$admin_info['userid']]['first_name'], $array_user_id_users[$admin_info['userid']]['last_name'], $array_user_id_users[$admin_info['userid']]['username']) . 'Xác nhận chăm sóc đơn hàng'
                ];

                $infoAPI = [];
                $infoAPI = [
                    'userid' => $admin_info['admin_id'],
                    'log_area' => 1,
                    'log_key' => 'LOG_CHANGE_ORDER_ADMIN',
                    'log_time' => NV_CURRENTTIME,
                    'log_data' => $log_data,
                    'order_id' => $row['orderid']
                ];

                $CreateBiddingAllLogs = CallAPI($infoAPI, 'CreateBiddingAllLogs');
            }

            // nếu có giao dịch nạp tiền
            if ($row['source_leads'] == 17) {
                $result_transaction = $db->query("SELECT * FROM nv4_wallet_transaction WHERE opportunities_id = " . $row['id']);
                if ($_transaction = $result_transaction->fetch()) {
                    $db->exec("UPDATE nv4_wallet_transaction SET caregiver_id = " . $admin_info['userid'] . " WHERE id = " . $_transaction['id']);
                }
            }

            // cập nhập leads nếu có
            if ($row['leadsid'] > 0) {
                $sql = "UPDATE " . NV_PREFIXLANG . '_' . $module_data . "_leads SET caregiver_id = " . $admin_info['userid'] . ", elasticsearch = 9 WHERE id = " . $row['leadsid'];
                $db->query($sql);
            }
        }
    }

    if ($row['orderid'] > 0) {
        $log_data[] = [
            nv_show_name_user($array_user_id_users[$admin_info['userid']]['first_name'], $array_user_id_users[$admin_info['userid']]['last_name'], $array_user_id_users[$admin_info['userid']]['username']) . ' xác nhận chăm sóc đơn hàng'
        ];
    } else {
        $log_data[] = [
            nv_show_name_user($array_user_id_users[$admin_info['userid']]['first_name'], $array_user_id_users[$admin_info['userid']]['last_name'], $array_user_id_users[$admin_info['userid']]['username']) . ' xác nhận chăm sóc cơ hội'
        ];
    }

    $sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (userid, log_area, log_key, log_time, log_data, oppotunities_id) VALUES (" . $admin_info['admin_id'] . ", 1, 'LOG_ADMIN_UPDATE_LEADS_INFO', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $id . ")";
    $db->query($sql);
    die('OK');
}

// đoạn kiểm tra quyền hạn trước khi sửa người chăm sóc hay thực hiện chăm sóc
if ($view_lead_messages == 1 and $row['source_leads'] != 4) {
    $view_lead_messages = 0;
}

// check lại leads nguội hay k
if ($is_open == 1) {
    $type_open_pre = $nv_Request->get_int('type_open_pre', 'post,get', 1);
    $id_open_pre = $nv_Request->get_int('id_open_pre', 'post,get', 0);
    if ($id_open_pre > 0) {
        if ($type_open_pre == 1) {
            $params_leads = [
                'leadid' => $id_open_pre
            ];
            $data_leads = nv_local_api('GetDetailLeads', $params_leads, $admin_info['username'], $module_name);
            $data_leads = json_decode($data_leads, true);
            $row_data = $data_leads['data'];
            if (empty($row_data)) {
                $is_open = 0;
            }
        } else {
            $params = [
                'opportunitiesid' => $id_open_pre
            ];
            $ListAllOpportunities = nv_local_api('GetDetailOpportunities', $params, $admin_info['username'], $module_name);
            $ListAllOpportunities = json_decode($ListAllOpportunities, true);
            $row_data = $ListAllOpportunities['data'];
            if (empty($row_data)) {
                $is_open = 0;
            }
        }
        if (!empty($row_data)) {
            $is_open_lead = 0;
            if (!empty($row['phone']) and !empty($row_data['phone']) and (substr($row_data['phone'], -9) == substr($row['phone'], -9))) {
                $is_open_lead = 1;
            }
            if (!empty($row['phone']) and !empty($row_data['sub_phone']) and (strpos($row_data['sub_phone'], substr($row['phone'], -9))) !== false) {
                $is_open_lead = 1;
            }
            if (!empty($row['sub_phone']) and !empty($row_data['phone']) and strpos($row['sub_phone'], $row_data['phone']) !== false) {
                $is_open_lead = 1;
            }
            if (!empty($row['sub_phone']) and !empty($row_data['sub_phone'])) {
                $i = array_intersect(explode(',', $row['sub_phone']), explode(',', $row_data['sub_phone']));
                foreach ($i as $p) {
                    $is_open_lead = 1;
                }
            }

            if (!empty($row['sub_email']) and !empty($row_data['sub_email'])) {
                $i = array_intersect(explode(',', $row['sub_email']), explode(',', $row_data['sub_email']));
                foreach ($i as $p) {
                    $is_open_lead = 1;
                }
            }

            if (!empty($row['email']) and !empty($row_data['email']) and $row_data['email'] == $row['email']) {
                $is_open_lead = 1;
            }
            if (!empty($row['sub_email']) and !empty($row_data['email']) and strpos($row['sub_email'], $row_data['email']) !== false) {
                $is_open_lead = 1;
            }
            if (!empty($row['email']) and !empty($row_data['sub_email']) and strpos($row_data['sub_email'], $row['email']) !== false) {
                $is_open_lead = 1;
            }

            $check_current_oppotunities = check_current_oppotunities($row);
            $open_check = $check_current_oppotunities['hotting'] == true ? 0 : 1;

            // gán lại $is_open == 0, tức là k có quyền truy cập
            if (($is_open_lead == 1 and $open_check == 1 and $row['status'] != 2) or defined('NV_IS_SPADMIN') or in_array($row['caregiver_id'], $caregiver_id_leads) or $row['status'] == 4 or $row['status'] == 0) {
                $is_open = 1;
            } else {
                $is_open = 0;
            }
        }
    }
}
if ($row['caregiver_id'] != 0 and !in_array($row['caregiver_id'], $caregiver_id_leads) and !defined('NV_IS_SPADMIN') and $is_open == 0 and $view_lead_messages == 0) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=opportunities&showheader=' . $showheader);
}

if ($nv_Request->isset_request('save', 'post')) {
    $note = $nv_Request->get_textarea('note', '', NV_ALLOWED_HTML_TAGS);
    $schedule = $nv_Request->get_title('schedule', 'post,get', '');
    if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $schedule, $m)) {
        $schedule = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
    } else {
        $schedule = 0;
    }
    if ($note != '') {
        if ($comentid > 0) {
            // Chỉ có thể cập nhật trường hợp tự thêm, trường hợp hệ thống ghi nhận từ email thì để đó
            $db->query("UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_comment SET
            update_time=" . NV_CURRENTTIME . ", note=" . $db->quote($note) . ", schedule = " . $schedule . "
            WHERE id=" . $comentid . ' AND id_mail=0');

            // Trường hợp sửa lịch hẹn, chỉ tính lịch hẹn mới nhất
            $comment_firt = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_comment WHERE schedule > 0 AND source = 2 AND sourceid = ' . $id . ' ORDER BY update_time DESC LIMIT 1')->fetchColumn();
            if ($schedule > 0 and $comment_firt == $comentid) {
                $update_data = [];
                if ($has_another_hot_lead) {
                    if (($check_hot_lead == true && $row['updatetime'] > $another_hot_lead['data']['updatetime']) || $row['caregiver_id'] == $another_hot_lead['data']['caregiver_id'] || $potential_customer) {
                        $update_data['schedule'] = $schedule;
                    } else {
                        $update_data['schedule'] = $row['schedule'];
                    }
                } else {
                    $update_data['schedule'] = $schedule;
                }
                $_params_update = [
                    'opportunitiesid' => $id,
                    'data' => $update_data,
                    'admin_id' => $admin_info['userid']
                ];
                $status_update = nv_local_api('UpdateOpportunities', $_params_update, $admin_info['username'], 'crmbidding');
            }
        } else {
            try {
                $db->beginTransaction();
                lead_with_locking($row, 2);
                $db->query("INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_comment (source, sourceid, post_id, timecreate, note, schedule) VALUES (2, " . $id . ", " . $admin_info['userid'] . ", " . NV_CURRENTTIME . ", " . $db->quote($note) . ", " . $schedule . ")");
                $data = [];
                if ($row['status'] == 4 or $row['status'] == 0) {
                    $data['status'] = 1;
                }

                // Nếu là comment đầu tiên của người chăm sóc thì cập nhật first_time
                if ($row['caregiver_id'] != 0 && $row['caregiver_id'] == $admin_info['userid'] && $row['first_time'] == 0) {
                    $data['first_time'] = $first_time;
                }

                // Nếu có lead trùng đang nóng thì kiểm tra lead hiện tại có nóng ko, nếu lead hiện tại nóng và có updatetime lớn hơn thì cập nhật updatetime
                // Hoặc lead hiện tại và lead nóng trùng cùng người chăm thì cập nhật lại updatetime
                // Hoặc lead hiện tại là KH tiềm năng (có đơn hàng hoặc gói vip) thì cập nhật lại updatetime
                if ($has_another_hot_lead) {
                    if (($check_hot_lead == true && $row['updatetime'] > $another_hot_lead['data']['updatetime']) || $row['caregiver_id'] == $another_hot_lead['data']['caregiver_id'] || $potential_customer) {
                        $data['updatetime'] = $data['last_comment'] = NV_CURRENTTIME;
                        $data['schedule'] = $schedule > 0 ? $schedule : $row['schedule'];
                    } else {
                        $data['updatetime'] = $row['updatetime'];
                        $data['last_comment'] = $row['last_comment'];
                        $data['schedule'] = $row['schedule'];
                    }
                } else {
                    $data['last_comment'] = NV_CURRENTTIME;
                    $data['schedule'] = $schedule > 0 ? $schedule : $row['schedule'];
                }

                // Nếu lead đã nguội mà chưa bị thu hồi và sale vẫn tiếp tục chăm thì sẽ tính lại quy trình 3 7 10 từ đầu
                $reset_3710 = false;
                if ($check_hot_lead == false && $row['status'] != 2 && $row['caregiver_id'] == $admin_info['userid']) {
                    $reset_3710 = true;
                    $data['first_time'] = $first_time;
                    $data['schedule'] = $schedule > 0 ? $schedule : 0;
                }

                //Nếu lead đang nóng theo ngày hẹn và quy trình trước đó theo ngày nhận lead đã fail mà tạo lại lịch hẹn mới thì tính quy trình chăm lại từ đầu
                $change_schedule = false;
                if ($check_hot_lead && $schedule > 0 && $row['schedule'] > $row['first_time'] && $row['schedule'] < NV_CURRENTTIME) {
                    $check_hot_without_schedule = check_careof_oppotunities($row);
                    if ($check_hot_without_schedule['hotting'] == false) {
                        $data['first_time'] = NV_CURRENTTIME;
                        $data['schedule'] = $schedule;
                        $change_schedule = true;
                    }
                }

                if ($row['caregiver_id'] == 0) {
                    $data['caregiver_id'] = $admin_info['userid'];
                    // cập nhập leads nếu có
                    if ($row['leadsid'] > 0) {
                        $db->query("UPDATE " . NV_PREFIXLANG . '_' . $module_data . "_leads SET caregiver_id = " . $admin_info['userid'] . " WHERE id = " . $row['leadsid']);
                    }

                    if (!empty($row['orderid'])) {
                        $infoAPI = [];

                        $infoAPI = [
                            'biddingorder_id' => $row['orderid'],
                            'admin_id' => $admin_info['admin_id'],
                            'data' => [
                                'caregiver_id' => $admin_info['userid']
                            ]
                        ];

                        $UpdateBiddingOrder = CallAPI($infoAPI, 'UpdateBiddingOrder');

                        // ghi log order khi chưa có người chăm sóc
                        $log_data[] = [
                            nv_show_name_user($array_user_id_users[$admin_info['userid']]['first_name'], $array_user_id_users[$admin_info['userid']]['last_name'], $array_user_id_users[$admin_info['userid']]['username']) . 'Xác nhận chăm sóc đơn hàng'
                        ];

                        $infoAPI = [];
                        $infoAPI = [
                            'userid' => $admin_info['admin_id'],
                            'log_area' => 1,
                            'log_key' => 'LOG_CHANGE_ORDER_ADMIN',
                            'log_time' => NV_CURRENTTIME,
                            'log_data' => $log_data,
                            'order_id' => $row['orderid']
                        ];

                        $CreateBiddingAllLogs = CallAPI($infoAPI, 'CreateBiddingAllLogs');
                    }

                    // nếu có giao dịch nạp tiền
                    if ($row['source_leads'] == 17) {
                        $result_transaction = $db->query("SELECT * FROM nv4_wallet_transaction WHERE opportunities_id = " . $row['id']);
                        if ($_transaction = $result_transaction->fetch()) {
                            $db->exec("UPDATE nv4_wallet_transaction SET caregiver_id = " . $admin_info['userid'] . " WHERE id = " . $_transaction['id']);
                        }
                    }

                    $log_data[] = [
                        nv_show_name_user($array_user_id_users[$admin_info['userid']]['first_name'], $array_user_id_users[$admin_info['userid']]['last_name'], $array_user_id_users[$admin_info['userid']]['username']) . ' xác nhận chăm sóc cơ hội'
                    ];
                    $_sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (userid, log_area, log_key, log_time, log_data, oppotunities_id) VALUES (" . $admin_info['admin_id'] . ", 1, 'LOG_ADMIN_UPDATE_LEADS_INFO', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $id . ")";
                    $db->query($_sql);
                    add_log_3710(2, $id, 'Comment đầu tiên, xác nhận chăm sóc cơ hội');
                }

                $_params_update = [
                    'opportunitiesid' => $id,
                    'admin_id' => $admin_info['userid'],
                    'data' => $data
                ];
                $status_update = nv_local_api('UpdateOpportunities', $_params_update, $admin_info['username'], 'crmbidding');
                $status_update = json_decode($status_update, true);

                if ($reset_3710) {
                    add_log_3710(2, $id, 'Lead nguội và sale vẫn tiếp tục chăm sóc, tính lại quy trình 3-7-10 từ đầu và reset lịch hẹn (nếu có)');
                }

                if ($change_schedule) {
                    add_log_3710(2, $id, 'Tạo lịch hẹn mới khi quy trình tính theo ngày nhận lead trước đó đã fail, tính lại quy trình 3-7-10 từ đầu');
                }

                $data = $_params_update = [];
                $db->commit();
            } catch (PDOException $e) {
                $db->rollBack();
                trigger_error($e->getMessage());
            }
        }
        nv_redirect_location(NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=' . $op . '&id=' . $id . '&showheader=' . $showheader);
    }
}

$row_old = $row;
$field_config = User::getFieldsConfig();

// Chỉnh sửa thông tin của cơ hội
if ($nv_Request->isset_request('submit', 'post')) {
    $row['name'] = nv_substr($nv_Request->get_title('name', 'post', ''), 0, 254);
    $row['phone'] = nv_substr($nv_Request->get_title('phone', 'post', ''), 0, 254);
    $row['sub_phone'] = nv_substr($nv_Request->get_title('sub_phone', 'post', ''), 0, 254);
    $row['email'] = nv_substr($nv_Request->get_string('email', 'post', ''), 0, 254);
    $row['sub_email'] = $nv_Request->get_string('sub_email', 'post', '');
    $row['address'] = nv_substr($nv_Request->get_title('address', 'post', ''), 0, 254);
    $row['company_name'] = nv_substr($nv_Request->get_title('company_name', 'post', ''), 0, 254);
    $row['address_company'] = nv_substr($nv_Request->get_title('address_company', 'post', ''), 0, 254);
    $row['tax'] = nv_substr($nv_Request->get_title('tax', 'post', ''), 0, 254);
    $row['status'] = $nv_Request->get_int('status', 'post', 1);
    $row['affilacate_id'] = $nv_Request->get_int('affilacate_id', 'post', $row_old['affilacate_id']);
    $row['caregiver_id'] = $nv_Request->get_int('caregiver_id', 'post', 0);
    $row['about'] = $nv_Request->get_textarea('about', '', NV_ALLOWED_HTML_TAGS);
    $row['siteid'] = $nv_Request->get_int('siteid', 'post', 0);
    $row['prefix_lang'] = $nv_Request->get_int('prefix_lang', 'post', 0);

    if (!empty($row['user_id'])) {
        foreach ($user_extfield_keys as $key) {
            if (isset($field_config[$key])) {
                $row[$key] = $nv_Request->get_typed_array($key, 'post', 'int', []);

                if (!empty($field_config[$key]['required']) and empty($row[$key])) {
                    $error[] = $nv_Lang->getModule('not_select') . ': ' . (empty($field_config[$key]['description']) ? $field_config[$key]['title'] : $field_config[$key]['description']);
                }
            }
        }
    }

    if ($row['name'] == '') {
        $error[] = $nv_Lang->getModule('error_required_name');
    }
    if ($row['phone'] == '' and $row['email'] == '') {
        $error[] = $nv_Lang->getModule('error_required_phone');
    } elseif ($row['phone'] != '' && !phonecheck($row['phone'], $row['prefix_lang'])) {
        $error[] = $nv_Lang->getModule('error_phone_number');
    } elseif ($row['email'] != '' && nv_check_valid_email($row['email']) != '') {
        $error[] = $nv_Lang->getModule('error_email');
    }

    // chưa có người chăm sóc thì mặc định lấy người chăm sóc là admin
    if ($row_old['caregiver_id'] == 0) {
        $row['caregiver_id'] = $row['caregiver_id'] > 0 ? $row['caregiver_id'] : $admin_info['userid'];
    }

    if ($row['source_leads'] == 2 and $row_old['affilacate_id'] == 0) {
        $row['affilacate_id'] = $row['affilacate_id'] > 0 ? $row['affilacate_id'] : $admin_info['userid'];
    }

    if ($row_old['caregiver_id'] != $row['caregiver_id']) {
        $row['first_time'] = NV_CURRENTTIME;
        $row['schedule'] = 0;
    }

    if (($row_old['status'] == 4 or $row_old['status'] == 0) and $row['caregiver_id'] > 0) {
        $row['status'] = 1;
    }

    if ($row['sub_email'] != '') {
        $row['sub_email'] = str_replace(';', ',', $row['sub_email']);
        $row['sub_email'] = str_replace("\n", ',', $row['sub_email']);
        $_arr_email = array();
        $list_mail = explode(',', $row['sub_email']);
        foreach ($list_mail as $_mail) {
            $_mail = trim($_mail);
            if (($check_valid_email = nv_check_valid_email($_mail)) != '') {
                $error[] = sprintf($nv_Lang->getModule('error_subemail'), $_mail);
            } elseif (!in_array($_mail, $_arr_email)) {
                $_arr_email[] = $_mail;
            }
        }
        $row['sub_email'] = implode(',', $_arr_email);
    }

    // Kiểm tra các thông tin check trùng nếu có thay đổi thì check có lead nóng không, nếu có lead nóng thì không cho update
    if ($row_old['phone'] != $row['phone'] || $row_old['email'] != $row['email'] || $row_old['sub_phone'] != $row['sub_phone'] || $row_old['sub_email'] != $row['sub_email'] || $row_old['caregiver_id'] != $row['caregiver_id']) {
        $hot_leads = check_hot_leads($row['phone'], $row['email'], $row['sub_phone'], $row['sub_email'], $row_old['id'], 2);
        if (isset($hot_leads) && $hot_leads['status'] == 1) {
            // Bỏ qua cảnh báo lead trùng nếu là trưởng nhóm đang chuyển nhóm cho người đang chăm lead nóng hoặc người chăm lead nóng đang chuyển người chăm sóc về mình hoặc là admin 2*
            $ignore_notice = (
                ((in_array($row_old['caregiver_id'], $caregiver_id_leads) || $row_old['caregiver_id'] == $admin_info['userid']) && $hot_leads['data']['caregiver_id'] == $row['caregiver_id']) ||
                $hot_leads['data']['caregiver_id'] == $admin_info['userid'] ||
                defined('NV_IS_SPADMIN')
            );
            if ($hot_leads['data']['id'] != $row_old['id'] && !$ignore_notice) {
                $error[] = hot_lead_error_message($hot_leads['data']);
            }
        }
    }

    if (empty($error)) {
        try {
            $db->beginTransaction();
            lead_with_locking($row, 2);
            $data = [
                'name' => $row['name'],
                'phone' => $row['phone'],
                'sub_phone' => $row['sub_phone'],
                'email' => $row['email'],
                'sub_email' => $row['sub_email'],
                'address' => $row['address'],
                'tax' => $row['tax'],
                'company_name' => $row['company_name'],
                'address_company' => $row['address_company'],
                'status' => $row['status'],
                'affilacate_id' => $row['affilacate_id'],
                'caregiver_id' => $row['caregiver_id'],
                'about' => $row['about'],
                'siteid' => $row['siteid'],
                'prefix_lang' => $row['prefix_lang']
            ];
            // Thay đổi người chăm sóc thì cập nhật lại first time
            if ($row_old['caregiver_id'] != $row['caregiver_id']) {
                $data['first_time'] = NV_CURRENTTIME;
                $data['schedule'] = 0;
            }
            $_params_update = [
                'opportunitiesid' => $id,
                'admin_id' => $admin_info['userid'],
                'data' => $data
            ];
            $status_update = nv_local_api('UpdateOpportunities', $_params_update, $admin_info['username'], 'crmbidding');
            $status_update = json_decode($status_update, true);
            $data = $_params_update = [];
            if ($status_update['code'] == "0000") {
                //Đã ghi log trong API, không cần thiết ghi log ở đây
                // $log_data = [
                //     $nv_Lang->getModule('log_update_opportunities_info')
                // ];
                // if ($row_old['name'] != $row['name']) {
                //     $log_data[] = [
                //         'Họ tên: ',
                //         $row_old['name'] . ' =&gt; ' . $row['name']
                //     ];
                // }
                // if ($row_old['phone'] != $row['phone']) {
                //     $log_data[] = [
                //         'Điện thoại:',
                //         $row_old['phone'] . ' =&gt; ' . $row['phone']
                //     ];
                // }
                // if ($row_old['sub_phone'] != $row['sub_phone']) {
                //     $log_data[] = [
                //         'Điện thoại khác:',
                //         $row_old['sub_phone'] . ' =&gt; ' . $row['sub_phone']
                //     ];
                // }
                // if ($row_old['email'] != $row['email']) {
                //     $log_data[] = [
                //         'Email:',
                //         $row_old['email'] . ' =&gt; ' . $row['email']
                //     ];
                // }
                // if ($row_old['sub_email'] != $row['sub_email']) {
                //     $log_data[] = [
                //         'Email khác:',
                //         $row_old['sub_email'] . ' =&gt; ' . $row['sub_email']
                //     ];
                // }
                // if ($row_old['address'] != $row['address']) {
                //     $log_data[] = [
                //         'Địa chỉ:',
                //         $row_old['address'] . ' =&gt; ' . $row['address']
                //     ];
                // }
                // if ($row_old['company_name'] != $row['company_name']) {
                //     $log_data[] = [
                //         'Tên công ty:',
                //         $row_old['company_name'] . ' =&gt; ' . $row['company_name']
                //     ];
                // }
                // if ($row_old['tax'] != $row['tax']) {
                //     $log_data[] = [
                //         'Mã số thuế',
                //         $row_old['tax'] . ' =&gt; ' . $row['tax']
                //     ];
                // }

                // if ($row_old['address_company'] != $row['address_company']) {
                //     $log_data[] = [
                //         'Địa chỉ công ty:',
                //         $row_old['address_company'] . ' =&gt; ' . $row['address_company']
                //     ];
                // }
                // if ($row_old['about'] != $row['about']) {
                //     $log_data[] = [
                //         'Thông tin khác:',
                //         $row_old['about'] . ' =&gt; ' . $row['about']
                //     ];
                // }
                // if ($row_old['status'] != $row['status']) {
                //     $log_data[] = [
                //         'Trạng thái:',
                //         $array_status_opportunities[$row_old['status']] . ' =&gt; ' . $array_status_opportunities[$row['status']]
                //     ];
                // }
                // if ($row_old['affilacate_id'] != $row['affilacate_id']) {
                //     $log_data[] = [
                //         'Người giới thiệu:',
                //         nv_show_name_user($array_user_id_users[$row_old['affilacate_id']]['first_name'], $array_user_id_users[$row_old['affilacate_id']]['last_name'], $array_user_id_users[$row_old['affilacate_id']]['username']) . ' =&gt; ' . nv_show_name_user($array_user_id_users[$row['affilacate_id']]['first_name'], $array_user_id_users[$row['affilacate_id']]['last_name'], $array_user_id_users[$row['affilacate_id']]['username'])
                //     ];
                // }
                // if ($row_old['siteid'] != $row['siteid']) {
                //     $log_data[] = [
                //         'Website:',
                //         $array_site[$row_old['siteid']] . ' =&gt; ' . $array_site[$row['siteid']]
                //     ];
                // }

                // Xác định và cập nhật thông tin tài khoản
                if (!empty($row['user_id']) and defined('API_CRM_KEY')) {
                    $update_key = [];
                    $aip_data = [];

                    foreach ($user_extfield_keys as $key) {
                        if (isset($field_config[$key]) and (array_diff($row[$key], $row['user_fields'][$key]) != [] or array_diff($row['user_fields'][$key], $row[$key]))) {
                            $update_key[] = $key . '=' . $db->quote(implode(',', $row[$key]));
                            $aip_data[$key] = implode(',', $row[$key]);

                            // Xác định log
                            $old_value = $new_value = [];
                            foreach ($row['user_fields'][$key] as $_val) {
                                if (isset($field_config[$key]['field_choices'][$_val])) {
                                    $old_value[] = get_value_by_lang2($key, $field_config[$key]['field_choices'][$_val]);
                                } else {
                                    $old_value[] = ('#' . $_val);
                                }
                            }
                            foreach ($row[$key] as $_val) {
                                if (isset($field_config[$key]['field_choices'][$_val])) {
                                    $new_value[] = get_value_by_lang2($key, $field_config[$key]['field_choices'][$_val]);
                                } else {
                                    $new_value[] = ('#' . $_val);
                                }
                            }

                            // $log_data[] = [
                            //     ((empty($field_config[$key]['description']) ? $field_config[$key]['title'] : $field_config[$key]['description']) . ':'),
                            //     (implode(', ', $old_value)) . ' =&gt; ' . (implode(', ', $new_value))
                            // ];
                        }
                    }

                    if (!empty($aip_data)) {
                        // Sắp xếp mảng theo key từ thấp đến cao
                        ksort($aip_data);

                        // Tạo đoạn mã hash để kiểm tra
                        $checkhash = $row['user']['userid'] . $row['user']['username'];
                        foreach ($aip_data as $key => $value) {
                            $checkhash .= $key . '=' . $value;
                        }
                        $aip_data['username'] = $row['user']['username'];
                        $aip_data['checkhash'] = sha1($checkhash);

                        $result = nv_local_api('UpdateUserCustom', $aip_data, $admin_info['username'], 'users');
                        $result = json_decode($result, true);
                        if ($result['status'] != 'success') {
                            throw new Exception($result['message']);
                        } else {
                            // Đồng bộ lại thông tin tài khoản ở đây
                            $sql = "UPDATE " . NV_USERS_GLOBALTABLE . " SET last_update=" . NV_CURRENTTIME . " WHERE userid=" . $row['user_id'];
                            $db->query($sql);

                            $sql = "UPDATE " . NV_USERS_GLOBALTABLE . "_info SET " . implode(', ', $update_key) . " WHERE userid=" . $row['user_id'];
                            $db->query($sql);
                        }
                    }
                }

                if ($row_old['caregiver_id'] != $row['caregiver_id']) {
                    $log_data[] = [
                        'Người chăm sóc:',
                        nv_show_name_user($array_user_id_users[$row_old['caregiver_id']]['first_name'], $array_user_id_users[$row_old['caregiver_id']]['last_name'], $array_user_id_users[$row_old['caregiver_id']]['username']) . ' =&gt; ' . nv_show_name_user($array_user_id_users[$row['caregiver_id']]['first_name'], $array_user_id_users[$row['caregiver_id']]['last_name'], $array_user_id_users[$row['caregiver_id']]['username'])
                    ];

                    // thay đổi người chăm sóc ở lead luôn
                    if ($row_old['leadsid'] > 0) {
                        $data = [
                            'caregiver_id' => $row['caregiver_id'],
                            'status' => $row['status'],
                            'first_time' => NV_CURRENTTIME,
                            'schedule' => 0
                        ];
                        $_params_update = [
                            'leadsid' => $row_old['leadsid'],
                            'admin_id' => $admin_info['userid'],
                            'data' => $data
                        ];
                        $status_update = nv_local_api('UpdateLeads', $_params_update, $admin_info['username'], 'crmbidding');
                        $status_update = json_decode($status_update, true);
                        $data = $_params_update = [];
                        $log_data_lead = [
                            $nv_Lang->getModule('log_update_leads_info')
                        ];
                        $log_data_lead[] = [
                            'Người chăm sóc:',
                            nv_show_name_user($array_user_id_users[$row_old['caregiver_id']]['first_name'], $array_user_id_users[$row_old['caregiver_id']]['last_name'], $array_user_id_users[$row_old['caregiver_id']]['username']) . ' =&gt; ' . nv_show_name_user($array_user_id_users[$row['caregiver_id']]['first_name'], $array_user_id_users[$row['caregiver_id']]['last_name'], $array_user_id_users[$row['caregiver_id']]['username'])
                        ];
                        if ($row_old['status'] != $row['status']) {
                            $log_data_lead[] = [
                                'Trạng thái:',
                                $array_status_opportunities[$row_old['status']] . ' =&gt; ' . $array_status_opportunities[$row['status']]
                            ];
                        }
                        $sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (userid, log_area, log_key, log_time, log_data, leads_id) VALUES (" . $admin_info['admin_id'] . ", 1, 'LOG_ADMIN_UPDATE_LEADS_INFO', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data_lead)) . ", " . $row_old['leadsid'] . ")";
                        $db->query($sql);
                    }

                    // khi thay đổi người chăm sóc thì gửi mail cho người chăm sóc trước
                    if ($row_old['caregiver_id'] > 0) {

                        $subject = "Thông báo Cơ hội kinh doanh của bạn bị đổi người chăm sóc";
                        $messages = 'Cơ hội kinh doanh của bạn đã nguội, không có đủ thời gian chăm sóc theo quy định nên đã được hệ thống mở ra cho sale khác chăm. Như vậy bạn sẽ không còn quyền chăm khách này nữa, thông tin Cơ hội kinh doanh này như sau: <br/>';
                        $messages .= '- Họ tên: ' . $row['name'] . '<br/>';
                        $messages .= '- Email: ' . $row['email'] . '<br/>';
                        $messages .= '- Số điện thoại: ' . $row['phone'] . '<br/>';
                        /*
                         * $messages .= '- Điện thoại khác: ' . $row['sub_phone'] . '<br/>';
                         * $messages .= '- Email khác: ' . $row['sub_email'] . '<br/>';
                         */
                        if ($row['tax'] != '') {
                            $messages .= '- MST: ' . $row['tax'] . '<br/><br/>';
                        }
                        if ($row['company_name'] != '') {
                            $messages .= '- Tên công ty: ' . $row['company_name'] . '<br/><br/>';
                        }

                        $messages .= '- Thời gian cập nhật gần nhất của bạn là: ' . nv_date('H:i:s d/m/Y', $row['updatetime']) . '<br/><br/>';
                        $messages .= 'Người thực hiện thay đổi: ' . $admin_info['full_name'] . '<br/>';
                        $messages .= 'Thời gian thực hiện thay đổi: ' . nv_date('H:i:s d/m/Y', NV_CURRENTTIME) . '<br/>';
                        if ($row['caregiver_id'] != 0) {
                            $messages .= 'Người chăm sóc mới: ' . nv_show_name_user($array_user_id_users[$row['caregiver_id']]['first_name'], $array_user_id_users[$row['caregiver_id']]['last_name'], $array_user_id_users[$row['caregiver_id']]['username']) . '<br/>';
                        } else {
                            $messages .= 'Người chăm sóc mới: Không có người chăm sóc.';
                        }
                        $messages .= 'Nếu bạn thấy hệ thống xác định sai cơ chế nóng nguội (dựa vào thời gian cập nhật gần nhất và thời gian thực hiện thay đổi), vui lòng liên lạc với người chăm sóc mới và thông báo cho Phòng kỹ thuật kiểm tra để giải quyết';

                        if (!empty($array_user_id_users[$row_old['caregiver_id']]['email'])) {
                            $stmt = $db->prepare("INSERT INTO nv4_vi_bidding_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, source_email, `messageid`, `reject`, `bounce`, `complaint`, `click`, `open`, `failure`) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, :source_email, '', '', '', '', '', '', '')");
                            $stmt->bindValue(':userid', 0, PDO::PARAM_INT);
                            $stmt->bindParam(':main_mail', $array_user_id_users[$row_old['caregiver_id']]['email'], PDO::PARAM_STR);
                            $stmt->bindValue(':cc_mail', '', PDO::PARAM_STR);
                            $stmt->bindValue(':number_phone', 0, PDO::PARAM_STR);
                            $stmt->bindParam(':title', $subject, PDO::PARAM_STR);
                            $stmt->bindParam(':content', $messages, PDO::PARAM_STR, strlen($messages));
                            $stmt->bindValue(':type', 0, PDO::PARAM_INT);
                            $stmt->bindValue(':vip', 0, PDO::PARAM_INT);
                            $stmt->bindValue(':addtime', NV_CURRENTTIME, PDO::PARAM_INT);
                            $stmt->bindValue(':send_time', 0, PDO::PARAM_INT);
                            $stmt->bindValue(':status', 0, PDO::PARAM_INT);
                            $stmt->bindValue(':source_email', 0, PDO::PARAM_INT); // Nếu email được gữi từ site id->0 còn của site support->1
                            $exc = $stmt->execute();

                            /*
                             * $send = nv_sendmail([
                             * $global_config['site_name'],
                             * $global_config['site_email']
                             * ], $array_user_id_users[$row['caregiver_id']]['email'], $subject, $messages);
                             */
                        }
                    }
                    //Ghi log chăm sóc 3 7 10
                    if ($row['caregiver_id'] != 0) {
                        if ($row_old['caregiver_id'] == 0) {
                            $caregiver = 'Không có người chăm sóc =&gt; ' . nv_show_name_user($array_user_id_users[$row['caregiver_id']]['first_name'], $array_user_id_users[$row['caregiver_id']]['last_name'], $array_user_id_users[$row['caregiver_id']]['username']);
                        } else {
                            $caregiver = nv_show_name_user($array_user_id_users[$row_old['caregiver_id']]['first_name'], $array_user_id_users[$row_old['caregiver_id']]['last_name'], $array_user_id_users[$row_old['caregiver_id']]['username']) . ' =&gt; ' . nv_show_name_user($array_user_id_users[$row['caregiver_id']]['first_name'], $array_user_id_users[$row['caregiver_id']]['last_name'], $array_user_id_users[$row['caregiver_id']]['username']);
                        }
                        add_log_3710(2, $id, 'Sửa người chăm sóc từ ' . $caregiver);
                    }
                }

                // if (sizeof($log_data) > 1) { // không có gì thay đổi thì k ghi log
                //     $sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (userid, log_area, log_key, log_time, log_data, oppotunities_id) VALUES (" . $admin_info['admin_id'] . ", 1, 'LOG_ADMIN_UPDATE_LEADS_INFO', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $id . ")";
                //     $db->query($sql);
                // }

                if ($row['orderid'] > 0 and $row_old['caregiver_id'] == 0) {
                    $row['orderid'] = explode(',', $row['orderid']);
                    foreach ($row['orderid'] as $orderid) {
                        $infoAPI = [];
                        $infoAPI = [
                            'biddingorder_id' => $orderid,
                            'admin_id' => $admin_info['admin_id'],
                            'data' => [
                                'source_leads' => $row['source_leads']
                            ]
                        ];
                        $UpdateBiddingOrder = CallAPI($infoAPI, 'UpdateBiddingOrder');

                        // ghi log order khi chưa có người chăm sóc
                        $log_data = [
                            [
                                'Đổi người chăm sóc: ',
                                nv_show_name_user($array_user_id_users[$row['caregiver_id']]['first_name'], $array_user_id_users[$row['caregiver_id']]['last_name'], $array_user_id_users[$row['caregiver_id']]['username'])
                            ],
                            [
                                "Cũ",
                                nv_show_name_user($array_user_id_users[$row_old['caregiver_id']]['first_name'], $array_user_id_users[$row_old['caregiver_id']]['last_name'], $array_user_id_users[$row_old['caregiver_id']]['username'])
                            ]
                        ];

                        $infoAPI = [];
                        $infoAPI = [
                            'userid' => $admin_info['admin_id'],
                            'log_area' => 1,
                            'log_key' => 'LOG_CHANGE_ORDER_ADMIN',
                            'log_time' => NV_CURRENTTIME,
                            'log_data' => $log_data,
                            'order_id' => $orderid
                        ];

                        $CreateBiddingAllLogs = CallAPI($infoAPI, 'CreateBiddingAllLogs');
                    }
                }

                // nếu có giao dịch nạp tiền
                if ($row['source_leads'] == 17 and $row_old['caregiver_id'] == 0) {
                    $result_transaction = $db->query("SELECT * FROM nv4_wallet_transaction WHERE opportunities_id = " . $row['id']);
                    if ($_transaction = $result_transaction->fetch()) {
                        $db->exec("UPDATE nv4_wallet_transaction SET caregiver_id = " . $admin_info['userid'] . " WHERE id = " . $_transaction['id']);
                    }
                }
                $db->commit();
                $nv_Cache->delMod($module_name);
                nv_insert_logs(NV_LANG_DATA, $module_name, 'Edit Oppotunities Info', 'ID: ' . $id, $admin_info['userid']);
                nv_redirect_location(NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=' . $op . '&id=' . $id . '&showheader=' . $showheader . '&is_open=' . $is_open);
            } else {
                $error[] = $status_update["message"];
            }
            $db->commit(); // Commit để giải phóng khóa từ hàm lead_with_locking
        } catch (PDOException $e) {
            $db->rollBack();
            trigger_error($e->getMessage());
        }
    }
}

// comment
if ($comentid > 0 && $action != '') {
    $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_comment WHERE id=' . $comentid;
    $edit_comment = $db->query($_sql)->fetch();
} else {
    if ($row['leadsid'] > 0) {
        $where = 'WHERE (source=2 AND sourceid=' . $id . ') OR (source=1 AND sourceid=' . $row['leadsid'] . ')';
    } else {
        $where = 'WHERE source=2 AND sourceid=' . $id;
    }

    $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_comment ' . $where . ' ORDER BY timecreate DESC';
    $_query = $db->query($_sql);
    while ($_row = $_query->fetch()) {
        $_row['action'] = NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=' . $op . '&id=' . $id . '&showheader=' . $showheader . '&comentid=' . $_row['id'] . '&action=edit';
        if ($_row['post_id'] >= 0) {
            $_row['first_name'] = isset($array_user_id_users[$_row['post_id']]) ? $array_user_id_users[$_row['post_id']]['first_name'] : '';
            $_row['last_name'] = isset($array_user_id_users[$_row['post_id']]) ? $array_user_id_users[$_row['post_id']]['last_name'] : '';
            $_row['username'] = isset($array_user_id_users[$_row['post_id']]) ? $array_user_id_users[$_row['post_id']]['username'] : '';
            $_row['fullname'] = nv_show_name_user($_row['first_name'], $_row['last_name'], $_row['username']);
        } else {
            $_row['fullname'] = $nv_Lang->getModule('telepro_comment');
        }

        $row['comment'][$_row['id']] = $_row;
    }
}

// Log của tài khoản
$array_logs = [];
$sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_alllogs WHERE oppotunities_id=" . $id . " ORDER BY log_time DESC";
$result = $db->query($sql);
while ($_row = $result->fetch()) {
    $array_logs[$_row['id']] = $_row;
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('URL_DTINFO_ADMIN', URL_DTINFO_ADMIN);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('FORM_ACTION', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $id . '&showheader=' . $showheader . '&is_open=' . $is_open);
$xtpl->assign('hidden_edit', $row['caregiver_id'] == $admin_info['userid'] or !defined('NV_IS_SPADMIN') ? 'hidden' : '');
$xtpl->assign('TOKEND', NV_CHECK_SESSION);
foreach ($array_lang as $key => $value) {
    if ($key == $row['prefix_lang']) {
        $row['title_lang'] = $value;
    }

    $xtpl->assign('OPTION', [
        'key' => $key,
        'title' => $value,
        'selected' => ($key == $row['prefix_lang']) ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.select_prefix_lang');
}
$row_tmp = $row;
$row['affilacate'] = $row['affilacate_id'];

if ($row['affilacate'] > 0) {
    $affilacate_user = get_user($row['affilacate']);
    $row['affilacate_id'] = isset($affilacate_user['username']) ? nv_show_name_user($affilacate_user['first_name'], $affilacate_user['last_name'], $affilacate_user['username']) : '';
} else {
    $row['affilacate_id'] = '';
}

$row['admin_id'] = $row['caregiver_id'];
$row['source_leads_id'] = $row['source_leads'];
$row['caregiver_id'] = isset($array_user_id_users[$row['caregiver_id']]['username']) ? nv_show_name_user($array_user_id_users[$row['caregiver_id']]['first_name'], $array_user_id_users[$row['caregiver_id']]['last_name'], $array_user_id_users[$row['caregiver_id']]['username']) : '';
$row['updatetime'] = $row['updatetime'] != 0 ? nv_date('H:i:s d/m/Y', $row['updatetime']) : '';
$row['timecreate'] = $row['timecreate'] != 0 ? nv_date('H:i:s d/m/Y', $row['timecreate']) : '';
$row['activity_time'] = $row['activity_time'] != 0 ? nv_date('H:i:s d/m/Y', $row['activity_time']) : '';
if ($row['status'] != 2 && $row['first_time'] > 0) {
    $row['starttime_status'] = $check_hot_lead == false ? $nv_Lang->getModule('starttime_status') : '<br>' . $nv_Lang->getModule('hot_remaining', $check_current_oppotunities['remaining']);
    $xtpl->assign('STARTTIME', $check_current_oppotunities['starttime'] . ' ' . $row['starttime_status']);
    $xtpl->parse('main.starttime');
}
$row['status'] = $array_status_opportunities[$row['status']];
$row['siteid_tmp'] = $row['siteid'];
$row['siteid'] = isset($array_site[$row['siteid']]) ? $array_site[$row['siteid']] : '';

// Hiển thị loại đối tượng và thông tin quan tâm nếu lead gắn với thành viên
if (!empty($row['user_id'])) {
    if (isset($field_config['marketing_types'], $field_config['custom_types'])) {
        foreach ($user_extfield_keys as $key) {
            $xtpl->assign('F_NAME', empty($field_config[$key]['description']) ? $field_config[$key]['title'] : $field_config[$key]['description']);
            $xtpl->assign('F_KEY', $key);

            $num = 0;
            foreach ($field_config[$key]['field_choices'] as $f_key => $f_title) {
                $xtpl->assign('F_VALUE', get_value_by_lang2($f_key, $f_title));
                $xtpl->assign('F_INPUT_KEY', $f_key);

                if (in_array($f_key, $row['user_fields'][$key])) {
                    $xtpl->parse('main.ext_uinfo.loop.items.item');
                    $num++;
                }
                if (in_array($f_key, $row[$key])) {
                    $xtpl->assign('F_INPUT_CHECKED', ' checked="checked"');
                } else {
                    $xtpl->assign('F_INPUT_CHECKED', '');
                }

                $xtpl->parse('main.ext_uinfo.loop.edititem');
            }
            if ($num) {
                $xtpl->parse('main.ext_uinfo.loop.items');
            } else {
                $xtpl->parse('main.ext_uinfo.loop.na');
            }

            $xtpl->parse('main.ext_uinfo.loop');
        }

        $xtpl->parse('main.ext_uinfo');
    }
}
$row['prefix_lang_letter'] = $row['prefix_lang'] == 1 ? '(' . $nv_Lang->getModule('lang_en') . ')' : '(' . $nv_Lang->getModule('lang_vi') . ')';
$xtpl->assign('ROW', $row);

/**
 *
 * @since 11/09/2021
 * @link https://vinades.org/dauthau/dauthau.info/-/issues/536
 *       https://vinades.org/dauthau/dauthau.info/-/issues/607
 * <AUTHOR>
 *         nút đồng bộ thông tin tài khoản với bên id.dauthau.net
 */
if (!empty($row['user_id']) and defined('ID_API_USER_KEY')) {
    $xtpl->parse('main.sync_user_btn');
}

if ($row['label'] != '') {
    $row['label'] = explode(',', $row['label']);
    foreach ($row['label'] as $label) {
        $xtpl->assign('LABEL', $array_label[$label]);
        $xtpl->parse('main.label');
    }
}
if ($row['schedule'] > 0 and $row['schedule'] >= NV_CURRENTTIME) {
    $xtpl->assign('SCHEDULE', nv_date('d/m/Y', $row['schedule']));
    $xtpl->parse('main.schedule');
}
if ($row['source_leads_id'] == 2 || $row['source_leads_id'] == 15) {
    $params_leads = [
        'leadid' => $row['leadsid']
    ];
    $data_leads = nv_local_api('GetDetailLeads', $params_leads, $admin_info['username'], $module_name);
    $data_leads = json_decode($data_leads, true);
    $xtpl->assign('BUSINESS_ID', ($row['source_leads_id'] == 2 ? 'businessid=' : 'solicitorid=') . $data_leads['data']['businessid']);
    $xtpl->parse('main.create_lock_seo');
}

if (!isset($array_user_id_users[$row_tmp['affilacate_id']])) {
    $affilacate_user = get_user($row_tmp['affilacate_id']);
    if (!empty($affilacate_user)) {
        $array_user_id_users[$affilacate_user['userid']] = $affilacate_user;
    }
}
if (!empty($array_user_id_users)) {
    foreach ($array_user_id_users as $value) {
        $value['fullname'] = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
        $value['selected'] = $value['userid'] == $row_tmp['caregiver_id'] ? 'selected="selected"' : '';
        $value['selected_affilacate'] = $value['userid'] == $row_tmp['affilacate_id'] ? 'selected="selected"' : '';
        $value['title'] = nv_show_name_user($value['first_name'], $value['last_name'], $value['username']);
        $xtpl->assign('CAREGIVER_ID', $value);
        $xtpl->parse('main.caregiver_id');
        $xtpl->parse('main.affilacate.affilacate_id');
    }
}

foreach ($array_site as $key => $value) {
    $xtpl->assign('OPTION', [
        'key' => $key,
        'title' => $value,
        'selected' => ($key == $row['siteid_tmp']) ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.siteid');
}
if (defined('NV_IS_SPADMIN') or ($row['source_leads_id'] == 2 and $row['affilacate'] == 0)) {
    $xtpl->parse('main.affilacate');
} else {
    $xtpl->parse('main.no_affilacate');
}

// các trạng thái tự động thì k cho phép sửa nữa:
$_array_status_opportunities = $array_status_opportunities;
if ($row_tmp['status'] == 2) {
    unset($_array_status_opportunities[4]);
    unset($_array_status_opportunities[3]);
    unset($_array_status_opportunities[1]);
} else {
    unset($_array_status_opportunities[2]);
    unset($_array_status_opportunities[4]);
}
foreach ($_array_status_opportunities as $key => $value) {
    $xtpl->assign('STATUS', array(
        'id' => $key,
        'title' => $value,
        'selected' => $key == $row_tmp['status'] ? 'selected="selected"' : ''
    ));
    $xtpl->parse('main.status');
}

$view_detail_email = $nv_Request->get_absint('id_mail', 'get', 0);
if (!empty($row['comment'])) {
    foreach ($row['comment'] as $value) {
        $value['timecreate'] = $value['timecreate'] > 0 ? nv_date('H:i:s d/m/Y', $value['timecreate']) : '';
        $value['update_time'] = $value['update_time'] > 0 ? nv_date('H:i:s d/m/Y', $value['update_time']) : '';
        $value['schedule_title'] = $value['schedule'] > 0 ? nv_date('d/m/Y', $value['schedule']) : '';
        $xtpl->assign('LOG', $value);
        if ($value['schedule'] > 0) {
            $xtpl->parse('main.comment.loop.schedule');
        }
        if ($admin_info['userid'] == $value['post_id'] and empty($value['id_mail'])) {
            $xtpl->parse('main.comment.loop.edit');
        }

        if (!empty($value['id_mail'])) {
            $xtpl->assign('LINK_EMAIL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $id . '&amp;showheader=1&amp;id_mail=' . $value['id_mail']);
            $xtpl->parse('main.comment.loop.send_mail');

            // Xem chi tiết email
            if ($view_detail_email == $value['id_mail']) {
                $sql = "SELECT tb1.subject, tb2.text_plain FROM " . NV_PREFIXLANG . "_" . $module_data . "_emails tb1,
                    " . NV_PREFIXLANG . "_" . $module_data . "_emails_detail tb2 WHERE tb1.email_id=tb2.email_id AND tb1.email_id=" . $value['id_mail'];
                $mail = $db->query($sql)->fetch();

                if (!empty($mail)) {
                    $xtpl = new XTemplate('view-mail.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
                    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
                    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

                    $mail['text_plain'] = nv_nl2br($mail['text_plain']);
                    $xtpl->assign('EMAIL', $mail);

                    $xtpl->parse('main');
                    $contents = $xtpl->text('main');

                    include NV_ROOTDIR . '/includes/header.php';
                    echo nv_admin_theme($contents, 0);
                    include NV_ROOTDIR . '/includes/footer.php';
                }
            }
        } else {
            $xtpl->parse('main.comment.loop.plain_text');
        }

        $xtpl->parse('main.comment.loop');
    }
    $xtpl->parse('main.comment');
}

// Xem email không tồn tại
if ($view_detail_email > 0) {
    nv_info_die($nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_content'), 404);
}

if (!empty($edit_comment)) {
    $edit_comment['schedule'] = $edit_comment['schedule'] > 0 ? nv_date('d/m/Y', $edit_comment['schedule']) : '';
    $xtpl->assign('EDIT_LOG', $edit_comment);
    $xtpl->parse('main.edit_comment');
} else {
    $xtpl->parse('main.add_comment');
}

$xtpl->assign('DISABLE', $row['user_id'] == 0 ? 'disabled="disabled"' : '');
// $xtpl->assign('DISABLE_ORDER', $row['orderid'] == 0 ? 'disabled="disabled"' : '');
if ($row_tmp['status'] == 4) {
    $xtpl->parse('main.accept');
}

if ($row['convert_contact'] > 0) {
    $xtpl->assign('LINK_VIEW_CONTACT', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=contact&id_contact=" . $row['convert_contact']);
    $xtpl->parse('main.view_contact');
} else {
    $xtpl->assign('LINK_CONVERT_CONTACT', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=contact&id_convert=" . $row['leadsid']);
    $xtpl->parse('main.convert_contact');
}

if ($row['convert_organization'] > 0) {
    $xtpl->assign('LINK_VIEW_ORG', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=organizations&view=" . $row['convert_organization']);
    $xtpl->parse('main.view_org');
} else {
    $xtpl->assign('LINK_CONVERT_ORG', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=organizations&id_convert=" . $row['leadsid']);
    $xtpl->parse('main.convert_org');
}

if ($row['leadsid'] > 0) {
    $xtpl->assign('LINK_OPPOTUNITIES', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $row['leadsid'] . '&showheader=' . $showheader);
    $xtpl->parse('main.leadsid');
}
if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
    $xtpl->parse('main.edit');
}

if (!empty($another_hot_lead['data']) && $row['caregiver_id'] != $another_hot_lead['data']['caregiver_id'] && !$potential_customer) {
    $xtpl->assign('ANOTHER_HOT_LEAD', $nv_Lang->getModule('another_hot_lead'));
    $xtpl->parse('main.another_hot_lead');
}
//Có lead trùng và nguội thì nhắc sale check trùng để gộp
if (isset($another_hot_lead) && ($another_hot_lead['status'] == 1 || $another_hot_lead['status'] == 2)) {
    $xtpl->parse('main.prompt_to_combine');
}
$_sql = 'SELECT tb1.userid, tb1.first_name, tb1.last_name, tb1.username, tb1.email FROM ' . NV_USERS_GLOBALTABLE . ' tb1 INNER JOIN ' . $db_config['prefix'] . '_authors tb2 ON tb1.userid = tb2.admin_id WHERE tb1.userid IN (SELECT `admin_id` FROM ' . NV_AUTHORS_GLOBALTABLE . ' ORDER BY lev ASC)';
$array_user_all = $nv_Cache->db($_sql, 'userid', 'users');

foreach ($array_logs as $log) {
    $log['log_time'] = nv_date('H:i:s d/m/Y', $log['log_time']);
    $log['user'] = isset($array_user_all[$log['userid']]) ? nv_show_name_user($array_user_all[$log['userid']]['first_name'], $array_user_all[$log['userid']]['last_name'], $array_user_all[$log['userid']]['username']) : ($log['userid'] == 0 ? $nv_Lang->getModule('system') : '');
    $xtpl->assign('LOG', $log);

    $log_data = json_decode($log['log_data'], true);
    if (!is_array($log_data)) {
        $log_data = [];
    }
    $log_data = array_values($log_data);
    $log_data_size = sizeof($log_data);
    if ($log_data_size > 0) {
        // Tiêu đề log
        $log_data_show = $log_data[0];
        unset($log_data[0]);
        $xtpl->assign('LOG_DATA_SHOW', $log_data_show);
        if (is_array($log_data_show)) {
            $xtpl->parse('main.logall.data.sarray');
        } else {
            $xtpl->parse('main.logall.data.sstring');
        }

        // Nội dung chi tiết khác
        if (!empty($log_data)) {
            foreach ($log_data as $dother) {
                $xtpl->assign('LOG_DATA_OTHER', $dother);
                if (isset($dother['type']) and $dother['type'] == 'link') {
                    // Dạng log có link
                    $xtpl->assign('LOG_DATA_OTHER', Log::getLogLink($dother));
                    $xtpl->parse('main.logall.data.other.loop.sstring');
                } elseif (isset($dother['type']) and $dother['type'] == 'directlink') {
                    // Dạng log link đến trang khác
                    $xtpl->assign('LOG_DATA_OTHER', Log::getLogDirectLink($dother));
                    $xtpl->parse('main.logall.data.other.loop.sstring');
                } elseif (is_array($dother)) {
                    $xtpl->parse('main.logall.data.other.loop.sarray');
                } else {
                    $xtpl->parse('main.logall.data.other.loop.sstring');
                }
                $xtpl->parse('main.logall.data.other.loop');
            }
            $xtpl->parse('main.logall.data.other');
            $xtpl->parse('main.logall.data.other1');
        }

        $xtpl->parse('main.logall.data');
    }

    $xtpl->parse('main.logall');
}

// Hiển thị link chi tiết khách hàng
if (isset($result_custom['data'])) {
    $xtpl->assign('LINK_VIEW_MANAGER_CUSTOMER', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=crmbidding" . "&amp;" . NV_OP_VARIABLE . "=detail_customer&amp;user_id=" . $row['user_id'] . '&amp;admin_id=' . $result_custom['data']['caregiver_id']);
    $xtpl->parse('main.show_link_detai_customer');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('opportunities');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';

function CallAPI($infoAPI, $API, $module_name = 'bidding', $api_url = API_DAUTHAUINFO_URL, $api_key = API_DAUTHAUINFO_KEY, $api_secret = API_DAUTHAUINFO_SECRET)
{
    if (isset($infoAPI['page'])) {
        $infoAPI['page'] = intval($infoAPI['page']);
    }

    if (isset($infoAPI['perpage'])) {
        $infoAPI['perpage'] = intval($infoAPI['perpage']);
    }

    foreach ($infoAPI as $key => $value) {
        $params_customs[$key] = $value;
    }

    $api = new DoApi($api_url, $api_key, $api_secret);
    $api->setModule($module_name)
        ->setLang('vi')
        ->setAction($API)
        ->setData($params_customs);
    $result = $api->execute();
    $error = $api->getError();
    $data = [];
    if (empty($error) and $result['status'] == 'success') {
        $data = $result;
    }
    return $data;
}
