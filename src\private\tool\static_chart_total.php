<?php
define('NV_SYSTEM', true);

// define('NV_ROOTDIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME) . '/../public_html')));
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

require NV_ROOTDIR . '/includes/mainfile.php';
use NukeViet\Api\DoApi;

if (file_exists(NV_ROOTDIR . '/tool/static_chart_total.txt')) {
    $date_from = file_get_contents(NV_ROOTDIR . '/tool/static_chart_total.txt');
} else {
    $date_from = 1514739600; // 01/01/2018
}

$row = [];
// lấy ngày trước đó để cộng dồn các thống kê, phục vụ cho thống kê tăng trưởng
$row_previous = $db->query('SELECT total_user, total_vip, total_vieweb, total_leads, total_opportunities, total_sales FROM `nv4_vi_crmbidding_daily_statistic` WHERE date < ' . $date_from . ' ORDER BY date DESC LIMIT 1')->fetch();

if (!empty($row_previous)) {
    $row['total_user'] = $row_previous['total_user'];
    $row['total_vip'] = $row_previous['total_vip'];
    $row['total_vieweb'] = $row_previous['total_vieweb'];
    $row['total_leads'] = $row_previous['total_leads'];
    $row['total_opportunities'] = $row_previous['total_opportunities'];
    $row['total_sales'] = $row_previous['total_sales'];
} else {
    $row['total_leads'] = 0;
    $row['total_vip'] = 0;
    $row['total_vieweb'] = 0;
    $row['total_user'] = 0;
    $row['total_opportunities'] = 0;
    $row['total_sales'] = 0;
}

if ($date_from < NV_CURRENTTIME) {
    $date = mktime(0, 0, 0, date('m', $date_from), date('d', $date_from), date('Y', $date_from));
    $todate = mktime(23, 59, 59, date('m', $date_from), date('d', $date_from), date('Y', $date_from));

    $row['num_leads'] = 0;
    $row['num_vips'] = 0;
    $row['num_vieweb'] = 0;
    $row['num_users'] = 0;
    $row['num_opportunities'] = 0;
    $row['num_sales'] = 0;

    $where_leads = [];
    $where_leads['AND'][] = [
        '>=' => [
            'timecreate' => $date
        ]
    ];
    $where_leads['AND'][] = [
        '<=' => [
            'timecreate' => $todate
        ]
    ];
    $params_leads = [
        'where' => $where_leads
    ];

    $api_leads = new DoApi(API_CRM_URL, API_CRM_KEY, API_CRM_SECRET);
    $api_leads->setModule('crmbidding')
        ->setLang('vi')
        ->setAction('ListAllLeads')
        ->setData($params_leads);

    $total_leads = $api_leads->execute();
    if (!empty($total_leads['total'])) {
        $row['num_leads'] = intval($total_leads['total']);
    }

    $where_opportunities = [];
    $where_opportunities['AND'][] = [
        '>=' => [
            'timecreate' => $date
        ]
    ];
    $where_opportunities['AND'][] = [
        '<=' => [
            'timecreate' => $todate
        ]
    ];

    $params_opportunities = [
        'where' => $where_opportunities
    ];

    $api_opportunities = new DoApi(API_CRM_URL, API_CRM_KEY, API_CRM_SECRET);
    $api_opportunities->setModule('crmbidding')
        ->setLang('vi')
        ->setAction('ListAllOpportunities')
        ->setData($params_opportunities);

    $total_opportunities = $api_opportunities->execute();
    if (!empty($total_opportunities['total'])) {
        $row['num_opportunities'] = intval($total_opportunities['total']);
    }

    // lây ra các giao dịch nằm trong ngày đó và có người giao dịch tương ứng
    $row['num_users'] = $db->query('SELECT count(*) FROM `nv4_users` WHERE regdate >=' . $date . ' AND regdate <=' . $todate)->fetchColumn();

    $where_vips = $params_vips = $params_vip99 = [];
    $where_vips['AND'][] = [
        '>=' => [
            'from_time' => $date
        ]
    ];
    $where_vips['AND'][] = [
        '<=' => [
            'from_time' => $todate
        ]
    ];

    $where_vip99 = $where_vips;
    $where_vips['AND'][] = [
        '!=' => [
            'vip' => 99
        ]
    ];
    $where_vip99['AND'][] = [
        '=' => [
            'vip' => 99
        ]
    ];

    $params_vips = [
        'where' => $where_vips
    ];
    $params_vip99 = [
        'where' => $where_vip99
    ];
    $api_vips = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
    $api_vips->setModule('bidding')
        ->setLang('vi')
        ->setAction('ListBiddingCustoms')
        ->setData($params_vips);
    $total_vips = $api_vips->execute();

    $api_vip99 = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
    $api_vip99->setModule('bidding')
        ->setLang('vi')
        ->setAction('ListBiddingCustoms')
        ->setData($params_vip99);

    $total_vip99 = $api_vip99->execute();


    if (!empty($total_vips['total'])) {
        $row['num_vips'] = intval($total_vips['total']);
    }
    if (!empty($total_vip99['total'])) {
        $row['num_vieweb'] = intval($total_vip99['total']);
    }

    //Lấy doanh số theo ngày, Tổng doanh số = Doanh số thực nhận của đơn hàng + Doanh số thực nhận của Ví tiền
    $total_sales = 0;
    $sql = 'SELECT SUM(total_end) as total_end FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE static_time >= ' . $date . ' AND static_time <=' . $todate;
    $stmt1 = $db->query($sql);
    while ($row1 = $stmt1->fetch()) {
        $row['num_sales'] += $row1['total_end'];
    }
    $stmt1->closeCursor();

    $sql_wallet = 'SELECT SUM(total_day) as total_day FROM nv4_vi_crmbidding_sale_static WHERE date >= ' . $date . ' AND date <=' . $todate;
    $stmt2 = $db->query($sql_wallet);
    while ($row2 = $stmt2->fetch()) {
        $row['num_sales'] += $row2['total_day'];
    }
    $row['num_sales'] = round($row['num_sales']);

    $row['total_leads'] += $row['num_leads'];
    $row['total_vip'] += $row['num_vips'];
    $row['total_vieweb'] += $row['num_vieweb'];
    $row['total_user'] += $row['num_users'];
    $row['total_opportunities'] += $row['num_opportunities'];
    $row['total_sales'] += $row['num_sales'];

    echo "- Ngày: " . nv_date('d/m/Y', $date) . "\n";
    echo "- Leads: " . $row['num_leads'] . "\n";
    echo "- Opportunities: " . $row['num_opportunities'] . "\n";
    echo "- Users: " . $row['num_users'] . "\n";
    echo "- Vips: " . $row['num_vips'] . "\n";
    echo "- Vieweb: " . $row['num_vieweb'] . "\n";
    echo "- Sales: " . $row['num_sales'] . "\n";
    echo "- Total leads: " . $row['total_leads'] . "\n";
    echo "- Total opportunities: " . $row['total_opportunities'] . "\n";
    echo "- Total users: " . $row['total_user'] . "\n";
    echo "- Total vips: " . $row['total_vip'] . "\n";
    echo "- Total vieweb: " . $row['total_vieweb'] . "\n";
    echo "- Total sales: " . $row['total_sales'] . "\n";

    try {
        $check = $db->query('SELECT * FROM nv4_vi_crmbidding_daily_statistic WHERE date = ' . $date)->fetch();
        if (empty($check)) {
            $stmt = $db->prepare('INSERT INTO nv4_vi_crmbidding_daily_statistic(date, num_leads, num_opportunities, num_users, num_vips, num_vieweb, total_leads, total_opportunities, total_user, total_vip, total_vieweb, num_sales, total_sales) VALUES (:date, :num_leads, :num_opportunities, :num_users, :num_vips, :num_vieweb, :total_leads, :total_opportunities, :total_user, :total_vip, :total_vieweb, :num_sales, :total_sales)');
        } else {
            $stmt = $db->prepare('UPDATE nv4_vi_crmbidding_daily_statistic SET num_leads=:num_leads, num_opportunities=:num_opportunities, num_users=:num_users, num_vips=:num_vips, num_vieweb=:num_vieweb, total_leads=:total_leads, total_opportunities=:total_opportunities, total_user=:total_user, total_vip=:total_vip, total_vieweb=:total_vieweb, num_sales=:num_sales, total_sales=:total_sales WHERE date=:date');
        }
        $stmt->bindParam(':date', $date, PDO::PARAM_INT);
        $stmt->bindParam(':num_leads', $row['num_leads'], PDO::PARAM_INT);
        $stmt->bindParam(':num_opportunities', $row['num_opportunities'], PDO::PARAM_INT);
        $stmt->bindParam(':num_users', $row['num_users'], PDO::PARAM_INT);
        $stmt->bindParam(':num_vips', $row['num_vips'], PDO::PARAM_INT);
        $stmt->bindParam(':num_vieweb', $row['num_vieweb'], PDO::PARAM_INT);
        $stmt->bindParam(':num_sales', $row['num_sales'], PDO::PARAM_INT);
        $stmt->bindParam(':total_leads', $row['total_leads'], PDO::PARAM_INT);
        $stmt->bindParam(':total_user', $row['total_user'], PDO::PARAM_INT);
        $stmt->bindParam(':total_opportunities', $row['total_opportunities'], PDO::PARAM_INT);
        $stmt->bindParam(':total_vip', $row['total_vip'], PDO::PARAM_INT);
        $stmt->bindParam(':total_vieweb', $row['total_vieweb'], PDO::PARAM_INT);
        $stmt->bindParam(':total_sales', $row['total_sales'], PDO::PARAM_INT);
        $exc = $stmt->execute();
        } catch (PDOException $e) {
        print_r($e);
        die('lỗi');
    }

    $date_from = $date_from + 86400;
    file_put_contents(NV_ROOTDIR . '/tool/static_chart_total.txt', $date_from);

    echo ('<meta http-equiv="refresh" content="1;url=/tool/static_chart_total.php?t=' . NV_CURRENTTIME . '" > Đang thực hiện...<br>');
} else {
    die('Đã chạy xong!!!');
}
echo "\n<br>Cập nhật trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
