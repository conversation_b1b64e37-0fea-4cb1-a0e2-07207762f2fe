<?php
register_shutdown_function("fatal_handler");

function fatal_handler()
{
    $error = error_get_last();
    if ($error !== NULL) {
        echo ("<pre><code>");
        print_r($error);
        die("</code></pre>");
    }
}
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __FILE__), PATHINFO_DIRNAME) . '/..')));

require NV_CONSOLE_DIR . '/server.php';
require NV_ROOTDIR . '/includes/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

set_time_limit(10000);

/*
 * $last_id = 0;
 * $filelog = NV_CONSOLE_DIR . '/tool_2025/update_user_info.txt';
 * if (file_exists($filelog)) {
 * $last_id = intval(file_get_contents($filelog));
 * } else {
 * $last_id = $db->query("SELECT MIN(userid) FROM nv4_users_info")->fetchColumn();
 * }
 */

$i = 0;
try {
    // $_sql = "SELECT * FROM `nv4_users_info` WHERE userid > " . $last_id . " ORDER BY userid ASC LIMIT 100";

    // chạy lại các tk sau do có tiền USD
    $_sql = "SELECT * FROM `nv4_users_info` WHERE userid IN (47866, 119142, 148387)";
    $_result = $db->query($_sql);
    while ($tmp = $_result->fetch()) {
        // $last_id = $tmp["userid"];
        // kiểm tra điểm có sử dụng điểm k
        // tổng điểm đã nạp
        $_sql = [];
        $check_used_point = $db->query("SELECT SUM(point_in) as s_point_in, SUM(point_out) as s_point_out FROM `nv4_points_customs_static` WHERE userid = " . $tmp['userid'])->fetch();
        if (!empty($check_used_point)) {
            if ($check_used_point['s_point_out'] > 0 and $tmp['used_point'] == 0) {
                $_sql[] = 'used_point = 1';
            }
            if ($check_used_point['s_point_in'] > 0) {
                $_sql[] = 'point_in = ' . $check_used_point['s_point_in'];
            }
        }

        // số điểm còn lại
        $point_total = $db->query("SELECT point_total FROM `nv4_points_customs` WHERE userid = " . $tmp['userid'])->fetchColumn();
        if ($point_total > 0) {
            $_sql[] = 'point_total = ' . $point_total;
        }

        // ví tiền
        $check_wallet = $db->query("SELECT * FROM `nv4_wallet_money` WHERE userid = " . $tmp['userid'] . " AND money_unit = 'VND'")->fetch();
        if (!empty($check_wallet)) {
            if ($check_wallet['money_in'] > 0) {
                $_sql[] = 'charge_wallet = ' . $check_wallet['money_in'];
            }
            if ($check_wallet['money_total'] > 0) {
                $_sql[] = "money_wallet = " . $check_wallet['money_total'];
            }
        }

        // vip, k tính còn hạn hay k, chỉ tính đã có gói vip
        $query_orders = $db->query("SELECT * FROM `nv4_vi_bidding_orders_general` WHERE userid = " . $tmp['userid']);
        $_arr_vip = [];
        $caregiver_id = 0;
        $revenue = 0;
        while ($tmp_order = $query_orders->fetch()) {
            $_arr_vip[$tmp_order['vip']] = $tmp_order['vip'];
            $caregiver_id = $tmp_order['caregiver_id'];
            $revenue += $tmp_order['total_end'];
        }
        if (!empty($_arr_vip)) {
            $_sql[] = "vip = " . $db->quote(implode(',', $_arr_vip));
        }

        if ($revenue > 0) {
            $_sql[] = "revenue = " . $revenue;
        }

        if ($caregiver_id == 0) {
            // nếu chưa xđ dc người chăm sóc thì lấy theo cơ hội gần nhất
            $caregiver_id = $db->query("SELECT caregiver_id FROM `nv4_vi_crmbidding_opportunities` WHERE user_id = " . $tmp['userid'] . " ORDER BY updatetime DESC LIMIT 1")->fetchColumn();
        }

        if ($caregiver_id > 0) {
            $_sql[] = "saleid = " . $caregiver_id;
        }

        if (!empty($_sql)) {
            $db->query("UPDATE nv4_users_info SET " . implode(',', $_sql) . " WHERE userid = " . $tmp['userid']);
            echo "\n <br>Update userid: " . $tmp['userid'];
        }
        $i++;
    }
    // file_put_contents($filelog, $last_id);
} catch (PDOException $e) {
    print_r($e);
    exit(1);
}

echo "\n <br><br>Tìm kiếm trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
if ($i == 0) {
    exit(1);
}
die('ok');
