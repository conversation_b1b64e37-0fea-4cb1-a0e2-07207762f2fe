<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */
if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

/**
 * Chức năng lấy ngôn ngữ cả vi và en
 */
function get_both_lang($lang_alias)
{
    global $nv_Lang, $module_file;
    $message_log = [
        'vi' => '',
        'en' => ''
    ];
    $nv_Lang->changeLang('vi');
    $nv_Lang->loadModule($module_file, false, true);
    $message_log['vi'] = $nv_Lang->getModule($lang_alias);
    $nv_Lang->changeLang('en');
    $nv_Lang->loadModule($module_file, false, true);
    $message_log['en'] = $nv_Lang->getModule($lang_alias);
    $nv_Lang->changeLang(NV_LANG_INTERFACE);
    return $message_log;
}

// Danh sách gói VIP
$global_arr_vip = [
    1 => $nv_Lang->getModule('vip1'),
    11 => $nv_Lang->getModule('vip11'),
    2 => $nv_Lang->getModule('vip2'),
    21 => $nv_Lang->getModule('vip21'),
    3 => $nv_Lang->getModule('vip3'),
    31 => $nv_Lang->getModule('vip31'),
    32 => $nv_Lang->getModule('vip32'),
    4 => $nv_Lang->getModule('vip4'),
    5 => $nv_Lang->getModule('vip5'),
    99 => $nv_Lang->getModule('vip99'),
    200 => $nv_Lang->getModule('non_vip99'),
    88 => $nv_Lang->getModule('vip88'),
    77 => $nv_Lang->getModule('vip77'),
    69 => $nv_Lang->getModule('vip69'),
    6 => $nv_Lang->getModule('vip6'),
    7 => $nv_Lang->getModule('vip7'),
    8 => $nv_Lang->getModule('vip8'),
    19 => $nv_Lang->getModule('vip19'),
    55 => $nv_Lang->getModule('vip55'),
    66 => $nv_Lang->getModule('vip66'),
    68 => $nv_Lang->getModule('vip68'),
    89 => $nv_Lang->getModule('vip89'),
    100 => $nv_Lang->getModule('vip100'),
    101 => $nv_Lang->getModule('vip101')
];
