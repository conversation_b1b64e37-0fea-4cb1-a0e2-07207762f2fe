<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Module\crmbidding\Log;

$array_search = $array_logs = $where_api = [];
$curent = nv_date('d-m-Y', NV_CURRENTTIME);
$curent_from = '01-' . nv_date('m-Y', NV_CURRENTTIME);
$array_search['time_from'] = $nv_Request->get_title('time_from', 'post,get', $curent_from);
$array_search['time_to'] = $nv_Request->get_title('time_to', 'post,get', $curent);
$array_search['admin_id'] = $nv_Request->get_int('admin_id', 'post,get', -1);
$array_search['log_type'] = $nv_Request->get_int('log_type', 'post,get', 0); // Phân loại chia đơn

$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;

if (preg_match("/^([0-9]{1,2})([-\/])([0-9]{1,2})([-\/])([0-9]{4})$/", $array_search['time_from'], $m)) {
    $sfrom = mktime(0, 0, 0, $m[3], $m[1], $m[5]);
} else {
    $sfrom = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
}
if (preg_match("/^([0-9]{1,2})([-\/])([0-9]{1,2})([-\/])([0-9]{4})$/", $array_search['time_to'], $m)) {
    $sto = mktime(23, 59, 59, $m[3], $m[1], $m[5]);
} else {
    $sto = mktime(23, 59, 59, nv_date('m', NV_CURRENTTIME), nv_date('d', NV_CURRENTTIME), nv_date('Y', NV_CURRENTTIME));
}

$error = [];
// giới hạn 6 tháng
$sfrom_check = $sto - (86400 * 6 * 30);
if ($sfrom < $sfrom_check) {
    $sfrom = $sfrom_check;
    $base_url .= '&amp;time_from=' . urlencode(nv_date('d/m/Y', $sfrom_check));
    $error[] = $nv_Lang->getModule('error_max_time');
}

$base_url .= '&amp;time_from=' . urlencode($array_search['time_from']);
$base_url .= '&amp;time_to=' . urlencode($array_search['time_to']);

$where_api['AND'] = [
    [
        '>=' => [
            'log_time' => intval($sfrom)
        ]
    ],
    [
        '<=' => [
            'log_time' => intval($sto)
        ]
    ],
    [
        '>' => [
            'log_type' => 1
        ]
    ]
];

// Danh sách các loại log
$user_action_logs = [
    2 => $nv_Lang->getModule('leads_messenger_to_sale'),
    3 => $nv_Lang->getModule('sale_order_to'),
    4 => $nv_Lang->getModule('api_pro_to_sale'),
    5 => $nv_Lang->getModule('sale_order_to_vieweb'),
    6 => $nv_Lang->getModule('share_transaction_wallet_to'),
    7 => $nv_Lang->getModule('sale_order_dtnet_to'),
    8 => $nv_Lang->getModule('leads_zalo_to_sale')
];

$where_api['AND_OR'] = [
    [
        '=' => [
            'log_key' => 'LOG_SYS_UPDATE_LEADS_INFO'
        ]
    ],
    [
        '=' => [
            'log_key' => 'SYSTEM_ADD_LEADS_FROM_USERS'
        ]
    ],
    [
        '=' => [
            'log_key' => 'SYSTEM_ADD_OPPORTUNITIES_FROM_ORDER'
        ]
    ],
    [
        '=' => [
            'log_key' => 'LOG_CHANGE_ORDER_ADMIN'
        ]
    ],
    [
        '=' => [
            'log_key' => 'SYSTEM_ADD_OPPORTUNITIES_FROM_ORDER_DTNET'
        ]
    ]
];

if ($array_search['admin_id'] > -1) {
    $base_url .= '&amp;admin_id=' . $array_search['admin_id'];
    $where_api['AND'][] = [
        '=' => [
            'caregiver_id' => $array_search['admin_id']
        ]
    ];
}

if ($array_search['log_type'] > 0) {
    $base_url .= '&amp;log_type=' . $array_search['log_type'];
    $where_api['AND'][] = [
        '=' => [
            'log_type' => $array_search['log_type']
        ]
    ];
}

// Check xem bao nhiêu dữ liệu
$_sql = 'SELECT tb1.userid, tb1.first_name, tb1.last_name, tb1.username, tb1.email FROM ' . NV_USERS_GLOBALTABLE . ' tb1 INNER JOIN ' . $db_config['prefix'] . '_authors tb2 ON tb1.userid = tb2.admin_id WHERE tb1.userid IN (SELECT `admin_id` FROM ' . NV_AUTHORS_GLOBALTABLE . ' ORDER BY lev ASC)';
$array_user_all = $nv_Cache->db($_sql, 'userid', 'users');

$page = $nv_Request->get_int('page', 'post,get', 1);
$per_page = 20;
$total = 0;
$params = [
    'page' => $page,
    'perpage' => $per_page,
    'get_total' => true
];
$order['log_time'] = "DESC";
$params['where'] = $where_api;
$params['order'] = $order;
$data_log = nv_local_api('ListAllLog', $params, $admin_info['username'], 'crmbidding');
$data_log = json_decode($data_log, true);
if (!empty($data_log['data'])) {
    $total = $data_log['total'];
    foreach ($data_log['data'] as $row) {
        $row['log_time'] = nv_date('H:i:s d/m/Y', $row['log_time']);
        $row['user_name'] = isset($array_user_all[$row['userid']]) ? nv_show_name_user($array_user_all[$row['userid']]['first_name'], $array_user_all[$row['userid']]['last_name'], $array_user_all[$row['userid']]['username']) : ($row['userid'] == 0 ? $nv_Lang->getModule('system') : '');
        $row['leads_info'] = '';
        $row['leads_info_link'] = '';
        if ($row['leads_id'] != 0) {
            $select_leads = $db->query('SELECT id, name FROM nv4_vi_crmbidding_leads WHERE id=' . $row['leads_id'])->fetch();
            if (!empty($select_leads)) {
                $row['leads_info'] = $select_leads['name'];
                $row['leads_info_link'] = NV_BASE_ADMINURL . 'index.php?' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_info&id=' . $row['leads_id'] . '&showheader=1';
            }
        } elseif ($row['orderid'] != 0 && $row['log_type'] == 7) {
            // Đơn hàng site dt-net
            $row['leads_info'] = $nv_Lang->getModule('name_bill') . '#' . $row['orderid'];
            $row['leads_info_link'] = URL_DTNET_ADMIN . 'index.php?language=vi&nv=dn&op=payment&vieworderid=' . $row['orderid'];
        } elseif ($row['orderid'] != 0) {
            // Đơn hàng site dt-info
            $row['leads_info'] = $nv_Lang->getModule('name_bill') . '#' . $row['orderid'];
            $row['leads_info_link'] = URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $row['orderid'];
        }
        $row['log_data'] = json_decode($row['log_data'], true);
        // Xử lý nội dung log
        if (is_array($row['log_data']) && count($row['log_data']) > 0) {
            $row['log_data_show'] = $row['log_data'][0];
            $row['log_data_other'] = array_slice($row['log_data'], 1);
            // Xử lý nội dung chi tiết khác
            $row['log_data_processed'] = [];
            foreach ($row['log_data_other'] as $dother) {
                if (isset($dother['type'])) {
                    if ($dother['type'] == 'link') {
                        $row['log_data_processed'][] = Log::getLogLink($dother);
                    } elseif ($dother['type'] == 'directlink') {
                        $row['log_data_processed'][] = Log::getLogDirectLink($dother);
                    } else {
                        $row['log_data_processed'][] = $dother;
                    }
                } else {
                    $row['log_data_processed'][] = $dother;
                }
            }
        } else {
            $row['log_data_show'] = null;
            $row['log_data_other'] = [];
        }
        $array_logs[$row['id']] = $row;
    }
}

$page_title = $nv_Lang->getModule('leads_log');

// Tất cả quản trị trong danh sách
$_sql = 'SELECT tb1.userid, tb1.first_name, tb1.last_name, tb1.username FROM ' . NV_USERS_GLOBALTABLE . ' tb1 INNER JOIN ' . $db_config['prefix'] . '_authors tb2 ON tb1.userid = tb2.admin_id WHERE tb1.userid IN (SELECT `admin_id` FROM ' . NV_AUTHORS_GLOBALTABLE . ' ORDER BY lev ASC)';
$array_admin_listall = $nv_Cache->db($_sql, 'userid', 'users');
$userOptionsList[] = [
    'key' => 0,
    'name' => $nv_Lang->getModule('system'),
    'selected' => ($array_search['admin_id'] === 0) ? 'selected' : ''
];
foreach ($array_admin_listall as $key => $user) {
    $fullname = nv_show_name_user($user['first_name'], $user['last_name'], $user['username']);
    $userOptionsList[] = [
        'key' => $key,
        'name' => $fullname . ' (' . $user['username'] . ')',
        'selected' => ($key == $array_search['admin_id']) ? 'selected' : ''
    ];
}

// Danh sách phân loại chia log
foreach ($user_action_logs as $key => $value) {
    $userActionLogs[] = [
        'key' => $key,
        'name' => $value,
        'selected' => ($key == $array_search['log_type']) ? 'selected' : ''
    ];
}

// Lấy danh sách chia đơn trong tháng
$_sql = 'SELECT userid, config FROM ' . NV_PREFIXLANG . '_crmbidding_groups_users ORDER BY weight ASC';
$_query = $db->query($_sql);
$array_user_set_order = $array_user_share_messenger_zalo = [];
while ($_row = $_query->fetch()) {
    $_row['config'] = json_decode($_row['config'], true);
    if (isset($_row['config']['set_order']) and $_row['config']['set_order'] == 1) {
        $array_user_set_order[$_row['userid']] = $_row;
    }
    if (isset($_row['config']['share_messenger_zalo']) and $_row['config']['share_messenger_zalo'] == 1) {
        $array_user_share_messenger_zalo[$_row['userid']] = $_row;
    }
}

// Danh sách đã chia đơn các gói vip
$array_orders_vip = $array_orders_vieweb = $apipro_to_sale_tmp = $array_order_mess = $array_order_zl = $array_transation_wallet = $array_order_dtnet = $array_user_set_order;

if (!empty($module_config[$module_name]['sale_order_to'])) {
    $sale_order_to = explode(',', $module_config[$module_name]['sale_order_to']);
    $sale_order_data = processSaleOrder($sale_order_to, $array_orders_vip, $array_user_set_order);
    $sale_orders_vip = $sale_order_data['sale_orders'];
    $next_order_user = $sale_order_data['next_user'];
}

// Danh sách đã chia đơn vieweb
if (!empty($module_config[$module_name]['sale_order_to_vieweb'])) {
    $sale_vieweb_to = explode(',', $module_config[$module_name]['sale_order_to_vieweb']);
    $sales_order_vieweb_data = processSaleOrder($sale_vieweb_to, $array_orders_vieweb, $array_user_set_order);
    $sales_orders_vieweb = $sales_order_vieweb_data['sale_orders'];
    $next_user_vieweb = $sales_order_vieweb_data['next_user'];
}

// Danh sách đã chia đơn messenger
if (!empty($module_config[$module_name]['leads_messenger_to_sale'])) {
    $sale_messenger_to = explode(',', $module_config[$module_name]['leads_messenger_to_sale']);
    $sales_order_messenger_data = processSaleOrder($sale_messenger_to, $array_order_mess, $array_user_share_messenger_zalo);
    $sales_orders_messenger = $sales_order_messenger_data['sale_orders'];
    $next_user_messenger = $sales_order_messenger_data['next_user'];
}

// Danh sách đã chia đơn zalo
if (!empty($module_config[$module_name]['leads_zalo_to_sale'])) {
    $sale_zalo_to = explode(',', $module_config[$module_name]['leads_zalo_to_sale']);
    $sales_order_zalo_data = processSaleOrder($sale_zalo_to, $array_order_zl, $array_user_share_messenger_zalo);
    $sales_orders_zalo = $sales_order_zalo_data['sale_orders'];
    $next_user_zalo = $sales_order_zalo_data['next_user'];
}

// Danh sách đã chia đơn apipro
if (!empty($module_config[$module_name]['api_pro_to_sale'])) {
    $api_pro_to_sale = explode(',', $module_config[$module_name]['api_pro_to_sale']);
    $sales_order_apipro_data = processSaleOrder($api_pro_to_sale, $apipro_to_sale_tmp, $array_user_set_order);
    $sales_orders_apipro = $sales_order_apipro_data['sale_orders'];
    $next_user_apipro = $sales_order_apipro_data['next_user'];
}

// Danh sách đã chia đơn nạp tiền
if (!empty($module_config[$module_name]['transation_wallet_to_sale'])) {
    $sale_transation_wallet = explode(',', $module_config[$module_name]['transation_wallet_to_sale']);
    $sale_transation_data = processSaleOrder($sale_transation_wallet, $array_transation_wallet, $array_user_set_order);
    $arr_sale_wallet = $sale_transation_data['sale_orders'];
    $next_transation_user = $sale_transation_data['next_user'];
}

// Danh sách đã chia đơn dauthau.net
if (!empty($module_config[$module_name]['sale_order_dtnet_to'])) {
    $sale_order_dtnet_to = explode(',', $module_config[$module_name]['sale_order_dtnet_to']);
    $sale_dtnet_data = processSaleOrder($sale_order_dtnet_to, $array_order_dtnet, $array_user_set_order);
    $arr_sale_dtnet = $sale_dtnet_data['sale_orders'];
    $next_dtnet_user = $sale_dtnet_data['next_user'];
}

// Xử lý giao diện
$tpl = new \NukeViet\Template\NVSmarty();
$tpl->setTemplateDir(NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$tpl->assign('LANG', $nv_Lang);
$tpl->assign('ARRAY_USERS', $userOptionsList);
$tpl->assign('ARRAY_USER_ACTION', $userActionLogs);
$tpl->assign('ARRAY_LOG', $array_logs);
$tpl->assign('PAGINATION', nv_generate_page($base_url, $total, $per_page, $page));
$tpl->assign('SEARCH', $array_search);
$tpl->assign('MODULE_NAME', $module_name);
$tpl->assign('OP', $op);

// Hiển thị tất cả các danh sách
if ($array_search['log_type'] == 0) {
    // Chia đơn VIP
    $tpl->assign('ARRAY_SALE_ORDERS', $sale_orders_vip);
    $tpl->assign('SALE_VIP_NEXT', $next_order_user);
    // Chia đơn VIEWEB
    $tpl->assign('ARRAY_SALE_VIEWB', $sales_orders_vieweb);
    $tpl->assign('SALE_VIEWEB_NEXT', $next_user_vieweb);
    // Chia đơn MESS
    $tpl->assign('ARRAY_SALE_MESS', $sales_orders_messenger);
    $tpl->assign('SALE_MESS_NEXT', $next_user_messenger);

    // Chia đơn zalo
    $tpl->assign('ARRAY_SALE_ZALO', $sales_orders_zalo);
    $tpl->assign('SALE_ZALO_NEXT', $next_user_zalo);

    // Chia đơn APIPRO
    $tpl->assign('ARRAY_SALE_APIPRO', $sales_orders_apipro);
    $tpl->assign('SALE_APIPRO_NEXT', $next_user_apipro);
    // Chia đơn WALLET
    $tpl->assign('ARRAY_SALE_WALLET', $arr_sale_wallet);
    $tpl->assign('SALE_WALLET_NEXT', $next_transation_user);
    // Chia đơn DT-NET
    $tpl->assign('ARRAY_SALE_DTNET', $arr_sale_dtnet);
    $tpl->assign('SALE_DTNET_NEXT', $next_dtnet_user);
} else {
    switch ($array_search['log_type']) {
        case 2:
            $tpl->assign('ARRAY_SALE_MESS', $sales_orders_messenger);
            $tpl->assign('SALE_MESS_NEXT', $next_user_messenger);
            break;
        case 3:
            $tpl->assign('ARRAY_SALE_ORDERS', $sale_orders_vip);
            $tpl->assign('SALE_VIP_NEXT', $next_order_user);
            break;
        case 4:
            $tpl->assign('ARRAY_SALE_APIPRO', $sales_orders_apipro);
            $tpl->assign('SALE_APIPRO_NEXT', $next_user_apipro);
            break;
        case 5:
            $tpl->assign('ARRAY_SALE_VIEWB', $sales_orders_vieweb);
            $tpl->assign('SALE_VIEWEB_NEXT', $next_user_vieweb);
            break;
        case 6:
            $tpl->assign('ARRAY_SALE_WALLET', $arr_sale_wallet);
            $tpl->assign('SALE_WALLET_NEXT', $next_transation_user);
            break;
        case 7:
            $tpl->assign('ARRAY_SALE_DTNET', $arr_sale_dtnet);
            $tpl->assign('SALE_DTNET_NEXT', $next_dtnet_user);
            break;
        case 8:
            $tpl->assign('ARRAY_SALE_ZALO', $sales_orders_zalo);
            $tpl->assign('SALE_ZALO_NEXT', $next_user_zalo);
            break;
        default:
            break;
    }
}

$tpl->assign('ERROR', $error);

$contents = $tpl->fetch('leads_log.tpl');
include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
