<?php

/**
 * NukeViet Content Management System
 * @version 5.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2025 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_MAINFILE')) {
    exit('Stop!!!');
}

/**
 * Nguyên tắc:
 * Khi ở tên miền id.dauthau.net nếu link bắt đầu bằng /vi|en/users/ thì nối vào iportal.vn
 * Khi ở tên miền iportal.vn nếu link không bắt đầu bằng /vi|en/users/ thì nối vào tùy module:
 * - calendar, redday => lich.dauthau.net
 * - supportticket => support.dauthau.net
 * - Các module khác thì chạy theo tên miền id.dauthau.net
 * Case tùy module này bỏ. Để mỗi id.dauthau.net rồi nó tự chuyển hướng sau vì iportal.vn có đi link ngoài là thấp
 */
nv_add_hook($module_name, 'get_rewrite_domain', $priority, function ($vars) {
    global $global_config;

    $op_rewrite_count = $vars[0] ?? null;
    $op_rewrite = $vars[1] ?? null;
    //$rewrite_end = $vars[2] ?? null;
    //$query_array = $vars[3] ?? null;
    //$is_amp = $vars[4] ?? null;
    $is_acp = $vars[5] ?? null;

    if (is_null($op_rewrite_count)) {
        return '';
    }
    $isModUser = (
        !$is_acp && !empty($op_rewrite[1]) && in_array($op_rewrite[1], ['users', 'two-step-verification'], true) &&
        in_array($op_rewrite[0], $global_config['allow_sitelangs'], true)
    );
    if ($isModUser and isset($op_rewrite[2]) and (str_starts_with($op_rewrite[2], 'avatar/') or $op_rewrite[2] == 'avatar')) {
        // Link avatar thì không fix cứng domain
        return '';
    }

    $currentDomainIsUser = in_array(NV_SERVER_NAME, [
        'iportal.vn',
        'test.iportal.vn',
        'portal.dauthau.vinades.my',
        'portal.dauthau.local'
    ], true);
    $currentModule = $op_rewrite[1] ?? '';

    if (defined('IS_DEV_LOCAL')) {
        // Trường hợp local
        if (NV_SERVER_PORT == '') {
            // Xampp
            $domain_user = 'https://portal.dauthau.vinades.my';
            $domain_other = 'https://id.dauthau.vinades.my';
        } else {
            // Docker
            $domain_user = 'https://portal.dauthau.local:8443';
            $domain_other = 'https://id.dauthau.local:8443';
        }
    } elseif (in_array(NV_SERVER_NAME, ['test.iportal.vn', 'idtest.dauthau.net'], true)) {
        // Site test
        $domain_user = 'https://test.iportal.vn';
        $domain_other = 'https://idtest.dauthau.net';
    } else {
        // Site chính
        $domain_user = 'https://iportal.vn';
        $domain_other = 'https://id.dauthau.net';
    }

    if (!$currentDomainIsUser and $isModUser) {
        return $domain_user;
    }
    if ($currentDomainIsUser and !$isModUser) {
        return $domain_other;
    }
    return '';
});
