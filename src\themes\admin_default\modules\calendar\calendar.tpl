<!-- BEGIN: main -->
<link rel="stylesheet" href="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/select2/select2.min.css">
<script type="text/javascript" src="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/clipboard/clipboard.min.js"></script>
<!-- BEGIN: add_btn -->
<div class="form-group">
    <a href="#" data-toggle="add" class="btn btn-success btn-sm"><i class="fa fa-plus"></i> {LANG.calendar_add}</a>
</div>
<script type="text/javascript">
    $(document).ready(function() {
        $('[data-toggle="add"]').on('click', function(e) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: $('#form-holder').offset().top
            }, 200, function() {
                $('[name="name_calendar"]').focus();
            });
        });
    });
</script>
<!-- END: add_btn -->
<div class="table-responsive">
    <table class="table table-striped table-bordered table-hover">
        <colgroup>
            <col class="w100">
        </colgroup>
        <thead>
        <tr>
            <th style="width: 10%" class="text-nowrap">{LANG.order}</th>
            <th style="width: 35%" class="text-nowrap">{LANG.name_calendar}</th>
            <th style="width: 15%" class="text-center text-nowrap">{LANG.type_calendar}</th>
            <th style="width: 15%" class="text-center text-nowrap">{LANG.status}</th>
            <th style="width: 25%" class="text-center text-nowrap">{LANG.function}</th>
        </tr>
        </thead>
        <tbody>
        <!-- BEGIN: loop -->
        <tr>
            <td class="text-center">
                <a href="javascript:void(0);" title="{LANG.order_weight_number}: {ROW.weight}" id="change_weight" onclick="nv_sort_calendar({ROW.id}, {ROW.weight})" class="btn btn-default btn-block">{ROW.weight}</a>
            </td>
            <td>
                <strong>{ROW.name_calendar}</strong>
                <div><small class="text-muted">{ROW.description}</small></div>
            </td>
            <td class="text-center">
                {ROW.is_calendar}
            </td>
            <td class="text-center">
                <input name="status" id="change_status{ROW.id}" value="1" type="checkbox"{ROW.status_render} onclick="nv_change_calendar_status('{ROW.id}', '{NV_CHECK_SESSION}');">
            </td>
            <td class="text-center text-nowrap">
                <a class="btn btn-sm btn-default" href="{ROW.url_edit}"><i class="fa fa-edit"></i> {GLANG.edit}</a>
                <a class="btn btn-sm btn-danger" href="javascript:void(0);" onclick="nv_delele_calendar('{ROW.id}', '{NV_CHECK_SESSION}');"><i class="fa fa-trash"></i> {GLANG.delete}</a>
            </td>
        </tr>
        <!-- END: loop -->
        </tbody>
    </table>
</div>

<div id="form-holder"></div>
<!-- BEGIN: error -->
<div class="alert alert-danger">{ERROR}</div>
<!-- END: error -->

<h2><i class="fa fa-th-large" aria-hidden="true"></i> {CAPTION}</h2>
<div class="panel panel-default">
    <div class="panel-body">
        <form method="post" action="{FORM_ACTION}" class="form-horizontal">
            <div class="form-group">
                <label class="col-sm-6 control-label" for="element_title">{LANG.name_calendar} <span class="fa-required text-danger">(<em class="fa fa-asterisk"></em>)</span>:</label>
                <div class="col-sm-18 col-lg-10">
                    <input type="text" id="element_name_calendar" name="name_calendar" value="{DATA.name_calendar}" class="form-control">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-6 control-label" for="element_description">{LANG.description}:</label>
                <div class="col-sm-18 col-lg-10">
                    <textarea class="form-control" rows="3" id="element_description" name="description">{DATA.description}</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-6 control-label" for="element_type">{LANG.type_calendar}:</label>
                <div class="col-sm-18 col-lg-10">
                    <select name="is_calendar">
                        <option value="0">-- {LANG.all} --</option>
                    <!-- BEGIN: is_calendar -->
                        <option value="{TYPE.key}"{TYPE.selected}>{TYPE.value}</option>
                    <!-- END: is_calendar -->
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-6 control-label" for="element_color">{LANG.color}:</label>
                <div class="col-sm-18 col-lg-10">
                    <input type="color" id="element_color" name="color" value="{DATA.color}">
                </div>
            </div>
            <div class="row">
                <div class="col-sm-18 col-sm-offset-6">
                    <input type="hidden" name="save" value="{NV_CHECK_SESSION}">
                    <button type="submit" class="btn btn-primary">{GLANG.submit}</button>
                </div>
            </div>
        </form>
    </div>
</div>
<div id="order_weight" title="{LANG.change_weight}">
    <strong id="order_weight_title"></strong>
    <form method="post" class="form-horizontal" id="form-weight">
        <input type="hidden" name="order_weight_id" value="0" id="order_weight_id"/>
        <div class="form-group">
            <label for="order_weight_number" class="col-sm-12 control-label">{LANG.order_weight_number}</label>
            <div class="col-sm-12">
                <input type="number" class="form-control text-center w100" id="order_weight_number"  value="" readonly="readonly">
            </div>
        </div>
        <div class="form-group">
            <label for="order_weight_new" class="col-sm-12 control-label">{LANG.order_weight_new}</label>
            <div class="col-sm-12">
                <input type="number" class="form-control text-center w100" name="order_weight_new" id="order_weight_new"  value="" min="1">
            </div>
        </div>
        <div class="form-group text-center">
            <input type="hidden" name="changeweight" value="{NV_CHECK_SESSION}">
            <button type="button" class="btn btn-primary" id="button_change_weight" onclick="nv_change_weight_calendar('{ROW.id}', '{NV_CHECK_SESSION}')">{LANG.action}</button>
        </div>
    </form>
</div>
<!-- BEGIN: scroll -->
<script type="text/javascript">
    $(document).ready(function() {
        $(window).on('load', function() {
            $('html, body').animate({
                scrollTop: $('#form-holder').offset().top
            }, 200, function() {
                $('[name="title"]').focus();
            });
        });
    })
</script>
<!-- END: scroll -->
<script type="text/javascript">
    $(function () {
        $( "#order_weight" ).dialog({
            autoOpen: false,
            show: {
                effect: "blind",
                duration: 500
            },
            hide: {
                effect: "explode",
                duration: 500
            }
        });
    })
</script>
<script>
    $('select').select2();
</script>
<!-- END: main -->
