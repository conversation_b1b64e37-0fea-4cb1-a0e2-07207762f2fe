<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\elink\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use PDO;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetAffiliateIntroducer implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'affiliate';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $global_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        $userid = $nv_Request->get_absint('userid', 'post', 0);

        $sql = "SELECT tb2.userid, tb2.username, tb2.first_name, tb2.last_name, tb2.email, tb1.pri_uid 
        FROM " . $db_config['prefix'] . "_elink_affiliate_set tb1
        INNER JOIN " . NV_USERS_GLOBALTABLE . " tb2 ON tb1.pri_uid=tb2.userid
        WHERE tb1.pre_uid=:userid";
        $stmt = $db->prepare($sql);
        $stmt->bindParam(':userid', $userid, PDO::PARAM_INT);
        $stmt->execute();

        $this->result->set('data', $stmt->fetch() ?: []);
        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
