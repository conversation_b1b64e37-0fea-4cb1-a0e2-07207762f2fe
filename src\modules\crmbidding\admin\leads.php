<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

// Nếu truy cập nhiều hơn 10 lần trong 1 phút hoặc 30 lần trong 5 phút thì báo lỗi
if (!isset($_SESSION['visit_lead_count'])) {
    $_SESSION['visit_lead_count'] = [];
}

// Xóa các mục cũ hơn 5 phút khỏi bộ đếm
$_SESSION['visit_lead_count'] = array_filter($_SESSION['visit_lead_count'], function ($timestamp) {
    return (NV_CURRENTTIME - $timestamp) <= 300;
});

// L<PERSON><PERSON> thời gian truy cập mới nhất
$_SESSION['visit_lead_count'][] = NV_CURRENTTIME;

// Đếm số lần truy cập trong 1 phút và 5 phút
$last1MinuteCount = count(array_filter($_SESSION['visit_lead_count'], function ($timestamp) {
    return (NV_CURRENTTIME - $timestamp) <= 60;
}));
$last5MinuteCount = count($_SESSION['visit_lead_count']);

// Kiểm tra điều kiện giới hạn
if ($last1MinuteCount > 10 || $last5MinuteCount > 30) {
    $page_title = 'Bạn truy cập nhanh quá';
    $contents = ' Truy cập chậm lại nhé bạn, hoặc mở 1 cửa sổ chỗ này thôi';
    if ($nv_Request->isset_request('autoLoad', 'post')) {
        include NV_ROOTDIR . '/includes/header.php';
        echo $contents;
        include NV_ROOTDIR . '/includes/footer.php';
    } else {
        include NV_ROOTDIR . '/includes/header.php';
        echo nv_admin_theme($contents);
        include NV_ROOTDIR . '/includes/footer.php';
    }
}


$showheader = $nv_Request->get_int('showheader', 'post,get', 1);
// Các biến tìm kiếm
$q = $nv_Request->get_title('q', 'post,get');
$group_leads = $nv_Request->get_int('group_leads', 'post,get', 0);
$status = $nv_Request->get_title('status', 'post,get', -1);
$caregiver_id = $nv_Request->get_int('caregiver_id', 'post,get', 0);
$label_search = $nv_Request->get_int('label', 'post,get', 0);
$error = "";

// order parameters 1-asc 2-desc
$orderby_source_lead = $nv_Request->get_int('orderby_source_lead', 'post,get', 0);
$orderby_name = $nv_Request->get_int('orderby_name', 'post,get', 0);
$orderby_phone = $nv_Request->get_int('orderby_phone', 'post,get', 0);
$orderby_email = $nv_Request->get_int('orderby_email', 'post,get', 0);
$orderby_status = $nv_Request->get_int('orderby_status', 'post,get', 0);
$orderby_schedule = $nv_Request->get_int('orderby_schedule', 'post,get', 0);
$orderby_time = $nv_Request->get_int('orderby_time', 'post,get', 0);
$orderby_affc = $nv_Request->get_int('orderby_affc', 'post,get', 0);
$orderby_care = $nv_Request->get_int('orderby_care', 'post,get', 0);
$orderby_siteid = $nv_Request->get_int('orderby_siteid', 'post,get', 0);

$search_time_type = $nv_Request->get_int('search_time_type', 'post,get', 0);
$array_search = array();
$array_search['time_from'] = $nv_Request->get_title('time_from', 'post,get', '');
$array_search['time_to'] = $nv_Request->get_title('time_to', 'post,get', '');
$array_search['care_time'] = $nv_Request->get_int('care_time', 'post,get', '');
$array_search['siteid'] = $nv_Request->get_int('siteid', 'post,get', '-1');
$array_search['prefix_lang'] = $nv_Request->get_int('prefix_lang', 'post,get', '-1');

if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_from'], $m)) {
    $sfrom = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
} else {
    $sfrom = 0;
}
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_to'], $m)) {
    $sto = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
} else {
    $sto = 0;
}

$array_care_time = array(
    1 => '1 ngày',
    3 => '3 ngày',
    5 => '5 ngày',
    7 => '7 ngày',
    9 => '9 ngày',
    15 => '15 ngày'
);

// các user mà sale làm trưởng nhóm
$caregiver_id_leads = array();
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE userid = ' . $admin_info['userid'];
$result = $db->query($sql);
$admin_config = [];
$is_leader = 0;
$view_lead_messages = 0;

while ($row_groups_users = $result->fetch()) {
    $admin_config = json_decode($row_groups_users['config'], true);
    // Nếu có cấu hình có lead đó thì xem có đang bật cấu hình xem nguồn lead messages k?
    $view_lead_messages = $admin_config['sale_view_messages'] ?? 0;

    if ($row_groups_users['is_leader'] == 1) {
        $is_leader = 1;
        $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $row_groups_users['group_id'] . ' AND userid != ' . $admin_info['userid'];
        $_result = $db->query($_sql);
        while ($_row_groups_users = $_result->fetch()) {
            $caregiver_id_leads[$_row_groups_users['userid']] = $_row_groups_users['userid'];
        }
    }
}
$caregiver_id_leads[$admin_info['userid']] = $admin_info['userid'];

// Fetch Limit
$show_view = false;

// List
$where = [];
$keys_check = [];
$array_order = [];
$keys_check = [
    'source_leads',
    'label',
    'name',
    'phone',
    'sub_phone',
    'email',
    'sub_email',
    'address',
    'tax',
    'status',
    'affilacate_id',
    'caregiver_id',
    'timecreate',
    'updatetime',
    'active',
    'last_comment',
    'schedule',
    'siteid'
];

$ListAllLeads = [];
if (!$nv_Request->isset_request('id', 'post,get')) {
    $show_view = true;
    $perpage = 20;
    $page = $nv_Request->get_int('page', 'get', 1);

    if (!empty($q)) {
        $_q = $db->dblikeescape($q);
        $where['OR'][] = [
            'like' => [
                'name' => '%' . $_q . '%'
            ]
        ];
        $where['OR'][] = [
            'like' => [
                'phone' => '%' . $_q . '%'
            ]
        ];
        //chuẩn hóa số điện thoại
        if (phonecheck($_q)) {
            $_tmp_phone = $_q;
            if (preg_match('/(\d{9})$/', $_q, $m)) {
                $_tmp_phone = $m[0];
            }
            $_tmp_phone = preg_replace('/[^0-9]/', '', $_tmp_phone);
            $_tmp_phone = (int)$_tmp_phone;
            $where['OR'][] = [
                'like' => [
                    'phone' => '%' . $_tmp_phone . '%'
                ]
            ];
            $where['OR'][] = [
                '=' => [
                    'phone_search' => $_tmp_phone
                ]
            ];
            $where['OR'][] = [
                'FIND_IN_SET' => [
                    'sub_phone_search' => $_tmp_phone
                ]
            ];
        }
        $where['OR'][] = [
            'like' => [
                'email' => '%' . $_q . '%'
            ]
        ];
        $where['OR'][] = [
            'like' => [
                'tax' => '%' . $_q . '%'
            ]
        ];
        $where['OR'][] = [
            'FIND_IN_SET' => [
                'sub_phone' => $_q
            ]
        ];
        $where['OR'][] = [
            'FIND_IN_SET' => [
                'sub_email' => $_q
            ]
        ];

        //tìm lead theo username của thành viên
        $lead_user = get_user_by_username($q);
        if (!empty($lead_user)) {
            $where['OR'][] = [
                '=' => [
                    'user_id' => $lead_user['userid']
                ]
            ];
        }
    }

    if ($sto > 0 and $sfrom > 0) {
        if ($search_time_type == 1) {
            $where['AND'][] = [
                '>=' => [
                    'timecreate' => $sfrom
                ]
            ];

            $where['AND'][] = [
                '<=' => [
                    'timecreate' => $sto
                ]
            ];
        } else if ($search_time_type == 2) {
            $where['AND'][] = [
                '>=' => [
                    'schedule' => $sfrom
                ]
            ];

            $where['AND'][] = [
                '<=' => [
                    'schedule' => $sto
                ]
            ];
        } else if ($search_time_type == 3) {
            // Thống kê theo ghi chú mới nhất
            // Tìm các leads có ghi chú mới nhất trong khoảng thời gian
            $comment_sql = 'SELECT c.sourceid, MAX(c.timecreate) as latest_comment_time
                           FROM ' . NV_PREFIXLANG . '_' . $module_data . '_comment c
                           WHERE c.source = 1';

            // Thêm điều kiện lọc theo người chăm sóc nếu có
            if (defined('NV_IS_SPADMIN')) {
                if ($caregiver_id > 0) {
                    $comment_sql .= ' AND c.post_id = ' . $caregiver_id;
                } else if ($caregiver_id == '-2') {
                    $comment_sql .= ' AND c.post_id = 0';
                }
            } else {
                if ($caregiver_id > 0 and isset($caregiver_id_leads[$caregiver_id])) {
                    $comment_sql .= ' AND c.post_id = ' . $caregiver_id;
                } else {
                    $comment_sql .= ' AND c.post_id IN (' . implode(',', $caregiver_id_leads) . ')';
                }
            }

            $comment_sql .= ' GROUP BY c.sourceid
                             HAVING latest_comment_time >= ' . $sfrom . ' AND latest_comment_time <= ' . $sto;

            $comment_result = $db->query($comment_sql);
            $lead_ids = [];
            while ($row = $comment_result->fetch()) {
                $lead_ids[] = $row['sourceid'];
            }

            if (!empty($lead_ids)) {
                $where['AND'][] = [
                    'IN' => [
                        'id' => '(' . implode(',', $lead_ids) . ')'
                    ]
                ];
            } else {
                // Nếu không có ghi chú nào trong khoảng thời gian, trả về kết quả rỗng
                $where['AND'][] = [
                    '=' => [
                        'id' => 0
                    ]
                ];
            }
        } else {
            $where['AND'][] = [
                '>=' => [
                    'updatetime' => $sfrom
                ]
            ];

            $where['AND'][] = [
                '<=' => [
                    'updatetime' => $sto
                ]
            ];
        }
    }

    if ($array_search['care_time'] > 0) {
        $time = NV_CURRENTTIME - 86400 * $array_search['care_time'];
        $where['AND'][] = [
            '<=' => [
                'updatetime' => $time
            ]
        ];
    }

    if ($array_search['siteid'] != '-1') {
        $where['AND'][] = [
            '=' => [
                'siteid' => $array_search['siteid']
            ]
        ];
    }

    if ($array_search['prefix_lang'] != '-1') {
        $where['AND'][] = [
            '=' => [
                'prefix_lang' => $array_search['prefix_lang']
            ]
        ];
    }

    if ($group_leads > 0) {
        $where['AND'][] = [
            '=' => [
                'source_leads' => $group_leads
            ]
        ];
    }
    // xem thống kê, $group_leads = 0 khác với xem list Leads
    if ($nv_Request->isset_request('view_leads', 'post,get')) {
        $group_leads = $nv_Request->get_int('group_leads', 'post,get', -1);
        if ($group_leads != -1) {
            $where['AND'][] = [
                '=' => [
                    'source_leads' => $group_leads
                ]
            ];
        }
    }

    if ($status != -1) {
        $where['AND'][] = [
            '=' => [
                'status' => $status
            ]
        ];
    }

    if ($label_search > 0) {
        $where['AND'][] = [
            'FIND_IN_SET' => [
                'label' => $label_search
            ]
        ];
    }

    // Chỉ áp dụng bộ lọc caregiver_id nếu không phải thống kê theo ghi chú
    // Vì khi search_time_type = 3, đã lọc theo người chăm sóc trong query comment rồi
    if ($search_time_type != 3) {
        if (defined('NV_IS_SPADMIN')) { // điều hành chung dc xem toàn bộ, hoặc xem theo bộ lọc người chăm sóc
            if ($caregiver_id > 0) {
                $where['AND'][] = [
                    '=' => [
                        'caregiver_id' => $caregiver_id
                    ]
                ];
            } else if ($caregiver_id == '-2') {
                $where['AND'][] = [
                    '=' => [
                        'caregiver_id' => 0
                    ]
                ];
            }
        } else { // sale được cấp quản trị
            if (!isset($lead_new)) {
                if ($caregiver_id > 0 and isset($caregiver_id_leads[$caregiver_id])) {
                    // sale xem leads của thành viên nhóm mình theo tìm kiếm $caregiver_id
                    $where['AND'][] = [
                        '=' => [
                            'caregiver_id' => $caregiver_id
                        ]
                    ];
                } else {
                    // nếu k tìm kiếm $caregiver_id thì xem toàn bộ theo nhóm sale
                    if (isset($admin_config['view_leads_new']) and $admin_config['view_leads_new'] == 1) {
                        $caregiver_id_leads[] = 0;
                        // cho xem các leads chưa có người chăm sóc caregiver_id =0
                    }
                    // Nếu bật cấu hình xem nguồn leads messages thì cho phép xem
                    if ($view_lead_messages) {
                        if ($group_leads == 0 or $group_leads == 4) {
                            $where['AND_OR'][] = [
                                '=' => [
                                    'source_leads' => 4
                                ]
                            ];
                        }

                        $where['AND_OR'][] = [
                            'IN' => [
                                'caregiver_id' => '(' . implode(',', $caregiver_id_leads) . ')'
                            ]
                        ];
                    } else {
                        $where['AND'][] = [
                            'IN' => [
                                'caregiver_id' => '(' . implode(',', $caregiver_id_leads) . ')'
                            ]
                        ];
                    }
                }
            }
        }
    }
    if (!$nv_Request->isset_request('view_leads', 'post,get')) {
        if (isset($lead_new)) {
            $where['AND'][] = [
                'IN' => [
                    'status' => '(4,0)'
                ]
            ];
            if (!defined('NV_IS_SPADMIN') and $data_config['view_leads'] == 2) {
                if ($is_leader == 1) {
                    // $db->limit($data_config['num_lead_leader_view']);
                    $perpage = $data_config['num_lead_leader_view'];
                } else {
                    // $db->limit($data_config['num_lead_sale_views']);
                    $perpage = $data_config['num_lead_sale_views'];
                }
            }
        } else {
            $where['AND'][] = [
                'NOT IN' => [
                    'status' => '(4,0)'
                ]
            ];
        }
    }

    // Order by danh sách leads
    if (isset($lead_new)) {
        // if (!defined('NV_IS_SPADMIN') and $data_config['view_leads'] == 2) {
        // if ($is_leader == 1) {
        // $num_items = $data_config['num_lead_leader_view'];
        // // $num_items = $data_config['num_lead_leader_view'];
        // } else {
        // $num_items = $data_config['num_lead_sale_views'];
        // // $num_items = $data_config['num_lead_sale_views'];
        // }
        // }
        // $order = 'status DESC, updatetime DESC';
        $array_order['status'] = 'DESC';
        $array_order['activity_time'] = 'DESC';
        $array_order['updatetime'] = 'DESC';
    } else {
        // $order = 'updatetime DESC, status ASC';
        $array_order['status'] = 'ASC';
        $array_order['activity_time'] = 'DESC';
        $array_order['updatetime'] = 'DESC';
    }

    if ($orderby_source_lead > 0) {
        $array_order = [];
        // $order = $orderby_source_lead == 1 ? 'source_leads ASC' : 'source_leads DESC';
        if ($orderby_source_lead == 1) {
            $array_order['source_leads'] = 'ASC';
        } else {
            $array_order['source_leads'] = 'DESC';
        }
    }

    if ($orderby_name > 0) {
        $array_order = [];
        // $order = $orderby_name == 1 ? 'name ASC' : 'name DESC';
        if ($orderby_name == 1) {
            $array_order['name'] = 'ASC';
        } else {
            $array_order['name'] = 'DESC';
        }
    }

    if ($orderby_phone > 0) {
        $array_order = [];
        // $order = $orderby_phone == 1 ? 'phone ASC' : 'phone DESC';
        if ($orderby_phone == 1) {
            $array_order['phone'] = 'ASC';
        } else {
            $array_order['phone'] = 'DESC';
        }
    }

    if ($orderby_email > 0) {
        $array_order = [];
        // $order = $orderby_email == 1 ? 'email ASC' : 'email DESC';
        if ($orderby_email == 1) {
            $array_order['email'] = 'ASC';
        } else {
            $array_order['email'] = 'DESC';
        }
    }

    if ($orderby_status > 0) {
        $array_order = [];
        // $order = $orderby_status == 1 ? 'status ASC' : 'status DESC';
        if ($orderby_status == 1) {
            $array_order['status'] = 'ASC';
        } else {
            $array_order['status'] = 'DESC';
        }
    }

    if ($orderby_schedule > 0) {
        $array_order = [];
        if ($orderby_schedule == 1) {
            $array_order['schedule'] = 'ASC';
        } else {
            $array_order['schedule'] = 'DESC';
        }
    }

    if ($orderby_time > 0) {
        $array_order = [];
        // $order = $orderby_time == 1 ? 'updatetime ASC' : 'updatetime DESC';
        if ($orderby_time == 1) {
            $array_order['updatetime'] = 'ASC';
        } else {
            $array_order['updatetime'] = 'DESC';
        }
    }

    if ($orderby_affc > 0) {
        $array_order = [];
        $order = $orderby_affc == 1 ? 'affilacate_id ASC' : 'affilacate_id DESC';
        if ($orderby_affc == 1) {
            $array_order['affilacate_id'] = 'ASC';
        } else {
            $array_order['affilacate_id'] = 'DESC';
        }
    }

    if ($orderby_care > 0) {
        $array_order = [];
        if ($orderby_care == 1) {
            $array_order['caregiver_id'] = 'ASC';
        } else {
            $array_order['caregiver_id'] = 'DESC';
        }
    }
    if ($orderby_siteid > 0) {
        $array_order = [];
        if ($orderby_siteid == 1) {
            $array_order['siteid'] = 'ASC';
        } else {
            $array_order['siteid'] = 'DESC';
        }
    }

    // Hiển thị danh sách leads
    $params = [
        'page' => $page,
        'perpage' => $perpage,
        'order' => $array_order
    ];

    // Nếu có điều kiện where thì gán
    if (!empty($where)) {
        $params['where'] = $where;
    }

    // GỌI API
    $List = nv_local_api('ListAllLeads', $params, $admin_info['username'], $module_name);
    $ListAllLeads = json_decode($List, true);
    if (!defined('NV_IS_SPADMIN') and $data_config['view_leads'] == 2 and isset($lead_new)) {
        if ($is_leader == 0) {
            $ListAllLeads['total'] = $data_config['num_lead_sale_views'];
        } else {
            $ListAllLeads['total'] = $data_config['num_lead_leader_view'];
        }
    }
}

if ($nv_Request->isset_request('delete_id', 'get') and $nv_Request->isset_request('delete_checkss', 'get')) {
    $id = $nv_Request->get_int('delete_id', 'get');
    $delete_checkss = $nv_Request->get_string('delete_checkss', 'get');

    if ($id > 0 and $delete_checkss == md5($id . NV_CACHE_PREFIX . $client_info['session_id'])) {
        $params = [
            'leadsid' => $id,
            'admin_id' => $admin_info['admin_id']
        ];

        $result_update = nv_local_api('DeleteLeads', $params, $admin_info['username'], $module_name);
        $result_update = json_decode($result_update, true);
        if ($result_update['code'] == "0000") {
            nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&showheader=' . $showheader);
        } else {
            $error = $result_update['message'];
        }
    } else {
        $error = $nv_Lang->getModule('error_delete_leads');
    }
}

$row = [];
$error = [];

$xtpl = new XTemplate('leads.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('ROW', $row);
$xtpl->assign('Q', $q);
$xtpl->assign('page', $page);

$xtpl->assign('NOT_CAREGIVERID', $caregiver_id == '-2' ? 'selected="selected"' : '');

$xtpl->assign('LINK_ADD', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=add');
$xtpl->assign('LINK_DUPLICATE', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=duplicate&type=1');
$xtpl->assign('LINK_UNDO_MERGE', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=undo_merge&type=1');

$show_advance_search = 'hidden';

if ($sfrom > 0 && $sto > 0) {
    $show_advance_search = '';
    $xtpl->assign('TIME_FROM', $array_search['time_from']);
    $xtpl->assign('TIME_TO', $array_search['time_to']);
}
if ($array_search['care_time'] > 0) {
    $show_advance_search = '';
}

$xtpl->assign('time_update_selected', $search_time_type == 0 ? 'selected' : '');
$xtpl->assign('time_create_selected', $search_time_type == 1 ? 'selected' : '');
$xtpl->assign('shedule_selected', $search_time_type == 2 ? 'selected' : '');
$xtpl->assign('comment_selected', $search_time_type == 3 ? 'selected' : '');

$xtpl->assign('ADVANDE_HIDDEN', $show_advance_search);

if ($show_advance_search == '') {
    $xtpl->assign('HIDDEN_BTN', 'hidden');
} else {
    $xtpl->assign('HIDDEN_BTN', '');
}
// Hiển thị danh sách leads
if ($show_view) {
    $listTable = show_leads();
    $xtpl->assign('TABLE', $listTable);
}

foreach ($array_label as $value) {
    $value['selected'] = $value['id'] == $label_search ? 'selected="selected"' : '';
    $xtpl->assign('LABEL', $value);
    $xtpl->parse('main.search.label');
}

foreach ($array_groups_leads as $value) {
    $value['selected'] = $value['id'] == $group_leads ? 'selected="selected"' : '';
    $xtpl->assign('GROUPS_LEADS', $value);
    $xtpl->parse('main.search.group_leads');
}

if (defined('NV_IS_SPADMIN')) {
    foreach ($array_user_id_users as $value) {
        $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
        $value['selected'] = $value['userid'] == $caregiver_id ? 'selected="selected"' : '';
        $value['title'] = $value['username'] . ' (' . $fullname . ')';
        $xtpl->assign('CAREGIVER_ID', $value);
        $xtpl->parse('main.search.caregiver_id');
    }
} else {
    foreach ($array_user_id_users as $value) {
        $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
        $value['selected'] = $value['userid'] == $caregiver_id ? 'selected="selected"' : '';
        $value['title'] = $value['username'] . ' (' . $fullname . ')';
        $xtpl->assign('CAREGIVER_ID', $value);
        if (isset($caregiver_id_leads[$value['userid']])) {
            $xtpl->parse('main.search.caregiver_id');
        }
    }
}

if (isset($lead_new)) {
    unset($array_status[1]);
    unset($array_status[2]);
    unset($array_status[3]);
} else {
    unset($array_status[0]);
    unset($array_status[4]);
}
foreach ($array_status as $key => $value) {
    $xtpl->assign('STATUS', array(
        'id' => $key,
        'title' => $value,
        'selected' => $key == $status ? 'selected="selected"' : ''
    ));
    $xtpl->parse('main.search.status');
}

foreach ($array_care_time as $key => $value) {
    $xtpl->assign('CARE_TIME', array(
        'id' => $key,
        'title' => $value,
        'selected' => $key == $array_search['care_time'] ? 'selected="selected"' : ''
    ));
    $xtpl->parse('main.search.care_time');
}
foreach ($array_site as $key => $value) {
    $xtpl->assign('OPTION', [
        'key' => $key,
        'title' => $value,
        'selected' => ($key == $array_search['siteid']) ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.search.select_siteid');
}

foreach ($array_lang as $key => $value) {
    $xtpl->assign('OPTION', [
        'key' => $key,
        'title' => $value,
        'selected' => ($key == $array_search['prefix_lang']) ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.search.select_prefix_lang');
}
if ($showheader) {
    $xtpl->parse('main.search');
}

if (!empty($error)) {
    $xtpl->assign('ERROR', $error);
    $xtpl->parse('main.error');
}

if (!empty($success)) {
    $xtpl->assign('SUCCESS', $success);
    $xtpl->parse('main.success');
}

if (!empty($lead_new)) {
    $xtpl->assign('UPDATE_TIME_NEW', $time_update_new);
    $xtpl->parse('main.show_scr_lead_new');
    $xtpl->parse('main.show_label_lead_new');
    $xtpl->parse('main.show_label_lead_new1');
}

if (!$lead_new_ajax) {
    $xtpl->parse('main.save_time_new');
}

if ($lead_new_ajax) {
    return $listTable;
} else {
    $xtpl->parse('main');
    $contents = $xtpl->text('main');
}

$page_title = $nv_Lang->getModule('leads');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';

function show_leads()
{
    global $nv_Request, $module_name, $ListAllLeads, $showheader, $group_leads, $caregiver_id, $label_search, $array_user_id_users, $array_groups_leads, $client_info, $op, $array_status, $status, $q, $sfrom, $sto, $array_search, $search_time_type, $orderby_source_lead, $orderby_name, $orderby_phone, $orderby_email, $orderby_status, $orderby_schedule, $orderby_time, $orderby_affc, $orderby_care, $orderby_siteid, $array_label, $op, $page, $error, $array_site, $nv_Lang, $time_update_new, $lead_new_ajax, $module_file, $global_config;

    $xtpl = new XTemplate('leads.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&showheader=' . $showheader;
    if (!empty($q)) {
        $base_url .= '&q=' . $q;
    }

    if (!empty($group_leads)) {
        $base_url .= '&group_leads=' . $group_leads;
    }

    if ($status != -1) {
        $base_url .= '&status=' . $status;
    }

    if (!empty($caregiver_id)) {
        $base_url .= '&caregiver_id=' . $caregiver_id;
    }

    if (!empty($label_search)) {
        $base_url .= '&label=' . $label_search;
    }
    if ($array_search['prefix_lang'] != -1) {
        $base_url .= '&prefix_lang=' . $array_search['prefix_lang'];
    }
    if ($sfrom > 0 && $sto > 0) {
        $base_url .= '&time_from=' . $array_search['time_from'] . '&time_to=' . $array_search['time_to'] . '&search_time_type=' . $search_time_type;
    }
    if ($array_search['care_time'] > 0) {
        $base_url .= '&care_time=' . $array_search['care_time'];
    }

    if ($nv_Request->isset_request('view_leads', 'post,get')) {
        $base_url .= '&view_leads=1';
    }

    $link_orderby_source_lead = $base_url . '&orderby_source_lead=1';
    $link_orderby_name = $base_url . '&orderby_name=1';
    $link_orderby_phone = $base_url . '&orderby_phone=1';
    $link_orderby_email = $base_url . '&orderby_email=1';
    $link_orderby_status = $base_url . '&orderby_status=1';
    $link_orderby_schedule = $base_url . '&orderby_schedule=1';
    $link_orderby_time = $base_url . '&orderby_time=1';
    $link_orderby_affc = $base_url . '&orderby_affc=1';
    $link_orderby_care = $base_url . '&orderby_care=1';
    $link_orderby_siteid = $base_url . '&orderby_siteid=1';

    if ($orderby_source_lead > 0) {
        if ($orderby_source_lead == 1) {
            $link_orderby_source_lead_desc = $base_url . '&orderby_source_lead=2' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_SOURCE_LEAD_DESC', $link_orderby_source_lead_desc);
            $xtpl->assign('ORDER_BY_SOURCE_LEAD', $link_orderby_source_lead_desc);
            $xtpl->parse('table_lead_new.source_lead.desc');
        } else {
            $link_orderby_source_lead_asc = $base_url . '&orderby_source_lead=1' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_SOURCE_LEAD_ASC', $link_orderby_source_lead_asc);
            $xtpl->assign('ORDER_BY_SOURCE_LEAD', $link_orderby_source_lead_asc);
            $xtpl->parse('table_lead_new.source_lead.asc');
        }
        $xtpl->parse('table_lead_new.source_lead');
        $base_url .= '&orderby_source_lead=' . $orderby_source_lead;
    } elseif (isset($ListAllLeads['data'])) {
        $xtpl->assign('ORDER_BY_SOURCE_LEAD', $link_orderby_source_lead . '&page=' . $ListAllLeads['page']);
    }

    if ($orderby_source_lead > 0) {
        if ($orderby_siteid == 1) {
            $link_orderby_siteid_desc = $base_url . '&orderby_siteid=2' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_SITEID_DESC', $link_orderby_siteid_desc);
            $xtpl->assign('ORDER_BY_SITEID', $link_orderby_siteid_desc);
            $xtpl->parse('table_lead_new.siteid.desc');
        } else {
            $link_orderby_siteid_asc = $base_url . '&orderby_siteid=1' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_SITEID_ASC', $link_orderby_siteid_asc);
            $xtpl->assign('ORDER_BY_SITEID', $link_orderby_siteid_asc);
            $xtpl->parse('table_lead_new.siteid.asc');
        }
        $xtpl->parse('table_lead_new.source_lead');
        $base_url .= '&orderby_siteid=' . $orderby_siteid;
    } elseif (isset($ListAllLeads['data'])) {
        $xtpl->assign('ORDER_BY_SITEID', $link_orderby_siteid . '&page=' . $ListAllLeads['page']);
    }

    if ($orderby_name > 0) {
        if ($orderby_name == 1) {
            $link_orderby_name_desc = $base_url . '&orderby_name=2' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_NAME_DESC', $link_orderby_name_desc);
            $xtpl->assign('ORDER_BY_NAME', $link_orderby_name_desc);
            $xtpl->parse('table_lead_new.name.desc');
        } else {
            $link_orderby_name_asc = $base_url . '&orderby_name=1' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_NAME_ASC', $link_orderby_name_asc);
            $xtpl->assign('ORDER_BY_NAME', $link_orderby_name_asc);
            $xtpl->parse('table_lead_new.name.asc');
        }
        $xtpl->parse('table_lead_new.name');
        $base_url .= '&orderby_name=' . $orderby_name;
    } elseif (isset($ListAllLeads['data'])) {
        $xtpl->assign('ORDER_BY_NAME', $link_orderby_name . '&page=' . $ListAllLeads['page']);
    }

    if ($orderby_phone > 0) {
        if ($orderby_phone == 1) {
            $link_orderby_phone_desc = $base_url . '&orderby_phone=2' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_PHONE_DESC', $link_orderby_phone_desc);
            $xtpl->assign('ORDER_BY_PHONE', $link_orderby_phone_desc);
            $xtpl->parse('table_lead_new.phone.desc');
        } else {
            $link_orderby_phone_asc = $base_url . '&orderby_phone=1' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_PHONE_ASC', $link_orderby_phone_asc);
            $xtpl->assign('ORDER_BY_PHONE', $link_orderby_phone_asc);
            $xtpl->parse('table_lead_new.phone.asc');
        }
        $xtpl->parse('table_lead_new.phone');
        $base_url .= '&orderby_phone=' . $orderby_phone;
    } elseif (isset($ListAllLeads['data'])) {
        $xtpl->assign('ORDER_BY_PHONE', $link_orderby_phone . '&page=' . $ListAllLeads['page']);
    }

    // orderby_status
    if ($orderby_status == 1) {
        $link_orderby_status_desc = $base_url . '&orderby_status=2' . '&page=' . $ListAllLeads['page'];
        $xtpl->assign('ORDER_BY_STATUS_DESC', $link_orderby_status_desc);
        $xtpl->assign('ORDER_BY_STATUS', $link_orderby_status_desc);
        $xtpl->parse('table_lead_new.status.desc');
        $xtpl->parse('table_lead_new.status');
        $base_url .= '&orderby_status=' . $orderby_status;
    } elseif (isset($ListAllLeads['data'])) {
        $link_orderby_status_asc = $base_url . '&orderby_status=1' . '&page=' . $ListAllLeads['page'];
        $xtpl->assign('ORDER_BY_STATUS_ASC', $link_orderby_status_asc);
        $xtpl->assign('ORDER_BY_STATUS', $link_orderby_status_asc);
        $xtpl->parse('table_lead_new.status.asc');
        $xtpl->parse('table_lead_new.status');
        $base_url .= '&orderby_status=' . $orderby_status;
    }

    // orderby_schedule
    if ($orderby_schedule == 1) {
        $link_orderby_schedule_desc = $base_url . '&orderby_schedule=2' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_SCHEDULE_DESC', $link_orderby_schedule_desc);
        $xtpl->assign('ORDER_BY_SCHEDULE', $link_orderby_schedule_desc);
        $xtpl->parse('table_lead_new.schedule.desc');
    } elseif (isset($ListAllLeads['data'])) {
        $link_orderby_schedule_asc = $base_url . '&orderby_schedule=1' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_SCHEDULE_ASC', $link_orderby_schedule_asc);
        $xtpl->assign('ORDER_BY_SCHEDULE', $link_orderby_schedule_asc);
        $xtpl->parse('table_lead_new.schedule.asc');
    }
    $xtpl->parse('table_lead_new.schedule');
    $base_url .= '&orderby_schedule=' . $orderby_schedule;
    // pr($base_url);

    if ($orderby_email > 0) {
        if ($orderby_email == 1) {
            $link_orderby_email_desc = $base_url . '&orderby_email=2' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_EMAIL_DESC', $link_orderby_email_desc);
            $xtpl->assign('ORDER_BY_EMAIL', $link_orderby_email_desc);
            $xtpl->parse('table_lead_new.email.desc');
        } else {
            $link_orderby_email_asc = $base_url . '&orderby_email=1' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_EMAIL_ASC', $link_orderby_email_asc);
            $xtpl->assign('ORDER_BY_EMAIL', $link_orderby_email_asc);
            $xtpl->parse('table_lead_new.email.asc');
        }
        $xtpl->parse('table_lead_new.email');
        $base_url .= '&orderby_email=' . $orderby_email;
    } elseif (isset($ListAllLeads['data'])) {
        $xtpl->assign('ORDER_BY_EMAIL', $link_orderby_email . '&page=' . $ListAllLeads['page']);
    }

    if ($orderby_time == 1 or $orderby_time == 0) {
        if (isset($ListAllLeads['data'])) {
            $link_orderby_time_desc = $base_url . '&orderby_time=2' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_TIME_DESC', $link_orderby_time_desc);
            $xtpl->assign('ORDER_BY_TIME', $link_orderby_time_desc);
            $xtpl->parse('table_lead_new.updatetime.desc');
        }
    } elseif (isset($ListAllLeads['data'])) {
        $link_orderby_time_asc = $base_url . '&orderby_time=1' . '&page=' . $ListAllLeads['page'];
        $xtpl->assign('ORDER_BY_TIME_ASC', $link_orderby_time_asc);
        $xtpl->assign('ORDER_BY_TIME', $link_orderby_time_asc);
        $xtpl->parse('table_lead_new.updatetime.asc');
    }
    $xtpl->parse('table_lead_new.updatetime');
    $base_url .= '&orderby_time=' . $orderby_time;

    if ($orderby_affc > 0) {
        if ($orderby_affc == 1) {
            $link_orderby_affc_desc = $base_url . '&orderby_affc=2' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_AFFC_DESC', $link_orderby_affc_desc);
            $xtpl->assign('ORDER_BY_AFFC', $link_orderby_affc_desc);
            $xtpl->parse('table_lead_new.affilacate.desc');
        } else {
            $link_orderby_affc_asc = $base_url . '&orderby_affc=1' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_AFFC_ASC', $link_orderby_affc_asc);
            $xtpl->assign('ORDER_BY_AFFC', $link_orderby_affc_asc);
            $xtpl->parse('table_lead_new.affilacate.asc');
        }
        $xtpl->parse('table_lead_new.affilacate');
        $base_url .= '&orderby_affc=' . $orderby_affc;
    } else {
        $xtpl->assign('ORDER_BY_AFFC', $link_orderby_affc);
    }

    if ($orderby_care > 0) {
        if ($orderby_care == 1) {
            $link_orderby_care_desc = $base_url . '&orderby_care=2' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_CARE_DESC', $link_orderby_care_desc);
            $xtpl->assign('ORDER_BY_CARE', $link_orderby_care_desc);
            $xtpl->parse('table_lead_new.care.desc');
        } else {
            $link_orderby_care_asc = $base_url . '&orderby_care=1' . '&page=' . $ListAllLeads['page'];
            $xtpl->assign('ORDER_BY_CARE_ASC', $link_orderby_care_asc);
            $xtpl->assign('ORDER_BY_CARE', $link_orderby_care_asc);
            $xtpl->parse('table_lead_new.care.asc');
        }
        $xtpl->parse('table_lead_new.care');
        $base_url .= '&orderby_care=' . $orderby_care;
    } else {
        $xtpl->assign('ORDER_BY_CARE', $link_orderby_care);
    }

    if ($page > 20) {
        $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
        $contents = nv_theme_alert($nv_Lang->getModule('info'), $nv_Lang->getModule('note_max_searchpage') . $btn);
        include NV_ROOTDIR . '/includes/header.php';
        echo nv_admin_theme($contents);
        include NV_ROOTDIR . '/includes/footer.php';
    }

    if (isset($ListAllLeads['data'])) {
        $generate_page = nv_generate_page($base_url, $ListAllLeads['total'], $ListAllLeads['perpage'], $ListAllLeads['page']);
        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('table_lead_new.generate_page');
        }

        $number = $ListAllLeads['page'] > 1 ? ($ListAllLeads['perpage'] * ($ListAllLeads['page'] - 1)) + 1 : 1;
        foreach ($ListAllLeads['data'] as $key => $view) {
            // Ghi nhận lại thời gian khi bắt đầu load trang lead để đánh dấu xem cái nào lead mới mất khi dùng ajax
            if ($number == 1 and $lead_new_ajax == 0) {
                $time_update_new = $view['activity_time'];
            }

            // && ((NV_CURRENTTIME - $time_update_new) < 60)
            if ($lead_new_ajax and $view['activity_time'] > $time_update_new && ((NV_CURRENTTIME - $view['activity_time']) < 60)) {
                $view['class_lead_new'] = 'bg_leade_new';
            } else {
                $view['class_lead_new'] = '';
            }

            $view['number'] = $number++;
            $view['status'] = $array_status[$view['status']];
            if (!empty($view['label_arr'])) {
                foreach ($view['label_arr'] as $label) {
                    $xtpl->assign('LABEL', $label);
                    $xtpl->parse('table_lead_new.loop.label');
                }
            }
            $view['timecreate'] = $view['timecreate'] != 0 ? nv_date('H:i d/m/Y', $view['timecreate']) : '';
            $view['updatetime'] = $view['updatetime'] != 0 ? nv_date('H:i d/m/Y', $view['updatetime']) : '';
            $view['activity_time'] = $view['activity_time'] != 0 ? nv_date('H:i d/m/Y', $view['activity_time']) : '';

            if ($view['schedule'] != 0) {
                $xtpl->assign('SCHEDULE', nv_date('d/m/Y', $view['schedule']));
                $xtpl->parse('table_lead_new.loop.schedule');
            }
            $view['prefix_lang_letter'] = $view['prefix_lang'] == 1 ? '(' . $nv_Lang->getModule('lang_en') . ')' : '(' . $nv_Lang->getModule('lang_vi') . ')';
            if ($view['affilacate_id'] > 0) {
                $affilacate_user = get_user($view['affilacate_id']);
                $view['affilacate_id'] =  isset($affilacate_user['username']) ? nv_show_name_user($affilacate_user['first_name'], $affilacate_user['last_name'], $affilacate_user['username']) : '';
            } else {
                $view['affilacate_id'] = '';
            }

            $view['caregiver_id'] = isset($array_user_id_users[$view['caregiver_id']]['username']) ? nv_show_name_user($array_user_id_users[$view['caregiver_id']]['first_name'], $array_user_id_users[$view['caregiver_id']]['last_name'], $array_user_id_users[$view['caregiver_id']]['username']) : '';
            $view['source_leads'] = isset($array_groups_leads[$view['source_leads']]['title']) ? $array_groups_leads[$view['source_leads']]['title'] : '';
            $view['siteid'] = isset($array_site[$view['siteid']]) ? $array_site[$view['siteid']] : '';
            $view['link_edit'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $view['id'];
            $view['link_view'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $view['id'] . '&showheader=' . $showheader;
            $view['link_delete'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;delete_id=' . $view['id'] . '&amp;delete_checkss=' . md5($view['id'] . NV_CACHE_PREFIX . $client_info['session_id']);
            $xtpl->assign('VIEW', $view);
            $xtpl->parse('table_lead_new.loop');
        }

        $xtpl->parse('table_lead_new');
        $contents = $xtpl->text('table_lead_new');
        return $contents;
    } else {
        $error = $nv_Lang->getModule('no_result');
        return $error;
    }
}
