<!-- BEGIN: main -->
<!-- BEGIN: user -->
<div class="panel panel-default">
    <!-- <div class="panel-heading">{LANG.opportunities_info_user}</div> -->
    <div class="panel-body">
        <ul class="logotherlists">
            <li>{LANG.username}: <b>{USERS.username}</b></li>
            <li>{LANG.name}: <b>{USERS.title}</b></li>
            <li>{LANG.phone}: <b>{USERS.phone}</b></li>
            <li>{LANG.email}: <b>{USERS.email}</b></li>
            <li>{LANG.tax}: <b>{USERS.mst}</b></li>
            <li>{LANG.gender}: <b>{USERS.gender}</b></li>
            <li>{LANG.regdate}: <b>{USERS.regdate}</b></li>
        </ul>
    </div>
</div>
<!-- END: user -->
<!-- BEGIN: error -->
<div class="alert alert-warning">{ERROR}</div>
<!-- END: error -->
<!-- BEGIN: set -->
<div class="panel panel-default">
    <div class="panel-heading">{LANG.ticket_info_user}</div>
    <div class="panel-body">
        <form class="form-horizontal" action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
            <input type="hidden" name="id" value="{ID}" />
            <table class="table table-striped table-bordered table-hover">
                <thead>
                    <tr>
                        <th class="w50">{LANG.number}</th>
                        <th class="w150">{LANG.name}</th>
                        <th class="w100">{LANG.phone}</th>
                        <th>{LANG.email}</th>
                        <th class="w150">{LANG.type_duplicate}</th>
                        <th>&nbsp;</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop -->
                    <tr>
                        <td>{USERS.userid}</td>
                        <td>{USERS.title}</td>
                        <td>{USERS.phone}</td>
                        <td>{USERS.email}</td>
                        <td>{USERS.type}</td>
                        <td class="text-center">
                            <a href="#" class="btn btn-sm btn-primary" onclick="chosse_user({ID}, {USERS.userid})">{LANG.chosse}</a>
                        </td>
                    </tr>
                    <!-- END: loop -->
                    <!-- BEGIN: loop_email -->
                    <tr>
                        <td>{USERS_EMAIL.userid}</td>
                        <td>{USERS_EMAIL.title}</td>
                        <td>{USERS_EMAIL.phone}</td>
                        <td>{USERS_EMAIL.email}</td>
                        <td>{USERS_EMAIL.type}</td>
                        <td class="text-center">
                            <a href="#" class="btn btn-sm btn-primary" onclick="chosse_user({ID}, {USERS_EMAIL.userid})">{LANG.chosse}</a>
                        </td>
                    </tr>
                    <!-- END: loop_email -->
                </tbody>
            </table>
        </form>
    </div>
</div>
<div class="panel panel-default">
    <div class="panel-heading">{LANG.opportunities_info_user_add}</div>
    <div class="panel-body" id="user_add"></div>
</div>
<script type="text/javascript">
$(document).ready(function() {
        $.ajax({
          method: "POST",
          url: script_name + '?' + nv_name_variable + '=users&' + nv_fc_variable + '=user_add',
          data: { showheader: "0", initdata: "{INITDATA}", nv_redirect: "{URL_BACK}"}
        }).done(function( res ) {
            $('#user_add').html(res);
       });
});
</script>
<!-- END: set -->
<!-- END: main -->
