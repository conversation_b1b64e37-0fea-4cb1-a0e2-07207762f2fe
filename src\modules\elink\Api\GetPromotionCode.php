<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\elink\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetPromotionCode implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'promotion';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $global_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        $_promotion_code = $nv_Request->get_title('code', 'post', '');
        if (empty($_promotion_code) or !preg_match('/^([a-zA-Z0-9\-\_]+)$/', $_promotion_code)) {
            return $this->result->setCode('1001')->setMessage($nv_Lang->getModule('apierror_procode'))->getResult();
        }

        $sql = "SELECT tb1.*, tb2.username FROM " . $db_config['prefix'] . "_elink_promotion_code tb1, " . NV_USERS_GLOBALTABLE . " tb2
        WHERE tb1.userid=tb2.userid AND tb2.active=1 AND tb1.promo_code=" . $db->quote($_promotion_code);
        $_promo = $db->query($sql)->fetch();

        if (empty($_promo)) {
            return $this->result->setCode('1002')->setMessage($nv_Lang->getModule('apierror_procode_exists'))->getResult();
        }

        // Lấy số người đã sử dụng của mã khuyến mãi này
        $sql = "SELECT COUNT(DISTINCT use_userid) FROM " . $db_config['prefix'] . "_elink_promotion_code_use
        WHERE promo_code=" . $db->quote($_promo['promo_code']);
        $_promo['distinct_used'] = $db->query($sql)->fetchColumn() ?: 0;

        // Lấy số lần sử dụng mã giảm giá của người này
        $current_userid = $nv_Request->get_absint('current_userid', 'post', 0);
        $_promo['my_used'] = -1;
        if ($current_userid) {
            $sql = "SELECT COUNT(id) FROM " . $db_config['prefix'] . "_elink_promotion_code_use
            WHERE promo_code=" . $db->quote($_promo['promo_code']) . " AND use_userid=" . $current_userid;
            $_promo['my_used'] = $db->query($sql)->fetchColumn() ?: 0;
        }

        $this->result->set('data', $_promo);
        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
