<!-- BEGIN: main -->
<div class="form-group">
    <!-- BEGIN: telepro -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#telepro" aria-expanded="false" aria-controls="telepro"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_TELEPRO} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="telepro">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name}: <b><a href="{TELEPRO.link}" class="show_telepro">{TELEPRO.name}</a></b>; {LANG.phone}: <b>{TELEPRO.phone}</b>; {LANG.email}: <b>{TELEPRO.email}</b>; Thời gian gọi: <b>{TELEPRO.timecall}</b>; {LANG.telepro_job_title}: {TELEPRO.job_title}
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: telepro -->

    <!-- BEGIN: business -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#business" aria-expanded="false" aria-controls="business"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_BUSINESS} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="business">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.company_name}: <b><a href="{BUSINESS.link}" target="_blank">{BUSINESS.companyname}</a></b>; {LANG.phone}: <b>{BUSINESS.phone}</b>; {LANG.email}: <b>{BUSINESS.email}</b>; {LANG.tax}: <b>{BUSINESS.code}</b>; Ngày phê duyệt: <b>{BUSINESS.ngay_phe_duyet}</b>; Cập nhật lần cuối:<b>{BUSINESS.update_time}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: business -->
    <!-- BEGIN: dkkd -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#dkkd" aria-expanded="false" aria-controls="dkkd"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_DKKD} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="dkkd">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.company_name}:
                <b><a href="{BUSINESS.link}" target="_blank">{BUSINESS.prof_name}</a></b>;
                {LANG.phone}: <b>{BUSINESS.info_phone}</b>;
                {LANG.email}: <b>{BUSINESS.info_email}</b>;
                {LANG.tax}: <b>{BUSINESS.prof_code}</b>;
                {LANG.nguoi_dai_dien}: <b>{BUSINESS.represent_name}</b>;
                <!-- BEGIN: company_related -->
                    {LANG.company_same_representative}:
                    <!-- BEGIN: loop_company -->
                        <b><a href="{COMPANY_RELATED.link_view}">{COMPANY_RELATED.company_name}</a></b>
                    <!-- END: loop_company -->
                </tr>
                <!-- END: company_related -->
                {LANG.dia_chi_tru_so}: <b>{BUSINESS.prof_address}</b>;
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: dkkd -->

    <!-- BEGIN: duplicate_profile -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#profile_dtnet" aria-expanded="false" aria-controls="profile_dtnet"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUMBER_PROFILE_TITLE} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="profile_dtnet">
        <ul class="logotherlists" id="profile_dtnet_list">
            <!-- BEGIN: loop -->
            <li>{LANG.company_name}: {PROFILE.prof_name_title} ; {LANG.phone}: <strong> {PROFILE.info_phone} </strong>; {LANG.email}: <strong> {PROFILE.info_email} </strong>; {LANG.tax}: <strong> {PROFILE.prof_code} </strong>; {LANG.nguoi_dai_dien}: <strong> {PROFILE.represent_name} </strong>; {LANG.dia_chi_tru_so}: <strong> {PROFILE.prof_address} </strong>;
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: duplicate_profile -->
    <!-- BEGIN: error_duplicate_profile -->
    <div>
        <div class="alert alert-warning">{LANG.err_check_duplicate_profile_dtnet}</div>
    </div>
    <!-- END: error_duplicate_profile -->

    <!-- BEGIN: user_phone -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#user_phone" aria-expanded="false" aria-controls="user_phone"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_USERS_PHONE} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="user_phone">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name}: <a href="{USERS_PHONE.link}" target="_blank"><b>{USERS_PHONE.title}</b></a><br>
                {LANG.phone}: <b>{USERS_PHONE.phone}</b><br>
                {LANG.email}: <b>{USERS_PHONE.email}</b><br>
                {LANG.tax}: <b>{USERS_PHONE.mst}</b><b>{USERS_PHONE.check_vip}</b><br>
                Thời gian đăng ký: <b>{USERS_PHONE.regdate}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: user_phone -->
    <!-- BEGIN: lead_phone -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#lead_phone" aria-expanded="false" aria-controls="lead_phone"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_LEADS_PHONE} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="lead_phone">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name}: <b>{LEADS_PHONE.name}</b>; {LANG.source_leads}: <b>{LEADS_PHONE.source_leads_title}</b>; Trùng trường dữ liệu: <b>{LEADS_PHONE.duplicate}</b>; {LANG.caregiver_id}: <b>{LEADS_PHONE.caregiver_id}</b>; {LANG.status}: <b>{LEADS_PHONE.status}</b>;<b>{LEADS_PHONE.check_vip}</b>; Cập nhật lần cuối:<b>{LEADS_PHONE.updatetime}{LEADS_PHONE.hot_remaining_to}; {LEADS_PHONE.notify_click_lead}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: lead_phone -->
    <!-- BEGIN: tawk -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#lead_tawk" aria-expanded="false" aria-controls="lead_tawk"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_TAWK} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="lead_tawk">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name}: <b>{TAWK.hoten}</b>; {LANG.sdt}: <b>{TAWK.sdt}</b>; {LANG.sdt_khach_nhap}: <b>{TAWK.sdt_text}</b>; {LANG.email}: <b>{TAWK.email}</b>; Cập nhật lần cuối:<b>{TAWK.thoi_gian_gui}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: tawk -->
    <!-- BEGIN: mess -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#lead_mess" aria-expanded="false" aria-controls="lead_mess"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_MESS} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="lead_mess">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name}: <b>{MESS.hoten}</b>; {LANG.sdt}: <b>{MESS.sdt}</b>; {LANG.sdt_khach_nhap}: <b>{MESS.sdt_text}</b>; {LANG.email}: <b>{MESS.email}</b>; Cập nhật lần cuối:<b>{MESS.thoi_gian_gui}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: mess -->
    <!-- BEGIN: teacher -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#lead_teacher" aria-expanded="false" aria-controls="lead_teacher"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_TEACHER} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="lead_teacher">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name}: <b>{TEACHER.name}</b>; {LANG.sdt}: <b>{TEACHER.phone}</b>; {LANG.sdt_khach_nhap}: <b>{TEACHER.phone_text}</b>; {LANG.email}: <b>{TEACHER.email}</b>; Cập nhật lần cuối:<b>{TEACHER.thoi_gian_gui}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: teacher -->
    <!-- BEGIN: tochuc -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#lead_tochuc" aria-expanded="false" aria-controls="lead_tochuc"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_TOCHUC} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="lead_tochuc">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.ten_to_chuc}: <b><a href="{TOCHUC.link}" target="_blank">{TOCHUC.ten_to_chuc}</a></b>; {LANG.dia_chi_tru_so}: <b>{TOCHUC.dia_chi_tru_so}</b>; {LANG.nguoi_dai_dien}: <b>{TOCHUC.nguoi_dai_dien}</b>; {LANG.ma_so_thue}: <b>{TOCHUC.ma_so_thue}</b>; {LANG.ngay_boc_tin}:<b> {TOCHUC.thoi_gian_boc}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: tochuc -->
    <!-- BEGIN: laboratory -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#lead_laboratory" aria-expanded="false" aria-controls="lead_laboratory"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_LABORATORY} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="lead_laboratory">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.tieu_de}: <b><a href="{LABORATORY.link}" target="_blank">{LABORATORY.tieu_de}</a></b>; {LANG.ma_so_thue}: <b>{LABORATORY.ma_so_thue}</b>; {LANG.ma_so_phongtn}: <b>{LABORATORY.ma_so_phongtn}</b>; {LANG.ten_phongtn}: <b>{LABORATORY.ten_phongtn}</b>; {LANG.ngay_boc_tin}:<b> {LABORATORY.thoi_gian_boc}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: laboratory -->
    <!-- BEGIN: oppotunities -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#oppotunities" aria-expanded="false" aria-controls="oppotunities"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_OPPOTUNTIES} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="oppotunities">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name}: <b>{OPPOTUNITIES.name}</b>; Trùng trường dữ liệu: <b>{OPPOTUNITIES.duplicate}</b>; {LANG.caregiver_id}: <b>{OPPOTUNITIES.caregiver_id}</b>; {LANG.status}: <b>{OPPOTUNITIES.status}</b>;<b>{OPPOTUNITIES.check_vip}</b>; Cập nhật lần cuối:<b>{OPPOTUNITIES.updatetime}{OPPOTUNITIES.hot_remaining_to}; {OPPOTUNITIES.notify_click_opportunities}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: oppotunities -->

    <!-- BEGIN: customs_log -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#customs_log" aria-expanded="false" aria-controls="customs_log"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_CUSTOMS_LOG} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="customs_log">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name}: <b>{CUSTOMS_LOG.name}</b>; Trùng trường dữ liệu: <b>{CUSTOMS_LOG.duplicate}</b>; {LANG.caregiver_id}: <b>{CUSTOMS_LOG.caregiver_id}</b>;<b>{CUSTOMS_LOG.check_vip}</b>; Thời gian: <b>{CUSTOMS_LOG.addtime}</b>;
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: customs_log -->

    <!-- BEGIN: duplicate_payment -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#duplicate_payment" aria-expanded="false" aria-controls="duplicate_payment"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_DUPLICATE_PAYMENT} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="duplicate_payment">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name_duplicate_payment}: <b>{DUPLICATE_PAYMENT.name}</b>; {LANG.duplicate}: <b>{DUPLICATE_PAYMENT.duplicate}</b>; {LANG.caregiver_id}: <b>{DUPLICATE_PAYMENT.caregiver_id}</b>;<b>{DUPLICATE_PAYMENT.check_vip}</b>; {LANG.createtime}: <b>{DUPLICATE_PAYMENT.addtime}</b>; {LANG.numbers_year}: <b>{DUPLICATE_PAYMENT.numbers_year} {LANG.year}</b>;
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: duplicate_payment -->
    <!-- BEGIN: error_duplicate_payment -->
    <div>
        <div class="alert alert-warning">{LANG.err_check_duplicate_payment_dtnet}</div>
    </div>
    <!-- END: error_duplicate_payment -->

    <!-- BEGIN: bank -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#bank" aria-expanded="false" aria-controls="bank"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_BANK} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="bank">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name}: <b>{BANK.name}</b>; {LANG.phone}: <b>{BANK.phone}</b>;{LANG.cccd}: <b>{BANK.idnumber}</b>; {LANG.email}: <b>{BANK.email}</b>; Giới tính: <b>{BANK.sex}</b>; Ngày sinh: <b>{BANK.birthday}</b>; Công việc: <b>{BANK.job}</b>; Địa chỉ: <b>{BANK.address}</b>;
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: bank -->
    <!-- BEGIN: canhan -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#lead_canhan" aria-expanded="false" aria-controls="lead_canhan"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_CANHAN} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="lead_canhan">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.lastname_firstname}: <b>{CANHAN.ho_va_ten}</b>; {LANG.cccd}: <b>{CANHAN.cccd}</b>; {LANG.ngay_sinh}: <b>{CANHAN.ngay_sinh}</b>;{LANG.trinh_do}: <b>{CANHAN.trinh_do_chuyen_mon}</b>; {LANG.ngay_boc_tin}:<b> {CANHAN.thoi_gian_boc}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: canhan -->
    <!-- BEGIN: chungchi_xaydung -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#lead_chungchi" aria-expanded="false" aria-controls="lead_chungchi"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_CHUNGCHI} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="lead_chungchi">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.lastname_firstname}: <b>{CANHAN.ho_va_ten}</b>; {LANG.so_chung_chi}: <b>{CHUNGCHI.so_chung_chi}</b>; {LANG.linh_vuc}: <b>{CHUNGCHI.ma_linh_vuc}</b>; {LANG.hang}:<b> {CHUNGCHI.hang}</b>; {LANG.ngay_het_han}:<b> {CHUNGCHI.ngay_het_han}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: chungchi_xaydung -->
    <!-- BEGIN: bni -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#bni" aria-expanded="false" aria-controls="bni"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_BNI} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="bni">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name}: <b>{BNI.full_name}</b>; {LANG.phone}: <b>{BNI.phone}</b>; {LANG.company}: <b>{BNI.company}</b>; {LANG.field}: <b>{BNI.profession}</b>; {LANG.address_title}: <b>{BNI.address}</b>; {LANG.chapter}: <b>{BNI.chapter.name}</b>; {BNI.isuser}
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: bni -->
    <!-- BEGIN: mobiphone -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#mobiphone" aria-expanded="false" aria-controls="mobiphone"><i class="fa fa-arrow-right" aria-hidden="true"></i> {MOBIPHONE_DUPLICATE} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
        <div class="collapse" id="mobiphone">
            <ul class="logotherlists">
                <!-- BEGIN: loop -->
                <li>{LANG.phone}: <b><a href='{MOBIPHONE.link_mobilephone}' class="show_mobifone"> {MOBIPHONE.sdt} </a></b>; <b>{MOBIPHONE.loai_cuoc_goi}</b>;<b>{MOBIPHONE.trang_thai_cuoc_goi}</b>; {LANG.nhanh_goi}: <b>{MOBIPHONE.nhanh}</b>; {LANG.gap_nv}: <b>{MOBIPHONE.chuyen_vien}</b>; {LANG.time_start}: <b>{MOBIPHONE.thoi_gian_bat_dau}</b>; {LANG.time_finish}: <b>{MOBIPHONE.thoi_gian_ket_thuc}</b>;
                </li>
                <!-- END: loop -->
            </ul>
        </div>
    </div>
    <!-- END: mobiphone -->
    <!-- BEGIN: voicecloud -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#voicecloud" aria-expanded="false" aria-controls="voicecloud"><i class="fa fa-arrow-right" aria-hidden="true"></i> {VOICECLOUD_DUPLICATE} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
        <div class="collapse" id="voicecloud">
            <ul class="logotherlists">
                <!-- BEGIN: loop -->
                <li>{LANG.phone}: <b><a href='{VOICECLOUD.link}' class="show_voicecloud"> {VOICECLOUD.phone} </a></b>; <b>{VOICECLOUD.direction}</b>;<b>{VOICECLOUD.trang_thai_cuoc_goi}</b>; {LANG.gap_nv}: <b>{VOICECLOUD.name}</b>; {LANG.time_start}: <b>{VOICECLOUD.calldatetimestart}</b>; {LANG.time_finish}: <b>{VOICECLOUD.calldatetimeend}</b>;
                </li>
                <!-- END: loop -->
            </ul>
        </div>
    </div>
    <!-- END: voicecloud -->
    <!-- BEGIN: email -->
    <!-- BEGIN: marketing_email -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#marketing_email_{NUM}" aria-expanded="false" aria-controls="marketing_email_{NUM}"><i class="fa fa-arrow-right" aria-hidden="true"></i> Có {NUM_MAIL} mail được gửi tới {MAIL} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
        <div class="collapse" id="marketing_email_{NUM}">
            <!-- BEGIN: campaign -->
            <a href="#"><b>***</b>Có {NUM_CAM} mail trong chiến dịch: <b>{NAME_CAM}</b></a>
            <ul class="logotherlists">
                <!-- BEGIN: loop -->
                <li>Event: {ITEM.event}; <b>Tiêu đề</b>:<b>{ITEM.subject}</b>; ID định danh: <b>{ITEM.contact_id}</b>; ID thành viên: <b>{ITEM.userid}</b>; <b>Trạng thái</b>:<b class="red">{ITEM.status}</b> - <span class="red">{ITEM.read}</span>; Link theo dõi: {ITEM.urls};
                </li>
                <!-- END: loop -->
            </ul>
            <!-- END: campaign -->
        </div>
    </div>
    <!-- END: marketing_email -->

    <!-- BEGIN: marketing_api_error -->
    <div class="alert alert-warning">
        <strong>Warning!</strong> Can't get list email. Error code: {ERROR_CODE}. Message: {ERROR_MESS}
    </div>
    <!-- END: marketing_api_error -->
    <!-- END: email -->
    <!-- BEGIN: email_sys -->
    <div>
        <a data-toggle="collapse" data-bs-toggle="collapse" href="#system_email" aria-expanded="false" aria-controls="system_email"><i class="fa fa-arrow-right" aria-hidden="true"></i> Mail gửi từ hệ thống &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
        <div class="collapse" id="system_email">
            <!-- BEGIN: bidding -->
            <a href="#"><b>***</b>Có {NUM_LOG} mail đăng kí, gia hạn VIP:</a>
            <ul class="logotherlists">
                <!-- BEGIN: logs -->
                <li>Loại mail: {TYPE_LOG}; Thời gian: {LOG.log_time}; <!-- BEGIN: loop --> <b>{KEY_LOG}</b>:{LOG_VALUE}; <!-- END: loop -->
                </li>
                <!-- END: logs -->
            </ul>
            <!-- END: bidding -->
            <!-- BEGIN: contact -->
            <a href="#"><b>***</b>Có {NUM_LOG} mail gửi từ module Liên Hệ</a>
            <ul class="logotherlists">
                <!-- BEGIN: loop -->
                <li>Tiêu đề: RE: {CONTACT.title}; Thời gian: <b>{CONTACT.reply_time}</b>; Nội dung: {CONTACT.reply_content};
                </li>
                <!-- END: loop -->
            </ul>
            <!-- END: contact -->
        </div>
    </div>
    <!-- END: email_sys -->

</div>

<script type="text/javascript">
    $(document).ready(function() {
        //Gọi API GetDuplicateProfile

        // Mở mail
        $('[data-toggle="openMail"]').on('click', function(e) {
            e.preventDefault();
            var w = 600;
            var h = 800;
            if (w > $(window).width()) {
                w = $(window).width() - 10;
            }
            if (h > $(window).height()) {
                h = $(window).height() - 10;
            }
            nv_open_browse($(this).attr('href'), 'NVImg', w, h, "resizable=no,scrollbars=no,toolbar=no,location=no,status=no");
        });
    });
    $('.show_telepro').click(function() {
        var url = $(this).attr('href');
        $.post(url, function(res) {
            $('.modal-body').empty();
            //modalShow('{LANG.telepro}', res);
            modalShow('{LANG.telepro}', res, () => {}, () => {
                location.reload();
            });
        });
        return false;
    });
    $('.show_voicecloud').click(function() {
        var url = $(this).attr('href');
        $.post(url, function(res) {
            $('.modal-body').empty();
            //modalShow('{LANG.voicecloud}', res);
            modalShow('{LANG.voicecloud}', res, () => {}, () => {
                location.reload();
            });
        });
        return false;
    });
    $('.show_mobifone').click(function() {
        var url = $(this).attr('href');
        $.post(url, function(res) {
            $('.modal-body').empty();
            //modalShow('{LANG.mobiphone}', res);
            modalShow('{LANG.mobiphone}', res, () => {}, () => {
                location.reload();
            });
        });
        return false;
    });
</script>
<!-- END: main -->
