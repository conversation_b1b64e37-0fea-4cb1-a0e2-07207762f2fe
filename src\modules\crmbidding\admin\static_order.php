<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$array_search = array();
$curent = nv_date('d/m/Y', NV_CURRENTTIME);
$curent_from = '01/' . nv_date('m/Y', NV_CURRENTTIME);
$array_search['time_from'] = $nv_Request->get_title('time_from', 'post,get', $curent_from);
$array_search['time_to'] = $nv_Request->get_title('time_to', 'post,get', $curent);
$array_search['admin_id'] = $nv_Request->get_int('admin_id', 'post,get', -1);

// api thống kê đơn hàng
$api = new NukeViet\Api\DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
$api->setModule('bidding')
    ->setLang('vi')
    ->setAction('StaticOrder');

$exc = $api->execute();
if ($exc['status'] != 'success') {
    trigger_error($exc['message']);
}

// lưu file để thống kê lại static_sale
$static_sale_file = NV_ROOTDIR . '/data/logs/static_sale.txt';
file_put_contents($static_sale_file, json_encode($array_search));

Header("Location: " . NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=static&t=' . NV_CURRENTTIME);
die();
