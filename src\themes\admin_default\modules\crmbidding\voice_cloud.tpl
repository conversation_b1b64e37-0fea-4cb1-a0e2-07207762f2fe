<!-- BEGIN: main -->
<!-- BEGIN: error -->
    <div class="alert alert-success">{ERROR}</div>
<!-- END: error -->
<!-- BEGIN: search -->
 <script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<link type="text/css" href="{NV_STATIC_URL}themes/default/images/{MODULE_FILE}/bootstrap-datepicker/css/bootstrap-datepicker.min.css" rel="stylesheet" />
<link type="text/css" href="{NV_STATIC_URL}themes/default/images/{MODULE_FILE}/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" />
<script type="text/javascript" src="{NV_STATIC_URL}themes/default/images/{MODULE_FILE}/bootstrap-datepicker/js/bootstrap-datepicker.min.js"></script>
<script type="text/javascript" src="{NV_STATIC_URL}themes/default/images/{MODULE_FILE}/bootstrap-datepicker/locales/bootstrap-datepicker.{NV_LANG_INTERFACE}.min.js"></script>
<div class="well">
    <form action="{NV_BASE_ADMINURL}index.php" method="get">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" /> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" /> <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
        <div class="row">
            <div class="col-xs-12 col-md-6 col-sm-6">
                <div class="form-group">
                    <label for="ipt_phone"><strong>{LANG.search_sdt}:</strong></label>
                    <input class="form-control" type="text" value="{PHONE}" name="phone" maxlength="255" placeholder="{LANG.search_sdt}" />
                </div>
            </div>
            <div class="col-xs-12 col-md-6 col-sm-6">
                <div class="form-group">
                    <label for="ipt_job_id"><strong>{LANG.type_view}:</strong></label>
                    <select class="form-control" name="type_view">
                        <option value="1" {SELECTED_TYPE_VIEW1}>{LANG.type_view1}</option>
                        <option value="2" {SELECTED_TYPE_VIEW2}>{LANG.type_view2}</option>
                    </select>
                </div>
            </div>
            <div class="col-xs-12 col-md-6 col-sm-6">
                <div class="form-group">
                    <label for="ipt_extension_sale"><strong>{LANG.gap_nv}:</strong></label>
                    <select class="form-control" name="extension_sale">
                        <option value="0">Tất cả</option>
                        <!-- BEGIN: loop_extension -->
                        <option value="{EXTENSION.key}" {EXTENSION.selected}>{EXTENSION.full_name} - {EXTENSION.extension_voicecloud}</option>
                        <!-- END: loop_extension -->
                    </select>
                </div>
            </div>
            <div class="col-xs-12 col-md-6 col-sm-6">
                <div class="form-group">
                    <div class="input-group">
                        <label for="ipt_fromtime"><strong>{LANG.time_call_start}:</strong></label>
                        <input class="form-control datepicker" type="text" value="{FROMTIME}" name="fromtime" maxlength="10" autocomplete="off" placeholder="dd-mm-yyyy">
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-md-6 col-sm-6">
                <div class="form-group">
                    <div class="input-group">
                        <label for="ipt_totime"><strong>{LANG.time_call_end}:</strong></label>
                        <input class="form-control datepicker" type="text" value="{TOTIME}" name="totime" maxlength="10" autocomplete="off" placeholder="dd-mm-yyyy">
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-md-6 col-sm-6">
                <div class="form-group">
                    <label class="visible-sm-block visible-md-block visible-lg-block">&nbsp;</label>
                    <input class="btn btn-primary" type="submit" value="{LANG.search_submit}" />
                </div>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    $(window).on('load', function() {
        $('.datepicker').datepicker({
            language: '{NV_LANG_INTERFACE}',
            format: 'dd/mm/yyyy',
            weekStart: 1,
            todayBtn: 'linked',
            autoclose: true,
            todayHighlight: true,
            zIndexOffset: 1000
        });
        $('select[name="extension_sale"]').select2({
            'language': '{NV_LANG_INTERFACE}',
            'width': '100%'
        });
    });
</script>
<!-- END: search -->

<!-- BEGIN: view -->
<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th class="w50">{LANG.number}</th>
                    <th class="w100">
                        <div class="inlineblock">{LANG.so_dien_thoai}</div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">{LANG.gap_nv}</div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">{LANG.status_leads}</div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">{LANG.thoi_gian_goi_dau}</div>
                    </th>
                </tr>
            </thead>
            <!-- BEGIN: generate_page -->
            <tfoot>
                <tr>
                    <td class="text-center" colspan="10">{NV_GENERATE_PAGE}</td>
                </tr>
            </tfoot>
            <!-- END: generate_page -->
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="text-center">{VIEW.number}</td>
                    <td class="text-center"><a href="{VIEW.link_detail}">{VIEW.phone}</a></td>
                    <td>
                        <!-- BEGIN: chuyen_vien --> {CHUYEN_VIEN.extension} ({CHUYEN_VIEN.name}), <!-- END: chuyen_vien -->
                    </td>
                    <td class="text-center">

                        <!-- BEGIN: created_leads -->
                            <a href="{VIEW.link_leads}"><i class="fa fa-check">&nbsp;</i>{VIEW.status_leads}</a>
                        <!-- END: created_leads -->

                        <!-- BEGIN: not_created_leads -->
                        {VIEW.status_leads}
                        <!-- END: not_created_leads -->

                    </td>
                    <td class="text-center">{VIEW.calldatetimestart}</td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
</form>
<!-- END: view -->

<!-- BEGIN: detail -->
<div class="table-responsive">
    <table class="table table-striped table-bordered table-hover">
        <thead>
            <tr>
                <th class="w50">{LANG.number}</th>
                <th class="w100">
                    <div class="inlineblock">{LANG.file_ghi_am}</div>
                </th>
                <th class="w150">
                    <div class="inlineblock">{LANG.so_dien_thoai}</div>
                </th>
                <th class="text-center">
                    <div class="inlineblock">{LANG.loai_cuoc_goi}</div>
                </th>
                <th class="text-center">
                    <div class="inlineblock">{LANG.status}</div>
                </th>
                <th class="text-center">
                    <div class="inlineblock">{LANG.nhan_vien}</div>
                </th>
                <th class="text-center">
                    <div class="inlineblock">{LANG.thoi_gian}</div>
                </th>
                <th class="text-center">
                    <div class="inlineblock">{LANG.about}</div>
                </th>
                <!-- BEGIN: rowspan_th -->
                <th class="text-center w400 {HIDDEN}">
                    <div class="inlineblock">{LANG.leads_created}</div>
                </th>
                <!-- END: rowspan_th -->
            </tr>
        </thead>
        <!-- BEGIN: generate_page -->
        <tfoot>
            <tr>
                <td class="text-center" colspan="10">{NV_GENERATE_PAGE}</td>
            </tr>
        </tfoot>
        <!-- END: generate_page -->
        <tbody>
            <!-- BEGIN: loop -->
            <tr>
                <td class="text-center">{VIEW.number}</td>
                <td>
                    <!-- BEGIN: recording --> <audio controls>
                        <source src="{VIEW.recordingpath}" type="audio/wav">
                        {LANG.browser_not_support}
                    </audio> <!-- END: recording -->
                </td>
                <td class="text-center"><a href="{VIEW.link_detail}">{VIEW.phone}</a><!-- BEGIN: download --> <a href="{VIEW.recordingpath}" title="{LANG.download_audio}"><i class="fa fa-download" aria-hidden="true"></i></a> <!-- END: download --></td>
                <td class="text-center">{VIEW.direction}</td>
                <td class="text-center">{VIEW.status}</td>
                <td>{VIEW.extension} ({VIEW.name})</td>
                <td class="text-center">{VIEW.thoi_gian}</td>
                <td>
                    {LANG.holdtime}{VIEW.holdtime} </br>
                    {LANG.duration}{VIEW.duration} </br>
                    {LANG.did_number}{VIEW.did_number} </br>
                    {LANG.billsec}{VIEW.billsec} </br>
                    {LANG.fee}{VIEW.fee} </br>
                </td>
                <!-- BEGIN: rowspan -->
                    <td rowspan="{ROWSPAN}" id="duplicate" class="{HIDDEN}">
                        <h1 class="text-center"><a><i class="fa fa-spinner fa-spin" aria-hidden="true"></i></a></h1>
                    </td>
                <!-- END: rowspan -->
            </tr>
            <!-- END: loop -->
        </tbody>
    </table>
</div>
<!-- END: detail -->

<script type="text/javascript">
$(window).on('load', function() {
    setTimeout(duplicate, 3000);
    function duplicate() {
        $.post(script_name + '?' + nv_name_variable + '=crmbidding&' + nv_fc_variable + '=voice_cloud&nocache=' + new Date().getTime(), 'phone={PHONE}&ajax=1', function(res) {
            $('#duplicate').html(res);
        });
    }
});
</script>

<!-- END: main -->

<!-- BEGIN: ajax -->
    <!-- BEGIN: business -->
    <div>
        <a data-toggle="collapse" href="#business" aria-expanded="false" aria-controls="business"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_BUSINESS} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="business">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.company_name}: <b><a href="{BUSINESS.link}" target="_blank">{BUSINESS.companyname}</a></b>; {LANG.phone}: <b>{BUSINESS.phone}</b>; {LANG.email}: <b>{BUSINESS.email}</b>; {LANG.tax}: <b>{BUSINESS.code}</b>; Ngày phê duyệt: <b>{BUSINESS.ngay_phe_duyet}</b>; Cập nhật lần cuối:<b>{BUSINESS.update_time}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: business -->

    <!-- BEGIN: duplicate_profile -->
    <div>
        <a data-toggle="collapse" href="#profile_dtnet" aria-expanded="false" aria-controls="profile_dtnet"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUMBER_PROFILE_TITLE} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="profile_dtnet">
        <ul class="logotherlists" id="profile_dtnet_list">
            <!-- BEGIN: loop -->
            <li>{LANG.company_name}: {PROFILE.prof_name_title} ; {LANG.phone}: <strong> {PROFILE.info_phone} </strong>; {LANG.email}: <strong> {PROFILE.info_email} </strong>; {LANG.tax}: <strong> {PROFILE.prof_code} </strong>; {LANG.nguoi_dai_dien}: <strong> {PROFILE.represent_name} </strong>; {LANG.dia_chi_tru_so}: <strong> {PROFILE.prof_address} </strong>;
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: duplicate_profile -->
    <!-- BEGIN: error_duplicate_profile -->
    <div>
        <div class="alert alert-warning">{LANG.err_check_duplicate_profile_dtnet}</div>
    </div>
    <!-- END: error_duplicate_profile -->

    <!-- BEGIN: duplicate_payment -->
    <div>
        <a data-toggle="collapse" href="#duplicate_payment" aria-expanded="false" aria-controls="duplicate_payment"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_DUPLICATE_PAYMENT} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="duplicate_payment">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name_duplicate_payment}: <b>{DUPLICATE_PAYMENT.name}</b>; {LANG.duplicate}: <b>{DUPLICATE_PAYMENT.duplicate}</b>; {LANG.caregiver_id}: <b>{DUPLICATE_PAYMENT.caregiver_id}</b>;<b>{DUPLICATE_PAYMENT.check_vip}</b>; {LANG.createtime}: <b>{DUPLICATE_PAYMENT.addtime}</b>; {LANG.numbers_year}: <b>{DUPLICATE_PAYMENT.numbers_year} {LANG.year}</b>;
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: duplicate_payment -->
    <!-- BEGIN: error_duplicate_payment -->
    <div>
        <div class="alert alert-warning">{LANG.err_check_duplicate_payment_dtnet}</div>
    </div>
    <!-- END: error_duplicate_payment -->

    <!-- BEGIN: customs_log -->
        <div>
            <a data-toggle="collapse" href="#customs_log" aria-expanded="false" aria-controls="customs_log"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_CUSTOMS_LOG} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
        </div>
        <div class="collapse" id="customs_log">
            <ul class="logotherlists">
                <!-- BEGIN: loop -->
                <li>{LANG.name}: <b>{CUSTOMS_LOG.name}</b>; Trùng trường dữ liệu: <b>{CUSTOMS_LOG.duplicate}</b>; {LANG.caregiver_id}: <b>{CUSTOMS_LOG.caregiver_id}</b>;<b>{CUSTOMS_LOG.check_vip}</b>; Thời gian: <b>{CUSTOMS_LOG.addtime}</b>;
                </li>
                <!-- END: loop -->
            </ul>
        </div>
    <!-- END: customs_log -->

    <!-- BEGIN: lead_phone -->
    <div>
        <a data-toggle="collapse" href="#lead_phone" aria-expanded="false" aria-controls="lead_phone"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_LEADS_PHONE} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="lead_phone">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name}: <b>{LEADS_PHONE.name}</b>; {LANG.source_leads}: <b>{LEADS_PHONE.source_leads}</b>; Trùng trường dữ liệu: <b>{LEADS_PHONE.duplicate}</b>; {LANG.caregiver_id}: <b>{LEADS_PHONE.caregiver_id}</b>; {LANG.status}: <b>{LEADS_PHONE.status}</b>;<b>{LEADS_PHONE.check_vip}</b>; Cập nhật lần cuối:<b>{LEADS_PHONE.updatetime}; {LEADS_PHONE.notify_click_lead}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: lead_phone -->

    <!-- BEGIN: oppotunities -->
    <div>
        <a data-toggle="collapse" href="#oppotunities" aria-expanded="false" aria-controls="oppotunities"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_OPPOTUNTIES} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="oppotunities">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name}: <b>{OPPOTUNITIES.name}</b>; Trùng trường dữ liệu: <b>{OPPOTUNITIES.duplicate}</b>; {LANG.caregiver_id}: <b>{OPPOTUNITIES.caregiver_id}</b>; {LANG.status}: <b>{OPPOTUNITIES.status}</b>;<b>{OPPOTUNITIES.check_vip}</b>; Cập nhật lần cuối:<b>{OPPOTUNITIES.updatetime}; {OPPOTUNITIES.notify_click_opportunities}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: oppotunities -->

    <!-- BEGIN: user_phone -->
    <div>
        <a data-toggle="collapse" href="#user_phone" aria-expanded="false" aria-controls="user_phone"><i class="fa fa-arrow-right" aria-hidden="true"></i> {NUM_USERS_PHONE} &nbsp; <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
    </div>
    <div class="collapse" id="user_phone">
        <ul class="logotherlists">
            <!-- BEGIN: loop -->
            <li>{LANG.name}: <a href="{USERS_PHONE.link}" target="_blank"><b>{USERS_PHONE.title}</b></a>; {LANG.phone}: <b>{USERS_PHONE.phone}</b>; {LANG.email}: <b>{USERS_PHONE.email}</b>; {LANG.tax}: <b>{USERS_PHONE.mst}</b><b>{USERS_PHONE.check_vip}</b>; Thời gian đăng ký <b>{USERS_PHONE.regdate}</b>
            </li>
            <!-- END: loop -->
        </ul>
    </div>
    <!-- END: user_phone -->
    <!-- BEGIN: no_data -->
    {NO_DATA}
    <!-- END: no_data -->
<!-- END ajax -- >
 