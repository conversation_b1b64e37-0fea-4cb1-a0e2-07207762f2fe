<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2021 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Tue, 14 Dec 2021 01:34:02 GMT
 */
global $array_user_id_users; 
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$row = [];
$error = [];
$success = [];
$page_title = $nv_Lang->getModule('main_contact');

// Request
$orderby_contactname = $nv_Request->get_int('orderby_contactname', 'post,get', 0);
$orderby_primaryphone = $nv_Request->get_int('orderby_primaryphone', 'post,get', 0);
$orderby_primaryemail = $nv_Request->get_int('orderby_primaryemail', 'post,get', 0);
$orderby_organizationsid = $nv_Request->get_int('orderby_organizationsid', 'post,get', 0);
$orderby_active = $nv_Request->get_int('orderby_active', 'post,get', 0);
$orderby_address = $nv_Request->get_int('orderby_address', 'post,get', 0);
$q = $nv_Request->get_title('q', 'post,get');
$filter_organizations = $nv_Request->get_int('filter_organizations', 'post,get', 0);
$id_contact = $nv_Request->get_int('id_contact', 'post,get', 0);
$id_convert = $nv_Request->get_int('id_convert', 'post,get', 0);

// Load select 2 Ajax
if ($nv_Request->isset_request('get_org', 'post, get')) {
    $q_select = $nv_Request->get_title('q_select', 'post, get', '');
    $array_org_select = $array_org_return = [];
    $array_org_return[] = [
        'id' => -1,
        'title' => $nv_Lang->getModule('not_in_org')
    ];
    if (!empty($q_select)) {
        $where['OR'][] = [
            'LIKE' => [
                'organizationname' => "%" . $q_select . "%",
            ],
        ];
        $params_org_select = [
            'userid' => $admin_info['userid'],
            'page' => 1,
            'perpage' => 15,
            'where' => $where
        ];
        $array_org_select = nv_local_api('ListAllOrganizations', $params_org_select, $admin_info['username'], 'crmbidding');
        $array_org_select = json_decode($array_org_select, true);
        if (!empty($array_org_select) and $array_org_select['code'] == "0000") {
            foreach ($array_org_select['data'] as $key => $value) {
                $array_org_return[] = [
                    'id' => $value['id'],
                    'title' => $value['organizationname']
                ];
            }
        }
    }
    nv_jsonOutput($array_org_return);
}

// Thay đổi trạng thái
if ($nv_Request->isset_request('change_status', 'post, get')) {
    $id = $nv_Request->get_int('id', 'post, get', 0);
    
    $params= [
        'contactid' => $id
    ];
    $row = nv_local_api('GetDetailContact', $params, $admin_info['username'], 'crmbidding');
    $row = json_decode($row, true);
    $row = $row['data'];
    
    $content = 'NO_' . $id;
    if (isset($row['active'])) {
        $active = ($row['active']) ? 0 : 1;
        $content = 'OK_' . $id;
    }
    $data = [
        'active' => intval($active)
    ];
    $_params_update = [
        'contactid' => $id,
        'admin_id' => $admin_info['userid'],
        'data' => $data,
    ];
    $status_update = nv_local_api('UpdateContact', $_params_update, $admin_info['username'], 'crmbidding');
    $status_update = json_decode($status_update, true);
    include NV_ROOTDIR . '/includes/header.php';
    echo $content;
    include NV_ROOTDIR . '/includes/footer.php';
}

// Delete
if ($nv_Request->isset_request('delete_id', 'get') and $nv_Request->isset_request('delete_checkss', 'get')) {
    $id = $nv_Request->get_int('delete_id', 'get');
    $delete_checkss = $nv_Request->get_string('delete_checkss', 'get');
    if ($id > 0 and $delete_checkss == md5($id . NV_CACHE_PREFIX . $client_info['session_id'])) {
        $_params_insert = [
            'contactid' => $id,
        ];
        $status_delete = nv_local_api('DeleteContact', $_params_insert, $admin_info['username'], 'crmbidding');
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    }
}

// Add, Edit
$row['id'] = $nv_Request->get_int('id', 'post,get', 0);
if ($nv_Request->isset_request('submit', 'post')) {
    $row['contactname'] = nv_substr($nv_Request->get_title('contactname', 'post', ''),0 , 248);
    $row['shortname'] = nv_substr($nv_Request->get_title('shortname', 'post', ''),0 , 250);
    $row['primaryphone'] = nv_substr($nv_Request->get_title('primaryphone', 'post', ''),0 ,250);
    $row['secondaryphone'] = nv_substr($nv_Request->get_title('secondaryphone', 'post', ''),0, 250);
    $row['primaryemail'] = nv_substr($nv_Request->get_title('primaryemail', 'post', ''),0, 250);
    $row['secondaryemail'] = nv_substr($nv_Request->get_title('secondaryemail', 'post', ''),0 ,250);
    $row['address'] = nv_substr($nv_Request->get_title('address', 'post', ''), 0 ,250);
    $row['description'] = nv_substr($nv_Request->get_title('description', 'post', ''),0 ,250);
    $row['organizationsid'] = $nv_Request->get_int('organizationsid', 'post', 0);
    $row['convert_leads'] = $nv_Request->get_int('convert_leads', 'post', 0);

    if (empty($row['contactname'])) {
        $error[] = $nv_Lang->getModule('error_required_contactname');
    } 
    
    if (!empty($row['contactname']) and (strlen($row['contactname']) > 250)) {
        $error[] = $nv_Lang->getModule('error_long_contactname');
    } 
    
    if (empty($row['organizationsid'])) {
        $error[] = $nv_Lang->getModule('error_required_organizationsid');
    }

    if (empty($row['primaryphone']) and empty($row['primaryemail'])) {
        $error[] = $nv_Lang->getModule('error_required_primary_contact');
    }

    if (empty($error)) {
        if (empty($row['id'])) {
            $other_data = [
                'shortname' => $row['shortname'],
                'secondaryphone' => trim($row['secondaryphone']),
                'secondaryemail' => $row['secondaryemail'],
                'address' => $row['address'],
                'description' => $row['description'],
                'convert_leads' => $row['convert_leads'],
            ];
            $_params_insert = [
                'contactname' => $row['contactname'],
                'primaryphone' => $row['primaryphone'],
                'primaryemail' => $row['primaryemail'],
                'organizationsid' => $row['organizationsid'],
                'admin_id' => $admin_info['userid'],
                'otherdata' => $other_data,
            ];
            $status_insert = nv_local_api('CreateContact', $_params_insert, $admin_info['username'], 'crmbidding');
            $status_insert = json_decode($status_insert, true);
            if (!empty($row['convert_leads']) and $status_insert['status'] == 'success') {
                $params_leads = [
                    'leadid' => $row['convert_leads']
                ];
                $data_leads = nv_local_api('GetDetailLeads', $params_leads, $admin_info['username'], $module_name);
                $data_leads = json_decode($data_leads, true);
                $row_detail_leads = $data_leads['data'];
                $params = $params_leads = [];
                $params = [
                    'leadsid' => $row['convert_leads'],
                    'admin_id' => $admin_info['admin_id'],
                    'data' => [
                        'convert_contact' => $status_insert['ContactID']
                    ]
                ];
                $result = nv_local_api('UpdateLeads', $params, $admin_info['username'], $module_name);
                
                $params_opportunities = [
                    'opportunitiesid' => $row_detail_leads['opportunities_id'],
                    'admin_id' => $admin_info['admin_id'],
                    'data' => [
                        'convert_contact' => $status_insert['ContactID']
                    ]
                ];
                $result_opportunities = nv_local_api('UpdateOpportunities', $params_opportunities, $admin_info['username'], $module_name);
            }
        } else {
            $data = [
                'contactname' => $row['contactname'],
                'primaryphone' => $row['primaryphone'],
                'primaryemail' => $row['primaryemail'],
                'organizationsid' => $row['organizationsid'],
                'shortname' => $row['shortname'],
                'secondaryphone' => $row['secondaryphone'],
                'secondaryemail' => $row['secondaryemail'],
                'address' => $row['address'],
                'description' => $row['description']
            ];
            $_params_update = [
                'contactid' => $row['id'],
                'admin_id' => $admin_info['userid'],
                'data' => $data,
            ];
            $status_update = nv_local_api('UpdateContact', $_params_update, $admin_info['username'], 'crmbidding');
            $status_update = json_decode($status_update, true);
        }

        $nv_Cache->delMod($module_name);
        if (empty($row['id']) and ($status_insert['status'] == "success")) {
            nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
        } else if (!empty($row['id']) and $status_update['status']  == "success") {
            nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
        }

        if (empty($row['id']) and $status_insert['status'] == "error") {
            $error[] = $status_insert['message'];
        }

        if (!empty($row['id']) and $status_update['status'] == "error") {
            $error[] = $status_update['message'];
        }
    }
} elseif ($row['id'] > 0) {
    $page_title = $nv_Lang->getModule('edit_contact');
    $params= [
        'contactid' => $row['id']
    ];
    $row = nv_local_api('GetDetailContact', $params, $admin_info['username'], 'crmbidding');
    $row = json_decode($row, true);
    $row = $row['data'];
    if (empty($row)) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    }
} elseif ($id_contact > 0) {
    $page_title = $nv_Lang->getModule('view_detail_contact');
    $params= [
        'contactid' => $id_contact
    ];
    $row = nv_local_api('GetDetailContact', $params, $admin_info['username'], 'crmbidding');
    $row = json_decode($row, true);
    $row = $row['data'];
    if (empty($row)) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    }
} else {
    $row['id'] = 0;
    $row['contactname'] = '';
    $row['shortname'] = '';
    $row['primaryphone'] = '';
    $row['secondaryphone'] = '';
    $row['primaryemail'] = '';
    $row['secondaryemail'] = '';
    $row['address'] = '';
    $row['description'] = '';
    $row['organizationsid'] = 0;
    $row['convert_leads'] = 0;
}
// Lấy filter
$array_organizations = [];
if (!empty($filter_organizations) or !empty($row['id']) or !empty($id_contact)) {
    if (!empty($filter_organizations)) {
        $params_org = [
            'organizationsid' => $filter_organizations
        ];
    }

    if (!empty($id_contact) or !empty($row['id'])) {
        $params_org = [
            'organizationsid' => $row['organizationsid']
        ];
    }
    $array_organizations = nv_local_api('GetDetailOrganizations', $params_org, $admin_info['username'], 'crmbidding');
    $array_organizations = json_decode($array_organizations, true);
}

if (!empty($id_contact)) {
    $params= [
        'contactid' => $id_contact
    ];
    $row = nv_local_api('GetDetailContact', $params, $admin_info['username'], 'crmbidding');
    $row = json_decode($row, true);
    $row = $row['data'];
    if (empty($row)) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    }
}

$arr_where_org = [];
$show_view = false;
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
if (!$nv_Request->isset_request('id', 'post,get') and !$nv_Request->isset_request('id_contact', 'post,get') and !$nv_Request->isset_request('id_convert', 'post, get')) {
    $where = $order = [];
    $params = [
        'userid' => $admin_info['userid'],
        'page' => $nv_Request->get_int('page', 'post,get', 1),
        'perpage' => 20
    ];
    
    // Where
    if (!empty($q)) {
        $where['OR'][] = [
            'LIKE' => [
                'contactname' => "%" . $q . "%",
            ],
        ];
        $where['OR'][] = [
            'LIKE' => [
                'primaryphone' =>  "%" . $q . "%",
            ],
        ];
        $where['OR'][] = [
            'LIKE' => [
                'primaryemail' => "%" . $q . "%",
            ],
        ];
        $where['OR'][] = [
            'LIKE' => [
                'address' => "%" . $q . "%",
            ],
        ];
        $base_url .= '&q=' . $q;
    }
    if ($filter_organizations != 0) {
        $where['AND'][] = [
            '=' => [
                'organizationsid' =>  $filter_organizations,
            ],
        ];
        $base_url .= '&filter_organizations=' . $filter_organizations;
    }

    if (!empty($where)) {
        $params['where'] = $where;
    }

    // Order
    if ($orderby_contactname > 0) {
        $order['contactname'] = $orderby_contactname == 1 ? 'ASC' : 'DESC';
    }
    if ($orderby_primaryphone > 0) {
        $order['primaryphone'] = $orderby_primaryphone == 1 ? 'ASC' : 'DESC';
    }
    if ($orderby_primaryemail > 0) {
        $order['primaryemail'] = $orderby_primaryemail == 1 ? 'ASC' : 'DESC';
    }
    if ($orderby_organizationsid > 0) {
        $order['organizationsid'] = $orderby_organizationsid == 1 ? 'ASC' : 'DESC';
    }
    if ($orderby_active > 0) {
        $order['active'] = $orderby_active == 1 ? 'ASC' : 'DESC';
    }
    if ($orderby_address > 0) {
        $order['address'] = $orderby_address == 1 ? 'ASC' : 'DESC';
    }
    if (!empty($order)) {
        $params['order'] = $order;
    }
   
    $data_contact = nv_local_api('ListAllContact', $params, $admin_info['username'], 'crmbidding');
    $data_contact = json_decode($data_contact, true);
    $show_view = true;
    if (isset($data_contact['code']) and $data_contact['code'] == '0000') {
        $per_page = $data_contact['perpage'];
        $page = $data_contact['page'];
        $num_items = $data_contact['total'];
        foreach ($data_contact['data'] as $key => $value) {
            $arr_where_org['OR'][] = [
                '=' => [
                    'id' => $value['organizationsid'],
                ],
            ];
        }
    } else {
        $per_page = 1;
        $page = 1;
        $num_items = 0;
    }

    $link_orderby_contactname = $base_url . '&orderby_contactname=1';
    $link_orderby_primaryphone = $base_url . '&orderby_primaryphone=1';
    $link_orderby_primaryemail = $base_url . '&orderby_primaryemail=1';
    $link_orderby_organizationsid = $base_url . '&orderby_organizationsid=1';
    $link_orderby_active = $base_url . '&orderby_active=1';
    $link_orderby_address = $base_url . '&orderby_address=1';
}
// Lấy thông tin Leads
if (!empty($id_convert)) {
    $arr_info_leads = [];
    $params['leadid'] = $id_convert;
    $arr_info_leads = nv_local_api('GetDetailLeads', $params, $admin_info['username'], $module_name);
    $params = [];
    $arr_info_leads = json_decode($arr_info_leads, true);
    if (isset($arr_info_leads['data'])) {
        $row['contactname'] = $arr_info_leads['data']['name'];
        $row['primaryphone'] = $arr_info_leads['data']['phone'];
        $row['secondaryphone'] = $arr_info_leads['data']['sub_phone'];
        $row['primaryemail'] = $arr_info_leads['data']['email'];
        $row['secondaryemail'] = $arr_info_leads['data']['sub_email'];
        $row['address'] = $arr_info_leads['data']['address'];
        $row['convert_leads'] = $id_convert;
    }
}

// Lấy ra danh sách tổ chức
$params_org = [
    'userid' => $admin_info['userid']
];
$array_organizationsid_crmbidding = $array_organizations_all = [];
if (!empty($arr_where_org)) {
    $params_org = [
        'where' => $arr_where_org
    ];
    $array_organizationsid_crmbidding = nv_local_api('ListAllOrganizations', $params_org, $admin_info['username'], 'crmbidding');
    $array_organizationsid_crmbidding = json_decode($array_organizationsid_crmbidding, true);
    if (isset($array_organizationsid_crmbidding['code']) and $array_organizationsid_crmbidding['code'] == '0000') {
        foreach ($array_organizationsid_crmbidding['data'] as $key => $value) {
            $array_organizations_all[$value['id']] = $value;
        }
    }
}


$xtpl = new XTemplate('contact.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('ROW', $row);
$xtpl->assign('Q', $q);

if (isset($array_organizations['code']) and $array_organizations['code'] == '0000') {
    if ($row['id'] > 0) {
        $xtpl->assign('ORG_EDIT', $array_organizations['data']);
        $xtpl->parse('main.selected_edit');
    } else {
        $xtpl->assign('ORG', $array_organizations['data']);
        $xtpl->parse('main.selected_search');
    }
} else {
    $array_organizations = [
        'id' => -1,
        'organizationname' => $nv_Lang->getModule('not_in_org')
    ];
    if (!empty($row['id']) and $row['id'] != 0) {
        $xtpl->assign('ORG_EDIT', $array_organizations);
        $xtpl->parse('main.selected_edit');
    } 
    if ($filter_organizations != 0) {
        $xtpl->assign('ORG', $array_organizations);
        $xtpl->parse('main.selected_search');
    }
}

if ($show_view) {
    if ($orderby_contactname  > 0) {
        if ($orderby_contactname  == 1) {
            $link_orderby_contactname_desc  = $base_url . '&orderby_contactname=2' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_CONTACT_NAME_DESC', $link_orderby_contactname_desc);
            $xtpl->assign('ORDER_BY_CONTACT', $link_orderby_contactname_desc);
            $xtpl->parse('main.view.contactname.desc');
        } else {
            $link_orderby_contactname_asc = $base_url . '&orderby_contactname=1' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_CONTACT_NAME_ASC', $link_orderby_contactname_asc);
            $xtpl->assign('ORDER_BY_CONTACT', $link_orderby_contactname_asc);
            $xtpl->parse('main.view.contactname.asc');
        }
        $xtpl->parse('main.view.contactname');
        $base_url .= '&orderby_contactname=' . $orderby_contactname;
    } else {
        $xtpl->assign('ORDER_BY_CONTACT', $link_orderby_contactname . '&page=' . $page);
    }

    if ($orderby_primaryphone  > 0) {
        if ($orderby_primaryphone  == 1) {
            $link_orderby_primaryphone_desc  = $base_url . '&orderby_primaryphone=2' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_PRIMARY_PHONE_DESC', $link_orderby_primaryphone_desc);
            $xtpl->assign('ORDER_BY_PRIMARY_PHONE', $link_orderby_primaryphone_desc);
            $xtpl->parse('main.view.primaryphone.desc');
        } else {
            $link_orderby_primaryphone_asc = $base_url . '&orderby_primaryphone=1' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_PRIMARY_PHONE_ASC', $link_orderby_primaryphone_asc);
            $xtpl->assign('ORDER_BY_PRIMARY_PHONE', $link_orderby_primaryphone_asc);
            $xtpl->parse('main.view.primaryphone.asc');
        }
        $xtpl->parse('main.view.primaryphone');
        $base_url .= '&orderby_primaryphone=' . $orderby_primaryphone;
    } else {
        $xtpl->assign('ORDER_BY_PRIMARY_PHONE', $link_orderby_primaryphone . '&page=' . $page);
    }

    if ($orderby_primaryemail > 0) {
        if ($orderby_primaryemail  == 1) {
            $link_orderby_primaryemail_desc  = $base_url . '&orderby_primaryemail=2' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_PRIMARY_EMAIL_DESC', $link_orderby_primaryemail_desc);
            $xtpl->assign('ORDER_BY_PRIMARY_EMAIL', $link_orderby_primaryemail_desc);
            $xtpl->parse('main.view.primaryemail.desc');
        } else {
            $link_orderby_primaryemail_asc = $base_url . '&orderby_primaryemail=1' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_PRIMARY_EMAIL_ASC', $link_orderby_primaryemail_asc);
            $xtpl->assign('ORDER_BY_PRIMARY_EMAIL', $link_orderby_primaryemail_asc);
            $xtpl->parse('main.view.primaryemail.asc');
        }
        $xtpl->parse('main.view.primaryemail');
        $base_url .= '&orderby_primaryemail=' . $orderby_primaryemail;
    } else {
        $xtpl->assign('ORDER_BY_PRIMARY_EMAIL', $link_orderby_primaryemail . '&page=' . $page);
    }

    if ($orderby_organizationsid > 0) {
        if ($orderby_organizationsid  == 1) {
            $link_orderby_organizationsid_desc = $base_url . '&orderby_organizationsid=2' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_ORGANIZATION_DESC', $link_orderby_organizationsid_desc);
            $xtpl->assign('ORDER_BY_ORGANIZATION', $link_orderby_organizationsid_desc);
            $xtpl->parse('main.view.organizationsid.desc');
        } else {
            $link_orderby_organizationsid_asc = $base_url . '&orderby_organizationsid=1' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_ORGANIZATION_ASC', $link_orderby_organizationsid_asc);
            $xtpl->assign('ORDER_BY_ORGANIZATION', $link_orderby_organizationsid_asc);
            $xtpl->parse('main.view.organizationsid.asc');
        }
        $xtpl->parse('main.view.organizationsid');
        $base_url .= '&orderby_organizationsid=' . $orderby_organizationsid;
    } else {
        $xtpl->assign('ORDER_BY_ORGANIZATION', $link_orderby_organizationsid . '&page=' . $page);
    }

    if ($orderby_active  > 0) {
        if ($orderby_active  == 1) {
            $link_orderby_active_desc  = $base_url . '&orderby_active=2' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_ACTICE_DESC', $link_orderby_active_desc);
            $xtpl->assign('ORDER_BY_ACTICE', $link_orderby_active_desc);
            $xtpl->parse('main.view.active.desc');
        } else {
            $link_orderby_active_asc = $base_url . '&orderby_active=1' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_ACTICE_ASC', $link_orderby_active_asc);
            $xtpl->assign('ORDER_BY_ACTICE', $link_orderby_active_asc);
            $xtpl->parse('main.view.active.asc');
        }
        $xtpl->parse('main.view.active');
        $base_url .= '&orderby_active=' . $orderby_active;
    } else {
        $xtpl->assign('ORDER_BY_ACTICE', $link_orderby_active  . '&page=' . $page);
    }

    if ($orderby_address  > 0) {
        if ($orderby_address  == 1) {
            $link_orderby_address_desc  = $base_url . '&orderby_address=2' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_ADDRESS_DESC', $link_orderby_address_desc);
            $xtpl->assign('ORDER_BY_ADDRESS', $link_orderby_address_desc);
            $xtpl->parse('main.view.address.desc');
        } else {
            $link_orderby_address_asc = $base_url . '&orderby_address=1' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_ADDRESS_ASC', $link_orderby_address_asc);
            $xtpl->assign('ORDER_BY_ADDRESS', $link_orderby_address_asc);
            $xtpl->parse('main.view.address.asc');
        }
        $xtpl->parse('main.view.address');
        $base_url .= '&orderby_address=' . $orderby_address;
    } else {
        $xtpl->assign('ORDER_BY_ADDRESS', $link_orderby_address  . '&page=' . $page);
    }
    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.view.generate_page');
    }
    $number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;
    if (!empty($data_contact['data'])) {
        foreach ($data_contact['data'] as $key => $value) {
            $value['number'] = $number++;
            $xtpl->assign('CHECK', $value['active'] == 1 ? 'checked' : '');
            $value['link_edit'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $value['id'];
            $value['link_delete'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;delete_id=' . $value['id'] . '&amp;delete_checkss=' . md5($value['id'] . NV_CACHE_PREFIX . $client_info['session_id']);
            $value['link_detail'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id_contact=' . $value['id'];
            $value['link_detail_org'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organizations&amp;view=' . $value['organizationsid'];
            $value['organizationsid'] = isset($array_organizations_all[$value['organizationsid']]) ? $array_organizations_all[$value['organizationsid']]['organizationname'] : '';
            $xtpl->assign('VIEW', $value);
            $xtpl->parse('main.view.loop');
        }
    } else {
        $xtpl->parse('main.view.empty');
    }
    $xtpl->parse('main.view');
}

if (!empty($error)) {
    if (!empty($row['organizationsid'])) {
        $params_org = [
            'organizationsid' => !empty($filter_organizations) ? $filter_organizations : $row['organizationsid']
        ];
        $array_organizations = nv_local_api('GetDetailOrganizations', $params_org, $admin_info['username'], 'crmbidding');
        $array_organizations = json_decode($array_organizations, true);
        if (!empty($array_organizations['data'])) {
            $xtpl->assign('ORG_EDIT', $array_organizations['data']);
            $xtpl->parse('main.selected_edit');
        }
    }
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

if (!empty($success)) {
    $xtpl->assign('SUCCESS', $success);
    $xtpl->parse('main.success');
}

foreach ($array_user_id_users as $value) {
    $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
    $xtpl->assign('OPTION', [
        'key' => $value['userid'],
        'title' => $value['username'] . ' (' . $fullname . ')',
    ]);
    $xtpl->parse('view_detail.select_affilacate_id');
    $xtpl->parse('view_detail.select_caregiver_id');
}
$xtpl->assign('ID_CREATE_LEAD', $id_contact);
$xtpl->parse('view_detail.type_contact');

foreach ($array_groups_leads as $value) {
    $xtpl->assign('OPTION', [
        'key' => $value['id'],
        'title' => $value['title'],
    ]);
    $xtpl->parse('view_detail.select_source_leads');
}

if ($nv_Request->isset_request('id_contact', 'post,get')) {
    $name_org = isset($array_organizations['data']['organizationname']) ? $array_organizations['data']['organizationname'] : '';
    $org_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=organizations&amp;view=' . $row['organizationsid'];
    $page_title = $nv_Lang->getModule('detail_contact');
    $xtpl->assign('BACK_URL', $base_url);
    $xtpl->assign('ORG_URL', $org_url);
    $xtpl->assign('NAME_ORG', $name_org);
    if ($row['convert_leads'] == 0) {
        $xtpl->assign('LINK_CONVERT_LEADS', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=add&id_contact=' . $row['id']);
        $xtpl->parse('view_detail.link_convert_leads');
    } else {
        $xtpl->assign('LINK_VIEW_LEADS', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $row['convert_leads']);
        $xtpl->parse('view_detail.link_view_leads');
    }
    $xtpl->parse('view_detail');
    $contents = $xtpl->text('view_detail');
} else {
    $xtpl->parse('main');
    $contents = $xtpl->text('main');
}

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
