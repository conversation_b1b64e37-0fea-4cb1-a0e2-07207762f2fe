<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\supportticket\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use NukeViet\Dauthau\Condition;
use NukeViet\Dauthau\Group;
use TicketStatus;

// use NukeViet\Dauthau\Order;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class ListAllTicket implements IApi
{

    private $result;
    private $page = 0;
    private $perpage = 0;
    private $total = 0;
    private $where = [];
    private $group_string = '';
    private $order_string = 'id DESC';

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'ticket';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $nv_Request;

        $this->page = $nv_Request->get_int('page', 'post', '1');
        $this->perpage = $nv_Request->get_int('perpage', 'post', '50');
        $use_elastic = $nv_Request->get_int('use_elastic', 'post', 0);
        if ($this->page <= 0 and $this->page > 9999) {
            return $this->result->setError()
                ->setCode('2005')
                ->setMessage('Page is integer, more than 0')
                ->getResult();
        }
        if ($this->perpage > 50) {
            return $this->result->setError()
                ->setCode('2002')
                ->setMessage('Perpage to larger')
                ->getResult();
        }

        $array_where = $nv_Request->get_array('where', 'post');
        if (!is_array($array_where)) {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Param where invalid to array')
                ->getResult();
        }

        $array_groups = $nv_Request->get_array('group', 'post');
        if (!is_array($array_groups)) {
            return $this->result->setError()
                ->setCode('2007')
                ->setMessage('Param group by invalid to json')
                ->getResult();
        }

        // key cho phép trong where
        $keys_check = [
            'id',
            'title',
            'cat_id',
            'content',
            'customer_id',
            'vip_id',
            'order_id',
            'label_ids',
            'assignee_to',
            'file_attach',
            'status',
            'point_price',
            'payment_time',
            'add_userid',
            'last_comment_userid',
            'last_comment_time',
            'add_time',
            'edit_time',
            'delete_time',
            'is_paid'
        ];

        $this->where[] = 'status>0';

        if (!empty($array_where)) {
            foreach ($array_where as $keys) {
                foreach ($keys as $key) {
                    $operator = array_key_first($key);
                    $field = array_key_first($key[$operator]);
                    if (!in_array($field, $keys_check)) {
                        return $this->result->setError()
                            ->setCode('2001')
                            ->setMessage('Missing field ' . $field . ' in data')
                            ->getResult();
                    }
                }
            }

            $condition = new Condition();
            $this->where[] = $condition->toSqlString($array_where);
        }

        if (!empty($array_groups)) {
            $group = new Group();
            $this->group_string = $group->toSqlString($array_groups);
        }

        try {
            $is_elas = false;

            // kiểm tra sử dụng elastic search hay mysql
            if ($is_elas and !$use_elastic) {
                $arr_data = $this->elastic();
            } else {
                $arr_data = $this->mysql();
            }
            if (!empty($arr_data)) {
                $this->result->setSuccess();
                $this->result->set('total', $this->total);
                $this->result->set('perpage', $this->perpage);
                $this->result->set('page', $this->page);
                $this->result->set('data', $arr_data);
            } else {
                $this->result->setSuccess()
                    ->setCode('4000')
                    ->setMessage('Empty data');
            }
        } catch (PDOException $e) {
            $this->result->setError()
                ->setCode('3000')
                ->setMessage(print_r($e, true));
        }

        return $this->result->getResult();
    }

    private function mysql()
    {
        global $db, $db_config, $array_ticket_labels, $array_ticket_cats, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_file = $module_info['module_file'];
        $module_data = $module_info['module_data'];

        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        //key select
        $keys_view = [
            'id',
            'title',
            'cat_id',
            'content',
            'customer_id',
            'vip_id',
            'order_id',
            'label_ids',
            'assignee_to',
            'file_attach',
            'status',
            'add_userid',
            'add_time',
            'edit_time',
            'delete_time',
            'is_paid'
        ];

        $db->sqlreset()
            ->select('COUNT(id) as num')
            ->from(TB_TICKET_ROW);

        if (!empty($this->where)) {
            $db->where(implode(' AND ', $this->where));
        }
        if (!empty($this->group_string)) {
            $db->group($this->group_string);
            $keys_view[] = 'count(id) as num';
        }

        $sth = $db->prepare($db->sql());
        $sth->execute();
        $this->total = $sth->fetchColumn();
        $db->select(implode(",", $keys_view));

        $db->order($this->order_string);
        if ($this->perpage > 0) {
            $db->limit($this->perpage)->offset(($this->page - 1) * $this->perpage);
        } 

        $sth = $db->prepare($db->sql());
        $sth->execute();
        $arr_data = [];
        while ($view = $sth->fetch()) {
            $view['link_detail'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . ($view['is_paid'] == 1 ? 'ticket_detail_paid' : 'ticket_detail') . '&amp;id=' . $view['id'];
            $view['cat_title'] = empty($array_ticket_cats[$view['cat_id']]) ? '' : $array_ticket_cats[$view['cat_id']]['title_' . NV_LANG_DATA];
            $view['status_display'] = TicketStatus::tryFrom($view['status'])?->getLabel();
            if (!empty($view['assignee_to']) && $view['is_paid'] != 1) {
                $assignee = [];
                $view['assignee_to'] = explode(',', $view['assignee_to']);
                foreach ($view['assignee_to'] as $userid) {
                    $assignee[] = get_user_info($userid);
                }
                $view['assignee'] = $assignee;
            } else {
                $view['assignee'] = [];
            }
            if (!empty($view['label_ids'])) {
                $label = [];
                $view['label_ids'] = explode(',', $view['label_ids']);
                foreach ($view['label_ids'] as $item) {
                    if (isset($array_ticket_labels[$item])) {
                        $label[] = $array_ticket_labels[$item];
                    }
                }
                $view['label'] = $label;
            } else {
                $view['label'] = [];
            }
            $arr_data[] = $view;
        }

        return $arr_data;
    }

    private function elastic()
    {
        return [];
    }
}
