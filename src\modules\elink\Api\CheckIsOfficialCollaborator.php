<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */
namespace NukeViet\Module\elink\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CheckIsOfficialCollaborator implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'affiliate';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $global_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        // Các thiết lập
        $sql = 'SELECT config_name, config_value FROM ' . $db_config['prefix'] . '_' . $module_data . '_config';
        $list = $nv_Cache->db($sql, '', $module_name);
        $global_elink_config = [];
        foreach ($list as $values) {
            $global_elink_config[$values['config_name']] = $values['config_value'];
        }

        $userid = $nv_Request->get_absint('userid', 'post', 0);
        if (empty($userid)) {
            return $this->result->setCode('1001')
                ->setMessage($nv_Lang->getModule('apierror_u'))
                ->getResult();
        }

        // Xác định xem cộng tác viên có phải chính thức hay không
        $official_collaborator = 0;

        // với admin thì không tính là CTV chính thức vì admin tính % kiểu khác
        $admin_id = $db->query('SELECT admin_id FROM ' . NV_AUTHORS_GLOBALTABLE . ' WHERE admin_id=' . $userid)->fetchColumn();
        if (!empty($admin_id)) {
            $official_collaborator = 0;
        } else {
            // Lấy trong nhóm
            $sql = "SELECT userid FROM " . NV_USERS_GLOBALTABLE . "_groups_users WHERE userid=" . $userid . " AND group_id=" . $global_elink_config['group_id_collaborator'];
            $uid = $db->query($sql)->fetchColumn();
            if (!empty($uid)) {
                $official_collaborator = 1;
            }

            // Lấy từ tài khoản
            if (empty($official_collaborator)) {
                $sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . " WHERE userid=" . $userid;
                $user = $db->query($sql)->fetch();
                if (!empty($user)) {
                    $user['in_groups'] = explode(',', $user['in_groups']);
                    if ($user['group_id'] == $global_elink_config['group_id_collaborator']) {
                        $official_collaborator = 1;
                    } elseif (in_array($global_elink_config['group_id_collaborator'], $user['in_groups'])) {
                        $official_collaborator = 1;
                    }
                }
            }
        }

        $this->result->set('data', $official_collaborator);
        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
