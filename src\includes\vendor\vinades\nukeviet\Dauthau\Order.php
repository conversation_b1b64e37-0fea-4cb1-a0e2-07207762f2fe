<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 24/4/2020, 20:6
 */
namespace NukeViet\Dauthau;

class Order
{

    const MODE_DESC = 'DESC';

    const MODE_ASC = 'ASC';

    private $offset = -1;

    private $orders = [];

    /**
     *
     * @return
     */
    public function add()
    {
        $this->offset++;
        $this->orders[$this->offset] = [];
        return $this;
    }

    /**
     *
     * @param string $field
     * @return
     */
    public function setField($field)
    {
        $this->orders[$this->offset]['field'] = $field;
        return $this;
    }

    /**
     *
     * @return
     */
    public function setType($Type)
    {
        if ($Type != self::MODE_ASC) {
            $Type = self::MODE_DESC;
        } else {
            $Type = self::MODE_ASC;
        }
        $this->orders[$this->offset]['type'] = $Type;
        return $this;
    }

    /**
     *
     * @return string
     */
    public function toText()
    {
        $text = array();
        foreach ($this->orders as $row) {
            $text[] = $row['field'] . ' ' . $row['type'];
        }
        return implode(', ', $text);
    }

    /**
     * Order::toSqlString()
     * params
     * $array_order[] = [
     * field=> TypeAsc
     * ]
     *
     * @return string
     */
    public function toSqlString($array_order)
    {
        foreach ($array_order as $field => $type) {
            if (isset($field) and isset($type)) {
                $this->add()
                ->setField($field)
                ->setType($type);
            }
        }
        if (!empty($this->orders)) {
            $sql = $this->toText();
            return $sql;
        }
        return false;
    }
}
