<!-- BEGIN: main -->
<!-- BEGIN: error -->
<div class="alert alert-warning">
    {ERROR}
</div>
<!-- END: error -->
<!-- BEGIN: bank_info -->
<div class="row">
    <div class="col-md-24">
        <table class="table table-striped table-bordered table-hover table-responsive">
            <thead>
                <tr>
                    <th>STT</th>
                    <th>{LANG.title_bank}</th>
                    <th>{LANG.account_holder}</th>
                    <th>{LANG.account_number}</th>
                    <th>{LANG.bank_name}</th>
                    <th>{LANG.description_content}</th>
                    <th>{LANG.note}</th>
                    <th>{LANG.show}</th>
                    <th>{LANG.action}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td>{loop.stt}</td>
                    <td>{loop.title}</td>
                    <td>{loop.account_holder}</td>
                    <td>
                        <div><small class="text-muted">{loop.bank_shortname}</small></div>
                        {loop.account_number}
                    </td>
                    <td>{loop.bank_name}</td>
                    <td>{loop.description_content}</td>
                    <td>{loop.note}</td>
                    <td>
                        <input type="checkbox" name="show" value="{loop.id}" {loop.status} onchange="nv_change_status({loop.id});">
                    </td>
                    <td>
                        <a class="btn btn-primary btn-sm" href="{loop.link_update}">
                            <i class="fa fa-edit"></i> {LANG.updatetime}
                        </a>
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
        <hr>
    </div>
</div>
<!-- END: bank_info -->

<div class="row">
    <div class="col-md-24">
        <h1 class="text-center">{LANG.title_form}</h1>
    </div>
    <div class="col-md-24">
        <form action="" class="form-horizontal" method="post">
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-10 col-lg-4"><strong>{LANG.title_bank} <sup>({LANG.lang_vi})</sup><span class="text-danger">(*)</span></strong></label>
                <div class="col-sm-14 col-md-14 col-lg-20">
                    <div class="cfg-msys">
                        <input type="text" class="form-control" name="title_vi" value="{bank_info_update.title_vi}">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-10 col-lg-4"><strong>{LANG.title_bank} <sup>({LANG.lang_en})</sup><span class="text-danger">(*)</span></strong></label>
                <div class="col-sm-14 col-md-14 col-lg-20">
                    <div class="cfg-msys">
                        <input type="text" class="form-control" name="title_en" value="{bank_info_update.title_en}">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-10 col-lg-4"><strong>{LANG.account_holder}  <sup>({LANG.lang_vi})</sup><span class="text-danger">(*)</span></strong></label>
                <div class="col-sm-14 col-md-14 col-lg-20">
                    <div class="cfg-msys">
                        <input type="text" class="form-control" name="account_holder_vi" value="{bank_info_update.account_holder_vi}">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-10 col-lg-4"><strong>{LANG.account_holder}  <sup>({LANG.lang_en})</sup><span class="text-danger">(*)</span></strong></label>
                <div class="col-sm-14 col-md-14 col-lg-20">
                    <div class="cfg-msys">
                        <input type="text" class="form-control" name="account_holder_en" value="{bank_info_update.account_holder_en}">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-10 col-lg-4"><strong>{LANG.account_number}<span class="text-danger">(*)</span></strong></label>
                <div class="col-sm-14 col-md-14 col-lg-20">
                    <div class="cfg-msys">
                        <input type="text" class="form-control" name="account_number" value="{bank_info_update.account_number}">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-10 col-lg-4"><strong>{LANG.bank_name} <sup class="">({LANG.lang_vi})</sup><span class="text-danger">(*)</span></strong></label>
                <div class="col-sm-14 col-md-14 col-lg-20">
                    <div class="cfg-msys">
                        <input type="text" class="form-control" name="bank_name_vi" value="{bank_info_update.bank_name_vi}">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-10 col-lg-4"><strong>{LANG.bank_name} <sup class="">({LANG.lang_en})</sup><span class="text-danger">(*)</span></strong></label>
                <div class="col-sm-14 col-md-14 col-lg-20">
                    <div class="cfg-msys">
                        <input type="text" class="form-control" name="bank_name_en" value="{bank_info_update.bank_name_en}">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-10 col-lg-4"><strong>{LANG.bank_shortname} <sup class="">({LANG.lang_vi})</sup><span class="text-danger">(*)</span></strong></label>
                <div class="col-sm-14 col-md-14 col-lg-20">
                    <div class="cfg-msys">
                        <input type="text" class="form-control" name="bank_shortname_vi" value="{bank_info_update.bank_shortname_vi}">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-10 col-lg-4"><strong>{LANG.bank_shortname} <sup class="">({LANG.lang_en})</sup><span class="text-danger">(*)</span></strong></label>
                <div class="col-sm-14 col-md-14 col-lg-20">
                    <div class="cfg-msys">
                        <input type="text" class="form-control" name="bank_shortname_en" value="{bank_info_update.bank_shortname_en}">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-10 col-lg-4"><strong>{LANG.description_content} <sup class="">({LANG.lang_vi})</sup></strong></label>
                <div class="col-sm-14 col-md-14 col-lg-20">
                    <div class="cfg-msys">
                        <textarea class="form-control" name="description_content_vi" id="" cols="30" rows="10">{bank_info_update.description_content_vi}</textarea>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-10 col-lg-4"><strong>{LANG.description_content} <sup class="">({LANG.lang_en})</sup></strong></label>
                <div class="col-sm-14 col-md-14 col-lg-20">
                    <div class="cfg-msys">
                        <textarea class="form-control" name="description_content_en" id="" cols="30" rows="10">{bank_info_update.description_content_en}</textarea>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-10 col-lg-4"><strong>{LANG.note} <sup class="">({LANG.lang_vi})</sup></strong></label>
                <div class="col-sm-14 col-md-14 col-lg-20">
                    <div class="cfg-msys">
                        <textarea class="form-control" name="note_vi" id="" cols="30" rows="10">{bank_info_update.note_vi}</textarea>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-10 col-lg-4"><strong>{LANG.note} <sup class="">({LANG.lang_en})</sup></strong></label>
                <div class="col-sm-14 col-md-14 col-lg-20">
                    <div class="cfg-msys">
                        <textarea class="form-control" name="note_en" id="" cols="30" rows="10">{bank_info_update.note_en}</textarea>
                    </div>
                </div>
            </div>
            <div class="col-sm-24 col-md-24 col-lg-24 text-center">
                <input class="btn btn-primary" type="submit" value="{LANG.save}" name="submit" />
            </div>
        </form>
    </div>
</div>

<div class="wrapper hide">
    <div class="toast">
        <div class="content">
            <div class="icon"><i class="uil uil-wifi"></i></div>
                <div class="details">
                    <span></span>
                    <p></p>
                </div>
            </div>
        <div class="close-icon"><i class="fa fa-times"></i></div>
    </div>
</div>
<!-- END: main -->
