<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\calendar\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use PDO;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    exit('Stop!!!');
}

class GetEventsOfDay implements IApi
{
    private $result;

    /**
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     * @return string
     */
    public static function getCat()
    {
        return 'Calendar';
    }

    /**
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];

        $month = $nv_Request->get_int('month', 'post', 0);
        $day = $nv_Request->get_int('day', 'post', 0);
        $year = $nv_Request->get_int('year', 'post', nv_date('Y'));
        $calendar_id = $nv_Request->get_int('calendar_id', 'post', 0);
        
        if ($month <= 0 || $month > 12 || $day <= 0 || $day > 31) {
            return $this->result->setError()->setCode('0001')->setMessage('Ngày tháng không hợp lệ!!!')->getResult();
        }

        $list = [];
        $date_pattern = sprintf('%02d/%02d/%04d', $day, $month, $year);
        $sql_tmp = "SELECT event_id FROM " . NV_PREFIXLANG . "_" . $module_data . "_repeats WHERE date_pattern = '" . $date_pattern . "'";
        $result_tmp = $db->query($sql_tmp);
        $event_ids = [];
        while ($row_tmp = $result_tmp->fetch()) {
            $event_ids[] = $row_tmp['event_id'];
        }
        
        if (!empty($event_ids)) {
            $event_id_list = implode(',', $event_ids);
            $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_event WHERE id IN (" . $event_id_list . ") AND calendar_id = " . $calendar_id . " AND status = 1";
            $result = $db->query($sql);
            
            while ($row = $result->fetch()) {
                $list[] = $row;
            }
        }

        $this->result->setSuccess();
        $this->result->set('data', $list);
        return $this->result->getResult();
    }
}
