<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2024 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Thu, 11 Dec 2024 08:48:59 GMT
 */

if (!isset($array_op[1]) || !is_numeric($array_op[1])) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}

if (!defined('NV_IS_USER')) {
    $redirect = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name, true);
    Header("Location: " . NV_BASE_SITEURL . "index.php?" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($redirect));
    die();
}

$page_title = $nv_Lang->getModule('log_econtract');
$key_words = $module_info['keywords'];
$array_mod_title[] = array(
    'title' => $nv_Lang->getModule('log_econtract'),
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op, true)
);
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['version-econtract'] . '/' . $array_op[1];

$econtract = $db->query('SELECT id, contract_no, current_version FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $array_op[1] . ' AND customer_id=' . $user_info['userid'])->fetch();
if (empty($econtract)) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}
$check_change_econtract = $nv_Lang->getModule('title_ssecontract', $econtract['contract_no']);
if ($nv_Request->get_title('action', 'post') == 'view_change_econtract') {
    $str_ids = $nv_Request->get_title('ids', 'post', '');
    if (!empty($str_ids)) {
        $arr_ids = explode('-', trim($str_ids));
        if (intval($arr_ids[0] > 0 && intval($arr_ids[1]) > 0)) {
            $data = [
                'version_1' => get_data_contract($econtract['id'], $econtract['contract_no'], $arr_ids[0]),
                'version_2' => get_data_contract($econtract['id'], $econtract['contract_no'], $arr_ids[1])
            ];
            nv_jsonOutput([
                'res' => 'success',
                'data' => $data
            ]);
        } else {
            $arr_ids = explode('-', trim($str_ids));
            nv_jsonOutput([
                'res' => 'error',
                'data' => $nv_Lang->getModule('data_invalid')
            ]);
        }
    } else {
        nv_jsonOutput([
            'res' => 'error',
            'data' => $nv_Lang->getModule('error_notfound_company')
        ]);
    }
}

$userid = $user_info['userid'];
$where[] = 'econtract_id=' . $array_op[1];

$data = [];
$per_page = 15;
$page = $nv_Request->get_int('page', 'post,get', 1);

$page_url = $base_url;
if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
}

// Lấy tổng số lượng rows
$db->sqlreset()
    ->select('COUNT(*)')
    ->from(NV_PREFIXLANG . '_' . $module_data . '_econtract_versions');

if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}
$sth = $db->prepare($db->sql());
$sth->execute();
$num_items = $sth->fetchColumn();

if ($num_items) {
    betweenURLs($page, ceil($num_items / $per_page), $base_url, '&amp;page=', $prevPage, $nextPage);
}

$db->select('*')
    ->from(NV_PREFIXLANG . '_' . $module_data . '_econtract_versions')
    ->order('created_at DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);

$sth = $db->prepare($db->sql());
$sth->execute();

$stt = ($page - 1) * $per_page;
while ($row = $sth->fetch()) {
    $row['stt'] = ++$stt;
    $row['user_update'] = $nv_Lang->getModule('system');
    if (isset($row['user_id'])) {
        $user_update = $db->query('SELECT first_name, last_name, userid  FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $row['user_id'])->fetch();
        $row['user_update'] = nv_show_name_user($user_update['first_name'], $user_update['last_name'], $user_update['userid']) ?? $nv_Lang->getModule('system');
    }
    $row['checked'] = $row['id'] == $econtract['current_version'] ? 'checked' : '';
    $contract_title = $econtract['contract_no'] . '-' . str_pad($row['version'], 2, '0', STR_PAD_LEFT);
    $link_contract = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['doc-econtract'] . '/' . $row['econtract_id'] . '/' . $row['id'];
    $view_contract = '<a href="' . $link_contract . '" target="_blank"> ' . $contract_title . '</a>';
    $row['contract_title'] = $nv_Lang->getModule('version') . ' ' . $view_contract . ($row['id'] == $econtract['current_version'] ? ' - ' . $nv_Lang->getModule('official_contract') : '');
    $row['created_at'] = nv_date('H:i:s d/m/Y', $row['created_at']);
    // Lấy thông tin log thay đổi
    $data[] = $row;
}

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
$canonicalUrl = getCanonicalUrl($page_url);

$contents = nv_theme_version_econtract($data, $generate_page, $check_change_econtract);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';

function get_data_contract($econtract_id, $econtract_no, $version_id)
{
    global $db, $module_data, $module_info, $nv_Lang;
    $data = [];
    $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions WHERE econtract_id=' . $econtract_id . ' AND id=' . $version_id)->fetch();
    if (!empty($result)) {
        $result_data = json_decode($result['contract_data'], true);
        $data['contract_no'] = $econtract_no;
        $data['version'] = $result['version'];
        $data['customer_type'] = $nv_Lang->getModule($result_data['customer_type'] == 0 ? 'individual' : 'company');
        $data['c_name'] = $result_data['c_name'];
        $data['cccd'] = $result_data['cccd'];
        $data['tax_code'] = $result_data['tax_code'];
        $data['representative'] = $result_data['representative'];
        $data['jobtitle'] = $result_data['jobtitle'];
        $data['phone'] = $result_data['phone'];
        $data['email'] = $result_data['email'];
        $data['c_address'] = $result_data['c_address'];
        $data['authorization_letter'] = $result_data['authorization_letter'];
        $data['receiver'] = $result_data['receiver'];
        $data['receiver_phone'] = $result_data['receiver_phone'];
        $data['receiver_address'] = $result_data['receiver_address'];
        $data['contract_path'] = empty($result['pdf_path']) ? '' : basename($result['pdf_path']);
        $data['contract_path'] = '';
        if (!empty($result['pdf_path'])) {
            $arr_filename_contract = explode('/', $result['pdf_path']);
            $contract_attachment = end($arr_filename_contract);
            $url_download_contract = NV_MAIN_DOMAIN . '/' . $result['pdf_path'];
            $file_path = NV_ROOTDIR . '/' . $result['pdf_path'];
            if (file_exists($file_path)) {
                $data['contract_path'] = '<a href="' . $url_download_contract . '" data-toggle="tooltip" title="' . $nv_Lang->getModule('click_download_attachment') . '" download="' . $contract_attachment . '">
                    <em class="fa fa-paperclip"></em> <span>' . $nv_Lang->getModule('attachment') . '</span>
                    </a>';
            }
        }
    }
    $xtpl = new XTemplate('version-change.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    if (!empty($data)) {
        $xtpl->assign('DATA', $data);
        $xtpl->parse('main.data');
    } else {
        $xtpl->parse('main.no_data');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}
