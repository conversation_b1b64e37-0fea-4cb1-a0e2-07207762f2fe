<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 16 Jan 2018 07:25:26 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$savesetting = $nv_Request->get_int('savesetting', 'post', 0);
$error = array();
$arr_config = $module_config[$module_name];

if ($savesetting == 1) {
    $error = array();
    $data = array();

    $ids = $nv_Request->get_array('ids', 'post', array());
    foreach ($ids as $key) {
        $data[$key]['exchange_money'] = $nv_Request->get_int('exchange_money_' . $key, 'post', 0);
        $data[$key]['exchange_point'] = $nv_Request->get_int('exchange_point_' . $key, 'post', 0);

        $_config_points = $db->query("SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_config WHERE id=" . $key)->fetch();
        if (empty($_config_points)) {
            $sth = $db->prepare("INSERT INTO " . $db_config['prefix'] . "_" . $module_data . "_config (id, exchange_money, exchange_point) VALUES (:id, :exchange_money, :exchange_point)");
        } else {
            $sth = $db->prepare("UPDATE " . $db_config['prefix'] . "_" . $module_data . "_config SET exchange_money = :exchange_money, exchange_point = :exchange_point WHERE id = :id");
        }
        $sth->bindParam(':id', $key, PDO::PARAM_INT);
        $sth->bindParam(':exchange_money', $data[$key]['exchange_money'], PDO::PARAM_INT);
        $sth->bindParam(':exchange_point', $data[$key]['exchange_point'], PDO::PARAM_INT);
        $sth->execute();
    }

    $arr_config['new_user'] = $nv_Request->get_int('new_user', 'post', 0);
    $arr_config['new_phone'] = $nv_Request->get_int('new_phone', 'post', 0);
    $arr_config['new_tax'] = $nv_Request->get_int('new_tax', 'post', 0);
    $arr_config['new_user_aff'] = $nv_Request->get_int('new_user_aff', 'post', 0);
    $arr_config['new_phone_aff'] = $nv_Request->get_int('new_phone_aff', 'post', 0);
    $arr_config['new_tax_aff'] = $nv_Request->get_int('new_tax_aff', 'post', 0);
    $arr_config['new_login'] = $nv_Request->get_int('new_login', 'post', 0);
    $arr_config['login_10m'] = $nv_Request->get_int('login_10m', 'post', 0);
    $arr_config['expired_time'] = $nv_Request->get_int('expired_time', 'post', 0);
    $arr_config['expired'] = $nv_Request->get_int('expired', 'post', 0);
    $arr_config['use_give_points'] = $nv_Request->get_int('use_give_points', 'post', '0');
    $arr_config['limit_give_points'] = $nv_Request->get_int('limit_give_points', 'post', '0');
    $arr_config['ratio_give_points'] = $nv_Request->get_int('ratio_give_points', 'post', '0');
    $arr_config['point_crawl_plus'] = $nv_Request->get_int('point_crawl_plus', 'post', '0');
    $arr_config['point_crawl_mintus'] = $nv_Request->get_int('point_crawl_mintus', 'post', '0');
    $arr_config['expired_crawl'] = $nv_Request->get_int('expired_crawl', 'post', '0');

    $sth = $db->prepare("UPDATE " . NV_CONFIG_GLOBALTABLE . " SET config_value = :config_value WHERE lang = '" . NV_LANG_DATA . "' AND module = :module_name AND config_name = :config_name");

    $sth->bindParam(':module_name', $module_name, PDO::PARAM_STR);
    foreach ($arr_config as $config_name => $config_value) {
        $sth->bindParam(':config_name', $config_name, PDO::PARAM_STR);
        $sth->bindParam(':config_value', $config_value, PDO::PARAM_STR);
        $sth->execute();
    }

    nv_insert_logs(NV_LANG_DATA, $module_name, NV_LANG_DATA, "Setting", $admin_info['userid']);

    $nv_Cache->delMod('settings');
    $nv_Cache->delMod($module_name);
    Header("Location: " . NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=config');
    die();
}

$config_points = $db->query("SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_config");
$config_points_array = array();
while ($_row = $config_points->fetch()) {
    $config_points_array[$_row['id']] = $_row;
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('DATA', $arr_config);

if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

if (empty($config_points_array)) {
    $xtpl->assign('KEY', 1);
    $xtpl->assign('CONFIG_WEIGHT_COUNT', 1);
    $xtpl->parse('main.loop');
} else {
    foreach ($config_points_array as $_row) {
        $xtpl->assign('KEY', $_row['id']);
        $xtpl->assign('CONFIG', $_row);
        $xtpl->parse('main.loop');
    }
    $xtpl->assign('CONFIG_WEIGHT_COUNT', sizeof($config_points_array));
}
$xtpl->assign('USE_GIVE_POINTS', $arr_config['use_give_points'] ? ' checked="checked"' : '');
$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('config');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
