<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$showheader = $nv_Request->get_int('showheader', 'post,get', 1);
$sdt = $nv_Request->get_title('sdt', 'get' , '');
$loai_cuoc_goi = $nv_Request->get_int('loai_cuoc_goi', 'post,get', -1);
$trang_thai_cuoc_goi = $nv_Request->get_int('trang_thai_cuoc_goi', 'post,get', -1);
if (empty($sdt)) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=list_mobiphone');
}
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&showheader=' . $showheader . '&sdt=' . $sdt;
$where = [];
if (!empty($sdt)) {
    $where[] = 'sdt LIKE :q_sdt ';
}
if ($loai_cuoc_goi != -1) {
    $where[] = 'loai_cuoc_goi = :loai_cuoc_goi';
    $base_url .= '&loai_cuoc_goi=' . $loai_cuoc_goi;
}
if ($trang_thai_cuoc_goi != -1) {
    $where[] = 'trang_thai_cuoc_goi = :trang_thai_cuoc_goi';
    $base_url .= '&loai_cuoc_goi=' . $trang_thai_cuoc_goi;
}
// Fetch Limit
$per_page = 50;
$page = $nv_Request->get_int('page', 'post,get', 1);
$db->sqlreset()
    ->select('COUNT(*)')
    ->from('' . NV_PREFIXLANG . '_' . $module_data . '_mobiphone');
if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}
$sth = $db->prepare($db->sql());
if (!empty($sdt)) {
    $sth->bindValue(':q_sdt', '%' . $sdt);
}
if ($loai_cuoc_goi != -1) {
    $sth->bindValue(':loai_cuoc_goi', $loai_cuoc_goi);
}
if ($trang_thai_cuoc_goi != -1) {
    $sth->bindValue(':trang_thai_cuoc_goi', $trang_thai_cuoc_goi);
}
$sth->execute();
$num_items = $sth->fetchColumn();
$order = 'thoi_gian_bat_dau DESC';
$db->select('*')
    ->order($order)
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}
$sth = $db->prepare($db->sql());
if (!empty($sdt)) {
    $sth->bindValue(':q_sdt', '%' . $sdt . '%');
}
if ($loai_cuoc_goi != -1) {
    $sth->bindValue(':loai_cuoc_goi', $loai_cuoc_goi);
}
if ($trang_thai_cuoc_goi != -1) {
    $sth->bindValue(':trang_thai_cuoc_goi', $trang_thai_cuoc_goi);
}
$sth->execute();
$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('SDT', $sdt);

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
if (!empty($generate_page)) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}
$number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;
while ($view = $sth->fetch()) {
    $view['thoi_gian_bat_dau'] = nv_date('H:i d/m/Y', $view['thoi_gian_bat_dau']);
    $view['number'] = $number++;
    $view['loai'] = ($view['loai_cuoc_goi'] == 0) ? $nv_Lang->getModule('goi_ra') : $nv_Lang->getModule('goi_vao');
    $view['trang_thai_cuoc_goi'] = ($view['trang_thai_cuoc_goi'] == 0) ? $nv_Lang->getModule('goi_nho') : $nv_Lang->getModule('goi_gap');
    $view['thoi_gian'] = $nv_Lang->getModule('time_start') . ':' . $view['thoi_gian_bat_dau'] . '</br>' .  $nv_Lang->getModule('time_finish') . ':'. nv_date('H:i d/m/Y', $view['thoi_gian_ket_thuc']);
    if ($view['link_s3'] != '' and preg_match('/^20/', $view['link_s3'], $m)) {
        $view['link'] = 'https://s3.ap-southeast-1.amazonaws.com/dauthau.asia/uploads/mobiphone/' . $view['link_s3'];
    }
    $xtpl->assign('VIEW', $view);
    if ($view['link'] != '') {
        $xtpl->parse('main.loop.recording');
        $xtpl->parse('main.loop.download');
    }
    $xtpl->parse('main.loop');
}
$array_loai_cuoc_goi = [
    '0' => $nv_Lang->getModule('goi_ra'),
    '1' => $nv_Lang->getModule('goi_vao'),
];
foreach ($array_loai_cuoc_goi as $key => $value) {
    $xtpl->assign('LOAI', array(
        'id' => $key,
        'title' => $value,
        'selected' => $key == $loai_cuoc_goi ? 'selected="selected"' : ''
    ));
    $xtpl->parse('main.search.array_loai_cuoc_goi');
}
$array_trang_thai_cuoc_goi = [
    '0' => $nv_Lang->getModule('goi_nho'),
    '1' => $nv_Lang->getModule('goi_gap'),
];
foreach ($array_trang_thai_cuoc_goi as $key => $value) {
    $xtpl->assign('STATUS', array(
        'id' => $key,
        'title' => $value,
        'selected' => $key == $trang_thai_cuoc_goi ? 'selected="selected"' : ''
    ));
    $xtpl->parse('main.search.array_trang_thai_cuoc_goi');
}
if ($showheader) {
    $xtpl->parse('main.search');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');
$page_title = $nv_Lang->getModule('khach_mobiphone');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';
