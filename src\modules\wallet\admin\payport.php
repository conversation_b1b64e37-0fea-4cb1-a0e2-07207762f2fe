<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('setup_payment');

/**
 * drawselect_number()
 *
 * @param string $select_name
 * @param integer $number_start
 * @param integer $number_end
 * @param integer $number_curent
 * @param string $func_onchange
 * @return
 */
function drawselect_number($select_name = "", $number_start = 0, $number_end = 1, $number_curent = 0, $func_onchange = "")
{
    $html = "<select class=\"form-control\" name=\"" . $select_name . "\" onchange=\"" . $func_onchange . "\">";

    for ($i = $number_start; $i < $number_end; $i++) {
        $select = ($i == $number_curent) ? "selected=\"selected\"" : "";
        $html .= "<option value=\"" . $i . "\"" . $select . ">" . $i . "</option>";
    }

    $html .= "</select>";

    return $html;
}

// Các cổng thanh toán trong CSDL
$array_setting_payment = [];
$sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_payment ORDER BY weight ASC";
$result = $db->query($sql);
$all_page = $result->rowCount();
while ($row = $result->fetch()) {
    $row['paymentname'] = $row['paymentname_' . NV_LANG_DATA];
    $row['bodytext'] = $row['bodytext_' . NV_LANG_DATA];
    $row['term'] = $row['term_' . NV_LANG_DATA];
    $array_setting_payment[$row['payment']] = $row;
}

// Các cổng thanh toán trên máy chủ
$check_config_payment = "/^([a-zA-Z0-9\-\_]+)\.config\.ini$/";
$payment_funcs = nv_scandir(NV_ROOTDIR . '/modules/' . $module_file . '/payment', $check_config_payment);

if (!empty($payment_funcs)) {
    $payment_funcs = preg_replace($check_config_payment, "\\1", $payment_funcs);
}

$array_setting_payment_key = array_keys($array_setting_payment);
$array_payment_other = [];

foreach ($payment_funcs as $payment) {
    $xml = simplexml_load_file(NV_ROOTDIR . '/modules/' . $module_file . '/payment/' . $payment . '.config.ini');

    if ($xml !== false) {
        $xmlconfig = $xml->xpath('config');

        $config = $xmlconfig[0];
        $array_config = [];
        $array_config_title = [];
        $array_config_note = [];

        foreach ($config as $key => $value) {
            $config_attr = $value->attributes();
            $array_config_title[$key] = !empty($array_config_title[$key]) ? $array_config_title[$key] : $key;
            if (isset($config_attr['lang']) && !empty($config_attr['lang'])) {
                $array_config[$key] = !empty($array_config[$key]) ? $array_config[$key] : [];
                $array_config[$key][(string)$config_attr['lang']] = trim($value);

                $array_config_title[$key] = $array_config_title[$key] == $key && (string) $config_attr['lang'] == NV_LANG_INTERFACE && !empty($config_attr['lang_' . NV_LANG_INTERFACE])  ? (string) $config_attr['lang_' . NV_LANG_INTERFACE]  : $array_config_title[$key];

                $array_config_note[$key] = empty($array_config_note[$key]) && (string) $config_attr['lang'] == NV_LANG_INTERFACE && !empty($config_attr['note_' . NV_LANG_INTERFACE])  ? (string) $config_attr['note_' . NV_LANG_INTERFACE]  : '';
            } else {
                $array_config[$key] = trim($value);
                $array_config_title[$key] = !empty($config_attr['lang_' . NV_LANG_INTERFACE]) ? (string) $config_attr['lang_' . NV_LANG_INTERFACE]  : $key;
                $array_config_note[$key] = !empty($config_attr['note_' . NV_LANG_INTERFACE]) ? (string) $config_attr['note_' . NV_LANG_INTERFACE]  : '';
            }
        }

        $array_payment_other[$payment] = [
            'payment' => $payment,
            'paymentname' => trim($xml->name),
            'domain' => trim($xml->domain),
            'images_button' => str_replace('[NV_BASE_SITEURL]', NV_BASE_SITEURL, trim($xml->images_button)),
            'config' => $array_config,
            'titlekey' => $array_config_title,
            'notekey' => $array_config_note,
            'currency_support' => trim($xml->currency),
            'allowedoptionalmoney' => intval($xml->optional) ? 1 : 0
        ];

        unset($config, $xmlconfig, $xml);
    }
}

$data_pay = [];

// Lấy dữ liệu khi tích hợp cổng thanh toán mới
$payment = $nv_Request->get_string('payment', 'get', '');
if (!empty($payment)) {
    // Get data have not in database
    if (!in_array($payment, $array_setting_payment_key)) {
        if (!empty($array_payment_other[$payment])) {
            $weight = $db->query("SELECT max(weight) FROM " . $db_config['prefix'] . "_" . $module_data . "_payment")->fetchColumn();
            $weight = intval($weight) + 1;

            $sql = "REPLACE INTO " . $db_config['prefix'] . "_" . $module_data . "_payment (
                payment, paymentname_" . NV_LANG_DATA . ", domain, active, weight, config,images_button, bodytext_" . NV_LANG_DATA . ", term_" . NV_LANG_DATA . ", currency_support, allowedoptionalmoney
            ) VALUES (
                " . $db->quote($payment) . ", " . $db->quote($array_payment_other[$payment]['paymentname']) . ",
                " . $db->quote($array_payment_other[$payment]['domain']) . ", '0', '" . $weight . "',
                '" . nv_base64_encode(serialize($array_payment_other[$payment]['config'])) . "',
                " . $db->quote($array_payment_other[$payment]['images_button']) . ", '', '', " . $db->quote($array_payment_other[$payment]['currency_support']) . ",
                " . $array_payment_other[$payment]['allowedoptionalmoney'] . "
            )";
            $db->query($sql);

            $nv_Cache->delMod($module_name);
        }
    }

    // Get data have in database
    $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_payment WHERE payment=" . $db->quote($payment);
    $result = $db->query($sql);
    $data_pay = $result->fetch();
    $data_pay['paymentname'] = $data_pay['paymentname_' . NV_LANG_DATA];
    $data_pay['bodytext'] = $data_pay['bodytext_' . NV_LANG_DATA];
    $data_pay['term'] = $data_pay['term_' . NV_LANG_DATA];
}

if ($nv_Request->isset_request('saveconfigpaymentedit', 'post')) {
    $payment = $nv_Request->get_title('payment', 'post', '', 0);
    $paymentname = $nv_Request->get_title('paymentname', 'post', '', 0);
    $domain = $nv_Request->get_title('domain', 'post', '', 0);
    $images_button = $nv_Request->get_title('images_button', 'post', '', 0);
    $active = $nv_Request->get_int('active', 'post', 0);
    $array_config = $nv_Request->get_array('config', 'post', []);
    $bodytext = $nv_Request->get_editor('bodytext', '', NV_ALLOWED_HTML_TAGS);
    $bodytext = nv_editor_nl2br($bodytext);
    $term = $nv_Request->get_editor('term', '', NV_ALLOWED_HTML_TAGS);
    $term = nv_editor_nl2br($term);
    $discount = $nv_Request->get_float('discount', 'post', '', 0);
    $discount_transaction = $nv_Request->get_float('discount_transaction', 'post', '', 0);

    if ($discount >= 100) {
        $discount = 0;
    }

    $active_completed_email = (int)$nv_Request->get_bool('active_completed_email', 'post', false);
    $active_incomplete_email = (int)$nv_Request->get_bool('active_incomplete_email', 'post', false);

    if (!empty($images_button) and preg_match('/^' . nv_preg_quote(NV_BASE_SITEURL . NV_UPLOADS_DIR . '/') . '/', $images_button)) {
        $lu = strlen(NV_BASE_SITEURL . NV_UPLOADS_DIR . "/" . $module_name . "/");
        $images_button = substr($images_button, $lu);
    }

    /**
     * Đối với những cấu hình riêng cho các ngôn ngữ khác nhau được lưu trong CSDL thì nó được lưu ở dạng array
     * ['vi' => 'xxx', 'en' => 'xxxx'] // Xem file readme.md của module.
     * Do đó cần lấy cấu hình đó ra và chỉ thay đổi ở vị trí ngôn ngữ thích ứng
     */
    list($config_old) = $db->query("SELECT config FROM " . $db_config['prefix'] . "_" . $module_data . "_payment WHERE payment=" . $db->quote($payment))->fetch(3);
    $config_old = !empty($config_old) ? unserialize(nv_base64_decode($config_old)) : '';
    foreach ($array_config as $key => $value) {
        if (!empty($config_old[$key]) && is_array($config_old[$key])) {
            $array_config[$key] = $config_old[$key];
            $array_config[$key][NV_LANG_DATA] = $value;
        }
    }

    $sql = "UPDATE " . $db_config['prefix'] . "_" . $module_data . "_payment
        SET paymentname_" . NV_LANG_DATA . " = " . $db->quote($paymentname) . ", domain = " . $db->quote($domain) . ",
        active=" . $active . ", config = '" . nv_base64_encode(serialize($array_config)) . "',
        images_button=" . $db->quote($images_button) . ", bodytext_" . NV_LANG_DATA . "=" . $db->quote($bodytext) . ", term_" . NV_LANG_DATA ."=" . $db->quote($term) . ",
        discount = " . $discount . ", discount_transaction= " . $discount_transaction . ",
        active_completed_email=" . $active_completed_email . ", active_incomplete_email=" . $active_incomplete_email . "
    WHERE payment = " . $db->quote($payment);
    $db->query($sql);

    nv_insert_logs(NV_LANG_DATA, $module_name, 'log_edit_payment', "edit " . $paymentname, $admin_info['userid']);
    $nv_Cache->delMod($module_name);

    nv_redirect_location(NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=" . $op);
}

$xtpl = new XTemplate("payport.tpl", NV_ROOTDIR . "/themes/" . $global_config['module_theme'] . "/modules/" . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

if (!empty($array_setting_payment) and empty($data_pay)) {
    foreach ($array_setting_payment as $value) {
        $value['link_edit'] = NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=" . $op . "&amp;payment=" . $value['payment'];
        $value['link_config'] = NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=config_payment&amp;payment=" . $value['payment'];
        $value['active'] = ($value['active'] == "1") ? "checked=\"checked\"" : "";
        $value['slect_weight'] = drawselect_number($value['payment'], 1, $all_page + 1, $value['weight'], "nv_chang_pays('" . $value['payment'] . "',this,url_change_weight,url_back);");

        $xtpl->assign('DATA_PM', $value);

        if ($value['payment'] == 'vnptepay') {
            $xtpl->parse('main.listpay.paymentloop.vnptepay');
        }

        $xtpl->parse('main.listpay.paymentloop');
    }

    $xtpl->assign('url_back', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=" . $op);
    $xtpl->assign('url_change', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=changepay");
    $xtpl->assign('url_active', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=actpay");
    $xtpl->parse('main.listpay');
}

if (!empty($array_payment_other) and empty($data_pay)) {
    $a = 1;
    foreach ($array_payment_other as $pay => $value) {
        if (!in_array($pay, $array_setting_payment_key)) {
            $value['link_edit'] = NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=" . $op . "&amp;payment=" . $value['payment'];
            $value['STT'] = $a;

            $xtpl->assign('ODATA_PM', $value);
            $xtpl->parse('main.olistpay.opaymentloop');
            $a++;
        }
    }

    if ($a > 1)
        $xtpl->parse('main.olistpay');
}

if (!empty($data_pay)) {
    if (defined('NV_EDITOR')) {
        require_once (NV_ROOTDIR . '/' . NV_EDITORSDIR . '/' . NV_EDITOR . '/nv.php');
    }

    $bodytext = nv_editor_br2nl($data_pay['bodytext']);
    $term = nv_editor_br2nl($data_pay['term']);

    if (!empty($bodytext)) {
        $bodytext = nv_htmlspecialchars($bodytext);
    }

    if (!empty($term)) {
        $term = nv_htmlspecialchars($term);
    }

    if (defined('NV_EDITOR') and nv_function_exists('nv_aleditor')) {
        $bodytext = nv_aleditor("bodytext", '100%', '300px', $bodytext);
        $term = nv_aleditor("term", '100%', '300px', $term);
    } else {
        $bodytext = "<textarea style=\"width:100%;height:300px\" name=\"bodytext\" id=\"bodytext\">" . $bodytext . "</textarea>";
        $term = "<textarea style=\"width:100%;height:300px\" name=\"term\" id=\"term\">" . $term . "</textarea>";
    }

    if (!empty($data_pay['images_button']) and file_exists(NV_UPLOADS_REAL_DIR . "/" . $module_name . "/" . $data_pay['images_button'])) {
        $data_pay['images_button'] = NV_BASE_SITEURL . NV_UPLOADS_DIR . "/" . $module_name . "/" . $data_pay['images_button'];
    }

    $xtpl->assign('EDITPAYMENT', sprintf($nv_Lang->getModule('editpayment'), $data_pay['payment']));

    if (!empty($data_pay['config'])) {
        $array_config = unserialize(nv_base64_decode($data_pay['config']));
        // Kiểm tra config nào là config dùng riêng cho mỗi ngôn ngữ thì sẽ lấy ngôn ngữ hiện tại.
        foreach ($array_config as $key => $cf) {
            if (is_array($cf)) {
                $array_config[$key] = !empty($cf[NV_LANG_DATA]) ? $cf[NV_LANG_DATA] : '';
            }
        }
    } else {
        $array_config = [];
    }

    $arkey_title = [];
    $arkey_note = [];

    if (!empty($array_payment_other[$data_pay['payment']]['titlekey'])) {
        $arkey_title = $array_payment_other[$data_pay['payment']]['titlekey'];
    }
    if (!empty($array_payment_other[$data_pay['payment']]['notekey'])) {
        $arkey_note = $array_payment_other[$data_pay['payment']]['notekey'];
    }
    if (!empty($array_payment_other[$data_pay['payment']]['config'])) {
        $array_config = array_merge($array_payment_other[$data_pay['payment']]['config'], $array_config);
    }

    foreach ($array_config as $key => $value) {
        if (isset($arkey_title[$key])) {
            $lang = $arkey_title[$key];
        } else {
            $lang = $key;
        }

        $value = $array_config[$key];

        $xtpl->assign('CONFIG_LANG', $lang);
        $xtpl->assign('CONFIG_NAME', $key);
        $xtpl->assign('CONFIG_VALUE', $value);

        if (!empty($arkey_note[$key])) {
            $xtpl->assign('CONFIG_NOTE', $arkey_note[$key]);
            $xtpl->parse('main.paymentedit.config.note');
        }

        $xtpl->parse('main.paymentedit.config');
    }

    $data_pay['active'] = ($data_pay['active'] == "1") ? "checked=\"checked\"" : "";
    $data_pay['active_completed_email'] = ($data_pay['active_completed_email'] == "1") ? "checked=\"checked\"" : "";
    $data_pay['active_incomplete_email'] = ($data_pay['active_incomplete_email'] == "1") ? "checked=\"checked\"" : "";

    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('DATA', $data_pay);
    $xtpl->assign('BODYTEXT', $bodytext);
    $xtpl->assign('TERM', $term);

    $config_link = NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=config";
    $xtpl->assign('DISCOUNT_MESSAGE', sprintf($nv_Lang->getModule('payport_discount_note'), $nv_Lang->getModule('payport_discount1'), $nv_Lang->getModule('payport_discount_transaction'), $nv_Lang->getModule('config_module'), $config_link, $nv_Lang->getModule('config_module')));

    if ($data_pay['payment'] != 'vnptepay') {
        $xtpl->parse('main.paymentedit.onepay');
    }

    $xtpl->parse('main.paymentedit');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
