<?php

use NukeViet\Elink\Session;

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2015 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Sat, 07 Mar 2015 03:43:56 GMT
 */

if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

if (!function_exists('EL_GetSessionReferralTarget')) {
    /*@return array[
     $session_static_rt['userid'] = $stat_userid;
     $session_static_rt['referer'] = $client_info['referer'];
     $session_static_rt['selfurl'] = $client_info['selfurl'];
     $session_static_rt['accesstime'] = NV_CURRENTTIME;
     $session_static_rt['ip'] = NV_CLIENT_IP;
     $session_static_rt['user_agent'] = NV_USER_AGENT;
     $session_static_rt['stat_id'] = 0;
     $session_static_rt['site_id'] = 0; // 0,1,2
     ]*/
    function EL_GetSessionReferralTarget()
    {
        global $nv_Request, $db_config, $db;

        $session_static_rt = $nv_Request->get_string('statistic_rt', 'session', '');
        $session_static_rt = empty($session_static_rt) ? [] : ((array) json_decode($session_static_rt, true));

        // Kiểm tra session hợp lệ
        if (!Session::checkSessionReferralTarget($session_static_rt)) {
            return [];
        }

        $session_static_rt['site_id'] = $nv_Request->get_absint('site_id', 'session', 0);

        // Kiểm tra, ghi vào CSDL
        if (empty($session_static_rt['stat_id'])) {
            $sql = "INSERT INTO " . $db_config['prefix'] . "_elink_statistics_link (
                site_id, userid, link_referer, link_target, access_time, access_ip, access_agent
            ) VALUES (
                " . $session_static_rt['site_id'] . ", " . $session_static_rt['userid'] . ", " . $db->quote($session_static_rt['referer']) . ",
                " . $db->quote($session_static_rt['selfurl']) . ", " . $session_static_rt['accesstime'] . ", " . $db->quote($session_static_rt['ip']) . ",
                " . $db->quote($session_static_rt['user_agent']) . "
            )";
            $stat_id = $db->insert_id($sql, 'id');

            // Ghi lại session thông kê
            if (!$stat_id) {
                return [];
            }

            $session_static_rt['stat_id'] = $stat_id;

            // Tính lại checksum
            $checksum = [];
            $checksum[] = $session_static_rt['stat_id'];
            $checksum[] = $session_static_rt['referer'];
            $checksum[] = $session_static_rt['selfurl'];
            $checksum[] = $session_static_rt['accesstime'];
            $checksum[] = $session_static_rt['ip'];

            $session_static_rt['checksum'] = md5(implode('|', $checksum));

            $nv_Request->set_Session('statistic_rt', json_encode($session_static_rt));
        }

        return $session_static_rt;
    }
}

nv_add_hook($module_name, 'sector2', $priority, function () {
    global $nv_Request, $db, $db_config, $global_config, $client_info, $user_info;

    if (!defined('NV_IS_AJAX') and !defined('NV_IS_MY_USER_AGENT') and !defined('NV_ADMIN') and defined('SSO_REGISTER_SECRET')) {
        $affiliate_rule = '/^[a-zA-Z0-9\_\-]+$/';

        // Lấy và giải mã meta trực tiếp từ request uri
        $dt_meta = $nv_Request->get_string('dt_meta', 'get', '');
        if (!empty($dt_meta)) {
            $iv = substr(SSO_REGISTER_SECRET, 0, 16);
            $dt_meta = strtr($dt_meta, '-_,', '+/=');
            $dt_meta = json_decode(openssl_decrypt($dt_meta, 'aes-256-cbc', SSO_REGISTER_SECRET, 0, $iv), true);

            if (is_array($dt_meta)) {
                // Mã giới thiệu
                $affiliate_code = (isset($dt_meta['affiliate_code']) and preg_match($affiliate_rule, $dt_meta['affiliate_code'])) ? $dt_meta['affiliate_code'] : '';

                // Ghi lại cookie cho mã giới thiệu
                if (!empty($affiliate_code)) {
                    // Kiểm tra thành viên này tồn tại
                    $affiliate_userid = $db->query("SELECT userid FROM " . NV_USERS_GLOBALTABLE . " WHERE username=" . $db->quote($affiliate_code) . " AND active=1")
                        ->fetchColumn();
                    if (!empty($affiliate_userid)) {
                        $nv_Request->set_Cookie('affiliate_code', $affiliate_code, NV_LIVE_COOKIE_TIME);
                        define('AFFILIATE_CODE', $affiliate_code);
                        define('AFFILIATE_UID', $affiliate_userid);
                    }
                }

                // Lưu site id
                if (isset($dt_meta['site_id'])) {
                    $nv_Request->set_Session('site_id', intval($dt_meta['site_id']));
                }

                // Session Referral & Target (không mã khuyến mãi)
                $statistic_rt_current = $nv_Request->get_string('statistic_rt', 'session', '');
                if (empty(Session::checkSessionReferralTarget($statistic_rt_current ? json_decode($statistic_rt_current, true) : []))) {
                    $statistic_rt = Session::checkSessionReferralTarget(isset($dt_meta['statistic_rt']) ? json_decode($dt_meta['statistic_rt'], true) : []);
                    if (!empty($statistic_rt)) {
                        $nv_Request->set_Session('statistic_rt', json_encode($statistic_rt));
                    }
                }
                unset($statistic_rt_current);

                // Session Referral & Target (tất cả)
                $statistic_rt_current = $nv_Request->get_string('statistic_all_rt', 'session', '');
                if (empty(Session::checkSessionReferralTarget($statistic_rt_current ? json_decode($statistic_rt_current, true) : []))) {
                    $statistic_rt = Session::checkSessionReferralTarget(isset($dt_meta['statistic_all_rt']) ? json_decode($dt_meta['statistic_all_rt'], true) : []);
                    if (!empty($statistic_rt)) {
                        $nv_Request->set_Session('statistic_all_rt', json_encode($statistic_rt));
                    }
                }
                unset($statistic_rt_current);
            }
        }

        // Lấy mã giới thiệu lưu từ cookie
        if (!defined('AFFILIATE_CODE')) {
            $affiliate_code = $nv_Request->get_title('affiliate_code', 'cookie', '');
            if (preg_match($affiliate_rule, $affiliate_code)) {
                $affiliate_userid = $db->query("SELECT userid FROM " . NV_USERS_GLOBALTABLE . " WHERE username=" . $db->quote($affiliate_code) . " AND active=1")
                    ->fetchColumn();
                if (!empty($affiliate_userid)) {
                    define('AFFILIATE_CODE', $affiliate_code);
                    define('AFFILIATE_UID', $affiliate_userid);
                }
            }
        }

        unset($affiliate_rule, $dt_meta, $affiliate_code, $affiliate_userid, $iv);
    }
});

/**
 * Xử lý liên kết thành viên với người giới thiệu sau khi đăng ký
 *
 * @param integer $userid
 * @return boolean
 */

if (!function_exists('nv_user_register_callback')) {

    function nv_user_register_callback($userid)
    {
        global $nv_Request, $db, $db_config;

        if (defined('NV_ADMIN')) {
            return false;
        }
        if (defined('AFFILIATE_UID')) {
            try {
                $db->query("INSERT INTO " . $db_config['prefix'] . "_elink_affiliate_set (pri_uid, pre_uid) VALUES (" . AFFILIATE_UID . ", " . intval($userid) . ")");
                $db->query("UPDATE " . NV_USERS_GLOBALTABLE . " SET num_aff_user = num_aff_user + 1 WHERE userid = " . AFFILIATE_UID);
            } catch (PDOException $e) {
                trigger_error(print_r($e, true));
            }
        }
    }
}
