<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CreateSaleStatic implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'salestatic';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $error = '';
        $error_code = '0000';
        $admin_id = $nv_Request->get_int('admin_id', 'post', '0');
        $row['date'] = $nv_Request->get_int('date', 'post', 0);
        $row['userid'] = $nv_Request->get_int('userid', 'post', 0);

        if ($row['date'] <= 0) {
            $error_code = '2003';
            $error = $nv_Lang->getModule('error_required_date');
        } elseif ($row['userid'] <= 0) {
            $error_code = '2004';
            $error = $nv_Lang->getModule('error_invalid_userid');
        }

        $otherdata = $nv_Request->get_array('otherdata', 'post');
        if (!is_array($otherdata)) {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Param otherdata invalid to array')
                ->getResult();
        }

        $keys_check = [
            'num_leads',
            'num_opportunities',
            'num_order',
            'num_vip',
            'money',
            'discount',
            'total',
            'price_reduce',
            'total_end',
            'recharge_day',
            'total_day',
            'bonus',
            'money_point_num',
            'money_point',
            'money_point_bonus'
        ];
        $array_sql = [];
        if (!empty($otherdata)) {
            // check $field
            foreach ($otherdata as $key => $value) {
                if (!in_array($key, $keys_check)) {
                    return $this->result->setError()
                        ->setCode('2009')
                        ->setMessage('Missing field ' . $key . ' in otherdata')
                        ->getResult();
                }
                $array_sql[$key] = $key;
            }
        }

        $row = array_merge($row, $otherdata);

        if (empty($error)) {
            try {
                $row['createtime'] = $row['updatetime'] = NV_CURRENTTIME;
                if (!empty($array_sql)) {
                    $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_sale_static (date, userid, ' . implode(',', $array_sql) . ') VALUES (:date, :userid, :' . implode(', :', $array_sql) . ')');
                } else {
                    $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_sale_static (date, userid) VALUES (:date, :userid)');
                }

                $stmt->bindParam(':date', $row['date'], PDO::PARAM_STR);
                $stmt->bindParam(':userid', $row['userid'], PDO::PARAM_INT);

                if (!empty($otherdata)) {
                    foreach ($otherdata as $key => $value) {
                        $stmt->bindValue(':' . $key, $value, PDO::PARAM_STR);
                    }
                }

                $exc = $stmt->execute();
                if ($exc) {
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Add sale static', 'UserID: ' . $row['userid'] . ' date: ' . $row['date'], $admin_id);
                    $this->result->setSuccess();
                    $this->result->set('user', [
                        'userid' => $row['userid'],
                        'date' => $row['date']
                    ]);
                } else {
                    $this->result->setError()
                        ->setCode('4001')
                        ->setMessage('userid not response');
                }
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode($error_code)
                ->setMessage($error)
                ->getResult();
        }
        return $this->result->getResult();
    }
}
