<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

use NukeViet\Dauthau\LangMulti;
use NukeViet\InForm\InForm;

$nv_hook_module = 'users'; // Module xảy ra event chứa data

$callback = function($vars, $from_data, $receive_data) {
    $user_id = $vars[0];

    $request = [];
    $request['receiver_ids'] = $user_id;
    $request['isdef'] = 'vi';

    $request['message'] = [
        'vi' => LangMulti::get('vi', 'admin_active_account'),
        'en' => LangMulti::get('en', 'admin_active_account'),
    ];
    $request['link'] = [
        'vi' => NV_MY_DOMAIN . '/vi/users/',
        'en' => NV_MY_DOMAIN. '/en/users/',
    ];
    InForm::creat($request);

    // Trả về true thì hệ thống không gửi email nữa
    return false;
};
nv_add_hook($module_name, 'admin_active_account', $priority, $callback, $hook_module, $pid);
