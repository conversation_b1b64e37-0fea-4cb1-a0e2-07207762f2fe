.bmd-modalContent{
     height: 700px;
}
 .table-bidding {
  tr td{
     border: 1px solid #fff !important;
  }

  td:nth-child(2n+1){
     background:tint($component-active-bg, 80%);
     font-weight: 700;
  }

  td:nth-child(2n){
     background:tint($component-active-bg, 90%);
  }
}

 .title-bidding {
  font-weight: 700;
  margin-bottom: 15px;
}

 .bidding-name{
     font-weight: 700;
     color: #429cdd;
     font-size: 18px;
 }
 .form-text {
    display: block;
    margin-top: 5px;
    font-style: italic;
 }
 .title-strong{
     font-weight: 700;
     color: #429cdd;
 }
 label.resetlable {
    font-weight: 400!important;
    margin: 0;
 }
 label.resetlable input {
    margin-top: 0!important;
 }
 .bidding-part{
     margin: 10px;
     padding-bottom:10px;
 }
 .bidding-form label{
     font-weight: 300;
     text-align: left !important;
 }
 .inline-block{
     display: inline-block;
 }
 .red{
     color: red !important;
 }
 .search_icon{
     margin: 10px 0;
 }
 .seach_choose .radio-inline{
     padding-top: 0 !important;
 }
 .bidding-btn{
     margin: 5px;
 }
 .metismenu{
     margin-left: -10px !important;
 }
 .bidding-form{
     font-size: 13px;
 }

 /*
  * Show các gói dịch vụ
  */
.panel-vipplan {
    height: calc(100% - #{$line-height-computed});
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    a {
        color: $text-color;
        font-weight: $font-weight-bold;
    }

    .panel-heading {
        color: #fff;
        padding-top: 25px;
        padding-bottom: 25px;
        position: relative;
        overflow: initial;
        background-blend-mode: overlay;
        background-size: 480px;
        background-image: url(../images/bg-page.webp) !important;

        .ribonnew {
            position: absolute;
            top: -1px;
            overflow: hidden;
            height: 80px;
            width: 80px;
            text-align: center;
            z-index: 0;
            right: -1px;
            border-top-right-radius: 2px;

            .newvip {
                font-size: 14px;
                color: #fe3d01;
                background: #ffd400;
                text-transform: uppercase;
                padding: 58px 80px 5px 40px;
                width: 281px;
                text-align: center;
                display: block;
                position: absolute;
                left: -47px;
                top: -31px;
                transition: all 0.5s ease;
                transform: rotate(40deg);
                box-shadow: 3px -4px 13px 3px rgba(0,0,0,0.75);
            }
        }
    }

    .vip-name {
        font-size: 28px;
        font-weight: $font-weight-bold;
        text-align: center;
        text-shadow: -1px -1px 1px rgba(255,255,255,.1), 1px 1px 1px rgba(0,0,0,.5);
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: $margin-base;

        &:before {
            content: loadcontent(vip2);
            font-family: $icomoon-font-family;
            font-size: 50px;
            line-height: 1;
            margin-right: $margin-base;
            margin-top: -15px;
        }
    }

    .vip-price {
        text-align: center;

        .price {
            font-size: $font-size-base * 1.5;
            font-weight: $font-weight-bold;
            letter-spacing: 2px;
            text-shadow: 1px 1px 0 $gray-light;
        }
    }

    .vip-price-old .price-old {
        text-decoration: line-through;
    }

    .features {
        margin: 0;
        padding: 0;

        li {
            position: relative;
            display: block;
            padding: 10px 0 10px 26px;
            margin-bottom: -1px;

            &:before {
                content: loadcontent(tick);
                font-family: $icomoon-font-family;
                line-height: 1;
                position: absolute;
                top: 13px;
                left: 0;
            }

            &:last-child {
                margin-bottom: -10px;
            }
        }

        li+li {
         border-top: 1px dotted $list-group-border;
        }
    }

    .detail {
        margin-top: auto;
        padding: 5px 15px;
        margin-bottom: 15px;
        text-align: center;
    }

    .btn-vip {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        border-width: 0;
        text-transform: uppercase;
        padding-top: $padding-base-vertical*2;
        padding-bottom: $padding-base-vertical*2;
        margin-bottom: -1px;
        position: relative;

        .choose {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .loading {
            position: absolute;
            top: 10px;
            right: 0;
            width: 30px;
            height: 30px;
            background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' style='margin:auto;background:0%25 0%25' viewBox='0 0 100 100' preserveAspectRatio='xMidYMid' display='block'%3E%3Ccircle cx='50' cy='50' r='30' stroke-width='8' stroke='%23fff' stroke-dasharray='47.12388980384689 47.12388980384689' fill='none' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' repeatCount='indefinite' dur='1s' keyTimes='0;1' values='0 50 50;360 50 50'/%3E%3C/circle%3E%3C/svg%3E");
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 30px 30px;
            display: none;
        }
    }

    .check-icon {
        margin-right: $margin-base;

        &:before {
            content: loadcontent(radio);
            font-family: $icomoon-font-family;
            font-size: $font-size-large;
            font-style: normal;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
        }
    }

    @each $vip, $color in $vip-colors {
      &.vip#{$vip} {
            background-color: tint($color, 93%);
            border-color: rgba(shade($color,10%), .8);
            &:hover {
                background-color: tint($color, 90%);
            }

            a:hover, a:active, a:focus {
                color: $color;
            }

            .panel-heading {
                background-color: rgba(shade($color,10%), .8) !important;
                color: setcolor($color);
            }

            .features li:before {
                color: $color;
            }

            .btn-vip {
                background-color: rgba($color, .9);
                border-color: rgba($color, .9);
                color: setcolor($color);
                font-weight: $btn-font-weight;

                &:hover,
                &:active,
                &:focus {
                    background-color: $color;
                    border-color: $color;
                    color: setcolor($color);
                }
            }
        }
    }

    &.selected .check-icon:before {
        content: loadcontent(check-radio);
    }

    &.load .loading {
        display: block;
    }
}

.price:after, .price-old:after, .price2:after {
    content: "\20AB";
    font-size: $font-size-small;
    font-weight: $font-weight-normal;
    margin-left: 2px;
    vertical-align:text-top;
}

.block-total {
    transition: height 0.5s ease;

    table, .promo {
        margin-bottom: $margin-xl;
    }

    table tfoot {
        border-top: 2px solid $gray;
    }

    table tr > td,
    table tr > th {
        &:first-child {
            width: 99%;
        }
        &:nth-child(2) {
          text-align: right;
          white-space: nowrap;
        }
    }

    .promo {
        .info {
            display: block;
            background-color: lighten($gray-lighter, 5%);
            color: $text-color;
            padding: $padding-base-horizontal;
            border: 1px solid $gray-lighter;
            border-radius: $border-radius-base;
        }
    }
    a.promo:hover, a.promo:focus {
        background-color: $gray-lighter;
    }
}

.basket {
    position: fixed;
    top: $navbar-height+10px;
    width: 46px;
    height: 46px;
    background-color: $brand-primary;
    background-image: $svg-basket;
    background-size: 26px 26px;
    background-position: center bottom 4px;
    background-repeat: no-repeat;
    border-radius: 50%;
    border: 2px solid $white;
    text-align: center;
    line-height: 1;
    z-index: $zindex-navbar-fixed+1;
    opacity: .9;

    &.danger {
        background-color: $brand-danger;
    }

    &.primary {
        background-color: $brand-primary;
    }

    .viplength {
        color: $component-active-color;
        font-weight: $font-weight-bold;
        font-size: $font-size-small;
    }

}

ul.vip-feature-list {
    list-style: disc;
    padding-left: 30px;
    margin-top: 6px;
}

ul.vip-feature-list li {
    margin-bottom: 6px;
}

ul.vip-feature-list li ul {
    list-style: circle;
}

/* Wizard đăng ký VIP */
.wizard {
    position: relative;
    background-color: #fff;
}

.wizard > .steps-container > .steps {
    list-style: none;
    padding: 0;
    margin: 0;
}

.wizard > .steps-container > .steps li {
    float: left;
    margin: 0;
    padding: 0 20px 0 30px;
    height: 48px;
    line-height: 46px;
    position: relative;
    background-color: #f7f7f7;
    color: #999;
    cursor: not-allowed;
}

.wizard > .steps-container > .steps li .chevron {
    border: 24px solid transparent;
    border-left: 14px solid #d4d4d4;
    border-right: 0;
    display: block;
    position: absolute;
    right: -14px;
    top: 0;
    z-index: 1;
}

.wizard > .steps-container > .steps li .chevron:before {
    border: 24px solid transparent;
    border-left: 14px solid #f7f7f7;
    border-right: 0;
    content: "";
    display: block;
    position: absolute;
    right: 1px;
    top: -24px;
}

.wizard > .steps-container > .steps li.active {
    background-color: #fff;
    color: #0685d6;
    cursor: default;
}

.wizard > .steps-container > .steps li.active .chevron:before {
    border-left-color: #fff;
}

.wizard > .steps-container > .steps li.complete {
    color: #fff;
    background: #0685d6;
}

.wizard > .steps-container > .steps li.complete .chevron:before {
    border-left-color: #0685d6;
}

.wizard > .step-content {
    clear: both;
    border-top: 1px solid #e2e2e2;
    float: left;
    width: 100%;
}

.area-vip label {
    margin: 0;
    padding-top: 7px;
}

.area-vip label [type="checkbox"] {
    margin-top: 0;
    margin-bottom: 0;
    top: -2px;
    position: relative;
}

ul.bidding-order-attach {
    margin: 7px 0;
}

ul.bidding-order-attach li {
    margin-bottom: 0;
}

.bidding-order-link {
    color: #428bca!important;
}
.tableresult .thead td{
    background-color: #0685d6 !important;
    vertical-align: middle;
    min-height: 1px;
    padding: 20px 8px;
    text-align: center;
    text-transform: uppercase;
    color: #fff;
    border-right: 1px #fff solid;
}

.tableresult .item_result{
    align-items: stretch;
    padding: 8px;
    border-right: 1px #ddd solid;
}
.tableresult .item_result h3{
    font-weight: 400;

}
.blur_filter {
    filter: blur(5px);
    -o-filter: blur(5px);
    -webkit-filter: blur(5px);
    -ms-filter: blur(5px);
    -moz-filter: blur(5px);

}
.open-list .bidding-list-header {
    background-color: #0685d6;
}
.open-list .bidding-list-header > td{
    min-height: 1px;
    padding: 20px 8px;
    text-align: center;
    text-transform: uppercase;
    color: #fff;
    border-right: 1px #fff solid;
}
.open-list .bidding-list-header th{
    min-height: 1px;
    padding: 20px 8px;
    text-align: center;
    text-transform: uppercase;
    color: #fff;
    border-right: 1px #fff solid;
}
.open-list .bidding-list-body .item {
    border-bottom: 1px #ddd solid;
}

.boder_bottom{
     border-bottom: 1px solid #ddd;
}

/*step progress bar*/
.container_step {
  width: 100%;
  margin: 0px auto;
  position: relative;
  z-index: 1;
}
.progressbar {
  margin: 0;
  padding: 0;
  counter-reset: step;
}
.progressbar > a, .progressbar > div {
  list-style-type: none;
  width: 20%;
  float: left;
  font-size: 12px;
  position: relative;
  text-align: center;
  text-transform: uppercase;
  color: #0e2132;
}

.progressbar .khlcnt:before{
    content: " \f271 ";
}

.progressbar .tbmt:before{
    content: '\f0a1';
}

.progressbar .kqlcnt:before{
    content: '\f274';
}

.progressbar .kqmt:before{
    content: '\f1ea';
}

.progressbar .tbmst:before{
    content: '\f0f3';
}

.progressbar .kqst:before{
    content: '\f046';
}


.progressbar li:before {
    width: 40px;
    height: 40px;
    /*content: counter(step);*/
    counter-increment: step;
    line-height: 40px;
    display: block;
    text-align: center;
    margin: 0 auto 0px auto;
    border-radius: 50%;
    background-color: #a5c1d2;
    font-family: FontAwesome;
    font-size:20px;
    color: white;
}
.progressbar li:after {
    width: 100%;
    height: 4px;
    content: '';
    position: absolute;
    background-color: #7d7d7d;
    top: 20px;
    left: -50%;
    z-index: -1;
}

.dropdown-menu > li:after{
        content: none;
}

.progressbar li.active:before{
    background-color: #0685d6;
}

.progressbar a:first-child li:after {
  content: none;
}
.progressbar li.active {
  color: #429cdd;
}
.progressbar li.active:before {
  border-color: #429cdd;
}
.progressbar  li.active:after {
  background-color: #429cdd;
}

.progressbar li.current:before {
  background-color: #11b94b;
  box-shadow: 4px 3px 6px 1px #838396
}

.progressbar li.active:hover::before {
  background-color: #7d0404e6;
  box-shadow: 4px 3px 6px 1px #838396;
}
li.hidden {
    display: none;
}

@media (max-width:720px) {
    .progressbar li:before {
        line-height: 40px;
        width: 40px;
        height: 40px;
        font-size:20px;
    }
    .progressbar a:after {
        top: 20px;
    }
}

.download {

    a {
        color: inherit;
        text-decoration: none;
    }

    a:hover {
        color: $primary;
        text-decoration: none;
    }

    a.btn-primary, a.btn-primary:hover {
        color: $white;
    }
}

.download-link {
    position: relative;
    overflow: hidden;

    span {
        display:inline-block;

        &:before {
            font-family: FontAwesome;
            content: " \f0f6 ";
            margin-right: 5px;
        }

        &:after {
            font-family: FontAwesome;
            margin-left: 2px;
        }

        &.is_file:before {
            content: " \f0c6 ";
        }

        &.is_pdf:before {
            content: " \f1c1 ";
        }

        &.is_zip:before {
            content: " \f1c6 ";
        }

        &.is_image:before {
            content: " \f1c5 ";
        }

        &.is_video:before {
            content: " \f1c8 ";
        }

        &.is_audio:before {
            content: " \f1c7 ";
        }

        &.is_word:before {
            content: " \f1c2 ";
        }

        &.is_ie:after {
            content: " \f26b ";
        }

        &.is_fast:after {
            content: " \f087 ";
        }

        &.is_gift:after {
            content: " \f11d ";
        }
    }

    &.is_other span.is_ie {
        text-decoration: line-through;
    }

    .list-group-item-light {
        background-color: #f5f5f5;
    }

    .list-group-item:first-child {
        border-top-right-radius: 0;
        border-top-left-radius: 0;
    }

    .display-flex > *:last-child > *:not(:last-child) {
        margin-right: 4px;
    }
}

@media (max-width: $screen-sm) {
    .download-link .display-flex {
        display: block;

        & > *:last-child {
            margin-top: 4px;
        }
    }
}

.nav-tabs.download-nav {
    border-bottom: 0;

    li:first-child {
        margin-left: 10px;
    }

    .nav-link {
        min-width: 130px;
        text-align: center;

        @media (max-width: $screen-xs-max) {
            min-width: inherit;
        }
    }
}

.disable-link {
    cursor: default !important;
    pointer-events: none;
    opacity: .7;
}

.prb_container {
    width: 100%;
    margin: $margin-base auto;
    position: relative;
    z-index: 1;
}
.prb-progressbar {
    text-align: center;
    margin-bottom: $margin-base;
    font-size: $font-size-small;
}

.prb {
    margin: 0;
    padding: 0;
    counter-reset: step;
    display: flex;
    flex-flow: row;

    & > * {
        flex-grow: 1;
        flex-basis: 0;
        text-align: center;
    }

    .item {
        float: left;
        font-size: $font-size-small;
        position: relative;
        color: $gray-900;

        .icn:before {
            width: 40px;
            height: 40px;
            counter-increment: step;
            line-height: 40px;
            display: block;
            text-align: center;
            margin: 0 auto;
            border-radius: 50%;
            background-color: $gray-400;
            font-family: "#{$icomoon-font-family}";
            font-size: 20px;
            color: $white;
        }

        .icn:after {
            width: 100%;
            height: 4px;
            content: "";
            position: absolute;
            background-color: $gray-400;
            top: 20px;
            left: -50%;
            z-index: -1;
        }

        .khlcnt:before {
            content: loadcontent(plan);
        }

        .tbmt:before {
            content: loadcontent(bullhorn);
        }

        .kqlcnt:before {
            content: loadcontent(certified);
        }

        .kqmt:before {
            content: loadcontent(checklist);
        }

        .tbmst:before {
            content: loadcontent(alarm);
        }

        .kqst:before {
            content: loadcontent(select);
        }

        .icn.active {
            color: $primary;
        }

        .icn.active:before {
            background-color: $primary;
            border-color: $primary;
        }

        .icn.current:before {
            background-color: $red;
            box-shadow: 4px 3px 6px 1px $gray-400;
        }
    }

    a.item:hover {
        .icn.active:before {
            background-color: $red;
            box-shadow: 4px 3px 6px 1px $gray-400;
        }
        .icn.current:before {
            background-color: darken($red, 15%);
            box-shadow: 4px 3px 6px 1px $gray-400;
        }
    }

    .item:first-child .icn:after {
        content: none;
    }

    .item .tl {
        display: block;
        padding: 10px 5px;
    }
}

@media (max-width: $screen-sm) {
    .prb .icn:before {
        line-height: 40px;
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
    .prb .icn:after {
        top: 20px;
    }
}

.bidding-alert {
    padding: $padding-base;
    margin-bottom: $margin-base;
}

.bidding-list {
    margin-bottom: 15px;

    .bidding-list-header {
        background-color: $primary;
        display: flex;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: stretch;

        & > div {
            position: relative;
            flex-grow: 0;
            min-height: 1px;
            flex-shrink: 1;
            padding: 10px 8px;
            text-align: center;
            text-transform: uppercase;
            color: $white;
            border-right: 1px $gray-300 solid;
            display: flex;
            align-items: center;
            justify-content: center;

            &:last-child  {
                border-right: 0;
            }
        }
    }

    .bidding-list-body {
        .item {
            display: flex;
            flex-wrap: nowrap;
            align-items: stretch;
            border-bottom: 1px $gray-300 solid;

            &:nth-child(2n) {
                background-color: $gray-100;
            }

            & > div {
                position: relative;
                flex-grow: 0;
                min-height: 1px;
                flex-shrink: 1;
                padding: 10px 5px ;
                border-right: 1px $gray-300 solid;
                order: 3;

                &:first-child {
                    border-left: 1px $gray-300 solid;
                }
            }
        }
    }

    .bidding-list-header > div.c-name,
    .bidding-list-body .item > div.c-name {
        flex-grow: 1;
    }

    .bidding-list-header > div.c-author,
    .bidding-list-body .item > div.c-author {
        flex: 0 0 200px;
        max-width: 200px;
    }

    &:not(.bidding-list-detail) .bidding-list-header > div.c-close,
    &:not(.bidding-list-detail) .bidding-list-body .item > div.c-close {
        flex: 0 0 198px;
        max-width: 198px;
    }

    &:not(.bidding-list-detail) .bidding-list-header > div.c-pub,
    &:not(.bidding-list-detail) .bidding-list-body .item > div.c-pub {
        flex: 0 0 150px;
        max-width: 150px;
    }

    &.bidding-list-detail .bidding-list-header > div.c-number,
    &.bidding-list-detail .bidding-list-body .item > div.c-number {
        flex: 0 0 130px;
        max-width: 130px;
    }
}

.bidding-list.bidding-list-detail .bidding-list-header > div.c-number.c-number-lg,
.bidding-list.bidding-list-detail .bidding-list-body .item > div.c-number.c-number-lg {
    flex: 0 0 140px;
    max-width: 140px;
}

.bidding-list.bidding-list-detail .bidding-list-header > div.c-close,
.bidding-list.bidding-list-detail .bidding-list-body .item > div.c-close,
.bidding-list.bidding-list-detail .bidding-list-header > div.c-pub,
.bidding-list.bidding-list-detail .bidding-list-body .item > div.c-pub {
    flex: 0 0 120px;
    max-width: 120px;
}

.bidding-list.bidding-list-detail .bidding-list-header > div.c-pub.c-pub-lg,
.bidding-list.bidding-list-detail .bidding-list-body .item > div.c-pub.c-pub-lg {
    flex: 0 0 160px;
    max-width: 160px;
}

.bidding-list.bidding-list-detail .bidding-list-header > div.c-open,
.bidding-list.bidding-list-detail .bidding-list-body .item > div.c-open {
    flex: 0 0 90px;
    max-width: 90px;
    text-align: center;
}

.bidding-list.bidding-list-detail .bidding-list-header > div.c-business,
.bidding-list.bidding-list-detail .bidding-list-body .item > div.c-business {
    flex: 0 0 100px;
    max-width: 100px;
    text-align: center;
}

.bidding-list.bidding-list-detail .bidding-list-header > div.c-stt,
.bidding-list.bidding-list-detail .bidding-list-body .item > div.c-stt {
    flex: 0 0 40px;
    max-width: 40px;
    text-align: center;
}
.bidding-list.bidding-list-detail .bidding-list-header > div.c-stt-lg,
.bidding-list.bidding-list-detail .bidding-list-body .item > div.c-stt-lg {
    flex: 0 0 80px;
    max-width: 80px;
    text-align: center;
}
.bidding-list.bidding-list-detail .bidding-list-header > div.c-gia,
.bidding-list.bidding-list-detail .bidding-list-body .item > div.c-gia {
    flex: 0 0 100px;
    max-width: 100px;
    text-align: center;
}
.bidding-list.bidding-list-detail .bidding-list-header > div.c-gia1,
.bidding-list.bidding-list-detail .bidding-list-body .item > div.c-gia1 {
    flex: 0 0 90px;
    max-width: 90px;
    text-align: center;
}
.bidding-list .bidding-list-header > div.c-tyle,
.bidding-list .bidding-list-body .item > div.c-tyle {
    flex: 0 0 60px;
    max-width: 60px;
    text-align: center;
}
.bidding-list .bidding-list-header > div.c-author1,
.bidding-list .bidding-list-body .item > div.c-author1 {
    flex: 0 0 140px;
    max-width: 140px;
}
.bidding-list .bidding-list-body .item > div.c-open h3 {
    font-weight: 400;
}

.bidding-list .bidding-list-header > div.c-adm,
.bidding-list .bidding-list-body .item > div.c-adm {
    flex: 0 0 80px;
    max-width: 80px;
}

.bidding-list .bidding-list-body .item > div.c-name h3 {
    font-weight: 400;
}

.bidding-list .bidding-list-body .item .label-name {
    display: none;
    font-weight: 700;
}

.bidding-list .bidding-list-pagenav {
    text-align: center;
}

@media (min-width: $screen-md-min) and (max-width: $screen-lg), (min-width: 676px) and (max-width: $screen-sm-max) {
    .bidding-list:not(.bidding-list-detail) .bidding-list-header > div.c-close,
    .bidding-list:not(.bidding-list-detail) .bidding-list-body .item > div.c-close {
        flex: 0 0 120px;
        max-width: 120px;
    }

    .bidding-list:not(.bidding-list-detail) .bidding-list-header > div.c-pub,
    .bidding-list:not(.bidding-list-detail) .bidding-list-body .item > div.c-pub {
        flex: 0 0 120px;
        max-width: 120px;
    }

    .bidding-list.bidding-list-detail .bidding-list-header > div.c-author,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-author {
        flex: 0 0 160px;
        max-width: 160px;
    }
}

@media (max-width: 676.98px), (min-width: $screen-sm) and (max-width: $screen-md-min) {
    .bidding-list .bidding-list-header {
        display: none;
    }

    .bidding-list .bidding-list-body .item {
        border: 1px $gray-300 solid;
        border-radius: 4px;
        margin-bottom: 15px;
        flex-direction: column;
        padding: 4px 8px 15px;
        background-color: $gray-100;
    }

    .bidding-list .bidding-list-body .item > div {
        border-right: 0;
        flex-basis: 0;
    }

    .bidding-list .bidding-list-body .item > div:first-child {
        border-left: 0;
    }

    .bidding-list .bidding-list-body .item > div.c-number,
    .bidding-list .bidding-list-body .item > div.c-number.c-number-lg,
    .bidding-list .bidding-list-body .item > div.c-name,
    .bidding-list .bidding-list-body .item > div.c-author,
    .bidding-list .bidding-list-body .item > div.c-close,
    .bidding-list .bidding-list-body .item > div.c-pub,
    .bidding-list .bidding-list-body .item > div.c-pub.c-pub-lg,
    .bidding-list .bidding-list-body .item > div.c-adm,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-number,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-number.c-number-lg,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-name,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-author,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-close,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-pub,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-pub.c-pub-lg,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-adm,
    .bidding-list:not(.bidding-list-detail) .bidding-list-body .item > div.c-close,
    .bidding-list:not(.bidding-list-detail) .bidding-list-body .item > div.c-pub {
        flex-basis: inherit;
        width: 100%;
        max-width: 100%;
        text-align: left;
    }

    .bidding-list .bidding-list-body .item > div.c-stt,
    .bidding-list .bidding-list-body .item > div.c-stt-lg,
    .bidding-list .bidding-list-body .item > div.c-open,
    .bidding-list .bidding-list-body .item > div.c-author1 ,
    .bidding-list .bidding-list-body .item > div.c-gia,
    .bidding-list .bidding-list-body .item > div.c-gia1,
    .bidding-list .bidding-list-body .item > div.c-tyle,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-stt,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-stt-lg,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-open,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-author1,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-gia,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-gia1,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-tyle{
        flex-basis: inherit;
        width: 100%;
        max-width: 100%;
        text-align: left;
    }

    .bidding-list.bidding-list-detail .bidding-list-header > div.c-stt,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-stt {
        display:none;
    }
    .bidding-list.bidding-list-detail .bidding-list-header > div.c-stt-lg,
    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-stt-lg {
        display:none;
    }
    .bidding-list .bidding-list-body .item > div {
        padding-bottom: 0;
    }

    .bidding-list .bidding-list-body .item > div.c-name h3 {
        font-weight: 500;
        font-size: 18px;
    }

    .bidding-list .bidding-list-body .item .label-name {
        display: inline-block;
        margin-right: 5px;
    }

    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-number {
        order: 2;
    }

    .bidding-list.bidding-list-detail .bidding-list-body .item > div.c-name {
        order: 1;
    }
}

.bidding-wrapper {
    a:not(.btn):not(.list-group-item):not(.disable-link) {
        color: $primary;
        text-decoration: none;
    }

    a:not(.btn):not(.list-group-item):not(.disable-link):hover {
        color: darken($primary, 15%);
        text-decoration: none;
    }

    blockquote {
        font-size: 15px;
    }

    .label {
            margin-right: 4px;
    }
}

.bidding-headertext {
    margin-top: $margin-top-base;
    margin-bottom: $margin-bottom-lg;
}

.bidding-title {
    font-weight: $font-weight-bold;
    padding: $margin-top-lg $margin-bottom-lg;
    text-align: center;

    .tl {
        color: $blue;
        font-size: $font-size-title;
    }

    .subtl {
        font-size: $font-size-subtitle;
        margin-bottom: $margin-bottom-base;
    }
}

.bidding-sub-title {
    font-weight: $font-weight-bold;
    margin-bottom: $margin-bottom-lg;
}

.social-buttons {
  line-height: 1;

  .fb-like {
    margin-right: 4px;
  }

  .twitter-bt {
    font: normal normal normal 11px/18px 'Helvetica Neue',Arial,sans-serif;
    vertical-align: bottom;
    width: 61px;
    height: 20px;
  }
}

.bidding-page-btn {
  display: flex;
  align-items: center;
  margin-bottom: $margin-bottom-lg;

  & > * ~ * {
    margin-left: auto;
  }

  .btn-follow {
    margin: 0;
  }

  .btn-print {
    margin-left: 20px;
  }

  .follow {
    .dropdown-menu {
        min-width: inherit;
        margin-top: 0;
        margin-left: 0;

        li > a {
          padding: 10px;
        }
      }
  }
}

.bidding-detail {
    margin-bottom: $margin-bottom-xl;
    background-color: rgb(244 247 248 / 46%);;

    .bidding-detail-item {
        display: flex;
        flex-wrap: nowrap;
        align-items: stretch;
        border-bottom: 1px $white solid;

        .c-tit  {
            background-color: rgb(225 234 236 / 46%);
            font-weight: $font-weight-bold;
            flex: 0 0 150px;
            max-width: 150px;
            padding: 8px;
        }

        .c-val  {
            flex-grow: 1;
            flex-shrink: 1;
            padding: 8px;
        }

        .cc-tit {
            background-color: rgb(225 234 236 / 46%);
            font-weight: $font-weight-bold;
            flex: 0 0 100%;
            padding: 8px;
        }

        &:nth-child(2n) {
          background-color: rgb(241 244 245 / 50%);
        }

        .label-default {
            background-color: $gray-600;
        }
        .padding {
            padding: 8px;
        }
        .c-tl {
            flex: 0 0 150px;
            background-color: rgb(225 234 236 / 46%);
            font-weight: $font-weight-bold;
            padding: 8px;
        }
        .c-vl {
            padding: 8px;
        }
    }

    .breadcrumb {
        padding: 8px 0;
        margin-bottom: 0;
        background-color: transparent;
    }
}

.bidding-detail .bidding-detail-item.col-four > div {
    flex: 0 0 50%;
    max-width: 50%;
    display: flex;
    flex-wrap: nowrap;
    align-items: stretch;
}

.bidding-detail .bidding-detail-item:not(.col-four) > div,
.bidding-detail .bidding-detail-item.col-four > div > div {
    border-right: 1px $white solid;
}

.bidding-detail .bidding-detail-item.col-four > div:last-child > div:first-child {
    border-left: 1px $white solid;
}


.bidding-detail .bidding-detail-item:not(.col-four) > div:last-child,
.bidding-detail .bidding-detail-item.col-four > div > div:last-child {
    border-right: 0;
}

.chance-cont {
  width: 100%;
  margin: 8px auto 20px;
  position: relative;
  z-index: 1;
}

.chance {
  margin: 0;
  padding: 0;
  display:flex;
  flex-flow:row;

  & > * {
    flex-grow:1;
    flex-basis: 0;
  }

  & > * ~ * {
    margin-left: 15px;
  }

  .tl {
    padding: 10px 20px 7px;
    text-align: center;
    font-weight: $font-weight-bold;
    font-size: $font-size-title;
  }

  .bt {
    padding: 10px 0;
    text-align: center;
  }

  .cont {
    height: 100%;
    display:flex;
    flex-direction: column;
    & > *:not(:last-child) {
        margin-bottom: 8px;
    }
    & > *:last-child {
        margin-top: auto;
    }
  }
}

@media (max-width: 676.98px), (min-width: $screen-sm) and (max-width: $screen-md-min) {
    .bidding-detail .bidding-detail-item {
        &.col-four {
            flex-wrap: wrap;

            & > div {
                flex: 0 0 100%;
                max-width: 100%;

                &:first-child {
                    border-bottom: 1px $white solid;
                }

                &:last-child > div:first-child {
                    border-left: 0;
                }
            }
        }

        .c-tit,
        .c-tit2,
        .c-tl {
            flex: 0 0 120px;
            max-width: 120px;
        }

        &.flex-direction-column {
            flex-direction: column;

            .c-tit, .c-val {
                flex: 0 0 100%;
                max-width: 100%
            }
            .c-tit {
                border-bottom: 1px $white solid;
            }
        }
    }

    .chance {
        flex-direction: column;

        & > * ~ * {
          margin-left: 0;
          margin-top: 15px;
        }
    }
}

#searchSector {
    display:flex;
    align-items: center;

    & > * ~ * {
        margin-left:4px
    }

    .column-text {
        flex: 1 1 auto;
    }
    .column-button {
        flex: 0 0 auto;
    }
}
.view_more {
    text-align: center;
    .btn {
        border: 0px solid transparent;
        background: none;
        text-transform: uppercase;
        color: #33691e;
        font-weight: 500;
        font-size: 15px;
    }
    .btn.active.focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn:active:focus, .btn:focus {
        outline: 0px auto -webkit-focus-ring-color;
        outline-offset: -0px;
    }
}
.body_bid_view {
    height: 200px;
    overflow: hidden;
    text-align: justify;
    box-shadow: 0 0px 0px rgba(0, 0, 0, 0.05);
}
.filltera{
    font-weight:bold;
    color: #d20c0c;
}
