<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */

if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

global $db_config, $nv_Cache;

// Tiền tệ hệ thống sử dụng
$sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_money_sys";
$global_array_money_sys = $nv_Cache->db($sql, 'code', $module_name);

// Các cổng thanh toán đang kích hoạt
$sql = 'SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_payment WHERE active = 1 ORDER BY weight ASC';
$global_array_payments = $nv_Cache->db($sql, 'payment', $module_name);
// nv_base64_encode(serialize($array_payment_other[$payment]['config']))
// unserialize(nv_base64_decode($config_old))
/**
 * Lấy cấu hình phù hợp với ngôn ngữ dữ liệu
 * Đúng ra sau khi giải mã thì để vậy sẽ hợp lý hơn.
 * Nhưng vì trong code đã có nhiều vị trí không xác định sử dụng ở định dạng chưa giải mã.
 * Do đó, sau khi giải mã config và điều chỉnh sẽ mã hóa lại
 */
foreach ($global_array_payments as $payment => $row) {
    $global_array_payments[$payment]['paymentname'] = $row['paymentname_' . NV_LANG_DATA];
    $global_array_payments[$payment]['bodytext'] = $row['bodytext_' . NV_LANG_DATA];
    $global_array_payments[$payment]['term'] = $row['term_' . NV_LANG_DATA];

    $payment_config = unserialize(nv_base64_decode($row['config']));
    if (is_array($payment_config)) {
        foreach ($payment_config as $key => $value) {
            $payment_config[$key] = (is_array($value)) ? ( isset($value[NV_LANG_DATA]) ? $value[NV_LANG_DATA] : '') : $value;
        }
    }
    $global_array_payments[$payment]['config'] = nv_base64_encode(serialize($payment_config));
}

// Các site thanh toán
$sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_sites ORDER BY weight ASC";
$global_array_sites = $nv_Cache->db($sql, 'id', $module_name);
