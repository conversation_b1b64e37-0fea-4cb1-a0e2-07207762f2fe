<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<!-- BEGIN: search -->
<form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-group">
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" /> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" /> <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
    <div class="row">
        <div class="col-md-20">
            <div class="form-group col-md-6">
                <input class="form-control" type="text" value="{Q}" name="q" maxlength="255" placeholder="Tìm kiếm khách hàng, thành viên..."/>
            </div>
            <div class="form-group col-md-6">
                <select class="form-control" name="caregiver_id">
                    <option value="0">{LANG.search_caregiver_id}</option>
                    <option value="-1"{ADMIN_SEARCH_NONE}>{LANG.search_not_caregiver_id}</option>
                    <!-- BEGIN: loop_admin_search -->
                    <option value="{ADMIN.key}"{ADMIN.selected_search}>{ADMIN.title}</option>
                    <!-- END: loop_admin_search -->
                </select>
            </div>
            <div class="form-group col-md-6">
                <select class="form-control" name="admin_id">
                    <option value="-1">{LANG.search_chot_don_id}</option>
                    <!-- BEGIN: loop_chot_don -->
                    <option value="{CHOT_DON.key}"{CHOT_DON.selected_search}>{CHOT_DON.title}</option>
                    <!-- END: loop_chot_don -->
                </select>
            </div>
            <!-- <div class="form-group col-md-5">
                <select class="form-control" name="search_status">
                    <option value="-1">{LANG.status_all}</option>
                    BEGIN: loop_status
                    <option value="{STATUS.key}"{STATUS.selected_search}>{STATUS.title}</option>
                    END: loop_status
                </select>
            </div> -->
            <div class="form-group col-md-6">
                <select class="form-control" name="search_website">
                    <option value="-1">--- {LANG.website} --- </option>
                    <!-- BEGIN: loop_website -->
                    <option value="{WEB.key}"{WEB.selected_web}>{WEB.title}</option>
                    <!-- END: loop_website -->
                </select>
            </div>
            <div class="form-group col-md-6">
                <div class="input-group">
                    <input class="form-control" type="text" placeholder="{LANG.static_from}" id="static_time_from" name="static_time_from" value="{ARRAY_SEARCH.static_time_from}"/>
                    <span class="input-group-btn">
                        <button class="btn btn-default" type="button" id="static_time_from_btn">
                            <em class="fa fa-calendar fa-fix">&nbsp;</em>
                        </button>
                    </span>
                </div>
            </div>
            <div class="form-group col-md-6">
                <div class="input-group">
                    <input class="form-control" type="text" placeholder="{LANG.static_to}" id="static_time_to" name="static_time_to" value="{ARRAY_SEARCH.static_time_to}"/>
                    <span class="input-group-btn">
                        <button class="btn btn-default" type="button" id="static_time_to_btn">
                            <em class="fa fa-calendar fa-fix">&nbsp;</em>
                        </button>
                    </span>
                </div>
            </div>
            <div class="form-group col-md-6">
                <div class="input-group">
                    <input class="form-control" type="text" placeholder="{LANG.create_from}" id="create_from" name="create_from" value="{ARRAY_SEARCH.create_from}"/>
                    <span class="input-group-btn">
                        <button class="btn btn-default" type="button" id="create_from_btn">
                            <em class="fa fa-calendar fa-fix">&nbsp;</em>
                        </button>
                    </span>
                </div>
            </div>
            <div class="form-group col-md-6">
                <div class="input-group">
                    <input class="form-control" type="text" placeholder="{LANG.create_to}" id="create_to" name="create_to" value="{ARRAY_SEARCH.create_to}"/>
                    <span class="input-group-btn">
                        <button class="btn btn-default" type="button" id="create_to_btn">
                            <em class="fa fa-calendar fa-fix">&nbsp;</em>
                        </button>
                    </span>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <input class="btn btn-primary btn-block" type="submit" value="{LANG.search_submit}" />
            </div>
        </div>
    </div>
</form>
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<script>
    $(document).ready(function() {
        $('.uidatepicker').datepicker({
            showOn : "both",
            dateFormat : "dd/mm/yy",
            changeMonth : true,
            changeYear : true,
            showOtherMonths : true,
            buttonImage : nv_base_siteurl + "assets/images/calendar.gif",
            buttonImageOnly : true
        });
    });
</script>
<!-- END: search -->

<div class="clearfix">
    <div class="pull-right">
        <b>{LANG.num_items} {NUM_ITEMS} </b>
    </div>
</div>
<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th class="w100 text-center">{LANG.stt}</th>
                    <th class="w100 text-center">{LANG.userid}</th>
                    <th class="w150 text-center">{LANG.coll_id}<br />{LANG.rate_chiet_khau}
                    </th>
                    <th class="w150 text-center">{LANG.order_admin}<br />{LANG.rate_chiet_khau}
                    </th>
                    <th class="w150 text-center">{LANG.caregiver_id}<br />{LANG.rate_chiet_khau}
                    </th>
                    <th class="mw100 text-center text-nowrap">{LANG.fee}</th>
                    <th class="w100 text-center">{LANG.website}</th>
                    <th class="mw200 text-center">{LANG.order_content}</th>
                    <th class="mw100 text-center text-nowrap">{LANG.time_create}<br />{LANG.update_static}
                    </th>
                    <th class="w150 text-center text-nowrap">{LANG.status}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="text-center">
                        {VIEW.stt}
                    </td>
                    <td>
                        <!-- BEGIN: user -->
                            <a href="{VIEW.link_detail}">{USERINFO.username}</a>
                            <div class="olist-promo text-muted text-nowrap">{USERINFO.email}</div>
                        <!-- END: user -->
                        <!-- BEGIN: business -->
                            <a href="{VIEW.link_detail}">{BINFO.companyname}</a>
                            <div class="olist-promo text-muted ">{BINFO.email}</div>
                        <!-- END: business -->
                        <!-- BEGIN: soclictor -->
                            <a href="{VIEW.link_detail}">{SINFO.title}</a>
                        <!-- END: soclictor -->
                        <!-- BEGIN: visitor -->
                            <a href="{VIEW.link_detail}">Khách thanh toán</a>
                        <!-- END: visitor -->
                    </td>
                    <td class="text-center">{VIEW.full_name_affiliate_userid}
                        <p class=" text-muted">{VIEW.introduce_value}</p>
                    </td>
                    <td class="text-center">{VIEW.full_name_admin}
                        <p class=" text-muted">{VIEW.successful_value}</p>
                    </td>
                    <td class="text-center">
                        {VIEW.full_name_caregiver_id}
                        <p class=" text-muted">{VIEW.caregiver_value}</p>
                    </td>
                     <td class="text-nowrap">{LANG.discount}: <strong class="text-danger">{VIEW.discount} VNĐ</strong>
                        <div class="olist-promo text-nowrap">
                            {LANG.con_lai}: <strong class="text-danger">{VIEW.total} VNĐ</strong>
                        </div> {LANG.chiet_khau}: <strong class="text-danger">{VIEW.price_reduce} VNĐ</strong>
                        <div class="olist-promo text-nowrap">
                            {LANG.thuc_con}: <strong class="text-danger">{VIEW.total_end} VNĐ</strong>
                        </div>
                        <div class="olist-promo text-nowrap">
                            {LANG.chiet_khau_thua}: <strong class="text-danger">{VIEW.discount_excess} VNĐ</strong>
                        </div>
<!-- BEGIN: affilicate_value -->
                        <div class="olist-promo text-nowrap">
                            {LANG.ctv}: <strong class="text-danger">{VIEW.affilicate_value} VNĐ</strong>
                        </div>
<!-- END: affilicate_value -->
                    </td>
                    <td class="text-center">{VIEW.siteid}</td>
                    <td>{VIEW.noi_dung}</td>

                    <td class="text-nowrap text-center">{VIEW.add_time}
                        <div class="olist-promo text-nowrap">{VIEW.static_time}</div>
                    </td>
                    <td>
                        <div>{STATUS}</div>
                        <!-- BEGIN: transaction -->
                        <span class="label label-success">{TRANSACTION_CODE}</span>
                        <!-- END: transaction -->
                        <!-- BEGIN: transaction_link -->
                        <a class="label label-success" href="{TRANSACTION_LINK}" target="_blank">{TRANSACTION_CODE}</a>
                        <!-- END: transaction_link -->
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>

            <tfoot>
                <tr>
                    <td colspan="2">{LANG.tong_chiet_khau}: <strong class="text-danger">{STATIC_TOTAL.total_percent}</strong></td>
                    <td>{LANG.coll_member}: <strong class="text-danger">{STATIC_TOTAL.introduce_value}</strong></td>
                    <td>{LANG.order_confirm}: <strong class="text-danger">{STATIC_TOTAL.successful_value}</strong></td>
                    <td>{LANG.caregive}: <strong class="text-danger">{STATIC_TOTAL.caregiver_value}</strong></td>
                    <td colspan="6">{LANG.doanh_so}: <strong class="text-danger">{STATIC_TOTAL.money}</strong>; <br /> {LANG.discount}: <strong class="text-danger">{STATIC_TOTAL.discount}</strong>; <br />{LANG.doanh_so_sau_giam}: <strong class="text-danger">{STATIC_TOTAL.total}</strong>;<br /> {LANG.chiet_khau_rieng}: <strong class="text-danger">{STATIC_TOTAL.price_reduce}</strong>; <br />{LANG.thuc_con}: <strong class="text-danger">{STATIC_TOTAL.total_end}</strong>;<br />Chiết khấu thừa: <strong class="text-danger">{STATIC_TOTAL.discount_excess}</strong>;
                    </td>
                </tr>
                <!-- BEGIN: generate_page -->
                <tr>
                    <td class="text-center" colspan="11">{NV_GENERATE_PAGE}</td>
                </tr>
                <!-- END: generate_page -->
            </tfoot>

        </table>
    </div>
</form>
<!-- BEGIN: payment_note_order -->
<div class="clearfix">
    <div class="pull-left">
        <b>{payment_note_order}</b>
    </div>
</div>
<!-- END: payment_note_order -->
<script type="text/javascript">
    $(document).ready(function($) {
        $('select[name="a"], select[name="chot_don"]').select2();
    });
    $("#static_time_from,#static_time_to,#create_from,#create_to").datepicker({
    dateFormat : "dd/mm/yy",
    changeMonth : true,
    changeYear : true,
    showOtherMonths : true,
    yearRange: '2018:2030',
    showOn : 'focus'
});

$('#static_time_from_btn').click(function() {
    $("#static_time_from").datepicker('show');
});

$('#static_time_to_btn').click(function() {
    $("#static_time_to").datepicker('show');
});

$('#create_from_btn').click(function() {
    $("#create_from").datepicker('show');
});

$('#create_to_btn').click(function() {
    $("#create_to").datepicker('show');
})
</script>
<!-- END: main -->
