<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */

if (!defined('NV_IS_FILE_SITEINFO')) {
    die('Stop!!!');
}

$data['title'] = sprintf($nv_Lang->getModule('notification_payport_ipn_alert'), $data['content']['payment'], $data['content']['ip'], nv_date('H:i d/m/Y', $data['content']['time']));
$data['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $data['module'] . '&amp;' . NV_OP_VARIABLE . '=payport&amp;payment=' . $data['content']['payment'];
