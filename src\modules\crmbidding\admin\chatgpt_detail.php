<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$showheader = $nv_Request->get_int('showheader', 'post,get', 1);
$crmBiddingChatGptId = $nv_Request->get_int('id', 'post,get', 1);
$error = [];

//$q = $nv_Request->get_title('q', 'post,get');

//SELECT DATA
$db->select('    
        tb1.*,
        tb2.uniqueid,
        tb2.last_activity,
        tb2.first_time,
	    tb3.first_name,
	    tb3.last_name,
	    tb3.username,
	    tb3.email,
	    tb4.phone
	')
    ->from(NV_PREFIXLANG . '_' . $module_data . '_chatgpt tb1
        LEFT JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_chatgpt_users tb2 ON tb2.id = tb1.crmbidding_chatgpt_id
        LEFT JOIN ' . NV_USERS_GLOBALTABLE . ' tb3 ON tb3.userid = tb2.userid
        LEFT JOIN ' . NV_USERS_GLOBALTABLE . '_info tb4 ON tb3.userid = tb4.userid
    ')
    ->where('tb1.crmbidding_chatgpt_id = :crmbidding_chatgpt_id')
    ->order('tb1.addtime DESC');

$sth = $db->prepare($db->sql());
$sth->bindValue(':crmbidding_chatgpt_id', $crmBiddingChatGptId);
$sth->execute();

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('LINK_DUPLICATE', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=duplicate&type=2');

//COMPARE DATA ROWS
$number = 1;
$user = [];
while ($view = $sth->fetch()) {
    if(!$user){
        $user = [
            'name' => nv_show_name_user($view['first_name'], $view['last_name'], $view['username']),
            'phone' => $view['phone'],
            'email' => $view['email'],
            'uniqueid' => $view['uniqueid'],
        ];
        $xtpl->assign('USER', $user);
    }

    $view['number'] = $number++;
    $view['addtime'] = date('H:i:s d/m/Y', $view['addtime']);
    $xtpl->assign('VIEW', $view);
    $xtpl->parse('main.loop');
}

if (!empty($user['phone']) or !empty($user['email'])) {
    $_sql = "SELECT id,name,phone,email FROM " . NV_PREFIXLANG . "_" . $module_data . "_leads";
    $where_duplicate = [];
    $num_items_duplicate = "";
    if (!empty($user['email'])) {
        $where_duplicate[] = "email = " . $db->quote($user['email']) . "";
    }
    if (!empty($user['phone'])) {
        $where_duplicate[] = "phone = " . $db->quote($user['phone']);
        $_tmp_phone = $user['phone'];
        if (preg_match('/(\d{9})$/', $_tmp_phone, $m)) {
            $_tmp_phone = $m[0];
        }
        $where_duplicate[] = "phone_search = " . $db->quote($_tmp_phone);
    }

    $_sql .= " WHERE (" . implode(' OR ', $where_duplicate) . ") ORDER BY id DESC";
    $result = $db->query($_sql);
    $items_duplicate = $result->fetchAll();
    if (sizeof($items_duplicate) > 0) {
        $xtpl->assign('STATUS_LEADS', $nv_Lang->getModule('da_tao_lead'));
        foreach ($items_duplicate as $item) {
            $xtpl->assign('LINK_LEADS', NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=' . 'leads_info&id=' . $item['id'] . '&showheader=' . $showheader);
            $xtpl->assign('NAME_LEADS', $item['name']);
            $xtpl->parse('main.link_leads');
        }
    } else {
        $xtpl->assign('STATUS_LEADS', $nv_Lang->getModule('chua_tao_lead'));
    }
    $xtpl->parse('main.status_leads');
}

//ERROR LIST
if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');
$page_title = $nv_Lang->getModule('chi_tiet_khach_chat_gpt');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';
