<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\supportticket\Api;

use LogKey;
use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CreateComment implements IApi
{

    private $result;
    private $log_id = 0;
    private $ticket_id = 0;
    private $parent = 0;
    private $content = '';
    private $old_content = '';
    private $reply_userid = 0;
    private $display_userid = 0;
    private $area = 0;
    private $file_attach = '';
    private $old_file = [];
    private $new_file = [];
    private $status = 0;
    private $status_view = 0;
    private $point_quote = 0;
    private $comment_type;
    private $edit_time = 0;
    private $point_offer = 0;
    private $payment_status = 0;
    private $is_paid = 0;
    private $ticket_add_time = 0;
    private $reply_range_time = 0;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'ticket';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $nv_Request, $admin_info, $db_config, $db, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $ticket = [];
        $error = '';
        $error_code = '0000';

        $row['log_id'] = $nv_Request->get_int('log_id', 'post', 0);
        $row['ticket_id'] = $nv_Request->get_int('ticket_id', 'post', 0);
        $row['parent'] = $nv_Request->get_int('parent', 'post', 0);
        $row['reply_userid'] = $nv_Request->get_int('reply_userid', 'post', 0);
        $row['display_userid'] = $nv_Request->get_int('display_userid', 'post', 0);
        $row['area'] = $nv_Request->get_int('area', 'post', 0);
        $row['content'] = $nv_Request->get_textarea('content', '', NV_ALLOWED_HTML_TAGS);
        $row['file_attach'] = $nv_Request->get_array('file_attach', 'post', []);
        $row['status'] = $nv_Request->get_int('status', 'post', 0);
        $row['status_view'] = $nv_Request->get_int('status_view', 'post', 0);
        $row['point_quote'] = $nv_Request->get_int('point_quote', 'post', 0);
        $row['comment_type'] = $nv_Request->get_int('comment_type', 'post', 0);
        $row['edit_time'] = $nv_Request->get_int('edit_time', 'post', 0);
        $row['point_offer'] = $nv_Request->get_int('point_offer', 'post', 0);
        $row['payment_status'] = $nv_Request->get_int('payment_status', 'post', 0);

        if ($row['ticket_id'] > 0) {
            $ticket = $db->query('SELECT * FROM ' . TB_TICKET_ROW . ' WHERE id=' . $row['ticket_id'])->fetch();
            $this->ticket_add_time = $ticket['add_time'];
            $this->is_paid = $ticket['is_paid'];
            if (empty($ticket)) {
                return $this->result->setError()
                    ->setCode('1000')
                    ->setMessage('Ticket not exit')
                    ->getResult();
            }
        }

        //Thời gian phản hồi: Nếu trả lời câu hỏi bổ sung thì tính theo câu hỏi bổ sung, không thì tính theo thời gian tạo ticket
        if ($row['parent'] > 0) {
            $parent = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE log_id=' . $row['parent'])->fetch();
            $this->reply_range_time = (int)(NV_CURRENTTIME - $parent['add_time']);
        } else {
            $this->reply_range_time = (int)(NV_CURRENTTIME - $this->ticket_add_time);
        }

        if ($row['ticket_id'] > 0 && $row['log_id']) {
            $ticket_cmt = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE log_id=' . $row['log_id'] . ' AND ticket_id=' . $row['ticket_id'])->fetch();
            if (empty($ticket_cmt)) {
                return $this->result->setError()
                    ->setCode('1000')
                    ->setMessage('Comment not exit')
                    ->getResult();
            }
            $this->old_content = $ticket_cmt['content'];
            $this->old_file = unserialize($ticket_cmt['file_attach']);
        }

        if (empty($row['content'])) {
            $error_code = '1001';
            $error = $nv_Lang->getModule('error_required_comment_content');
        }

        $count_files = count($row['file_attach']);
        if ($count_files > NV_ATTACH_LIMITED) {
            $error_code = '1004';
            $error = sprintf($nv_Lang->getModule('error_upload_limited'), NV_ATTACH_LIMITED);
        }

        if ($row['point_quote'] < 0) {
            $error_code = '1005';
            $error = $nv_Lang->getModule('error_point_quote');
        }

        if ($row['point_offer'] < 0) {
            $error_code = '1006';
            $error = $nv_Lang->getModule('error_point_quote');
        }

        if (empty($error)) {
            $this->log_id = $row['log_id'];
            $this->ticket_id = $row['ticket_id'];
            $this->parent = $row['parent'];
            $this->reply_userid = $row['reply_userid'];
            $this->display_userid = $row['display_userid'];
            $this->area = $row['area'];
            $this->content = $row['content'];
            $this->file_attach = serialize($row['file_attach']);
            $this->new_file = $row['file_attach'];
            $this->status = $row['status'];
            $this->status_view = $row['status_view'];
            $this->point_quote = $row['point_quote'];
            $this->comment_type = $row['comment_type'];
            $this->edit_time = $row['edit_time'];
            $this->point_offer = $row['point_offer'];
            $this->payment_status = $row['payment_status'];

            if ($this->save()) {
                $this->result->setSuccess();
                $this->result->setMessage($nv_Lang->getModule('success_log_add'));
                $this->result->set('log_id', $this->log_id);
                comment_notification_to_user($ticket, $this->log_id, $this->point_quote, $row['display_userid'], $row['content']);
            } else {
                if (isset($uploaded) and $uploaded['status'] == 'success') {
                    multi_upload_unlink($uploaded['dir']);
                }
                $this->result->setError()->setCode('1007')->setMessage($nv_Lang->getModule('error_cant_save'));
            }
        } else {
            $this->result->setError()->setCode($error_code)->setMessage($error);
        }
        return $this->result->getResult();
    }

    private function save()
    {
        global $db, $nv_Cache, $db_config;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];

        $current_time = NV_CURRENTTIME;
        if ($this->log_id == 0) {
            $stmt = $db->prepare('INSERT INTO ' . TB_TICKET_LOG . ' (content, ticket_id, parent, file_attach, reply_userid, display_userid, area, add_time, status, status_view, point_quote, comment_type, edit_time, point_offer, payment_status, point_final, is_paid, ticket_add_time, reply_range_time) VALUES
                (:content, :ticket_id, :parent, :file_attach, :reply_userid, :display_userid, :area, :add_time, :status, :status_view, :point_quote, :comment_type, :edit_time, :point_offer, :payment_status, :point_final, :is_paid, :ticket_add_time, :reply_range_time)');

            $stmt->bindParam(':reply_userid', $this->reply_userid, PDO::PARAM_INT);
            $stmt->bindParam(':display_userid', $this->display_userid, PDO::PARAM_INT);
            $stmt->bindParam(':area', $this->area, PDO::PARAM_INT);
            $stmt->bindParam(':ticket_id', $this->ticket_id, PDO::PARAM_INT);
            $stmt->bindParam(':parent', $this->parent, PDO::PARAM_INT);
            $stmt->bindParam(':add_time', $current_time, PDO::PARAM_INT);
            $stmt->bindParam(':status', $this->status, PDO::PARAM_INT);
            $stmt->bindParam(':status_view', $this->status_view, PDO::PARAM_INT);
            $stmt->bindParam(':point_quote', $this->point_quote, PDO::PARAM_INT);
            $stmt->bindParam(':comment_type', $this->comment_type, PDO::PARAM_INT);
            $stmt->bindParam(':point_offer', $this->point_offer, PDO::PARAM_INT);
            $stmt->bindParam(':payment_status', $this->payment_status, PDO::PARAM_INT);
            $stmt->bindParam(':point_final', $this->point_quote, PDO::PARAM_INT);
            $stmt->bindParam(':is_paid', $this->is_paid, PDO::PARAM_INT);
            $stmt->bindParam(':ticket_add_time', $this->ticket_add_time, PDO::PARAM_INT);
            $stmt->bindParam(':reply_range_time', $this->reply_range_time, PDO::PARAM_INT);
        } else {
            $stmt = $db->prepare('UPDATE ' . TB_TICKET_LOG . ' SET display_userid = :display_userid, content = :content, file_attach = :file_attach, edit_time = :edit_time WHERE log_id=' . $this->log_id);
        }
        $stmt->bindParam(':edit_time', $this->edit_time, PDO::PARAM_INT);
        $stmt->bindParam(':display_userid', $this->display_userid, PDO::PARAM_INT);
        $stmt->bindParam(':file_attach', $this->file_attach, PDO::PARAM_STR);
        $stmt->bindParam(':content', $this->content, PDO::PARAM_STR, strlen($this->content));

        $exc = $stmt->execute();
        if ($exc) {
            $nv_Cache->delMod($module_name);
            if (empty($this->log_id)) {
                $this->log_id = $db->lastInsertId();
                if ($this->parent != 0) {
                    $sql = 'UPDATE ' . TB_TICKET_LOG . ' SET status = ' . $this->status . ', status_view = ' . $this->status_view . ' WHERE log_id = ' . $this->parent;
                    $db->query($sql);
                }
            } else {
                $this->addLog();
            }
            return true;
        }

        return false;
    }

    private function addLog()
    {
        global $db, $module_data, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];

        $log_data = [
            LogKey::AdminUpdate->getLabel()
        ];

        if ($this->old_content != $this->content) {
            $log_data[] = [
                $nv_Lang->getModule('log_update_comment') . ':',
                $this->old_content . ' =&gt; ' . $this->content
            ];
        }

        if ($this->old_file != $this->new_file) {
            $log_data[] = [
                $nv_Lang->getModule('log_update_image') . ':',
                implode(', ', $this->old_file) . ' =&gt; ' . implode(', ', $this->new_file)
            ];
        }


        if (!empty($this->ticket_id) && !empty($this->reply_userid)) {
            add_ticket_logs($this->reply_userid, LogKey::AdminReply->value, $log_data, $this->ticket_id, 1);
        }
    }
}
