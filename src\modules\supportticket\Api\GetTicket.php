<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\supportticket\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use TicketStatus;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetTicket implements IApi
{

    private $result;
    private $assignee = [];
    private $add_userid = 0;
    private $status = 0;
    private $is_point = false;
    private $is_leader = false;
    private $is_special_admin = false;
    private $is_same_deparment = false;
    private $is_paid = 0;
    private $last_comment_userid = 0;
    private $cat_id = 0;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'ticket';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $db_config, $nv_Lang, $admin_info, $special_admin_array;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];

        $ticket_id = $nv_Request->get_int('id', 'post', 0);
        $userid = $nv_Request->get_int('userid', 'post', 0);
        if ($ticket_id > 0) {
            $row = $db->query('SELECT * FROM ' . TB_TICKET_ROW . ' WHERE id=' . $ticket_id . ' AND delete_time=0')->fetch();
            if (empty($row)) {
                $this->result->setError()
                    ->setCode('4000')
                    ->setMessage($nv_Lang->getModule('api_error_400'));
            } else {
                $this->assignee = !empty($row['assignee_to']) ? explode(',', $row['assignee_to']) : [];
                $this->add_userid = $row['add_userid'];
                $this->status = $row['status'];
                $this->is_leader = $this->isLeader($userid);
                $this->is_special_admin = in_array($admin_info['userid'], $special_admin_array);
                $this->is_same_deparment = in_array($row['cat_id'], get_cat_by_userid());
                $this->is_point = $this->isPoint($row['cat_id']);
                $this->is_paid = $row['is_paid'];
                $this->last_comment_userid = $row['last_comment_userid'];
                $this->cat_id = $row['cat_id'];

                $this->result->setSuccess();
                $canView = $this->canView($userid);
                $this->result->set('canView', $canView);
                if ($canView) {
                    $this->result->set('data', $row);
                }
                $this->result->set('canEdit', $this->canEdit($userid));
                $this->result->set('canReply', $this->canReply($userid));
                $this->result->set('isPoint', $this->is_point);
            }
        } else {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Ticket is not exist')
                ->getResult();
        }
        return $this->result->getResult();
    }

    private function canEdit($userid)
    {
        if ($userid == $this->add_userid || empty($this->assignee) || in_array($userid, $this->assignee) || defined('NV_IS_SPADMIN') || $this->is_leader || $this->is_special_admin) {
            return true;
        }

        return false;
    }

    private function canView($userid)
    {
        if ($userid == $this->add_userid || empty($this->assignee) || in_array($userid, $this->assignee) || defined('NV_IS_SPADMIN') || $this->is_leader || $this->is_special_admin || $this->is_same_deparment) {
            return true;
        }

        return false;
    }

    //Điều kiện để mở form comment
    private function canReply($userid)
    {
        global $db, $admin_info;

        if ($this->status == TicketStatus::Close->value) {
            return false;
        }

        /**
         * Nếu là ticket trả phí thì sẽ được comment không cần assign
         * Nếu là Ticket trả phí (AI Tư vấn đấu thầu)
         * - Chỉ chuyên gia mới được trả lời
         * - Chỉ chuyển gia mới được quyền mark câu trả lời của AI
         */
        if ($this->is_paid == 1) {
            $_sql = 'SELECT * FROM ' . TB_TICKET_CATADMIN . ' WHERE userid=' . $admin_info['userid'] . ' AND cat_id=' . $this->cat_id;
            $_result = $db->query($_sql)->fetch();
            if (!empty($_result)) {
                return true;
            }
            return false;
        }

        if ($userid == $this->add_userid || in_array($userid, $this->assignee) || defined('NV_IS_SPADMIN') || $this->is_leader || $this->is_special_admin) {
            return true;
        }

        return false;
    }

    //Kiểm tra user hiện tại có phải leader của người đang chăm sóc hoặc người tạo ticket ko
    private function isLeader($userid)
    {
        global $module_name, $module_data, $nv_Cache, $db_config;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $members = get_member_by_leader($userid);
        if (sizeof($members) > 0) {
            foreach ($members as $member) {
                if (in_array($member, $this->assignee) || $member == $this->add_userid) {
                    return true;
                }
            }
        }

        return false;
    }

    //Kiểm tra ticket này có tính điểm ko
    private function isPoint($cat_id)
    {
        global $array_ticket_cats;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        if (isset($array_ticket_cats[$cat_id]) && $array_ticket_cats[$cat_id]['is_point'] == 1) {
            return true;
        }

        return false;
    }
}
