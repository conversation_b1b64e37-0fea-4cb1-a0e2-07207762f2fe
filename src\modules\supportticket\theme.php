<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2023 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 27 Feb 2023 09:02:35 GMT
 */

if (!defined('NV_IS_MOD_SUPPORTTICKET')) {
    die('Stop!!!');
}

/**
 * nv_theme_supportticket_main()
 *
 * @param mixed $array_data
 * @param mixed $generate_page
 * @return
 */
function nv_theme_supportticket_main($array_data, $generate_page, $page, $per_page, $array_search)
{
    global $module_info, $op, $module_name, $array_ticket_status, $array_active_cats, $module_name, $nv_Lang;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    $action_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
    $xtpl->assign('ACTION', $action_url);
    $xtpl->assign('LINK_ADD', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['add']);

    if (!empty($array_data)) {
        $number = $per_page * ($page - 1);
        foreach ($array_data as $view) {
            if ($view['is_paid'] == 1) {
                $view['url_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail_paid'] . '&amp;id=' . $view['id'];
            } else {
                $view['url_detail'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail'] . '&amp;id=' . $view['id'];
            }
            $view['number'] = ++$number;
            $view['cat_title'] = empty($array_active_cats[$view['cat_id']]) ? '' : $array_active_cats[$view['cat_id']]['title_' . NV_LANG_DATA];
            $view['status_proccess'] = TicketStatusClient::tryFrom($view['status'])?->getLabel() ?? '';
            $view['add_time'] = nv_date('H:i:s d/m/Y', $view['activity_time']);
            $view['status_tooltip'] = TicketStatusClient::tryFrom($view['status_client'])?->getMessage() ?? '';
            $view['status_class'] = TicketStatusClient::tryFrom($view['status_client'])?->getIcon() ?? '';

            $xtpl->assign('VIEW', $view);
            $xtpl->parse('main.data.loop');
        }

        if ($generate_page) {
            $xtpl->assign('GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.data.page');
        }
        $xtpl->parse('main.data');
    } else {
        $xtpl->parse('main.empty');
    }

    $xtpl->assign('SEARCH', $array_search['q']);

    foreach ($array_active_cats as $value) {
        $xtpl->assign('OPTION', [
            'key' => $value['cat_id'],
            'title' => NV_LANG_DATA == 'vi' ? $value['title_vi'] : $value['title_en'],
            'selected' => ($value['cat_id'] == $array_search['cat']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.select_cat_id');
    }

    // issue 2168: Cập nhật hiển thị trạng thái ở ngoài site
    foreach ($array_ticket_status as $key) {
        $xtpl->assign('OPTION', [
            'key' => $key,
            'title' => TicketStatus::tryFrom($key)->getLabel(),
            'selected' => $key == $array_search['status'] ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.select_status');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_theme_supportticket_detail()
 *
 * @param mixed $array_data
 * @return
 */
function nv_theme_supportticket_detail($array_data, $array_comment, $comment, $error, $page_url, $generate_page, $customer_info, $customs_points, $attach_files, $user_vips, $user_orders, $is_open_ticket)
{
    global $module_info, $user_info, $op, $array_ticket_cats, $array_ticket_labels, $nv_Lang, $module_config, $module_name;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('FORM_ACTION', $page_url);
    if (isset($array_data['link_update'])) {
        $xtpl->assign('FORM_UPDATE', $array_data['link_update']);
    }
    $xtpl->assign('TICKET_ID', $array_data['id']);
    $xtpl->assign('TOKEN', NV_CHECK_SESSION);
    $xtpl->assign('MUADIEM', NV_BASE_SITEURL . NV_LANG_DATA . '/points/#muadiem');
    $xtpl->assign('ATTACH_LIMITED', NV_ATTACH_LIMITED);
    $xtpl->assign('ATTACH_LIMITED_MESSAGE', sprintf($nv_Lang->getModule('error_upload_limited'), NV_ATTACH_LIMITED));
    $xtpl->assign('LIMITED_FILE_SIZE', sprintf($nv_Lang->getModule('limited_file_size'), NV_ATTACH_LIMITED, nv_convertfromBytes(NV_UPLOAD_MAX_FILESIZE), NV_MAX_WIDTH, NV_MAX_HEIGHT));

    if (in_array($array_data['cat_id'], [10, 11])) {
        $point_info = [
            'bonus_point' => $nv_Lang->getModule('number_of_point', number_format($array_data['bonus_point'], 0, '.', '.')),
            'url_point_history' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['point_log'] . '&amp;id=' . $array_data['id'],
        ];
        $xtpl->assign('POINT', $point_info);
        $xtpl->parse('main.handle_plus_point');
    }

    $customer_info['fullname'] = nv_show_name_user($customer_info['first_name'], $customer_info['last_name'], $customer_info['username']);
    $xtpl->assign('CUSTOMER', $customer_info);
    $category = $array_ticket_cats[$array_data['cat_id']];
    $category['title'] = $category['title_' . NV_LANG_DATA];
    $xtpl->assign('CATEGORY', $category);
    if (isset($user_vips[$array_data['vip_id']]) && $array_data['vip_id'] > 0) {
        $xtpl->assign('VIP', $user_vips[$array_data['vip_id']]['title']);
        $xtpl->parse('main.vip');
    }
    if (isset($user_orders[$array_data['order_id']]) && $array_data['order_id'] > 0) {
        $xtpl->assign('ORDER', $user_orders[$array_data['order_id']]['title'] . ' (' . nv_date('d/m/Y', $user_orders[$array_data['order_id']]['add_time']) . ')');
        $xtpl->parse('main.order');
    }

    //Bật tắt nhận thông báo có cmt qua mail
    $array_data['notify'] = empty($array_data['notify']) ? '' : ' checked="checked"';
    $xtpl->assign('NOTIFY', $array_data['notify']);

    $array_data['file_attach'] = decode_file($array_data['file_attach']);
    if (sizeof($array_data['file_attach']) > 0) {
        foreach ($array_data['file_attach'] as $file) {
            $attached = [];
            $attached['name'] = $file;
            $attached['link'] = $page_url . '&amp;preview=' . $file;
            $xtpl->assign('FILE', $attached);
            $xtpl->parse('main.detail_attach.loop');
        }
        $xtpl->parse('main.detail_attach');
    }

    if (!empty($array_data['assignee_list'])) {
        $xtpl->assign('ASSIGNEE', $array_data['assignee_list']);
        $xtpl->parse('main.assignee');
    }
    if (!empty($array_data['label_ids'])) {
        $label_title = [];
        $array_data['label_ids'] = explode(',', $array_data['label_ids']);
        foreach ($array_data['label_ids'] as $item) {
            $label = $array_ticket_labels[$item];
            $label_title[] = $label['title_' . NV_LANG_DATA];
        }
        $xtpl->assign('LABEL', implode(', ', $label_title));
        $xtpl->parse('main.label');
    }

    if ($array_data['customer_id'] == $user_info['userid'] and $array_data['status'] != 4) {
        $xtpl->parse('main.show_feedback');
        if (!defined('NV_EDITOR')) {
            define('NV_EDITOR', 'ckeditor5-classic');
        }
        if (defined('NV_EDITOR')) {
            require_once NV_ROOTDIR . '/' . NV_EDITORSDIR . '/' . NV_EDITOR . '/nv.php';
        }
        if (nv_function_exists('nv_aleditor')) {
            $comment_content = nv_aleditor('commentContent', '100%', '150px', $comment['content'] ?? '', 'User');
            $xtpl->assign('COMMENT', $comment_content);
        }
        if (!empty($error)) {
            $xtpl->assign('ERROR', $error);
            $xtpl->parse('main.can_reply.error');
        }
        $total_file = !empty($attach_files) ? count($attach_files) + 1 : 0;
        $xtpl->assign('TOTAL_FILE', $total_file);
        if (!empty($attach_files)) {
            foreach ($attach_files as $key => $file) {
                $file['index'] = $key + 1;
                $xtpl->assign('FILE', $file);
                $xtpl->parse('main.can_reply.files');
            }
        }

        $xtpl->parse('main.can_reply');
    }

    //Cho phép KH đóng ticket
    if ($array_data['status'] != TicketStatus::Close->value && $array_data['status_client'] != TicketStatusClient::Close->value) {
        $xtpl->parse('main.close_ticket');
    }

    // #issue 2273: Bổ sung tính năng mở lại ticket
    if ($is_open_ticket) {
        $content_open = sprintf($nv_Lang->getModule('title_open_reply'), $module_config[$module_name]['close_unreply'], $module_config[$module_name]['open_reply'], $page_url, $array_data['id']);
        $xtpl->assign('CONTENT_OPEN_REPLY', $content_open);
        $xtpl->parse('main.open_reply');
    }

    $array_data['status'] = TicketStatusClient::tryFrom($array_data['status_client'])?->getLabel() ?? '';
    $array_data['activity_time'] = $array_data['activity_time'] > 0 ? nv_date('H:i d/m/Y', $array_data['activity_time']) : '';
    $array_data['pay_status'] = $array_data['payment_status'] == 2 ? $nv_Lang->getModule('payment_status2') : ($array_data['payment_status'] == 1 ? $nv_Lang->getModule('payment_status1') : $nv_Lang->getModule('payment_status3'));
    $xtpl->assign('ROW', $array_data);

    //Danh mục tính điểm
    if (isset($array_ticket_cats[$array_data['cat_id']]) && $array_ticket_cats[$array_data['cat_id']]['is_point'] == 1) {
        if ($array_data['point_price'] > 0) {
            $xtpl->assign('POINT_TOTAL', number_format($customs_points['point_total'], 0, '.', '.'));
            $xtpl->parse('main.is_point.display');
        }
        if ($array_data['payment_status'] == 2 && $array_data['point_price'] == 0 || $array_data['payment_status'] == 3 && $array_data['point_price'] > 0) {
            $xtpl->parse('main.is_point.remind');
        } elseif ($array_data['payment_status'] == 2 && $array_data['point_price'] > 0) {
            $xtpl->parse('main.is_point.submit');
        }
        $xtpl->parse('main.is_point');
    }

    if (!empty($array_comment)) {
        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.comments.generate_page');
        }
        foreach ($array_comment as $cmt) {
            if (sizeof($cmt['file_attach']) > 0) {
                foreach ($cmt['file_attach'] as $file) {
                    $attached = [];
                    $attached['name'] = $file;
                    $attached['link'] = $page_url . '&amp;preview=' . $file;
                    $xtpl->assign('FILE', $attached);
                    $xtpl->parse('main.comments.loop.detail_attach.loop');
                }
                $xtpl->parse('main.comments.loop.detail_attach');
            }
            if (!empty($cmt['user_extend']) && $cmt['user_extend']['signature_state'] == 'on') {
                $xtpl->assign('SIGNATURE', $cmt['user_extend']['signature_' . NV_LANG_DATA]);
                $xtpl->parse('main.comments.loop.signature');
            }
            $xtpl->assign('COMMENT', $cmt);
            $xtpl->parse('main.comments.loop');
        }
        $xtpl->parse('main.comments');
    }

    if ($array_data['cat_id'] == 10 and !empty($array_data['link_error'])) {
        $xtpl->assign('LINK_ERROR', nv_htmlspecialchars($array_data['link_error']));
        $xtpl->parse('main.link_error');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_supportticket_detail_paid($array_data, $ai_comments, $expert_comments, $page_url, $customs_points, $ticket_points, $is_open_ticket, $is_comment_refund)
{
    global $module_info, $user_info, $op, $array_ticket_cats, $array_ticket_labels, $nv_Lang, $module_config, $module_name;

    $xtpl = new XTemplate('detail_paid.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('FORM_ACTION', $page_url);
    $xtpl->assign('TICKET_ID', $array_data['id']);
    $xtpl->assign('TOKEN', NV_CHECK_SESSION);
    $xtpl->assign('TEMPLATE', 'default');
    $xtpl->assign('ACTION', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail_paid'] . '&amp;id=' . $array_data['id']);
    $xtpl->assign('ATTACH_LIMITED', NV_ATTACH_LIMITED);
    $xtpl->assign('ATTACH_LIMITED_MESSAGE', sprintf($nv_Lang->getModule('error_upload_limited'), NV_ATTACH_LIMITED));
    $xtpl->assign('LIMITED_FILE_SIZE', sprintf($nv_Lang->getModule('limited_file_size'), NV_ATTACH_LIMITED, nv_convertfromBytes(NV_UPLOAD_MAX_FILESIZE), NV_MAX_WIDTH, NV_MAX_HEIGHT));

    $ticket_points = ($ticket_points >= 0) ? 0 : $ticket_points * -1;
    $point_info = [
        'current_point' => $nv_Lang->getModule('number_of_point', number_format($customs_points['point_total'], 0, '.', '.')),
        'url_get_point' => NV_BASE_SITEURL . NV_LANG_DATA . '/points/#muadiem',
        'number_of_point' => $nv_Lang->getModule('number_of_point', $ticket_points),
        'url_point_history' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['point_log'] . '&amp;id=' . $array_data['id'],
    ];
    $xtpl->assign('POINT', $point_info);

    $ticket_info = [
        'customer' => get_user_info($array_data['customer_id']),
        'category' => $array_ticket_cats[$array_data['cat_id']]['title_' . NV_LANG_DATA],
        'status' => TicketStatusClient::tryFrom($array_data['status_client'])?->getLabel(),
    ];
    $xtpl->assign('TICKET', $ticket_info);

    $notify_point = [
        'notify_min_point_ai' => $nv_Lang->getModule('notify_min_point_ai', number_format($array_data['min_point_ai'], 0, '.', '.')),
        'notify_min_point_ai_additional' => $nv_Lang->getModule('notify_min_point_ai_additional', number_format($array_data['min_point_ai'], 0, '.', '.')),
        'notify_min_point_expert_additional' => $nv_Lang->getModule('notify_min_point_expert_additional', number_format($array_data['min_point_expert'], 0, '.', '.')),
        'notify_min_point_expert' => $nv_Lang->getModule('notify_min_point_expert', number_format($array_data['min_point_expert'], 0, '.', '.')),
        'notify_min_point_both' => $nv_Lang->getModule('notify_min_point_both', number_format($array_data['min_point_ai'] + $array_data['min_point_expert'], 0, '.', '.'), number_format($array_data['min_point_ai'], 0, '.', '.'), number_format($array_data['min_point_expert'], 0, '.', '.')),
        'notify_insufficient_point_view' => $nv_Lang->getModule('notify_insufficient_point_view', $point_info['url_get_point']),
        'min_point_expert' => $array_data['min_point_expert'],
        'disabled_ai' => ($array_data['ask_ai'] == 1 || $array_data['status'] == TicketStatus::Close->value) ? 'disabled' : '',
        'disabled_expert' => ($array_data['ask_expert'] == 1 || $array_data['status'] == TicketStatus::Close->value) ? 'disabled' : '',
        'checked_ai' => ($array_data['ask_ai'] == 1) ? 'checked' : '',
        'checked_expert' => ($array_data['ask_expert'] == 1) ? 'checked' : '',
    ];
    $xtpl->assign('NOTIFY', $notify_point);

    $ai_comment = empty($ai_comments) ? ['log_id' => 0, 'comment_type' => 0, 'rating_number' => 0, 'refund_status' => 0] : $ai_comments[0];
    $display_rating = empty($ai_comments) ? 'none' : (($ai_comments[0]['payment_status'] == PaymentStatus::Done->value) ? 'block' : 'none');
    $display_additional = empty($ai_comments) ? 'none' : (($ai_comments[0]['status'] == CommentStatus::Invalid->value && sizeof($ai_comments[0]['children']) == 0 ? 'block' : 'none'));
    $ai_comment = [
        'id' => empty($ai_comments) ? 0 : $ai_comments[0]['log_id'],
        'question' => nv_only_text($array_data['content']),
        'reply' => (!empty($ai_comments) && $ai_comments[0]['payment_status'] == PaymentStatus::Process->value && $customs_points['point_total'] >= $array_data['min_point_ai']) ? 1 : 0,
        'display' => empty($ai_comments) ? 'none' : 'block',
        'content' => empty($ai_comments) ? '' : (($ai_comments[0]['status'] == CommentStatus::Invalid->value) ? $nv_Lang->getModule('invalid_ai_content') : $ai_comments[0]['content']),
        'time' => empty($ai_comments) ? '' : (empty($ai_comments[0]['edit_time']) ? '' : nv_date('H:i:s d/m/Y', $ai_comments[0]['edit_time'])),
        'rating' => get_rating_component($array_data, $ai_comment, $display_rating, $display_additional),
    ];
    $xtpl->assign('AI_COMMENT', $ai_comment);

    //Danh sách AI comment
    if (!empty($ai_comments) && sizeof($ai_comments[0]['children']) > 0  && $array_data['ask_ai'] == 1) {
        $count_question = 0;
        foreach ($ai_comments[0]['children'] as $child_comment) {
            ++$count_question;
            if (sizeof($child_comment['children']) > 0) {
                foreach ($child_comment['children'] as $grandchild_comment) {
                    if (sizeof($grandchild_comment['children']) > 0) {
                        foreach ($grandchild_comment['children'] as $great_grandchild_comment) {
                            $great_grandchild_comment['author_comment'] = $nv_Lang->getModule('reply_extent_from_expert', get_user_info($great_grandchild_comment['reply_userid'])['fullname']);
                            $great_grandchild_comment['reply_from'] = $nv_Lang->getModule('reply_extent_from_expert', get_user_info($great_grandchild_comment['reply_userid'])['fullname']);
                            $great_grandchild_comment['time'] = nv_date('H:i:s d/m/Y', $great_grandchild_comment['edit_time'] ? $great_grandchild_comment['add_time'] : $great_grandchild_comment['edit_time']);
                            $great_grandchild_comment['display_id'] = $grandchild_comment['payment_status'] == PaymentStatus::Process->value ? 'ai_reply_content_addtition' : 'ai_reply_content_' . $grandchild_comment['log_id'];
                            $xtpl->assign('GREAT_GRANDCHILD_COMMENT', $great_grandchild_comment);
                            $xtpl->parse('main.ai_child_comment.loop.grandchild.loop.great_grandchild.loop');
                        }
                        $xtpl->parse('main.ai_child_comment.loop.grandchild.loop.great_grandchild');
                    }
                    $grandchild_comment['time'] = empty($grandchild_comment['edit_time']) ? '' : nv_date('H:i:s d/m/Y', $grandchild_comment['edit_time']);
                    $grandchild_comment['display_id'] = $grandchild_comment['payment_status'] == PaymentStatus::Process->value ? 'ai_reply_content_addtition' : 'ai_reply_content_' . $grandchild_comment['log_id'];
                    $display_rating = ($grandchild_comment['payment_status'] == PaymentStatus::Done->value) ? 'block' : 'none';
                    // $display_additional = ($grandchild_comment['payment_status'] == PaymentStatus::Done->value && sizeof($ai_comments[0]['children']) == $count_question && $array_data['status'] != TicketStatus::Close->value) ? 'block' : 'none';
                    $display_additional = 'none'; //Với AI chỉ hỏi bổ sung 1 lần duy nhất, sau này có cơ chế khác thì sửa lại
                    $grandchild_comment['rating'] = get_rating_component($array_data, $grandchild_comment, $display_rating, $display_additional);
                    $grandchild_comment['content'] = ($grandchild_comment['status'] == CommentStatus::Invalid->value) ? $nv_Lang->getModule('invalid_ai_content_additional') : $grandchild_comment['content'];
                    $xtpl->assign('AI_GRANDCHILD', $grandchild_comment);
                    $xtpl->parse('main.ai_child_comment.loop.grandchild.loop');
                }
                $xtpl->parse('main.ai_child_comment.loop.grandchild');
            }
            if ($child_comment['comment_type'] == CommentType::ExpertAdditionalForAI->value) {
                $author_comment = $nv_Lang->getModule('reply_extent_from_expert', get_user_info($child_comment['reply_userid'])['fullname']);
            } else {
                $author_comment = $nv_Lang->getModule('your_additional');
            }
            $child_comment['author_comment'] = $author_comment;
            $child_comment['time'] = empty($child_comment['add_time']) ? '' : nv_date('H:i:s d/m/Y', $child_comment['add_time']);
            $child_comment['question'] = nv_only_text($child_comment['content']);
            $child_comment['display_id'] = $child_comment['status'] == CommentStatus::Process->value ? 'ai_reply_content_addtition' : 'ai_reply_content_' . $child_comment['log_id'];
            $xtpl->assign('AI_CHILD', $child_comment);
            $xtpl->parse('main.ai_child_comment.loop');
        }
        $xtpl->parse('main.ai_child_comment');
    }

    //Danh sách chuyên gia comment
    if (!empty($expert_comments)) {
        //Danh sách comment gốc của các chuyên gia
        foreach ($expert_comments as $parent_comment) {
            $total_question = 0;
            if (sizeof($parent_comment['children']) > 0) {
                //Danh sách câu trả lời bổ sung của chuyên gia, câu hỏi bổ sung của KH
                $filtered_children = array_filter($parent_comment['children'], function ($child) {
                    return isset($child['comment_type']) && $child['comment_type'] == CommentType::Customer->value;
                });
                $total_question = count($filtered_children);
                $count_question = 0;
                foreach ($parent_comment['children'] as $child_comment) {
                    if ($child_comment['comment_type'] ==  CommentType::Customer->value) {
                        ++$count_question;
                    }
                    if (sizeof($child_comment['children']) > 0) {
                        //Câu trả lời của chuyên gia cho câu hỏi bổ sung
                        foreach ($child_comment['children'] as $grandchild_comment) {
                            if (sizeof($grandchild_comment['children']) > 0) {
                                //Câu trả lời bổ sung cho câu trả lời bên trên
                                foreach ($grandchild_comment['children'] as $additional_answer) {
                                    $additional_answer = get_answer_additional_component($additional_answer, $grandchild_comment);
                                    $xtpl->assign('ADDITIONAL_ANSWER', $additional_answer);
                                    $xtpl->parse('main.expert_comment.loop.child.loop.grandchild.loop.additional.loop');
                                }
                                $xtpl->parse('main.expert_comment.loop.child.loop.grandchild.loop.additional');
                            }
                            $grandchild_comment['time'] = empty($grandchild_comment['edit_time']) ? nv_date('H:i:s d/m/Y', $grandchild_comment['add_time']) : nv_date('H:i:s d/m/Y', $grandchild_comment['edit_time']);
                            $display_rating = ($grandchild_comment['payment_status'] == PaymentStatus::Done->value) ? 'block' : 'none';
                            $display_additional = ($grandchild_comment['payment_status'] == PaymentStatus::Done->value && $total_question == $count_question && $array_data['status'] != TicketStatus::Close->value) ? 'block' : 'none';
                            $grandchild_comment['payment'] = ($grandchild_comment['payment_status'] == PaymentStatus::Process->value && $grandchild_comment['comment_type'] == CommentType::Expert->value) ? get_payment_component($grandchild_comment, $customs_points) : '';
                            $grandchild_comment['rating'] = get_rating_component($array_data, $grandchild_comment, $display_rating, $display_additional);
                            $expert = get_user_info($grandchild_comment['reply_userid']);
                            $grandchild_comment['caption'] = $nv_Lang->getModule('reply_from_expert', $expert['fullname']);
                            $grandchild_comment['content'] = get_content_component($grandchild_comment);
                            $xtpl->assign('EXPERT_GRANDCHILD', $grandchild_comment);
                            $xtpl->parse('main.expert_comment.loop.child.loop.grandchild.loop');
                        }
                        $xtpl->parse('main.expert_comment.loop.child.loop.grandchild');
                    }
                    $child_comment = ($child_comment['comment_type'] == CommentType::Customer->value) ? get_question_component($child_comment) : get_answer_additional_component($child_comment, $parent_comment);
                    $xtpl->assign('EXPERT_CHILD', $child_comment);
                    $xtpl->parse('main.expert_comment.loop.child.loop');
                }
                $xtpl->parse('main.expert_comment.loop.child');
            }
            $parent_comment['time'] = empty($parent_comment['edit_time']) ? nv_date('H:i:s d/m/Y', $parent_comment['add_time']) : nv_date('H:i:s d/m/Y', $parent_comment['edit_time']);
            $display_rating = ($parent_comment['payment_status'] == PaymentStatus::Done->value) ? 'block' : 'none';
            $display_additional = ($parent_comment['payment_status'] == PaymentStatus::Done->value && $total_question == 0 && $array_data['status'] != TicketStatus::Close->value) ? 'block' : 'none';
            $parent_comment['payment'] = ($parent_comment['payment_status'] == PaymentStatus::Process->value && $parent_comment['comment_type'] == CommentType::Expert->value) ? get_payment_component($parent_comment, $customs_points) : '';
            $parent_comment['rating'] = get_rating_component($array_data, $parent_comment, $display_rating, $display_additional);
            $expert = get_user_info($parent_comment['reply_userid']);
            $parent_comment['caption'] = $nv_Lang->getModule('reply_from_expert', $expert['fullname']);
            $parent_comment['content'] = get_content_component($parent_comment);
            $xtpl->assign('EXPERT_PARENT', $parent_comment);
            // Danh sách file
            if ($array_data['ask_expert'] == 1) {
                $total_file = !empty($attach_files) ? count($attach_files) + 1 : 0;
                $xtpl->assign('TOTAL_FILE', $total_file);
                if (!empty($attach_files)) {
                    foreach ($attach_files as $key => $file) {
                        $file['index'] = $key + 1;
                        $xtpl->assign('FILE', $file);
                        $xtpl->parse('main.expert_comment.loop.ask_expert_file.files');
                    }
                }
                $xtpl->parse('main.expert_comment.loop.ask_expert_file');
            }
            $xtpl->parse('main.expert_comment.loop');
        }
        $xtpl->parse('main.expert_comment');
    }

    $array_data['file_attach'] = decode_file($array_data['file_attach']);
    if (sizeof($array_data['file_attach']) > 0) {
        $xtpl->parse('main.waring_ask_ai_file_attach');
        $xtpl->parse('main.waring_ask_all_file_attach');
        foreach ($array_data['file_attach'] as $file) {
            $attached = [];
            $attached['name'] = $file;
            $attached['link'] = $page_url . '&amp;preview=' . $file;
            $xtpl->assign('FILE', $attached);
            $xtpl->parse('main.detail_attach.loop');
        }
        $xtpl->parse('main.detail_attach');
    }

    if (!empty($array_data['assignee_list'])) {
        $xtpl->assign('ASSIGNEE', $array_data['assignee_list']);
        $xtpl->parse('main.assignee');
    }
    if (!empty($array_data['label_ids'])) {
        $label_title = [];
        $array_data['label_ids'] = explode(',', $array_data['label_ids']);
        foreach ($array_data['label_ids'] as $item) {
            $label = $array_ticket_labels[$item];
            $label_title[] = $label['title_' . NV_LANG_DATA];
        }
        $xtpl->assign('LABEL', implode(', ', $label_title));
        $xtpl->parse('main.label');
    }

    // if ($array_data['customer_id'] == $user_info['userid']) {
    //     $xtpl->parse('main.show_feedback');
    //     if (!defined('NV_EDITOR')) {
    //         define('NV_EDITOR', 'ckeditor5-classic');
    //     }
    //     if (defined('NV_EDITOR')) {
    //         require_once NV_ROOTDIR . '/' . NV_EDITORSDIR . '/' . NV_EDITOR . '/nv.php';
    //     }
    //     if (nv_function_exists('nv_aleditor')) {
    //         $comment_content = nv_aleditor('commentContent', '100%', '150px', $comment['content'] ?? '', 'User');
    //         $xtpl->assign('COMMENT', $comment_content);
    //     }
    //     if (!empty($error)) {
    //         $xtpl->assign('ERROR', $error);
    //         $xtpl->parse('main.can_reply.error');
    //     }
    //     $total_file = !empty($attach_files) ? count($attach_files) + 1 : 0;
    //     $xtpl->assign('TOTAL_FILE', $total_file);
    //     if (!empty($attach_files)) {
    //         foreach ($attach_files as $key => $file) {
    //             $file['index'] = $key + 1;
    //             $xtpl->assign('FILE', $file);
    //             $xtpl->parse('main.can_reply.files');
    //         }
    //     }

    //     $xtpl->parse('main.can_reply');
    // }

    // Cho phép KH cập nhật ticket
    if ($array_data['status_client'] == TicketStatusClient::Draft->value) {
        $xtpl->assign('FORM_UPDATE', $array_data['link_update']);
        $xtpl->parse('main.update_ticket');
    } else {
        $xtpl->assign('ADD_TICKET', $array_data['link_add']);
        $xtpl->parse('main.add_ticket');
    }

    //Cho phép KH đóng ticket
    if ($array_data['status_client'] != TicketStatusClient::Close->value) {
        // Cảnh báo khi khách hàng đóng ticket mà có yc hoàn điểm
        $message_waring_close = $nv_Lang->getModule($is_comment_refund ? 'waring_ticket_close' : 'ticket_close_message');
        $xtpl->assign('WARING_CLOSE_TICKET', $message_waring_close);
        $xtpl->parse('main.close_ticket');
    }

    // Cho phép KH xóa ticket lưu nháp
    if ($array_data['status'] == TicketStatus::Draft->value && $array_data['delete_time'] == 0) {
        $xtpl->parse('main.delete_ticket');
    }

    // #issue 2273: Bổ sung tính năng mở lại ticket
    if ($is_open_ticket) {
        $content_open = sprintf($nv_Lang->getModule('title_open_reply'), $module_config[$module_name]['close_unreply'], $module_config[$module_name]['open_reply'], $page_url, $array_data['id']);
        $xtpl->assign('CONTENT_OPEN_REPLY', $content_open);
        $xtpl->parse('main.open_reply');
    }

    $array_data['status'] = TicketStatusClient::tryFrom($array_data['status_client'])->getLabel();
    $array_data['customer_edit_time'] = $array_data['customer_edit_time'] > 0 ? nv_date('H:i d/m/Y', $array_data['customer_edit_time']) : '';
    $array_data['pay_status'] = $array_data['payment_status'] == 2 ? $nv_Lang->getModule('payment_status2') : ($array_data['payment_status'] == 1 ? $nv_Lang->getModule('payment_status1') : $nv_Lang->getModule('payment_status3'));
    $xtpl->assign('ROW', $array_data);

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_theme_supportticket_add()
 *
 * @param mixed $array_data
 * @return
 */
function nv_theme_supportticket_add($array_data, $attach_files_old, $error, $get_user_vips, $get_user_orders)
{
    global $module_info, $module_name, $op, $array_active_cats, $array_active_labels, $array_ticket_status, $nv_Lang, $module_config;

    $xtpl = new XTemplate('add.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('ROW', $array_data);
    $xtpl->assign('ATTACH_LIMITED', NV_ATTACH_LIMITED);
    $xtpl->assign('NV_UPLOAD_MAX_FILESIZE', NV_UPLOAD_MAX_FILESIZE);
    $xtpl->assign('ATTACH_LIMITED_MESSAGE', sprintf($nv_Lang->getModule('error_upload_limited'), NV_ATTACH_LIMITED));
    $xtpl->assign('LIMITED_FILE_SIZE', sprintf($nv_Lang->getModule('limited_file_size'), NV_ATTACH_LIMITED, nv_convertfromBytes(NV_UPLOAD_MAX_FILESIZE), NV_MAX_WIDTH, NV_MAX_HEIGHT));
    $xtpl->assign('MAX_TITLE_LENGTH', $module_config[$module_name]['max_title_length']);
    $xtpl->assign('ERROR_MAX_TITLE_LENGTH', $nv_Lang->getModule('error_length_title', $module_config[$module_name]['max_title_length']));

    $action_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
    $xtpl->assign('ACTION', $action_url);
    $xtpl->assign('CAPTION', ($array_data['id'] > 0) ? $nv_Lang->getModule('ticket_edit') : $nv_Lang->getModule('ticket_add'));

    if (!defined('NV_EDITOR')) {
        define('NV_EDITOR', 'ckeditor5-classic');
    }
    if (defined('NV_EDITOR')) {
        require_once NV_ROOTDIR . '/' . NV_EDITORSDIR . '/' . NV_EDITOR . '/nv.php';
    }
    if (nv_function_exists('nv_aleditor')) {
        $array_data['content'] = nv_aleditor('content', '100%', '300px', $array_data['content'], 'User');
        $xtpl->assign('CONTENT', $array_data['content']);
    }

    foreach ($array_active_cats as $value) {
        $title = NV_LANG_DATA == 'vi' ? $value['title_vi'] : $value['title_en'];
        $is_point = $value['is_point'] === 1 ? ' (' . $nv_Lang->getModule('is_point') . ')' : '';
        $xtpl->assign('OPTION', [
            'key' => $value['cat_id'],
            'charge' => $value['is_point'],
            'price' => $value['point_price'],
            'message' => sprintf($nv_Lang->getModule('point_price_message'), $value['point_price']),
            'title' => $title . $is_point,
            'selected' => ($value['cat_id'] == $array_data['cat_id']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.select_cat_id');
    }

    $array_data['label_ids'] = empty($array_data['label_ids']) ? [] : explode(',', $array_data['label_ids']);
    foreach ($array_active_labels as $value) {
        $xtpl->assign('OPTION', [
            'key' => $value['label_id'],
            'title' => NV_LANG_DATA == 'vi' ? $value['title_vi'] : $value['title_en'],
            'selected' => in_array($value['label_id'], $array_data['label_ids']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.select_label_ids');
    }

    foreach ($array_ticket_status as $key => $value) {
        $xtpl->assign('OPTION', [
            'key' => $key,
            'title' => $value,
            'selected' => ($key == $array_data['status']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.select_status');
    }

    foreach ($get_user_vips as $vip) {
        $xtpl->assign('OPTION', [
            'key' => $vip['id'],
            'title' => $vip['title'],
            'selected' => ($vip['id'] == $array_data['vip_id']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.select_vip');
    }

    foreach ($get_user_orders as $order) {
        $order_title = $order['id'] > 0 ? $order['title'] . ' (' . nv_date('d/m/Y', $order['add_time']) . ')' : $order['title'];
        $xtpl->assign('OPTION', [
            'key' => $order['id'],
            'title' => $order_title,
            'selected' => ($order['id'] == $array_data['order_id']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.select_order');
    }

    $attach_files = decode_file($attach_files_old);
    $total_file = !empty($attach_files) ? count($attach_files) + 1 : 0;
    $xtpl->assign('TOTAL_FILE', $total_file);
    if (!empty($attach_files)) {
        foreach ($attach_files as $key => $file) {
            $xtpl->assign('FILE', $file);
            $xtpl->parse('main.files');
        }
    }

    if (!empty($error)) {
        $xtpl->assign('ERROR', $error);
        $xtpl->parse('main.error');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_supportticket_point_log($row, $array_data, $generate_page, $customer_info, $user_money, $customs_points, $ticket_points)
{
    global $module_info, $op, $module_name, $module_name, $nv_Lang;
    $xtpl = new XTemplate('point_log.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    $ticket_points = ($ticket_points >= 0) ? 0 : $ticket_points * -1;
    $customer = [
        'total_of_point' => $nv_Lang->getModule('total_of_point', $row['id']),
        'number_of_point' => $nv_Lang->getModule('number_of_point', $ticket_points),
        'spending_user' => $customer_info['fullname'] . ' (' . $customer_info['username'] . ')',
        'wallet_balance' => $user_money['money_total'] . ' ' . $user_money['money_unit'],
        'current_point' => $nv_Lang->getModule('number_of_point', number_format($customs_points['point_total'], 0, '.', '.')),
        'url_get_point' => NV_BASE_SITEURL . NV_LANG_DATA . '/points/#muadiem',
    ];
    $xtpl->assign('CUSTOMER', $customer);
    $xtpl->assign('HISTORY_TITLE',  $nv_Lang->getModule('point_history_title', $row['id']),);

    if (!empty($array_data)) {
        foreach ($array_data as $view) {
            $view['add_time'] = nv_date('H:i d/m/Y', $view['add_time']);
            $view['point'] = $view['status'] == 1 ? '+' . $view['point'] : '-' . $view['point'];
            $xtpl->assign('VIEW', $view);
            $xtpl->parse('main.data.loop');
        }

        if ($generate_page) {
            $xtpl->assign('GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.data.page');
        }
        $xtpl->parse('main.data');
    } else {
        $xtpl->parse('main.empty');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}
