<?php

use NukeViet;

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) die('Stop!!!');

if ($nv_Request->isset_request('download', 'get')) {
    $download = $nv_Request->get_int('download', 'post, get', 1);
    if ($download == 1) {
        $file_src = NV_ROOTDIR . '/modules/' . $module_file . '/template_import_leads.xlsx';
        $file_basename = change_alias($nv_Lang->getModule('import_filename')) . '.xlsx';
    } else {
        $file_src = NV_ROOTDIR . '/modules/' . $module_file . '/template_telepro.xls';
        $file_basename = change_alias($nv_Lang->getModule('import_sel_datatype0')) . '.xlsx';
    }

    $directory = NV_ROOTDIR . '/modules/' . $module_file;
    $download = new NukeViet\Files\Download($file_src, $directory, $file_basename, true, 0);
    $download->download_file();
    exit();
}

$page_title = $nv_Lang->getModule('import');
set_time_limit(50000);
ini_set('memory_limit', '3500M');
ini_set('max_execution_time', 50000);
$post = array();
$error = '';
$import_result = array(
    'num_read' => 0,
    'num_install' => 0,
    'num_update' => 0
);
$import_result_show = false;

if ($nv_Request->isset_request('submit', 'post')) {
    $post['existstype'] = $nv_Request->get_int('existstype', 'post', 0);
    $post['datatype'] = $nv_Request->get_int('datatype', 'post', 0);

    if (!isset($_FILES, $_FILES['fileexcel'], $_FILES['fileexcel']['tmp_name'])) {
        $error = $nv_Lang->getModule('import_error_file1');
    } elseif (!is_uploaded_file($_FILES['fileexcel']['tmp_name'])) {
        $error = $nv_Lang->getModule('import_error_file1');
    } else {
        $upload = new NukeViet\Files\Upload(array(
            'documents'
        ), $global_config['forbid_extensions'], $global_config['forbid_mimes'], NV_UPLOAD_MAX_FILESIZE, NV_MAX_WIDTH, NV_MAX_HEIGHT);
        $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
        $upload_info = $upload->save_file($_FILES['fileexcel'], NV_ROOTDIR . '/' . NV_TEMP_DIR, false, $global_config['nv_auto_resize']);
        if (!empty($upload_info['error'])) {
            $error = $upload_info['error'];
        } elseif (!in_array($upload_info['ext'], array(
            'xls',
            'xlsx'
        ))) {
            $error = $nv_Lang->getModule('import_error_file2');
        } elseif (!nv_class_exists('\PhpOffice\PhpSpreadsheet\IOFactory')) {
            $error = $nv_Lang->getModule('import_error_class');
        } else {
            $objPHPExcel = \PhpOffice\PhpSpreadsheet\IOFactory::load($upload_info['name']);
            try {
                $objWorksheet = $objPHPExcel->getActiveSheet();
            } catch (Exception $e) {
                $error = $nv_Lang->getModule('import_error_file3');
            }
        }
    }

    if (empty($error)) {
        $arrayadmin = array();
        $arrayadmin[1124] = 'Thu';
        $arrayadmin[34] = 'Hà';
        $arrayadmin[13] = 'Linh';
        $arrayadmin[11897] = 'Nga';
        $arrayadmin[12] = 'Ngọc';
        $arrayadmin[11895] = 'CTV Tiến';
        $arrayadmin[14285] = 'Mai Hà';
        $arrayadmin[13664] = 'Thu Hằng';

        if ($post['datatype'] == 1) {
            $array_data_read = array();
            $stt = 0;
            if ($post['datatype'] == 1) {
                // phản hồi qua trang khác
                $array_field = array();
                $array_field['A'] = 'company_name';
                $array_field['B'] = 'name';
                $array_field['C'] = 'phone';
                $array_field['D'] = 'email';
                $array_field['E'] = 'source_leads';
                $array_field['F'] = 'caregiver_id';
                $array_field['G'] = 'result';
                $array_field['H'] = 'status';
                $array_field['I'] = 'timecreate';

                $array_field['J'] = 'about';
                $array_field['K'] = 'tax';
                $array_field['L'] = 'user_id';

                $highestRow = $objWorksheet->getHighestRow();
                for ($row = 2; $row <= $highestRow; ++$row) {
                    $arrar_tmp = array();
                    foreach ($array_field as $col => $field) {
                        $cell = $objWorksheet->getCell('' . $col . $row . '');
                        if ($field == 'timecreate') {
                            $value = $cell->getFormattedValue();
                            if (preg_match('/^([0-9]+)\/([0-9]+)$/', $value, $m)) {
                                $value = mktime(0, 0, 0, $m[1], $m[2], '2019');
                            } else if (preg_match('/^([0-9]+)\/([0-9]+)\/([0-9]+)$/', $value, $m)) {
                                $value = mktime(0, 0, 0, $m[1], $m[2], '20' . $m[3]);
                            } else {
                                $value = NV_CURRENTTIME;
                            }
                        } else {
                            $value = trim($cell->getCalculatedValue());
                        }
                        if ($field == 'source_leads') {
                            $value = trim($cell->getCalculatedValue());
                            switch ($value) {
                                case 'Fanpage':
                                    $value = 4;
                                    break;
                                case 'Khác (các group, tự tìm)':
                                    $value = 7;
                                    break;
                                case 'Zalo admin':
                                    $value = 5;
                                    break;
                                case 'Tawt.to':
                                    $value = 6;
                                    break;
                                case 'SMS':
                                    $value = 8;
                                    break;
                                case 'Khách thành viên':
                                    $value = 1;
                                    break;
                                case 'DS Telepro':
                                    $value = 3;
                                    break;
                                case 'Nhà thầu':
                                    $value = 2;
                                    break;

                                default:
                                    $value = 7;
                                    break;
                            }
                        }
                        $arrar_tmp[$field] = $value;
                    }

                    // Trống phone thì kết thúc
                    if (empty($arrar_tmp['phone']) and empty($arrar_tmp['email'])) {
                        break;
                    } else {
                        $stt++;
                        $array_data_read[$stt] = $arrar_tmp;
                    }
                }
            }

            if (empty($error) and !empty($array_data_read)) {
                $import_result['num_read'] = sizeof($array_data_read);
                $import_result_show = true;
                try {
                    $db->beginTransaction();
                    foreach ($array_data_read as $row) {
                        $tmp = explode(',', $row['phone']);
                        foreach ($tmp as $k => $_tmp) {
                            if ($_tmp != '' && !preg_match('/^0[0-9]{9,10}$/', $_tmp)) {
                                $tmp[$k] = '0' . $_tmp;
                            } else {
                                $tmp[$k] = $_tmp;
                            }
                        }
                        $row['phone'] = $tmp[0];
                        unset($tmp[0]);
                        $row['sub_phone'] = implode(',', $tmp);

                        $tmp = explode(',', $row['email']);
                        $row['email'] = $tmp[0];
                        unset($tmp[0]);
                        $row['sub_email'] = implode(',', $tmp);

                        if (in_array($row['caregiver_id'], $arrayadmin)) {
                            $admin_id = array_keys($arrayadmin, $row['caregiver_id']);
                            $row['caregiver_id'] = $admin_id[0];
                        } else {
                            $row['caregiver_id'] = 0;
                        }

                        if ($row['status'] == 'Không có nhu cầu') {
                            $row['status'] = 3;
                        } else if ($row['status'] == 'Đã mua') {
                            $row['status'] = 2;
                        } else if ($row['caregiver_id'] > 0) {
                            $row['status'] = 1;
                        } else {
                            $row['status'] = 0;
                        }

                        if ($row['user_id'] != '') {
                            $result = $db->query("SELECT userid FROM " . NV_USERS_GLOBALTABLE . " WHERE username= " . $db->quote($row['user_id']));
                            if ($_row = $result->fetch()) {
                                $row['user_id'] = $_row['userid'];
                            } else {
                                $row['user_id'] = 0;
                            }
                        } else {
                            $row['user_id'] = 0;
                        }

                        if ($row['source_leads'] == 3) {
                            $result = $db->query("SELECT id FROM " . NV_PREFIXLANG . "_" . $module_data . "_telepro WHERE phone= " . $db->quote($row['phone']));
                            if ($_row = $result->fetch()) {
                                $row['teleproid'] = $_row['id'];
                            } else {
                                $row['teleproid'] = 0;
                            }
                        } else {
                            $row['teleproid'] = 0;
                        }

                        // Kiểm tra trùng lặp
                        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_leads WHERE phone=" . $db->quote($row['phone']);
                        $result = $db->query($sql);

                        if (!$result->rowCount()) {
                            $db->query("INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_leads (source_leads, user_id, teleproid, name, phone, sub_phone, email, sub_email, tax, company_name, status, caregiver_id, timecreate, active, about) VALUES (" . $db->quote($row['source_leads']) . ", " . $db->quote($row['user_id']) . ", " . $db->quote($row['teleproid']) . ", " . $db->quote($row['name']) . ", " . $db->quote($row['phone']) . ", " . $db->quote($row['sub_phone']) . ", " . $db->quote($row['email']) . ", " . $db->quote($row['sub_email']) . ", " . $db->quote($row['tax']) . ", " . $db->quote($row['company_name']) . ", " . $db->quote($row['status']) . ", " . $db->quote($row['caregiver_id']) . ", " . $db->quote($row['timecreate']) . ", 1, " . $db->quote($row['about']) . ")");

                            if ($_id = $db->lastInsertId()) {
                                $db->exec("DELETE FROM " . NV_PREFIXLANG . "_" . $module_data . "_comment WHERE sourceid = " . $_id . " AND source = 1");
                                $db->query("INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_comment (source, sourceid, timecreate, note) VALUES (1, " . $_id . ", " . $db->quote($row['timecreate']) . ", " . $db->quote($row['result']) . ")");
                                $import_result['num_install']++;
                            }
                        } else {
                            if (!empty($post['existstype'])) {
                                $_row = $result->fetch();
                                $exc = $db->exec("UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_leads SET user_id=" . $db->quote($row['user_id']) . ", teleproid=" . $db->quote($row['teleproid']) . ", name=" . $db->quote($row['name']) . ", phone=" . $db->quote($row['phone']) . ", sub_phone=" . $db->quote($row['sub_phone']) . ", email=" . $db->quote($row['email']) . ", sub_email=" . $db->quote($row['sub_email']) . ", tax=" . $db->quote($row['tax']) . ", company_name=" . $db->quote($row['company_name']) . ", status=" . $db->quote($row['status']) . ", caregiver_id=" . $db->quote($row['caregiver_id']) . ", timecreate=" . $db->quote($row['timecreate']) . ", about=" . $db->quote($row['about']) . " WHERE id = " . $_row['id']);
                                if ($exc) {
                                    $db->exec("DELETE FROM " . NV_PREFIXLANG . "_" . $module_data . "_comment WHERE sourceid = " . $_row['id'] . " AND source = 1");
                                    $db->query("INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_comment (source, sourceid, timecreate, note) VALUES (1, " . $_row['id'] . ", " . $db->quote($row['timecreate']) . ", " . $db->quote($row['result']) . ")");
                                    $import_result['num_update']++;
                                }
                            }
                        }
                    }
                    $db->commit();
                } catch (PDOException $e) {
                    $db->rollBack();
                    trigger_error($e);
                    die($nv_Lang->getModule('error_request'));
                }
            }
        } else if ($post['datatype'] == 0) {
            $array_data_read = array();
            $array_field = array();
            $array_field["E"] = 'phone';
            $array_field["T"] = 'name';
            $array_field['U'] = 'email';
            $array_field['N'] = 'note';
            $array_field['G'] = 'timecall';
            $array_field['V'] = 'thoigian_nghetuvan';
            $array_field['I'] = 'recording';
            $array_field['R'] = 'status';
            $array_field['D'] = 'about1';
            $array_field['O'] = 'about2';

            $highestRow = $objWorksheet->getHighestRow();
            $stt = 0;
            for ($row = 2; $row <= $highestRow; ++$row) {
                $arrar_tmp = array();
                foreach ($array_field as $col => $field) {
                    $cell = $objWorksheet->getCell('' . $col . $row . '');
                    if ($field == 'timecall') {
                        $value = $cell->getFormattedValue();
                        if (preg_match('/^([0-9]+)\/([0-9]+)\/([0-9]+)\s([0-9]+)\:([0-9]+)$/', $value, $m)) {
                            $value = mktime($m[4], $m[5], 0, $m[2], $m[1], $m[3]);
                        } else {
                            $value = NV_CURRENTTIME;
                        }
                    } else {
                        $value = trim($cell->getCalculatedValue());
                    }

                    $arrar_tmp[$field] = $value;
                }

                // Trống phone thì kết thúc
                if (empty($arrar_tmp['phone'])) {
                    break;
                } else {
                    $stt++;
                    $array_data_read[$stt] = $arrar_tmp;
                }
            }

            if (empty($error)) {
                $import_result['num_read'] = sizeof($array_data_read);
                $import_result_show = true;
                try {
                    $db->beginTransaction();
                    foreach ($array_data_read as $row) {

                        if ($row['phone'] != '' && !preg_match('/^0[0-9]{9,10}$/', $row['phone'])) {
                            $row['phone'] = '0' . $row['phone'];
                        }
                        // Kiểm tra trùng lặp
                        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_telepro WHERE phone=" . $db->quote($row['phone']);
                        $result = $db->query($sql);
                        if (!$result->rowCount()) {
                            $db->query("INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_telepro (phone, name, email, note, timecall, thoigian_nghetuvan, recording, status, about) VALUES (" . $db->quote($row['phone']) . "," . $db->quote($row['name']) . "," . $db->quote($row['email']) . "," . $db->quote($row['note']) . "," . $db->quote($row['timecall']) . "," . $db->quote($row['thoigian_nghetuvan']) . "," . $db->quote($row['recording']) . "," . $db->quote($row['status']) . ", " . $db->quote($row['about1'] . ' ' . $row['about2']) . ")");
                            if ($db->lastInsertId()) {
                                $import_result['num_install']++;
                            }
                        } elseif (!empty($post['existstype'])) {
                            $_row = $result->fetch();
                            $exc = $db->exec("UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_telepro SET phone=" . $db->quote($row['phone']) . ",name=" . $db->quote($row['name']) . ",email=" . $db->quote($row['email']) . ",note=" . $db->quote($row['note']) . ",timecall=" . $db->quote($row['timecall']) . ",thoigian_nghetuvan=" . $db->quote($row['thoigian_nghetuvan']) . ",recording=" . $db->quote($row['recording']) . ",status=" . $db->quote($row['status']) . ", about= " . $db->quote($row['about1'] . ' ' . $row['about2']) . " WHERE id = " . $_row['id']);
                            if ($exc) {
                                $import_result['num_update']++;
                            }
                        }
                    }
                    $db->commit();
                } catch (PDOException $e) {
                    $db->rollBack();
                    trigger_error($e);
                    die($nv_Lang->getModule('error_request'));
                }
            }
        } else if ($post['datatype'] == 2) { // BANKDATA
            $array_data_read = array();
            $array_field = array();
            $array_field["F"] = 'phone';
            $array_field["A"] = 'name';
            $array_field['H'] = 'email';
            $array_field['B'] = 'idnumber';
            $array_field['C'] = 'birthday';
            $array_field['D'] = 'sex';
            $array_field['E'] = 'job';
            $array_field['G'] = 'address';

            $highestRow = $objWorksheet->getHighestRow();
            $stt = 0;
            for ($row = 2; $row <= $highestRow; ++$row) {
                $arrar_tmp = array();
                foreach ($array_field as $col => $field) {
                    $cell = $objWorksheet->getCell('' . $col . $row . '');
                    if ($field == 'birthday') {
                        $value = $cell->getFormattedValue();
                        if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $value, $m)) {
                            $value = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
                        } else {
                            $value = 0;
                        }
                    } else {
                        $value = trim($cell->getValue());
                    }
                    if ($field == 'email') {
                        $value = strtolower($value);
                    }
                    $value = $crypt->encrypt($value);
                    $arrar_tmp[$field] = $value;
                }
                if (empty($arrar_tmp['name'])) {
                    break;
                } else {
                    $stt++;
                    $array_data_read[$stt] = $arrar_tmp;
                }
            }
            if (empty($error)) {
                $import_result['num_read'] = sizeof($array_data_read);
                $import_result_show = true;
                try {
                    $db->beginTransaction();
                    foreach ($array_data_read as $row) {
                        // Kiểm tra trùng lặp
                        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_databank WHERE idnumber=" . $db->quote($row['idnumber']);
                        $result = $db->query($sql);
                        if (!$result->rowCount()) {
                            $db->query("INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_databank (name, phone, email, idnumber, birthday, sex, job, address, addtime) VALUES (" . $db->quote($row['name']) . "," . $db->quote($row['phone']) . "," . $db->quote($row['email']) . ", " . $db->quote($row['idnumber']) . ", " . $db->quote($row['birthday']) . "," . $db->quote($row['sex']) . ", " . $db->quote($row['job']) . ", " . $db->quote($row['address']) . ", " . NV_CURRENTTIME . ")");
                            if ($db->lastInsertId()) {
                                $import_result['num_install']++;
                            }
                        } elseif (!empty($post['existstype'])) {
                            $_row = $result->fetch();
                            $exc = $db->exec("UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_databank SET name=" . $db->quote($row['name']) . ",phone=" . $db->quote($row['phone']) . ",email=" . $db->quote($row['email']) . ",idnumber=" . $db->quote($row['idnumber']) . ",birthday=" . $db->quote($row['birthday']) . ",sex=" . $db->quote($row['sex']) . ",job=" . $db->quote($row['job']) . ",address=" . $db->quote($row['address']) . " WHERE id = " . $_row['id']);
                            if ($exc) {
                                $import_result['num_update']++;
                            }
                        }
                    }
                    $db->commit();
                } catch (PDOException $e) {
                    $db->rollBack();
                    trigger_error($e);
                    die($nv_Lang->getModule('error_request'));
                }
                unlink($upload_info['name']);
            }
        }elseif ($post['datatype']==3){
            //Mobiphone record
            $array_data_read = array();
            $array_field = array();
            $array_field["A"] = 'phieu_ghi';
            $array_field["B"] = 'sdt';
            $array_field['C'] = 'chuyen_vien';
            $array_field['D'] = 'loai_cuoc_goi';
            $array_field['E'] = 'nhanh';
            $array_field['F'] = 'thoi_gian_bat_dau';
            $array_field['G'] = 'thoi_gian_ket_thuc';
            $array_field['H'] = 'thoi_gian_cho';
            $array_field['I'] = 'thoi_gian_hold';
            $array_field['J'] = 'thoi_gian_dam_thoai';
            $array_field['K'] = 'trang_thai_cuoc_goi';
            $array_field['L'] = 'trang_thai_ket_thuc';
            $array_field['M'] = 'khao_sat';
            $array_field['N'] = 'kq_khao_sat';
            $array_field['O'] = 'link';

            $highestRow = $objWorksheet->getHighestRow();
            $stt = 0;
            for ($row = 2; $row <= $highestRow; ++$row) {
                $arrar_tmp = array();
                foreach ($array_field as $col => $field) {
                    $cell = $objWorksheet->getCell('' . $col . $row . '');
                    $value = trim($cell->getValue());
                    if($field=="thoi_gian_bat_dau" || $field=="thoi_gian_ket_thuc"){
                        $date = substr($value, 0, 10);
                        $time = substr($value, -8);

                        if (preg_match('/^([0-9]{4})\-([0-9]{1,2})\-([0-9]{1,2})$/', $date, $m)) {
                            if (preg_match('/([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2})$/', trim($time), $t)) {
                                $value = mktime($t[1], $t[2], $t[3], $m[2], $m[3], $m[1]);
                            } else {
                                $value = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
                            }
                        } else {
                            $value = 0;
                        }
                    }
                    if($field == "loai_cuoc_goi"){
                       $value = $value=="Gọi vào" ? 1 : 0;
                    }elseif ($field == "trang_thai_cuoc_goi"){
                        $value = $value=="Cuộc gọi gặp" ? 1 : 0;
                    }elseif ($field=="trang_thai_ket_thuc"){
                        $value = $value=="Khách hàng" ? 1 : 0;
                    }
                    $arrar_tmp[$field] = $value;
                }
                if (empty($arrar_tmp['phieu_ghi'])) {
                    break;
                } else {
                    $stt++;
                    $array_data_read[$stt] = $arrar_tmp;
                }
            }

            if (empty($error)) {
                $import_result['num_read'] = sizeof($array_data_read);
                $import_result_show = true;
                try {
                    $db->beginTransaction();
                    foreach ($array_data_read as $row) {
                        // Kiểm tra trùng lặp
                        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_mobiphone WHERE phieu_ghi=" . $db->quote($row['phieu_ghi']);
                        $result = $db->query($sql);
                        if (!$result->rowCount()) {
                            $db->query("INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_mobiphone (phieu_ghi, sdt, chuyen_vien, loai_cuoc_goi, nhanh, thoi_gian_bat_dau, thoi_gian_ket_thuc, thoi_gian_cho, thoi_gian_hold, thoi_gian_dam_thoai, trang_thai_cuoc_goi, trang_thai_ket_thuc, khao_sat, kq_khao_sat, link)
                                VALUES (" . $db->quote($row['phieu_ghi']) . "," . $db->quote($row['sdt']) . "," . $db->quote($row['chuyen_vien']) . ", " . $db->quote($row['loai_cuoc_goi']) . ", " . $db->quote($row['nhanh']) . "," . $db->quote($row['thoi_gian_bat_dau']) . ", " . $db->quote($row['thoi_gian_ket_thuc']) . ", " . $db->quote($row['thoi_gian_cho']) . ", " . $db->quote($row['thoi_gian_hold']). ", "
                                . $db->quote($row['thoi_gian_dam_thoai']) . ", " . $db->quote($row['trang_thai_cuoc_goi']) . ", " . $db->quote($row['trang_thai_ket_thuc']) . ", " . $db->quote($row['khao_sat']) . ", " . $db->quote($row['kq_khao_sat']) . ", " . $db->quote($row['link']) . ")");
                            if ($db->lastInsertId()) {
                                $import_result['num_install']++;
                            }
                        } elseif (!empty($post['existstype'])) {
                            $_row = $result->fetch();
                            $exc = $db->exec("UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_mobiphone SET sdt=" . $db->quote($row['sdt']) . ",chuyen_vien=" . $db->quote($row['chuyen_vien']) . ",loai_cuoc_goi=" . $db->quote($row['loai_cuoc_goi']) . ",nhanh=" . $db->quote($row['nhanh']) . ",thoi_gian_bat_dau=" . $db->quote($row['thoi_gian_bat_dau']) . ",thoi_gian_ket_thuc=" . $db->quote($row['thoi_gian_ket_thuc']) . ",thoi_gian_cho=" . $db->quote($row['thoi_gian_cho'])
                                . ",thoi_gian_hold=" . $db->quote($row['thoi_gian_hold']) . ",thoi_gian_dam_thoai=" . $db->quote($row['thoi_gian_dam_thoai']) . ",trang_thai_cuoc_goi=" . $db->quote($row['trang_thai_cuoc_goi']) . ",trang_thai_ket_thuc=" . $db->quote($row['trang_thai_ket_thuc']) . ",khao_sat=" . $db->quote($row['khao_sat']) . ",kq_khao_sat=" . $db->quote($row['kq_khao_sat']) . ",link=" . $db->quote($row['link']) . " WHERE phieu_ghi = " . $_row['phieu_ghi']);
                            if ($exc) {
                                $import_result['num_update']++;
                            }
                        }
                    }
                    $db->commit();
                } catch (PDOException $e) {
                    $db->rollBack();
                    trigger_error($e);
                    die($nv_Lang->getModule('error_request'));
                }
                unlink($upload_info['name']);
            }
        }

        $nv_Cache->delMod($module_name);
    }
} else {
    $post['datatype'] = 0;
    $post['existstype'] = 0;
}

$xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $global_config['module_theme'] . "/modules/" . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('DATA', $post);
$xtpl->assign('DOWNLOAD_TEMPLATE', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "&amp;download=1");
$xtpl->assign('DOWNLOAD_TEMPLATE2', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "&amp;download=2");

if (!empty($error)) {
    $xtpl->assign('ERROR', $error);
    $xtpl->parse('main.error');
}

if ($import_result_show) {
    $xtpl->assign('NUM_READ', number_format($import_result['num_read'], 0, ',', '.'));
    $xtpl->assign('NUM_INSTALL', number_format($import_result['num_install'], 0, ',', '.'));
    $xtpl->assign('NUM_UPDATE', number_format($import_result['num_update'], 0, ',', '.'));
    $xtpl->parse('main.result');
}
for ($i = 0; $i <= 3; $i++) {
    $datatype = array(
        'key' => $i,
        'title' => $nv_Lang->getModule('import_sel_datatype' . $i),
        'selected' => $i == $post['datatype'] ? ' selected="selected"' : ''
    );
    //print_r($datatype);die('ok');
    $xtpl->assign('DATATYPE', $datatype);
    $xtpl->parse('main.datatype');
}
for ($i = 0; $i <= 1; $i++) {
    $existstype = array(
        'key' => $i,
        'title' => $nv_Lang->getModule('import_sel_existstype' . $i),
        'selected' => $i == $post['existstype'] ? ' selected="selected"' : ''
    );

    $xtpl->assign('EXISTSTYPE', $existstype);
    $xtpl->parse('main.existstype');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include (NV_ROOTDIR . "/includes/header.php");
echo nv_admin_theme($contents);
include (NV_ROOTDIR . "/includes/footer.php");
