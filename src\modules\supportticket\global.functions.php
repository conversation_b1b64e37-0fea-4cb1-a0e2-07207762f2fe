<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2023 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 27 Feb 2023 09:02:35 GMT
 */

use NukeViet\InForm\InForm;
use NukeViet\Dauthau\LangMulti;

if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

require NV_ROOTDIR . '/modules/' . $module_file . '/enum.php';

if (!defined('NV_MIME_INI_FILE')) {
    define('NV_MIME_INI_FILE', NV_ROOTDIR . '/includes/ini/mime.ini');
}

//Giới hạn file tải lên
if (!defined('NV_ATTACH_LIMITED')) {
    define('NV_ATTACH_LIMITED', 5);
}

if (!defined('SITE_ID_DOMAIN')) {
    define('SITE_ID_DOMAIN', 'https://id.dauthau.net');
}

define('TB_TICKET_ROW', $db_config['prefix'] . '_' . $module_data . '_row');
define('TB_TICKET_LOG', $db_config['prefix'] . '_' . $module_data . '_comments');
define('TB_TICKET_POINT_LOG', $db_config['prefix'] . '_' . $module_data . '_point_logs');
define('TB_TICKET_ALLLOG', $db_config['prefix'] . '_' . $module_data . '_alllog');
define('TB_TICKET_LABEL', $db_config['prefix'] . '_' . $module_data . '_label');
define('TB_TICKET_CAT', $db_config['prefix'] . '_' . $module_data . '_cat');
define('TB_TICKET_CATADMIN', $db_config['prefix'] . '_' . $module_data . '_cat_admins');
define('TB_TICKET_CHAT_PRIVATE', $db_config['prefix'] . '_' . $module_data . '_chat_private');

global $nv_Cache, $array_ticket_cats, $array_active_cats, $array_ticket_labels, $array_active_labels, $array_ticket_status, $array_vip_status, $array_admin_users, $array_admin_listall, $groups_users;
$nv_Lang->loadModule($module_name, false, true);

//Tất cả danh mục
$_sql = 'SELECT * FROM ' . TB_TICKET_CAT . ' ORDER BY weight ASC';
$array_ticket_cats = $nv_Cache->db($_sql, 'cat_id', $module_name);
//Danh mục active
$_sql = 'SELECT * FROM ' . TB_TICKET_CAT . ' WHERE active=1 ORDER BY weight ASC';
$array_active_cats = $nv_Cache->db($_sql, 'cat_id', $module_name);
//Danh mục trả phí active
$array_paid_cats = [];
foreach ($array_active_cats as $cat) {
    $cat['is_point'] == 1 && $array_paid_cats[] = $cat;
}

//Tất cả nhãn
$_sql = 'SELECT * FROM ' . TB_TICKET_LABEL . ' ORDER BY weight ASC';
$array_ticket_labels = $nv_Cache->db($_sql, 'label_id', $module_name);
//Nhãn active
$_sql = 'SELECT * FROM ' . TB_TICKET_LABEL . ' WHERE active=1 ORDER BY weight ASC';
$array_active_labels = $nv_Cache->db($_sql, 'label_id', $module_name);

//Tất cả user
// $_sql = 'SELECT userid, first_name, last_name, username, email FROM ' . NV_USERS_GLOBALTABLE;
// $array_users = $nv_Cache->db($_sql, 'userid', 'users');

//Trạng thái ticket
$array_ticket_status = [
    TicketStatus::Open->value,
    TicketStatus::Done->value,
    TicketStatus::Process->value,
    TicketStatus::Close->value,
    TicketStatus::RefuseToAddPoint->value,
    TicketStatus::Draft->value
];

// Trạng thái ticket ngoài site
$array_ticket_status_sites = [
    TicketStatusClient::Open->value,
    TicketStatusClient::Done->value,
    TicketStatusClient::Process->value,
    TicketStatusClient::Close->value,
    TicketStatusClient::Unread->value
];

//Loại bỏ trạng thái
function unset_status($array)
{
    global $array_ticket_status;

    foreach ($array as $item) {
        unset($array_ticket_status[$item]);
    }
}

//Trạng thái gói VIP
$array_vip_status = [
    1 => $nv_Lang->getModule('vip_status1'),
    2 => $nv_Lang->getModule('vip_status0')
];

//Giới tính
$global_array_genders = [
    'N' => [
        'key' => 'N',
        'title' => $nv_Lang->getModule('na'),
        'selected' => ''
    ],
    'M' => [
        'key' => 'M',
        'title' => $nv_Lang->getModule('male'),
        'selected' => ''
    ],
    'F' => [
        'key' => 'F',
        'title' => $nv_Lang->getModule('female'),
        'selected' => ''
    ]
];
$nv_Lang->changeLang(NV_LANG_DATA);

//Tất cả quản trị đang hoạt động
$_sql = 'SELECT tb1.userid, tb1.first_name, tb1.last_name, tb1.username, tb1.email, tb1.gender FROM ' . NV_USERS_GLOBALTABLE . ' tb1 INNER JOIN ' . $db_config['prefix'] . '_authors tb2 ON tb1.userid = tb2.admin_id WHERE tb1.userid IN (SELECT `admin_id` FROM ' . NV_AUTHORS_GLOBALTABLE . ' ORDER BY lev ASC) AND tb1.active = 1 AND tb2.is_suspend = 0';
$array_admin_users = $nv_Cache->db($_sql, 'userid', 'users');

//Tất cả quản trị trong danh sách
$_sql = 'SELECT tb1.userid, tb1.first_name, tb1.last_name, tb1.username, tb1.email, tb1.gender FROM ' . NV_USERS_GLOBALTABLE . ' tb1 INNER JOIN ' . $db_config['prefix'] . '_authors tb2 ON tb1.userid = tb2.admin_id WHERE tb1.userid IN (SELECT `admin_id` FROM ' . NV_AUTHORS_GLOBALTABLE . ' ORDER BY lev ASC)';
$array_admin_listall = $nv_Cache->db($_sql, 'userid', 'users');

//Quản trị trong nhóm crm
$_sql = 'SELECT * FROM nv4_vi_crmbidding_groups_users';
$groups_users = $nv_Cache->db($_sql, '', 'crmbidding');

// Lấy danh sách thành viên của team nếu là leader
function get_member_by_leader($userid)
{
    global $groups_users;

    //Lấy ra những nhóm mà user đang làm leader
    $leader_groups = [];
    foreach ($groups_users as $user) {
        if ($user['userid'] == $userid && $user['is_leader'] == 1) {
            $leader_groups[] = $user['group_id'];
        }
    }

    //Lấy tất cả thành viên của các nhóm mà user làm leader
    if (sizeof($leader_groups) > 0) {
        $member_list = [];
        foreach ($leader_groups as $group) {
            foreach ($groups_users as $user) {
                if ($user['group_id'] == $group && $userid != $user['userid']) {
                    $member_list[] = $user['userid'];
                }
            }
        }
        return array_unique($member_list);
    }

    return [];
}

//Tải nhiều ảnh
function multi_upload($attach_files, $old_file = null, $existing_files = [])
{
    global $global_config, $module_data, $nv_Lang;

    $file_uploaded = $file_uploaded_dir = [];
    if (!empty($attach_files)) {
        $upload = new NukeViet\Files\Upload(['images', 'documents', 'adobe'], $global_config['forbid_extensions'], $global_config['forbid_mimes'], NV_UPLOAD_MAX_FILESIZE, NV_MAX_WIDTH, NV_MAX_HEIGHT);
        $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
        foreach ($attach_files as $userfile) {
            $upload_info = $upload->save_file($userfile, NV_UPLOADS_REAL_DIR . '/' . $module_data, false);
            if (is_file($userfile['tmp_name'])) {
                @unlink($userfile['tmp_name']);
            }
            if (empty($upload_info['error'])) {
                $file_uploaded[] = $upload_info['basename'];
                $file_uploaded_dir[] = $upload_info['name'];
            } else {
                if (sizeof($file_uploaded_dir) > 0) {
                    multi_upload_unlink($file_uploaded_dir);
                }
                return [
                    'status'    => 'error',
                    'message'   => $upload_info['error']
                ];
            }
        }
    }

    // Xóa file cũ
    if (!empty($old_file)) {
        $old_files = json_decode($old_file, true);
        $files_to_delete = array_diff($old_files, $existing_files);
        foreach ($files_to_delete as $file_name) {
            $file_path = NV_UPLOADS_REAL_DIR . '/' . $module_data . '/' . $file_name;
            if (file_exists($file_path)) {
                @unlink($file_path);
            }
        }
    }

    if (!empty($attach_files)) {
        $file_data = array_merge($existing_files, $file_uploaded);
    } else {
        $file_data = $existing_files;
    }

    return [
        'status'    => 'success',
        'data'      => $file_data,
        'dir'       => $file_uploaded_dir
    ];
}

//Xóa ảnh đã tải lên
function multi_upload_unlink($file_uploaded_dir)
{
    foreach ($file_uploaded_dir as $file_dir) {
        @unlink($file_dir);
    }
}

//Xác định loại file
function file_type($ext)
{
    return in_array($ext, ['gif', 'jpg', 'jpeg', 'png', 'webp'], true) ? 'image' : (in_array($ext, ['doc', 'docx'], true) ? 'doc' : ($ext == 'pdf' ? 'pdf' : 'file'));
}

//Xác định đuôi mở rộng
function getextension($filename)
{
    if (strpos($filename, '.') === false) {
        return '';
    }
    $filename = basename(strtolower($filename));
    $filename = explode('.', $filename);

    return array_pop($filename);
}

//Lấy thông tin của user bất kỳ
function get_user_info($userid)
{
    global $db, $array_admin_listall, $array_admin_users;

    //Trả về admin active
    if (isset($array_admin_users[$userid])) {
        $user_info = $array_admin_users[$userid];
        $user_info['fullname'] = nv_show_name_user($user_info['first_name'], $user_info['last_name'], $user_info['username']);
        return $user_info;
    }

    //Trả về admin trong author
    if (isset($array_admin_listall[$userid])) {
        $user_info = $array_admin_listall[$userid];
        $user_info['fullname'] = nv_show_name_user($user_info['first_name'], $user_info['last_name'], $user_info['username']);
        return $user_info;
    }

    //Không thuộc author thì truy vấn user
    $_sql = 'SELECT userid, first_name, last_name, username, email FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $userid;
    $user = $db->query($_sql)->fetch();
    if (!empty($user)) {
        $user_info = $user;
        $user_info['fullname'] = nv_show_name_user($user_info['first_name'], $user_info['last_name'], $user_info['username']);
        return $user_info;
    }

    return [];
}

// Lấy thông tin user chịu trách nhiệm nhận phản hồi của bộ phận
function get_user_in_cat($cat_id)
{
    global $db, $db_config, $module_data;
    $users = [];
    $result = $db->query('SELECT userid FROM ' . $db_config['prefix'] . '_' . $module_data . '_cat_admins WHERE cat_id=' . $cat_id . ' AND is_send_email=1');

    while ($row = $result->fetch()) {
        $users[] = $row['userid'];
    }

    return $users;
}

// Lấy thông tin nhân sự thuộc phòng ban
function get_user_of_cat($userid)
{
    global $db, $array_ticket_cats, $db_config, $module_data, $nv_Lang;
    $cat_title = '!'; // Nếu trường hợp nhân sự chưa được thêm vào phòng ban thì chỉ hiển thị tên
    $_sql = 'SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_cat_admins WHERE userid=' . $userid;
    $result = $db->query($_sql)->fetch();
    if (!empty($result) && isset($array_ticket_cats[$result['cat_id']])) {
        $cat_title = sprintf($nv_Lang->getModule('user_of_cat'), $array_ticket_cats[$result['cat_id']]['title_vi']);
    }
    return $cat_title;
}

function get_user_extend($userid)
{
    global $db, $db_config, $module_name;

    $_sql = 'SELECT * FROM ' . $db_config['prefix'] . '_' . $module_name . '_user_extend WHERE userid=' . $userid;
    $info = $db->query($_sql)->fetch();

    if (!empty($info)) {
        return $info;
    }

    return [];
}

//Hiển thị chức vụ trong cmt
function display_position($cmt)
{
    global $nv_Lang;

    if ($cmt['area'] == 0) {
        $position = $nv_Lang->getModule('comment_position_customer');
    } elseif ($cmt['area'] == 1) {
        if ($cmt['display_userid'] > 0 && $cmt['display_userid'] != $cmt['reply_userid']) {
            $position = '<span class="text-danger">' . $nv_Lang->getModule('comment_position_expert') . '</span>';
        } elseif (!empty($cmt['user_extend']) && $cmt['user_extend']['position_state'] == 'on') {
            $position = '<span class="text-primary">' . $cmt['user_extend']['position_' . NV_LANG_DATA] . '</span>';
        } else {
            $position = '<span class="text-primary">' . $nv_Lang->getModule('comment_position_staff') . '</span>';
        }
    }
    return $position;
}

//Hiển thị tên trong cmt
function display_fullname($cmt, $user_info)
{
    if (empty($user_info)) return '';

    if (!empty($cmt['user_extend']) && $cmt['user_extend']['prefix_state'] == 'on') {
        $fullname = $cmt['user_extend']['prefix_' . NV_LANG_DATA] . '. ' . $user_info['fullname'];
    } else {
        $fullname = $user_info['fullname'];
    }

    return $fullname;
}

//Lấy ra các gói vip của user
function get_user_vips($userid)
{
    global $nv_Lang;
    $arr_where['AND'][] = [
        '=' => [
            'user_id' => $userid
        ]
    ];

    $params['where'] = $arr_where;

    $api = new NukeViet\Api\DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
    $api->setModule('bidding')
        ->setLang(NV_LANG_DATA)
        ->setAction('ListBiddingCustoms')
        ->setData($params);
    $result_custom = $api->execute();
    $err = $api->getError();

    $select_vip = [];
    $select_vip[0] = [
        'id'    => 0,
        'title' => $nv_Lang->getModule('select_vip')
    ];

    if (empty($err) and $result_custom['status'] == 'success' and $result_custom['code'] == '0000') {
        foreach ($result_custom['data'] as $vip) {
            $select_vip[$vip['vip']] = [
                'id'    => $vip['vip'],
                'title' => $vip['vip_name'],
                'caregiver' => $vip['admin_id'],
                'user_id' => $vip['user_id'],
            ];
        }
    }

    return $select_vip;
}

//Lấy ra các gói đơn hàng của user
function get_user_orders($userid)
{
    global $nv_Lang;
    $arr_where['AND'][] = [
        '=' => [
            'userid' => $userid
        ]
    ];

    $params['where'] = $arr_where;

    $api = new NukeViet\Api\DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
    $api->setModule('bidding')
        ->setLang(NV_LANG_DATA)
        ->setAction('ListBiddingOrder')
        ->setData($params);
    $result_order = $api->execute();
    $err = $api->getError();
    $select_order = [];
    $select_order[] = [
        'id'    => 0,
        'title' => $nv_Lang->getModule('select_order')
    ];

    if (empty($err) and $result_order['status'] == 'success' and $result_order['code'] == '0000') {
        $params = [
            'arr_orderid' => array_keys($result_order['data']),
            'get_orders' => 1
        ];
        $api->setModule('bidding')
            ->setLang(NV_LANG_DATA)
            ->setAction('GetBiddingOrderCustomsLog')
            ->setData($params);
        $result_order_log = $api->execute();
        $err = $api->getError();

        if (empty($err) and $result_order_log['status'] == 'success' and $result_order['code'] == '0000') {
            foreach ($result_order_log['data'] as $order) {
                $select_order[$order['id']] = [
                    'id'    => $order['id'],
                    'title' => $order['title'],
                    'add_time' => $order['add_time'],
                    'caregiver' => $order['caregiver_id']
                ];
            }
        }
    }

    return $select_order;
}

//Lấy ra người chăm sóc lead thành viên của user
function get_lead_cagiver(int $userid)
{
    global $db;
    include NV_ROOTDIR . '/modules/crmbidding/functions/hotlead.functions.php';

    //Lấy người chăm sóc gói vip có end_time lớn nhất
    $arr_where['AND'][] = [
        '=' => [
            'user_id' => $userid
        ]
    ];
    $arr_where['AND'][] = [
        '>=' => [
            'end_time' => NV_CURRENTTIME
        ]
    ];

    $params['where'] = $arr_where;

    $api = new NukeViet\Api\DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
    $api->setModule('bidding')
        ->setLang(NV_LANG_DATA)
        ->setAction('ListBiddingCustoms')
        ->setData($params);
    $result_custom = $api->execute();
    $err = $api->getError();

    if (empty($err) and $result_custom['status'] == 'success' and $result_custom['code'] == '0000') {
        foreach ($result_custom['data'] as $vip) {
            return $vip['admin_id'];
        }
    }

    //Lấy người chăm sóc opportunity có tg tạo gần nhất, ưu tiên opportunity nóng
    $opportunities_sql = 'SELECT * FROM nv4_vi_crmbidding_opportunities WHERE user_id = ' . $userid . ' AND status != 2 ORDER BY updatetime DESC LIMIT 10';
    $opportunities = $db->query($opportunities_sql)->fetchAll();
    if (!empty($opportunities)) {
        $count = 0;
        $caregiver_id = 0;
        foreach ($opportunities as $opportunity) {
            $check_current_oppotunities = check_current_oppotunities($opportunity);
            $hotlead = $check_current_oppotunities['hotting'];
            if ($hotlead) {
                return $opportunity['caregiver_id'];
            }

            if ($count == 0) {
                $caregiver_id = $opportunity['caregiver_id'];
            }
            ++$count;
        }
        return $caregiver_id;
    }

    //Lấy người chăm sóc lead có tg tạo gần nhất, ưu tiên lead nóng
    $leads_sql = 'SELECT caregiver_id FROM nv4_vi_crmbidding_leads WHERE user_id = ' . $userid . ' ORDER BY updatetime DESC LIMIT 10';
    $leads = $db->query($leads_sql)->fetchAll();
    if (!empty($leads)) {
        $count = 0;
        $caregiver_id = 0;
        foreach ($leads as $lead) {
            $check_current_lead = check_current_lead($lead);
            $hotlead = $check_current_lead['hotting'];
            if ($hotlead) {
                return $lead['caregiver_id'];
            }

            if ($count == 0) {
                $caregiver_id = $lead['caregiver_id'];
            }
            ++$count;
        }
        return $caregiver_id;
    }

    return 0;
}

//Lấy lead hoặc cơ hội của thành viên (nếu có)
function get_lead_info(int $userid)
{
    global $db;
    include NV_ROOTDIR . '/modules/crmbidding/functions/hotlead.functions.php';

    //Lấy opportunity có tg tạo gần nhất, ưu tiên opportunity nóng
    $opportunities_sql = 'SELECT * FROM nv4_vi_crmbidding_opportunities WHERE user_id = ' . $userid . ' AND status != 2 ORDER BY updatetime DESC LIMIT 10';
    $opportunities = $db->query($opportunities_sql)->fetchAll();
    if (!empty($opportunities)) {
        $count = 0;
        foreach ($opportunities as $opportunity) {
            $opportunity['type'] = 'opportunity';
            $check_current_oppotunities = check_current_oppotunities($opportunity);
            $hotlead = $check_current_oppotunities['hotting'];
            if ($hotlead) {
                return $opportunity;
            }

            if ($count == 0) {
                return $opportunity;
            }
            ++$count;
        }
    }

    //Lấy lead có tg tạo gần nhất, ưu tiên lead nóng
    $leads_sql = 'SELECT * FROM nv4_vi_crmbidding_leads WHERE user_id = ' . $userid . ' ORDER BY updatetime DESC LIMIT 10';
    $leads = $db->query($leads_sql)->fetchAll();
    if (!empty($leads)) {
        $count = 0;
        foreach ($leads as $lead) {
            $lead['type'] = 'lead';
            $check_current_lead = check_current_lead($lead);
            $hotlead = $check_current_lead['hotting'];
            if ($hotlead) {
                return $lead;
            }

            if ($count == 0) {
                return $lead;
            }
            ++$count;
        }
    }

    return [];
}

//Lấy thông tin nhân viên
function get_staff_info($userid)
{
    $reply_user = get_user_info($userid);
    $reply_user_fullname = empty($reply_user) ? '' : nv_show_name_user($reply_user['first_name'], $reply_user['last_name'], $reply_user['username']);
    $reply_user_extend = get_user_extend($userid);
    $staff_display = [];
    // Thứ tự hiển thị: {prefix_state/Danh xưng} {full name} - {position_state/Chức vụ}
    if (!empty($reply_user_extend)) {
        if ($reply_user_extend['prefix_state'] == 'on') {
            $staff_display[] = $reply_user_extend['prefix_' . NV_LANG_DATA] . '.';
        } else { // Trường hợp chưa cấu hình prefix_state trong admin thì sử dụng giới tính
            $staff_display[] = $reply_user['gender'] == 'F' ? 'Mrs.' : 'Mr.';
        }
        $staff_display[] = $reply_user_fullname;
        if ($reply_user_extend['position_state'] == 'on') {
            $staff_display[] = '-';
            $staff_display[] = $reply_user_extend['position_' . NV_LANG_DATA];
        }
    } else {
        $staff_display[] = $reply_user['gender'] == 'F' ? 'Mrs.' : 'Mr.' . $reply_user_fullname;
    }

    return $staff_display;
}

//Lưu mail vào hàng đợi
if (!function_exists('nv_pending_mail')) {
    function nv_pending_mail($subject, $messages, $main_mail, $cc_mail = '')
    {
        global $db;

        try {
            $stmt = $db->prepare("INSERT INTO nv4_vi_bidding_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, source_email, `messageid`, `reject`, `bounce`, `complaint`, `click`, `open`, `failure`) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, :source_email, '', '', '', '', '', '', '')");
            $stmt->bindValue(':userid', 0, PDO::PARAM_INT);
            $stmt->bindParam(':main_mail', $main_mail, PDO::PARAM_STR);
            $stmt->bindValue(':cc_mail', $cc_mail, PDO::PARAM_STR);
            $stmt->bindValue(':number_phone', 0, PDO::PARAM_STR);
            $stmt->bindParam(':title', $subject, PDO::PARAM_STR);
            $stmt->bindParam(':content', $messages, PDO::PARAM_STR, strlen($messages));
            $stmt->bindValue(':type', 0, PDO::PARAM_INT);
            $stmt->bindValue(':vip', 0, PDO::PARAM_INT);
            $stmt->bindValue(':addtime', NV_CURRENTTIME, PDO::PARAM_INT);
            $stmt->bindValue(':send_time', 0, PDO::PARAM_INT);
            $stmt->bindValue(':status', 0, PDO::PARAM_INT);
            $stmt->bindValue(':source_email', 1, PDO::PARAM_INT); //Nếu email được gữi từ site id->0 còn của site support->1
            $stmt->execute();
        } catch (PDOException $e) {
            trigger_error($e);
        }
    }
}
//Gửi thông báo ticket mới đến khách hàng
function ticket_notification_to_user($data, $is_admin = false, $mail_template = 'default')
{
    global $nv_Lang, $module_name, $module_file, $module_info;

    $user_info = get_user_info($data['customer_id']);
    $data['link'] = URL_SUPPORT . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . ($data['is_paid'] == 1 ? $module_info['alias']['detail_paid'] : $module_info['alias']['detail']) . '&amp;id=' . $data['id'];
    $data['title'] = html_entity_decode(nv_htmlspecialchars($data['title']));

    //Gửi thông báo
    if ($is_admin) {
        $arrMess = [
            'vi' => sprintf(LangMulti::get('vi', 'notice_to_customer_admin'), $data['title']),
            'en' => sprintf(LangMulti::get('en', 'notice_to_customer_admin'), $data['title']),
        ];
    } else {
        $arrMess = [
            'vi' => sprintf(LangMulti::get('vi', 'notice_to_customer'), $data['title']),
            'en' => sprintf(LangMulti::get('en', 'notice_to_customer'), $data['title']),
        ];
    }
    $link_detail = $data['is_paid'] == 1 ? $module_info['alias']['detail_paid'] : $module_info['alias']['detail'];
    InForm::insertInfrom($data['customer_id'], $arrMess, 'supportticket/' . $link_detail . '/?id=' . $data['id']);

    //Gửi mail
    if (!empty($user_info)) {
        $xtpl = new XTemplate('mail_create_to_customer.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/' . NV_LANG_DATA);
        $xtpl->assign('TICKET', $data);
        $xtpl->assign('CUSTOMER', $user_info);

        if ($is_admin) {
            if ($data['send_notify'] == 1) {
                $xtpl->parse('main.admin.custom');
            } else {
                if (!empty($data['assignee_to'])) {
                    $assignee_to = explode(',', $data['assignee_to']);
                    $assignee_display = [];
                    foreach ($assignee_to as $caregiver_id) {
                        $assignee_display[] = implode(' ', get_staff_info($caregiver_id));
                    }
                    $staff_display = implode(', ', $assignee_display);
                    $xtpl->assign('STAFF', $staff_display);
                    $xtpl->assign('USER_ASSIGN', $data['point_price'] > 0 ? $nv_Lang->getModule('user_initiator') : $nv_Lang->getModule('user_handler'));
                    $xtpl->parse('main.admin.default.staff_display');
                }
                if ($data['point_price'] > 0) {
                    $xtpl->parse('main.admin.default.paid');
                } else {
                    $xtpl->parse('main.admin.default.free');
                }
                $xtpl->parse('main.admin.default');
            }
            $xtpl->parse('main.admin');
        } else {
            if ($data['point_price'] > 0) {
                $xtpl->parse('main.site.paid');
            } else {
                $xtpl->parse('main.site.free');
            }
            $xtpl->parse('main.site');
        }
        $xtpl->parse('main');

        $messages = $xtpl->text('main');
        $subject = ($data['send_notify'] == 1) ? $nv_Lang->getModule('mail_to_customer_with_notify_subject', truncate_title($data['title'])) : $nv_Lang->getModule('mail_subject', truncate_title($data['title']));
        nv_pending_mail($subject, $messages, $user_info['email']);
    }
}

//Gửi thông báo ticket mới đến quản trị
function ticket_notification_to_admin($data, $mail_template = 'default')
{
    global $module_name, $module_config, $nv_Lang, $module_file, $array_active_cats;

    $user_info = get_user_info($data['customer_id']);

    $data['admin_link'] = SITE_ID_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . ($data['is_paid'] == 1 ? 'ticket_detail_paid' : 'ticket_detail') . '&amp;id=' . $data['id'];
    $data['title'] = html_entity_decode(nv_htmlspecialchars($data['title']));
    $data['time_limit'] = $data['add_time'] + 86400;
    $data['time_limit'] = nv_date('H:i:s d/m/Y', $data['time_limit']);

    //Gửi thông báo
    $notify_obid = $data['id'] ?? 0;
    $caregiver_id = 0;
    nv_insert_notification($module_name, 'to_admin', array(
        'name' => $user_info['fullname'],
        'link' => $data['admin_link'],
        'ticket_id' => $data['id']
    ), $notify_obid, $caregiver_id, 0, 1, 1);

    $subject = $nv_Lang->getModule('mail_to_expert_subject', truncate_title($data['title']), $user_info['fullname']);
    $xtpl = new XTemplate('mail_create_to_admin.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/' . NV_LANG_DATA);
    $xtpl->assign('TICKET', $data);
    $xtpl->assign('CUSTOMER', $user_info);
    $xtpl->assign('CAT_TITLE', $array_active_cats[$data['cat_id']]['title_' . NV_LANG_DATA]);
    $xtpl->parse('main');
    $messages = $xtpl->text('main');

    // Gữi email tới người trực tiếp nhận email của bộ phận
    $response_recipients = get_user_in_cat($data['cat_id']);
    if (!empty($response_recipients)) {
        $username_array = [];
        foreach ($response_recipients as $caregiver_id) {
            $caregiver_info = get_user_info($caregiver_id);
            if (!empty($caregiver_info)) {
                $username_array[] = $caregiver_info['username'];
                nv_pending_mail($subject, $messages, $caregiver_info['email']);
            }

            //Gửi thông báo
            nv_insert_notification($module_name, 'user_reply', array(
                'name' => $user_info['fullname'],
                'link' => $data['admin_link'],
                'ticket_id' => $data['id']
            ), $notify_obid, $caregiver_id, 0, 1, 1);
        }

        // Thông báo đến slack
        if (!empty($username_array)) {
            $caregiver_array = [];
            foreach ($username_array as $username) {
                $username = replace_slack_username($username);
                $caregiver_array[] = '<@' . str_replace(' ', '_', $username) . '>';
            }
            $caregiver_list = implode(', ', $caregiver_array) . " ";
            $message = $nv_Lang->getModule('slack_to_admin', $user_info['fullname'], $caregiver_list, $data['admin_link'], $data['time_limit']);
            $message = urlencode($message);
            notify_to_slack($message);
        }
    } else {
        nv_pending_mail($subject, $messages, $module_config[$module_name]['email_support']);
    }
}

//Gửi thông báo ticket mới đến người đang chăm sóc đơn hàng/gói vip
function ticket_notification_to_caregiver($data, $caregiver_id, $send_to_slack = false, $mail_template = 'default')
{
    global $module_name, $module_file, $nv_Lang;

    $data['admin_link'] = SITE_ID_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail&amp;id=' . $data['id'];
    $user_info = get_user_info($data['customer_id']);
    $data['title'] = html_entity_decode(nv_htmlspecialchars($data['title']));
    $data['time_limit'] = $data['add_time'] + 86400;
    $data['time_limit'] = nv_date('H:i:s d/m/Y', $data['time_limit']);

    //Gửi thông báo
    $notify_obid = $data['id'] ?? 0;
    nv_insert_notification($module_name, 'to_caregiver', array(
        'name' => $user_info['fullname'],
        'link' => $data['admin_link'],
        'ticket_id' => $data['id']
    ), $notify_obid, $caregiver_id, 0, 1, 1);

    //Gửi mail
    $caregiver_info = get_user_info($caregiver_id);
    if (!empty($caregiver_info)) {
        $cat_title = get_user_of_cat($caregiver_id);
        $xtpl = new XTemplate('mail_create_to_caregiver.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
        $xtpl->assign('TICKET', $data);
        $xtpl->assign('CUSTOMER', $user_info);
        $xtpl->assign('STAFF', implode(' ', get_staff_info($caregiver_id)));
        $xtpl->assign('CAT_TITLE', $cat_title);

        if (defined('NV_ADMIN')) {
            $xtpl->parse('main.admin');
        } else {
            $xtpl->parse('main.site');
        }
        $xtpl->parse('main');
        $subject = sprintf($nv_Lang->getModule('mail_subject'), truncate_title($data['title']));
        $messages = $xtpl->text('main');
        nv_pending_mail($subject, $messages, $caregiver_info['email']);

        // Thông báo đến slack
        if ($send_to_slack) {
            $username = replace_slack_username($caregiver_info['username']);
            $caregiver_username = '<@' . str_replace(' ', '_', $username) . '>';
            $message = $nv_Lang->getModule('slack_to_caregiver', $user_info['fullname'], $caregiver_username, $data['admin_link'], $data['time_limit']);
            $message = urlencode($message);
            notify_to_slack($message);
        }
    }
}

//Gửi thông báo assignee đến người chăm sóc
function ticket_notification_assignee_to_caregiver($data, $data_old, $mail_template = 'default')
{
    global $module_name, $nv_Lang, $module_file, $module_info;

    $data_old['admin_link'] = $data['admin_link'] = SITE_ID_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail&amp;id=' . $data['id'];
    $user_info = get_user_info($data['customer_id']);
    $user_fullname = empty($user_info) ? '' : nv_show_name_user($user_info['first_name'], $user_info['last_name'], $user_info['username']);

    $notify_obid = $data['id'] ?? 0;
    $data['title'] = html_entity_decode(nv_htmlspecialchars($data['title']));
    $data_old['title'] = html_entity_decode(nv_htmlspecialchars($data_old['title']));
    $assignee = !empty($data['assignee_to']) ? explode(',', $data['assignee_to']) : [];
    $assignee_old = !empty($data_old['assignee_to']) ? explode(',', $data_old['assignee_to']) : [];
    $title_data_old = truncate_title($data_old['title']);
    //Thông báo cho người vừa mới unassignee
    $assignee_minus = array_diff($assignee_old, $assignee);
    if (sizeof($assignee_minus) > 0) {
        $subject = sprintf($nv_Lang->getModule('mail_subject'), $title_data_old);
        foreach ($assignee_minus as $caregiver_id) {
            //Gửi mail
            $caregiver_info = get_user_info($caregiver_id);
            $cat_title = get_user_of_cat($caregiver_id);
            if (!empty($caregiver_info)) {
                $xtpl = new XTemplate('mail_unassignee_to_caregiver.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
                $xtpl->assign('TICKET', $data_old);
                $xtpl->assign('CUSTOMER', $user_info);
                $xtpl->assign('STAFF', implode(' ', get_staff_info($caregiver_id)));
                $xtpl->assign('CAT_TITLE', $cat_title);
                $xtpl->parse('main');

                $messages = $xtpl->text('main');
                nv_pending_mail($subject, $messages, $caregiver_info['email']);
            }

            //Gửi thông báo
            nv_insert_notification($module_name, 'unassignee', array(
                'name' => $user_fullname,
                'link' => $data['admin_link'],
                'ticket_id' => $data_old['id']
            ), $notify_obid, $caregiver_id, 0, 1, 1);
        }
    }

    //Thông báo cho người vừa mới được assignee
    $assignee_plus = array_diff($assignee, $assignee_old);
    $assignee_display = [];
    if (sizeof($assignee_plus) > 0) {
        $subject = sprintf($nv_Lang->getModule('mail_subject'), $title_data_old);
        foreach ($assignee_plus as $caregiver_id) {
            //Gửi mail cho người đc assinee
            $caregiver_info = get_user_info($caregiver_id);
            if (!empty($caregiver_info)) {
                $cat_title = get_user_of_cat($caregiver_id);
                $xtpl = new XTemplate('mail_assignee_to_caregiver.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
                $xtpl->assign('TICKET', $data_old);
                $xtpl->assign('CUSTOMER', $user_info);
                $xtpl->assign('STAFF', implode(' ', get_staff_info($caregiver_id)));
                $xtpl->assign('CAT_TITLE', $cat_title);
                $xtpl->parse('main');

                $messages = $xtpl->text('main');
                nv_pending_mail($subject, $messages, $caregiver_info['email']);
            }
            $assignee_display[] = implode(' ', get_staff_info($caregiver_id));
            //Gửi thông báo
            nv_insert_notification($module_name, 'assignee_to_caregiver', array(
                'name' => $user_fullname,
                'link' => $data['admin_link'],
                'ticket_id' => $data_old['id']
            ), $notify_obid, $caregiver_id, 0, 1, 1);
        }
    }

    //Thông báo cho khách hàng
    if (sizeof($assignee_plus) > 0) {
        $user_info = get_user_info($data['customer_id']);
        $data['link'] = NV_MY_DOMAIN . NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . ($data['is_paid'] == 1 ? $module_info['alias']['detail_paid'] : $module_info['alias']['detail']) . '&amp;id=' . $data['id'];
        $staff_display = implode(', ', $assignee_display);

        //Gửi thông báo
        $arrMess = [
            'vi' => sprintf(LangMulti::get('vi', 'notice_assignee_to_customer'), $staff_display),
            'en' => sprintf(LangMulti::get('en', 'notice_assignee_to_customer'), $staff_display),
        ];
        $res = InForm::insertInfrom($data['customer_id'], $arrMess, 'supportticket/detail/?id=' . $data['id']);

        //Gửi mail
        if (!empty($user_info)) {
            $xtpl = new XTemplate('mail_assignee_to_customer.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
            $xtpl->assign('TICKET', $data_old);
            $xtpl->assign('CUSTOMER', $user_info);
            $xtpl->assign('STAFF', $staff_display);
            $xtpl->parse('main');

            $subject = sprintf($nv_Lang->getModule('mail_subject'), $title_data_old);
            $messages = $xtpl->text('main');
            nv_pending_mail($subject, $messages, $user_info['email']);
        }
    }
}

//Gửi thông báo cmt đến người chăm sóc
function comment_notification_to_caregiver($data, $mail_template = 'default')
{
    global $module_name, $module_config, $nv_Lang, $module_file;

    $data['admin_link'] = SITE_ID_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail&amp;id=' . $data['id'];
    $user_info = get_user_info($data['customer_id']);

    $notify_obid = $data['id'] ?? 0;
    $data['title'] = html_entity_decode(nv_htmlspecialchars($data['title']));
    $subject = sprintf($nv_Lang->getModule('mail_subject'), truncate_title($data['title']));

    $assignee = !empty($data['assignee_to']) ? explode(',', $data['assignee_to']) : [];
    //Thông báo cho tất cả người chăm sóc
    if (sizeof($assignee) > 0) {
        foreach ($assignee as $caregiver_id) {
            $caregiver_info = get_user_info($caregiver_id);
            if (!empty($caregiver_info)) {
                $cat_title = get_user_of_cat($caregiver_id);
                $xtpl = new XTemplate('mail_reply_to_caregiver.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
                $xtpl->assign('TICKET', $data);
                $xtpl->assign('CUSTOMER', $user_info);
                $xtpl->assign('STAFF', implode(' ', get_staff_info($caregiver_id)));
                $xtpl->assign('CAT_TITLE', $cat_title);
                $xtpl->parse('main');
                $messages = $xtpl->text('main');
                nv_pending_mail($subject, $messages, $caregiver_info['email']);
            }

            //Gửi thông báo
            nv_insert_notification($module_name, 'user_reply', array(
                'name' => $user_info['fullname'],
                'link' => $data['admin_link'],
                'ticket_id' => $data['id']
            ), $notify_obid, $caregiver_id, 0, 1, 1);
        }
    } else {
        //Gửi mail đến bộ phận nếu không có thì gữi tới mail của email_support
        $response_recipients = get_user_in_cat($data['cat_id']);
        if (!empty($response_recipients)) {
            foreach ($response_recipients as $caregiver_id) {
                $caregiver_info = get_user_info($caregiver_id);
                if (!empty($caregiver_info)) {
                    $cat_title = get_user_of_cat($caregiver_id);
                    $xtpl = new XTemplate('mail_reply_to_caregiver.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
                    $xtpl->assign('TICKET', $data);
                    $xtpl->assign('CUSTOMER', $user_info);
                    $xtpl->assign('STAFF', implode(' ', get_staff_info($caregiver_id)));
                    $xtpl->assign('CAT_TITLE', $cat_title);
                    $xtpl->parse('main');
                    $messages = $xtpl->text('main');
                    nv_pending_mail($subject, $messages, $caregiver_info['email']);
                }

                //Gửi thông báo
                nv_insert_notification($module_name, 'user_reply', array(
                    'name' => $user_info['fullname'],
                    'link' => $data['admin_link'],
                    'ticket_id' => $data['id']
                ), $notify_obid, $caregiver_id, 0, 1, 1);
            }
        } else {
            if (!empty($module_config[$module_name]['email_support'])) {
                $xtpl = new XTemplate('mail_reply_to_caregiver.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
                $xtpl->assign('TICKET', $data);
                $xtpl->assign('CUSTOMER', $user_info);
                $xtpl->assign('STAFF', $module_config[$module_name]['email_support']);
                $xtpl->parse('main');
                $messages = $xtpl->text('main');
                nv_pending_mail($subject, $messages, $module_config[$module_name]['email_support']);
            }
        }

        //Gửi thông báo
        nv_insert_notification($module_name, 'user_reply', array(
            'name' => $user_info['fullname'],
            'link' => $data['admin_link'],
            'ticket_id' => $data['id']
        ), $notify_obid, 0, 0, 1, 1);
    }
}

//Gửi thông báo cmt đến khách hàng khi có nhân viên trả lời
function comment_notification_to_user($data, $comment_id, $point_quote, $reply_userid, $content, $mail_template = 'default')
{
    global $module_name, $nv_Lang, $module_file, $module_info;

    $user_info = get_user_info($data['customer_id']);
    $data['link'] = URL_SUPPORT . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . ($data['is_paid'] == 1 ? $module_info['alias']['detail_paid'] : $module_info['alias']['detail']) . '&amp;id=' . $data['id'];
    $staff_display = get_staff_info($reply_userid);
    $staff_display = implode(' ', $staff_display);
    $data['title'] = html_entity_decode(nv_htmlspecialchars($data['title']));

    //Gửi thông báo
    $arrMess = $data['is_paid'] == 1 ? [
        'vi' => sprintf(LangMulti::get('vi', 'reply_to_customer_by_paid'), $data['title'], $data['link']),
        'en' => sprintf(LangMulti::get('en', 'reply_to_customer_by_paid'), $data['title'], $data['link']),
    ] : [
        'vi' => sprintf(LangMulti::get('vi', 'reply_to_customer'), $staff_display, $data['title']),
        'en' => sprintf(LangMulti::get('en', 'reply_to_customer'), $staff_display, $data['title']),
    ];
    $res = InForm::insertInfrom($data['customer_id'], $arrMess, 'supportticket/' . ($data['is_paid'] == 1 ? $module_info['alias']['detail_paid'] : $module_info['alias']['detail']) . '/?id=' . $data['id'] . '#' . $comment_id);

    //Gửi mail
    if ($data['notify'] == 0) return;
    if (!empty($user_info)) {
        $xtpl = new XTemplate('mail_reply_to_customer.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
        $xtpl->assign('TICKET', $data);
        $xtpl->assign('CUSTOMER', $user_info);
        $xtpl->assign('STAFF', $staff_display);
        $xtpl->assign('CMT_TIME', nv_date('d/m/Y H:i:s', NV_CURRENTTIME));

        if ($data['is_paid'] == 1) {
            if ($point_quote > 0) {
                $xtpl->assign('PAID_POINT', $point_quote);
                $xtpl->parse('main.is_paid.paid');
            } else {
                $xtpl->parse('main.is_paid.free');
            }
            $xtpl->parse('main.is_paid');
        } elseif ($data['is_paid'] == 0) {
            $xtpl->parse('main.is_free');
        }

        $xtpl->parse('main');
        $data['title'] = truncate_title($data['title']);
        $subject = $data['is_paid'] == 1 ? $nv_Lang->getModule('mail_to_customer_subject', $data['title'], $data['id']) : $nv_Lang->getModule('mail_subject', $data['title']);
        $messages = $xtpl->text('main');
        nv_pending_mail($subject, $messages, $user_info['email']);
    }
}

//Gửi thông báo thanh toán đến người dùng
function payment_notification_to_customer($data, $paid = false, $mail_template = 'default')
{
    global $module_name, $nv_Lang, $module_file, $module_info;

    $user_info = get_user_info($data['customer_id']);
    $data['link'] = NV_MY_DOMAIN . NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail_paid'] . '&amp;id=' . $data['id'];
    $data['title'] = html_entity_decode(nv_htmlspecialchars($data['title']));

    //Gửi thông báo
    if ($paid) {
        $arrMess = [
            'vi' => sprintf(LangMulti::get('vi', 'notice_payment_success_to_customer'), $data['point_price'], $data['title']),
            'en' => sprintf(LangMulti::get('en', 'notice_payment_success_to_customer'), $data['point_price'], $data['title']),
        ];
    } else {
        $arrMess = [
            'vi' => sprintf(LangMulti::get('vi', 'notice_payment_to_customer'), $data['point_price']),
            'en' => sprintf(LangMulti::get('en', 'notice_payment_to_customer'), $data['point_price']),
        ];
    }
    $res = InForm::insertInfrom($data['customer_id'], $arrMess, 'supportticket/detail/?id=' . $data['id']);

    //Gửi mail
    if (!empty($user_info)) {
        $template = $paid ? 'mail_payment_success_to_customer.tpl' : 'mail_payment_to_customer.tpl';
        $xtpl = new XTemplate($template, NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
        $xtpl->assign('TICKET', $data);
        $xtpl->assign('CUSTOMER', $user_info);
        $xtpl->parse('main');

        $subject = sprintf($nv_Lang->getModule('mail_subject'), truncate_title($data['title']));
        $messages = $xtpl->text('main');
        nv_pending_mail($subject, $messages, $user_info['email']);
    }
}

//Gửi thông báo đã thanh toán đến quản trị
function payment_notification_to_admin($data, $mail_template = 'default')
{
    global $module_name, $module_config, $nv_Lang, $module_file;

    $data['admin_link'] = SITE_ID_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . ($data['is_paid'] == 1 ? 'ticket_detail_paid' : 'ticket_detail') . '&amp;id=' . $data['id'];
    $user_info = get_user_info($data['customer_id']);
    $notify_obid = $data['id'] ?? 0;
    $data['title'] = html_entity_decode(nv_htmlspecialchars($data['title']));
    $subject = sprintf($nv_Lang->getModule('mail_subject'), truncate_title($data['title']));

    $assignee = !empty($data['assignee_to']) ? explode(',', $data['assignee_to']) : [];
    //Thông báo cho tất cả người chăm sóc
    if (sizeof($assignee) > 0) {
        foreach ($assignee as $caregiver_id) {
            $caregiver_info = get_user_info($caregiver_id);
            //Gửi mail
            if (!empty($caregiver_info)) {
                $cat_title = get_user_of_cat($caregiver_id);
                $xtpl = new XTemplate('mail_payment_to_admin.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
                $xtpl->assign('TICKET', $data);
                $xtpl->assign('CUSTOMER', $user_info);
                $xtpl->assign('STAFF', implode(' ', get_staff_info($caregiver_id)));
                $xtpl->assign('CAT_TITLE', $cat_title);
                $xtpl->parse('main');
                $messages = $xtpl->text('main');
                nv_pending_mail($subject, $messages, $caregiver_info['email']);
            }

            //Gửi thông báo
            nv_insert_notification($module_name, 'payment_to_admin', array(
                'name' => $user_info['fullname'],
                'link' => $data['admin_link'],
                'ticket_id' => $data['id']
            ), $notify_obid, $caregiver_id, 0, 1, 1);
        }
    } else {
        //Gửi mail đến bộ phận - Nếu không có thì gữi mail tới email_support
        $response_recipients = get_user_in_cat($data['cat_id']);
        if (!empty($response_recipients)) {
            foreach ($response_recipients as $caregiver_id) {
                $caregiver_info = get_user_info($caregiver_id);
                if (!empty($caregiver_info)) {
                    $cat_title = get_user_of_cat($caregiver_id);
                    $xtpl = new XTemplate('mail_payment_to_admin.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
                    $xtpl->assign('TICKET', $data);
                    $xtpl->assign('CUSTOMER', $user_info);
                    $xtpl->assign('STAFF', implode(' ', get_staff_info($caregiver_id)));
                    $xtpl->assign('CAT_TITLE', $cat_title);
                    $xtpl->parse('main');
                    $messages = $xtpl->text('main');
                    nv_pending_mail($subject, $messages, $caregiver_info['email']);
                }

                //Gửi thông báo
                nv_insert_notification($module_name, 'payment_to_admin', array(
                    'name' => $user_info['fullname'],
                    'link' => $data['admin_link'],
                    'ticket_id' => $data['id']
                ), $notify_obid, $caregiver_id, 0, 1, 1);
            }
        } else {
            if (!empty($module_config[$module_name]['email_support'])) {
                $xtpl = new XTemplate('mail_payment_to_admin.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
                $xtpl->assign('TICKET', $data);
                $xtpl->assign('CUSTOMER', $user_info);
                $xtpl->assign('STAFF', $module_config[$module_name]['email_support']);
                $xtpl->parse('main');
                $messages = $xtpl->text('main');
                nv_pending_mail($subject, $messages, $module_config[$module_name]['email_support']);
            }
        }

        //Gửi thông báo
        nv_insert_notification($module_name, 'payment_to_admin', array(
            'name' => $user_info['fullname'],
            'link' => $data['admin_link'],
            'ticket_id' => $data['id']
        ), $notify_obid, 0, 0, 1, 1);
    }
}

//Gửi mail thông báo đến KH khi ticket bị đóng
function close_notification_to_user($data, $is_admin = false, $mail_template = 'default')
{
    global $nv_Lang, $module_name, $module_file, $module_info;

    $user_info = get_user_info($data['customer_id']);
    $data['link'] = URL_SUPPORT . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . ($data['is_paid'] == 1 ? $module_info['alias']['detail_paid'] : $module_info['alias']['detail']) . '&amp;id=' . $data['id'];

    $data['title'] = html_entity_decode(nv_htmlspecialchars($data['title']));

    //Gửi thông báo
    $arrMess = [
        'vi' => sprintf(LangMulti::get('vi', 'notice_close_to_customer'), $data['title']),
        'en' => sprintf(LangMulti::get('en', 'notice_close_to_customer'), $data['title']),
    ];
    $res = InForm::insertInfrom($data['customer_id'], $arrMess, 'supportticket/detail/?id=' . $data['id']);

    //Gửi mail
    if (!empty($user_info)) {
        $xtpl = new XTemplate('mail_close_to_customer.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
        $xtpl->assign('TICKET', $data);
        $xtpl->assign('CUSTOMER', $user_info);
        // if ($is_admin) {
        //     $xtpl->parse('main.admin');
        // } else {
        //     $xtpl->parse('main.site');
        // }
        $xtpl->parse('main');
        $subject = sprintf($nv_Lang->getModule('mail_subject'), truncate_title($data['title']));
        $messages = $xtpl->text('main');
        nv_pending_mail($subject, $messages, $user_info['email']);
    }
}

// Hàm gửi email cho người chăm sóc
function send_mail_to_caregiver($subject, $data, $caregiver_id, $template, $mail_template = 'default')
{
    global $module_file, $module_config, $module_name;
    $caregiver_info = get_user_info($caregiver_id);
    if (empty($caregiver_info)) {
        $staff_info = 'Admin';
        $send_email = $module_config[$module_name]['email_support'];
    } else {
        $staff_info = implode(' ', get_staff_info($caregiver_id));
        $send_email = $caregiver_info['email'];
    }

    $cat_title = get_user_of_cat($caregiver_id);
    $xtpl = new XTemplate($template, NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
    $xtpl->assign('TICKET', $data);
    $xtpl->assign('STAFF', $staff_info);
    $xtpl->assign('CAT_TITLE', $cat_title);
    $xtpl->parse('main');
    $messages = $xtpl->text('main');
    nv_pending_mail($subject, $messages, $send_email);
}

// Hàm thông báo khi đóng hoặc mở lại ticket
function process_notification_to_caregiver($data, $notify_type)
{
    global $module_name, $module_config, $nv_Lang;

    $data['admin_link'] = SITE_ID_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . ($data['is_paid'] == 1 ? 'ticket_detail_paid' : 'ticket_detail') . '&amp;id=' . $data['id'];
    $user_info = get_user_info($data['customer_id']);
    $data['title'] = html_entity_decode(nv_htmlspecialchars($data['title']));
    $notify_obid = $data['id'] ?? 0;
    $subject = sprintf($nv_Lang->getModule('mail_subject'), truncate_title($data['title']));

    $assignee = !empty($data['assignee_to']) ? explode(',', $data['assignee_to']) : [];
    $response_recipients = (!empty($assignee)) ? $assignee : get_user_in_cat($data['cat_id']);

    if (!empty($response_recipients)) {
        foreach ($response_recipients as $caregiver_id) {
            send_mail_to_caregiver($subject, $data, $caregiver_id, 'mail_' . $notify_type . '_to_caregiver.tpl');

            // Gửi thông báo
            nv_insert_notification($module_name, 'user_' . $notify_type, array(
                'name' => $user_info['fullname'],
                'link' => $data['admin_link'],
                'ticket_id' => $data['id']
            ), $notify_obid, $caregiver_id, 0, 1, 1);
        }
    } else {
        if (!empty($module_config[$module_name]['email_support'])) {
            send_mail_to_caregiver($subject, $data, 0, 'mail_' . $notify_type . '_to_caregiver.tpl');

            // Gửi thông báo
            nv_insert_notification($module_name, 'user_' . $notify_type, array(
                'name' => $user_info['fullname'],
                'link' => $data['admin_link'],
                'ticket_id' => $data['id']
            ), $notify_obid, 0, 0, 1, 1);
        }
    }
}

// Gửi mail thông báo khi đóng ticket
function close_notification_to_caregiver($data)
{
    process_notification_to_caregiver($data, 'close');
}

// Gửi mail thông báo khi mở lại ticket
function reopen_notification_to_caregiver($data)
{
    process_notification_to_caregiver($data, 'reopen');
}

//Debug dữ liệu
function dev_debug($data)
{
    file_put_contents(NV_ROOTDIR . '/debug.log', print_r($data, true), LOCK_EX);
}

//Ghi point logs
function add_point_logs($userid, $ticket_id, $comment_id, $point, $status, $message)
{
    global $db;
    $stmt = $db->prepare("INSERT INTO " . TB_TICKET_POINT_LOG . " (userid, ticket_id, comment_id, point, status, message, add_time) VALUES (:userid, :ticket_id, :comment_id, :point, :status, :message, :add_time)");
    $stmt->bindParam(':userid', $userid, PDO::PARAM_INT);
    $stmt->bindParam(':ticket_id', $ticket_id, PDO::PARAM_INT);
    $stmt->bindParam(':comment_id', $comment_id, PDO::PARAM_INT);
    $stmt->bindParam(':point', $point, PDO::PARAM_INT);
    $stmt->bindParam(':status', $status, PDO::PARAM_INT);
    $stmt->bindParam(':message', $message, PDO::PARAM_STR);
    $stmt->bindValue(':add_time', NV_CURRENTTIME, PDO::PARAM_INT);
    $stmt->execute();
}

//Ghi ticket logs
function add_ticket_logs($userid, $log_key, $log_data, $ticket_id, $log_area = 0, $vips_id = 0)
{
    global $db;
    $log_data = json_encode($log_data);
    $stmt = $db->prepare("INSERT INTO " . TB_TICKET_ALLLOG . " (userid, log_area, log_key, log_time, log_data, ticket_id, vips_id) VALUES (:userid, :log_area, :log_key, :log_time, :log_data, :ticket_id, :vips_id)");
    $stmt->bindParam(':userid', $userid, PDO::PARAM_INT);
    $stmt->bindParam(':log_area', $log_area, PDO::PARAM_INT);
    $stmt->bindParam(':log_key', $log_key, PDO::PARAM_STR);
    $stmt->bindValue(':log_time', NV_CURRENTTIME, PDO::PARAM_INT);
    $stmt->bindParam(':log_data', $log_data, PDO::PARAM_STR);
    $stmt->bindParam(':ticket_id', $ticket_id, PDO::PARAM_INT);
    $stmt->bindParam(':vips_id', $vips_id, PDO::PARAM_INT);
    $stmt->execute();
}

//Cập nhật thông tin tương tác
function update_ticket_activity($ticket_id, $status, $status_client, $last_comment_userid)
{
    global $db;
    $stmt = $db->prepare("UPDATE " . TB_TICKET_ROW . " SET status=:status, status_client=:status_client, last_comment_userid=:last_comment_userid, last_comment_time=:last_comment_time, activity_time=:activity_time WHERE id=:id");
    $stmt->bindParam(':status', $status, PDO::PARAM_INT);
    $stmt->bindParam(':status_client', $status_client, PDO::PARAM_INT);
    $stmt->bindParam(':last_comment_userid', $last_comment_userid, PDO::PARAM_INT);
    $stmt->bindValue(':last_comment_time', NV_CURRENTTIME, PDO::PARAM_INT);
    $stmt->bindValue(':activity_time', NV_CURRENTTIME, PDO::PARAM_INT);
    $stmt->bindParam(':id', $ticket_id, PDO::PARAM_INT);
    $stmt->execute();
}

// Kiểm tra tình trạng câu hỏi hiện tại để cập nhật status
function check_current_status($ticket)
{
    global $db;
    $answer = $db->query(
        '
        SELECT
            COUNT(IF(comment_type IN (' . CommentType::AI->value . ',' . CommentType::Expert->value . '), 1, NULL)) as total,
            COUNT(IF(comment_type = ' . CommentType::AI->value . ', 1, NULL)) as total_ai,
            COUNT(IF(comment_type = ' . CommentType::Expert->value . ', 1, NULL)) as total_expert,
            COUNT(IF(comment_type = ' . CommentType::AI->value . ' AND status IN (' . CommentStatus::Open->value . ',' . CommentStatus::Process->value . '), 1, NULL)) as total_ai_process,
            COUNT(IF(comment_type = ' . CommentType::Expert->value . ' AND status IN (' . CommentStatus::Open->value . ',' . CommentStatus::Process->value . '), 1, NULL)) as total_expert_process
        FROM ' . TB_TICKET_LOG . '
        WHERE ticket_id=' . $ticket['id']
    )->fetch();

    if ($ticket['ask_ai'] == 1 && $ticket['ask_expert'] == 1) {
        if ($answer['total'] == 0 || ($answer['total_ai'] > 0 && $answer['total_expert'] == 0)) {
            return TicketStatus::Open->value;
        }
        if ($answer['total_ai_process'] == 0 && $answer['total_expert_process'] == 0) {
            return TicketStatus::Done->value;
        }
        if (($answer['total_expert'] > 0 && $answer['total_ai'] == 0) || $answer['total_ai_process'] > 0 || $answer['total_expert_process'] > 0) {
            return TicketStatus::Process->value;
        }
    } elseif ($ticket['ask_ai'] == 1) {
        if ($answer['total_ai'] == 0) {
            return TicketStatus::Open->value;
        }
        if ($answer['total_ai_process'] == 0) {
            return TicketStatus::Done->value;
        }
        if ($answer['total_ai_process'] > 0) {
            return TicketStatus::Process->value;
        }
    } elseif ($ticket['ask_expert'] == 1) {
        if ($answer['total_expert'] == 0) {
            return TicketStatus::Open->value;
        }
        if ($answer['total_expert_process'] == 0) {
            return TicketStatus::Done->value;
        }
        if ($answer['total_expert_process'] > 0) {
            return TicketStatus::Process->value;
        }
    }

    return TicketStatus::Draft->value;
}

//Kiểm tra câu trả lời của AI có phải là nội dung có giá trị hay không
function check_valid_ai_content($content)
{
    global $module_config, $module_name;
    $keyworks = str_replace("\n", "", $module_config[$module_name]['invalid_ai_keywords']);
    $keyworks = explode(',', $keyworks);
    $keyworks = array_map('trim', $keyworks);
    $keyworks = array_filter($keyworks);
    $keyworks = array_values($keyworks);
    if (empty($keyworks)) {
        return true;
    }

    $pattern = '/' . implode('|', array_map('preg_quote', $keyworks)) . '/i';
    if (preg_match($pattern, $content)) {
        return false;
    }

    return true;
}

//Hỏi chatbot
function ask_chatbot_ai($question)
{
    $reply_content = '';
    // Header để browser biết là đang gởi stream
    header("Content-Type: text/event-stream");
    header("Cache-Control: no-cache");
    header('X-Accel-Buffering: no'); // Nếu server là nginx thì thêm header này để nginx không buffer data mà đẩy thẳng về client

    // Tắt buffer của PHP
    while (ob_get_level()) {
        ob_end_flush();
    }
    set_time_limit(0);

    $ch = curl_init();

    $current_time = time();
    $hashsecret = md5(API_CHATBOT_SECRET . '_' . $current_time);

    // Request body đến API chatbot
    $body = [
        'apikey' => API_CHATBOT_KEY,
        'hashsecret' => $hashsecret,
        'timestamp' => $current_time,
        'question' => $question
    ];

    // Set cURL options
    curl_setopt($ch, CURLOPT_URL, API_CHATBOT_URL . ':' . API_CHATBOT_PORT);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($body));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, false); // Không in kết quả cURL ra
    curl_setopt($ch, CURLOPT_WRITEFUNCTION, function ($ch, $data) use (&$reply_content) {
        $data_with_newlines = str_replace("\n", "\\n", $data);
        echo "event: message\n";
        echo "data: " . $data_with_newlines . "\n\n";
        flush(); // Đẩy dữ liệu xuống client
        $reply_content .= $data;
        return strlen($data);
    }); // Callback xử lý dữ liệu từ cURL

    curl_exec($ch);
    curl_close($ch);

    $valid_content = check_valid_ai_content($reply_content);
    $words = preg_split('/\s+/', preg_replace('/[^\p{L}\p{N}\s]/u', '', trim($reply_content)));
    $count_word = count($words);

    return [
        'valid' => $valid_content,
        'content' => $reply_content,
        'count_word' => $count_word,
    ];
}

//Hàm loại bỏ thẻ html, khoảng trắng 2 đầu, và ký tự khoảng trắng
function nv_only_text($text)
{
    if (empty($text)) {
        return '';
    }
    $text = strip_tags($text);
    $text = str_replace('&nbsp;', ' ', $text);
    $text = nv_htmlspecialchars($text);
    $text = trim($text);
    $text = preg_replace('/[\r\n]+/', ' ', $text);
    $text = preg_replace('/\s+/', ' ', $text);

    return $text;
}

//Gửi thông báo đến khách hàng khi khách hàng hỏi AI
function notification_selection_to_customer($ticket, $selection, $mail_template = 'default')
{
    global $nv_Lang, $module_name, $module_file, $module_info;

    $user_info = get_user_info($ticket['customer_id']);
    $ticket['link'] = NV_MY_DOMAIN . NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail_paid'] . '&amp;id=' . $ticket['id'];
    $ticket['title'] = html_entity_decode(nv_htmlspecialchars($ticket['title']));
    $ticket['min_point_total'] = $ticket['min_point_ai'] + $ticket['min_point_expert'];

    if (!empty($user_info)) {
        //Gửi mail
        $xtpl = new XTemplate('mail_selection_to_customer.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
        $xtpl->assign('TICKET', $ticket);
        $xtpl->assign('CUSTOMER', $user_info);
        if ($selection == 1) {
            $inform_key = 'create_ai_to_customer';
            $xtpl->parse('main.ai');
        } elseif ($selection == 2) {
            $inform_key = 'create_expert_to_customer';
            $xtpl->parse('main.expert');
        } elseif ($selection == 3) {
            $inform_key = 'create_both_to_customer';
            $xtpl->parse('main.all');
        }
        $xtpl->parse('main');
        $messages = $xtpl->text('main');
        $subject = $nv_Lang->getModule('mail_to_customer_subject', truncate_title($ticket['title']), $ticket['id']);
        nv_pending_mail($subject, $messages, $user_info['email']);

        //Gửi thông báo
        $arrMess = [
            'vi' => sprintf(LangMulti::get('vi', $inform_key), $ticket['title']),
            'en' => sprintf(LangMulti::get('en', $inform_key), $ticket['title']),
        ];
        InForm::insertInfrom($ticket['customer_id'], $arrMess, 'supportticket/' . $module_info['alias']['detail_paid'] . '/?id=' . $ticket['id']);
    }
}

//Gửi thông báo đã yêu cầu hoàn điểm đến khách hàng
function notification_refund_to_customer($ticket, $comment, $status, $mail_template = 'default')
{
    global $nv_Lang, $module_name, $module_file, $module_info;

    $user_info = get_user_info($ticket['customer_id']);
    $ticket['link'] = NV_MY_DOMAIN . NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail_paid'] . '&amp;id=' . $ticket['id'];
    $ticket['title'] = html_entity_decode(nv_htmlspecialchars($ticket['title']));

    if (!empty($user_info)) {
        //Gửi mail
        $xtpl = new XTemplate('mail_refund_to_customer.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
        $xtpl->assign('TICKET', $ticket);
        $xtpl->assign('COMMENT', $comment);
        $xtpl->assign('CUSTOMER', $user_info);
        if ($status == RefundStatus::Open) {
            $inform_key = 'refund_open_to_customer';
            $xtpl->parse('main.open');
        } elseif ($status == RefundStatus::Accept) {
            $inform_key = 'refund_accept_to_customer';
            $xtpl->parse('main.accept');
        } elseif ($status == RefundStatus::Refuse) {
            $inform_key = 'refund_refuse_to_customer';
            $xtpl->parse('main.refuse');
        }
        $xtpl->parse('main');
        $messages = $xtpl->text('main');
        $subject = $nv_Lang->getModule('mail_to_customer_subject', truncate_title($ticket['title']), $ticket['id']);
        nv_pending_mail($subject, $messages, $user_info['email']);

        //Gửi thông báo
        $arrMess = [
            'vi' => sprintf(LangMulti::get('vi', $inform_key), $ticket['title']),
            'en' => sprintf(LangMulti::get('en', $inform_key), $ticket['title']),
        ];
        InForm::insertInfrom($ticket['customer_id'], $arrMess, 'supportticket/' . $module_info['alias']['detail_paid'] . '/?id=' . $ticket['id']);
    }
}

//Gửi thông báo đến chuyên gia khi KH chọn hỏi chuyên gia
function notification_selection_to_expert($ticket, $mail_template = 'default')
{
    global $nv_Lang, $module_name, $module_file, $module_info;

    $user_info = get_user_info($ticket['customer_id']);
    $ticket['link'] = NV_MY_DOMAIN . NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail_paid'] . '&amp;id=' . $ticket['id'];
    $ticket['admin_link'] = SITE_ID_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail_paid&amp;id=' . $ticket['id'];
    $ticket['title'] = html_entity_decode(nv_htmlspecialchars($ticket['title']));
    $ticket['time_limit'] = $ticket['add_time'] + 86400;
    $ticket['time_limit'] = nv_date('H:i:s d/m/Y', $ticket['time_limit']);

    if (!empty($user_info)) {
        $sale_username_array = [];
        if (!empty($ticket['assignee_to'])) {
            $asignee_list = explode(',', $ticket['assignee_to']);
            foreach ($asignee_list as $saleid) {
                $sale_info = get_user_info($saleid);
                if (!empty($sale_info)) {
                    $sale_username_array[] = $sale_info['username'];
                }
            }
        }

        $username_array = [];
        $response_recipients = get_user_in_cat($ticket['cat_id']);
        if (!empty($response_recipients)) {
            foreach ($response_recipients as $expert) {
                $expert_info = get_user_info($expert);
                if (!empty($expert_info)) {
                    $username_array[] = $expert_info['username'];
                    $xtpl = new XTemplate('mail_selection_to_expert.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
                    $xtpl->assign('TICKET', $ticket);
                    $xtpl->assign('EXPERT', $expert_info);
                    $xtpl->assign('CUSTOMER', $user_info);
                    $xtpl->parse('main');
                    $messages = $xtpl->text('main');
                    $subject = $nv_Lang->getModule('mail_to_expert_subject', truncate_title($ticket['title']), $user_info['fullname']);
                    nv_pending_mail($subject, $messages, $expert_info['email']);
                }
            }
        }
        // Thông báo đến slack
        if (!empty($username_array)) {
            $expert_array = [];
            foreach ($username_array as $username) {
                $expert_array[] = '<@' . str_replace(' ', '_', $username) . '>';
            }
            $expert_list = implode(', ', $expert_array) . " ";

            if (!empty($sale_username_array)) {
                $sale_array = [];
                foreach ($sale_username_array as $username) {
                    $username = replace_slack_username($username);
                    $sale_array[] = '<@' . str_replace(' ', '_', $username) . '>';
                }
                $sale_list = implode(', ', $sale_array) . " ";
                $message = $nv_Lang->getModule('slack_to_expert_and_sale', $user_info['fullname'], $sale_list, $expert_list, $ticket['admin_link'], $ticket['time_limit']);
            } else {
                $message = $nv_Lang->getModule('slack_to_expert', $user_info['fullname'], $expert_list, $ticket['admin_link'], $ticket['time_limit']);
            }

            $message = urlencode($message);
            notify_to_slack($message);
        }
    }
}

//Gửi thông báo đến chuyên gia và sale(nếu có) khi KH chọn hỏi AI
function notification_selected_ai_to_expert($ticket)
{
    global $nv_Lang, $module_name, $module_file, $module_info;

    $user_info = get_user_info($ticket['customer_id']);
    $ticket['link'] = NV_MY_DOMAIN . NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail_paid'] . '&amp;id=' . $ticket['id'];
    $ticket['admin_link'] = SITE_ID_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail_paid&amp;id=' . $ticket['id'];
    $ticket['title'] = html_entity_decode(nv_htmlspecialchars($ticket['title']));

    if (!empty($user_info)) {
        $sale_username_array = [];
        if (!empty($ticket['assignee_to'])) {
            $asignee_list = explode(',', $ticket['assignee_to']);
            foreach ($asignee_list as $saleid) {
                $sale_info = get_user_info($saleid);
                if (!empty($sale_info)) {
                    $sale_username_array[] = $sale_info['username'];
                }
            }
        }

        $username_array = [];
        $response_recipients = get_user_in_cat($ticket['cat_id']);
        if (!empty($response_recipients)) {
            foreach ($response_recipients as $expert) {
                $expert_info = get_user_info($expert);
                if (!empty($expert_info)) {
                    $username_array[] = $expert_info['username'];
                }
            }
        }
        // Thông báo đến slack
        if (!empty($username_array)) {
            $expert_array = [];
            foreach ($username_array as $username) {
                $expert_array[] = '<@' . str_replace(' ', '_', $username) . '>';
            }
            $expert_list = implode(', ', $expert_array) . " ";

            if (!empty($sale_username_array)) {
                $sale_array = [];
                foreach ($sale_username_array as $username) {
                    $username = replace_slack_username($username);
                    $sale_array[] = '<@' . str_replace(' ', '_', $username) . '>';
                }
                $sale_list = implode(', ', $sale_array) . " ";
                $message = $nv_Lang->getModule('slack_selected_ai_to_expert_and_sale', $user_info['fullname'], $sale_list, $ticket['admin_link']);
            } else {
                $message = $nv_Lang->getModule('slack_selected_ai_to_expert', $user_info['fullname'], $expert_list, $ticket['admin_link']);
            }

            $message = urlencode($message);
            notify_to_slack($message);
        }
    }
}

//Gửi thông báo đến chuyên gia khi KH hỏi bổ sung
function notification_additional_to_expert($ticket, $root_comment, $additional_comment, $mail_template = 'default')
{
    global $nv_Lang, $module_name, $module_file, $module_info;

    $user_info = get_user_info($ticket['customer_id']);
    $ticket['link'] = NV_MY_DOMAIN . NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail_paid'] . '&amp;id=' . $ticket['id'];
    $ticket['admin_link'] = SITE_ID_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail_paid&amp;id=' . $ticket['id'];
    $ticket['title'] = html_entity_decode(nv_htmlspecialchars($ticket['title']));
    $ticket['time_limit'] = $additional_comment['add_time'] + 86400;
    $ticket['time_limit'] = nv_date('H:i:s d/m/Y', $ticket['time_limit']);

    if (!empty($user_info)) {
        $expert_info = get_user_info($root_comment['reply_userid']);
        if (!empty($expert_info)) {
            $xtpl = new XTemplate('mail_additional_to_expert.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
            $xtpl->assign('TICKET', $ticket);
            $xtpl->assign('EXPERT', $expert_info);
            $xtpl->assign('CUSTOMER', $user_info);
            $xtpl->parse('main');
            $messages = $xtpl->text('main');
            $subject = $nv_Lang->getModule('mail_to_expert_subject', truncate_title($ticket['title']), $user_info['fullname']);
            nv_pending_mail($subject, $messages, $expert_info['email']);

            // Thông báo đến slack
            $username = replace_slack_username($expert_info['username']);
            $expert_username = '<@' . str_replace(' ', '_', $username) . '>';
            $message = $nv_Lang->getModule('slack_to_expert', $user_info['fullname'], $expert_username, $ticket['admin_link'], $ticket['time_limit']);
            $message = urlencode($message);
            notify_to_slack($message);
        }
    }
}

// Gửi thông báo yêu cầu hoàn điểm đến admin
function notification_refund_to_admin($ticket, $comment, $mail_template = 'default')
{
    global $nv_Lang, $module_name, $module_file, $module_info;

    $user_info = get_user_info($ticket['customer_id']);
    $ticket['link'] = NV_MY_DOMAIN . NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail_paid'] . '&amp;id=' . $ticket['id'];
    $ticket['admin_link'] = SITE_ID_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail_paid&amp;id=' . $ticket['id'];
    $ticket['title'] = html_entity_decode(nv_htmlspecialchars($ticket['title']));
    $comment['time_limit'] = $comment['rating_add_time'] + 86400;
    $comment['time_limit'] = nv_date('H:i:s d/m/Y', $comment['time_limit']);

    if (!empty($user_info)) {
        $response_recipients = get_user_in_cat($ticket['cat_id']);
        if (!empty($response_recipients)) {
            foreach ($response_recipients as $expert) {
                $expert_info = get_user_info($expert);
                if (!empty($expert_info)) {
                    $xtpl = new XTemplate('mail_refund_to_admin.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
                    $xtpl->assign('TICKET', $ticket);
                    $xtpl->assign('EXPERT', $expert_info);
                    $xtpl->assign('CUSTOMER', $user_info);
                    $xtpl->assign('COMMENT', $comment);
                    $xtpl->parse('main');
                    $messages = $xtpl->text('main');
                    $subject = $nv_Lang->getModule('mail_refund_to_admin_subject', $user_info['fullname'], truncate_title($ticket['title']), $ticket['id']);
                    nv_pending_mail($subject, $messages, $expert_info['email']);
                }
            }
        }
    }
}

// Gửi thông báo đển khách hàng đã xử lý cộng điểm
function notification_bonus_point_to_customer($ticket, $action_type, $mail_template = 'default')
{
    global $nv_Lang, $module_name, $module_file, $module_info, $array_active_cats;
    $user_info = get_user_info($ticket['customer_id']);
    $ticket['link'] = NV_MY_DOMAIN . NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail'] . '&amp;id=' . $ticket['id'];
    $ticket['title'] = html_entity_decode(nv_htmlspecialchars($ticket['title']));
    $ticket['cat_title'] = $array_active_cats[$ticket['cat_id']]['title_' . NV_LANG_DATA];

    if (!empty($user_info)) {
        $xtpl = new XTemplate('mail_bonus_point_to_customer.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
        $xtpl->assign('TICKET', $ticket);
        $xtpl->assign('CUSTOMER', $user_info);
        if ($action_type == PlusPointStatus::Deny->value) {
            $xtpl->parse('main.deny');
        } else {
            $xtpl->parse('main.plus');
        }
        $xtpl->parse('main');
        $messages = $xtpl->text('main');
        $subject = $nv_Lang->getModule('mail_bonus_point_to_customer', $ticket['cat_title'], truncate_title($ticket['title']));
        nv_pending_mail($subject, $messages, $user_info['email']);

        //Gửi thông báo
        $arrMess = [
            'vi' => sprintf(LangMulti::get('vi', $ticket['cat_id'] == 10 ? 'notice_bonus_report_to_customer' : 'notice_bonus_ides_to_customer'), $ticket['title'], $ticket['id']),
            'en' => sprintf(LangMulti::get('en', $ticket['cat_id'] == 10 ? 'notice_bonus_report_to_customer' : 'notice_bonus_ides_to_customer'), $ticket['title'], $ticket['id']),
        ];
        $link_detail = $ticket['is_paid'] == 1 ? $module_info['alias']['detail_paid'] : $module_info['alias']['detail'];
        InForm::insertInfrom($ticket['customer_id'], $arrMess, 'supportticket/' . $link_detail . '/?id=' . $ticket['id']);
    }
}

// Gửi mail Báo lỗi/ Đóng góp ý tưởng tới customer
function ticket_notification_report_ideas_to_user($data, $mail_template = 'default')
{
    global $nv_Lang, $module_name, $module_file, $module_info;

    $user_info = get_user_info($data['customer_id']);
    $data['link'] = URL_SUPPORT . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail'] . '&amp;id=' . $data['id'];
    $data['title'] = html_entity_decode(nv_htmlspecialchars($data['title']));

    //Gửi thông báo
    $arrMess = [
        'vi' => sprintf(LangMulti::get('vi', 'notice_to_customer'), $data['title']),
        'en' => sprintf(LangMulti::get('en', 'notice_to_customer'), $data['title']),
    ];
    $link_detail = $data['is_paid'] == 1 ? $module_info['alias']['detail_paid'] : $module_info['alias']['detail'];
    InForm::insertInfrom($data['customer_id'], $arrMess, 'supportticket/' . $link_detail . '/?id=' . $data['id']);

    //Gửi mail
    if (!empty($user_info)) {
        $xtpl = new XTemplate('mail_create_report_ideas_to_customer.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
        $xtpl->assign('TICKET', $data);
        $xtpl->assign('CUSTOMER', $user_info);
        $subject = sprintf($nv_Lang->getModule('mail_subject'), truncate_title($data['title']));
        if ($data['cat_id'] == 10) {
            $xtpl->parse('main.report');
        } else {
            $xtpl->parse('main.ideas');
        }
        $xtpl->parse('main');

        $messages = $xtpl->text('main');
        nv_pending_mail($subject, $messages, $user_info['email']);
    }
}

// Gửi mail Báo lỗi/ Đóng góp ý tưởng tới admin
function ticket_notification_report_ideas_to_admin($data, $mail_template = 'default')
{
    global $module_name, $nv_Lang, $module_file;

    $user_info = get_user_info($data['customer_id']);
    $data['admin_link'] = SITE_ID_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . ($data['is_paid'] == 1 ? 'ticket_detail_paid' : 'ticket_detail') . '&amp;id=' . $data['id'];
    $data['title'] = html_entity_decode(nv_htmlspecialchars($data['title']));
    $data['time_limit'] = $data['add_time'] + 86400;
    $data['time_limit'] = nv_date('H:i:s d/m/Y', $data['time_limit']);

    //Gửi thông báo
    $notify_obid = $data['id'] ?? 0;
    nv_insert_notification($module_name, $data['cat_id'] == 10 ? 'report_to_admin' : 'ideas_to_admin', array(
        'name' => $user_info['fullname'],
        'link' => $data['admin_link'],
        'ticket_id' => $data['id']
    ), $notify_obid, 0, 0, 1, 1);

    // Gữi email tới người trực tiếp nhận email của bộ phận
    $response_recipients = get_user_in_cat($data['cat_id']);
    if (!empty($response_recipients)) {
        $username_array = [];
        foreach ($response_recipients as $caregiver_id) {
            $caregiver_info = get_user_info($caregiver_id);
            if (!empty($caregiver_info)) {
                $username_array[] = $caregiver_info['username'];
                $subject = sprintf($nv_Lang->getModule($data['cat_id'] == 10 ? 'mail_report_to_admin' : 'mail_ideas_to_admin'), $user_info['fullname']);
                $xtpl = new XTemplate('mail_create_report_ideas_to_admin.tpl', NV_ROOTDIR . '/themes/admin_default/modules/' . $module_file . '/mail/' . $mail_template . '/'  . NV_LANG_DATA);
                $xtpl->assign('TICKET', $data);
                $xtpl->assign('CUSTOMER', $user_info);
                $xtpl->assign('CAT_TITLE', $data['cat_id'] == 10 ? 'BÁO LỖI' : 'ĐÓNG GÓP Ý TƯỞNG');
                $xtpl->parse('main');

                $messages = $xtpl->text('main');
                nv_pending_mail($subject, $messages, $caregiver_info['email']);
            }
        }

        // Thông báo đến slack
        if (!empty($username_array)) {
            $caregiver_array = [];
            foreach ($username_array as $username) {
                $username = replace_slack_username($username);
                $caregiver_array[] = '<@' . str_replace(' ', '_', $username) . '>';
            }
            $caregiver_list = implode(', ', $caregiver_array) . " ";
            $message = $nv_Lang->getModule('slack_to_admin', $user_info['fullname'], $caregiver_list, $data['admin_link'], $data['time_limit']);
            $message = urlencode($message);
            notify_to_slack($message);
        }
    }
}

// Chức năng lấy ngôn ngữ cả vi và en
function get_both_lang($lang_alias)
{
    global $nv_Lang, $module_file;
    $message_log = [
        'vi' => '',
        'en' => ''
    ];
    $nv_Lang->changeLang('vi');
    $nv_Lang->loadModule($module_file, false, true);
    $message_log['vi'] = $nv_Lang->getModule($lang_alias);

    $nv_Lang->changeLang('en');
    $nv_Lang->loadModule($module_file, false, true);
    $message_log['en'] = $nv_Lang->getModule($lang_alias);
    $nv_Lang->changeLang(NV_LANG_INTERFACE);

    return $message_log;
}

// Nếu không có tiêu đề thì lấy câu đầu tiên trong nội dung làm tiêu đề
function extract_first_sentence($content)
{
    // Xác định các ký tự phân cách câu
    $delimiters = ['.', '!', '?'];
    $positions = [];
    foreach ($delimiters as $delimiter) {
        $pos = strpos($content, $delimiter);
        if ($pos !== false) {
            $positions[] = $pos;
        }
    }
    if (empty($positions)) {
        return $content;
    }
    $endPos = min($positions);
    $title = substr($content, 0, $endPos + 1);
    $title = trim($title);
    return $title;
}

// Trường hợp tiêu đề quá dài thì sẽ cắt
function truncate_title($title)
{
    if (nv_strlen($title) > 160) {
        $title = nv_substr($title, 0, 160);
        $title .= '...';
    }
    return $title;
}

function decode_file($file_attach = '')
{
    $arr_file = [];
    if (!empty($file_attach)) {
        $decoded_json = json_decode($file_attach, true);
        if (is_array($decoded_json)) {
            $arr_file = $decoded_json;
        } else {
            $unserialized = @unserialize($file_attach);
            $arr_file = is_array($unserialized) ? $unserialized : [];
        }
    }
    return $arr_file;
}

function notify_to_slack($message)
{
    if (!defined('NV_API_TICKET') || !defined('NV_CHANNEL_TICKET')) {
        return;
    }

    $data = "payload=" . json_encode([
        "channel" => NV_CHANNEL_TICKET,
        "text" => $message,
        "icon_emoji" => ':timer_clock'
    ]);

    $ch = curl_init(NV_API_TICKET);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    curl_close($ch);
}

/**
 * @param mixed $ticket
 * @return bool
 */
function check_ticket_answer($ticket)
{
    global $db, $db_config, $module_data;
    $comment_type = [
        CommentType::Expert->value,
        CommentType::AI->value,
        CommentType::Staff->value,
    ];
    $answer = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $ticket['id'] . ' AND comment_type IN (' . implode(',', $comment_type) . ')')->fetch();

    if (empty($answer)) {
        return false;
    }

    return true;
}

/** Thay thế username để khớp với username slack */
function replace_slack_username($username)
{
    $mappings = [
        'anhduc80' => 'anhduc',
        'Thuy Duong Vinades' => 'thuyduong',
        'Thuha274' => 'nguyenthuha',
        'ngochoa_vndes' => 'ngochoa',
        'quynhtth' => 'huongquynhqt',
        'HaiXuan' => 'haixuan357',
    ];
    $username = $mappings[$username] ?? $username;

    return $username;
}

// Hiển thị thời gian đã trả lời cmt
function reply_range_time($times)
{
    global $nv_Lang;

    if ($times < 60) {
        return $times . ' ' . $nv_Lang->getModule('seconds');
    } elseif ($times < 3600) {
        return round($times / 60, 2) . ' ' . $nv_Lang->getModule('minutes');
    } else {
        return round($times / 3600, 2) . ' ' . $nv_Lang->getModule('hours');
    }
}

/**
 * Kiểm tra xem admin có quyền xử lý ticket trả phí thuộc danh mục hay không
 *
 * @param int $userid ID của admin
 * @param int $cat_id ID danh mục
 * @return bool True nếu có quyền, false nếu không
 */
function can_handle_paid_ticket($cat_id)
{
    global $db, $admin_info;

    $sql = 'SELECT userid FROM ' . TB_TICKET_CATADMIN . ' WHERE userid=' . $admin_info['userid'] . ' AND cat_id=' . (int)$cat_id;
    $_result = $db->query($sql)->fetchColumn();
    return empty($_result) ? false : true;
}
