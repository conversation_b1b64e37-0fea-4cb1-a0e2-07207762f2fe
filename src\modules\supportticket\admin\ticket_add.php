<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$row = [];
$error = $success = '';
$row['id'] = $nv_Request->get_int('id', 'post,get', 0);
if ($nv_Request->isset_request('submit', 'post')) {
    $row['title'] = $nv_Request->get_title('title', 'post', '');
    $row['content'] = $nv_Request->get_editor('content', '', NV_ALLOWED_HTML_TAGS);
    $row['cat_id'] = $nv_Request->get_int('cat_id', 'post', 0);
    $row['customer_id'] = $nv_Request->get_int('customer_id', 'post', 0);
    $row['vip_id'] = $nv_Request->get_int('vip_id', 'post', 0);
    $row['order_id'] = $nv_Request->get_int('order_id', 'post', 0);
    $row['label_ids'] = $nv_Request->get_array('label_ids', 'post', []);
    $row['status'] = TicketStatus::Open->value;
    $row['add_userid'] = $admin_info['userid'];
    $row['file_attach'] = $_FILES['file_attach'] ?? [];
    $row['prefix_lang'] = NV_LANG_DATA == 'en' ? 1 : 0;
    $row['send_notify'] = $nv_Request->get_int('send_notify', 'post', 0);

    $create = nv_local_api('CreateTicket', $row, $admin_info['username'], $module_name);
    $createTicket = json_decode($create, true);
    if ($createTicket['status'] == 'success') {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . ($createTicket['is_paid'] == 1 ? 'ticket_detail_paid' : 'ticket_detail') . '&id=' . $createTicket['ticket_id']);
    } else {
        $error = $createTicket['message'];
    }
} else {
    $row['id'] = 0;
    $row['title'] = '';
    $row['content'] = '';
    $row['cat_id'] = 0;
    $row['customer_id'] = 0;
    $row['vip_id'] = 0;
    $row['order_id'] = 0;
    $row['label_ids'] = [];
    $row['file_attach'] = '';
    $row['status'] = 0;
    $row['send_notify'] = 0;
}

$row['content'] = nv_htmlspecialchars($row['content']);
$row['file_attach'] = nv_htmlspecialchars($row['file_attach']);

// Tìm và chọn một khách hàng theo: name, username, email, sđt, mã số thuế
if ($nv_Request->get_title('ajax_get_customer', 'post', '') === NV_CHECK_SESSION) {
    $respon = [
        'results' => [],
        'pagination' => [
            'more' => false
        ]
    ];

    $q = $nv_Request->get_title('q', 'post', '');
    $page = $nv_Request->get_absint('page', 'post', 1);
    $lang_all = $nv_Request->get_absint('all', 'post', 0);

    if (!empty($q) && strlen($q) >= 3) {
        $fullname = $global_config['name_show'] == 0 ? "CONCAT(tb1.last_name, ' ', tb1.first_name)" : "CONCAT(tb1.first_name, ' ', tb1.last_name)";
        $dbkey = $db->dblikeescape($q);

        if (ctype_digit($dbkey)) {
            $query = 'SELECT tb1.userid, ' . $fullname . ' AS full_name, tb1.username
                FROM ' . NV_USERS_GLOBALTABLE . ' tb1
                WHERE tb1.username = ' . $db->quote($dbkey) . '
                UNION ALL
                SELECT tb1.userid, ' . $fullname . ' AS full_name, tb1.username
                FROM ' . NV_USERS_GLOBALTABLE . ' tb1
                LEFT JOIN ' . NV_USERS_GLOBALTABLE . '_info tb2 ON tb1.userid = tb2.userid
                WHERE tb2.phone = ' . $db->quote($dbkey) . '
                UNION ALL
                SELECT tb1.userid, ' . $fullname . ' AS full_name, tb1.username
                FROM ' . NV_USERS_GLOBALTABLE . ' tb1
                LEFT JOIN ' . NV_USERS_GLOBALTABLE . '_info tb2 ON tb1.userid = tb2.userid
                WHERE tb2.mst = ' . $db->quote($dbkey) . '
                ORDER BY userid ASC LIMIT 20';
        } elseif (empty(nv_check_valid_email($dbkey))) {
            $query = 'SELECT tb1.userid, ' . $fullname . ' AS full_name, tb1.username
                FROM ' . NV_USERS_GLOBALTABLE . ' tb1
                WHERE tb1.email = ' . $db->quote($dbkey) . '
                ORDER BY tb1.userid ASC LIMIT 1';
        } else {
            $query = 'SELECT tb1.userid, ' . $fullname . ' AS full_name, tb1.username
                FROM ' . NV_USERS_GLOBALTABLE . ' tb1
                WHERE tb1.username LIKE \'' . $dbkey . '%\'
                ORDER BY userid ASC LIMIT 50';
        }

        $result = $db->query($query);
        while ($row = $result->fetch()) {
            $respon['results'][] = [
                'id' => $row['userid'],
                'text' => $row['full_name'] . ' (' . $row['username'] . ')'
            ];
        }
    }
    nv_jsonOutput($respon);
}

if ($nv_Request->isset_request('getcatid', 'post')) {
    $cat_id = $nv_Request->get_int('cat_id', 'post', 0);
    $sql_query = $db->query('SELECT cat_id, is_customer FROM nv4_supportticket_cat WHERE cat_id=' . $cat_id)->fetch();
    nv_jsonOutput($sql_query);
}

if ($nv_Request->isset_request('getuser', 'post')) {
    $userid = $nv_Request->get_int('userid', 'post', 0);
    $vips = get_user_vips($userid);
    $orders = get_user_orders($userid);
    nv_jsonOutput([
        'vips'   => $vips,
        'orders' => $orders
    ]);
}

$tpl = new \NukeViet\Template\NVSmarty();
$tpl->setTemplateDir(NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$tpl->assign('LANG', $nv_Lang);
$tpl->assign('ACTION', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
$tpl->assign('OP', $op);
$tpl->assign('ROW', $row);
$tpl->assign('ATTACH_LIMITED', NV_ATTACH_LIMITED);
$tpl->assign('NV_UPLOAD_MAX_FILESIZE', NV_UPLOAD_MAX_FILESIZE);
$tpl->assign('ATTACH_LIMITED_MESSAGE', sprintf($nv_Lang->getModule('error_upload_limited'), NV_ATTACH_LIMITED));
$tpl->assign('LIMITED_FILE_SIZE', sprintf($nv_Lang->getModule('limited_file_size'), NV_ATTACH_LIMITED, nv_convertfromBytes(NV_UPLOAD_MAX_FILESIZE), NV_MAX_WIDTH, NV_MAX_HEIGHT));

if (!defined('NV_EDITOR')) {
    define('NV_EDITOR', 'ckeditor5-classic');
}

if (defined('NV_EDITOR')) {
    require_once NV_ROOTDIR . '/' . NV_EDITORSDIR . '/' . NV_EDITOR . '/nv.php';
}
if (nv_function_exists('nv_aleditor')) {
    $row['content'] = nv_aleditor('content', '100%', '300px', $row['content']);
    $tpl->assign('CONTENT', $row['content']);
}

foreach ($array_active_cats as $value) {
    $cat_options[] = [
        'key' => $value['cat_id'],
        'title' => $value['title_' . NV_LANG_DATA],
        'selected' => ($value['cat_id'] == $row['cat_id']) ? ' selected' : ''
    ];
}
$tpl->assign('CAT_OPTIONS', $cat_options);

foreach ($array_active_labels as $value) {
    $label_options[] = [
        'key' => $value['label_id'],
        'title' => $value['title_' . NV_LANG_DATA],
        'selected' => in_array($value['label_id'], $row['label_ids'])
    ];
}
$tpl->assign('LABEL_OPTIONS', $label_options);

$attach_files = decode_file($row['file_attach']);
$total_file = !empty($attach_files['name']) > 0 ? count($attach_files['name']) + 1 : 0;
$tpl->assign('TOTAL_FILE', $total_file);

for ($i = 0; $i < 2; ++$i) {
    $notify_options[] = [
        'key' => $i,
        'title' => $i == 0 ? $nv_Lang->getModule('send_notify_default') : $nv_Lang->getModule('send_notify_custom'),
        'checked' => $row['send_notify'] == $i ? ' checked' : ''
    ];
}
$tpl->assign('NOTIFY_OPTIONS', $notify_options);

$tpl->assign('ERROR', $error);
$tpl->assign('SUCCESS', $success);

$contents = $tpl->fetch('ticket_add.tpl');

$page_title = $nv_Lang->getModule('add');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
