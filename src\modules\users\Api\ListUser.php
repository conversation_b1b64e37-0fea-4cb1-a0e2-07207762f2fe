<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\users\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use NukeViet\Dauthau\Condition;
use NukeViet\Dauthau\Order;
use PDOException;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class ListUser implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'user';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $nv_Request, $db, $db_config, $nv_Lang;

            $module_name = Api::getModuleName();
            $module_info = Api::getModuleInfo();
            $module_data = $module_info['module_data'];
            $module_file = $module_info['module_file'];
            $admin_id = Api::getAdminId();

            $row = [];
            $error = '';
            $error_code = '';

            try {
                $page = $nv_Request->get_int('page', 'post', '1');
                $perpage = $nv_Request->get_int('perpage', 'post', '50');
                if ($page  <= 0 or $page > 9999) {
                    return $this->result->setError()
                    ->setCode('2005')
                    ->setMessage('Invalid page, should be more than 0')
                    ->getResult();
                }
                if ($perpage > 50 or $perpage <= 0) {
                    return $this->result->setError()
                        ->setCode('2002')
                        ->setMessage('Invalid perpage (should be less than 50 and more than 0)')
                        ->getResult();
                }

                /*
                * $where = [];
                * $where['AND'][] = [
                * '=' => [
                * 'user_id' => 8223,
                * ],
                * ];
                * $where['AND'][] = [
                * '=' => [
                * 'source_leads' => 1
                * ],
                * ];
                */

                $array_where = $nv_Request->get_array('where', 'post');
                if (!is_array($array_where)) {
                    return $this->result->setError()
                        ->setCode('2000')
                        ->setMessage('Param where must be an array')
                        ->getResult();
                }
                /*
                * $order = [];
                * $order['timecreate'] = 'DESC';
                * $order['id'] = 'ASC';
                */
                $array_order = $nv_Request->get_array('order', 'post');
                if (!is_array($array_order)) {
                    return $this->result->setError()
                        ->setCode('2003')
                        ->setMessage('Param order must be an array')
                        ->getResult();
                }

                $array_groups = $nv_Request->get_array('group', 'post');
                if (!is_array($array_groups)) {
                    return $this->result->setError()
                    ->setCode('2007')
                    ->setMessage('Param group must be an array')
                    ->getResult();
                }

                // key cho phép trong where
                $keys_check = [
                    'userid',
                    'username',
                    'email',
                ];

                // key cho phép hiển thị, cũng là key order
                $keys_view = [
                    'userid',
                    'username',
                    'email'
                ];

                $where = array();
                // $where[] = 'solicitor_code != ""';
                if (!empty($array_where)) {
                    // check $field
                    foreach ($array_where as $keys) {
                        foreach ($keys as $key) {
                            $operator = array_key_first($key);
                            $field = array_key_first($key[$operator]);
                            if (!in_array($field, $keys_check)) {
                                return $this->result->setError()
                                ->setCode('2001')
                                ->setMessage('Missing field ' . $field . ' in data')
                                ->getResult();
                            }
                        }
                    }

                    $condition = new Condition();
                    $where[] = $condition->toSqlString($array_where);
                }

                $order_string = 'userid DESC';
                if (!empty($array_order)) {
                    // check $field
                    foreach ($array_order as $field => $type) {
                        if (!in_array($field, $keys_view)) {
                            return $this->result->setError()
                            ->setCode('2004')
                            ->setMessage('Missing field ' . $field . ' in data order')
                            ->getResult();
                        }
                    }

                    $order = new Order();
                    $order_string = $order->toSqlString($array_order);
                }

                if (empty($error)) {
                    $arr_select = $keys_view;

                    $db->sqlreset()
                        ->select('COUNT(userid) as num')
                        ->from(NV_USERS_GLOBALTABLE);

                    if (!empty($where)) {
                        $db->where(implode(' AND ', $where));
                    }

                    $sth = $db->prepare($db->sql());
                    $sth->execute();
                    $total = $sth->fetchColumn();
                    $db->select(implode(',', $arr_select));
                    $db->order($order_string);
                    $db->limit($row['per_page'])->offset(($row['page'] - 1) * $row['per_page']);
                    $sth = $db->prepare($db->sql());
                    $sth->execute();

                    $arr_data = [];
                    while ($view = $sth->fetch()) {
                        $arr_data[$view['userid']] = $view;
                    }

                    // lấy thông tin bảng users_info
                    if (!empty($arr_data)) {
                        $db->sqlreset();
                        $db->select('*')->from('nv4_users_info')->where('userid IN (' . implode(',', array_column($arr_data, 'userid')) . ')');
                        $sth = $db->query($db->sql());
                        while ($view = $sth->fetch()) {
                            foreach(array_keys($view) as $key) {
                                $arr_data[$view['userid']][$key] = $view[$key];
                            }
                        }
                    }
                    if (!empty($arr_data)) {
                        $this->result->setSuccess();
                        $this->result->set('data', $arr_data);
                    } else {
                        $this->result->setSuccess()
                            ->setCode('4000')
                            ->setMessage($nv_Lang->getModule('api_user_not_exists'));
                    }
                }


            } catch (PDOException $e) {
                $this->result->setError();
                $this->result->setCode('3001');
                $this->result->setMessage(print_r($e, true));
                return $this->result->getResult();
            }

            return $this->result->getResult();
    }
}
