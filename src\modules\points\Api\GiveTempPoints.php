<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\points\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GiveTempPoints implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'point';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        // Các site thanh toán
        $sql = "SELECT * FROM " . $db_config['prefix'] . "_wallet_sites ORDER BY weight ASC";
        $global_array_sites = $nv_Cache->db($sql, 'id', 'wallet');

        $array = [];
        $array['type'] = $nv_Request->get_absint('type', 'post', 0);
        $array['site_id'] = $nv_Request->get_absint('site_id', 'post', 0);
        $array['userid'] = $nv_Request->get_absint('userid', 'post', 0);
        $array['givetime'] = $nv_Request->get_absint('givetime', 'post', 0) ?: NV_CURRENTTIME;

        $array['checkexists'] = $nv_Request->get_int('checkexists', 'post', -1);
        $array['checkuser'] = $nv_Request->get_absint('checkuser', 'post', 0) ?: $array['userid'];
        $array['pre_uid'] = $nv_Request->get_absint('pre_uid', 'post', 0);

        if (!isset($global_array_sites[$array['site_id']])) {
            return $this->result->setCode('1001')->setMessage($nv_Lang->getModule('api_err_site'))->getResult();
        }

        // Custom số điểm tặng
        $array['givepoints'] = $nv_Request->get_absint('givepoints', 'post', 0);
        $array['givemessage'] = html_entity_decode($nv_Request->get_textarea('givemessage', '', 'b,strong,em,i'));
        $array['givelog'] = html_entity_decode($nv_Request->get_title('givelog', 'post', ''));
        $array['giveexpired'] = $nv_Request->get_absint('giveexpired', 'post', 0);
        $array['link_detail'] = $nv_Request->get_string('link_detail', 'post', '');
        $array['key_crawl'] = $nv_Request->get_string('key_crawl', 'post', '');

        if ($array['type'] > 100) {
            if ($array['givepoints'] <= 0) {
                return $this->result->setCode('1001')->setMessage($nv_Lang->getModule('api_err_give1'))->getResult();
            }
            if (empty($array['givemessage'])) {
                return $this->result->setCode('1001')->setMessage($nv_Lang->getModule('api_err_give2'))->getResult();
            }
            if ($array['giveexpired'] <= 0) {
                return $this->result->setCode('1001')->setMessage($nv_Lang->getModule('api_err_give3'))->getResult();
            }
            if ($array['type'] == 104 || $array['type'] == 106 || $array['type'] == 107) {
                $array['checkexists'] = $array['type'];
            }
        }

        try {
            // Kiểm tra không tồn tại mới tặng hoặc tặng luôn
            $is_give = true;
            if ($array['checkexists'] > -1) {
                $sql = "SELECT id FROM " . $db_config['prefix'] . "_" . $module_data . "_users_tmp
                WHERE userid=" . $array['checkuser'] . " AND type=" . $array['type'];
                $is_give = $db->query($sql)->fetchColumn() ? false : true;
            }

            if ($is_give) {
                // Type = 8 là có sự thay đổi về bóc tin
                if ($array['type'] == 8) {
                    $getPoint = $db->query("SELECT config_value FROM " . $db_config['prefix'] . "_config WHERE config_name = 'point_crawl_plus'")->fetchcolumn();
                    $array['givepoints'] = $getPoint;
                }

                if (!empty($array['key_crawl'])) {
                    $arr = explode(':', $array['key_crawl']);
                    $title = strtolower(trim($arr[0]));
                    $array['key_crawl'] = $nv_Lang->getModule('title_note_' . $title) . trim($arr[1]);
                }
                // return json_encode($array, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);;

                $sql = "INSERT INTO " . $db_config['prefix'] . "_" . $module_data . "_users_tmp (
                    site_id, userid, pre_uid, type, status, addtime, givepoints, givemessage, givelog, giveexpired, link_detail, key_crawl
                ) VALUES (
                    " . $array['site_id'] . ", " . $array['userid'] . ",  " . $array['pre_uid'] . ", " . $array['type'] . ",
                    0, " . $array['givetime'] . ",
                    " . $array['givepoints'] . ", " . $db->quote($array['givemessage']) . ", " . $db->quote($array['givelog']) . ",
                    " . $array['giveexpired'] . ","
                    . $db->quote($array['link_detail']) . ","
                    . $db->quote($array['key_crawl']) ."
                )";
                $check = $db->exec($sql);
                if ($check) {
                    $this->result->set('data', 1);
                } else {
                    $this->result->set('data', 0);
                }
            } else {
                $this->result->set('data', 0);
            }
        } catch (\Exception $e) {
            return $this->result->setCode('1002')->setMessage($e->getMessage())->getResult();
        }

        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
