<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2023 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 27 Feb 2023 09:02:35 GMT
 */

if (!defined('NV_IS_FILE_SITEINFO')) {
    die('Stop!!!');
}

if (isset($data['content']) and $data['type'] == 'to_admin') {
    $data['title'] = sprintf($nv_Lang->getModule('notification_to_admin'), $data['content']['name']);
} elseif (isset($data['content']) and $data['type'] == 'to_caregiver') {
    $data['title'] = sprintf($nv_Lang->getModule('notification_to_caregiver'), $data['content']['name']);
} elseif (isset($data['content']) and $data['type'] == 'assignee_to_caregiver') {
    $data['title'] = sprintf($nv_Lang->getModule('notification_assignee_to_caregiver'), $data['content']['name']);
} elseif (isset($data['content']) and $data['type'] == 'unassignee') {
    $data['title'] = sprintf($nv_Lang->getModule('notification_unassignee'), $data['content']['name']);
} elseif (isset($data['content']) and $data['type'] == 'user_reply') {
    $data['title'] = sprintf($nv_Lang->getModule('notification_user_reply'), $data['content']['name']);
} elseif (isset($data['content']) and $data['type'] == 'payment_to_admin') {
    $data['title'] = sprintf($nv_Lang->getModule('notification_user_payment'), $data['content']['name'], $data['content']['ticket_id']);
} elseif (isset($data['content']) and $data['type'] == 'user_close') {
    $data['title'] = sprintf($nv_Lang->getModule('notification_user_close'), $data['content']['name']);
} elseif (isset($data['content']) and $data['type'] == 'user_reopen') {
    $data['title'] = sprintf($nv_Lang->getModule('notification_user_reopen'), $data['content']['name']);
} elseif (isset($data['content']) and $data['type'] == 'admin_process_refund') {
    $data['title'] = $nv_Lang->getModule('notification_to_expert');
} elseif (isset($data['content']) and $data['type'] == 'report_to_admin') {
    $data['title'] = sprintf($nv_Lang->getModule('notification_report_to_admin'), $data['content']['name'], $data['content']['ticket_id']);
} elseif (isset($data['content']) and $data['type'] == 'ideas_to_admin') {
    $data['title'] = sprintf($nv_Lang->getModule('notification_ideas_to_admin'), $data['content']['name'], $data['content']['ticket_id']);
} elseif (isset($data['content']) and $data['type'] == 'tagged_in_notes') {
    $data['title'] = sprintf($nv_Lang->getModule('notification_tagged_in_notes'), $data['content']['ticket_id']);
} elseif (isset($data['content']) and $data['type'] == 'assign_tagged_in_notes') {
    $data['title'] = sprintf($nv_Lang->getModule('notification_assignee_tagged_in_notes'), $data['content']['ticket_id']);
} else {
    $data['title'] = sprintf($nv_Lang->getModule('notification_to_admin'), $data['content']['name']);
}
$data['link'] = $data['content']['link'];
