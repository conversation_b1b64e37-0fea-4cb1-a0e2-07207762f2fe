<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2023 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 27 Feb 2023 09:02:35 GMT
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Module\supportticket\Log;

global $nv_Request, $db, $nv_Lang, $nv_Cache, $array_ticket_cats, $array_ticket_labels;
// Get content info
if ($nv_Request->isset_request('getinfo', 'post')) {
    $id = $nv_Request->get_int('id', 'post', '0');
    $data = get_ticket_by_id($id);
    nv_jsonOutput($data);
}

//Get user info
if ($nv_Request->isset_request('view_users', 'post,get')) {
    $user_id = $nv_Request->get_int('user_id', 'post', '0');
    $array_user = [];
    $sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . " as a INNER JOIN " . NV_USERS_GLOBALTABLE . "_info as b ON a.userid= b.userid WHERE a.userid = " . $db->quote($user_id);
    $result = $db->query($sql);
    while ($_row = $result->fetch()) {
        $array_user = $_row;
    }
    $xtpl = new XTemplate('convert_user.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('MODULE_UPLOAD', $module_upload);
    $xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
    $xtpl->assign('OP', $op);
    if (!empty($array_user)) {
        $array_user['title'] = nv_show_name_user($array_user['first_name'], $array_user['last_name'], $array_user['username']);
        $array_user['link_view'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=users&amp;' . NV_OP_VARIABLE . '=edit&amp;userid=' . $array_user['userid'] . '&check_admin=' . md5(NV_CHECK_SESSION . $array_user['userid']);
        $array_user['gender'] = $global_array_genders[$array_user['gender']]['title'];
        $array_user['regdate'] = nv_date('d/m/Y', $array_user['regdate']);
        $xtpl->assign('USERS', $array_user);
        $xtpl->parse('main.user');
    }
    $xtpl->parse('main');
    $contents = $xtpl->text('main');
    die($contents);
}

// Edit detail
if ($nv_Request->isset_request('updatedetail', 'post')) {
    $ticket_id = $nv_Request->get_int('ticket_id', 'post', 0);
    $data = [
        'title' => nv_substr($nv_Request->get_title('title', 'post', ''), 0, 249),
        'content' => $nv_Request->get_editor('content', '', NV_ALLOWED_HTML_TAGS)
    ];
    $params_update = [
        'ticket_id' => $ticket_id,
        'admin_id' => $admin_info['userid'],
        'data' => $data,
    ];
    $update = nv_local_api('UpdateTicket', $params_update, $admin_info['username'], $module_name);
    $updateDetail = json_decode($update, true);

    nv_jsonOutput($updateDetail);
}

// Edit point
if ($nv_Request->isset_request('updatepoint', 'post')) {
    $ticket_id = $nv_Request->get_int('ticket_id', 'post', 0);
    $point_price = $nv_Request->get_absint('point_price', 'post', 0);
    if ($point_price > 1000000) {
        nv_jsonOutput([
            'status' => 'error',
            'message' => $nv_Lang->getModule('point_limited')
        ]);
    }
    $data = [
        'point_price' => $point_price,
        'status' => $point_price > 0 ? 5 : 4,
        'payment_status' => $point_price >= $array_ticket_cats[$data['cat_id']]['point_price'] ? 2 : 3,
        'payment_time' => $point_price > 0 ? NV_CURRENTTIME : 0,
    ];
    $params_update = [
        'ticket_id' => $ticket_id,
        'admin_id' => $admin_info['userid'],
        'data' => $data,
    ];
    $update = nv_local_api('UpdateTicket', $params_update, $admin_info['username'], $module_name);
    $updateDetail = json_decode($update, true);

    //Gửi mail thông báo cho khách hàng vào thanh toán
    if ($updateDetail['status'] == 'success' && $point_price > 0) {
        $params = [
            'id'        =>  $ticket_id,
            'userid'    =>  $admin_info['userid']
        ];
        // GỌI API
        $ticket = nv_local_api('GetTicket', $params, $admin_info['username'], $module_name);
        $ticket = json_decode($ticket, true);
        payment_notification_to_customer($ticket['data']);
    }
    nv_jsonOutput($updateDetail);
}

// Edit option
if ($nv_Request->isset_request('updateoption', 'post')) {
    $ticket_id = $nv_Request->get_int('ticket_id', 'post', 0);
    $vip = $nv_Request->get_int('vip', 'post', 0);
    $order = $nv_Request->get_int('order', 'post', 0);
    $label = $nv_Request->get_typed_array('label', 'post', 'int', []);
    $status = $nv_Request->get_int('status', 'post', 0);
    $notify = $nv_Request->get_int('notify', 'post', 0);
    $data = [
        'vip_id' => $vip,
        'order_id' => $order,
        'label_ids' => implode(',', $label),
        'assignee_to' => '',
        'status' => $status,
        'notify' => $notify,
    ];
    //Ticket đang ở trạng thái nháp mới được phép đổi bộ phận
    $cat = $nv_Request->get_int('cat', 'post', 0);
    $data['cat_id'] = $cat;
    $ticket = get_ticket_by_id($ticket_id);
    if (!empty($ticket) && $status == TicketStatus::Open->value) {
        if ($cat != $ticket['data']['cat_id']) {
            if (!empty($array_ticket_cats[$cat]) && $array_ticket_cats[$cat]['is_point'] == 1) {
                $data['is_paid'] = 1;
                $data['ask_expert'] = 1;
            } else {
                $data['is_paid'] = 0;
                $data['ask_expert'] = 0;
            }
        }
    }
    if ($status == TicketStatus::Close->value) {
        $data['status_client'] = TicketStatus::Close->value;
    }
    $params_update = [
        'ticket_id' => $ticket_id,
        'admin_id' => $admin_info['userid'],
        'data' => $data,
    ];
    $update = nv_local_api('UpdateTicket', $params_update, $admin_info['username'], $module_name);
    $updateDetail = json_decode($update, true);

    if ($data['status'] == TicketStatus::Close->value) {
        close_notification_to_user($ticket['data'], true);
    }

    nv_jsonOutput($updateDetail);
}

//Chi tiết ticket
$page_title = $nv_Lang->getModule('detail');
$row = [];
$row['id'] = $nv_Request->get_int('id', 'post,get', 0);
$per_page = 50;
$page = $nv_Request->get_int('page', 'post,get', 1);
$redirect = 0;
$array_related_status = $array_ticket_status;
if ($row['id'] > 0) {
    $ticket = get_ticket_by_id($row['id']);
    if (empty($ticket)) {
        $redirect = 1;
    } else {
        if ($ticket['canView']) {
            $row = $ticket['data'];
        } else {
            $redirect = 1;
        }
    }
} else {
    $redirect = 1;
}

if ($redirect == 1) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=tickets');
}

// Change invalid status
if ($nv_Request->isset_request('change_invalid', 'post, get')) {
    $comment_id = $nv_Request->get_int('comment_id', 'post, get', 0);
    $content = 'NO_' . $comment_id . '_' . $row['ask_expert'];

    $query = 'SELECT content,invalid FROM ' . TB_TICKET_LOG . ' WHERE log_id=' . $comment_id;
    $comment = $db->query($query)->fetch();
    if (isset($comment['invalid'])) {
        $invalid = ($comment['invalid']) ? 0 : 1;
        $query = 'UPDATE ' . TB_TICKET_LOG . ' SET invalid=' . intval($invalid) . ' WHERE log_id=' . $comment_id;
        $db->query($query);
        $current_user = get_user_info($admin_info['userid']);
        $log_data = [
            LogKey::MarkInvalid->getLabel(),
            [
                $nv_Lang->getModule('log_user'),
                $current_user['fullname'],
            ],
            [
                $nv_Lang->getModule('log_data'),
                $comment['content'],
            ],
            [
                $nv_Lang->getModule('comment_status_invalid'),
                $comment['invalid'] . ' => ' . $invalid,
            ]
        ];

        add_ticket_logs($admin_info['userid'], LogKey::MarkInvalid->value, $log_data, $row['id']);
        $content = 'OK_' . $comment_id . '_' . $row['ask_expert'];
    }
    $nv_Cache->delMod($module_name);
    include NV_ROOTDIR . '/includes/header.php';
    echo $content;
    include NV_ROOTDIR . '/includes/footer.php';
}

// Chuyển hướng khi là ticket miễn phí
if ($row['is_paid'] == 0) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail&amp;id=' . $row['id']);
}

$comment = $error = [];
//Kiểm tra trước khi edit comment
$commentid = $nv_Request->get_int('commentid', 'post,get', 0);
if ($commentid > 0) {
    $params = [
        'ticket_id' =>  $row['id'],
        'log_id'    =>  $commentid,
        'userid'    =>  $admin_info['userid']
    ];
    // GỌI API
    $comment_check = nv_local_api('GetComment', $params, $admin_info['username'], $module_name);
    $comment_check = json_decode($comment_check, true);
    $comment_detail = $comment_check['data'];
    $comment_detail['file_attach'] = decode_file($comment_detail['file_attach']);
}

// Lấy thông tin điểm đã được sử dụng để xem câu trả l
$params = [
    'ticket_id' => $row['id'],
    'customer_id' => $row['customer_id']
];
$ticket_points = nv_local_api('GetTotalPointInComment', $params, $admin_info['username'], $module_name);
$ticket_points = json_decode($ticket_points, true);

//Lấy danh sách comment
$params = [
    'ticket_id' =>  $row['id'],
    'is_paid' => $row['is_paid'],
    'page' => $page,
    'perpage' => $per_page
];
$comments = nv_local_api('ListAllComment', $params, $admin_info['username'], $module_name);
$comments = json_decode($comments, true);

// Lấy thông tin điểm của khách hàng
if (!class_exists('nukeviet_points')) {
    require_once NV_ROOTDIR . '/modules/points/points.class.php';
}
$nv_points = new nukeviet_points();
$customs_points = $nv_points->my_point($row['customer_id']);

// Render form trả lời và trả lời bổ sung
if ($nv_Request->isset_request('render_form_expert_comment', 'post') == NV_CHECK_SESSION && $ticket['canReply']) {
    $comment_parent_id = $nv_Request->get_int('comment_parent_id', 'post', 0);
    $comment_parent_type = $nv_Request->get_int('comment_parent_type', 'post', 0);

    nv_jsonOutput([
        'success' => true,
        'data' => [
            'html' => get_form_comment_additional($comment_parent_id, $comment_parent_type, $row['min_point_expert']),
            'comment_id' => $comment_parent_id,
        ],
    ]);
}

// Xử lý trả lời và trả lời bổ sung
if ($nv_Request->isset_request('comment_token', 'post') == NV_CHECK_SESSION && $ticket['canReply']) {
    $add_comment['parent'] = $nv_Request->get_int('comment_parent_id', 'post', 0);
    $add_comment['comment_type'] = $nv_Request->get_int('comment_type', 'post', 0);
    $add_comment['point_quote'] = $nv_Request->get_int('point_quote', 'post', 0);
    $add_comment['content'] = $nv_Request->get_editor('comment_reply', '', NV_ALLOWED_HTML_TAGS);
    $add_comment['ticket_id'] = $row['id'];
    $add_comment['area'] = 1;
    $add_comment['reply_userid'] = $admin_info['userid'];
    $add_comment['display_userid'] = $admin_info['userid'];
    $add_comment['edit_time'] = NV_CURRENTTIME;
    $add_comment['point_offer'] = $row['offer_point_expert'];
    $add_comment['status'] = CommentStatus::Done->value;

    // Nếu là comment bổ sung cho câu trả lời => Không tính điểm
    $add_comment['payment_status'] = $add_comment['point_quote'] > 0 ? PaymentStatus::Process->value : PaymentStatus::Done->value;

    $create = nv_local_api('CreateComment', $add_comment, $admin_info['username'], $module_name);
    $createComment = json_decode($create, true);
    if ($createComment['status'] == 'success') {
        // Cập nhật trạng thái gốc của ticket
        $current_status = check_current_status($row);
        update_ticket_activity($row['id'], $current_status, $current_status, $admin_info['userid']);
        // Cập nhật status của yêu cầu bổ sung khi chuyên gia trả lời
        if ($add_comment['comment_type'] == CommentType::Expert->value) {
            $db->query('UPDATE ' . TB_TICKET_LOG . ' SET status= ' . CommentStatus::Done->value . ' WHERE log_id=' . $add_comment['parent'])->fetch();
        }
        // Cập nhật ghi log
        if ($add_comment['comment_type'] == CommentType::Expert->value) {
            $log_key = LogKey::ExpertReply->value;
            $log_label = LogKey::ExpertReply->getLabel();
        } else if ($add_comment['comment_type'] == CommentType::ExpertAdditionalForAI->value) {
            $log_key = LogKey::ExpertAddtionalForAI->value;
            $log_label = Logkey::ExpertAddtionalForAI->getLabel();
        } else {
            $log_key = LogKey::ExpertAddtional->value;
            $log_label = Logkey::ExpertAddtional->getLabel();
        }

        $log_data = [
            $log_label,
            [
                $nv_Lang->getModule('role_expert'),
                nv_show_name_user($array_admin_users[$admin_info['userid']]['first_name'], $array_admin_users[$admin_info['userid']]['last_name'], $array_admin_users[$admin_info['userid']]['username'])
            ],
            [
                $nv_Lang->getModule('log_data'),
                $add_comment['content'],
            ]
        ];
        if ($add_comment['point_quote'] > 0) {
            $log_data[] = [
                $nv_Lang->getModule('point_number'),
                $add_comment['point_quote'],
            ];
        }
        add_ticket_logs($admin_info['userid'], $log_key, $log_data, $row['id'], 1);

        nv_jsonOutput([
            'success' => true,
            'message' => $nv_Lang->getModule('success_log_add'),
        ]);
    } else {
        nv_jsonOutput([
            'success' => false,
            'message' => $nv_Lang->getModule('error_reply_by_expert'),
        ]);
    }
}

// Chuyên gia/Admin trả lời yêu cầu của ticket parent = 0
if ($nv_Request->isset_request('commentSubmit', 'post') and $ticket['canReply']) {
    $comment['ticket_id'] = $row['id'];
    $comment['reply_userid'] = $admin_info['userid'];
    $comment['display_userid'] = $admin_info['userid'];
    $comment['area'] = 1;
    $comment['content'] = $nv_Request->get_editor('commentContent', '', NV_ALLOWED_HTML_TAGS);
    $comment['file_attach'] = $nv_Request->get_array('file_attach', 'post', []);
    $comment['status'] = CommentStatus::Done->value;
    $comment['point_quote'] = $nv_Request->get_int('point_quote', 'post', 0);
    $comment['point_offer'] = $row['offer_point_expert'];
    $comment['comment_type'] = CommentType::Expert->value;

    // Kiểm tra điểm
    $comment['payment_status'] = $comment['point_quote'] > 0 ? PaymentStatus::Process->value : PaymentStatus::Done->value;

    // Kiểm tra không có nội dung
    if (empty($comment['content'])) {
        $error = $nv_Lang->getModule('error_required_content');
    }

    // Kiểm tra Chuyên gia đã comment hay chưa -> Nếu đã comment rồi thì k cho comment nữa
    $sql = 'SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $row['id'] . ' AND parent=0 AND comment_type=' . CommentType::Expert->value . ' AND reply_userid=' . $admin_info['userid'];
    $check_expert_comment = $db->query($sql)->fetch();
    if (!empty($check_expert_comment)) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail_paid&id=' . $row['id']);
    }

    $file_attach = [];
    foreach ($comment['file_attach'] as $file) {
        if (nv_is_file($file, NV_UPLOADS_DIR . '/' . $module_upload)) {
            $file = substr($file, strlen(NV_BASE_SITEURL . NV_UPLOADS_DIR . '/' . $module_upload . '/'));
        } else {
            $error = $nv_Lang->getModule('error_required_file');
            $file = '';
        }
        $file_attach[] = $file;
    }
    $comment['file_attach'] = $file_attach;

    if (empty($error)) {
        $create = nv_local_api('CreateComment', $comment, $admin_info['username'], $module_name);
        $createComment = json_decode($create, true);
        if ($createComment['status'] == 'success') {
            // Cập nhật trạng thái gốc của ticket
            $current_status = check_current_status($row);
            update_ticket_activity($row['id'], $current_status, $current_status, $admin_info['userid']);
            // Lưu log cập nhật ticket
            $log_data = [
                Logkey::ExpertReply->getLabel(),
                [
                    $nv_Lang->getModule('role_expert'),
                    nv_show_name_user($array_admin_users[$admin_info['userid']]['first_name'], $array_admin_users[$admin_info['userid']]['last_name'], $array_admin_users[$admin_info['userid']]['username'])
                ],
                [
                    $nv_Lang->getModule('log_data'),
                    $comment['content'],
                ],
                [
                    $nv_Lang->getModule('point_number'),
                    $comment['point_quote'],
                ]
            ];
            add_ticket_logs($admin_info['userid'], LogKey::ExpertReply->value, $log_data, $row['id'], 1);

            nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail_paid&id=' . $row['id']);
        } else {
            $error = $createComment['message'];
        }
    }
}

// Hiển thị form hoàn điểm
if ($nv_Request->isset_request('refund_form_token', 'post') == NV_CHECK_SESSION) {
    $comment_id = $nv_Request->get_int('comment_id', 'post', 0);

    if ($row['status'] == TicketStatus::Close->value) {
        nv_jsonOutput([
            'success' => false,
            'message' => $nv_Lang->getModule('waring_ticket_close_cannot_refund'),
        ]);
    }

    $comment = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $row['id'] . ' AND log_id=' . $comment_id . ' AND status=' . CommentStatus::Done->value . ' AND comment_type IN (' . CommentType::AI->value . ',' . CommentType::Expert->value . ') AND refund_status=' . RefundStatus::Open->value)->fetch();
    if (!$comment) {
        nv_jsonOutput([
            'success' => false,
            'message' => $nv_Lang->getModule('message_error_refund'),
        ]);
    }

    // Kiểm tra thời gian xử lý yêu cầu hoàn điểm
    $time_allowed_refund = (86400 * $module_config[$module_name]['return_point']) + $comment['rating_add_time'];
    if ($comment && $time_allowed_refund < NV_CURRENTTIME) {
        nv_jsonOutput([
            'success' => false,
            'message' => $nv_Lang->getModule('error_of_refund_over_time'),
        ]);
    }

    if ($comment) {
        nv_jsonOutput([
            'success' => true,
            'data' => [
                'html' => get_refund_form($comment, $rating),
                'comment_id' => $comment_id,
            ],
        ]);
    }
}

// Xử lý hoàn điểm
if ($nv_Request->isset_request('refund_token', 'post') == NV_CHECK_SESSION) {
    $refunds['status'] = $nv_Request->get_int('refund_status', 'post', 0);
    $refunds['log_id'] = $nv_Request->get_int('comment_refund_id', 'post', 0);
    $refunds['rating_reply'] = $nv_Request->get_editor('rating_reply', '', NV_ALLOWED_HTML_TAGS);
    $refunds['comment_type'] = $nv_Request->get_int('reply_role', 'post', 0);

    if ($row['status'] == TicketStatus::Close->value) {
        nv_jsonOutput([
            'success' => false,
            'message' => $nv_Lang->getModule('waring_ticket_close_cannot_refund'),
        ]);
    }

    if (!in_array($refunds['status'], [RefundStatus::Accept->value, RefundStatus::Refuse->value])) {
        if (empty($result)) {
            nv_jsonOutput([
                'success' => false,
                'message' => $nv_Lang->getModule('message_success_refund_refuse'),
            ]);
        }
    }

    $sql = 'SELECT * FROM ' . TB_TICKET_LOG . ' WHERE log_id=' . $refunds['log_id'] . ' AND payment_status=' . PaymentStatus::Done->value . ' AND status=' . CommentStatus::Done->value . ' AND refund_status=' . RefundStatus::Open->value . ' AND ticket_id=' . $row['id'] . ' AND rating_number < 3';
    $result = $db->query($sql)->fetch();

    if (empty($result)) {
        nv_jsonOutput([
            'success' => false,
            'message' => $nv_Lang->getModule('message_error_refund'),
        ]);
    }

    // Kiểm tra thời gian gửi câu trả lời
    $time_allowed_refund = (86400 * $module_config[$module_name]['return_point']) + $result['rating_add_time'];
    if (!empty($result) && $time_allowed_refund < NV_CURRENTTIME) {
        nv_jsonOutput([
            'success' => false,
            'message' => $nv_Lang->getModule('error_of_refund_over_time'),
        ]);
    }

    // Nếu câu trả lời của AI/Expert đang xét point_final=0 thì không xử lý hoàn điểm
    if (!empty($result) && $result['point_final'] <= 0) {
        nv_jsonOutput([
            'success' => false,
            'message' => $nv_Lang->getModule('message_error_refund_point'),
        ]);
    }

    if (!empty($result)) {
        // Cập nhật log khi admin phản hồi đánh giá
        $log_data = [
            LogKey::AdminReplyReview->getLabel(),
            [
                $result['comment_type'] == CommentType::AI->value ? $nv_Lang->getModule('role_ai') : $nv_Lang->getModule('role_expert'),
                $result['comment_type'] == CommentType::AI->value ? '' : nv_show_name_user($array_admin_users[$result['reply_userid']]['first_name'], $array_admin_users[$result['reply_userid']]['last_name'], $array_admin_users[$result['reply_userid']]['username'])
            ],
            [
                $nv_Lang->getModule('status'),
                $nv_Lang->getModule($refunds['status'] == RefundStatus::Refuse->value ? 'refund_refuse' : 'refund_accept'),
            ],
            [
                $nv_Lang->getModule('log_data'),
                $refunds['rating_reply'],
            ]
        ];
        if ($refunds['status'] == RefundStatus::Accept->value && $result['point_final'] > 0) {
            $log_data[] = [
                $nv_Lang->getModule('point_number'),
                $result['point_final'],
            ];
            // Xử lý hoàn điểm
            $message_log = [];
            if ($result['parent'] == 0) {
                $nv_Lang->changeLang('vi');
                $nv_Lang->loadModule($module_file, false, true);
                $message_log['vi'] = $result['comment_type'] == CommentType::AI->value ? $nv_Lang->getModule('log_payment_refund_ai', $row['id']) : $nv_Lang->getModule('log_payment_refund_expert', get_user_info($result['reply_userid'])['fullname'], $row['id']);
                $nv_Lang->changeLang('en');
                $nv_Lang->loadModule($module_file, false, true);
                $message_log['en'] = $result['comment_type'] == CommentType::AI->value ? $nv_Lang->getModule('log_payment_refund_ai', $row['id']) : $nv_Lang->getModule('log_payment_refund_expert', get_user_info($result['reply_userid'])['fullname'], $row['id']);
                $nv_Lang->changeLang(NV_LANG_INTERFACE);
            } else {
                $nv_Lang->changeLang('vi');
                $nv_Lang->loadModule($module_file, false, true);
                $message_log['vi'] = $result['comment_type'] == CommentType::AI->value ? $nv_Lang->getModule('log_payment_refund_ai_additional', $result['parent']) : $nv_Lang->getModule('log_payment_refund_expert_additional', get_user_info($result['reply_userid'])['fullname'], $result['parent']);
                $nv_Lang->changeLang('en');
                $nv_Lang->loadModule($module_file, false, true);
                $message_log['en'] = $result['comment_type'] == CommentType::AI->value ? $nv_Lang->getModule('log_payment_refund_ai_additional', $result['parent']) : $nv_Lang->getModule('log_payment_refund_expert_additional', get_user_info($result['reply_userid'])['fullname'], $result['parent']);
                $nv_Lang->changeLang(NV_LANG_INTERFACE);
            }
            $message = json_encode($message_log);
            // Nếu yêu cầu AI hoàn điểm thì hoàn gấp đôi
            if ($result['comment_type'] == CommentType::AI->value) {
                // Xử lý trường hợp nếu câu trả lời của AI chưa có điểm point_final thì lấy điểm off để làm
                if ($result['point_final'] == 0) {
                    $result['point_final'] = $row['min_point_ai'];
                }
                $result['point_refund'] = $result['point_final'] * 2;
            } else {
                $result['point_refund'] = $result['point_final'];
            }
            $update_point = $nv_points->update(0, $result['point_refund'], $row['customer_id'], $message, true);
            if ($nv_points->isError()) {
                nv_jsonOutput([
                    'success' => false,
                    'message' => $update_point,
                ]);
            }
            // Cập nhật point-logs
            add_point_logs($row['customer_id'], $row['id'], $result['log_id'], $result['point_refund'], 1, $message);
        }
        // Cập nhật status của comment
        $db->query('UPDATE ' . TB_TICKET_LOG . ' SET refund_status= ' . $refunds['status'] . ' , rating_reply=' . $db->quote($refunds['rating_reply']) . ', reply_add_time=' . NV_CURRENTTIME . ($refunds['status'] == RefundStatus::Accept->value ? ', point_refund=' . $result['point_refund'] . ', status=' . CommentStatus::Refund->value : '') . ' WHERE log_id=' . $result['log_id'])->fetch();
        // Cập nhật trạng thái gốc của ticket
        $current_status = check_current_status($row);
        update_ticket_activity($row['id'], $current_status, $current_status, $admin_info['userid']);

        add_ticket_logs($admin_info['userid'], LogKey::AdminReplyReview->value, $log_data, $row['id'], 1);
        // Gửi mail tới khách hàng
        $refund_status = $refunds['status'] == RefundStatus::Refuse->value ? RefundStatus::Refuse : RefundStatus::Accept;
        $result['rating_reply'] = $refunds['rating_reply'];
        notification_refund_to_customer($row, $result, $refund_status);
        // Thông báo hoàn điểm thành công
        nv_jsonOutput([
            'success' => true,
            'message' => $nv_Lang->getModule('message_success_refund_accept'),
        ]);
    }
}

/**
 * Xử lý chat nội bộ
 * - Lấy thông tin chat
 * - Gửi tin nhắn chat
 * - Các file đính kèm khi trao đổi sẽ được lưu trong thư mục uploads/supportticket/chat/{ticket_id}
 * - Lấy thông tin admin
 */
if ($nv_Request->isset_request('get_admin_user', 'post')) {
    $optionData = [];
    $_sql_cat = 'SELECT userid FROM ' . TB_TICKET_CATADMIN . ' WHERE cat_id= ' . $row['cat_id'];
    $_result = $db->query($_sql_cat)->fetchAll(PDO::FETCH_COLUMN);

    foreach ($_result as $admin_userid) {
        $cat_admin_info = get_user_info($admin_userid);

        if (!empty($cat_admin_info)) {
            $fullname = nv_show_name_user($cat_admin_info['first_name'], $cat_admin_info['last_name'], $cat_admin_info['username']);

            $optionData[] = [
                'username' => $cat_admin_info['username'],
                'fullname' => $fullname,
            ];
        }
    }

    nv_jsonOutput([
        'success' => true,
        'data' => $optionData
    ]);
}

if ($nv_Request->isset_request('get_chat_messages', 'post')) {
    $ticket_id = $nv_Request->get_int('ticket_id', 'post', 0);

    // Get chat messages
    $sql = "SELECT * FROM " . TB_TICKET_CHAT_PRIVATE . "
            WHERE ticket_id=" . $ticket_id . "
            ORDER BY created_time ASC";

    $messages = $db->query($sql)->fetchAll();
    $result = [];

    foreach ($messages as $msg) {
        $file_info = [];
        if (!empty($msg['file_path'])) {
            $file_path = NV_BASE_SITEURL . NV_UPLOADS_DIR . '/' . $module_upload . '/chat/' . $msg['file_path'];
            $file_ext = nv_getextension($msg['file_path']);
            $is_image = in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif', 'webp']);

            $file_info = [
                'path' => $file_path,
                'name' => basename($msg['file_path']),
                'is_image' => $is_image
            ];
        }

        $get_user_info = get_user_info($msg['admin_id']);
        $get_user_info['fullname'] = empty($get_user_info) ? '' : nv_show_name_user($get_user_info['first_name'], $get_user_info['last_name'], $get_user_info['username']);

        $result[] = [
            'id' => $msg['id'],
            'content' => $msg['content'],
            'admin_chat' => $msg['admin_id'] == $admin_info['userid'] ? 'my-message' : 'other-message',
            'file' => $file_info,
            'created_time' => nv_date('H:i d/m/Y', $msg['created_time']),
            'admin_name' => $get_user_info['fullname']
        ];
    }

    nv_jsonOutput([
        'success' => true,
        'messages' => $result
    ]);
}

if ($nv_Request->isset_request('send_chat', 'post')) {
    $chat = [];
    $chat['ticket_id'] = $nv_Request->get_int('ticket_id', 'post', 0);
    $chat['content'] = $nv_Request->get_textarea('content', '', NV_ALLOWED_HTML_TAGS);
    $chat['admin_id'] = $admin_info['userid'];
    $chat['created_time'] = NV_CURRENTTIME;
    $chat['file_path'] = '';

    if (empty($chat['ticket_id']) || empty($chat['content'])) {
        nv_jsonOutput([
            'success' => false,
            'message' => 'Không thấy dữ liệu'
        ]);
    }

    // Xử lý upload file
    $uploaded_file_path = null;
    if (isset($_FILES['file']) && !empty($_FILES['file']['name'])) {

        // Kiểm tra và tạo thư mục gốc
        $base = NV_UPLOADS_REAL_DIR . '/' . $module_name;
        if (!is_dir($base)) {
            nv_mkdir(NV_UPLOADS_REAL_DIR, $module_name);
            if (!is_writable($base)) {
                @chmod($base, 0777);
            }
        }

        // Tạo thư mục chat trong thư mục module
        $base_chat = $base . '/chat';
        if (!is_dir($base_chat)) {
            nv_mkdir($base, 'chat');
            if (!is_writable($base_chat)) {
                @chmod($base_chat, 0777);
            }
        }

        // Tạo thư mục theo ticket_id
        $fullPath = $base_chat . '/' . $chat['ticket_id'];
        if (!is_dir($fullPath)) {
            nv_mkdir($base_chat, $chat['ticket_id']);
            if (!is_writable($fullPath)) {
                @chmod($fullPath, 0777);
            }
        }

        // Định nghĩa các loại file được phép
        $allowed_extensions = ['images', 'documents', 'adobe'];
        $upload = new NukeViet\Files\Upload(
            $allowed_extensions,
            $global_config['forbid_extensions'],
            $global_config['forbid_mimes']
        );
        $upload->setLanguage($lang_global);
        $upload_info = $upload->save_file($_FILES['file'], $fullPath, false);

        if (empty($upload_info['error'])) {
            $chat['file_path'] = $chat['ticket_id'] . '/' . $upload_info['basename'];
            $uploaded_file_path = $fullPath . '/' . $upload_info['basename'];
        } else {
            nv_jsonOutput([
                'success' => false,
                'message' => $upload_info['error']
            ]);
        }
    }

    $stmt = $db->prepare('INSERT INTO ' . TB_TICKET_CHAT_PRIVATE . '
        (ticket_id, admin_id, content, file_path, created_time)
    VALUES
        (:ticket_id, :admin_id, :content, :file_path, :created_time)');

    $stmt->bindParam(':ticket_id', $chat['ticket_id'], PDO::PARAM_INT);
    $stmt->bindParam(':admin_id', $chat['admin_id'], PDO::PARAM_INT);
    $stmt->bindParam(':content', $chat['content'], PDO::PARAM_STR, strlen($chat['content']));
    $stmt->bindParam(':file_path', $chat['file_path'], PDO::PARAM_STR);
    $stmt->bindParam(':created_time', $chat['created_time'], PDO::PARAM_INT);

    $execute_result = $stmt->execute();

    if ($execute_result) {
        // Kiểm tra các tag @username trong nội dung
        preg_match_all('/@(\w+)/', $chat['content'], $matches);
        $tagged_usernames = $matches[1];

        // Gửi thông báo cho các user được tag
        if (!empty($tagged_usernames)) {
            foreach ($tagged_usernames as $username) {
                // Lấy user id từ username
                $sql = "SELECT userid FROM " . NV_USERS_GLOBALTABLE . " WHERE username=:username";
                $stmt = $db->prepare($sql);
                $stmt->bindParam(':username', $username, PDO::PARAM_STR);
                $stmt->execute();
                $tagged_user = $stmt->fetch();

                if ($tagged_user) {
                    $ticket_link = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=ticket_detail&amp;id=' . $chat['ticket_id'];

                    // Gửi thông báo
                    nv_insert_notification($module_name, 'tagged_in_notes', array(
                        'link' => $ticket_link,
                        'ticket_id' => $chat['ticket_id']
                    ), $chat['ticket_id'], $tagged_user['userid'], 0, 1,  0);
                }
            }
        }

        // Gửi thông báo tới người phụ trách ticket khi có trao đổi
        if (!empty($row['assignee_to'])) {
            $array_assignee = explode(',', $row['assignee_to']);
            foreach ($array_assignee as $key => $assignee_id) {
                $ticket_link = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=ticket_detail&amp;id=' . $chat['ticket_id'];

                // Gửi thông báo
                nv_insert_notification($module_name, 'assign_tagged_in_notes', array(
                    'link' => $ticket_link,
                    'ticket_id' => $chat['ticket_id']
                ), $chat['ticket_id'], $assignee_id, 0, 1, 0);
            }
        }

        nv_jsonOutput([
            'success' => true,
            'message' => 'Thành công'
        ]);
    } else {
        // Xóa file đã tải lên
        if ($uploaded_file_path !== null && file_exists($uploaded_file_path)) {
            @unlink($uploaded_file_path);
        }

        $error_info = $stmt->errorInfo();
        $error_message = 'Đã xảy ra lỗi khi lưu dữ liệu';
        if (!empty($error_info[2])) {
            $error_message .= ': ' . $error_info[2];
        }

        nv_jsonOutput([
            'success' => false,
            'message' => $error_message
        ]);
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('UPLOADS_DIR_USER', NV_UPLOADS_DIR . '/' . $module_upload);
$xtpl->assign('TOKEN', NV_CHECK_SESSION);
$xtpl->assign('OP', $op);
$xtpl->assign('ATTACH_LIMITED', NV_ATTACH_LIMITED);
$xtpl->assign('ATTACH_LIMITED_MESSAGE', sprintf($nv_Lang->getModule('error_upload_limited'), NV_ATTACH_LIMITED));
$xtpl->assign('LIMITED_FILE_SIZE', sprintf($nv_Lang->getModule('limited_file_size'), NV_ATTACH_LIMITED, nv_convertfromBytes(NV_UPLOAD_MAX_FILESIZE), NV_MAX_WIDTH, NV_MAX_HEIGHT));
$xtpl->assign('FORM_ACTION', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $row['id']);
$xtpl->assign('EDIT_COMMENT', (isset($comment_check) && $comment_check['status'] == 'success' &&  $comment_check['canEdit']) ? $commentid : 0);
$xtpl->assign('MIN_POINT_EXPERT', $row['min_point_expert']);

$row['add_time'] = $row['add_time'] > 0 ? nv_date('H:i d/m/Y', $row['add_time']) : '';
if ($row['edit_time'] > 0) {
    $row['edit_time'] = nv_date('H:i d/m/Y', $row['edit_time']);
    $xtpl->assign('EDIT_TIME', $row['edit_time']);
    $xtpl->parse('main.show_edit_time');
}
$row['pay_status'] = PaymentStatus::tryFrom($row['payment_status'])?->getLabel();
$xtpl->assign('ROW', $row);

if (!defined('NV_EDITOR')) {
    define('NV_EDITOR', 'ckeditor5-classic');
}

if (defined('NV_EDITOR')) {
    require_once NV_ROOTDIR . '/' . NV_EDITORSDIR . '/' . NV_EDITOR . '/nv.php';
}
if (nv_function_exists('nv_aleditor')) {
    $comment_content = !empty($comment['content']) ? $comment['content'] : ((isset($comment_check) && $comment_check['status'] == 'success' &&  $comment_check['canEdit']) ? $comment_check['data']['content'] : '');
    $row['comment'] = nv_aleditor('commentContent', '100%', '250px', $comment_content);
    $xtpl->assign('COMMENT', $row['comment']);
}

$allow_editor = (defined('NV_EDITOR') and nv_function_exists('nv_aleditor')) ? true : false;

if (!defined('CKEDITOR') and $allow_editor) {
    define('CKEDITOR', true);
    $my_head .= '<script type="text/javascript" src="' . NV_STATIC_URL . NV_EDITORSDIR . '/' . NV_EDITOR . '/ckeditor.js?t=' . $global_config['timestamp'] . '"></script>';
}
$xtpl->assign('CONTENT', $allow_editor ? 'true' : 'false');

if ($ticket_points['status'] == 'success' && $ticket_points['code'] == '0000') {
    // Hiển thị cảnh báo mức điểm mà khách hàng đề xuất khi hỏi chuyên gia
    if ($row['ask_expert'] == 1) {
        $xtpl->assign('CUSTOMER_WILLING_PAY', $nv_Lang->getModule('customer_willing_to_pay', $row['offer_point_expert']));
        $xtpl->parse('main.point_customer_using.noti_pay_to_expert');
    }

    // Hiển thị điểm của customer
    if (!empty($ticket_points['data'][CommentType::Customer->getLabel()])) {
        $statistics_customer = [];
        $statistics_customer['role_name'] = CommentType::Customer->getLabel();
        $statistics_customer['expert_name'] = get_user_info($row['customer_id'])['fullname'];
        $statistics_customer['customer_total_comment'] = $ticket_points['data'][CommentType::Customer->getLabel()]['count'];
        $statistics_customer['customer_negative'] = $ticket_points['data'][CommentType::Customer->getLabel()]['points']['negative']; // Điểm trừ khi xem câu trả lời
        $statistics_customer['customer_positive'] = $ticket_points['data'][CommentType::Customer->getLabel()]['points']['positive']; // Điểm được hoàn lại
        $xtpl->assign('CUSTOMER_TOTAL_POINT', $statistics_customer);
        $xtpl->parse('main.point_customer_using.point_customer_total');
    }

    // Hiển thị điểm thanh toán cho AI
    if (!empty($ticket_points['data'][CommentType::AI->getLabel()])) {
        $statistics_ai = [];
        $statistics_ai['role_name'] = CommentType::AI->getLabel();
        $statistics_ai['expert_name'] = '';
        $statistics_ai['ai_total_comment'] = $ticket_points['data'][CommentType::AI->getLabel()]['count'];
        $statistics_ai['total_point'] = $ticket_points['data'][CommentType::AI->getLabel()]['points'];
        $xtpl->assign('AI_TOTAL_POINT', $statistics_ai);
        $xtpl->parse('main.point_customer_using.point_final_ai');
    }

    // Hiển thị điểm của AI và Expert
    if (!empty($ticket_points['data'][CommentType::Expert->getLabel()])) {
        foreach ($ticket_points['data'][CommentType::Expert->getLabel()] as $expert_id => $expert_comments) {
            $statistics_expert = [];
            $statistics_expert['role_name'] = CommentType::Expert->getLabel();
            $statistics_expert['expert_name'] = !empty($expert_id) ? get_user_info($expert_id)['fullname'] : '';
            $statistics_expert['total_comment'] = $expert_comments[CommentType::Expert->getLabel()];
            $statistics_expert['total_add_comment'] = $expert_comments[CommentType::ExpertAdditional->getLabel()];
            $statistics_expert['total_point'] = $expert_comments['points'];
            $xtpl->assign('EXPERT_TOTAL_POINT', $statistics_expert);
            $xtpl->parse('main.point_customer_using.expert_loop');
        }
    }

    $xtpl->parse('main.point_customer_using');
}

//Ticket info
$get_user_info = get_user_info($row['customer_id']);
$get_user_info['fullname'] = empty($get_user_info) ? '' : nv_show_name_user($get_user_info['first_name'], $get_user_info['last_name'], $get_user_info['username']);
$xtpl->assign('CUSTOMER', $get_user_info);

// Người tạo yêu cầu
$get_add_userid = get_user_info($row['add_userid']);
$get_add_userid['fullname'] = empty($get_add_userid) ? '' : nv_show_name_user($get_add_userid['first_name'], $get_add_userid['last_name'], $get_add_userid['username']);
$xtpl->assign('ADD_USER', $get_add_userid);

//Loại yc đã chọn
if (isset($array_ticket_cats[$row['cat_id']])) {
    $xtpl->assign('DETAIL_CAT', [
        'title' => $array_ticket_cats[$row['cat_id']]['title_' . NV_LANG_DATA],
    ]);
    $xtpl->parse('main.detail_cat');
}
//Thông tin gói vip
$user_vips = get_user_vips($row['customer_id']);
if ($row['vip_id'] > 0 && isset($user_vips[$row['vip_id']])) {
    $xtpl->assign('DETAIL_VIP', [
        'title' => $user_vips[$row['vip_id']]['title'],
        'link' => URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=cus_info&amp;id=' . $user_vips[$row['vip_id']]['user_id'] . '-' . $user_vips[$row['vip_id']]['id'],
    ]);
    $xtpl->parse('main.detail_vip');
}
//Thông tin đơn hàng
$user_orders = get_user_orders($row['customer_id']);
if ($row['order_id'] > 0 && isset($user_orders[$row['order_id']])) {
    $xtpl->assign('DETAIL_ORDER', [
        'title' => $user_orders[$row['order_id']]['title'],
        'link' => URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;vieworderid=' . $user_orders[$row['order_id']]['id'],
    ]);
    $xtpl->parse('main.detail_order');
}
//Ưu tiên
if (!empty($row['label_ids'])) {
    $labels = explode(',', $row['label_ids']);
    foreach ($labels as $label) {
        $xtpl->assign('DETAIL_LABEL', [
            'title' => NV_LANG_DATA == 'vi' ? $array_ticket_labels[$label]['title_vi'] : $array_ticket_labels[$label]['title_en'],
            'color' => $array_ticket_labels[$label]['color'],
        ]);
        $xtpl->parse('main.detail_label.loop');
    }
    $xtpl->parse('main.detail_label');
}
//Status
if ($row['status'] > 0) {
    $detail_status = TicketStatus::tryFrom($row['status']);
    $xtpl->assign('DETAIL_STATUS', $detail_status?->getLabel());
    $xtpl->parse('main.detail_status');
}

//Hiển thị lead/cơ hội nóng hoặc mới nhất
$lead_info = get_lead_info($row['customer_id']);
if (!empty($lead_info)) {
    if ($lead_info['type'] == 'lead') {
        $xtpl->assign('DETAIL_LEAD', [
            'label' => $nv_Lang->getModule('newest_lead'),
            'name' => $lead_info['name'],
            'id' => $lead_info['id'],
            'type' => 1,
            'link' => NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=crmbidding&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $lead_info['id'],
        ]);
    } else {
        $xtpl->assign('DETAIL_LEAD', [
            'label' => $nv_Lang->getModule('newest_opportunity'),
            'name' => $lead_info['name'],
            'id' => $lead_info['id'],
            'type' => 2,
            'link' => NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=crmbidding&amp;' . NV_OP_VARIABLE . '=opportunities_info&amp;id=' . $lead_info['id'],
        ]);
    }
    $xtpl->parse('main.detail_lead');
    if ($ticket['canReply']) {
        $xtpl->parse('main.detail_lead_info');
        $xtpl->parse('main.detail_lead_script');
    }
}

//Update info
$root_ticket_status = $array_ticket_status; //Lưu lại danh sách status gốc
if ($ticket['canEdit']) {
    $xtpl->parse('main.can_edit_detail');

    if ($row['payment_status'] != 1) {
        foreach ($array_ticket_cats as $value) {
            $xtpl->assign('OPTION', [
                'key' => $value['cat_id'],
                'title' => NV_LANG_DATA == 'vi' ? $value['title_vi'] : $value['title_en'],
                'selected' => ($value['cat_id'] == $row['cat_id']) ? ' selected="selected"' : ''
            ]);
            $xtpl->parse('main.can_edit_info.select_cat_id.loop');
        }
        $xtpl->parse('main.can_edit_info.select_cat_id');
    } else {
        $xtpl->assign('CAT_NAME', $array_ticket_cats[$row['cat_id']]['title_' . NV_LANG_DATA]);
        $xtpl->parse('main.can_edit_info.selected_cat_id');
    }

    $row['label_ids'] = empty($row['label_ids']) ? [] : explode(',', $row['label_ids']);
    foreach ($array_ticket_labels as $value) {
        $xtpl->assign('OPTION', [
            'key' => $value['label_id'],
            'title' => NV_LANG_DATA == 'vi' ? $value['title_vi'] : $value['title_en'],
            'selected' => in_array($value['label_id'], $row['label_ids']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.can_edit_info.select_label_ids');
    }

    // Trạng thái của ticket
    $array_ticket_status = [TicketStatus::Open->value, TicketStatus::Done->value, TicketStatus::Process->value, TicketStatus::Close->value];
    foreach ($array_ticket_status as $key) {
        $xtpl->assign('OPTION', [
            'key' => $key,
            'title' => TicketStatus::tryFrom($key)->getLabel(),
            'selected' => ($key == $row['status']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.can_edit_info.select_status');
    }
    $xtpl->parse('main.can_edit_info');
}

//Hiển thị ảnh đính kèm của ticket
$row['file_attach'] = decode_file($row['file_attach']);
if (sizeof($row['file_attach']) > 0) {
    foreach ($row['file_attach'] as $file) {
        $attached = [];
        $attached['src'] = get_image_src($file);
        $attached['name'] = $file;
        if (file_type(getextension($file)) == 'image') {
            $xtpl->assign('IMAGE', $attached);
            $xtpl->parse('main.detail_attach.loop_image');
        } else {
            $xtpl->assign('FILE', $attached);
            $xtpl->parse('main.detail_attach.loop_file');
        }
    }
    $xtpl->parse('main.detail_attach');
}

//Hiển thị danh sách comment
$experts_responded = [];
if ($comments['status'] == 'success' && $comments['code'] == '0000' && empty($commentid)) {
    $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $row['id'];
    $generate_page = nv_generate_page($base_url, $comments['total'], $comments['perpage'], $comments['page']);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.comments.generate_page');
    }
    $array_comment = array_reverse($comments['data']);

    //Phân loại comment theo cấp đầu tiên và các comment con
    $root_comments = [
        CommentType::Expert->value => [],
        CommentType::AI->value => [],
    ];
    $all_comments = [];

    foreach ($array_comment as $comment) {
        if ($comment['parent'] == 0) {
            // Phân loại theo cấp đầu tiên dựa trên type
            if ($comment['comment_type'] == CommentType::Expert->value) {
                $root_comments[CommentType::Expert->value][] = $comment;
            } else if ($comment['comment_type'] == CommentType::AI->value) {
                $root_comments[CommentType::AI->value][] = $comment;
            }
        } else {
            // Comment con không phân loại theo type
            $all_comments[$comment['parent']][] = $comment;
        }
    }
    // Gắn comment con vào comment cấp đầu tiên cho từng loại
    $ai_comments = attach_comment_children($root_comments[CommentType::AI->value], $all_comments);
    $expert_comments = attach_comment_children($root_comments[CommentType::Expert->value], $all_comments);

    if (!empty($ai_comments) && $row['ask_ai'] == 1) {
        $ai_comment = empty($ai_comments) ? ['log_id' => 0] : $ai_comments[0];
        $ai_comment = [
            'id' => empty($ai_comments) ? 0 : $ai_comments[0]['log_id'],
            'display' => empty($ai_comments) ? 'none' : 'block',
            'content' => empty($ai_comments) ? '' : $ai_comments[0]['content'],
            'comment_time' => empty($ai_comments) ? '' : nv_date('H:i:s d/m/Y', empty($ai_comments[0]['edit_time']) ? $ai_comments[0]['add_time'] : $ai_comments[0]['edit_time']),
            'response_point' => $nv_Lang->getModule('response_point', $ai_comments[0]['status'] == CommentStatus::Invalid->value ? 0 : $ai_comments[0]['point_final']),
            'customer_review' => get_customer_review_component($ai_comment, $row['customer_id'], can_handle_paid_ticket($row['cat_id'])),
            'comment_invalid' => $ai_comments[0]['status'] == CommentStatus::Invalid->value ? $nv_Lang->getModule('error_invalid_ai') : '',
        ];

        $xtpl->assign('AI_COMMENT', $ai_comment);
        foreach ($ai_comments[0]['children'] as $child_comment) {
            if (sizeof($child_comment['children']) > 0) {
                foreach ($child_comment['children'] as $grandchild_comment) {
                    if (sizeof($grandchild_comment['children']) > 0) {
                        foreach ($grandchild_comment['children'] as $great_grandchild_comment) {
                            $great_grandchild_comment['reply_from'] = $nv_Lang->getModule('reply_extent_from_expert', get_user_info($great_grandchild_comment['reply_userid'])['fullname']);
                            $great_grandchild_comment['comment_time'] = nv_date('H:i:s d/m/Y', $great_grandchild_comment['edit_time'] ? $great_grandchild_comment['add_time'] : $great_grandchild_comment['edit_time']);
                            // Đánh giá của khách hàng
                            $xtpl->assign('GREAT_GRANDCHILD_COMMENT', $great_grandchild_comment);
                            $xtpl->parse('main.ai_comment.loop_child.grandchild.loop.great_grandchild.loop');
                        }
                        $xtpl->parse('main.ai_comment.loop_child.grandchild.loop.great_grandchild');
                    }
                    $grandchild_comment['reply_from'] = $nv_Lang->getModule('reply_from_ai');
                    $grandchild_comment['comment_time'] = nv_date('H:i:s d/m/Y', empty($grandchild_comment['edit_time']) ? $grandchild_comment['add_time'] : $grandchild_comment['edit_time']);
                    $grandchild_comment['response_point'] = $nv_Lang->getModule('response_point', $grandchild_comment['status'] == CommentStatus::Invalid->value ? 0 : $grandchild_comment['point_final']);
                    $grandchild_comment['comment_invalid'] = $grandchild_comment['status'] == CommentStatus::Invalid->value ? $nv_Lang->getModule('error_invalid_ai') : '';
                    // Đánh giá của khách hàng
                    $grandchild_comment['customer_review'] = get_customer_review_component($grandchild_comment, $row['customer_id'], can_handle_paid_ticket($row['cat_id']));
                    $xtpl->assign('GRANDCHILD_COMMENT', $grandchild_comment);
                    $xtpl->parse('main.ai_comment.loop_child.grandchild.loop');
                }
                $xtpl->parse('main.ai_comment.loop_child.grandchild');
            }
            if ($child_comment['comment_type'] == CommentType::ExpertAdditionalForAI->value) {
                $customer_question = $nv_Lang->getModule('reply_extent_from_expert', get_user_info($child_comment['reply_userid'])['fullname']);
            } else {
                $customer_question = $nv_Lang->getModule('question_of_customer', get_user_info($row['customer_id'])['fullname']);
            }
            $child_comment['customer_question'] = $customer_question;
            $child_comment['comment_time'] = nv_date('H:i:s d/m/Y', empty($child_comment['edit_time']) ? $child_comment['add_time'] : $child_comment['edit_time']);
            $child_comment['display_id'] = $child_comment['status'] == CommentStatus::Process->value ? 'ai_reply_content_addtition' : 'ai_reply_content_' . $child_comment['log_id'];
            $xtpl->assign('CHILD_COMMENT', $child_comment);
            $xtpl->parse('main.ai_comment.loop_child');
        }

        // Kiểm tra trạng thái comment AI để hiển thị form trả lời bổ sung
        $check_comment_ai = $db->query('SELECT t1.log_id, t1.comment_type, t1.status, t1.invalid
            FROM ' . TB_TICKET_LOG . ' t1
            WHERE t1.ticket_id = ' . $row['id'] . '
            AND t1.comment_type = ' . CommentType::AI->value . '
            AND NOT EXISTS (
                SELECT 1 FROM ' . TB_TICKET_LOG . ' t2
                WHERE t2.ticket_id = ' . $row['id'] . '
                AND t2.parent = t1.log_id
                AND t2.comment_type = ' . CommentType::ExpertAdditionalForAI->value . '
            )
            ORDER BY t1.log_id DESC')->fetchAll();

        if (!empty($check_comment_ai)) {
            $latest_ai_comment = $check_comment_ai[0];
            // Trường hợp câu trả lời của AI không có giá trị hoặc được Chuyên gia mark invalid
            if (can_handle_paid_ticket($row['cat_id']) && ($latest_ai_comment['status'] == CommentStatus::Invalid->value || $latest_ai_comment['invalid'] == 1)) {
                // Trường hợp 1: Chỉ có comment gốc
                if (count($check_comment_ai) == 1) {
                    $c_parent_type = $check_comment_ai[0]['comment_type'];
                    $_log_id = $check_comment_ai[0]['log_id'];
                } else {
                    $c_parent_type = $latest_ai_comment['comment_type'];
                    $_log_id = $latest_ai_comment['log_id'];
                }
                $xtpl->assign('COMMENT_PARENT_TYPE', $c_parent_type);
                $xtpl->assign('QUESTION_AI_ID', $_log_id);
                $xtpl->parse('main.ai_comment.expert_reply_additional_for_ai');
            }
        }

        $xtpl->parse('main.ai_comment');
    }

    if (!empty($expert_comments)) {
        foreach ($expert_comments as $comment) {
            $is_question_by_customer = false;
            $experts_responded[] = $comment['reply_userid'];
            // Xử lý hiển thị comment con
            foreach ($comment['children'] as $child_comment) {
                $is_question_by_customer = $child_comment['comment_type'] == CommentType::Customer->value;
                if (sizeof($child_comment['children']) > 0) {
                    $last_grandchild_comment = null;
                    foreach ($child_comment['children'] as $grandchild_comment) {
                        // Câu TL bổ sung cho câu TL của YC bổ sung
                        if (sizeof($grandchild_comment['children']) > 0) {
                            foreach ($grandchild_comment['children'] as $great_grandchild_comment) {
                                $great_grandchild_comment['reply_from'] = $nv_Lang->getModule('reply_extent_from_expert', get_user_info($great_grandchild_comment['reply_userid'])['fullname']);
                                $great_grandchild_comment['comment_time'] = nv_date('H:i:s d/m/Y', $great_grandchild_comment['edit_time'] ? $great_grandchild_comment['add_time'] : $great_grandchild_comment['edit_time']);
                                // Đánh giá của khách hàng
                                $xtpl->assign('GREAT_GRANDCHILD_COMMENT', $great_grandchild_comment);
                                $xtpl->parse('main.expert_comment.loop.loop_child.grandchild.loop.great_grandchild.loop');
                            }
                            $xtpl->parse('main.expert_comment.loop.loop_child.grandchild.loop.great_grandchild');
                        }
                        $grandchild_comment['reply_from'] = $nv_Lang->getModule('reply_from_expert', get_user_info($grandchild_comment['reply_userid'])['fullname']);
                        $grandchild_comment['comment_time'] = nv_date('H:i:s d/m/Y', $grandchild_comment['edit_time'] ? $grandchild_comment['add_time'] : $grandchild_comment['edit_time']);
                        $grandchild_comment['response_point'] = $nv_Lang->getModule('response_point', $grandchild_comment['point_final']);
                        // Đánh giá của khách hàng
                        $grandchild_comment['customer_review'] = get_customer_review_component($grandchild_comment, $row['customer_id'], can_handle_paid_ticket($row['cat_id']));
                        $xtpl->assign('GRANDCHILD_COMMENT', $grandchild_comment);
                        $xtpl->parse('main.expert_comment.loop.loop_child.grandchild.loop');
                        $last_grandchild_comment = $grandchild_comment;
                    }
                    $xtpl->parse('main.expert_comment.loop.loop_child.grandchild');
                }
                if ($child_comment['comment_type'] == CommentType::Expert->value || $child_comment['comment_type'] == CommentType::ExpertAdditional->value) {
                    $source_comment = $nv_Lang->getModule('reply_extent_from_expert', get_user_info($child_comment['reply_userid'])['fullname']);
                } else {
                    $source_comment = $nv_Lang->getModule('question_of_customer', get_user_info($row['customer_id'])['fullname']);
                }
                $child_comment['comment_time'] = nv_date('H:i:s d/m/Y', !empty($child_comment['edit_time']) ? $child_comment['edit_time'] : $child_comment['add_time']);
                $child_comment['customer_question'] = $source_comment;
                $child_comment['display_id'] = $child_comment['status'] == CommentStatus::Process->value ? 'expert_reply_content_addtition' : 'expert_reply_content_' . $child_comment['log_id'];
                if ($child_comment['comment_type'] == CommentType::Expert->value) {
                    $child_comment['customer_review'] = get_customer_review_component($child_comment, $row['customer_id'], can_handle_paid_ticket($row['cat_id']));
                    $child_comment['response_point'] = $nv_Lang->getModule('response_point', $child_comment['point_final']);
                }
                // Hiển thị ảnh đính kèm của ticket
                $child_comment['file_attach'] = decode_file($child_comment['file_attach']);
                if (!empty($child_comment['file_attach']) && sizeof($child_comment['file_attach']) > 0) {
                    foreach ($child_comment['file_attach'] as $file) {
                        $attached = [];
                        $attached['src'] = get_image_src($file);
                        $attached['name'] = $file;
                        if (file_type(getextension($file)) == 'image') {
                            $xtpl->assign('IMAGE', $attached);
                            $xtpl->parse('main.expert_comment.loop.loop_child.detail_attach.loop_image');
                        } else {
                            $xtpl->assign('FILE', $attached);
                            $xtpl->parse('main.expert_comment.loop.loop_child.detail_attach.loop_file');
                        }
                    }
                    $xtpl->parse('main.expert_comment.loop.loop_child.detail_attach');
                }
                $xtpl->assign('CHILD_COMMENT', $child_comment);
                $xtpl->parse('main.expert_comment.loop.loop_child');
            }

            // Logic hiển thị form trả lời
            if ($comment['reply_userid'] == $admin_info['userid'] && $row['status'] != TicketStatus::Close->value) {
                // Tìm yêu cầu bổ sung mới nhất chưa được trả lời
                $latest_additional_request = $db->query('SELECT log_id, comment_type FROM ' . TB_TICKET_LOG . '
                    WHERE ticket_id=' . $row['id'] . '
                    AND status!=' . CommentStatus::Done->value . '
                    AND comment_type=' . CommentType::Customer->value . '
                    ORDER BY add_time DESC
                    LIMIT 1')->fetch();

                if (!empty($latest_additional_request)) {
                    // Kiểm tra xem yêu cầu này đã có câu trả lời chưa
                    $has_response = $db->query('SELECT log_id FROM ' . TB_TICKET_LOG . '
                        WHERE ticket_id=' . $row['id'] . '
                        AND parent=' . $latest_additional_request['log_id'] . '
                        AND status!=' . CommentStatus::Done->value . '
                        AND comment_type=' . CommentType::Expert->value)->fetchColumn();

                    // Chỉ hiển thị form trả lời cho yêu cầu bổ sung mới nhất chưa được trả lời
                    if (empty($has_response)) {
                        $xtpl->assign('COMMENT_PARENT_TYPE', $latest_additional_request['comment_type']);
                        $xtpl->assign('QUESTION_EXPERT_ID', $latest_additional_request['log_id']);
                        $xtpl->parse('main.expert_comment.loop.expert_reply_comment');
                    }
                } else {
                    // Nếu không có yêu cầu bổ sung chưa được trả lời
                    if ($comment['parent'] == 0 && !$is_question_by_customer) {
                        // Cho phép trả lời bổ sung nếu là câu trả lời gốc
                        $xtpl->assign('COMMENT_PARENT_TYPE', $comment['comment_type']);
                        $xtpl->assign('QUESTION_EXPERT_ID', $comment['log_id']);
                        $xtpl->parse('main.expert_comment.loop.expert_parent_reply_additional');
                    } else if (
                        isset($last_grandchild_comment) &&
                        $last_grandchild_comment['reply_userid'] == $admin_info['userid']
                    ) {
                        // Hoặc cho phép trả lời nếu comment cuối cùng thuộc về người dùng hiện tại
                        $xtpl->assign('COMMENT_PARENT_TYPE', $last_grandchild_comment['comment_type']);
                        $xtpl->assign('QUESTION_EXPERT_ID', $last_grandchild_comment['log_id']);
                        $xtpl->parse('main.expert_comment.loop.expert_grandchild_reply_comment');
                    }
                }
            }

            // Đánh giá của khách hàng
            $comment['response_point'] = $nv_Lang->getModule('response_point', $comment['point_final']);
            $comment['customer_review'] = get_customer_review_component($comment, $row['customer_id'], can_handle_paid_ticket($row['cat_id']));
            $comment['reply_from_expert'] = $nv_Lang->getModule('reply_from_expert', get_user_info($comment['reply_userid'])['fullname']);
            $comment['comment_time'] = empty($comment) ? '' : nv_date('H:i:s d/m/Y', !empty($comment['edit_time']) ? $comment['edit_time'] : $comment['add_time']);
            $comment['display'] = empty($comment) ? 'none' : 'block';
            $xtpl->assign('EXPERT_COMMENT', $comment);
            $xtpl->parse('main.expert_comment.loop');
        }
        $xtpl->parse('main.expert_comment');
    }
}

//Cho phép trả lời
if ($ticket['canReply'] && !in_array($admin_info['userid'], $experts_responded) && $row['ask_expert'] == 1) {
    if (!empty($error)) {
        $xtpl->assign('ERROR', $error);
        $xtpl->parse('main.can_reply.error');
    }

    $total_file = !empty($comment_detail['file_attach']) ? count($comment_detail['file_attach']) + 1 : 0;
    $xtpl->assign('TOTAL_FILE', $total_file);
    if (!empty($comment_detail['file_attach'])) {
        $file = [];
        foreach ($comment_detail['file_attach'] as $key => $value) {
            $file['key'] = $key;
            $file['value'] = empty($value) ? '' : NV_BASE_SITEURL . NV_UPLOADS_DIR . '/' . $module_upload . '/' . $value;
            $xtpl->assign('FILE', $file);
            $xtpl->parse('main.can_reply.files');
        }
    }
    $_class = '';
    if ($row['ask_expert'] != 1) {
        // Trường hợp không phải hỏi chuyên gia thì ẩn đi
        $_class = ' d-none';
        $xtpl->assign('MIN_POINT_EXPERT', 0);
    }
    $xtpl->assign('HIDE', $_class);
    $xtpl->parse('main.can_reply');
    $xtpl->parse('main.can_edit');
} else {
    $my_head .= '<script type="text/javascript" src="' . NV_STATIC_URL . NV_EDITORSDIR . '/' . NV_EDITOR . '/ckeditor.js?t=' . $global_config['timestamp'] . '"></script>';
}
//Hiển thị danh sách log
$where_api['AND'][] = [
    '=' => [
        'ticket_id' => $row['id']
    ]
];
$params = [
    'page' => 1,
    'perpage' => 20
];
$order['log_time'] = "DESC";
$params['where'] = $where_api;
$params['order'] = $order;
$data_log = nv_local_api('ListAllLog', $params, $admin_info['username'], $module_name);
$array_logs = json_decode($data_log, true);
if ($array_logs['status'] == 'success' and $array_logs['code'] == '0000') {
    foreach ($array_logs['data'] as $log) {
        $log['log_time'] = $log['log_time'] != 0 ? nv_date('H:i d/m/Y', $log['log_time']) : '';
        $get_user_info = get_user_info($log['userid']);
        $log['user'] = !empty($get_user_info) ? nv_show_name_user($get_user_info['first_name'], $get_user_info['last_name'], $get_user_info['username']) : ($log['userid'] == 0 ? $nv_Lang->getModule('system') : '');
        $xtpl->assign('LOG', $log);
        $log_data = json_decode($log['log_data'], true);
        if (!is_array($log_data)) {
            $log_data = [];
        }
        $log_data = array_values($log_data);
        $log_data_size = sizeof($log_data);
        if ($log_data_size > 0) {
            // Tiêu đề log
            $log_data_show = $log_data[0];
            unset($log_data[0]);
            $xtpl->assign('LOG_DATA_SHOW', $log_data_show);
            if (is_array($log_data_show)) {
                $xtpl->parse('main.alllog.data.sarray');
            } else {
                $xtpl->parse('main.alllog.data.sstring');
            }

            // Nội dung chi tiết khác
            if (!empty($log_data)) {
                foreach ($log_data as $dother) {
                    $xtpl->assign('LOG_DATA_OTHER', $dother);
                    if (isset($dother['type']) and $dother['type'] == 'link') {
                        // Dạng log có link
                        $xtpl->assign('LOG_DATA_OTHER', Log::getLogLink($dother));
                        $xtpl->parse('main.alllog.data.other.loop.sstring');
                    } elseif (isset($dother['type']) and $dother['type'] == 'directlink') {
                        // Dạng log link đến trang khác
                        $xtpl->assign('LOG_DATA_OTHER', Log::getLogDirectLink($dother));
                        $xtpl->parse('main.alllog.data.other.loop.sstring');
                    } elseif (is_array($dother)) {
                        $xtpl->parse('main.alllog.data.other.loop.sarray');
                    } else {
                        $xtpl->parse('main.alllog.data.other.loop.sstring');
                    }
                    $xtpl->parse('main.alllog.data.other.loop');
                }
                $xtpl->parse('main.alllog.data.other');
                $xtpl->parse('main.alllog.data.other1');
            }
            $xtpl->parse('main.alllog.data');
        }
        $xtpl->parse('main.alllog');
    }
}

//Hiển thị ticket liên quan
$params = [
    'page' => $page,
    'perpage' => $per_page
];

$where = [];
$where['AND'][] = [
    '=' => [
        'customer_id' => $row['customer_id']
    ]
];
$where['AND'][] = [
    '!=' => [
        'id' => $row['id']
    ]
];
$where['AND'][] = [
    '=' => [
        'is_paid' => 1 // Các ticket trả phí
    ]
];

$where['AND'][] = [
    '!=' => [
        'status' => TicketStatus::Draft->value
    ]
];

$where['AND'][] = [
    '=' => [
        'delete_time' => 0
    ]
];

// Nếu có điều kiện where thì gán
if (!empty($where)) {
    $params['where'] = $where;
}

// GỌI API
$array_ticket_status = $root_ticket_status; //Trả lại danh sách status gốc
$list_related = nv_local_api('ListAllTicket', $params, $admin_info['username'], $module_name);
$ticket_related = json_decode($list_related, true);
if ($ticket_related['status'] == 'success' and $ticket_related['code'] == '0000') {
    $number = 1;
    foreach ($ticket_related['data'] as $related) {
        $related['number'] = $number;
        ++$number;
        $related['link_detail'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail_paid&amp;id=' . $related['id'];
        $related['cat_title'] = empty($array_ticket_cats[$related['cat_id']]) ? '' : $array_ticket_cats[$related['cat_id']]['title_' . NV_LANG_DATA];
        $related['status'] = TicketStatus::tryFrom($related['status'])?->getLabel() ?? '';
        $related['add_time'] = nv_date('H:i:s d/m/Y', $related['add_time']);
        $xtpl->assign('RELATED', $related);
        $xtpl->parse('main.related.loop');
    }

    $xtpl->parse('main.related');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
