<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

$showheader = $nv_Request->get_int('showheader', 'post,get', 1);
$phone = $nv_Request->get_title('phone', 'post,get', '');
$callid = $nv_Request->get_title('callid', 'post,get', '');
$error = '';
if (empty($callid)) {
    if (!empty($phone) and !phonecheck($phone)) {
        $error = $nv_Lang->getModule('error_phone_number');
    }
}

$type_view = $nv_Request->get_int('type_view', 'post,get', 1);
if ($type_view == 2) {
    $fromcall_start = $nv_Request->get_title('fromtime', 'post,get', ''); // Thời gian gọi đầu tiên
    $tocall_end = $nv_Request->get_title('totime', 'post,get', ''); // Thời gian gọi cuối cùng
} else {
    $fromcall_start = nv_substr($nv_Request->get_title('fromtime', 'post,get', nv_date('d/m/Y', NV_CURRENTTIME - 86400 * 30)), 0, 10); // Thời gian gọi đầu tiên
    $tocall_end = nv_substr($nv_Request->get_title('totime', 'post,get', nv_date('d/m/Y', NV_CURRENTTIME)), 0, 10); // Thời gian gọi cuối cùng
}
$extension_sale = $nv_Request->get_int('extension_sale', 'post,get', 0); // Sale extension
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $fromcall_start, $m)) {
    $fcall = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
} else {
    $fcall = 0;
}

if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $tocall_end, $m)) {
    $tcall = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
} else {
    $tcall = 0;
}

if (($tcall - $fcall) > (86400 * 365)) {
    $fcall = $tcall - 86400 * 30;
    $fromcall_start = nv_date('d/m/Y', $fcall);
    $error = $nv_Lang->getModule('error_time');
}

// lấy các sale mà sale đó quản lý
$caregiver_id_leads = array();
$extension_ids_sale = [];
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users';
if (!defined('NV_IS_SPADMIN')) {
    $sql .= ' WHERE userid = ' . $admin_info['userid'];
}
$result = $db->query($sql);
while ($row_groups_users = $result->fetch()) {
    if ($row_groups_users['is_leader'] == 1) {
        $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $row_groups_users['group_id'] . ' AND userid != ' . $admin_info['userid'];
        $_result = $db->query($_sql);
        while ($_row_groups_users = $_result->fetch()) {
            $config = json_decode($_row_groups_users['config'], true);
            $caregiver_id_leads[$_row_groups_users['userid']] = $_row_groups_users['userid'];
            $extension_ids_sale[$_row_groups_users['userid']] = [
                'userid' => $_row_groups_users['userid'],
                'extension_voicecloud' => isset($config['extension_voicecloud']) ? $config['extension_voicecloud'] : 0
            ];
        }
    }
}

$caregiver_id_leads[$admin_info['userid']] = $admin_info['userid'];
$array_extension_sale = [];

if (!empty($extension_ids_sale)) {
    $filtered_extension_ids_sale = [];
    foreach ($extension_ids_sale as $userid => $sale) {
        if ($sale['extension_voicecloud'] > 0) {
            $filtered_extension_ids_sale[$userid] = $sale;
        }
    }
    if (!empty($filtered_extension_ids_sale)) {
        $sql = 'SELECT userid, username, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', array_keys($filtered_extension_ids_sale)) . ')';
        $result = $db->query($sql);
        while ($row = $result->fetch()) {
            $array_extension_sale[$row['userid']] = [
                'userid' => $row['userid'],
                'username' => $row['username'],
                'first_name' => $row['first_name'],
                'last_name' => $row['last_name'],
                'extension_voicecloud' => $extension_ids_sale[$row['userid']]['extension_voicecloud']
            ];
        }
    }
}

if ($nv_Request->isset_request('ajax', 'post, get')) {
    if (!empty($phone)) {

        // Chuẩn hóa số điện thoại
        $_tmp_phone = $phone;
        if (preg_match('/(\d{9})$/', $phone, $m)) {
            $_tmp_phone = $m[0];
        }
        $_tmp_phone = preg_replace('/[!@#$%^&*()_+\-=\[\]{};:"\\|,.<>\s]/', '', $_tmp_phone);
        $_tmp_phone = (int) $_tmp_phone;
        $leads_record = $oppor_record = $users_record = $array_business = $array_customs_log = $array_user_phone = [];

        // Kiểm tra có nhà thầu
        $infoAPI = [];
        $arr_where = [];
        $arr_where['OR'][] = [
            '=' => [
                'phone_search' => $_tmp_phone
            ]
        ];
        if (!empty($arr_where)) {
            $infoAPI = [
                'where' => $arr_where
            ];
            $businesslistings_info = CallAPI($infoAPI, 'ListAllBusinessListings', 'businesslistings', API_API_URL, API_API_KEY, API_API_SECRET);

            if (isset($businesslistings_info['data'])) {
                foreach ($businesslistings_info['data'] as $k => $_row) {
                    $array_business[$_row['id']] = $_row;
                }
            }
        }

        // Đơn hàng dauthau_info
        $where = [];
        if (!empty($_tmp_phone)) {
            $where['OR'][] = [
                'LIKE' => [
                    'phone' => '%' . $_tmp_phone
                ]
            ];

            $params = [
                'where' => $where
            ];
            $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
            $api_dtinfo->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListBiddingCustomsLog')
                ->setData($params);
            $result = $api_dtinfo->execute();
            $error = $api_dtinfo->getError();
            if (empty($error) and $result['status'] == 'success') {
                foreach ($result['data'] as $_row) {
                    $_row['isset'] = 0;
                    if ($_row['userdelete'] == 0) {
                        $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
                        $api_dtinfo->setModule('bidding')
                            ->setLang('vi')
                            ->setAction('GetBiddingOrder')
                            ->setData([
                            'id' => $_row['order_id']
                        ]);
                        $check_order = $api_dtinfo->execute();
                        if ($check_order['status'] == 'success' and !empty($check_order['data'])) {
                            $_row['isset'] = 1;
                            $_row['order'] = $check_order['data'];
                        }
                    }
                    $array_customs_log[$_row['id']] = $_row;
                }
            }
        }

        // Kiểm tra liên kết với leads
        $where = [];
        $where['AND'][] = [
            '=' => [
                'active' => 1
            ]
        ];
        $where['OR'][] = [
            '=' => [
                'phone_search' => $_tmp_phone
            ]
        ];

        $where['OR'][] = [
            'FIND_IN_SET' => [
                'sub_phone_search' => $_tmp_phone
            ]
        ];

        $params_leads = [];
        $params_leads = [
            'where' => $where
        ];

        // GỌI API
        $List = nv_local_api('ListAllLeads', $params_leads, $admin_info['username'], $module_name);
        $ListAllLeads = json_decode($List, true);
        if (isset($ListAllLeads['data'])) {
            foreach ($ListAllLeads['data'] as $key => $value) {
                $leads_record[$value['id']] = $value;
            }
        }

        if (!empty($leads_record)) {
            $params = [];
            $where['AND'][] = [
                'IN' => [
                    'leadsid' => implode(',', array_keys($leads_record))
                ]
            ];

            $params = [
                'where' => $where
            ];

            $opportunities = nv_local_api('ListAllOpportunities', $params, $admin_info['username'], $module_name);
            $check_opportunities = json_decode($opportunities, true);
            if (isset($check_opportunities['data'])) {
                foreach ($check_opportunities['data'] as $key => $value) {
                    $leads_record[$value['leadsid']]['opportunities'] = $value;
                    if ($value['orderid'] != '') {

                        $where = [];
                        $where['AND'][] = [
                            'IN' => [
                                'order_id' => '(' . $value['orderid'] . ')'
                            ]
                        ];
                        $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
                        $api_dtinfo->setModule('bidding')
                            ->setLang('vi')
                            ->setAction('ListBiddingCustomsLog')
                            ->setData([
                            'where' => $where
                        ]);
                        $result = $api_dtinfo->execute();
                        $error = $api_dtinfo->getError();
                        if (empty($error) and $result['status'] == 'success') {
                            foreach ($result['data'] as $_order) {
                                $leads_record[$value['leadsid']]['order'][$_order['id']] = $_order;
                            }
                        }
                    }
                }
            }
        }

        // Kiểm tra liên kết với Oppotunities - cơ hội kinh doanh
        $where = [];
        $where['AND'][] = [
            '=' => [
                'active' => 1
            ]
        ];

        $where['OR'][] = [
            '=' => [
                'phone_search' => $_tmp_phone
            ]
        ];

        $where['OR'][] = [
            'FIND_IN_SET' => [
                'sub_phone_search' => $_tmp_phone
            ]
        ];

        $params = [];
        $params = [
            'where' => $where
        ];

        // GỌI API
        $ListAllOpportunities = nv_local_api('ListAllOpportunities', $params, $admin_info['username'], $module_name);
        $ListAllOpportunities = json_decode($ListAllOpportunities, true);

        if (isset($ListAllOpportunities['data'])) {
            foreach ($ListAllOpportunities['data'] as $key => $value) {
                $oppor_record[$value['id']] = $value;
                if ($value['orderid'] != '') {
                    $where = [];
                    $where['AND'][] = [
                        'IN' => [
                            'order_id' => '(' . $value['orderid'] . ')'
                        ]
                    ];
                    $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
                    $api_dtinfo->setModule('bidding')
                        ->setLang('vi')
                        ->setAction('ListBiddingCustomsLog')
                        ->setData([
                        'where' => $where
                    ]);
                    $result = $api_dtinfo->execute();
                    $error = $api_dtinfo->getError();
                    if (empty($error) and $result['status'] == 'success') {
                        foreach ($result['data'] as $_order) {
                            $oppor_record[$value['id']]['order'][$_order['id']] = $_order;
                        }
                    }
                }
            }
        }

        // Kiểm tra liên kết với Users - thành viên
        $sql = "SELECT userid,email,regdate,username,first_name,last_name,md5username FROM " . NV_USERS_GLOBALTABLE;
        $where = [];
        $sql_info = "SELECT userid, phone, mst FROM " . NV_USERS_GLOBALTABLE . "_info ";
        $_where = "phone LIKE " . $db->quote('%' . $_tmp_phone . '');

        $_arr_user_info = [];
        $sql_info .= " WHERE " . $_where . " ORDER BY userid DESC";
        $result_info = $db->query($sql_info);
        while ($_row = $result_info->fetch()) {
            $_arr_user_info[$_row['userid']] = $_row['userid'];
        }

        if (!empty($_arr_user_info)) {
            $where[] = "userid IN (" . implode(',', $_arr_user_info) . ")";
        }

        if (!empty($where)) {
            $sql .= " WHERE " . implode(' OR ', $where);
            $sql .= "  ORDER BY userid DESC";
            $result = $db->query($sql);
            $array_user_not_info = [];
            while ($_row = $result->fetch()) {
                $_row['order'] = [];
                $array_user_phone[$_row['userid']] = $_row;
            }

            if (!empty($array_user_phone)) {
                $query_user_not_info = $db->query("SELECT userid, phone, mst FROM " . NV_USERS_GLOBALTABLE . "_info WHERE userid IN (" . implode(',', array_keys($array_user_phone)) . ")");
                while ($_row = $query_user_not_info->fetch()) {
                    if (isset($array_user_phone[$_row['userid']])) {
                        $array_user_phone[$_row['userid']]['phone'] = $_row['phone'];
                        $array_user_phone[$_row['userid']]['mst'] = $_row['mst'];
                    }
                }
            }
        }
        if (!empty($array_user_phone)) {
            $where = [];
            $where['AND'][] = [
                'IN' => [
                    'user_id' => '(' . implode(',', array_keys($array_user_phone)) . ')'
                ]
            ];
            $params = [
                'where' => $where
            ];
            $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
            $api_dtinfo->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListBiddingCustomsLog')
                ->setData($params);
            $result = $api_dtinfo->execute();
            $error = $api_dtinfo->getError();
            if (empty($error) and $result['status'] == 'success') {
                foreach ($result['data'] as $_order) {
                    if (!empty($array_user_phone[$_order['user_id']])) {
                        $array_user_phone[$_order['user_id']]['order'][$_order['id']] = $_order;
                    }
                }
            }
        }

        // Kiểm tra liên kết với hồ sơ khách hàng, đơn hàng trên dauthau.net
        $array_profile_dtnet = [];
        $error_api_getduplicate_profile = '';
        $array_payment_dtnet = [];
        $error_api_listduplicate_payment = '';

        if (defined('API_DAUTHAUNET_URL')) {
            // Kiểm tra liên kết profile cơ sở dữ liệu dauthau.net: GetDuplicateProfile
            $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
            $api->setModule('')
                ->setLang('vi')
                ->setAction('GetDuplicateProfile')
                ->setData([
                'phone' => $phone
            ]);
            $result = $api->execute();
            $error = $api->getError();
            if (empty($error) and $result['status'] == 'success') {
                $array_profile_dtnet = $result['profile_duplicate'];
            } else {
                if (!empty($error)) {
                    $error_api_getduplicate_profile = $error;
                } elseif ($result['status'] == 'error') {
                    $error_api_getduplicate_profile = $result;
                }
            }

            // Kiểm tra liên kết đơn hàng cơ sở dữ liệu dauthau.net
            $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
            $api->setModule('')
                ->setLang('vi')
                ->setAction('ListDuplicatePayment')
                ->setData([
                'phone' => $phone
            ]);
            $result = $api->execute();
            $error = $api->getError();
            if (empty($error) and $result['status'] == 'success') {
                if ($result['code'] != 4000) {
                    $array_payment_dtnet = $result['payment_duplicate'];
                }
            } else {
                if (!empty($error)) {
                    $error_api_listduplicate_payment = $error;
                } elseif ($result['status'] == 'error') {
                    $error_api_listduplicate_payment = $result;
                }
            }
        }

        $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
        $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
        $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
        $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
        $xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
        $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
        $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
        $xtpl->assign('MODULE_NAME', $module_name);
        $xtpl->assign('MODULE_UPLOAD', $module_upload);
        $xtpl->assign('MODULE_FILE', $module_file);
        $xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
        $xtpl->assign('OP', $op);

        // Nhà thầu
        if (sizeof($array_business) > 0) {
            $dauthau_asia_url = preg_replace('/(\/api\.php)$/', '/index.php?', API_DAUTHAUINFO_URL);
            $xtpl->assign('NUM_BUSINESS', sprintf($nv_Lang->getModule('duplicate_business'), sizeof($array_business)));
            foreach ($array_business as $business) {
                $business['link'] = $dauthau_asia_url . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($business['companyname']) . "-" . $business['id'];
                if (!empty($_tmp_phone) and !empty($business['phone']) and (substr($business['phone'], -9) == substr($_tmp_phone, -9))) {
                    $business['phone'] = '<span class="red">' . $business['phone'] . '</span>';
                }
                $business['update_time'] = nv_date('H:i d/m/Y', $business['update_time']);
                $business['ngay_phe_duyet'] = $business['ngay_phe_duyet'] != '' ? nv_date('H:i d/m/Y', $business['ngay_phe_duyet']) : '';
                $xtpl->assign('BUSINESS', $business);
                $xtpl->parse('ajax.business.loop');
            }
            $xtpl->parse('ajax.business');
        }

        // Hồ sơ khách hàng trên dauthau.net
        if (empty($error_api_getduplicate_profile)) {
            if (!empty($array_profile_dtnet)) {
                $xtpl->assign('NUMBER_PROFILE_TITLE', sprintf($nv_Lang->getModule('duplicate_profile_dtnet'), count($array_profile_dtnet)));
                foreach ($array_profile_dtnet as $key => $value) {
                    $value['link'] = 'https://dauthau.net/vi/dn/' . $value['prof_alias'];
                    $value['prof_name_title'] = $value['status'] == 1 ? '<a href="' . $value['link'] . '" target="_blank"><strong>' . $value['prof_name'] . '</strong></a>' : '<strong>' . $value['prof_name'] . '</strong>';

                    if (!empty($_tmp_phone) and !empty($value['info_phone']) and (substr($value['info_phone'], -9) == substr($_tmp_phone, -9))) {
                        $value['info_phone'] = '<span class="red">' . $value['info_phone'] . '</span>';
                    }
                    $xtpl->assign('PROFILE', $value);

                    $xtpl->parse('ajax.duplicate_profile.loop');
                }
                $xtpl->parse('ajax.duplicate_profile');
            }
        } else {
            $xtpl->parse('ajax.error_duplicate_profile');
        }

        // Đơn hàng trên dauthau.net
        if (empty($error_api_listduplicate_payment)) {
            if (!empty($array_payment_dtnet)) {
                $xtpl->assign('NUM_DUPLICATE_PAYMENT', sprintf($nv_Lang->getModule('duplicate_payment_dtnet'), sizeof($array_payment_dtnet)));
                foreach ($array_payment_dtnet as $duplicate_payment) {
                    $duplicate_payment['duplicate'] = '';
                    $duplicate_payment['link'] = '';
                    if (!empty($_tmp_phone) and !empty($duplicate_payment['contact_phone']) and (substr($duplicate_payment['contact_phone'], -9) == substr($_tmp_phone, -9))) {
                        $duplicate_payment['link'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;q=' . $duplicate_payment['contact_phone'];
                        $duplicate_payment['duplicate'] = '<span class="red">' . $duplicate_payment['contact_phone'] . '</span>';
                    }

                    if (!empty($duplicate_payment['link']) and $duplicate_payment['isset'] != 0) {
                        $duplicate_payment['name'] = '<a href="' . $duplicate_payment['link'] . '">' . $duplicate_payment['name'] . '</a>';
                    }

                    $duplicate_payment['check_vip'] = ' ' . $nv_Lang->getModule('payment');
                    if (!empty($duplicate_payment['vip'])) {
                        if ($duplicate_payment['is_renewal'] == 1) {
                            $duplicate_payment['check_vip'] .= ' ' . $nv_Lang->getModule('check_vip_renewal1') . ' ';
                        } elseif ($duplicate_payment['is_renewal'] == 2) {
                            $duplicate_payment['check_vip'] .= ' ' . $nv_Lang->getModule('check_vip_renewal2') . ' ';
                        } else {
                            $duplicate_payment['check_vip'] .= ' ' . $nv_Lang->getModule('check_vip_renewal0') . ' ';
                        }
                    } else {
                        $duplicate_payment['check_vip'] .= ' ' . $nv_Lang->getModule('check_vip_authen') . ' ';
                    }

                    $duplicate_payment['check_vip'] .= $nv_Lang->getModule($duplicate_payment['vip']);
                    if ($duplicate_payment['status'] == 1) {
                        $duplicate_payment['check_vip'] .= ' ' . $nv_Lang->getModule('check_vip_already_payment') . '.';
                    } else {
                        $duplicate_payment['check_vip'] .= ' ' . $nv_Lang->getModule('check_vip_not_payment') . '.';
                    }

                    $duplicate_payment_caregiver_id = $duplicate_payment['caregiver_id'];
                    if ($duplicate_payment_caregiver_id > 0) {
                        $duplicate_payment['caregiver_id'] = isset($array_user_id_users[$duplicate_payment_caregiver_id]['username']) ? nv_show_name_user($array_user_id_users[$duplicate_payment_caregiver_id]['first_name'], $array_user_id_users[$duplicate_payment_caregiver_id]['last_name'], $array_user_id_users[$duplicate_payment_caregiver_id]['username']) : $nv_Lang->getModule('not_caregiver');
                    } else {
                        $duplicate_payment['caregiver_id'] = $nv_Lang->getModule('not_caregiver');
                    }

                    $duplicate_payment['addtime'] = nv_date('H:i d/m/Y', $duplicate_payment['addtime']);

                    $xtpl->assign('DUPLICATE_PAYMENT', $duplicate_payment);
                    $xtpl->parse('ajax.duplicate_payment.loop');
                }
                $xtpl->parse('ajax.duplicate_payment');
            }
        } else {
            $xtpl->parse('ajax.error_duplicate_payment');
        }

        // Đơn hàng dauthau.info
        if (sizeof($array_customs_log) > 0) {
            $xtpl->assign('NUM_CUSTOMS_LOG', sprintf($nv_Lang->getModule('duplicate_customs'), sizeof($array_customs_log)));
            foreach ($array_customs_log as $customs_log) {
                $customs_log['duplicate'] = '';
                $customs_log['link'] = '';
                if (!empty($_tmp_phone) and !empty($customs_log['phone']) and (substr($customs_log['phone'], -9) == substr($_tmp_phone, -9))) {
                    $customs_log['link'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;q=' . $customs_log['phone'];
                    $customs_log['duplicate'] = '<span class="red">' . $customs_log['phone'] . '</span>';
                }

                if (!empty($customs_log['link']) and $customs_log['isset'] != 0) {
                    $customs_log['name'] = '<a href="' . $customs_log['link'] . '">' . $customs_log['name'] . '</a>';
                }

                $customs_log['check_vip'] = '; Đơn hàng';
                if ($customs_log['is_renewal'] == 1) {
                    $customs_log['check_vip'] .= ' gia hạn ';
                } else {
                    $customs_log['check_vip'] .= ' mới ';
                }
                $customs_log['check_vip'] .= $nv_Lang->getModule('vip' . $customs_log['vip']);
                if ($customs_log['status'] == 1) {
                    $customs_log['check_vip'] .= ' đã thanh toán.';
                } else {
                    $customs_log['check_vip'] .= ' chưa thanh toán.';
                }
                if ($customs_log['isset'] == 0) {
                    $customs_log['userdelete'] = isset($customs_log['userdelete']) ? $customs_log['userdelete'] : 0;
                    $userdelete = isset($array_user_id_users[$customs_log['userdelete']]['username']) ? nv_show_name_user($array_user_id_users[$customs_log['userdelete']]['first_name'], $array_user_id_users[$customs_log['userdelete']]['last_name'], $array_user_id_users[$customs_log['userdelete']]['username']) : 'N/A';
                    $customs_log['check_vip'] .= '(Đơn hàng đã bị xóa bởi: ' . $userdelete . ')';
                }

                $customs_log_caregiver_id = !empty($customs_log['order']) ? $customs_log['order']['caregiver_id'] : 0;
                if ($customs_log_caregiver_id > 0) {
                    $customs_log['caregiver_id'] = isset($array_user_id_users[$customs_log_caregiver_id]['username']) ? nv_show_name_user($array_user_id_users[$customs_log_caregiver_id]['first_name'], $array_user_id_users[$customs_log_caregiver_id]['last_name'], $array_user_id_users[$customs_log_caregiver_id]['username']) : $nv_Lang->getModule('not_caregiver');
                }

                $customs_log['addtime'] = nv_date('H:i d/m/Y', $customs_log['addtime']);

                $xtpl->assign('CUSTOMS_LOG', $customs_log);
                $xtpl->parse('ajax.customs_log.loop');
            }
            $xtpl->parse('ajax.customs_log');
        }

        // Leads
        if (sizeof($leads_record) > 0) {
            $xtpl->assign('NUM_LEADS_PHONE', sprintf($nv_Lang->getModule('duplicate_leads_phone'), sizeof($leads_record)));
            foreach ($leads_record as $lead_phone) {
                $lead_phone['caregiverid'] = $lead_phone['caregiver_id'];
                $lead_phone['caregiver_id'] = isset($array_user_id_users[$lead_phone['caregiver_id']]['username']) ? nv_show_name_user($array_user_id_users[$lead_phone['caregiver_id']]['first_name'], $array_user_id_users[$lead_phone['caregiver_id']]['last_name'], $array_user_id_users[$lead_phone['caregiver_id']]['username']) : $nv_Lang->getModule('not_caregiver');
                $lead_phone['source_leads'] = isset($array_groups_leads[$lead_phone['source_leads']]['title']) ? $array_groups_leads[$lead_phone['source_leads']]['title'] : '';
                $lead_phone['status_id'] = $lead_phone['status'];
                $lead_phone['status'] = $array_status[$lead_phone['status']];

                $lead_phone['check_vip'] = '';
                if (isset($lead_phone['order']) and !empty($lead_phone['order'])) {
                    foreach ($lead_phone['order'] as $order) {
                        $lead_phone['check_vip'] = '; Đã tạo đơn hàng VIP ' . $order['vip'] . ', Chờ thanh toán.';
                        if ($order['status'] == 1) {
                            $lead_phone['check_vip'] = '; Đã đăng ký VIP ' . $order['vip'];
                        }
                    }
                }
                $lead_phone['duplicate'] = '';
                $is_open_lead = 0;
                if (!empty($_tmp_phone) and !empty($lead_phone['phone']) and (substr($lead_phone['phone'], -9) == substr($_tmp_phone, -9))) {
                    $lead_phone['duplicate'] = '<span class="red">' . $lead_phone['phone'] . '</span>';
                    $is_open_lead = 1;
                }

                /*
                 * trường hợp mở leads:
                 * - Thời gian chăm sóc gần nhất lớn hơn 3 ngày :
                 * - Quá lịch hẹn, nếu có
                 */
                $open_check = 0;
                // có lịch hẹn
                if ($lead_phone['schedule'] > 0) {
                    // lịch hẹn lớn hơn thời gian update
                    if ($lead_phone['schedule'] > $lead_phone['updatetime']) {
                        // kiểm tra thời hạn của lịch hẹn với thời gian hiện tại
                        if (NV_CURRENTTIME > $lead_phone['schedule']) {
                            /*
                             * Kiểm tra quy trình 3 7 10
                             */
                            $numday = ceil((NV_CURRENTTIME - $lead_phone['schedule']) / 86400);
                            if ($numday <= 3) {
                                if (check_comment(1, $lead_phone['id'], $lead_phone['schedule'], 3) == false) {
                                    $open_check = 1;
                                }
                            } else if ($numday > 3 and $numday <= 7) {
                                if (check_comment(1, $lead_phone['id'], $lead_phone['schedule'], 7) == false) {
                                    $open_check = 1;
                                }
                            } else if ($numday > 7 and $numday <= 10) {
                                if (check_comment(1, $lead_phone['id'], $lead_phone['schedule'], 10) == false) {
                                    $open_check = 1;
                                }
                            } else if ($numday > 10 and $numday <= 40) {
                                if (check_comment(1, $lead_phone['id'], $lead_phone['schedule'], 30) == false) {
                                    $open_check = 1;
                                }
                            }
                            /*
                             * Sau quy trình 3 7 10 sẽ mở ra cho người khác nhận nếu không có update gì trong 3 ngày gần nhất
                             */
                            if ($numday > 40 and $lead_phone['updatetime'] + 259200 <= NV_CURRENTTIME) {
                                $open_check = 1;
                            }
                        }
                    } else {
                        // lịch hẹn nhỏ hơn thời gian update
                        if ($lead_phone['updatetime'] < (NV_CURRENTTIME - 864000)) {
                            // kiểm tra theo update nếu quá 10 ngày
                            $open_check = 1;
                        }
                    }
                } else {
                    /*
                     * Trong 3 ngày làm việc đầu tiên kể từ khi sale nhận lead về chăm sóc
                     * nếu lead không có thông tin cập nhật mới thì lead đó sẽ được mở trên phần Check trùng, nếu tool Nhả lead chưa xử lý đến.
                     */
                    if ($lead_phone['first_time'] == $lead_phone['updatetime']) {
                        if ($lead_phone['first_time'] + 259200 <= NV_CURRENTTIME) {
                            $open_check = 1;
                        }
                    } else {
                        /*
                         * Kiểm tra quy trình 3 7 10
                         */
                        $numday = ceil((NV_CURRENTTIME - $lead_phone['first_time']) / 86400);
                        if ($numday <= 3) {
                            if (check_comment(1, $lead_phone['id'], $lead_phone['first_time'], 3) == false) {
                                $open_check = 1;
                            }
                        } else if ($numday > 3 and $numday <= 7) {
                            if (check_comment(1, $lead_phone['id'], $lead_phone['first_time'], 7) == false) {
                                $open_check = 1;
                            }
                        } else if ($numday > 7 and $numday <= 10) {
                            if (check_comment(1, $lead_phone['id'], $lead_phone['first_time'], 10) == false) {
                                $open_check = 1;
                            }
                        } else if ($numday > 10 and $numday <= 40) {
                            if (check_comment(1, $lead_phone['id'], $lead_phone['first_time'], 30) == false) {
                                $open_check = 1;
                            }
                        }
                        /*
                         * Sau quy trình 3 7 10 sẽ mở ra cho người khác nhận nếu không có update gì trong 3 ngày gần nhất
                         */
                        if ($numday > 40 and $lead_phone['updatetime'] + 259200 <= NV_CURRENTTIME) {
                            $open_check = 1;
                        }
                    }
                }

                if (($is_open_lead == 1 and $lead_phone['affilacate_id'] == 0 and $open_check == 1 and $lead_phone['status_id'] != 2 and $lead_phone['opportunities_id'] == 0) or defined('NV_IS_SPADMIN') or in_array($lead_phone['caregiverid'], $caregiver_id_leads) or $lead_phone['status_id'] == 4 or $lead_phone['status_id'] == 0) {
                    $link_lead = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $lead_phone['id'] . '&is_open=' . $is_open_lead . '&id_open_pre=' . $row['id'] . '&type_open_pre=' . $type;
                    $lead_phone['name'] = '<a href="' . $link_lead . '">' . $lead_phone['name'] . '</a>';
                    $lead_phone['notify_click_lead'] = $nv_Lang->getModule('notify_click_lead');
                }

                $lead_phone['updatetime'] = nv_date('H:i d/m/Y', $lead_phone['updatetime']);
                $xtpl->assign('LEADS_PHONE', $lead_phone);
                $xtpl->parse('ajax.lead_phone.loop');
            }
            $xtpl->parse('ajax.lead_phone');
        }

        // Oppotunities
        if (sizeof($oppor_record) > 0) {
            $xtpl->assign('NUM_OPPOTUNTIES', sprintf($nv_Lang->getModule('duplicate_oppotunities'), sizeof($oppor_record)));
            foreach ($oppor_record as $oppotunities) {
                $oppotunities['caregiverid'] = $oppotunities['caregiver_id'];
                $oppotunities['caregiver_id'] = isset($array_user_id_users[$oppotunities['caregiver_id']]['username']) ? nv_show_name_user($array_user_id_users[$oppotunities['caregiver_id']]['first_name'], $array_user_id_users[$oppotunities['caregiver_id']]['last_name'], $array_user_id_users[$oppotunities['caregiver_id']]['username']) : $nv_Lang->getModule('not_caregiver');
                $oppotunities['status_id'] = $oppotunities['status'];
                $oppotunities['status'] = $array_status_opportunities[$oppotunities['status']];

                $oppotunities['check_vip'] = '';
                if (isset($oppotunities['order']) and !empty($oppotunities['order'])) {
                    foreach ($oppotunities['order'] as $order) {
                        $oppotunities['check_vip'] = '; Đã tạo đơn hàng VIP ' . $order['vip'] . ', Chờ thanh toán.';
                        if ($order['status'] == 1) {
                            $oppotunities['check_vip'] = '; Đã đăng ký VIP ' . $order['vip'];
                        }
                    }
                }
                $oppotunities['duplicate'] = '';
                $is_open_lead = 0;
                if (!empty($_tmp_phone) and !empty($oppotunities['phone']) and (substr($oppotunities['phone'], -9) == substr($_tmp_phone, -9))) {
                    $oppotunities['duplicate'] = '<span class="red">' . $oppotunities['phone'] . '</span>';
                    $is_open_lead = 1;
                }

                /*
                 * trường hợp mở leads:
                 * - Thời gian chăm sóc gần nhất lớn hơn 3 ngày :
                 * - Quá lịch hẹn, nếu có
                 */
                $open_check = 0;
                // có lịch hẹn
                if ($oppotunities['schedule'] > 0) {
                    // lịch hẹn lớn hơn thời gian update
                    if ($oppotunities['schedule'] > $oppotunities['updatetime']) {
                        // kiểm tra thời hạn của lịch hẹn với thời gian hiện tại
                        if (NV_CURRENTTIME > $oppotunities['schedule']) {
                            /*
                             * Kiểm tra quy trình 3 7 10
                             */
                            $numday = ceil((NV_CURRENTTIME - $oppotunities['schedule']) / 86400);
                            if ($numday <= 3) {
                                if (check_comment(2, $oppotunities['id'], $oppotunities['schedule'], 3) == false) {
                                    $open_check = 1;
                                }
                            } else if ($numday > 3 and $numday <= 7) {
                                if (check_comment(2, $oppotunities['id'], $oppotunities['schedule'], 7) == false) {
                                    $open_check = 1;
                                }
                            } else if ($numday > 7 and $numday <= 10) {
                                if (check_comment(2, $oppotunities['id'], $oppotunities['schedule'], 10) == false) {
                                    $open_check = 1;
                                }
                            } else if ($numday > 10 and $numday <= 40) {
                                if (check_comment(2, $oppotunities['id'], $oppotunities['schedule'], 30) == false) {
                                    $open_check = 1;
                                }
                            }
                            /*
                             * Sau quy trình 3 7 10 sẽ mở ra cho người khác nhận nếu không có update gì trong 3 ngày gần nhất
                             */
                            if ($numday > 40 and $oppotunities['updatetime'] + 259200 <= NV_CURRENTTIME) {
                                $open_check = 1;
                            }
                        }
                    } else {
                        // lịch hẹn nhỏ hơn thời gian update
                        if ($oppotunities['updatetime'] < (NV_CURRENTTIME - 864000)) {
                            // kiểm tra theo update nếu quá 10 ngày
                            $open_check = 1;
                        }
                    }
                } else {
                    /*
                     * Trong 3 ngày làm việc đầu tiên kể từ khi sale nhận lead về chăm sóc
                     * nếu lead không có thông tin cập nhật mới thì lead đó sẽ được mở trên phần Check trùng, nếu tool Nhả lead chưa xử lý đến.
                     */
                    if ($oppotunities['first_time'] == $oppotunities['timecreate'] or $oppotunities['first_time'] == $oppotunities['updatetime']) {
                        if ($oppotunities['first_time'] + 259200 <= NV_CURRENTTIME) {
                            $open_check = 1;
                        }
                    } else {
                        /*
                         * Kiểm tra quy trình 3 7 10
                         */
                        $numday = ceil((NV_CURRENTTIME - $oppotunities['first_time']) / 86400);
                        if ($numday <= 3) {
                            if (check_comment(2, $oppotunities['id'], $oppotunities['first_time'], 3) == false) {
                                $open_check = 1;
                            }
                        } else if ($numday > 3 and $numday <= 7) {
                            if (check_comment(2, $oppotunities['id'], $oppotunities['first_time'], 7) == false) {
                                $open_check = 1;
                            }
                        } else if ($numday > 7 and $numday <= 10) {
                            if (check_comment(2, $oppotunities['id'], $oppotunities['first_time'], 10) == false) {
                                $open_check = 1;
                            }
                        } else if ($numday > 10 and $numday <= 40) {
                            if (check_comment(2, $oppotunities['id'], $oppotunities['first_time'], 30) == false) {
                                $open_check = 1;
                            }
                        }
                        /*
                         * Sau quy trình 3 7 10 sẽ mở ra cho người khác nhận nếu không có update gì trong 3 ngày gần nhất
                         */
                        if ($numday > 40 and $oppotunities['updatetime'] + 259200 <= NV_CURRENTTIME) {
                            $open_check = 1;
                        }
                    }
                }

                if (($is_open_lead == 1 and $oppotunities['affilacate_id'] == 0 and $open_check == 1 and $oppotunities['status_id'] != 2) or defined('NV_IS_SPADMIN') or in_array($oppotunities['caregiverid'], $caregiver_id_leads) or $oppotunities['status_id'] == 4 or $oppotunities['status_id'] == 0) {
                    $link_op = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=opportunities_info&amp;id=' . $oppotunities['id'] . '&is_open=' . $is_open_lead . '&id_open_pre=' . $row['id'] . '&type_open_pre=' . $type;
                    $oppotunities['name'] = '<a href="' . $link_op . '">' . $oppotunities['name'] . '</a>';
                    $oppotunities['notify_click_opportunities'] = $nv_Lang->getModule('notify_click_opportunities');
                }

                $oppotunities['updatetime'] = nv_date('H:i d/m/Y', $oppotunities['updatetime']);
                $xtpl->assign('OPPOTUNITIES', $oppotunities);
                $xtpl->parse('ajax.oppotunities.loop');
            }
            $xtpl->parse('ajax.oppotunities');
        }

        // Users
        if (sizeof($array_user_phone) > 0) {
            $xtpl->assign('NUM_USERS_PHONE', sprintf($nv_Lang->getModule('duplicate_users'), sizeof($array_user_phone)));
            foreach ($array_user_phone as $user_email) {
                $user_email['title'] = nv_show_name_user($user_email['first_name'], $user_email['last_name'], $user_email['username']);
                $user_email['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=users&amp;' . NV_OP_VARIABLE . '=memberlist/' . change_alias($user_email['username']) . '-' . $user_email['md5username'];
                $user_email['check_vip'] = '';
                if (isset($user_email['order']) and !empty($user_email['order'])) {
                    foreach ($user_email['order'] as $order) {
                        $user_email['check_vip'] = '; Đã tạo đơn hàng VIP ' . $order['vip'] . ', Chờ thanh toán.';
                        if ($order['status'] == 1) {
                            $user_email['check_vip'] = '; Đã đăng ký VIP ' . $order['vip'];
                        }
                    }
                }
                if (!empty($_tmp_phone) and !empty($user_email['phone']) and (substr($user_email['phone'], -9) == substr($_tmp_phone, -9))) {
                    $user_email['phone'] = '<span class="red">' . $user_email['phone'] . '</span>';
                }
                $user_email['regdate'] = nv_date('H:i d/m/Y', $user_email['regdate']);
                $xtpl->assign('USERS_PHONE', $user_email);
                $xtpl->parse('ajax.user_phone.loop');
            }
            $xtpl->parse('ajax.user_phone');
        }

        if (empty($array_business) and empty($array_profile_dtnet) and empty($array_payment_dtnet) and empty($array_customs_log) and empty($leads_record) and empty($oppor_record) and empty($array_user_phone)) {
            $xtpl->assign('NO_DATA', $nv_Lang->getModule('not_found_data'));
            $xtpl->parse('ajax.no_data');
        }
        $xtpl->parse('ajax');
        $contents = $xtpl->text('ajax');

        include NV_ROOTDIR . '/includes/header.php';
        echo $contents;
        include NV_ROOTDIR . '/includes/footer.php';
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('MODULE_FILE', $module_file);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('PHONE', $phone);
$xtpl->assign('FROMTIME', $fromcall_start);
$xtpl->assign('TOTIME', $tocall_end);
$xtpl->assign('SELECTED_TYPE_VIEW' . $type_view, 'selected="selected"');

foreach ($array_extension_sale as $extension) {
    $full_name = nv_show_name_user($extension['first_name'], $extension['last_name']);
    $xtpl->assign('EXTENSION', [
        'key' => $extension['extension_voicecloud'],
        'selected' => $extension_sale == $extension['extension_voicecloud'] ? 'selected="selected"' : '',
        'full_name' => $full_name,
        'extension_voicecloud' => $extension['extension_voicecloud']
    ]);
    $xtpl->parse('main.search.loop_extension');
}

$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&showheader=' . $showheader;
if (!empty($q)) {
    $base_url .= '&q=' . $q;
}
$base_url .= '&type_view=' . $type_view;
$per_page = 50;
$page = $nv_Request->get_int('page', 'post,get', 1);

// sale thông thường phải xem qua 1 sdt ở check trùngg
if (!defined('NV_IS_SPADMIN') and $is_leader == 0 and empty($callid)) {
    nv_info_die($nv_Lang->getModule('info'), $nv_Lang->getModule('info_notallowed'), $nv_Lang->getModule('info_notallowed'));
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
}

if (empty($error)) {
    if ($type_view == 2) {
        $where = [];
        $show_link = '';
        if (!empty($phone)) {
            $where[] = 'phone = ' . $db->quote($phone);
            $base_url .= '&phone=' . $phone;
        }

        if ($fcall > 0) {
            $where[] = 'calldatetimestart >= ' . $fcall;
            $base_url .= '&fcall=' . $fcall;
        }

        if ($tcall > 0) {
            $where[] = 'calldatetimestart <= ' . $tcall;
            $base_url .= '&tcall=' . $tcall;
        }

        if ($extension_sale > 0) {
            $where[] = 'extension = ' . $extension_sale;
            $base_url .= '&extension_sale=' . $extension_sale;
        }

        if (!preg_match('/^([0-9]{10})$/', $phone) and !preg_match('/^([0-9]{11})$/', $phone)) {
            $show_link = 'hidden';
        }

        if (!empty($callid)) {
            $where[] = 'callid =' . $db->quote($callid);
            $base_url .= '&callid=' . $callid;
        }

        $db->sqlreset()
            ->select('COUNT(*)')
            ->from('' . NV_PREFIXLANG . '_' . $module_data . '_voicecloud');

        if (!empty($where)) {
            $db->where(implode(' AND ', $where));
        }
        $sth = $db->prepare($db->sql());
        $sth->execute();
        $num_items = $sth->fetchColumn();
        $order = 'calldatetimestart DESC';
        $db->select('*')
            ->order($order)
            ->limit($per_page)
            ->offset(($page - 1) * $per_page);
        $sth = $db->prepare($db->sql());
        $sth->execute();

        // Lấy số lượng cuộc gọi của số điện thoại
        $db->select('COUNT(*)')
            ->order($order)
            ->limit($per_page)
            ->offset(($page - 1) * $per_page);
        $sth_1 = $db->prepare($db->sql());
        $sth_1->execute();
        $total_row = $sth_1->fetchColumn();

        $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.detail.generate_page');
        }
        $number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;
        $num_row = 0;
        $xtpl->assign('HIDDEN', $show_link);
        while ($view = $sth->fetch()) {
            $num_row++;
            if ($view['link_s3'] != '' and preg_match('/^20/', $view['link_s3'], $m) and $view['calldatetimestart'] < (NV_CURRENTTIME - (86400 * 90)) and $view['billsec'] > 0) {
                $view['recordingpath'] = 'https://s3.ap-southeast-1.amazonaws.com/dauthau.asia/uploads/voicecloud/' . $view['link_s3'];
            } else if (preg_match('/pbx/', $view['recordingpath']) and $view['calldatetimestart'] > (NV_CURRENTTIME - (86400 * 90))) {
                // các link cũ pbx dc đổi sang portal chỉ tính các cuộc gọi trong 90 ngày trở lại
                /*
                 * https://pbx.voicecloud.vn/play_audio/data/209824f0-70c3-4559-ab3d-4743f66d85f3
                 * https://portal.voicecloud.vn/play_audio/data/209824f0-70c3-4559-ab3d-4743f66d85f3
                 */
                $view['recordingpath'] = str_replace('pbx', 'portal', $view['recordingpath']);
            }
            $view['link_detail'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=voice_cloud&type_view=2&showheader=' . $showheader . '&phone=' . $view['phone'];
            $view['calldatetimestart'] = nv_date('H:i d/m/Y', $view['calldatetimestart']);
            $view['number'] = $number++;
            $view['direction'] = ($view['direction'] == 'Outgoing' or $view['direction'] == 'Outbound') ? $nv_Lang->getModule('goi_ra') : $nv_Lang->getModule('goi_vao');
            $view['thoi_gian'] = $nv_Lang->getModule('time_start') . ': ' . $view['calldatetimestart'] . '</br>' . ($view['calldatetimeend'] > 0 ? ($nv_Lang->getModule('time_finish') . ': ' . nv_date('H:i d/m/Y', $view['calldatetimeend'])) : '');

            $xtpl->assign('VIEW', $view);
            if ($num_row == 1 and empty($callid)) {
                $xtpl->assign('ROWSPAN', $total_row);
                $xtpl->parse('main.detail.rowspan_th');
                $xtpl->parse('main.detail.loop.rowspan');
            }

            if ($view['recordingpath'] != '' and $view['billsec'] > 0) {
                $xtpl->parse('main.detail.loop.recording');
                $xtpl->parse('main.detail.loop.download');
            }
            $xtpl->parse('main.detail.loop');
        }
        $xtpl->parse('main.detail');
    } else {

        // Trang danh sách voicecloud
        $where = [];
        if (!empty($phone)) {
            $where[] = 'phone = ' . $db->quote($phone);
            $base_url .= '&phone=' . $phone;
        }

        if ($fcall > 0) {
            $where[] = 'calldatetimestart >= ' . $fcall;
            $base_url .= '&fromtime=' . $fromcall_start;
        }

        if ($tcall > 0) {
            $where[] = 'calldatetimestart <= ' . $tcall;
            $base_url .= '&totime=' . $tocall_end;
        }

        if ($extension_sale > 0) {
            $where[] = 'extension = ' . $extension_sale;
            $base_url .= '&extension_sale=' . $extension_sale;
        }

        $db->sqlreset()
            ->select('*, MIN(calldatetimestart) as calldatetimestart')
            ->from('' . NV_PREFIXLANG . '_' . $module_data . '_voicecloud')
            ->group('phone')
            ->order('calldatetimestart DESC')
            ->limit($per_page)
            ->offset(($page - 1) * $per_page);

        $_sql = "SELECT phone FROM " . NV_PREFIXLANG . "_" . $module_data . "_voicecloud ";
        if (!empty($where)) {
            $db->where(implode(' AND ', $where));
            $_sql .= " WHERE " . implode(' AND ', $where);
        }

        $_sql .= " GROUP BY phone";
        $num_items_arr = $db->query($_sql)->fetchAll();
        $num_items = sizeof($num_items_arr);
        $sth = $db->prepare($db->sql());
        $sth->execute();

        $number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;

        $_tmp_phone_arr = [];
        $array_voice = [];
        while ($view = $sth->fetch()) {
            $_tmp_phone = $view['phone'];
            if (preg_match('/(\d{9})$/', $view['phone'], $m)) {
                $_tmp_phone = $m[0];
            }
            $_tmp_phone = preg_replace('/[!@#$%^&*()_+\-=\[\]{};:"\\|,.<>\s]/', '', $_tmp_phone);
            $_tmp_phone_arr[] = (int) $_tmp_phone;
            $array_voice[] = $view;
        }

        $where = [];
        $where['AND'][] = [
            '=' => [
                'active' => 1
            ]
        ];

        foreach ($_tmp_phone_arr as $value) {
            $where['OR'][] = [
                '=' => [
                    'phone_search' => $value
                ]
            ];

            $where['OR'][] = [
                'FIND_IN_SET' => [
                    'sub_phone_search' => $value
                ]
            ];
        }

        $params_leads = $leads_record = [];
        $params_leads = [
            'where' => $where
        ];
        if (!empty($array_voice)) {
            $leads_record = getallleads($params_leads, $_tmp_phone_arr, 1, 50);
        }
        foreach ($array_voice as $view) {
            if (!empty($view['phone'])) {
                $_sql = "SELECT name, extension FROM " . NV_PREFIXLANG . "_" . $module_data . "_voicecloud WHERE phone = " . $view['phone'];
                $result = $db->query($_sql);
                $list_chuyen_vien = $result->fetchAll();
                if (!empty($list_chuyen_vien)) {
                    foreach ($list_chuyen_vien as $chuyen_vien) {
                        $xtpl->assign('CHUYEN_VIEN', $chuyen_vien);
                        $xtpl->parse('main.view.loop.chuyen_vien');
                    }
                }
                $xtpl->parse('main.view.loop.status_leads');
            }
            $view['calldatetimestart'] = nv_date('H:i d/m/Y', $view['calldatetimestart']);
            $view['link_detail'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=voice_cloud&type_view=2&showheader=' . $showheader . '&phone=' . $view['phone'];
            $view['number'] = $number++;

            // Chuẩn hóa số điện thoại
            $status_leads = 0;
            $view['link_leads'] = '';
            $view['status_leads'] = $nv_Lang->getModule('not_created_leads');

            $_tmp_phone = $view['phone'];
            if (preg_match('/(\d{9})$/', $view['phone'], $m)) {
                $_tmp_phone = $m[0];
            }
            $_tmp_phone = preg_replace('/[!@#$%^&*()_+\-=\[\]{};:"\\|,.<>\s]/', '', $_tmp_phone);
            $_tmp_phone = (int) $_tmp_phone;

            // Liên kết với leads
            if (isset($leads_record[$_tmp_phone])) {
                $view['status_leads'] = $nv_Lang->getModule('created_leads');
                $status_leads = 1;
                // if (count($_leads) > 1) {
                $view['link_leads'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads&q=' . $view['phone'];
                /*
                 * } else if (count($_leads) == 1){
                 * $view['link_leads'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_info&id=' . $ids . '&showheader=1';
                 * //}
                 */
            }

            $xtpl->assign('VIEW', $view);
            if ($status_leads == 1) {
                $xtpl->parse('main.view.loop.created_leads');
            } else {
                $xtpl->parse('main.view.loop.not_created_leads');
            }
            $xtpl->parse('main.view.loop');
        }
        $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.view.generate_page');
        }
        $xtpl->parse('main.view');
    }
}
if ($showheader) {
    $xtpl->parse('main.search');
}

if (!empty($error)) {
    $xtpl->assign('ERROR', $error);
    $xtpl->parse('main.error');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');
$page_title = $nv_Lang->getModule('voicecloud');

include NV_ROOTDIR . '/includes/header.php';
if ($showheader) {
    echo nv_admin_theme($contents, $showheader);
} else {
    echo $contents;
}

include NV_ROOTDIR . '/includes/footer.php';

function getallleads($params_leads, $_tmp_phone_arr, $page, $per_page, $leads_record = [])
{
    global $module_name, $admin_info;

    $params_leads['page'] = $page;
    $params_leads['per_page'] = $per_page;

    $List = nv_local_api('ListAllLeads', $params_leads, $admin_info['username'], $module_name);
    $ListAllLeads = json_decode($List, true);
    if (isset($ListAllLeads['data'])) {
        foreach ($ListAllLeads['data'] as $value) {
            if (!empty($value['phone_search']) and in_array($value['phone_search'], $_tmp_phone_arr)) {
                $leads_record[$value['phone_search']] = $value;
            } else if (!empty($value['sub_phone_search'])) {
                $sub_phone_search = explode(',', $value['sub_phone_search']);
                foreach ($sub_phone_search as $sp) {
                    if (in_array($sp, $_tmp_phone_arr)) {
                        $leads_record[$sp] = $value;
                    }
                }
            }
        }
        if ($ListAllLeads['total'] > ($page * $per_page)) {
            return getallleads($params_leads, $_tmp_phone_arr, ($page + 1), $per_page, $leads_record);
        } else {
            return $leads_record;
        }
    }
}

function CallAPI($infoAPI, $API, $module_name = 'businesslistings', $api_url = API_DAUTHAUINFO_URL, $api_key = API_DAUTHAUINFO_KEY, $api_secret = API_DAUTHAUINFO_SECRET)
{
    if (isset($infoAPI['page'])) {
        $infoAPI['page'] = intval($infoAPI['page']);
    }

    if (isset($infoAPI['perpage'])) {
        $infoAPI['perpage'] = intval($infoAPI['perpage']);
    }

    if (!empty($infoAPI)) {
        foreach ($infoAPI as $key => $value) {
            $params_customs[$key] = $value;
        }
    }

    $api = new DoApi($api_url, $api_key, $api_secret);

    if (!empty($params_customs)) {
        $api->setModule($module_name)
            ->setLang('vi')
            ->setAction($API)
            ->setData($params_customs);
    } else {
        $api->setModule($module_name)
            ->setLang('vi')
            ->setAction($API);
    }

    $result = $api->execute();
    $error = $api->getError();
    $data = [];
    if (empty($error) and $result['status'] == 'success') {
        $data = $result;
    }
    return $data;
}
