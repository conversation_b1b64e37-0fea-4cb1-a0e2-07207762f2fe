<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('static_renewal');

$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users';
$result = $db->query($sql);
$array_groups_users = array();
while ($row = $result->fetch()) {
    $row['config'] = json_decode($row['config'], true);
    $array_groups_users[$row['userid']] = $row;
}

$array_search = array();
$array_search['admin_id'] = $nv_Request->get_int('admin_id', 'post,get', 0);
$array_search['static_vieweb'] = $nv_Request->get_int('static_vieweb', 'post,get', 0);
$year = $nv_Request->get_int('year', 'post,get', nv_date('Y', NV_CURRENTTIME));
$array_search['type_chart'] = $nv_Request->get_int('type_chart', 'post,get', 1);
$curent_from = '01/' . nv_date('m/Y', NV_CURRENTTIME);
$curent = nv_date('d/m/Y', NV_CURRENTTIME);
$array_search['time_from'] = $nv_Request->get_title('time_from', 'post,get', $curent_from);
$array_search['time_to'] = $nv_Request->get_title('time_to', 'post,get', $curent);

if ($array_search['type_chart'] == '1') {
    $time_search_from = mktime(0, 0, 0, 01, 01, $year);
    $time_search_to = mktime(23, 59, 59, 12, 31, $year);
} else {
    if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_from'], $m)) {
        $time_search_from = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
    } else {
        $time_search_from = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
    }
    if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_to'], $m)) {
        $time_search_to = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
    } else {
        $time_search_to = mktime(23, 59, 59, nv_date('m', NV_CURRENTTIME), nv_date('d', NV_CURRENTTIME), nv_date('Y', NV_CURRENTTIME));
    }
}

/**
 * điều hành chung xem tất
 * Sales nào dc cấu hình xem tất thì dc xem còn lại chỉ xem của mình
 * trường hợp trưởng nhóm thì xem thêm các thành viên của nhóm, $arr_admin_view
 */
$arr_admin_view = $arr_admin_view_tmp = [];
if ($data_config['view_static'] == 2 and !defined('NV_IS_SPADMIN')) {
    if (isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 0) {
        if ($array_groups_users[$admin_info['userid']]['is_leader'] == 1) {
            $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $array_groups_users[$admin_info['userid']]['group_id'] . ' AND userid != ' . $admin_info['userid'];
            $_result = $db->query($_sql);
            while ($_row_groups_users = $_result->fetch()) {
                $arr_admin_view[$_row_groups_users['userid']] = $_row_groups_users['userid'];
            }
            $arr_admin_view[$admin_info['userid']] = $admin_info['userid'];
            $arr_admin_view_tmp = $arr_admin_view;
            if ($array_search['admin_id'] != 0) {
                $arr_admin_view = [];
            }
        } else {
            $array_search['admin_id'] = $admin_info['userid'];
        }
    }
}

$sql = 'SELECT * FROM  ' . NV_PREFIXLANG . '_bidding_orders_general WHERE static_time >= ' . $time_search_from . ' AND static_time <= ' . $time_search_to . '';
if (!empty($arr_admin_view)) {
    $sql .= ' AND admin_id IN (' . implode(',', $arr_admin_view) . ') ';
} else if ($array_search['admin_id'] > 0) {
    $sql .= ' AND admin_id = ' . $array_search['admin_id'];
}

if ($array_search['static_vieweb'] == 1) {
    $sql .= ' AND vip != 99';
}

// loại trừ các gói của admin
$arr_admin = [];
$result_admin = $db->query('SELECT admin_id FROM nv4_authors');
while ($_admin_info = $result_admin->fetch()) {
    $arr_admin[$_admin_info['admin_id']] = $_admin_info['admin_id'];
}

// các tài khoản test mà k muốn thống kê đưa vào đây, lấy theo nhóm test https://id.dauthau.net/qtdtvnds/index.php?language=vi&nv=users&op=groups&userlist=19
$result = $db->query('SELECT userid FROM ' . NV_GROUPS_GLOBALTABLE . '_users WHERE group_id = 19');
while ($_gr_user_info = $result->fetch()) {
    $arr_admin[$_gr_user_info['userid']] = $_gr_user_info['userid'];
}

if (!empty($arr_admin)) {
    $sql .= ' AND userid NOT IN (' . implode(',', $arr_admin) . ')';
}

$sql .= ' ORDER BY static_time ASC';

$_result = $db->query($sql);
$array_vip = $array_vip_renewal = $array_timeline = [];
if ($array_search['type_chart'] == 1) {
    while ($_row = $_result->fetch()) {
        $month = nv_date('m', $_row['static_time']);
        $array_timeline[$month] = $month . '/' . $year;
        $array_vip[$month][$_row['id']] = $_row;
        if ($_row['is_renewal']) {
            $array_vip_renewal[$month][$_row['id']] = $_row;
        }
    }
} else {
    while ($_row = $_result->fetch()) {
        $date = nv_date('d/m/Y', $_row['static_time']);
        $array_timeline[$date] = $date;
        $array_vip[$date][$_row['id']] = $_row;
        if ($_row['is_renewal']) {
            $array_vip_renewal[$date][$_row['id']] = $_row;
        }
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_FILE', $module_file);
$xtpl->assign('TEMPLATE', $global_config['module_theme']);
$xtpl->assign('OP', $op);
$xtpl->assign('SEARCH', $array_search);
$xtpl->assign('STATIC_VIEWEB', $array_search['static_vieweb'] == 1 ? 'checked="checked"' : '');
$xtpl->assign('SR_TYPE_CHART' . $array_search['type_chart'], ' selected="selected"');

if ($array_search['type_chart'] == 1) {
    $xtpl->assign('HIDE_TYPE1', ' style="display: block;"');
    $xtpl->assign('HIDE_TYPE2', ' style="display: none;"');
} else {
    $xtpl->assign('HIDE_TYPE1', ' style="display: none;"');
    $xtpl->assign('HIDE_TYPE2', ' style="display: block;"');
}

$series_line = [];
$series_line['0'] = [
    "name" => 'VIP'
];
foreach ($array_vip as $key => $value) {
    $array_vip[$key] = sizeof($value);
}
$series_line['0']["data"] = array_values($array_vip);
$series_line['1'] = [
    "name" => 'VIP gia hạn'
];
foreach ($array_vip_renewal as $key => $value) {
    $array_vip_renewal[$key] = sizeof($value);
}
$series_line['1']["data"] = array_values($array_vip_renewal);

$xtpl->assign('DATA_LINE', json_encode($series_line));
$xtpl->assign('DATA_TIMELINE', json_encode(array_values(array_unique($array_timeline))));

// Xuất danh sách admin ra
$sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (SELECT admin_id FROM ' . NV_AUTHORS_GLOBALTABLE . ') AND active = 1';
if ($data_config['view_static'] == 2 and !defined('NV_IS_SPADMIN')) {
    if (isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 0) {
        if (!empty($arr_admin_view_tmp)) {
            $sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arr_admin_view_tmp) . ') AND active = 1';
        } else {
            $sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $admin_info['userid'] . ' AND active = 1';
        }
    }
}
$result = $db->query($sql);
while ($_user_info = $result->fetch()) {
    $xtpl->assign('OPTION', array(
        'key' => $_user_info['userid'],
        'title' => nv_show_name_user($_user_info['first_name'], $_user_info['last_name'], $_user_info['username']),
        'selected' => $array_search['admin_id'] == $_user_info['userid'] ? ' selected="selected"' : ''
    ));
    $xtpl->parse('main.loop_admin');
}

for ($i = 2018; $i < 2030; $i++) {
    $xtpl->assign('OPTION', array(
        'key' => $i,
        'title' => $i,
        'selected' => $i == $year ? ' selected="selected"' : ''
    ));
    $xtpl->parse('main.loop_year');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
