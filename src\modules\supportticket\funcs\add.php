<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2023 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 27 Feb 2023 09:02:35 GMT
 */

if (!defined('NV_IS_MOD_SUPPORTTICKET')) {
    die('Stop!!!');
}

if (!defined('NV_IS_USER')) {
    $redirect = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=" . $op, true);
    Header("Location: " . NV_BASE_SITEURL . "index.php?" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($redirect));
    die();
}

$row = $attach_files = [];
$error = $attach_files_old = '';
$row['id'] = $nv_Request->get_int('id', 'post,get', 0);
$type = $nv_Request->get_int('type', 'get', 0); // Điều kiện chọn loại yêu cầu
$cat_id = $nv_Request->get_int('cat_id', 'get', 0);
$link_error = $nv_Request->get_title('link_error', 'get', ''); // Link dữ liệu khi gửi yêu cầu báo lỗi

$userid = $user_info['userid'];
$page_title = ($row['id'] > 0) ? $nv_Lang->getModule('ticket_edit') : $nv_Lang->getModule('ticket_add');

$array_mod_title[] = array(
    'title' => $page_title,
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op, true)
);
$page_title = $page_title . ' - ' . $module_config[$module_name]['sitename'];
$key_words = $module_info['keywords'];

if ($nv_Request->isset_request('submit', 'post')) {
    $row['id'] = $nv_Request->get_int('ticket_id', 'post', 0);
    $row['title'] = nv_substr($nv_Request->get_title('title', 'post', ''), 0, 249);
    $row['content'] = $nv_Request->get_editor('content', '', NV_ALLOWED_HTML_TAGS);
    $row['cat_id'] = $nv_Request->get_int('cat_id', 'post', 0);
    $row['point_price'] = $nv_Request->get_int('point_price', 'post', 0);
    $row['customer_id'] = $userid;
    $row['vip_id'] = $nv_Request->get_int('vip_id', 'post', 0);
    $row['order_id'] = $nv_Request->get_int('order_id', 'post', 0);
    $row['status'] = TicketStatus::Open->value;
    $row['status_client'] = TicketStatusClient::Open->value;
    $row['add_userid'] = $userid;
    $row['add_time'] = NV_CURRENTTIME;
    $row['file_attach'] = $_FILES['file_attach'] ?? [];
    $row['file_attach_existing'] = $nv_Request->get_array('file_attach_existing', 'post', []);
    $row['prefix_lang'] = NV_LANG_DATA == 'en' ? 1 : 0;
    $row['is_paid'] = 0;
    $row['min_point_ai'] = 0;
    $row['min_point_expert'] = 0;
    $row['link_error'] = '';
    if ($row['cat_id'] == 10) { // LYC báo lỗi
        $request_error = $nv_Request->get_title('link_error', 'post', '');
        if (!empty($request_error)) {
            $iv = nv_substr(SUPPORT_TICKET_SECRET_KEY, 0, 16);
            $request_error = strtr($request_error, '-_,', '+/=');
            $request_error = openssl_decrypt($request_error, 'aes-256-cbc', SUPPORT_TICKET_SECRET_KEY, 0, $iv);
            // Kiểm tra đúng domain của dauthau.asia
            if (!empty($request_error) && parse_url($request_error, PHP_URL_HOST) === parse_url(URL_DTINFO, PHP_URL_HOST)) {
                $row['link_error'] = $request_error;
            } else {
                $error = $nv_Lang->getModule('error_link_is_incorrect'); // Trường hợp cố tình thay đổi value của link_error thì báo lỗi
            }
        }
    }

    $list_vips = get_user_vips($userid);
    $list_orders = get_user_orders($userid);
    $lead_cagiver = get_lead_cagiver($userid);

    if (empty(trim($row['content'])) || preg_match('/^<p>(?:&nbsp;|\s)*<\/p>(<p>(?:&nbsp;|\s)*<\/p>)*$/', $row['content'])) {
        $error = $nv_Lang->getModule('error_required_content');
    } elseif (strlen($row['content']) <= 10) {
        $error = $nv_Lang->getModule('error_strlen');
    } elseif (empty($row['cat_id'])) {
        $error = $nv_Lang->getModule('error_required_cat_id');
    } elseif (!isset($array_active_cats[$row['cat_id']])) {
        //Kiểm tra bộ phận có khả dụng ko
        $error = $nv_Lang->getModule('error_cant_save') . ' 001';
    }  elseif ($row['vip_id'] > 0 && !isset($list_vips[$row['vip_id']])) {
        //Kiểm tra vip có thuộc user hiện tại ko
        $error = $nv_Lang->getModule('error_cant_save') . ' 002';
    } elseif ($row['order_id'] > 0 && !isset($list_orders[$row['order_id']])) {
        //Kiểm tra đơn hàng có thuộc user hiện tại ko
        $error = $nv_Lang->getModule('error_cant_save') . ' 003';
    }

    // Nếu không nhập title thì lấy 1 phần content làm tiêu đề
    if (empty($row['title'])) {
        $row['title'] = extract_first_sentence(nv_only_text($row['content']));
    }

    if (nv_strlen($row['title']) > $module_config[$module_name]['max_title_length']) {
        $error = $nv_Lang->getModule('error_length_title', $module_config[$module_name]['max_title_length']);
    }

    // Nếu như là `Yêu cầu tính phí` thì khi lưu xong sẽ ở trạng thái lưu nháp
    if (isset($array_active_cats[$row['cat_id']]) && $array_active_cats[$row['cat_id']]['is_point'] === 1) {
        $row['status'] = TicketStatus::Draft->value;
        $row['status_client'] = TicketStatusClient::Draft->value;
        $row['is_paid'] = 1;
        $row['min_point_ai'] = $array_active_cats[$row['cat_id']]['point_ai'];
        $row['min_point_expert'] = $array_active_cats[$row['cat_id']]['point_price'];
    }

    if (!empty($row['file_attach'])) {
        $count_files = count($row['file_attach']['name']);
        if ($count_files > NV_ATTACH_LIMITED) {
            $error_code = '1004';
            $error = sprintf($nv_Lang->getModule('error_upload_limited'), NV_ATTACH_LIMITED);
        } else {
            for ($i = 0; $i < $count_files; $i++) {
                $file['name'] = $row['file_attach']['name'][$i];
                $file['type'] = $row['file_attach']['type'][$i];
                $file['size'] = $row['file_attach']['size'][$i];
                $file['tmp_name'] = $row['file_attach']['tmp_name'][$i];
                $file['error'] = $row['file_attach']['error'][$i];
                $attach_files[] = $file;
            }
        }
    }
    $old_file = '';
    $row_old = $db->query('SELECT file_attach FROM ' . TB_TICKET_ROW . ' WHERE id=' . $row['id'] . ' AND delete_time=0')->fetch();
    if ($row_old) {
        $old_file = $row_old['file_attach'];
    }
    $uploaded = multi_upload($attach_files, $old_file, $row['file_attach_existing']);
    if ($uploaded['status'] == 'error') {
        $error = $uploaded['message'];
    }

    $assignee_ids = [];
    if (!empty($row['cat_id']) && $array_active_cats[$row['cat_id']]['assignee_sale'] == 1) {
        if ($row['vip_id'] > 0) {
            if (isset($list_vips[$row['vip_id']]) && $list_vips[$row['vip_id']]['caregiver'] > 0) {
                $assignee_ids[] = $list_vips[$row['vip_id']]['caregiver'];
            }
        }
        if ($row['order_id'] > 0) {
            if (isset($list_orders[$row['order_id']]) && $list_orders[$row['order_id']]['caregiver'] > 0) {
                $assignee_ids[] = $list_orders[$row['order_id']]['caregiver'];
            }
        }
        //Nếu không dùng gói nào và có lead nóng thì assignee cho người chăm lead
        if ($lead_cagiver > 0 && empty($assignee_ids)) {
            $assignee_ids[] = $lead_cagiver;
        }

        $assignee_ids = array_unique($assignee_ids);
    }

    //Loại bỏ những tài khoản quản trị đã bị khóa
    foreach ($assignee_ids as $key => $value) {
        if (!isset($array_admin_users[$value])) {
            unset($assignee_ids[$key]);
        }
    }

    //Lưu cả danh sách nhân viên chăm sóc để hỗ trợ việc thông báo
    $assignee_to = !empty($assignee_ids) ? implode(',', $assignee_ids) : '';

    if (empty($error)) {
        if ($row['id'] == 0) {
            $sql = 'INSERT INTO ' . TB_TICKET_ROW . '
                    (title, content, cat_id, customer_id, vip_id, order_id, assignee_to, file_attach, status, status_client, point_price, min_point_ai, min_point_expert, is_paid, add_userid, add_time, activity_time, customer_edit_time, prefix_lang, link_error)
                    VALUES (:title, :content, :cat_id, :customer_id, :vip_id, :order_id, :assignee_to, :file_attach, :status, :status_client, :point_price, :min_point_ai, :min_point_expert, :is_paid, :add_userid, :add_time, :activity_time, :customer_edit_time, :prefix_lang, :link_error)';
        } else {
            $sql = 'UPDATE ' . TB_TICKET_ROW . ' SET
                    title = :title, content = :content, cat_id = :cat_id, vip_id = :vip_id, order_id = :order_id, assignee_to = :assignee_to, status = :status, status_client = :status_client,
                    is_paid = :is_paid ,point_price = :point_price, min_point_ai = :min_point_ai, min_point_expert = :min_point_expert, edit_time = :edit_time, activity_time = :activity_time,  customer_edit_time = :customer_edit_time, prefix_lang = :prefix_lang, file_attach = :file_attach WHERE id = :id';
        }

        $stmt = $db->prepare($sql);
        $stmt->bindParam(':title', $row['title'], PDO::PARAM_STR);
        $stmt->bindParam(':content', $row['content'], PDO::PARAM_STR, strlen($row['content']));
        $stmt->bindParam(':cat_id', $row['cat_id'], PDO::PARAM_INT);
        $stmt->bindParam(':vip_id', $row['vip_id'], PDO::PARAM_INT);
        $stmt->bindParam(':order_id', $row['order_id'], PDO::PARAM_INT);
        $stmt->bindParam(':assignee_to', $assignee_to, PDO::PARAM_STR);
        $stmt->bindParam(':status', $row['status'], PDO::PARAM_INT);
        $stmt->bindParam(':status_client', $row['status_client'], PDO::PARAM_INT);
        $stmt->bindParam(':is_paid', $row['is_paid'], PDO::PARAM_INT);
        $stmt->bindParam(':point_price', $row['point_price'], PDO::PARAM_INT);
        $stmt->bindParam(':min_point_ai', $row['min_point_ai'], PDO::PARAM_INT);
        $stmt->bindParam(':min_point_expert', $row['min_point_expert'], PDO::PARAM_INT);
        $stmt->bindParam(':activity_time', $row['add_time'], PDO::PARAM_INT);
        $stmt->bindValue(':customer_edit_time', NV_CURRENTTIME, PDO::PARAM_INT);
        $stmt->bindParam(':prefix_lang', $row['prefix_lang'], PDO::PARAM_INT);

        if (empty($error) && $row['id'] == 0) {
            $stmt->bindParam(':add_userid', $userid, PDO::PARAM_INT);
            $stmt->bindParam(':add_time', $row['add_time'], PDO::PARAM_INT);
            $stmt->bindParam(':customer_id', $row['customer_id'], PDO::PARAM_INT);
            $stmt->bindParam(':link_error', $row['link_error'], PDO::PARAM_STR);
        } else {
            $stmt->bindParam(':id', $row['id'], PDO::PARAM_INT);
            $stmt->bindParam(':edit_time', $row['add_time'], PDO::PARAM_INT);
        }

        if (isset($uploaded) && $uploaded['status'] == 'success') {
            $row['file_attach'] = $uploaded['data'] ?? [];
        }
        $row['file_attach'] = json_encode($row['file_attach']);
        $stmt->bindParam(':file_attach', $row['file_attach'], PDO::PARAM_STR);

        $exc = $stmt->execute();
        if ($exc) {
            $nv_Cache->delMod($module_name);

            if (empty($error) && $row['id'] == 0) {
                $row['id'] = $db->lastInsertId();
                // Lưu vào bảng lịch sử hoạt động của khách - từ 0h hnay đến 0h hôm sau
                $_tomorrow = strtotime('tomorrow'); // 0h hôm sau
                $_expired = $_tomorrow - NV_CURRENTTIME;
                $time_set = $nv_Request->get_int($module_name . '_support_ai_' . $user_info['userid'], 'cookie', 0);
                if ($time_set == 0) {
                    $noi_dung = $nv_Lang->getModule('support_ai');
                    $sql = 'INSERT INTO nv4_history_activity_custom (userid, content, time_click) VALUES
                        (' . $user_info['userid'] . ', ' . $db->quote($noi_dung) . ', ' . NV_CURRENTTIME . ')';
                    $exc = $db->query($sql);
                    if ($exc) {
                        $nv_Request->set_Cookie($module_name . '_support_ai_' . $user_info['userid'], NV_CURRENTTIME, $_expired);
                    }
                }
                $log_key = LogKey::CustomerInsert->value;
                $log = [
                    LogKey::CustomerInsert->getLabel(),
                    [
                        $nv_Lang->getModule('log_insert_ticket_info'),
                    ],
                ];
            } else {
                $log_key = LogKey::CustomerUpdate->value;
                $log = [
                    LogKey::CustomerUpdate->getLabel(),
                    [
                        $nv_Lang->getModule('log_update_ticket_info'),
                    ],
                ];
            }

            if (!empty($row['id']) && !empty($userid)) {
                add_ticket_logs($userid, $log_key, $log, $row['id']);
            }

            // Nếu là ticket thu phí thì chỉ gửi mail cho client => Khi hỏi chuyên gia thì mới gửi mail cho chuyên gia
            if (empty($error) && $row['status'] != TicketStatus::Draft->value) {
                if (in_array($row['cat_id'], [10, 11])) {
                    ticket_notification_report_ideas_to_admin($row);
                } elseif (sizeof($assignee_ids) > 0) {
                    foreach ($assignee_ids as $caregiver_id) {
                        ticket_notification_to_caregiver($row, $caregiver_id, true);
                    }
                } else {
                    ticket_notification_to_admin($row);
                }
            }
        } else {
            if (isset($uploaded) && $uploaded['status'] == 'success') {
                multi_upload_unlink($uploaded['dir']);
            }
        }

        if ($row['is_paid'] == 0) {
            if (in_array($row['cat_id'], [10, 11])) {
                ticket_notification_report_ideas_to_user($row);
            } else {
                ticket_notification_to_user($row);
            }
        }
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . ($row['is_paid'] == 1 ? $module_info['alias']['detail_paid'] : $module_info['alias']['detail'] ) . '&id=' . $row['id']);
    } elseif (isset($uploaded) && $uploaded['status'] == 'success') {
        multi_upload_unlink($uploaded['dir']);
    }
} elseif (!empty($row['id'])) {
    $row = $db->query('SELECT * FROM ' . TB_TICKET_ROW . ' WHERE id=' . $row['id'] . ' AND delete_time=0')->fetch();
    if (empty($row) || $row['status_client'] != TicketStatusClient::Draft->value) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
    }
    $attach_files_old = $row['file_attach'];
} else {
    $row['id'] = 0;
    $row['title'] = '';
    $row['content'] = '';
    $row['cat_id'] = 0;
    if ($type == 1) {
        // Lấy thông tin loại yêu cầu tính phí
        foreach ($array_active_cats as $key => $cat) {
            if ($cat['is_point'] == 1) {
                $row['cat_id'] = $cat['cat_id'];
                break;
            }
        }
    } elseif (isset($array_active_cats[$cat_id])) {
        $row['cat_id'] = $array_active_cats[$cat_id]['cat_id'];
    }
    $row['link_error'] = $link_error;
    $row['customer_id'] = 0;
    $row['vip_id'] = 0;
    $row['order_id'] = 0;
    $row['label_ids'] = '';
    $row['assignee_to'] = '';
    $row['file_attach'] = '';
    $row['status'] = 0;
    $row['status_client'] = 0;
}

if ($nv_Request->isset_request('getcatid', 'post')) {
    $cat_id = $nv_Request->get_int('cat_id', 'post', 0);
    $sql_query = $db->query('SELECT cat_id, is_customer FROM ' . TB_TICKET_CAT . ' WHERE cat_id=' . $cat_id)->fetch();
    nv_jsonOutput($sql_query);
}

$get_user_vips = get_user_vips($userid);
$get_user_orders = get_user_orders($userid);

$contents = nv_theme_supportticket_add($row, $attach_files_old, $error, $get_user_vips, $get_user_orders);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
