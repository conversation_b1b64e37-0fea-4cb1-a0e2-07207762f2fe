<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>

<div class="panel">
	<form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline">
		<input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}"> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}"> <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}">
		
		<div class="row">
			<div class="col-md-8 text-right">
				<select class="form-control" name="type">
					<!-- BEGIN: loop_type -->
					<option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
					<!-- END: loop_type -->
				</select>
				<select class="form-control" name="type_chart">
					<option value="1" {TYPE_CHART1}>{LANG.type_chart1}</option>
					<option value="2" {TYPE_CHART2}>{LANG.type_chart2}</option>
				</select>
			</div>
			<div class="col-md-8">
				<div class="form-group">
					<label>{LANG.from_date}:</label> <input class="form-control w100 uidatepicker" type="text" value="{ARRAY_SEARCH.time_from}" name="time_from" maxlength="10" autocomplete="off">
				</div>
				<div class="form-group">
					<label>{LANG.to}:</label> <input class="form-control w100 uidatepicker" type="text" value="{ARRAY_SEARCH.time_to}" name="time_to" maxlength="10" autocomplete="off">
				</div>
			</div>
			<div class="col-md-8">
				<input class="btn btn-primary" type="submit" value="{LANG.search_submit}">
			</div>
		</div>
		
		<div class="row row-compare">
			<div class="col-md-8">
				<p class="text-right" style="margin: 5px 0 0;">
					<input
						<!-- BEGIN: compare_checked -->checked<!-- END: compare_checked -->
						type="checkbox"
						id="compareMode"
						name="compare_mode"
						value="1"
						onchange="handleCheckCompare()"
					>
					<label for="compareMode"> So sánh với</label>
				</p>
			</div>
			<div class="col-md-8 date-range2 <!-- BEGIN: compared -->compared<!-- END: compared -->">
				<div class="form-group">
					<label>{LANG.from_date}:</label> <input class="form-control w100 uidatepicker" type="text" value="{ARRAY_SEARCH.time2_from}" name="time2_from" maxlength="10" autocomplete="off">
				</div>
				<div class="form-group">
					<label>{LANG.to}:</label> <input class="form-control w100 uidatepicker" type="text" value="{ARRAY_SEARCH.time2_to}" name="time2_to" maxlength="10" autocomplete="off">
				</div>
			</div>
		</div>
	</form>
</div>

<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/chart/chart.js"></script>
<div class="row">
	<div id="canvas-holder" class="col-md-24">
		<canvas id="chart-area" width="800" height="400"></canvas>
	</div>
</div>
<style>
	.row-compare {
		height: 35px;
	}
	.date-range2 {
		display: none;
	}
	.date-range2.compared {
		display: block;
	}
</style>

<script>
	window.chartColors = {
		red : 'rgb(255, 99, 132)',
		orange : 'rgb(255, 159, 64)',
		yellow : 'rgb(255, 205, 86)',
		green : 'rgb(75, 192, 192)',
		blue : 'rgb(54, 162, 235)',
		purple : 'rgb(153, 102, 255)',
		grey : 'rgb(201, 203, 207)'
	};
</script>

<script type="text/javascript">
	var isCompareMode = false;
</script>

<!-- BEGIN: compare_mode -->
<script type="text/javascript">
	var isCompareMode = true;
</script>
<!-- END: compare_mode -->

<script type="text/javascript">
	var lineChartData = {
		labels : [ {LABEL} ],
		datasets : [
			{
				label : 'Leads',
				borderColor : window.chartColors.yellow,
				backgroundColor : window.chartColors.yellow,
				fill : false,
				yAxisID: 'leads',
				data : [ {DATA_LEADS} ]
			},
			{
				label : 'Cơ hội kinh doanh',
				borderColor : window.chartColors.blue,
				backgroundColor : window.chartColors.blue,
				fill : false,
				yAxisID: 'oppotunities',
				data : [ {DATA_OPPOTUNITIESs} ]
			},
			{
				label : 'User đăng ký',
				borderColor : window.chartColors.red,
				backgroundColor : window.chartColors.red,
				fill : false,
				yAxisID: 'users',
				data : [ {DATA} ]
			},
			{
				label : 'VIP đăng ký',
				borderColor : window.chartColors.green,
				backgroundColor : window.chartColors.green,
				fill : false,
				yAxisID: 'vips',
				data : [ {DATA_VIPS} ]
			},
			{
				label : 'VIEWEB',
				borderColor : window.chartColors.grey,
				backgroundColor : window.chartColors.grey,
				fill : false,
				yAxisID: 'viewebs',
				data : [ {DATA_VIEWEB} ]
			},
			{
				label : 'Doanh số',
				borderColor : window.chartColors.purple,
				backgroundColor : window.chartColors.purple,
				fill : false,
				yAxisID: 'sales',
				data : [ {DATA_SALES} ]
			}
		]
	};
	
	if (isCompareMode) {
		lineChartData.datasets = [
			{
				label : 'Leads',
				borderColor : window.chartColors.yellow,
				backgroundColor : window.chartColors.yellow,
				fill : false,
				yAxisID: 'leads',
				data : [ {DATA_LEADS} ]
			},
			{
				label : 'Leads 2',
				borderDash: [3, 3],
				borderColor : window.chartColors.yellow,
				backgroundColor : window.chartColors.yellow,
				fill : false,
				yAxisID: 'leads',
				data : [ {DATA_LEADS_2} ]
			},
			{
				label : 'Cơ hội kinh doanh',
				borderColor : window.chartColors.blue,
				backgroundColor : window.chartColors.blue,
				fill : false,
				yAxisID: 'oppotunities',
				data : [ {DATA_OPPOTUNITIESs} ]
			},
			{
				label : 'Cơ hội kinh doanh 2',
				borderDash: [3, 3],
				borderColor : window.chartColors.blue,
				backgroundColor : window.chartColors.blue,
				fill : false,
				yAxisID: 'oppotunities',
				data : [ {DATA_OPPOTUNITIESs_2} ]
			},
			{
				label : 'User đăng ký',
				borderColor : window.chartColors.red,
				backgroundColor : window.chartColors.red,
				fill : false,
				yAxisID: 'users',
				data : [ {DATA} ]
			},
			{
				label : 'User đăng ký 2',
				borderDash: [3, 3],
				borderColor : window.chartColors.red,
				backgroundColor : window.chartColors.red,
				fill : false,
				yAxisID: 'users',
				data : [ {DATA_2} ]
			},
			{
				label : 'VIP đăng ký',
				borderColor : window.chartColors.green,
				backgroundColor : window.chartColors.green,
				fill : false,
				yAxisID: 'vips',
				data : [ {DATA_VIPS} ]
			},
			{
				label : 'VIP đăng ký 2',
				borderDash: [3, 3],
				borderColor : window.chartColors.green,
				backgroundColor : window.chartColors.green,
				fill : false,
				yAxisID: 'vips',
				data : [ {DATA_VIPS_2} ]
			},
			{
				label : 'VIEWEB',
				borderColor : window.chartColors.grey,
				backgroundColor : window.chartColors.grey,
				fill : false,
				yAxisID: 'viewebs',
				data : [ {DATA_VIEWEB} ]
			},
			{
				label : 'VIEWEB 2',
				borderDash: [3, 3],
				borderColor : window.chartColors.grey,
				backgroundColor : window.chartColors.grey,
				fill : false,
				yAxisID: 'viewebs',
				data : [ {DATA_VIEWEB_2} ]
			},
			{
				label : 'Doanh số',
				borderColor : window.chartColors.purple,
				backgroundColor : window.chartColors.purple,
				fill : false,
				yAxisID: 'sales',
				data : [ {DATA_SALES} ]
			},
			{
				label : 'Doanh số 2',
				borderDash: [3, 3],
				borderColor : window.chartColors.purple,
				backgroundColor : window.chartColors.purple,
				fill : false,
				yAxisID: 'sales',
				data : [ {DATA_SALES_2} ]
			}
		];
	}
</script>

<script type="text/javascript">
	function handleCheckCompare() {
		$('.date-range2').toggle();
	}
	
	function handleClick(evt, item, legend) {
		const currentColor = item.fillStyle;
		const index = item.datasetIndex;
		const ci = legend.chart;
		
		let index2 = -1;
		let item2;
		
		for (let i = 0; i < legend.legendItems.length; i++) {
			const legendItem = legend.legendItems[i];
			if (currentColor == legendItem.fillStyle && legendItem.datasetIndex != index) {
				index2 = legendItem.datasetIndex;
				item2 = legendItem;
				break;
			}
		}
		
		if (ci.isDatasetVisible(index)) {
			ci.hide(index);
			ci.hide(index2);
			item.hidden = true;
			item2.hidden = true;
		} else {
			ci.show(index);
			ci.show(index2);
			item.hidden = false;
			item2.hidden = false;
		}
	}

	window.onload = function() {
		var ctx = document.getElementById('chart-area').getContext('2d');
		var lineChart = new Chart(ctx,{
			type: 'line',
			data: lineChartData,
			options: {
				responsive: true,
				interaction: {
					mode: 'index',
					intersect: false,
				},
				stacked: false,
				plugins: {
						title: {
							display: false,
							text: 'Biểu đồ tăng trưởng'
						},
						legend: {
							display: true,
							position: 'top',
							onClick: isCompareMode ? handleClick : Chart.defaults.plugins.legend.onClick,
						}
					},
				scales: {
					users: {
						type: 'linear',
						display: true,
						position: 'left',
						grid: {
							drawOnChartArea: false,
						},
						title: {
							display: true,
							text: 'User đăng ký'
						}
					},
					oppotunities: {
						type: 'linear',
						display: true,
						position: 'left',
						grid: {
							drawOnChartArea: false,
						},
						title: {
							display: true,
							text: 'Cơ hội kinh doanh'
						}
					},
					leads: {
						type: 'linear',
						display: true,
						position: 'left',
						grid: {
							drawOnChartArea: false,
						},
						title: {
							display: true,
							text: 'Leads'
						}
					},
					vips: {
						type: 'linear',
						display: true,
						position: 'right',
						grid: {
							drawOnChartArea: false,
						},
						title: {
							display: true,
							text: 'VIP đăng ký'
						}
					},
					viewebs: {
						type: 'linear',
						display: true,
						position: 'right',
						grid: {
							drawOnChartArea: false,
						},
						title: {
							display: true,
							text: 'VIEWEB'
						}
					},
					sales: {
						type: 'linear',
						display: true,
						position: 'right',
						grid: {
							drawOnChartArea: false,
						},
						title: {
							display: true,
							text: 'Doanh số'
						}
					},
				}
			},
		})
	};

	$(document).ready(function() {
		$('.uidatepicker').datepicker({
			showOn : "both",
			dateFormat : "dd/mm/yy",
			changeMonth : true,
			changeYear : true,
			showOtherMonths : true,
			buttonImage : nv_base_siteurl + "assets/images/calendar.gif",
			buttonImageOnly : true
		});
	});
</script>
<!-- END: main -->