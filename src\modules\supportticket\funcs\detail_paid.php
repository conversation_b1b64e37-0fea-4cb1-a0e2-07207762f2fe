<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2023 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 27 Feb 2023 09:02:35 GMT
 */
// Func xử lý

if (!defined('NV_IS_MOD_SUPPORTTICKET')) {
    die('Stop!!!');
}

if (!defined('NV_IS_USER')) {
    $redirect = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=" . $module_info['alias']['detail_paid'], true);
    Header("Location: " . NV_BASE_SITEURL . "index.php?" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($redirect));
    die();
}

$array_data = $message_log = $attach_files = [];

$ticket_id = $nv_Request->get_int('id', 'get,post', 0);
$row = $db->query('SELECT * FROM ' . TB_TICKET_ROW . ' WHERE id=' . $ticket_id . ' AND is_paid=1 AND delete_time=0')->fetch();
if (empty($row) || $row['customer_id'] != $user_info['userid']) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
} else {
    $array_data = $row;
    //Câu trả lời của AI nếu khách có chọn hỏi AI
    $comment_ai = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $array_data['id'] . ' AND parent=0 AND comment_type=' . CommentType::AI->value)->fetch();
    if (!empty($comment_ai)) {
        //Yêu cầu bổ sung hiện tại của KH đến AI(nếu có)
        $question_ai_additional = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $array_data['id'] . ' AND parent=' . $comment_ai['log_id'] . ' AND comment_type=' . CommentType::Customer->value)->fetch();
        if (!empty($question_ai_additional)) {
            //Câu trả lời bổ sung của AI cho câu hỏi bổ sung hiện tại(nếu có)
            $comment_ai_additional = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $array_data['id'] . ' AND parent=' . $question_ai_additional['log_id'] . ' AND comment_type=' . CommentType::AI->value)->fetch();
        }
    }
}

//Cho phép Editor luôn hiển thị trên trang này
if (!defined('NV_EDITOR')) {
    define('NV_EDITOR', 'ckeditor5-classic');
}
if (defined('NV_EDITOR')) {
    require_once NV_ROOTDIR . '/' . NV_EDITORSDIR . '/' . NV_EDITOR . '/nv.php';
}
$allow_editor = (defined('NV_EDITOR') and nv_function_exists('nv_aleditor')) ? true : false;

if (!defined('CKEDITOR') and $allow_editor) {
    define('CKEDITOR', true);
    $my_head .= '<script type="text/javascript" src="' . NV_STATIC_URL . NV_EDITORSDIR . '/ckeditor/ckeditor.js?t=' . $global_config['timestamp'] . '"></script>';
}

//Thông tin điểm hiện có của người dùng
if (!class_exists('nukeviet_points')) {
    require_once NV_ROOTDIR . '/modules/points/points.class.php';
}
$nv_points = new nukeviet_points();
$customs_points = $nv_points->my_point($user_info['userid']);

//Ghi nhận và xử lý lựa chọn hỏi AI/Chuyên gia của người dùng
if ($nv_Request->get_title('token_selection', 'post', '') == NV_CHECK_SESSION && $array_data['status'] != TicketStatus::Close->value) {
    $ai_support = $nv_Request->get_int('ai_support', 'post', 0);
    $expert = $nv_Request->get_int('expert', 'post', 0);
    if ($ai_support == 1 && $expert == 1) {
        $offer_point_expert = $nv_Request->get_int('suggest_both_point', 'post', 0);
        $sent_selection = 3;
    } elseif ($expert == 1) {
        $offer_point_expert = $nv_Request->get_int('suggest_expert_point', 'post', 0);
        $sent_selection = ($array_data['ask_ai'] == 1) ? 3 : 2;
    } else {
        $offer_point_expert = 0;
        $sent_selection = ($array_data['ask_expert'] == 1) ? 3 : 1;
    }
    // Cập nhật trạng thái
    $status = TicketStatusClient::Open->value; // Đã gửi
    $status_client = TicketStatusClient::Open->value; // Đã gửi

    //Cảnh báo nếu không đủ điểm trả cho AI
    if ($ai_support == 1 && $customs_points['point_total'] < $array_data['min_point_ai']) {
        nv_jsonOutput([
            'success' => false,
            'code' => 1001,
            'message' => $nv_Lang->getModule('notify_insufficient_points', NV_BASE_SITEURL . NV_LANG_DATA . '/points/#muadiem'),
        ]);
    }

    //Không cho phép nhập điểm bé hơn điểm tối thiểu
    if ($expert == 1 &&  $offer_point_expert < $array_data['min_point_expert']) {
        nv_jsonOutput([
            'success' => false,
            'code' => 1002,
            'message' => $nv_Lang->getModule('notify_offer_point_expert', $array_data['min_point_expert']),
        ]);
    }

    //Update lựa chọn người dùng đã chọn
    if ($ai_support == 1 && $array_data['ask_ai'] == 0 || $expert == 1 && $array_data['ask_expert'] == 0) {
        $query = 'UPDATE ' . TB_TICKET_ROW . '
            SET ask_ai = IF(' . $ai_support .' = 1, 1, ask_ai), ask_expert = IF(' . $expert .' = 1, 1, ask_expert), offer_point_expert = IF(' . $offer_point_expert .' > 0, ' . $offer_point_expert . ', offer_point_expert),
            status =  ' . $status . ', status_client = ' . $status_client . ' WHERE id=' . $array_data['id'];
        $db->query($query);

        //Tạo cmt của AI (status chưa tl)
        $reply_ai = false;
        if ($ai_support == 1 && $array_data['min_point_ai'] > 0) {//Lưu ý: Nếu cấu hình AI = 0 thì AI sẽ ko phản hồi hay tạo comment
            //Tạo comment
            if (empty($comment_ai)) {
                $create_comment = $db->query('INSERT INTO ' . TB_TICKET_LOG . ' (ticket_id, comment_type, status, payment_status, add_time, is_paid, ticket_add_time) VALUES (' . $array_data['id'] . ',' . CommentType::AI->value . ',' . CommentStatus::Process->value . ',' . PaymentStatus::Process->value . ',' . NV_CURRENTTIME . ',' . 1 . ',' . $array_data['add_time'] . ')');
                if ($create_comment) {
                    $comment_ai = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $array_data['id'] . ' AND comment_type=' . CommentType::AI->value)->fetch();
                }
            }

            //Phản hồi về frontend cho phép hỏi AI
            if (!empty($comment_ai) && $comment_ai['payment_status'] == PaymentStatus::Process->value) {
                $reply_ai = true;
            }
        }

        $this_ticket = $db->query('SELECT * FROM ' . TB_TICKET_ROW . ' WHERE id=' . $ticket_id)->fetch();
        if ($ai_support == 1 && $expert == 1) {
            notification_selection_to_customer($this_ticket, 3);
            notification_selection_to_expert($this_ticket);
            add_ticket_logs($this_ticket['customer_id'], LogKey::CustomerSendBoth->value, [LogKey::CustomerSendBoth->getLabel()], $this_ticket['id']);
        } elseif ($expert == 1) {
            notification_selection_to_customer($this_ticket, 2);
            notification_selection_to_expert($this_ticket);
            add_ticket_logs($this_ticket['customer_id'], LogKey::CustomerSendExpert->value, [LogKey::CustomerSendExpert->getLabel()], $this_ticket['id']);
        } elseif ($ai_support == 1) {
            notification_selection_to_customer($this_ticket, 1);
            notification_selected_ai_to_expert($this_ticket);
            add_ticket_logs($array_data['customer_id'], LogKey::CustomerSendAi->value, [LogKey::CustomerSendAi->getLabel()], $array_data['id']);
        }

        nv_jsonOutput([
            'success' => true,
            'data' => [
                'disabled_ai' => ($ai_support == 1 || $array_data['ask_ai']) ? true : false,
                'disabled_expert' => ($expert == 1 || $array_data['ask_expert']) ? true : false,
                'sent_selection' => $sent_selection,
                'comment_id' => $comment_ai['log_id'],
                'comment_time' => nv_date('H:i:s d/m/Y', $comment_ai['add_time']),
                'reply_ai' => $reply_ai
            ]
        ]);
    }
}
//AI trả lời câu hỏi, hoàn thành việc trả lời thì lưu lại câu trả lời, ghi log và trừ điểm
$question = $nv_Request->get_title('question', 'get', '');
$ai_reply_content = $nv_Request->get_int('ai_reply_content', 'get', 0);
if ($ai_reply_content == 1 && !empty($question) && $comment_ai['payment_status'] == PaymentStatus::Process->value && $customs_points['point_total'] >= $array_data['min_point_ai'] && $array_data['status'] != TicketStatus::Close->value) {
    $reply_content = ask_chatbot_ai($question);
    $json_data = json_decode($reply_content['content'], true);
    $displayContent = '';
    if (!empty($reply_content['content']) && !isset($json_data['error'])) {
        //Kiểm tra xem nội dung có giá trị hay không, nếu có mới trừ điểm và ghi log
        if ($reply_content['valid']) {
            // Load ngôn ngữ tiếng việt
            $nv_Lang->changeLang('vi');
            $nv_Lang->loadModule($module_file, false, true);
            $message_log['vi'] = $nv_Lang->getModule('log_payment_ai_support', $array_data['id']);
            // Load ngôn ngữ tiếng anh
            $nv_Lang->changeLang('en');
            $nv_Lang->loadModule($module_file, false, true);
            $message_log['en'] = $nv_Lang->getModule('log_payment_ai_support', $array_data['id']);
            $nv_Lang->changeLang(NV_LANG_INTERFACE);
            $message = json_encode($message_log);
            if ($array_data['min_point_ai'] > 0) {
                $update_point = $nv_points->update(0, $array_data['min_point_ai'], $user_info['userid'], $message);
                if ($nv_points->isError()) {
                    nv_jsonOutput([
                        'success' => false,
                        'code' => 1003,
                        'message' => $update_point,
                    ]);
                }
                add_point_logs($user_info['userid'], $array_data['id'], $comment_ai['log_id'], $array_data['min_point_ai'], -1, $message);
            }
            $status = CommentStatus::Done;
            $payment_status = PaymentStatus::Done;
        } else {
            $status = CommentStatus::Invalid;
            $payment_status = PaymentStatus::Cancel;
            $displayContent = $nv_Lang->getModule('invalid_ai_content');
        }
        $reply_range_time = (int)(NV_CURRENTTIME - $array_data['add_time']);
        $db->query('UPDATE ' . TB_TICKET_LOG . ' SET content=' . $db->quote($reply_content['content']) . ', status=' . $status->value . ', payment_status=' . $payment_status->value . ', point_final=' . $array_data['min_point_ai'] . ', count_word=' . $reply_content['count_word'] . ', edit_time=' . NV_CURRENTTIME . ', reply_range_time=' . $reply_range_time . ' WHERE log_id=' . $comment_ai['log_id'])->fetch();
        $current_status = check_current_status($array_data);
        update_ticket_activity($array_data['id'], $current_status, $current_status, 0);
        $log_data = [
            LogKey::AiReply->getLabel(),
            [
                $nv_Lang->getModule('status'),
                $status->getLabel(),
            ],
            [
                $nv_Lang->getModule('payment_status'),
                $payment_status->getLabel()
            ]
        ];
        add_ticket_logs($array_data['customer_id'], LogKey::AiReply->value, $log_data, $array_data['id']);
    }
    echo "event: displayContent\n";
    echo "data: " . $displayContent . "\n\n";
    flush();
    echo "event: stop\n";
    echo "data: stopped\n\n";
    flush();
    die;
}

//Yêu cầu bổ sung đến AI
if ($nv_Request->get_title('ai_additional_token', 'post', '') == NV_CHECK_SESSION && $array_data['status'] != TicketStatus::Close->value) {
    //Kiểm tra xem có đủ điểm không
    if ($customs_points['point_total'] < $array_data['min_point_ai']) {
        nv_jsonOutput([
            'success' => false,
            'code' => 2001,
            'message' => $nv_Lang->getModule('notify_insufficient_points_additional', NV_BASE_SITEURL . NV_LANG_DATA . '/points/#muadiem'),
        ]);
    }

    //Kiểm tra xem câu trả lời chính có vô giá trị không, không thì mới cho tạo bổ sung
    if ($comment_ai['status'] != CommentStatus::Invalid->value) {
        nv_jsonOutput([
            'success' => false,
            'code' => 2002,
            'message' => 'Error!',
        ]);
    }

    //Tạo câu hỏi bổ sung và câu tl của AI (status chưa tl)
    $reply_ai = false;
    $ai_additional_content = $nv_Request->get_editor('ai_additional_content', '', NV_ALLOWED_HTML_TAGS);
    //Bắt buộc ký tự tối thiểu khi gửi yêu cầu bổ sung
    $content_only_text = nv_only_text($ai_additional_content);
    if (empty($content_only_text) || strlen($content_only_text) <= 10) {
        nv_jsonOutput([
            'success' => false,
            'code' => 2003,
            'message' => empty($content_only_text) ? $nv_Lang->getModule('notify_empty_content') : $nv_Lang->getModule('notify_short_content_ai'),
        ]);
    }
    //Nếu chưa có câu hỏi bổ sung nào hoặc có câu hỏi đã hoàn thành thì mới cho tạo câu hỏi bổ sung mới
    if (empty($question_ai_additional)) {
        //Tạo câu hỏi bổ sung (có parent là câu tl gốc của AI)
        $create_additional_question = $db->query('INSERT INTO ' . TB_TICKET_LOG . ' (ticket_id, comment_type, reply_userid, parent, status, content, add_time, is_paid, ticket_add_time) VALUES (' . $array_data['id'] . ',' . CommentType::Customer->value . ',' . $array_data['customer_id'] . ',' . $comment_ai['log_id'] . ',' . CommentStatus::Process->value . ',' . $db->quote($ai_additional_content) . ',' . NV_CURRENTTIME . ',' . 1 . ',' . $array_data['add_time'] . ')');
        if ($create_additional_question) {
            $question_ai_additional = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $array_data['id'] . ' AND parent=' . $comment_ai['log_id'] . ' AND status!=' . CommentStatus::Done->value . ' AND comment_type=' . CommentType::Customer->value)->fetch();
        }
        update_ticket_activity($array_data['id'], TicketStatus::Process->value, TicketStatusClient::Process->value, $array_data['customer_id']);
        add_ticket_logs($array_data['customer_id'], LogKey::CustomerAdditionalAi->value, [LogKey::CustomerAdditionalAi->getLabel()], $array_data['id']);
    }
    //Tạo câu trả lời(AI) cho câu hỏi bổ sung
    if (empty($comment_ai_additional)) {
        //Tạo câu trả lời cho câu hỏi bổ sung (có parent là câu hỏi bổ sung vừa tạo phía trên)
        $create_additional_comment = $db->query('INSERT INTO ' . TB_TICKET_LOG . ' (ticket_id, comment_type, parent, status, payment_status, add_time, is_paid, ticket_add_time) VALUES (' . $array_data['id'] . ',' . CommentType::AI->value . ',' . $question_ai_additional['log_id'] . ',' . CommentStatus::Process->value . ',' . PaymentStatus::Process->value . ','  . NV_CURRENTTIME . ',' . 1 . ',' . $array_data['add_time'] . ')');
        if ($create_additional_comment) {
            $comment_ai_additional = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $array_data['id'] . ' AND parent=' . $question_ai_additional['log_id'] . ' AND status!=' . CommentStatus::Done->value . ' AND comment_type=' . CommentType::AI->value)->fetch();
        }
    }

    //Phản hồi về frontend cho phép hỏi AI
    if (!empty($comment_ai_additional) && $comment_ai_additional['payment_status'] == PaymentStatus::Process->value) {
        $reply_ai = true;
    }

    nv_jsonOutput([
        'success' => true,
        'data' => [
            'question' => $ai_additional_content,
            'reply_ai' => $reply_ai
        ]
    ]);
}
//AI trả lời câu hỏi bổ sung
$question = $nv_Request->get_title('question', 'get', '');
$ai_reply_content_addtition = $nv_Request->get_int('ai_reply_content_addtition', 'get', 0);
if ($ai_reply_content_addtition == 1 && !empty($question) && $comment_ai_additional['payment_status'] == PaymentStatus::Process->value && $customs_points['point_total'] >= $array_data['min_point_ai'] && $array_data['status'] != TicketStatus::Close->value) {
    $reply_content = ask_chatbot_ai($question);
    $json_data = json_decode($reply_content['content'], true);
    $displayContent = '';
    if (!empty($reply_content['content']) && !isset($json_data['error'])) {
        //Kiểm tra xem nội dung có giá trị hay không, nếu có mới trừ điểm và ghi log
        if ($reply_content['valid']) {
            $message = json_encode(get_both_lang('log_payment_ai_support_additional'));
            if ($array_data['min_point_ai'] > 0) {
                $update_point = $nv_points->update(0, $array_data['min_point_ai'], $user_info['userid'], $message);
                if ($nv_points->isError()) {
                    nv_jsonOutput([
                        'success' => false,
                        'code' => 1003,
                        'message' => $update_point,
                    ]);
                }
                add_point_logs($user_info['userid'], $array_data['id'], $comment_ai_additional['log_id'], $array_data['min_point_ai'], -1, $message);
            }
            $status = CommentStatus::Done;
            $payment_status = PaymentStatus::Done;
        } else {
            $status = CommentStatus::Invalid;
            $payment_status = PaymentStatus::Cancel;
            $displayContent = $nv_Lang->getModule('invalid_ai_content_additional');
        }
        //Cập nhật trạng thái câu hỏi bổ sung
        $db->query('UPDATE ' . TB_TICKET_LOG . ' SET status=' . $status->value . ', edit_time=' . NV_CURRENTTIME . ' WHERE log_id=' . $question_ai_additional['log_id'])->fetch();
        //Cập nhật câu trả lời bổ sung
        $reply_range_time = (int)(NV_CURRENTTIME - $question_ai_additional['add_time']);
        $db->query('UPDATE ' . TB_TICKET_LOG . ' SET content=' . $db->quote($reply_content['content']) . ', status=' . $status->value . ', payment_status=' . $payment_status->value . ', point_final=' . $array_data['min_point_ai'] . ', count_word=' . $reply_content['count_word'] . ', edit_time=' . NV_CURRENTTIME . ', reply_range_time=' . $reply_range_time . ' WHERE log_id=' . $comment_ai_additional['log_id'])->fetch();
        $current_status = check_current_status($array_data);
        update_ticket_activity($array_data['id'], $current_status, $current_status, 0);
        $log_data = [
            LogKey::AiReply->getLabel(),
            [
                $nv_Lang->getModule('status'),
                $status->getLabel(),
            ],
            [
                $nv_Lang->getModule('payment_status'),
                $payment_status->getLabel()
            ]
        ];
        add_ticket_logs($array_data['customer_id'], LogKey::AiReply->value, $log_data, $array_data['id']);
    }
    echo "event: displayContent\n";
    echo "data: " . $displayContent . "\n\n";
    flush();
    echo "event: stop\n";
    echo "data: stopped\n\n";
    flush();
    die;
}

//Hiển thị form đánh giá
if ($nv_Request->get_title('rating_token', 'post', '') == NV_CHECK_SESSION) {
    $comment_id = $nv_Request->get_int('comment_id', 'post', 0);
    $rating = $nv_Request->get_int('rating', 'post', 0);
    $comment = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $array_data['id'] . ' AND log_id=' . $comment_id . ' AND status=' . CommentStatus::Done->value .' AND comment_type IN (' . CommentType::AI->value . ',' . CommentType::Expert->value. ')')->fetch();
    if ($comment && !in_array($comment['refund_status'], [RefundStatus::Accept->value, RefundStatus::Refuse->value])) {
        $rating = ($rating == 0) ? $comment['rating_number'] : $rating;
        nv_jsonOutput([
            'success' => true,
            'data' => [
                'html' => get_review_form_component($array_data, $comment, $rating),
                'comment_id' => $comment_id,
                'content' => $comment['rating_content'],
            ],
        ]);
    }
}

//Lưu lại đánh giá
if ($nv_Request->get_title('review_token', 'post', '') == NV_CHECK_SESSION) {
    $comment_id = $nv_Request->get_int('comment', 'post', 0);
    $rating = $nv_Request->get_int('rating', 'post', 0);
    $rating_content = $nv_Request->get_editor('content', '', NV_ALLOWED_HTML_TAGS);
    $refund = $nv_Request->get_int('refund', 'post', 0);
    $content_only_text = nv_only_text($rating_content);
    if (empty($content_only_text) || strlen($content_only_text) <= 5) {
        nv_jsonOutput([
            'success' => false,
            'code' => 3001,
            'message' => empty($content_only_text) ? $nv_Lang->getModule('notify_empty_review') : $nv_Lang->getModule('notify_short_content_review'),
        ]);
    }
    if ($rating < 1 || $rating > 5 || ($refund == 1 && $rating > 2)) {
        nv_jsonOutput([
            'success' => false,
            'code' => 3002,
            'message' => 'Error!',
        ]);
    }
    $comment = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $array_data['id'] . ' AND log_id=' . $comment_id . ' AND payment_status=' . PaymentStatus::Done->value .' AND comment_type IN (' . CommentType::AI->value . ',' . CommentType::Expert->value. ')')->fetch();
    //Nếu ko có comment hoặc comment đã xử lý hoàn điểm hoặc có đánh giá nhưng ticket đã đóng thì không cho chỉnh sửa đánh giá
    if (empty($comment) || in_array($comment['refund_status'], [RefundStatus::Accept->value, RefundStatus::Refuse->value]) || ($comment['rating_number'] > 0 && $array_data['status'] == TicketStatus::Close->value)) {
        nv_jsonOutput([
            'success' => false,
            'code' => 3003,
            'message' => 'Error!',
        ]);
    }

    //Ticket đã đóng hoặc comment đã trả lời quá 7 ngày chỉ được đánh giá không được yêu cầu hoàn điểm
    if ($array_data['status'] == TicketStatus::Close->value || NV_CURRENTTIME > ($comment['add_time'] + 604800)) {
        $refund = 0;
    }

    //Lưu lại đánh giá
    $db->query('UPDATE ' . TB_TICKET_LOG . ' SET refund_status = IF(' . $refund .' = 1, ' . RefundStatus::Open->value . ', 0), rating_content=' . $db->quote($rating_content) . ', rating_number=' . $rating . ', rating_add_time=' . NV_CURRENTTIME . ' WHERE log_id=' . $comment['log_id'])->fetch();
    $comment = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $array_data['id'] . ' AND log_id=' . $comment_id . ' AND payment_status=' . PaymentStatus::Done->value .' AND comment_type IN (' . CommentType::AI->value . ',' . CommentType::Expert->value. ')')->fetch();
    if ($comment) {
        $expert_info = get_user_info($comment['reply_userid']);
        if ($refund == 1) {
            update_ticket_activity($array_data['id'], TicketStatus::Process->value, TicketStatusClient::Process->value, $array_data['customer_id']);
            notification_refund_to_customer($array_data, $comment, RefundStatus::Open);
            notification_refund_to_admin($array_data, $comment);
        }
        $log_data = [
            LogKey::CustomerReview->getLabel(),
            [
                $comment['reply_userid'] == 0 ? $nv_Lang->getModule('role_ai') : $nv_Lang->getModule('role_expert'),
                $expert_info['fullname']
            ],
            [
                $nv_Lang->getModule('review'),
                $rating
            ],
            [
                $nv_Lang->getModule('log_data'),
                $content_only_text,
            ],
            [
                $nv_Lang->getModule('refund_request'),
                $refund,
            ]
        ];
        add_ticket_logs($array_data['customer_id'], LogKey::CustomerReview->value, $log_data, $array_data['id']);
        nv_jsonOutput([
            'success' => true,
            'data' => [
                'html' => get_review_component($array_data, $comment),
            ],
        ]);
    }
}

//Yêu cầu bổ sung đến chuyên gia
if ($nv_Request->get_title('expert_additional_token', 'post', '') == NV_CHECK_SESSION && $array_data['status'] != TicketStatus::Close->value) {
    //Kiểm tra xem có đủ điểm không
    if ($customs_points['point_total'] < $array_data['min_point_expert']) {
        nv_jsonOutput([
            'success' => false,
            'code' => 4001,
            'message' => $nv_Lang->getModule('notify_insufficient_points_additional', NV_BASE_SITEURL . NV_LANG_DATA . '/points/#muadiem'),
        ]);
    }

    $parent_id = $nv_Request->get_int('comment_id', 'post', 0);
    $expert_additional_content = $nv_Request->get_editor('expert_additional_content', '', NV_ALLOWED_HTML_TAGS);
    $expert_file_attach = $_FILES['file_attach'] ?? [];
    // Xử lý hình ảnh hoặc file
    if (!empty($expert_file_attach)) {
        $count_files = count($expert_file_attach['name']);
        if ($count_files > NV_ATTACH_LIMITED) {
            nv_jsonOutput([
                'success' => false,
                'code' => 1004,
                'message' => $nv_Lang->getModule('error_upload_limited', NV_ATTACH_LIMITED),
            ]);
        } else {
            for ($i = 0; $i < $count_files; $i++) {
                $file['name'] = $expert_file_attach['name'][$i];
                $file['type'] = $expert_file_attach['type'][$i];
                $file['size'] = $expert_file_attach['size'][$i];
                $file['tmp_name'] = $expert_file_attach['tmp_name'][$i];
                $file['error'] = $expert_file_attach['error'][$i];
                $attach_files[] = $file;
            }
        }
    }
    if (!empty($attach_files)) {
        $uploaded = multi_upload($attach_files);
        if ($uploaded['status'] == 'error') {
            nv_jsonOutput([
                'success' => false,
                'code' => 1004,
                'message' => $uploaded['message'],
            ]);
        }
    }
    if (isset($uploaded) && $uploaded['status'] == 'success') {
        $attach_files = $uploaded['data'];
    }
    $expert_file_attach = json_encode($attach_files);

    //Kiểm tra xem comment chính đã được trả lời và thanh toán chưa hoặc có tồn tại không, nếu không thì không được phép tạo yc bổ sung
    $root_comment_expert = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $array_data['id'] . ' AND parent=0 AND payment_status=' . PaymentStatus::Done->value . ' AND comment_type=' . CommentType::Expert->value . ' AND log_id=' . $parent_id)->fetch();
    if (empty($root_comment_expert)) {
        nv_jsonOutput([
            'success' => false,
            'code' => 4002,
            'message' => 'Bạn chưa xem yêu cầu chính không thể tạo câu hỏi bổ sung!',
        ]);
    }

    //Bắt buộc ký tự tối thiểu khi gửi yêu cầu bổ sung
    $content_only_text = nv_only_text($expert_additional_content);
    if (empty($content_only_text) || strlen($content_only_text) <= 10) {
        nv_jsonOutput([
            'success' => false,
            'code' => 4003,
            'message' => empty($content_only_text) ? $nv_Lang->getModule('notify_empty_content') : $nv_Lang->getModule('notify_short_content_expert'),
        ]);
    }

    //Nếu chưa có câu hỏi bổ sung nào hoặc có câu hỏi đã hoàn thành thì mới cho tạo câu hỏi bổ sung mới
    $question_expert_additional = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $array_data['id'] . ' AND parent=' . $root_comment_expert['log_id'] . ' AND status!=' . CommentStatus::Done->value . ' AND comment_type=' . CommentType::Customer->value . ' AND reply_userid=' . $array_data['customer_id'])->fetch();
    if (empty($question_expert_additional)) {
        //Tạo câu hỏi bổ sung (có parent là câu tl gốc của chuyên gia)
        $create_additional_question = $db->query('INSERT INTO ' . TB_TICKET_LOG . ' (ticket_id, comment_type, reply_userid, parent, status, content, file_attach, point_offer, add_time, is_paid, ticket_add_time) VALUES (' . $array_data['id'] . ',' . CommentType::Customer->value . ',' . $array_data['customer_id'] . ',' . $root_comment_expert['log_id'] . ',' . CommentStatus::Open->value . ',' . $db->quote($expert_additional_content) . ',' . $db->quote($expert_file_attach) . ',' . $array_data['offer_point_expert'] . ',' . NV_CURRENTTIME . ',' . 1 . ',' . $array_data['add_time'] . ')');
        if ($create_additional_question) {
            $question_expert_additional = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $array_data['id'] . ' AND parent=' . $root_comment_expert['log_id'] . ' AND status!=' . CommentStatus::Done->value . ' AND comment_type=' . CommentType::Customer->value . ' AND reply_userid=' . $array_data['customer_id'])->fetch();
            update_ticket_activity($array_data['id'], TicketStatus::Process->value, TicketStatusClient::Process->value, $array_data['customer_id']);
            $expert_info = get_user_info($root_comment_expert['reply_userid']);
            $log_data = [
                LogKey::CustomerAdditionalExpert->getLabel(),
                [
                    $nv_Lang->getModule('role_expert'),
                    $expert_info['fullname']
                ]
            ];
            notification_additional_to_expert($array_data, $root_comment_expert, $question_expert_additional);
            add_ticket_logs($array_data['customer_id'], LogKey::CustomerAdditionalExpert->value, $log_data, $array_data['id']);
        }
        //Hiển thị câu hỏi ra
        nv_jsonOutput([
            'success' => true,
            'data' => [
                'question' => get_question_component($question_expert_additional),
            ]
        ]);
    }

    //Báo lỗi có câu hỏi chưa được xử lý
    nv_jsonOutput([
        'success' => false,
        'code' => 4004,
        'message' => 'Bạn đã gửi câu hỏi bổ sung rồi, vui lòng chờ chuyên gia phản hồi trước khi gửi câu hỏi mới',
    ]);
}

//Xem câu trả lời từ chuyên gia
if ($nv_Request->get_title('payment_token', 'post', '') == NV_CHECK_SESSION && $array_data['status'] != TicketStatus::Close->value) {
    $comment_id = $nv_Request->get_int('comment', 'post', 0);
    $comment = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $array_data['id'] . ' AND payment_status=' . PaymentStatus::Process->value . ' AND comment_type=' . CommentType::Expert->value . ' AND log_id=' . $comment_id)->fetch();

    //Kiểm tra xem comment đã được trả lời và thanh toán chưa hoặc có tồn tại không hoặc điểm đề xuất có lớn hơn điểm chuyên gia đưa ra không
    if (empty($comment)) {
        nv_jsonOutput([
            'success' => false,
            'code' => 5002,
            'message' => 'Bạn không có câu trả lời nào cần thanh toán',
        ]);
    }

    //Kiểm tra xem có đủ điểm không
    if ($customs_points['point_total'] < $comment['point_quote']) {
        nv_jsonOutput([
            'success' => false,
            'code' => 5001,
            'message' => $nv_Lang->getModule('notify_insufficient_point_view', NV_BASE_SITEURL . NV_LANG_DATA . '/points/#muadiem'),
        ]);
    }

    //Trừ điểm và ghi log
    $expert = get_user_info($comment['reply_userid']);
    if ($comment['parent'] == 0) {
        // Load ngôn ngữ tiếng việt
        $nv_Lang->changeLang('vi');
        $nv_Lang->loadModule($module_file, false, true);
        $message_log['vi'] = $nv_Lang->getModule('log_payment_expert', $expert['fullname'], $array_data['id']);
        // Load ngôn ngữ tiếng anh
        $nv_Lang->changeLang('en');
        $nv_Lang->loadModule($module_file, false, true);
        $message_log['en'] = $nv_Lang->getModule('log_payment_expert', $expert['fullname'], $array_data['id']);
        $nv_Lang->changeLang(NV_LANG_INTERFACE);
    } else {
        // Load ngôn ngữ tiếng việt
        $nv_Lang->changeLang('vi');
        $nv_Lang->loadModule($module_file, false, true);
        $message_log['vi'] = $nv_Lang->getModule('log_payment_expert_additional', $expert['fullname']);
        // Load ngôn ngữ tiếng anh
        $nv_Lang->changeLang('en');
        $nv_Lang->loadModule($module_file, false, true);
        $message_log['en'] = $nv_Lang->getModule('log_payment_expert_additional', $expert['fullname']);
        $nv_Lang->changeLang(NV_LANG_INTERFACE);
    }
    $message = json_encode($message_log);
    if ($comment['point_quote'] > 0) {
        $update_point = $nv_points->update(0, $comment['point_quote'], $user_info['userid'], $message);
        if ($nv_points->isError()) {
            nv_jsonOutput([
                'success' => false,
                'code' => 5003,
                'message' => $update_point,
            ]);
        }
        $expert_info = get_user_info($comment['reply_userid']);
        $log_data = [
            LogKey::CustomerPaymentExpert->getLabel(),
            [
                $nv_Lang->getModule('role_expert'),
                $expert_info['fullname']
            ]
        ];
        add_ticket_logs($array_data['customer_id'], LogKey::CustomerPaymentExpert->value, $log_data, $array_data['id']);
        add_point_logs($user_info['userid'], $array_data['id'], $comment['log_id'], $comment['point_quote'], -1, $message);
    }
    //Cập nhật câu trả lời bổ sung
    $db->query('UPDATE ' . TB_TICKET_LOG . ' SET status=' . CommentStatus::Done->value . ', payment_status=' . PaymentStatus::Done->value . ', edit_time=' . NV_CURRENTTIME . ', point_final=' . $comment['point_quote'] . ' WHERE log_id=' . $comment['log_id'])->fetch();
    //Cập nhật trạng thái câu hỏi bổ sung
    if ($comment['parent'] > 0) {
        $db->query('UPDATE ' . TB_TICKET_LOG . ' SET status=' . CommentStatus::Done->value . ', edit_time=' . NV_CURRENTTIME . ' WHERE log_id=' . $comment['parent'])->fetch();
    }

    //Lấy danh sách comment bổ sung
    $array_additional = [];
    $additional_query = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $array_data['id'] . ' AND comment_type=' . CommentType::ExpertAdditional->value . ' AND parent=' . $comment_id);
    while ($cmt = $additional_query->fetch()) {
        $cmt_row['log_id'] = $cmt['log_id'];
        $cmt_row['content'] = $cmt['content'];
        $array_additional[] = $cmt_row;
    }
    nv_jsonOutput([
        'success' => true,
        'data' => [
            'content' => $comment['content'],
            'additional' => $array_additional,
        ],
    ]);
}

//Đóng ticket
if ($nv_Request->get_string('close_token', 'post', '') == NV_CHECK_SESSION && $array_data['status'] != TicketStatus::Close->value) {
    $ticket_id = $nv_Request->get_int('ticket_id', 'post', 0);
    $row = $db->query('SELECT * FROM ' . TB_TICKET_ROW . ' WHERE id=' . $ticket_id . ' AND customer_id=' . $user_info['userid'] . ' AND delete_time=0')->fetch();
    if (empty($row)) {
        die();
    }

    $stmt = $db->prepare('UPDATE ' . TB_TICKET_ROW . ' SET status=:status, status_client=:status_client, edit_time=' . NV_CURRENTTIME . ', activity_time=' . NV_CURRENTTIME . ' WHERE id=' . $ticket_id);

    $stmt->bindValue(':status', TicketStatus::Close->value, PDO::PARAM_INT);
    $stmt->bindValue(':status_client', TicketStatusClient::Close->value, PDO::PARAM_INT);
    $exc = $stmt->execute();

    if (!empty($row['id']) && !empty($user_info['userid']) && $exc) {
        close_notification_to_caregiver($row);
        $response = [
            'status' => 'success',
            'message' => $nv_Lang->getModule('ticket_closed')
        ];
        add_ticket_logs($array_data['customer_id'], LogKey::CustomerClosedTicket->value, [LogKey::CustomerClosedTicket->getLabel()], $array_data['id']);
        nv_jsonOutput($response);
    }
}

// Xóa ticket
if ($nv_Request->get_string('delete_token', 'post', '') == NV_CHECK_SESSION && $array_data['status'] == TicketStatus::Draft->value) {
    $ticket_id = $nv_Request->get_int('ticket_id', 'post', 0);
    $row = $db->query('SELECT * FROM ' . TB_TICKET_ROW . ' WHERE id=' . $ticket_id . ' AND customer_id=' . $user_info['userid'] . ' AND delete_time=0')->fetch();
    if (empty($row)) {
        nv_jsonOutput([
            'status' => 'error'
        ]);
    }

    $db->query('UPDATE ' . TB_TICKET_ROW . ' SET delete_time=' . NV_CURRENTTIME . ' WHERE id = ' . $db->quote($ticket_id));
    nv_jsonOutput([
        'status' => 'success'
    ]);
}

//Tính tổng điểm đã chi tiêu
$db->sqlreset()
    ->select('SUM(CASE WHEN status = 1 THEN point ELSE -point END) AS total')
    ->from(TB_TICKET_POINT_LOG)
    ->where('ticket_id=' . $row['id'] . ' AND userid=' . $user_info['userid'])
    ->group('ticket_id, userid');
$sth = $db->prepare($db->sql());
$sth->execute();
$total_points = $sth->fetch();
$ticket_points = empty($total_points) ? 0 : $total_points['total'];

$page_title = $row['title'] . ' - ' . $module_config[$module_name]['sitename'];
$key_words = $module_info['keywords'];

$array_mod_title[] = array(
    'title' => $row['title']
);

//Lấy toàn bộ comment của ticket này và phân nhóm theo AI và chuyên gia
$db->sqlreset()
    ->select('*')
    ->from(TB_TICKET_LOG)
    ->where('ticket_id=' . $array_data['id']);
$sth = $db->prepare($db->sql());
$sth->execute();

$array_comments = [];
while ($comment = $sth->fetch()) {
    $array_comments[] = $comment;
}

//Phân loại comment theo cấp đầu tiên và các comment con
$root_comments = [
    CommentType::Expert->value => [],
    CommentType::AI->value => []
];
$all_comments = [];

foreach ($array_comments as $comment) {
    if ($comment['parent'] == 0) {
        // Phân loại theo cấp đầu tiên dựa trên type
        if ($comment['comment_type'] == CommentType::Expert->value) {
            $root_comments[CommentType::Expert->value][] = $comment;
        } else if ($comment['comment_type'] == CommentType::AI->value) {
            $root_comments[CommentType::AI->value][] = $comment;
        }
    } else {
        // Comment con không phân loại theo type
        $all_comments[$comment['parent']][] = $comment;
    }
}

// Gắn comment con vào comment cấp đầu tiên cho từng loại
$ai_comments = attach_comment_children($root_comments[CommentType::AI->value], $all_comments);
$expert_comments = attach_comment_children($root_comments[CommentType::Expert->value], $all_comments);
//Xem ảnh
if ($nv_Request->isset_request('preview', 'get') and $array_data['customer_id'] == $user_info['userid']) {
    $file = $nv_Request->get_string('preview', 'get', '');
    $extension = pathinfo(NV_UPLOADS_REAL_DIR . '/' . $module_upload . '/' . $file, PATHINFO_EXTENSION);
    $extension = $extension == 'jpg' ? 'jpeg' : $extension;

    if (file_type($extension) == 'image') {
        $path = NV_UPLOADS_REAL_DIR . '/' . $module_upload . '/' . $file;
        if (!file_exists($path)) {
            header('HTTP/1.0 404 Not Found');
            exit;
        }

        header('Pragma: public');
        header('Expires: 0');
        header('Cache-Control: public');
        header('Content-Description: File Transfer');
        header('Content-Type: image/' . $extension);
        header('Content-Length: ' . filesize($path));

        readfile($path);
    } else {
        $file_info = pathinfo(NV_UPLOADS_REAL_DIR . '/' . $module_upload . '/' . $file);
        $download = new NukeViet\Files\Download(NV_UPLOADS_REAL_DIR . '/' . $module_upload . '/' . $file, $file_info['dirname'], $file_info['basename'], true);
        $download->download_file();
    }
    exit;
}

$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail_paid'] . '&amp;id=' . $ticket_id;
$page_url = $base_url;
$canonicalUrl = getCanonicalUrl($page_url);

if ($array_data['status'] == TicketStatus::Draft->value) {
    $array_data['link_update'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=add&amp;id=' . $array_data['id'];
} else {
    $array_data['link_add'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=add';
}

// #issue 2273: Bổ sung tính năng mở lại ticket
$time_allowed_open = (86400 * $module_config[$module_name]['open_reply']) + $array_data['activity_time'];
$is_open_ticket = false;
if ($array_data['status'] == TicketStatus::Close->value && $array_data['status_client'] == TicketStatusClient::Close->value && $time_allowed_open > NV_CURRENTTIME) {
    $is_open_ticket = true;
}

// Kiểm tra yêu cầu hoàn điểm
$is_comment_refund = false;
$row_comment_refund = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $ticket_id . ' AND refund_status=' . RefundStatus::Open->value . ' AND rating_number<3 AND rating_content!=""')->fetch();
if ($row_comment_refund) {
    $is_comment_refund = true;
}

$contents = nv_theme_supportticket_detail_paid($array_data, $ai_comments, $expert_comments, $page_url, $customs_points, $ticket_points, $is_open_ticket, $is_comment_refund);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
