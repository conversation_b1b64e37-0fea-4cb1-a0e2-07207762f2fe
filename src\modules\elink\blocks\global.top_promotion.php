<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jul 06, 2011, 06:31:13 AM
 */

if (!defined('NV_MAINFILE'))
    die('Stop!!!');

if (!function_exists('nv_block_elink_top_promotion')) {
    /**
     * @param array $block_config
     * @return string
     */
    function nv_block_elink_top_promotion($block_config)
    {
        global $db, $site_mods, $global_config, $nv_Cache, $db_slave, $nv_Request, $db_config, $user_info, $module_config, $nv_Lang;

        $module = $block_config['module'];
        $module_data = $site_mods[$module]['module_data'];
        $module_file = $site_mods[$module]['module_file'];
        $module_info = $site_mods[$module];

        $nv_Lang->loadModule($module_file, false, true);

        if (file_exists(NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file . '/block.top_promotion.tpl')) {
            $block_theme = $global_config['module_theme'];
        } else {
            $block_theme = 'default';
        }

        $xtpl = new XTemplate('block.top_promotion.tpl', NV_ROOTDIR . '/themes/' . $block_theme . '/modules/' . $module_file);
        $xtpl->assign('LANG', \NukeViet\Core\Language::$tmplang_module);
        $nv_Lang->changeLang();

        $sql = "SELECT tb1.first_name, tb1.last_name, SUM(tb2.scount) totalscount FROM
        " . NV_USERS_GLOBALTABLE . " tb1, " . $db_config['prefix'] . "_" . $module_data . "_statistics tb2, " . $db_config['prefix'] . "_" . $module_data . "_promotion_code tb3
        WHERE tb3.promo_code=tb2.sval AND tb2.stype=1 AND tb2.islink='' AND tb1.userid=tb3.userid
        GROUP BY tb3.userid ORDER BY totalscount DESC LIMIT 10";
        $result = $db->query($sql);

        $stt = 1;
        while ($row = $result->fetch()) {
            $row['stt'] = $stt++;
            $row['full_name'] = nv_show_name_user($row['first_name'], $row['last_name']);
            $row['scount'] = number_format($row['totalscount'], 0, ',', '.');
            $xtpl->assign('ROW', $row);
            $xtpl->parse('main.loop');
        }

        $xtpl->parse('main');
        return $xtpl->text('main');
    }
}

if (defined('NV_SYSTEM')) {
    global $site_mods;
    $module = $block_config['module'];
    if (isset($site_mods[$module])) {
        $content = nv_block_elink_top_promotion($block_config);
    }
}
