<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$page_title = $nv_Lang->getModule('static_tytrong_doanhthu');
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=';

// C<PERSON>u hình thứ tự hiển thị các gói vip (theo request của <PERSON>)
// @link: https://docs.google.com/spreadsheets/d/1MzRI45J6J0bUxf4SRldIaUF8OMPO2KEykM0NUSRQayg/edit?gid=0#gid=0
$vip_order = [99, 19, 1, 2, 3, 4, 5, 6, 7, 11, 21, 66, 68, 77, 88, 89, 'x3', 100, 31, 33, 44, 'nopphixacthuc', 'abasic', 'avip1', 'apro1', 'apro2', 'bvieweb', 'bvip1', 'bvip2', 'bpro', 8, 101];
$vip_order = array_flip($vip_order);

// Lấy danh sách nhóm đang hoạt động
$array_groups = array_filter($groupsList, function($group) {
    return $group['act'] == 1;
});

$array_search = array();
$curent = nv_date('d/m/Y', NV_CURRENTTIME);
$curent_from = '01/' . nv_date('m/Y', NV_CURRENTTIME);
$array_search['time_from'] = $nv_Request->get_title('time_from', 'post,get', $curent_from);
$array_search['time_to'] = $nv_Request->get_title('time_to', 'post,get', $curent);
$array_search['admin_id'] = $nv_Request->get_int('admin_id', 'post,get', -1);
$array_search['group_id'] = $nv_Request->get_int('group_id', 'post,get', -1); //Xem theo nhóm
$array_search['display_confirm_the_order'] = $nv_Request->get_int('display_confirm_the_order', 'post,get', 0); // Chỉ hiển thị các đơn hàng được tính chốt đơn

/**
 * điều hành chung xem tất
 * Sales nào dc cấu hình xem tất thì dc xem còn lại chỉ xem của mình
 * trường hợp trưởng nhóm thì xem thêm các thành viên của nhóm, $arr_admin_view
 */

$arr_admin_view = $arr_admin_view_tmp = $arr_staff_view = [];
/**
 * Nếu ở chế độ xem `Tất cả` => Bổ sung tìm theo nhóm
 */
if ($data_config['view_static'] == 1 && $array_search['group_id'] != -1 && isset($array_groups[$array_search['group_id']])) {
    foreach ($arr_group_user2[$array_search['group_id']] as $user_id => $user_data) {
        if ($user_data['group_id'] == $array_search['group_id']) {
            $arr_admin_view[$user_id] = $user_id;
        }
    }
    $arr_staff_view = $arr_admin_view;
    $arr_admin_view_tmp = $arr_admin_view;
} else if (!defined('NV_IS_SPADMIN')) {
    if (isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 0) {
        if ($array_groups_users[$admin_info['userid']]['is_leader'] == 1) {
            $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id IN (' . implode(', ', array_keys($array_user_gr[$admin_info['userid']])) . ') AND userid != ' . $admin_info['userid'];
            $_result = $db->query($_sql);
            while ($_row_groups_users = $_result->fetch()) {
                $arr_admin_view[$_row_groups_users['userid']] = $_row_groups_users['userid'];
            }
            $arr_admin_view[$admin_info['userid']] = $admin_info['userid'];
            $arr_admin_view_tmp = $arr_admin_view;
            if ($array_search['admin_id'] != -1 && $data_config['view_static'] == 2) {
                $arr_admin_view = [];
            }
        } elseif ($data_config['view_static'] == 2) {
            $array_search['admin_id'] = $admin_info['userid'];
        }
    }
    $arr_staff_view = $arr_admin_view;
}

// Xử lý liên kết giữa người phụ trách và nhóm
if ($nv_Request->isset_request('get_admin', 'post')) {
    $group_id = $nv_Request->get_int('groupId', 'post', -1);

    $arr_admin_groups = $result_admin = [];
    if ($group_id != -1) {
        foreach ($arr_group_user2[$group_id] as $user_id => $user_data) {
            if ($user_data['group_id'] == $group_id) {
                $arr_admin_groups[$user_data['userid']] = $user_data['userid'];
            }
        }
    }

    if (!empty($arr_admin_groups)) {
        // Nếu tìm theo nhóm => Danh sách người phụ trách thuộc nhóm đó
        $sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arr_admin_groups) . ') AND active = 1';
    } elseif ($data_config['view_static'] == 2 and !defined('NV_IS_SPADMIN') and isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 0) {
        // Trường hợp Phân quyền xem thống kê: Sale nào xem sale đó
        if (!empty($arr_admin_view_tmp)) {
            $sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arr_admin_view_tmp) . ') AND active = 1';
        } else {
            $sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $admin_info['userid'] . ' AND active = 1';
        }
    } else {
        $sql = 'SELECT userid, first_name, last_name, username, tb2.is_suspend FROM ' . NV_USERS_GLOBALTABLE . ' tb1 INNER JOIN ' . $db_config['prefix'] . '_authors tb2 ON tb1.userid = tb2.admin_id WHERE tb1.userid IN (SELECT admin_id FROM ' . NV_AUTHORS_GLOBALTABLE . ') AND tb1.active = 1 AND tb2.is_suspend = 0';
        // Thêm marketing vào list danh sách khi xem hết tất cả
        $new_item = array(
            'id' => 0,
            'title' => $nv_Lang->getModule('marketing'),
        );
        array_push($result_admin, $new_item);
    }

    $result = $db->query($sql);
    while ($_user_info = $result->fetch()) {
        $fullname = nv_show_name_user($_user_info['first_name'], $_user_info['last_name'], $_user_info['userid']);
        $result_admin[$_user_info['userid']] = [
            'id'    => $_user_info['userid'],
            'title' => $_user_info['username'] . ' (' . $fullname . ')',
        ];
    }

    nv_jsonOutput([
        'list_admin' => $result_admin
    ]);
}

$showchart = $nv_Request->get_int('showchart', 'post,get', 0);
$showheader = $nv_Request->get_int('showheader', 'post,get', 1);

$array_admin_except = [];
$result_admin = $db->query('SELECT admin_id FROM nv4_authors');
while ($_admin_info = $result_admin->fetch()) {
    $array_admin_except[$_admin_info['admin_id']] = $_admin_info['admin_id'];
}

// các tài khoản test mà k muốn thống kê đưa vào đây, lấy theo nhóm test https://id.dauthau.net/qtdtvnds/index.php?language=vi&nv=users&op=groups&userlist=19
$sql = 'SELECT userid FROM ' . NV_GROUPS_GLOBALTABLE . '_users WHERE group_id = 19';
$result = $db->query($sql);
while ($_gr_user_info = $result->fetch()) {
    $array_admin_except[$_gr_user_info['userid']] = $_gr_user_info['userid'];
}

if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_from'], $m)) {
    $sfrom = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
} else {
    $sfrom = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
}
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_to'], $m)) {
    $sto = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
} else {
    $sto = mktime(23, 59, 59, nv_date('m', NV_CURRENTTIME), nv_date('d', NV_CURRENTTIME), nv_date('Y', NV_CURRENTTIME));
}

$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE static_time >= ' . $sfrom . ' AND static_time <=' . $sto;
if (($data_config['view_static'] == 2 && !defined('NV_IS_SPADMIN')) || ($data_config['view_static'] == 1 && $array_search['group_id'] != -1)) {
    if (!empty($arr_admin_view)) {
        if ($array_search['display_confirm_the_order'] == 1) {
            $sql .= ' AND admin_id IN (' . implode(',', $arr_admin_view) . ')'; // Chỉ hiển thị các đơn hàng được tính chốt đơn
        } else {
            $sql .= ' AND (affiliate_userid IN (' . implode(',', $arr_admin_view) . ') OR caregiver_id IN (' . implode(',', $arr_admin_view) . ') OR admin_id IN (' . implode(',', $arr_admin_view) . '))';
        }
    } else if ($array_search['admin_id'] != -1) {
        $sql .= ' AND (affiliate_userid = ' . $array_search['admin_id'] . ' OR caregiver_id = ' . $array_search['admin_id'] . ' OR admin_id = ' . $array_search['admin_id'] . ')';
    }
} elseif ($array_search['admin_id'] != -1) {
    $sql .= ' AND (affiliate_userid = ' . $array_search['admin_id'] . ' OR caregiver_id = ' . $array_search['admin_id'] . ' OR admin_id = ' . $array_search['admin_id'] . ')';
}

if (!empty($array_admin_except)) {
    $sql .= ' AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')';
}

$result = $db->query($sql);

// pr($result->fetchAll());

$array_vip = $_row = $_row_new = $array_vip_new = $_row_renewwal = $array_vip_renewwal = $_row_1year_renewwal = $array_1year_renewwal = [];
while ($row = $result->fetch()) {
    // THeo gói VIP
    if ($row['vip'] == '' and $row['siteid'] == 2) {
        $row['vip'] = 'nopphixacthuc';
    }
    if (!isset($_row[$row['vip']])) {
        $_row[$row['vip']]['num'] = 0;
        $row['total_end'] > 0 && $_row[$row['vip']]['num'] = 1; // chỉ đếm những cái nào doanh số thực nhận > 0
        
        $_row[$row['vip']]['total_end'] = 0;
        $_row[$row['vip']]['vip_order'] = $vip_order[$row['vip']] ?? 100; // chưa có thứ tự thì đẩy xuống cuối cùng
        if ($array_search['admin_id'] != -1) {
            if ($row['admin_id'] == $array_search['admin_id']) {
                $_row[$row['vip']]['total_end'] = $row['total_end'];
            }
            $array_vip[$row['vip']] = $_row[$row['vip']];
        } else {
            $_row[$row['vip']]['total_end'] = $row['total_end'];
            $array_vip[$row['vip']] = $_row[$row['vip']];
        }
    } else {
        $row['total_end'] > 0 && $_row[$row['vip']]['num'] += 1; // chỉ đếm những cái nào doanh số thực nhận > 0
        if ($array_search['admin_id'] != -1) {
            if ($row['admin_id'] == $array_search['admin_id']) {
                $_row[$row['vip']]['total_end'] += $row['total_end'];
            }
            $array_vip[$row['vip']] = $_row[$row['vip']];
        } else {
            $_row[$row['vip']]['total_end'] += $row['total_end'];
            $array_vip[$row['vip']] = $_row[$row['vip']];
        }
    }

    // THeo gói VIP mới
    if ($row['is_renewal'] == 0 || $row['is_renewal'] == 2) {
        if (!isset($_row_new[$row['vip']])) {
            $_row_new[$row['vip']]['num'] = 0;
            $row['total_end'] > 0 && $_row_new[$row['vip']]['num'] = 1;
            $_row_new[$row['vip']]['total_end'] = 0;
            if ($array_search['admin_id'] != -1) {
                if ($row['admin_id'] == $array_search['admin_id']) {
                    $_row_new[$row['vip']]['total_end'] = $row['total_end'];
                }
                $array_vip_new[$row['vip']] = $_row_new[$row['vip']];
            } else {
                $_row_new[$row['vip']]['total_end'] = $row['total_end'];
                $array_vip_new[$row['vip']] = $_row_new[$row['vip']];
            }
        } else {
            $row['total_end'] > 0 && $_row_new[$row['vip']]['num'] += 1;
            if ($array_search['admin_id'] != -1) {
                if ($row['admin_id'] == $array_search['admin_id']) {
                    if (isset($_row_new[$row['vip']]['total'])) {
                        $_row_new[$row['vip']]['total_end'] += $row['total_end'];
                    } else {
                        $_row_new[$row['vip']]['total_end'] = $row['total_end'];
                    }
                }
                $array_vip_new[$row['vip']] = $_row_new[$row['vip']];
            } else {
                $_row_new[$row['vip']]['total_end'] += $row['total_end'];
                $array_vip_new[$row['vip']] = $_row_new[$row['vip']];
            }
        }
    } else if ($row['is_renewal'] == 1) { // THeo gói VIP gia hạn
        if (!isset($_row_renewwal[$row['vip']])) {
            $_row_renewwal[$row['vip']]['num'] = 0;
            $row['total_end'] > 0 && $_row_renewwal[$row['vip']]['num'] = 1;
            $_row_renewwal[$row['vip']]['total_end'] = 0;
            if ($array_search['admin_id'] != -1) {
                if ($row['admin_id'] == $array_search['admin_id']) {
                    $_row_renewwal[$row['vip']]['total_end'] = $row['total_end'];
                }
                $array_vip_renewwal[$row['vip']] = $_row_renewwal[$row['vip']];
            } else {
                $_row_renewwal[$row['vip']]['total_end'] = $row['total_end'];
                $array_vip_renewwal[$row['vip']] = $_row_renewwal[$row['vip']];
            }
        } else {
            $row['total_end'] > 0 && $_row_renewwal[$row['vip']]['num'] += 1;
            if ($array_search['admin_id'] != -1) {
                if ($row['admin_id'] == $array_search['admin_id']) {
                    $_row_renewwal[$row['vip']]['total_end'] += $row['total_end'];
                }
                $array_vip_renewwal[$row['vip']] = $_row_renewwal[$row['vip']];
            } else {
                $_row_renewwal[$row['vip']]['total_end'] += $row['total_end'];
                $array_vip_renewwal[$row['vip']] = $_row_renewwal[$row['vip']];
            }
        }
        
        if ($row['is_1year_renewal'] == 1) {
            if (!isset($_row_1year_renewwal[$row['vip']])) {
                $_row_1year_renewwal[$row['vip']]['num'] = 0;
                $row['total_end'] > 0 && $_row_1year_renewwal[$row['vip']]['num'] = 1;
                $_row_1year_renewwal[$row['vip']]['total_end'] = 0;
                if ($array_search['admin_id'] != -1) {
                    if ($row['admin_id'] == $array_search['admin_id']) {
                        $_row_1year_renewwal[$row['vip']]['total_end'] = $row['total_end'];
                    }
                    $array_1year_renewwal[$row['vip']] = $_row_1year_renewwal[$row['vip']];
                } else {
                    $_row_1year_renewwal[$row['vip']]['total_end'] = $row['total_end'];
                    $array_1year_renewwal[$row['vip']] = $_row_1year_renewwal[$row['vip']];
                }
            } else {
                $row['total_end'] > 0 && $_row_1year_renewwal[$row['vip']]['num'] += 1;
                if ($array_search['admin_id'] != -1) {
                    if ($row['admin_id'] == $array_search['admin_id']) {
                        $_row_1year_renewwal[$row['vip']]['total_end'] += $row['total_end'];
                    }
                    $array_1year_renewwal[$row['vip']] = $_row_1year_renewwal[$row['vip']];
                } else {
                    $_row_1year_renewwal[$row['vip']]['total_end'] += $row['total_end'];
                    $array_1year_renewwal[$row['vip']] = $_row_1year_renewwal[$row['vip']];
                }
            }
        }
    }
}

// pr($array_vip);

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);

$xtpl->assign('LINK_UPDATE', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=static_order&amp;time_from=' . $array_search['time_from'] . '&amp;time_to=' . $array_search['time_to'] . '&amp;admin_id=' . $array_search['admin_id']);
$xtpl->assign('LINK_UPDATE_LEADS', NV_BASE_SITEURL . 'crawls/static_groups_leads.php?date_from=' . $sfrom . '&date_to=' . $sto . '&t=' . NV_CURRENTTIME);

$xtpl->assign('LINK_CHART', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=static&amp;showheader=0&amp;showchart=1&amp;time_from=' . $array_search['time_from'] . '&amp;time_to=' . $array_search['time_to'] . '&amp;caregiver_id=' . $array_search['admin_id']);

if (defined('NV_IS_SPADMIN') or $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) {
    $xtpl->parse('main.update');
    $xtpl->parse('main.update_leads');
}

if ($array_search['admin_id'] == 0) {
    $xtpl->assign('marketing_selected', ' selected="selected"');
}

// Xuất danh sách các nhóm và ở chế độ xem là: Tất cả
if ($data_config['view_static'] == 1) {
    if ($array_search['admin_id'] != -1) {
        $array_search['group_id'] = $array_groups_users[$array_search['admin_id']]['group_id'] ?? -1;
    }

    foreach ($array_groups as $group_id => $value) {
        $xtpl->assign('OPTION_GROUP', array(
            'key' => $group_id,
            'title' => $value['title'],
            'selected' => $array_search['group_id'] == $group_id ? ' selected="selected"' : ''
        ));
        $xtpl->parse('main.view_static.loop_admin_group');
    }
    // Chế độ xem
    $xtpl->assign('DISPLAY_CONFIRM_ORDER', $array_search['display_confirm_the_order'] == 1 ? 'checked="checked"' : '');
    $xtpl->parse('main.view_static');
}

$xtpl->assign('ARRAY_SEARCH', $array_search);

// Kể cả các gói vip ko có đơn nào cũng hiển thị ra
foreach ($vip_order as $k => $v) {
    if (!isset($array_vip[$k])) {
        $array_vip[$k] = [
            'num' => 0,
            'total_end' => 0,
            'vip_order' => $v
        ];
    }
}

uasort($array_vip, function ($a, $b) {
    if ($a['vip_order'] == $b['vip_order']) return 0;
    return ($a['vip_order'] < $b['vip_order']) ? -1 : 1;
});

$a = 0;
$static_vip = array();
$static_vip['num'] = 0;
$static_vip['total_end'] = 0;
$static_vip['num_vip_new'] = 0;
$static_vip['total_end_vip_new'] = 0;
$static_vip['tytrong_vip_new'] = 0;

$static_vip['num_vip_renewal'] = 0;
$static_vip['total_end_vip_renewal'] = 0;
$static_vip['tytrong_vip_renewal'] = 0;

$static_vip['num_vip_1year_renewal'] = 0;
$static_vip['total_end_vip_1year_renewal'] = 0;

foreach ($array_vip as $v => $vip) {
    $data_vip = array();
    $data_vip['stt'] = ++$a;
    $data_vip['vip_title'] = $nv_Lang->getModule('vip' . $v);
    $data_vip['num'] = $vip['num'];
    $data_vip['total_end'] = number_format($vip['total_end']);

    $data_vip['link_order'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=payment&amp;showheader=0&amp;static_time_from=' . $array_search['time_from'] . '&amp;static_time_to=' . $array_search['time_to'] . '&amp;vip=' . $v;
    $data_vip['link_vip'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=customs&amp;showheader=0&amp;time_static_from=' . $array_search['time_from'] . '&amp;time_static_to=' . $array_search['time_to'] . '&amp;s_vip=' . $v;
    if ($array_search['admin_id'] != -1) {
        $data_vip['link_order'] .= '&amp;show_order_admin=1&amp;caregiver_id=' . $array_search['admin_id'];
        $data_vip['link_vip'] .= '&amp;show_order_admin=1&amp;s_admin=' . $array_search['admin_id'];
    }
    if ($v == 33 or $v == 44) {
        $data_vip['link_vip'] = $data_vip['link_order'];
    }
    
    // Tính toán phần vip_new
    $data_vip['num_vip_new'] = isset($array_vip_new[$v]['num']) ? $array_vip_new[$v]['num'] : 0;
    $total_end_vip_new_number = isset($array_vip_new[$v]['total_end']) ? $array_vip_new[$v]['total_end'] : 0;
    $data_vip['total_end_vip_new'] = number_format($total_end_vip_new_number);
    $data_vip['tytrong_vip_new'] = $vip['total_end'] > 0 ? number_format($total_end_vip_new_number / $vip['total_end'] * 100, 2) : 0;
    
    // Tính toán phần vip gia han
    $data_vip['num_vip_renewal'] = isset($array_vip_renewwal[$v]['num']) ? $array_vip_renewwal[$v]['num'] : 0;
    $total_end_vip_renewal_number = isset($array_vip_renewwal[$v]['total_end']) ? $array_vip_renewwal[$v]['total_end'] : 0;
    $data_vip['total_end_vip_renewal'] = number_format($total_end_vip_renewal_number);
    $data_vip['tytrong_vip_renewal'] = $vip['total_end'] > 0 ? number_format($total_end_vip_renewal_number / $vip['total_end'] * 100, 2) : 0;
    
    // Tính toán phần vip gia han năm đầu
    $data_vip['num_vip_1year_renewal'] = isset($array_1year_renewwal[$v]['num']) ? $array_1year_renewwal[$v]['num'] : 0;
    $total_end_vip_1year_renewal_number = isset($array_1year_renewwal[$v]['total_end']) ? $array_1year_renewwal[$v]['total_end'] : 0;
    $data_vip['total_end_vip_1year_renewal'] = number_format($total_end_vip_1year_renewal_number);
    
    $xtpl->assign('DATA_VIP', $data_vip);

    if ($admin_info['userid'] == $array_search['admin_id'] || in_array($array_search['admin_id'], $arr_staff_view) || defined('NV_IS_SPADMIN') || $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) {
        $xtpl->parse('main.loopvip.has_link');
    } else {
        $xtpl->parse('main.loopvip.havent_link');
    }
    $xtpl->parse('main.loopvip');

    $static_vip['num'] += $vip['num'];
    $static_vip['num_vip_new'] += $data_vip['num_vip_new'];
    $static_vip['num_vip_renewal'] += $data_vip['num_vip_renewal'];
    $static_vip['num_vip_1year_renewal'] += $data_vip['num_vip_1year_renewal'];
    $static_vip['total_end'] += $vip['total_end'];
    $static_vip['total_end_vip_new'] += $total_end_vip_new_number;
    $static_vip['total_end_vip_renewal'] += $total_end_vip_renewal_number;
    $static_vip['total_end_vip_1year_renewal'] += $total_end_vip_1year_renewal_number;
}
foreach ($static_vip as $key => $value) {
    $static_vip['tytrong_vip_new'] = $static_vip['total_end'] ? number_format($static_vip['total_end_vip_new']/$static_vip['total_end'] * 100, 2) : 0;
    $static_vip['tytrong_vip_renewal'] = $static_vip['total_end'] ? number_format($static_vip['total_end_vip_renewal']/$static_vip['total_end'] * 100, 2) : 0;
}
foreach ($static_vip as $key => $value) {
    if ($key != 'tytrong_vip_new' && $key != 'tytrong_vip_renewal') {
        $static_vip[$key] = number_format($value);
    }
}
$xtpl->assign('STATIC_VIP', $static_vip);

if ((isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) or defined('NV_IS_SPADMIN')) {
    $xtpl->parse('main.static_all');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';
