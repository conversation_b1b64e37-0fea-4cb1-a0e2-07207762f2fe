<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use NukeViet\Module\crmbidding\Log;
use NukeViet\Module\crmbidding\LogRow;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CreateOpportunities implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'opportunities';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $array_site, $array_user_id_users, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $error = '';
        $error_code = '0000';
        $admin_id = $nv_Request->get_int('admin_id', 'post', '0');
        $row['name'] = $nv_Request->get_title('name', 'post', '');
        $row['phone'] = $nv_Request->get_title('phone', 'post', '');
        $row['email'] = strtolower($nv_Request->get_title('email', 'post', ''));
        $row['siteid'] = $nv_Request->get_int('siteid', 'post', 0);
        $row['prefix_lang'] = $nv_Request->get_int('prefix_lang', 'post', 0);
        $row['logs_status'] = $nv_Request->get_int('logs_status', 'post', 1);

        if ($row['name'] == '') {
            $error_code = '2003';
            $error = $nv_Lang->getModule('error_required_name');
        } else if ($row['phone'] == '' and $row['email'] == '') {
            $error_code = '2004';
            $error = $nv_Lang->getModule('error_required_phone');
        } elseif ($row['phone'] != '' && !phonecheck($row['phone'], $row['prefix_lang'])) {
            $error_code = '2005';
            $error = $nv_Lang->getModule('error_phone_number');
        } elseif ($row['email'] != '' && nv_check_valid_email($row['email']) != '') {
            $error_code = '2006';
            $error = $nv_Lang->getModule('error_email');
        } else if (!in_array($row['siteid'], array_keys($array_site))) {
            $error_code = '2010';
            $error = $nv_Lang->getModule('siteid_not_defined');
        } else if ($admin_id > 0 and !isset($array_user_id_users[$admin_id])) {
            $error_code = '2011';
            $error = $nv_Lang->getModule('admin_id_not_defind');
        }

        $otherdata = $nv_Request->get_array('otherdata', 'post');
        if (!is_array($otherdata)) {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Param otherdata invalid to array')
                ->getResult();
        }

        $keys_check = [
            'leadsid',
            'user_id',
            'orderid',
            'sub_phone',
            'sub_email',
            'address',
            'tax',
            'company_name',
            'address_company',
            'affilacate_id',
            'caregiver_id',
            'about',
            'is_system',
            'source_leads',
            'label',
            'convert_contact',
            'convert_organization',
            'customs_id',
            'orderid_dtnet',
            'last_orderid_site',
            'is_recall'
        ];
        $array_sql = [];
        if (!empty($otherdata)) {
            // check $field
            foreach ($otherdata as $key => $value) {
                if (!in_array($key, $keys_check)) {
                    return $this->result->setError()
                        ->setCode('2009')
                        ->setMessage('Missing field ' . $key . ' in otherdata')
                        ->getResult();
                }
                $array_sql[$key] = $key;
            }
        }

        $row = array_merge($row, $otherdata);

        if (isset($row['sub_email']) and $row['sub_email'] != '') {
            $row['sub_email'] = strtolower($row['sub_email']);
            $row['sub_email'] = str_replace(" ", "", $row['sub_email']);
            $row['sub_email'] = str_replace(';', ',', $row['sub_email']);
            $row['sub_email'] = str_replace("\n", ',', $row['sub_email']);
            $_arr_email = array();
            $list_mail = explode(',', $row['sub_email']);
            foreach ($list_mail as $_mail) {
                $_mail = trim($_mail);
                if ((nv_check_valid_email($_mail)) != '') {
                    $error_code = '2008';
                    $error = sprintf($nv_Lang->getModule('error_subemail'), $_mail);
                } elseif (!in_array($_mail, $_arr_email)) {
                    $_arr_email[] = $_mail;
                }
            }
            $otherdata['sub_email'] = implode(',', $_arr_email);
        }

        if (!isset($row['phone_search'])) {
            $row['phone_search'] = 0;
            if (preg_match('/(\d{9})$/', $row['phone'], $m)) {
                $row['phone_search'] = $m[0];
            }
        }
        if ($row['sub_phone'] != '') {
            $row['sub_phone'] = str_replace(' ', '', $row['sub_phone']);
            $row['sub_phone'] = str_replace(';', ',', $row['sub_phone']);
            $row['sub_phone'] = str_replace("\n", ',', $row['sub_phone']);
            $_arr_phone = $sub_phone_search = [];
            $list_phone = explode(',', $row['sub_phone']);
            foreach ($list_phone as $key => $_phone) {
                $_phone = trim($_phone);
                $_arr_phone[] = $_phone;
                if (!phonecheck($_phone)) {
                    $error_code = '2007';
                    $error = sprintf($nv_Lang->getModule('error_sub_phone'), $_phone);
                } else {
                    if (preg_match('/(\d{9})$/', $_phone, $m)) {
                        $sub_phone_search[$key] = $m[0];
                    }
                }
            }
            $otherdata['sub_phone'] = implode(',', $_arr_phone);
            if (!isset($row['sub_phone_search'])) {
                $row['sub_phone_search'] = implode(',', $sub_phone_search);
                $otherdata['sub_phone_search'] = $row['sub_phone_search'];
                $array_sql['sub_phone_search'] = 'sub_phone_search';
            }
        }

        if (!empty($row['tax']) and taxcodecheck2($row['tax']) == false) {
            $error = $nv_Lang->getModule('error_tax');
            $error_code = '2012';
        }

        if (isset($otherdata['source_leads'])) {
            if ($otherdata['source_leads'] == 14) {
                $check_duplicate_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities WHERE active=1 AND name=' . $db->quote($row['name']) . ' AND phone = ' . $db->quote($row['phone']) . ' AND email =' . $db->quote($row['email']) . ' AND source_leads= ' . $otherdata['source_leads'] .  ' AND (status = 1 OR status = 4)';
            } else {
                $check_duplicate_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities WHERE active=1 AND name=' . $db->quote($row['name']) . ' AND phone = ' . $db->quote($row['phone']) . ' AND email =' . $db->quote($row['email']) . ' AND source_leads= ' . $otherdata['source_leads'];
            }
            $_row = $db->query($check_duplicate_sql)
                ->fetch();
            if (!empty($_row)) {
                $error_code = '4002';
                $error = $nv_Lang->getModule('error_duplicate');
            }
        }

        if (empty($error)) {
            try {
                $row['timecreate'] = $row['updatetime'] = $row['first_time'] = $row['activity_time'] = NV_CURRENTTIME;
                if (!empty($array_sql)) {
                    $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities (name, phone, phone_search, email, siteid, prefix_lang, status, timecreate, updatetime, active, first_time, activity_time, ' . implode(',', $array_sql) . ') VALUES (:name, :phone, :phone_search, :email, :siteid, :prefix_lang, :status, :timecreate, :updatetime, :active, :first_time, :activity_time, :' . implode(', :', $array_sql) . ')');
                } else {
                    $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities (name, phone, phone_search, email, siteid, prefix_lang, status, timecreate, updatetime, active, first_time, activity_time) VALUES (:name, :phone, :phone_search, :email, :siteid, :prefix_lang, :status, :timecreate, :updatetime, :active, :first_time, :activity_time)');
                }

                $stmt->bindParam(':name', $row['name'], PDO::PARAM_STR);
                $stmt->bindParam(':phone', $row['phone'], PDO::PARAM_STR);
                $stmt->bindParam(':phone_search', $row['phone_search'], PDO::PARAM_STR);
                $stmt->bindParam(':email', $row['email'], PDO::PARAM_STR);
                if ((!isset($otherdata['is_system']) or $otherdata['is_system'] == 0) and isset($otherdata['caregiver_id']) and $otherdata['caregiver_id'] > 0) {
                    $stmt->bindValue(':status', 1, PDO::PARAM_INT);
                    $stmt->bindParam(':first_time', $row['first_time'], PDO::PARAM_INT);
                } else {
                    $stmt->bindValue(':status', 4, PDO::PARAM_INT);
                    $stmt->bindValue(':first_time', 0, PDO::PARAM_INT);
                }

                $stmt->bindValue(':active', 1, PDO::PARAM_INT);
                $stmt->bindParam(':timecreate', $row['timecreate'], PDO::PARAM_INT);
                $stmt->bindParam(':updatetime', $row['updatetime'], PDO::PARAM_INT);
                $stmt->bindParam(':activity_time', $row['activity_time'], PDO::PARAM_INT);
                $stmt->bindParam(':siteid', $row['siteid'], PDO::PARAM_INT);
                $stmt->bindParam(':prefix_lang', $row['prefix_lang'], PDO::PARAM_INT);

                if (!empty($otherdata)) {
                    foreach ($otherdata as $key => $value) {
                        $stmt->bindValue(':' . $key, $value, PDO::PARAM_STR);
                    }
                }

                $exc = $stmt->execute();
                $id = $db->lastInsertId();
                if ($exc and $id > 0) {
                    if ($row['logs_status'] == 1) {
                        $log = new Log($nv_Lang->getModule('opportunities_add_log'));
                        $logRow = new LogRow();

                        if ($otherdata['leadsid']) {
                            $logRow->setLink($nv_Lang->getModule('leads_info'), $module_name, 'leads_info', 'id=' . $otherdata['leadsid']);
                            $log->add($logRow);
                        }

                        $sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (
                            userid, log_area, log_key, log_time, log_data, oppotunities_id
                        ) VALUES (
                            " . $admin_id . ", 1, 'LOG_ADMIN_UPDATE_LEADS_INFO',
                            " . NV_CURRENTTIME . ", " . $db->quote($log->toString()) . ", " . $id . "
                        )";
                        $db->query($sql);
                        unset($log);
                    }

                    if ($otherdata['leadsid']) {
                        $db->exec("UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_leads SET
                            opportunities_id=" . $id . ", status=2, updatetime= " . NV_CURRENTTIME . ",
                            caregiver_id = " . $row['caregiver_id'] . ", elasticsearch = 0
                        WHERE id=" . $otherdata['leadsid']);

                        $log = new Log($nv_Lang->getModule('opportunities_add'));
                        $logRow->setLink($row['name'], $module_name, 'opportunities_info', 'id=' . $id);
                        $log->add($logRow);

                        $sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (
                            userid, log_area, log_key, log_time, log_data, leads_id
                        ) VALUES (
                            " . $admin_id . ", 1, 'LOG_ADMIN_UPDATE_LEADS_INFO',
                            " . NV_CURRENTTIME . ", " . $db->quote($log->toString()) . ", " . $otherdata['leadsid'] . "
                        )";
                        $db->query($sql);
                        unset($log);
                    }

                    $nv_Cache->delMod($module_name);
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Add Opportunities', $id, $admin_id);
                    // gọi code đẩy dữ liệu lên ES luôn
                    update_es();
                    $this->result->setSuccess();
                    $this->result->set('OpportunitiesID', $id);
                } else {
                    $this->result->setError()
                        ->setCode('4001')
                        ->setMessage('OpportunitiesID not response');
                }
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode($error_code)
                ->setMessage($error)
                ->getResult();
        }
        return $this->result->getResult();
    }
}
