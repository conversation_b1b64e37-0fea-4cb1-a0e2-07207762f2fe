<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$page_title = $nv_Lang->getModule('config_sale');

$data = $data_old = [];
$stmt = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_config WHERE module = ' . $db->quote($module_name));
while ($temp = $stmt->fetch()) {
    $data[$temp['config_name']] = $temp['config_value'];
    $data_old[$temp['config_name']] = $temp['config_value'];
}
$error = array();

$_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_crmbidding_groups_users ORDER BY weight ASC';
$_query = $db->query($_sql);
$array_user_id_users_all = $array_user_id_users = $array_user_id_users_not_set_order = array();
while ($_row = $_query->fetch()) {
    if ($_row['config']!= '') {
        $_row['config'] = json_decode($_row['config'], true);
        $_info = $db->query('SELECT * FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $_row['userid'])->fetch();
        if (!empty($_info)) {
            $_row['username'] = $_info['username'];
        }

        if (isset($_row['config']['set_order']) and $_row['config']['set_order'] == 1) {
            $array_user_id_users[$_row['userid']] = $_row;
            $array_user_id_users_all[$_row['userid']] = $_row['userid'];
        } else {
            $array_user_id_users_not_set_order[$_row['userid']] = $_row['userid'];
        }
    }
}

if ($nv_Request->isset_request('ajax_action', 'post') and $admin_info['userid'] == '8223') {
    $id = $nv_Request->get_int('id', 'post', 0);
    $new_vid = $nv_Request->get_int('new_vid', 'post', 0);
    $content = 'NO_' . $id;
    if ($new_vid > 0) {
        $weight = 1;
        $_array_user_id_users = $array_user_id_users;
        unset($_array_user_id_users[$id]);
        foreach ($_array_user_id_users as $row) {
            if ($weight == $new_vid) {
                ++$weight;
            }
            $db->query('UPDATE ' . NV_PREFIXLANG . '_crmbidding_groups_users SET weight=' . $weight . ' WHERE userid=' . $row['userid']);
            ++$weight;
        }
        $sql = 'UPDATE ' . NV_PREFIXLANG . '_crmbidding_groups_users SET weight=' . $new_vid . ' WHERE userid=' . $id;
        $db->query($sql);
        $content = 'OK_' . $id;

        // cập nhật thứ tự các sale k được chia đơn về 0
        $sql = 'UPDATE ' . NV_PREFIXLANG . '_crmbidding_groups_users SET weight=0 WHERE userid IN(' . implode(',', $array_user_id_users_not_set_order) . ')';
        $db->query($sql);
    }
    include NV_ROOTDIR . '/includes/header.php';
    echo $content;
    include NV_ROOTDIR . '/includes/footer.php';
}

if ($nv_Request->isset_request('submit', 'post, get') and $admin_info['userid'] == '8223') {
    $data = array();
    $data['orderid_to'] = $nv_Request->get_title('orderid_to', 'post', $data_old['orderid_to']);
    $data['sale_order_to'] = $nv_Request->get_title('sale_order_to', 'post', $data_old['sale_order_to']);
    $data['sale_order_to_vieweb'] = $nv_Request->get_title('sale_order_to_vieweb', 'post', $data_old['sale_order_to_vieweb']);
    $data['sale_order_dtnet_to'] = $nv_Request->get_title('sale_order_dtnet_to', 'post', $data_old['sale_order_dtnet_to']);
    $data['orderid_dtnet_to'] = $nv_Request->get_title('orderid_dtnet_to', 'post', $data_old['orderid_dtnet_to']);
    $data['sale_telepro_callback_to'] = $nv_Request->get_title('sale_telepro_callback_to', 'post', $data_old['sale_telepro_callback_to']);
    $data['leads_messenger_id'] = $nv_Request->get_int('leads_messenger_id', 'post', $data_old['leads_messenger_id']);
    $data['leads_messenger_to_sale'] = $nv_Request->get_title('leads_messenger_to_sale', 'post', $data_old['leads_messenger_to_sale']);
    $data['api_pro_to_sale'] = $nv_Request->get_title('api_pro_to_sale', 'post', $data_old['api_pro_to_sale']);
    $data['last_active_profile_dtnet'] = $nv_Request->get_title('last_active_profile_dtnet', 'post', $data_old['last_active_profile_dtnet']);
    $data['sale_active_profile_dtnet_to'] = $nv_Request->get_title('sale_active_profile_dtnet_to', 'post', $data_old['sale_active_profile_dtnet_to']);

    $data['transation_wallet_id'] = $nv_Request->get_title('transation_wallet_id', 'post', $data_old['transation_wallet_id']);
    $data['transation_wallet_to_sale'] = $nv_Request->get_title('transation_wallet_to_sale', 'post', $data_old['transation_wallet_to_sale']);

    $data['leads_zalo_id'] = $nv_Request->get_int('leads_zalo_id', 'post', $data_old['leads_zalo_id']);
    $data['leads_zalo_to_sale'] = $nv_Request->get_title('leads_zalo_to_sale', 'post', $data_old['leads_zalo_to_sale']);
    $data['sale_view_zalo_to'] = $nv_Request->get_title('sale_view_zalo_to', 'post', $data_old['sale_view_zalo_to']);

    if (empty($error)) {
        $sth = $db->prepare("UPDATE " . NV_CONFIG_GLOBALTABLE . " SET config_value = :config_value WHERE lang = '" . NV_LANG_DATA . "' AND module = :module_name AND config_name = :config_name");

        $sth->bindParam(':module_name', $module_name, PDO::PARAM_STR);
        foreach ($data as $config_name => $config_value) {
            $sth->bindParam(':config_name', $config_name, PDO::PARAM_STR);
            $sth->bindParam(':config_value', $config_value, PDO::PARAM_STR);
            $sth->execute();
        }

        nv_insert_logs(NV_LANG_DATA, $module_name, NV_LANG_DATA, "Setting", $admin_info['userid']);
        $nv_Cache->delMod('settings');
        $nv_Cache->delMod($module_name);

        Header("Location: " . NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=' . $op);
        die();
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);

$xtpl->assign('DATA', $data);

$num_items = sizeof($array_user_id_users);
foreach ($array_user_id_users as $user) {
    for ($i = 1; $i <= $num_items; ++$i) {
        $xtpl->assign('WEIGHT', [
            'key' => $i,
            'title' => $i,
            'selected' => ($i == $user['weight']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.user.weight_loop');
    }

    $xtpl->assign('USERS', $user);
    $xtpl->parse('main.user');
}

if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}
if ($admin_info['userid'] == '8223') {
    $xtpl->parse('main.submit');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
