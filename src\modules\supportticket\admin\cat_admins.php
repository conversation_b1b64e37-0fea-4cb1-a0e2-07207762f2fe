<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

// Cập nhật nhận email khi có ticket mới được tạo ngoài site
if ($nv_Request->isset_request('change_status_send_email', 'post, get')) {
    $cat_id = $nv_Request->get_int('cat_id', 'post, get', 0);
    $userid = $nv_Request->get_int('userid', 'post, get', 0);
    $content = 'NO_' . $cat_id;

    $query = 'SELECT is_send_email FROM ' . TB_TICKET_CATADMIN . ' WHERE cat_id=' . $cat_id . ' AND userid=' . $userid;
    $row = $db->query($query)->fetch();
    if (isset($row['is_send_email'])) {
        $active = ($row['is_send_email']) ? 0 : 1;
        $query = 'UPDATE ' . TB_TICKET_CATADMIN . ' SET is_send_email=' . intval($active) . ' WHERE cat_id=' . $cat_id . ' AND userid=' . $userid;
        $db->query($query);
        $content = 'OK_' . $cat_id;
    }
    $nv_Cache->delMod($module_name);
    include NV_ROOTDIR . '/includes/header.php';
    echo $content;
    include NV_ROOTDIR . '/includes/footer.php';
}

// Lấy danh sách nhân sự theo cat hoặc nếu cat_di không nằm trong danh sách cat thì sẽ chuyển về trang cat bt
$cat_id = $nv_Request->get_absint('cat_id', 'get', 0);
if (empty($cat_id) || !isset($array_ticket_cats[$cat_id]) || (!defined('NV_IS_SPADMIN') && !in_array($admin_info['userid'], $special_admin_array))) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=cat');
}
$page_title = sprintf($nv_Lang->getModule('list_human_resources'), $array_ticket_cats[$cat_id]['title_' . NV_LANG_DATA]);
$base_url = $page_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&cat_id=' . $cat_id;
$error = $array_users = $array_userids = $user_for_cat = $data = [];
$total = 0;
$per_page = 15;
$page = $nv_Request->get_int('page', 'get', 1);
$check_send_mail = false;

// Danh sách member
$db->sqlreset()
    ->select('COUNT(*)')
    ->from(TB_TICKET_CATADMIN)
    ->where('cat_id=' . $cat_id);
$total = $db->query($db->sql())->fetchColumn();
$db->select('userid, is_send_email')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$result = $db->query($db->sql());
while ($row = $result->fetch()) {
    $user_for_cat[] = $row['userid'];
    $array_users[$row['userid']] = $row;
    if ($row['is_send_email'] == 1) {
        $check_send_mail = true;
    }
}
$result->closeCursor();

if (!empty($array_users)) {
    $sql = 'SELECT userid, username, first_name, last_name, email FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', array_keys($array_users)) . ')';
    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        $userid = $row['userid'];
        if (isset($array_users[$userid])) {
            $array_userids[$userid] = array_merge($array_users[$userid], $row);
        }
    }
}

// Hiển thị lỗi nếu bộ phận chưa có người nhận email khi có ticket mới
if (!empty($array_userids) && $check_send_mail == false) {
    $error[] = $nv_Lang->getModule('empty_check_mail_cat');
}

// Thêm nhân sự vào bộ phận
if ($nv_Request->isset_request('addUser', 'post')) {
    $insert_success = false;
    $cat_users = $nv_Request->get_typed_array('cat_users', 'post', 'int', []);
    if (empty($cat_users)) {
        $error[] = $nv_Lang->getModule('empty_user');
    } else {
        $array_user_diff = array_diff($cat_users, $user_for_cat); // Lọc ra những nhân sự chưa có trong bộ phận
        if (empty($array_user_diff)) {
            $error[] = $nv_Lang->getModule('user_already_exists');
        } else {
            foreach ($array_user_diff as $user) {
                $query = $db->query('SELECT COUNT(*) FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $user);
                if ($query->fetchColumn()) {
                    try {
                        $stmt = $db->prepare('INSERT INTO ' . TB_TICKET_CATADMIN . ' (userid, cat_id, add_time, is_send_email)
                            VALUES (:userid, :cat_id, :add_time, :is_send_email)');
                        $stmt->bindParam(':userid', $user, PDO::PARAM_INT);
                        $stmt->bindParam(':cat_id', $cat_id, PDO::PARAM_INT);
                        $stmt->bindValue(':add_time', NV_CURRENTTIME, PDO::PARAM_INT);
                        $stmt->bindValue(':is_send_email', $array_ticket_cats[$cat_id]['is_point'] == 1 ? 1 : 0, PDO::PARAM_INT);
                        $exc = $stmt->execute();
                        if ($exc) {
                            $nv_Cache->delMod($module_name);
                            $insert_success = true;
                        }
                    } catch (PDOException $e) {
                        trigger_error($e->getMessage());
                    }
                } else {
                    $error[] = $nv_Lang->getModule('empty_user');
                }
            }
        }
    }
    if ($insert_success == true) {
        nv_redirect_location($page_url);
    }
}

// Xóa nhân xử khỏi bộ phận
if ($nv_Request->isset_request('gid,exclude', 'post')) {
    $gid = $nv_Request->get_int('gid', 'post', 0);
    $uid = $nv_Request->get_int('exclude', 'post', 0);

    $row = $db->query('SELECT * FROM ' . TB_TICKET_CATADMIN . ' WHERE cat_id=' . $gid . ' AND userid=' . $uid)->fetch();
    if (!empty($row)) {
        try {
            $db->query('DELETE FROM ' . TB_TICKET_CATADMIN . ' WHERE cat_id = ' . $gid . ' AND userid = ' . $uid);
            $nv_Cache->delMod($module_name);
            nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_DELETE_USER_CAT', 'Member Id: ' . $uid . ' group ID: ' . $gid, $admin_info['userid']);
            nv_jsonOutput('OK');
        } catch (PDOException $e) {
            trigger_error($e->getMessage());
        }
    } else {
        nv_jsonOutput($nv_Lang->getModule('UserNotInGroup'));
    }
}

// Chuyển bộ phận
if ($nv_Request->isset_request('changegroups', 'post')) {
    $group_id = $nv_Request->get_int('groups', 'post');
    $userid = $nv_Request->get_int('userid', 'post');

    $sql = 'SELECT COUNT(*) FROM ' . TB_TICKET_CATADMIN . ' WHERE cat_id=' . $group_id . ' AND userid= ' . $userid;
    $result = $db->query($sql)->fetchColumn();
    if (empty($result)) {
        try {
            $sql = 'UPDATE ' . TB_TICKET_CATADMIN . ' SET cat_id=' . $group_id . ', edit_time= ' . NV_CURRENTTIME . ' WHERE userid=' . $userid . ' AND cat_id=' . $cat_id;
            $db->query($sql);
            $nv_Cache->delMod($module_name);
            nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_MOVE_USER_CAT', 'group_id: ' . $group_id, $admin_info['userid']);
            nv_jsonOutput('OK');
        } catch (PDOException $e) {
            trigger_error($e->getMessage());
            nv_jsonOutput($nv_Lang->getModule('error_change_manager'));
        }
    } else {
        nv_jsonOutput($nv_Lang->getModule('UserInGroup', $array_ticket_cats[$group_id]['title_' . NV_LANG_DATA]));
    }
}

// Danh sách loại yêu cầu
foreach ($array_active_cats as $value) {
    if ($value['cat_id'] == $cat_id) {
        continue;
    }
    $cat_options[] = [
        'key' => $value['cat_id'],
        'title' => $value['title_' . NV_LANG_DATA]
    ];
}

// Lấy danh sách admin
foreach ($array_admin_users as $value) {
    $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
    $userOptions[] = [
        'key' => $value['userid'],
        'name' => $value['username'] . ' (' . $fullname . ')',
    ];
}

// Lấy danh sách nhân sự xử lý của bộ phận
foreach ($array_users as &$user) {
    $userid = $user['userid'];
    if (isset($array_userids[$userid])) {
        $user = array_merge($user, $array_userids[$userid]);
        $user['full_name'] = nv_show_name_user($user['first_name'], $user['last_name'], $user['username']);
        $user['link_user_extend'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=user_extend&amp;userid=' . $user['userid'] . '&amp;config_for=1';
    }
    $user['is_send_email'] = ($user['is_send_email'] == 1 ? 'checked' : '');
    $data[] = $user;
}

// Xử lý giao diện
$tpl = new \NukeViet\Template\NVSmarty();
$tpl->setTemplateDir(NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$tpl->assign('LANG', $nv_Lang);
$tpl->assign('URL_USER', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE);
$tpl->assign('MODULE_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&cat_id=' . $cat_id);
$tpl->assign('GID', $cat_id);
$tpl->assign('ERROR', implode('<br />', $error));
$tpl->assign('CAT_OPTIONS', $cat_options);
$tpl->assign('USER_OPTIONS', $userOptions);
$tpl->assign('DATA', $data);
$tpl->assign('PAGINATION', nv_generate_page($base_url, $total, $per_page, $page));
$contents = $tpl->fetch('cat_admins.tpl');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
