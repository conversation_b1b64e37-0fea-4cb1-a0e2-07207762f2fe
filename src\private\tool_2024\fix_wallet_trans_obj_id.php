<?php
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __FILE__), PATHINFO_DIRNAME) . '/..')));

require NV_CONSOLE_DIR . '/server.php';
require NV_ROOTDIR . '/includes/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

$last_id = 0;
$filelog = NV_CONSOLE_DIR . '/tool_2024/fix_wallet_trans_obj_id.txt';
if (file_exists($filelog)) {
    $last_id = intval(file_get_contents($filelog));
}
// tool xử lý các nv4_wallet_transaction bị lưu nhầm obj_id k lưu đúng id giao dịch điểm mà lưu admin_id,
/*
 * update($arrConfig[$row['moneyConfig']], MONEY_UNIT, 0, $account_info['userid'], json_encode($message), $tacdong, 0, $admin_info['userid'], $customs_points);
 * update($money, $money_unit, $site_id, $userid, $message = '', $is_add = false, $adminid = 0, $id_point = 0)
 *
 * Lưu sai:$adminid luôn =0
 * $id_point = $admin_info['userid'];
 */

$sql = "SELECT * FROM nv4_wallet_transaction WHERE id > " . $last_id . " AND obj_type = 'admin_add_point' ORDER BY id ASC LIMIT 100";
$result = $db->query($sql);
$arr_tmp = [];
while ($row = $result->fetch()) {
    $arr_tmp[$row['id']] = $row;
}
if (empty($arr_tmp)) {
    echo "Đã chạy hết!!";
    exit(1);
} else {
    foreach ($arr_tmp as $row) {
        $last_id = $row['id'];
        $adminid = $row['obj_id'];
        // check xem có log mua điểm từ admin k
        $result_points_log = $db->query("SELECT id FROM nv4_points_log WHERE site_id  = " . $row['site_id'] . " AND status = 1 AND userid = " . $row['userid'] . " AND admin_id = " . $row['obj_id'] . " AND created_time = " . $row['created_time'] . "");
        // nếu có thì update = logid còn k có thì gán giá trị âm để check lại
        if ($point_log = $result_points_log->fetch()) {
            $id_points_log = $point_log['id'];
        } else {
            $id_points_log = '-' . $row['obj_id'];
        }

        $db->query("UPDATE nv4_wallet_transaction SET adminid = " . $adminid . ", obj_id=" . $id_points_log . ", id_sale_static = " . $adminid . " WHERE id=" . $row['id']);

        // tính toán lưu SaleStatic để k phải viết tool khác
        $date = mktime(0, 0, 0, date('m', $row['transaction_time']), date('d', $row['transaction_time']), date('Y', $row['transaction_time']));
        $money_point = $row['money_total'];
        $money_point_bonus = ($money_point * 10) / 100; // mặc định 10%
        $db->query("INSERT INTO nv4_vi_crmbidding_sale_static (date, userid, money_point_num, money_point, money_point_bonus) VALUES (" . $date . ", " . $adminid . ", 1, " . $money_point . ", " . $money_point_bonus . ")
        ON DUPLICATE KEY UPDATE money_point_num = money_point_num+1, money_point=money_point + " . $money_point . ", money_point_bonus=money_point_bonus + " . $money_point_bonus . "");

        echo "Wallet transid " . $last_id . ": obj_id=" . $id_points_log . "\n";
    }
    file_put_contents($filelog, $last_id);
    echo "Xong " . $last_id . "\n";
}
