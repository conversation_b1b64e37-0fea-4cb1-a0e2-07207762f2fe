<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_MAINFILE')) {
    exit('Stop!!!');
}

$lang_translator['author'] = 'VINADES.,JSC <<EMAIL>>';
$lang_translator['createdate'] = '04/03/2010, 15:22';
$lang_translator['copyright'] = '@Copyright (C) 2009-2021 VINADES.,JSC. All rights reserved';
$lang_translator['info'] = '';
$lang_translator['langtype'] = 'lang_module';

$lang_module['calendar'] = 'Calendar';
$lang_module['main'] = 'Main Page';
$lang_module['calendar_admin'] = 'Calendar Management';
$lang_module['is_required'] = 'Is required';
$lang_module['addtime'] = 'Added Time';
$lang_module['edittime'] = 'Updated Time';

$lang_module['order'] = 'Order';
$lang_module['name_calendar'] = 'Calendar Name';
$lang_module['status'] = 'Status';
$lang_module['function'] = 'Function';
$lang_module['description'] = 'Description';
$lang_module['alias'] = 'Alias';
$lang_module['calendar_add'] = 'Add Calendar';
$lang_module['calendar_edit'] = 'Edit Calendar';
$lang_module['name_calendar_error'] = 'Error: You have not entered the calendar name';
$lang_module['name_calendar_error_duplicate'] = 'Error: Calendar name already exists';
$lang_module['alias_error_duplicate'] = 'Error: Alias already exists';
$lang_module['color'] = 'Calendar Color';
$lang_module['color'] = 'Calendar color';
$lang_module['is_calendar_import'] = 'Calendar';
$lang_module['is_redday'] = 'Historical Calendar';
$lang_module['type_calendar'] = 'Calendar type';
$lang_module['is_calendar_error'] = 'Error: You have not selected a calendar type';

$lang_module['event'] = 'Event';
$lang_module['event_admin'] = 'Event Management';
$lang_module['calendar_select'] = 'Select Calendar';
$lang_module['name_event'] = 'Event Name';
$lang_module['event_add'] = 'Add Event';
$lang_module['event_edit'] = 'Edit Event';
$lang_module['name_event_error_duplicate'] = 'Error: Event name already exists';
$lang_module['change_weight'] = 'Change Order';
$lang_module['order_weight_number'] = 'Current Order Number';
$lang_module['order_weight_new'] = 'New Order Number';
$lang_module['action'] = 'Action';
$lang_module['all'] = 'All';
$lang_module['date_event'] = 'Event Date';
$lang_module['date_event_error'] = 'Error: Start date must be before end date';
$lang_module['alias_error'] = 'Error: Alias cannot be empty';
$lang_module['error_required_id'] = 'Error: You have not selected an id to delete';
$lang_module['delete_success'] = 'Delete successful';
$lang_module['changeweight_success'] = 'Order change successful';
$lang_module['status_repeat'] = 'Repeat Status';
$lang_module['end_date_repeat'] = 'End Repeat Date';
$lang_module['no_repeat'] = 'No Repeat';
$lang_module['every_day'] = 'Every Day';
$lang_module['every_week'] = 'Every Week';
$lang_module['every_month'] = 'Every Month';
$lang_module['every_year'] = 'Every Year';
$lang_module['repeat_until_date_error'] = 'Error: End repeat date must be greater than event date or cannot be empty';

$lang_module['search_keywords'] = 'Search Keywords';
$lang_module['main_search_from'] = 'From Date';
$lang_module['main_search_to'] = 'To Date';

$lang_module['import_ical'] = 'Import from Calendar';
$lang_module['error_required_calendar'] = 'Error: You have not selected a calendar';
$lang_module['truncate'] = 'Delete all event data before importing';

$lang_module['alert_action_event_success_1'] = 'Creating event success';
$lang_module['alert_action_event_success_2'] = 'Updating event success';

$lang_module['day_lunar'] = 'Day';
$lang_module['lunar'] = 'Lunar';
$lang_module['in_the_past'] = 'In the past';
$lang_module['time'] = 'Time';
$lang_module['start'] = 'Start';
$lang_module['end'] = 'End';
$lang_module['warning_repeat_day'] = 'Note: The system limits to a maximum of 30 daily repetitions';
$lang_module['warning_repeat_week'] = 'Note: The system limits to a maximum of 20 weekly repetitions';
$lang_module['warning_repeat_month'] = 'Note: The system limits to a maximum of 15 monthly repetitions';
$lang_module['warning_repeat_year'] = 'Note: The system limits to a maximum of 10 yearly repetitions';

$lang_module['is_lunar_calendar'] = 'Use lunar calendar';
$lang_module['lunar_info'] = 'Lunar calendar information';
$lang_module['lunar_calendar_notice'] = 'Please enter lunar date manually in dd/mm/yyyy format';
