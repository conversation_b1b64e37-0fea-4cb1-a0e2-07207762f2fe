<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/includes/mainfile.php';

$leadid = 0;
if (file_exists(NV_ROOTDIR . '/tool/tool_change_admin_leads.txt')) {
    $leadid = file_get_contents(NV_ROOTDIR . '/tool/tool_change_admin_leads.txt');
    $leadid = intval($leadid);
}
$a = 0;
$sql = 'SELECT * FROM nv4_vi_crmbidding_leads WHERE (caregiver_id = 0 OR caregiver_id = 4) AND leadid > ' . $leadid . ' ORDER BY id ASC LIMIT 100';
$result = $db->query($sql);
while ($_row = $result->fetch()) {
    $_opportunities = $db->query('SELECT * FROM nv4_vi_crmbidding_opportunities WHERE leadsid = ' . $_row['id'] . '')->fetch();
    if (!empty($_opportunities) and $_opportunities['caregiver_id'] > 0) {
        $db->query('UPDATE nv4_vi_crmbidding_leads SET caregiver_id = ' . $_opportunities['caregiver_id'] . ' WHERE id = ' . $_row['id']);
    }
    file_put_contents(NV_ROOTDIR . '/tool/tool_change_admin_leads.txt', $_row['id']);
    ++$a;
}

echo '<br><br>Update trong: ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
if ($a > 0) {
    echo ('<meta http-equiv="refresh" content="1;url=/tool/tool_change_admin_leads.php?t=' . NV_CURRENTTIME . '" > Dang thuc hien<br><br><br><br>');
}
die('ok');
