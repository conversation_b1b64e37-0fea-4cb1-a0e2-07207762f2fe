<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript"
    src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">

<link type="text/css" href="{NV_BASE_SITEURL}themes/{TEMPLATE}/images/{MODULE_FILE}/plugins/apexcharts/apexcharts.css"
    rel="stylesheet">
<script type="text/javascript"
    src="{NV_BASE_SITEURL}themes/{TEMPLATE}/images/{MODULE_FILE}/plugins/apexcharts/apexcharts.min.js"></script>

<div class="panel">
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline" id="ltablesearch">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}"> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
        <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}">
        <div class="form-group">
            <select class="form-control" name="type_chart">
                <option value="1" {SR_TYPE_CHART1}>{LANG.static_renewal_type_chart_1}</option>
                <option value="2" {SR_TYPE_CHART2}>{LANG.static_renewal_type_chart_2}</option>
            </select>
        </div>
        <div class="form-group">
            <div class="sr-type1" {HIDE_TYPE1}>
                <label>{LANG.year}</label>
                <select class="form-control" name="year">
                    <!-- BEGIN: loop_year -->
                    <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                    <!-- END: loop_year -->
                </select>
            </div>
            <div class="sr-type2" {HIDE_TYPE2}>
                <div class="form-group">
                    <label>{LANG.from_date}:</label> <input class="form-control w100 uidatepicker" type="text" value="{SEARCH.time_from}" name="time_from" maxlength="10" autocomplete="off">
                </div>
                <div class="form-group">
                    <label>{LANG.to}:</label> <input class="form-control w100 uidatepicker" type="text" value="{SEARCH.time_to}" name="time_to" maxlength="10" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="form-group">
            <select class="form-control" name="admin_id">
                <option value="0">{LANG.admin_id_all}</option>
                <!-- BEGIN: loop_admin -->
                <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                <!-- END: loop_admin -->
            </select>
        </div>
        <div class="form-group">
            <input class="form-control" type="checkbox" value="1" name="static_vieweb"{STATIC_VIEWEB}>
            <label>{LANG.static_vieweb}</label>
        </div>
        <div class="form-group">
            <input class="btn btn-primary" type="submit" value="{LANG.view}">
        </div>
    </form>
</div>
<div class="row">
    <h1 class="text-center">{LANG.static_renewal_chart}</h1>
    <div id="chartline"></div>
    <script type="text/javascript">
        $(document).ready(function () {
            var options = {
                series: {DATA_LINE},
                chart: {
                    height: 350,
                    type: 'line',
                    zoom: {
                        enabled: false
                    },
                },
                stroke: {
                    width: 2
                },
                colors: ['#2E93fA', '#66DA26', '#546E7A', '#E91E63', '#FF9800', '#f54e42', '#f5f542', '#035419', '#42f5ad', '#42cef5', '#4251f5', '#d442f5', '#f542a4'],
                dataLabels: {
                    enabled: false
                },
                legend: {
                    tooltipHoverFormatter: function (val, opts) {
                        return val + ' - ' + opts.w.globals.series[opts.seriesIndex][opts.dataPointIndex] + ''
                    }
                },
                markers: {
                    size: 2,
                    hover: {
                        sizeOffset: 5
                    }
                },
                xaxis: {
                    categories: {DATA_TIMELINE},
                },
                tooltip: {
                    y: [
                        {
                            title: {
                                formatter: function (val) {
                                    return val;
                                }
                            }
                        }
                    ]
                },
                grid: {
                    borderColor: '#f1f1f1',
                }
            };
            var chart = new ApexCharts(document.querySelector("#chartline"), options);
            chart.render();

        });
    </script>
</div>
<style>
    table thead tr td,
    table thead tr th {
        vertical-align: middle !important;
    }
</style>
<script type="text/javascript">
    $(document).ready(function ($) {
        $("select[name='admin_id']").select2();
        $('select[name="type_chart"]').change(function () {
            if ($(this).val() == '1') {
                $(this).parents().next().find('.sr-type1').show();
                $(this).parents().next().find('.sr-type1').siblings().hide();
            } else {
                $(this).parents().next().find('.sr-type2').show();
                $(this).parents().next().find('.sr-type2').siblings().hide();
            }
        });
        $('.uidatepicker').datepicker({
            showOn: "both",
            dateFormat: "dd/mm/yy",
            changeMonth: true,
            changeYear: true,
            showOtherMonths: true,
            buttonImage: nv_base_siteurl + "assets/images/calendar.gif",
            buttonImageOnly: true
        });
    });
</script>
<!-- END: main -->