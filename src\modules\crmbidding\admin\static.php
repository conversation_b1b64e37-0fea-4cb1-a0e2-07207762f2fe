<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

/*
    !!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    ! LƯU Ý QUAN TRỌNG:
    !!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    
    Hiện tại ở menu `Thống kê tỷ trọng doanh thu` (function static_tytrong_doanhthu.php) sử dụng logic SQL y hệt như ở function này, chỉ là bảng thống kê rút gọn đi
    V<PERSON> vậy, bất kì thay đổi logic dữ <PERSON>ệu ở bảng này cần cập nhật cho cả function static_tytrong_doanhthu.php nữa để tránh gặp issue như này
    @link https://vinades.org/dauthau/dauthau.info/-/issues/3317

*/ 

$page_title = $nv_Lang->getModule('static');
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=';

// Lấy danh sách nhóm đang hoạt động
$array_groups = array_filter($groupsList, function ($group) {
    return $group['act'] == 1;
});

$array_search = array();
$curent = nv_date('d/m/Y', NV_CURRENTTIME);
$curent_from = '01/' . nv_date('m/Y', NV_CURRENTTIME);
$array_search['time_from'] = $nv_Request->get_title('time_from', 'post,get', $curent_from);
$array_search['time_to'] = $nv_Request->get_title('time_to', 'post,get', $curent);
$array_search['admin_id'] = $nv_Request->get_int('admin_id', 'post,get', -1);
$array_search['group_id'] = $nv_Request->get_int('group_id', 'post,get', -1); //Xem theo nhóm
$array_search['display_confirm_the_order'] = $nv_Request->get_int('display_confirm_the_order', 'post,get', 0); // Chỉ hiển thị các đơn hàng được tính chốt đơn

/**
 * điều hành chung xem tất
 * Sales nào dc cấu hình xem tất thì dc xem còn lại chỉ xem của mình
 * trường hợp trưởng nhóm thì xem thêm các thành viên của nhóm, $arr_admin_view
 */

$arr_admin_view = $arr_admin_view_tmp = $arr_staff_view = [];
/**
 * Nếu ở chế độ xem `Tất cả` => Bổ sung tìm theo nhóm
 */
if ($data_config['view_static'] == 1 && $array_search['group_id'] != -1 && isset($array_groups[$array_search['group_id']])) {
    foreach ($arr_group_user2[$array_search['group_id']] as $user_id => $user_data) {
        if ($user_data['group_id'] == $array_search['group_id']) {
            $arr_admin_view[$user_id] = $user_id;
        }
    }
    $arr_staff_view = $arr_admin_view;
    $arr_admin_view_tmp = $arr_admin_view;
} else if (!defined('NV_IS_SPADMIN')) {
    if (isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 0) {
        if ($array_groups_users[$admin_info['userid']]['is_leader'] == 1) {
            $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id IN (' . implode(', ', array_keys($array_user_gr[$admin_info['userid']])) . ') AND userid != ' . $admin_info['userid'];
            $_result = $db->query($_sql);
            while ($_row_groups_users = $_result->fetch()) {
                $arr_admin_view[$_row_groups_users['userid']] = $_row_groups_users['userid'];
            }
            $arr_admin_view[$admin_info['userid']] = $admin_info['userid'];
            $arr_admin_view_tmp = $arr_admin_view;
            if ($array_search['admin_id'] != -1 && $data_config['view_static'] == 2) {
                $arr_admin_view = [];
            }
        } elseif ($data_config['view_static'] == 2) {
            $array_search['admin_id'] = $admin_info['userid'];
        }
    }
    $arr_staff_view = $arr_admin_view;
}

// Xử lý liên kết giữa người phụ trách và nhóm
if ($nv_Request->isset_request('get_admin', 'post')) {
    $group_id = $nv_Request->get_int('groupId', 'post', -1);

    $arr_admin_groups = $result_admin = [];
    if ($group_id != -1) {
        foreach ($arr_group_user2[$group_id] as $user_id => $user_data) {
            if ($user_data['group_id'] == $group_id) {
                $arr_admin_groups[$user_data['userid']] = $user_data['userid'];
            }
        }
    }

    if (!empty($arr_admin_groups)) {
        // Nếu tìm theo nhóm => Danh sách người phụ trách thuộc nhóm đó
        $sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arr_admin_groups) . ') AND active = 1';
    } elseif ($data_config['view_static'] == 2 and !defined('NV_IS_SPADMIN') and isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 0) {
        // Trường hợp Phân quyền xem thống kê: Sale nào xem sale đó
        if (!empty($arr_admin_view_tmp)) {
            $sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arr_admin_view_tmp) . ') AND active = 1';
        } else {
            $sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $admin_info['userid'] . ' AND active = 1';
        }
    } else {
        $sql = 'SELECT userid, first_name, last_name, username, tb2.is_suspend FROM ' . NV_USERS_GLOBALTABLE . ' tb1 INNER JOIN ' . $db_config['prefix'] . '_authors tb2 ON tb1.userid = tb2.admin_id WHERE tb1.userid IN (SELECT admin_id FROM ' . NV_AUTHORS_GLOBALTABLE . ') AND tb1.active = 1 AND tb2.is_suspend = 0';
        // Thêm marketing vào list danh sách khi xem hết tất cả
        $new_item = array(
            'id' => 0,
            'title' => $nv_Lang->getModule('marketing'),
        );
        array_push($result_admin, $new_item);
    }

    $result = $db->query($sql);
    while ($_user_info = $result->fetch()) {
        $fullname = nv_show_name_user($_user_info['first_name'], $_user_info['last_name'], $_user_info['userid']);
        $result_admin[$_user_info['userid']] = [
            'id'    => $_user_info['userid'],
            'title' => $_user_info['username'] . ' (' . $fullname . ')',
        ];
    }

    nv_jsonOutput([
        'list_admin' => $result_admin
    ]);
}

$showchart = $nv_Request->get_int('showchart', 'post,get', 0);
$showheader = $nv_Request->get_int('showheader', 'post,get', 1);

$array_admin_except = [];
$result_admin = $db->query('SELECT admin_id FROM nv4_authors');
while ($_admin_info = $result_admin->fetch()) {
    $array_admin_except[$_admin_info['admin_id']] = $_admin_info['admin_id'];
}

// các tài khoản test mà k muốn thống kê đưa vào đây, lấy theo nhóm test https://id.dauthau.net/qtdtvnds/index.php?language=vi&nv=users&op=groups&userlist=19
$sql = 'SELECT userid FROM ' . NV_GROUPS_GLOBALTABLE . '_users WHERE group_id = 19';
$result = $db->query($sql);
while ($_gr_user_info = $result->fetch()) {
    $array_admin_except[$_gr_user_info['userid']] = $_gr_user_info['userid'];
}

if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_from'], $m)) {
    $sfrom = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
} else {
    $sfrom = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
}
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_to'], $m)) {
    $sto = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
} else {
    $sto = mktime(23, 59, 59, nv_date('m', NV_CURRENTTIME), nv_date('d', NV_CURRENTTIME), nv_date('Y', NV_CURRENTTIME));
}

// table 1: thống kê theo Sales
$sql = 'SELECT userid, SUM(num_leads) as num_leads, SUM(num_opportunities) as num_opportunities FROM ' . NV_PREFIXLANG . '_' . $module_data . '_sale_static WHERE date >= ' . $sfrom . ' AND date <=' . $sto;

/**
 * Tìm theo người phụ trách
 * Khi xem ở chế độ `Sale nào xem sale đó` hoặc Ở chế độ xem `Tất cả` nhưng điều kiện là xem theo nhóm
 */

if ($array_search['admin_id'] != -1) {
    $sql .= ' AND userid = ' . $array_search['admin_id'];
} elseif (!empty($arr_admin_view) && (($data_config['view_static'] == 2 && !defined('NV_IS_SPADMIN')) || ($data_config['view_static'] == 1 && $array_search['group_id'] != -1))) {
    $sql .= ' AND userid IN (' . implode(',', $arr_admin_view) . ') ';
}

$sql .= ' GROUP BY userid';
$result = $db->query($sql);

$array_sales = array();
$array_sales[0]['num_leads'] = 0;
$array_sales[0]['num_opportunities'] = 0;
$array_sales[0]['num_vip'] = 0;
$array_sales[0]['order'] = [];
$array_sales[0]['total'] = 0;
$array_sales[0]['money'] = 0;
$array_sales[0]['discount'] = 0;
$array_sales[0]['price_reduce'] = 0;
$array_sales[0]['total_end'] = 0;
$array_sales[0]['discount_excess'] = 0;
$array_sales[0]['introduce_value'] = 0;
$array_sales[0]['successful_value'] = 0;
$array_sales[0]['caregiver_value'] = 0;
$default_sale = $array_sales[0];

$array_sales[-1]['num_leads'] = 0;
$array_sales[-1]['num_opportunities'] = 0;
$array_sales[-1]['num_vip'] = 0;
$array_sales[-1]['order'] = [];
$array_sales[-1]['total'] = 0;
$array_sales[-1]['money'] = 0;
$array_sales[-1]['discount'] = 0;
$array_sales[-1]['price_reduce'] = 0;
$array_sales[-1]['total_end'] = 0;
$array_sales[-1]['discount_excess'] = 0;
$array_sales[-1]['introduce_value'] = 0;
$array_sales[-1]['successful_value'] = 0;
$array_sales[-1]['caregiver_value'] = 0;
while ($row = $result->fetch()) {
    $row['num_vip'] = 0;
    $row['order'] = [];
    $row['total'] = 0;
    $row['money'] = 0;
    $row['discount'] = 0;
    $row['price_reduce'] = 0;
    $row['total_end'] = 0;
    $row['discount_excess'] = 0;
    $row['introduce_value'] = 0;
    $row['successful_value'] = 0;
    $row['caregiver_value'] = 0;
    $array_sales[$row['userid']] = $row;
}

$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE static_time >= ' . $sfrom . ' AND static_time <=' . $sto;
$admin_condition = '';
if ($array_search['admin_id'] != -1) {
    $admin_condition = ' AND (affiliate_userid = ' . $array_search['admin_id'] . ' OR caregiver_id = ' . $array_search['admin_id'] . ' OR admin_id = ' . $array_search['admin_id'] . ')';
}
if (($data_config['view_static'] == 2 && !defined('NV_IS_SPADMIN')) || ($data_config['view_static'] == 1 && $array_search['group_id'] != -1)) { // Trường hợp xem tất cả
    if (!empty($arr_admin_view)) { // Trường hợp là leader
        if ($array_search['display_confirm_the_order'] == 1) { // Chỉ hiển thị các đơn hàng được tính chốt đơn
            if ($array_search['admin_id'] != -1) {
                $sql .= ' AND admin_id = ' . $array_search['admin_id'];
            } else {
                $sql .= ' AND admin_id IN (' . implode(',', $arr_admin_view) . ')';
            }
        } else {
            if ($array_search['admin_id'] != -1) {
                $sql .= ' AND (affiliate_userid = ' . $array_search['admin_id'] . ' OR caregiver_id = ' . $array_search['admin_id'] . ' OR admin_id = ' . $array_search['admin_id'] . ')';
            } else {
                /**
                 * Lấy danh sách affiliate_userid, caregiver_id hoặc admin_id thuộc $arr_admin_view
                 * trường hợp nếu affiliate_userid thuộc $arr_admin_view nhưng mà admin_id hoặc caregiver_id không nằm trong $arr_admin_view thì bỏ qua
                 */
                $sql .= ' AND (
                    (
                        affiliate_userid IN (' . implode(',', $arr_admin_view) . ')
                        AND caregiver_id IN (' . implode(',', $arr_admin_view) . ')
                        AND admin_id IN (' . implode(',', $arr_admin_view) . ')
                    )
                    OR caregiver_id IN (' . implode(',', $arr_admin_view) . ')
                    OR admin_id IN (' . implode(',', $arr_admin_view) . ')
                )';
            }
        }
    } else {
        if ($array_search['admin_id'] != -1) {
            $sql .= $admin_condition;
        }
    }
} else {
    // Trường hợp sale nào xem sale đó
    if ($array_search['admin_id'] != -1) {
        $sql .= $admin_condition;
    }
}

if (!empty($array_admin_except)) {
    $sql .= ' AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')';
}

$result = $db->query($sql);
$array_vip = $_row = $_row_new = $array_vip_new = $_row_renewwal = $array_vip_renewwal = $arr_orders_general = [];
while ($row = $result->fetch()) {
    $arr_orders_general[$row['order_id']] = $row['order_id'];

    // nếu đơn hàng CTV thì người chốt đơn và gt là -1 để tính chung là CTV
    if ($row['official_collaborator'] == 1) {
        $row['affiliate_userid'] = -1;
        $row['admin_id'] = -1;
    }
    if ($array_search['display_confirm_the_order'] != 1) {
        if (!isset($array_sales[$row['affiliate_userid']])) {
            $array_sales[$row['affiliate_userid']] = $default_sale;
        }

        // Theo sale
        if (isset($array_sales[$row['affiliate_userid']]['introduce_value'])) {
            $array_sales[$row['affiliate_userid']]['introduce_value'] += $row['introduce_value'];
        } else {
            $array_sales[$row['affiliate_userid']]['introduce_value'] = $row['introduce_value'];
        }
    }
    /*
     * if (isset($array_sales[$row['affiliate_userid']])) {
     * $array_sales[$row['affiliate_userid']]['introduce_value'] += $row['introduce_value'];
     * }
     */
    if ($array_search['admin_id'] != -1) {
        if (isset($array_sales[$row['admin_id']]) and $row['admin_id'] == $array_search['admin_id']) {
            $array_sales[$array_search['admin_id']]['total'] += $row['total'];
            $array_sales[$array_search['admin_id']]['money'] += $row['money'];
            $array_sales[$array_search['admin_id']]['discount'] += $row['discount'];
            $array_sales[$array_search['admin_id']]['price_reduce'] += $row['price_reduce'];
            $array_sales[$array_search['admin_id']]['total_end'] += $row['total_end'];
            $array_sales[$array_search['admin_id']]['discount_excess'] += $row['discount_excess'];
            $array_sales[$array_search['admin_id']]['successful_value'] += $row['successful_value'];
            $array_sales[$array_search['admin_id']]['order'][$row['order_id']] = $row['order_id'];
        } else if ($row['admin_id'] == $array_search['admin_id']) {
            $array_sales[$array_search['admin_id']]['total'] = $row['total'];
            $array_sales[$array_search['admin_id']]['money'] = $row['money'];
            $array_sales[$array_search['admin_id']]['discount'] = $row['discount'];
            $array_sales[$array_search['admin_id']]['price_reduce'] = $row['price_reduce'];
            $array_sales[$array_search['admin_id']]['total_end'] = $row['total_end'];
            $array_sales[$array_search['admin_id']]['discount_excess'] = $row['discount_excess'];
            $array_sales[$array_search['admin_id']]['successful_value'] = $row['successful_value'];
            $array_sales[$array_search['admin_id']]['order'][$row['order_id']] = $row['order_id'];
        }
    } else {
        if (isset($array_sales[$row['admin_id']]['successful_value'])) {
            $array_sales[$row['admin_id']]['total'] += $row['total'];
            $array_sales[$row['admin_id']]['money'] += $row['money'];
            $array_sales[$row['admin_id']]['discount'] += $row['discount'];
            $array_sales[$row['admin_id']]['price_reduce'] += $row['price_reduce'];
            $array_sales[$row['admin_id']]['total_end'] += $row['total_end'];
            $array_sales[$row['admin_id']]['discount_excess'] += $row['discount_excess'];
            $array_sales[$row['admin_id']]['successful_value'] += $row['successful_value'];
            $array_sales[$row['admin_id']]['order'][$row['order_id']] = $row['order_id'];
        } else {
            $array_sales[$row['admin_id']]['userid'] = $row['admin_id'];
            $array_sales[$row['admin_id']]['num_vip'] = 0;
            $array_sales[$row['admin_id']]['order'][$row['order_id']] = $row['order_id'];
            $array_sales[$row['admin_id']]['num_leads'] = 0;
            $array_sales[$row['admin_id']]['num_opportunities'] = 0;
            $array_sales[$row['admin_id']]['total'] = $row['total'];
            $array_sales[$row['admin_id']]['money'] = $row['money'];
            $array_sales[$row['admin_id']]['discount'] = $row['discount'];
            $array_sales[$row['admin_id']]['price_reduce'] = $row['price_reduce'];
            $array_sales[$row['admin_id']]['total_end'] = $row['total_end'];
            $array_sales[$row['admin_id']]['discount_excess'] = $row['discount_excess'];
            $array_sales[$row['admin_id']]['successful_value'] = $row['successful_value'];
            $array_sales[$row['admin_id']]['introduce_value'] = 0;
        }
    }
    if (isset($array_sales[$row['caregiver_id']]['caregiver_value'])) {
        $array_sales[$row['caregiver_id']]['caregiver_value'] += $row['caregiver_value'];
    } else {
        $array_sales[$row['caregiver_id']]['caregiver_value'] = $row['caregiver_value'];
    }

    if (isset($array_sales[$row['caregiver_id']]) or isset($array_sales[$row['admin_id']])) {
        if (isset($array_sales[$row['caregiver_id']]['num_vip'])) {
            $array_sales[$row['caregiver_id']]['num_vip'] += 1;
        } else {
            $array_sales[$row['caregiver_id']]['num_vip'] = 1;
        }

        if ($row['caregiver_id'] != $row['admin_id']) {
            $array_sales[$row['admin_id']]['num_vip'] += 1;
        }

        // $array_sales[$admin_id]['order'][$row['order_id']] = $row['order_id'];
    }

    // THeo gói VIP

    if ($row['vip'] == '' and $row['siteid'] == 2) {
        $row['vip'] = 'nopphixacthuc';
    }
    if (!isset($_row[$row['vip']])) {
        $_row[$row['vip']]['num'] = 1;
        if ($array_search['admin_id'] != -1) {
            $_row[$row['vip']]['total'] = 0;
            $_row[$row['vip']]['money'] = 0;
            $_row[$row['vip']]['discount'] = 0;
            $_row[$row['vip']]['price_reduce'] = 0;
            $_row[$row['vip']]['total_end'] = 0;
            $_row[$row['vip']]['successful_value'] = 0;
            $_row[$row['vip']]['affilicate_value'] = $row['affilicate_value'];
            if ($row['affiliate_userid'] == $array_search['admin_id']) {
                $_row[$row['vip']]['introduce_value'] = $row['introduce_value'];
            } else {
                $_row[$row['vip']]['introduce_value'] = 0;
            }
            if ($row['admin_id'] == $array_search['admin_id']) {
                $_row[$row['vip']]['total'] = $row['total'];
                $_row[$row['vip']]['money'] = $row['money'];
                $_row[$row['vip']]['discount'] = $row['discount'];
                $_row[$row['vip']]['price_reduce'] = $row['price_reduce'];
                $_row[$row['vip']]['total_end'] = $row['total_end'];
                $_row[$row['vip']]['successful_value'] = $row['successful_value'];
            }
            if ($row['caregiver_id'] == $array_search['admin_id']) {
                $_row[$row['vip']]['caregiver_value'] = $row['caregiver_value'];
            }
            $array_vip[$row['vip']] = $_row[$row['vip']];
        } else {
            $_row[$row['vip']]['total'] = $row['total'];
            $_row[$row['vip']]['money'] = $row['money'];
            $_row[$row['vip']]['discount'] = $row['discount'];
            $_row[$row['vip']]['price_reduce'] = $row['price_reduce'];
            $_row[$row['vip']]['total_end'] = $row['total_end'];
            $_row[$row['vip']]['affilicate_value'] = $row['affilicate_value'];
            $_row[$row['vip']]['introduce_value'] = $row['introduce_value'];
            $_row[$row['vip']]['successful_value'] = $row['successful_value'];
            $_row[$row['vip']]['caregiver_value'] = $row['caregiver_value'];
            $array_vip[$row['vip']] = $_row[$row['vip']];
        }
    } else {
        $_row[$row['vip']]['num'] += 1;
        if ($array_search['admin_id'] != -1) {
            $_row[$row['vip']]['affilicate_value'] += $row['affilicate_value'];
            if ($row['affiliate_userid'] == $array_search['admin_id']) {
                $_row[$row['vip']]['introduce_value'] += $row['introduce_value'];
            } else {
                $_row[$row['vip']]['introduce_value'] += 0;
            }
            if ($row['admin_id'] == $array_search['admin_id']) {
                $_row[$row['vip']]['total'] += $row['total'];
                $_row[$row['vip']]['money'] += $row['money'];
                $_row[$row['vip']]['discount'] += $row['discount'];
                $_row[$row['vip']]['price_reduce'] += $row['price_reduce'];
                $_row[$row['vip']]['total_end'] += $row['total_end'];
                $_row[$row['vip']]['successful_value'] += $row['successful_value'];
            }
            if ($row['caregiver_id'] == $array_search['admin_id']) {
                if (isset($_row[$row['vip']]['caregiver_value'])) {
                    $_row[$row['vip']]['caregiver_value'] += $row['caregiver_value'];
                } else {
                    $_row[$row['vip']]['caregiver_value'] = $row['caregiver_value'];
                }
            }
            $array_vip[$row['vip']] = $_row[$row['vip']];
        } else {
            $_row[$row['vip']]['total'] += $row['total'];
            $_row[$row['vip']]['money'] += $row['money'];
            $_row[$row['vip']]['discount'] += $row['discount'];
            $_row[$row['vip']]['price_reduce'] += $row['price_reduce'];
            $_row[$row['vip']]['total_end'] += $row['total_end'];
            $_row[$row['vip']]['affilicate_value'] += $row['affilicate_value'];
            $_row[$row['vip']]['introduce_value'] += $row['introduce_value'];
            $_row[$row['vip']]['successful_value'] += $row['successful_value'];
            $_row[$row['vip']]['caregiver_value'] += $row['caregiver_value'];
            $array_vip[$row['vip']] = $_row[$row['vip']];
        }
    }

    // THeo gói VIP mới
    if ($row['is_renewal'] == 0 || $row['is_renewal'] == 2) {
        if (!isset($_row_new[$row['vip']])) {
            $_row_new[$row['vip']]['num'] = 1;
            if ($array_search['admin_id'] != -1) {
                $_row_new[$row['vip']]['affilicate_value'] = $row['affilicate_value'];
                if ($row['affiliate_userid'] == $array_search['admin_id']) {
                    $_row_new[$row['vip']]['introduce_value'] = $row['introduce_value'];
                } else {
                    $_row_new[$row['vip']]['introduce_value'] = 0;
                }

                if ($row['admin_id'] == $array_search['admin_id']) {
                    $_row_new[$row['vip']]['total'] = $row['total'];
                    $_row_new[$row['vip']]['money'] = $row['money'];
                    $_row_new[$row['vip']]['discount'] = $row['discount'];
                    $_row_new[$row['vip']]['price_reduce'] = $row['price_reduce'];
                    $_row_new[$row['vip']]['total_end'] = $row['total_end'];
                    $_row_new[$row['vip']]['successful_value'] = $row['successful_value'];
                }

                if ($row['caregiver_id'] == $array_search['admin_id']) {
                    $_row_new[$row['vip']]['caregiver_value'] = $row['caregiver_value'];
                }
                $array_vip_new[$row['vip']] = $_row_new[$row['vip']];
            } else {
                $_row_new[$row['vip']]['total'] = $row['total'];
                $_row_new[$row['vip']]['money'] = $row['money'];
                $_row_new[$row['vip']]['discount'] = $row['discount'];
                $_row_new[$row['vip']]['price_reduce'] = $row['price_reduce'];
                $_row_new[$row['vip']]['total_end'] = $row['total_end'];
                $_row_new[$row['vip']]['affilicate_value'] = $row['affilicate_value'];
                $_row_new[$row['vip']]['introduce_value'] = $row['introduce_value'];
                $_row_new[$row['vip']]['successful_value'] = $row['successful_value'];
                $_row_new[$row['vip']]['caregiver_value'] = $row['caregiver_value'];
                $array_vip_new[$row['vip']] = $_row_new[$row['vip']];
            }
        } else {
            $_row_new[$row['vip']]['num'] += 1;
            if ($array_search['admin_id'] != -1) {
                $_row_new[$row['vip']]['affilicate_value'] += $row['affilicate_value'];
                if ($row['affiliate_userid'] == $array_search['admin_id']) {
                    $_row_new[$row['vip']]['introduce_value'] += $row['introduce_value'];
                } else {
                    $_row_new[$row['vip']]['introduce_value'] += 0;
                }
                if ($row['admin_id'] == $array_search['admin_id']) {
                    if (isset($_row_new[$row['vip']]['total'])) {
                        $_row_new[$row['vip']]['total'] += $row['total'];
                        $_row_new[$row['vip']]['money'] += $row['money'];
                        $_row_new[$row['vip']]['discount'] += $row['discount'];
                        $_row_new[$row['vip']]['price_reduce'] += $row['price_reduce'];
                        $_row_new[$row['vip']]['total_end'] += $row['total_end'];
                        $_row_new[$row['vip']]['successful_value'] += $row['successful_value'];
                    } else {
                        $_row_new[$row['vip']]['total'] = $row['total'];
                        $_row_new[$row['vip']]['money'] = $row['money'];
                        $_row_new[$row['vip']]['discount'] = $row['discount'];
                        $_row_new[$row['vip']]['price_reduce'] = $row['price_reduce'];
                        $_row_new[$row['vip']]['total_end'] = $row['total_end'];
                        $_row_new[$row['vip']]['successful_value'] = $row['successful_value'];
                    }
                }
                if ($row['caregiver_id'] == $array_search['admin_id']) {
                    $_row_new[$row['vip']]['caregiver_value'] += $row['caregiver_value'];
                }
                $array_vip_new[$row['vip']] = $_row_new[$row['vip']];
            } else {
                $_row_new[$row['vip']]['total'] += $row['total'];
                $_row_new[$row['vip']]['money'] += $row['money'];
                $_row_new[$row['vip']]['discount'] += $row['discount'];
                $_row_new[$row['vip']]['price_reduce'] += $row['price_reduce'];
                $_row_new[$row['vip']]['total_end'] += $row['total_end'];
                $_row_new[$row['vip']]['affilicate_value'] += $row['affilicate_value'];
                $_row_new[$row['vip']]['introduce_value'] += $row['introduce_value'];
                $_row_new[$row['vip']]['successful_value'] += $row['successful_value'];
                $_row_new[$row['vip']]['caregiver_value'] += $row['caregiver_value'];
                $array_vip_new[$row['vip']] = $_row_new[$row['vip']];
            }
        }
    } else if ($row['is_renewal'] == 1) { // THeo gói VIP gia hạn
        if (!isset($_row_renewwal[$row['vip']])) {
            $_row_renewwal[$row['vip']]['num'] = 1;
            if ($array_search['admin_id'] != -1) {
                $_row_renewwal[$row['vip']]['affilicate_value'] = $row['affilicate_value'];
                if ($row['affiliate_userid'] == $array_search['admin_id']) {
                    $_row_renewwal[$row['vip']]['introduce_value'] = $row['introduce_value'];
                } else {
                    $_row_renewwal[$row['vip']]['introduce_value'] = 0;
                }
                if ($row['admin_id'] == $array_search['admin_id']) {
                    $_row_renewwal[$row['vip']]['total'] = $row['total'];
                    $_row_renewwal[$row['vip']]['money'] = $row['money'];
                    $_row_renewwal[$row['vip']]['discount'] = $row['discount'];
                    $_row_renewwal[$row['vip']]['price_reduce'] = $row['price_reduce'];
                    $_row_renewwal[$row['vip']]['total_end'] = $row['total_end'];
                    $_row_renewwal[$row['vip']]['successful_value'] = $row['successful_value'];
                }
                if ($row['caregiver_id'] == $array_search['admin_id']) {
                    $_row_renewwal[$row['vip']]['caregiver_value'] = $row['caregiver_value'];
                }
                $array_vip_renewwal[$row['vip']] = $_row_renewwal[$row['vip']];
            } else {
                $_row_renewwal[$row['vip']]['total'] = $row['total'];
                $_row_renewwal[$row['vip']]['money'] = $row['money'];
                $_row_renewwal[$row['vip']]['discount'] = $row['discount'];
                $_row_renewwal[$row['vip']]['price_reduce'] = $row['price_reduce'];
                $_row_renewwal[$row['vip']]['total_end'] = $row['total_end'];
                $_row_renewwal[$row['vip']]['affilicate_value'] = $row['affilicate_value'];
                $_row_renewwal[$row['vip']]['introduce_value'] = $row['introduce_value'];
                $_row_renewwal[$row['vip']]['successful_value'] = $row['successful_value'];
                $_row_renewwal[$row['vip']]['caregiver_value'] = $row['caregiver_value'];
                $array_vip_renewwal[$row['vip']] = $_row_renewwal[$row['vip']];
            }
        } else {
            $_row_renewwal[$row['vip']]['num'] += 1;
            if ($array_search['admin_id'] != -1) {
                $_row_renewwal[$row['vip']]['affilicate_value'] += $row['affilicate_value'];
                if ($row['affiliate_userid'] == $array_search['admin_id']) {
                    $_row_renewwal[$row['vip']]['introduce_value'] += $row['introduce_value'];
                } else {
                    $_row_renewwal[$row['vip']]['introduce_value'] += 0;
                }
                if ($row['admin_id'] == $array_search['admin_id']) {
                    $_row_renewwal[$row['vip']]['total'] += $row['total'];
                    $_row_renewwal[$row['vip']]['money'] += $row['money'];
                    $_row_renewwal[$row['vip']]['discount'] += $row['discount'];
                    $_row_renewwal[$row['vip']]['price_reduce'] += $row['price_reduce'];
                    $_row_renewwal[$row['vip']]['total_end'] += $row['total_end'];
                    $_row_renewwal[$row['vip']]['successful_value'] += $row['successful_value'];
                }
                if ($row['caregiver_id'] == $array_search['admin_id']) {
                    if (isset($_row_renewwal[$row['vip']]['caregiver_value'])) {
                        $_row_renewwal[$row['vip']]['caregiver_value'] += $row['caregiver_value'];
                    } else {
                        $_row_renewwal[$row['vip']]['caregiver_value'] = $row['caregiver_value'];
                    }
                }
                $array_vip_renewwal[$row['vip']] = $_row_renewwal[$row['vip']];
            } else {
                $_row_renewwal[$row['vip']]['total'] += $row['total'];
                $_row_renewwal[$row['vip']]['money'] += $row['money'];
                $_row_renewwal[$row['vip']]['discount'] += $row['discount'];
                $_row_renewwal[$row['vip']]['price_reduce'] += $row['price_reduce'];
                $_row_renewwal[$row['vip']]['total_end'] += $row['total_end'];
                $_row_renewwal[$row['vip']]['affilicate_value'] += $row['affilicate_value'];
                $_row_renewwal[$row['vip']]['introduce_value'] += $row['introduce_value'];
                $_row_renewwal[$row['vip']]['successful_value'] += $row['successful_value'];
                $_row_renewwal[$row['vip']]['caregiver_value'] += $row['caregiver_value'];
                $array_vip_renewwal[$row['vip']] = $_row_renewwal[$row['vip']];
            }
        }
    }
}

// Thống kê doanh thu theo ví tiền của sale
$array_wallet = [];
$array_wallet_point = [];
$sql_wallet = 'SELECT userid, SUM(recharge_day) as recharge_day, SUM(total_day) as total_day, SUM(bonus) as bonus, SUM(money_point_num) as money_point_num, SUM(money_point) as money_point, SUM(money_point_bonus) as money_point_bonus FROM nv4_vi_crmbidding_sale_static WHERE date >= ' . $sfrom . ' AND date <=' . $sto;

if (($data_config['view_static'] == 2 && !defined('NV_IS_SPADMIN')) || ($data_config['view_static'] == 1 && $array_search['group_id'] != -1)) {
    if (!empty($arr_admin_view) && $array_search['admin_id'] == -1) {
        $sql_wallet .= ' AND userid IN (' . implode(',', $arr_admin_view) . ') ';
    } elseif ($array_search['admin_id'] != -1) {
        $sql_wallet .= ' AND userid = ' . $array_search['admin_id'];
    }
} elseif ($array_search['admin_id'] != -1) {
    $sql_wallet .= ' AND userid = ' . $array_search['admin_id'];
}
$sql_wallet .= ' GROUP BY userid';
$result_wallet = $db->query($sql_wallet);

while ($row = $result_wallet->fetch()) {
    if ($row['recharge_day'] > 0) {
        $array_wallet[$row['userid']] = $row;
    }
    if ($row['money_point'] > 0) {
        $array_wallet_point[$row['userid']] = $row;
    }
}
// xử lý thêm trường hợp giao dịch tự động do khách nạp qua vnpay,
$array_wallet[0] = [];
$array_wallet[0]['recharge_day'] = 0;
$array_wallet[0]['total_day'] = 0;
$array_wallet[0]['bonus'] = 0;

$db->sqlreset();
$db->from($db_config['prefix'] . '_wallet_transaction');
$db->select('COUNT(CASE WHEN status = 1 THEN id END) num, SUM(CASE WHEN status = -1 AND transaction_type = 1 THEN -money_total WHEN status = 1 THEN money_total END) money_total');
$db->where('transaction_status = 4 AND transaction_time >= ' . $sfrom . ' AND transaction_time <=' . $sto . ' AND customer_id > 0 AND adminid = 0 AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')');
$result_0 = $db->query($db->sql())
    ->fetch();
if (!empty($result_0)) {
    $array_wallet[0]['recharge_day'] = $result_0['num'];
    $array_wallet[0]['total_day'] = $result_0['money_total'];
    $array_wallet[0]['bonus'] = ($result_0['money_total'] * 10) / 100;
}

// xử lý các th thống kê nạp điểm cho mkt
$array_wallet_point[0] = [];
$array_wallet_point[0]['money_point_num'] = 0;
$array_wallet_point[0]['money_point'] = 0;
$array_wallet_point[0]['money_point_bonus'] = 0;

$db->sqlreset();
$db->from($db_config['prefix'] . '_wallet_transaction');
$db->select('COUNT(*) num, sum(money_total) money_total');
$db->where('status = -1 AND transaction_status = 4 AND transaction_time >= ' . $sfrom . ' AND transaction_time <=' . $sto . ' AND id_sale_static = 0 AND obj_id > 0 AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')');

$result_0 = $db->query($db->sql())
    ->fetch();
if (!empty($result_0)) {
    $array_wallet_point[0]['money_point_num'] = $result_0['num'];
    $array_wallet_point[0]['money_point'] = $result_0['money_total'];
    $array_wallet_point[0]['money_point_bonus'] = ($result_0['money_total'] * 10) / 100;
}

// thống kê tổng:
$arr_static = [];

// tổng doanh thu
$sql = 'SELECT SUM(money) as total_money, SUM(discount) as discount, SUM(total) as total, SUM(price_reduce) as price_reduce, SUM(total_end) as total_end FROM ' . NV_PREFIXLANG . '_bidding_orders_general ';
if (!empty($array_admin_except)) {
    $sql .= ' WHERE userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')';
}
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $static_from = mktime(0, 0, 0, 01, 01, 2015);
    $static_to = mktime(23, 59, 59, 12, 31, nv_date('Y', NV_CURRENTTIME));
    $arr_static['all']['link'] = $base_url . 'static&time_from=' . nv_date('d/m/Y', $static_from) . '&time_to=' . nv_date('d/m/Y', $static_to);
    $arr_static['all']['total_money'] = $row['total_money'] > 0 ? number_format($row['total_money']) : 0;
    $arr_static['all']['total_end'] = $row['total_end'] > 0 ? number_format($row['total_end']) : 0;
    $arr_static['all']['sum_totalmoney'] = $row['total_money'] > 0 ? $row['total_money'] : 0;
    $arr_static['all']['sum_totalend'] = $row['total_end'] > 0 ? $row['total_end'] : 0;
}
$sql = 'SELECT SUM(CASE WHEN status = -1 AND transaction_type = 1 THEN -money_total WHEN status = 1 THEN money_total END) as s_money_total, SUM(CASE WHEN status = -1 AND obj_id > 0 AND id_sale_static >= 0 THEN money_total END) as s_money_point FROM ' . $db_config['prefix'] . '_wallet_transaction WHERE transaction_status = 4 AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')';
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_static['all']['wallet'] = $row['s_money_total'] > 0 ? number_format($row['s_money_total']) : 0;
    $arr_static['all']['point'] = $row['s_money_point'] > 0 ? number_format($row['s_money_point']) : 0;
    $arr_static['all']['sum_totalmoney'] += $row['s_money_total'] > 0 ? $row['s_money_total'] : 0;
    $arr_static['all']['sum_totalend'] += $row['s_money_point'] > 0 ? $row['s_money_point'] : 0;
}

$arr_static['all']['sum_totalmoney'] = number_format($arr_static['all']['sum_totalmoney']);
$arr_static['all']['sum_totalend'] = number_format($arr_static['all']['sum_totalend']);

// năm
$static_from = mktime(0, 0, 0, 01, 01, nv_date('Y', NV_CURRENTTIME));
$static_to = mktime(23, 59, 59, 12, 31, nv_date('Y', NV_CURRENTTIME));
$sql = 'SELECT SUM(money) as total_money, SUM(discount) as discount, SUM(total) as total, SUM(price_reduce) as price_reduce, SUM(total_end) as total_end FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE static_time >= ' . $static_from . ' AND static_time <=' . $static_to;
if (!empty($array_admin_except)) {
    $sql .= ' AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')';
}
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_static['year']['link'] = $base_url . 'static&time_from=' . nv_date('d/m/Y', $static_from) . '&time_to=' . nv_date('d/m/Y', $static_to);
    $arr_static['year']['total_money'] = $row['total_money'] > 0 ? number_format($row['total_money']) : 0;
    $arr_static['year']['total_end'] = $row['total_end'] > 0 ? number_format($row['total_end']) : 0;
    $arr_static['year']['sum_totalmoney'] = $row['total_money'] > 0 ? $row['total_money'] : 0;
    $arr_static['year']['sum_totalend'] = $row['total_end'] > 0 ? $row['total_end'] : 0;
}

$sql = 'SELECT SUM(CASE WHEN status = -1 AND transaction_type = 1 THEN -money_total WHEN status = 1 THEN money_total END) as s_money_total, SUM(CASE WHEN status = -1 AND obj_id > 0 AND id_sale_static >= 0 THEN money_total END) as s_money_point FROM ' . $db_config['prefix'] . '_wallet_transaction WHERE transaction_status = 4 AND transaction_time >= ' . $static_from . ' AND transaction_time <=' . $static_to . ' AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')';
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_static['year']['wallet'] = $row['s_money_total'] > 0 ? number_format($row['s_money_total']) : 0;
    $arr_static['year']['point'] = $row['s_money_point'] > 0 ? number_format($row['s_money_point']) : 0;
    $arr_static['year']['sum_totalmoney'] += $row['s_money_total'] > 0 ? $row['s_money_total'] : 0;
    $arr_static['year']['sum_totalend'] += $row['s_money_point'] > 0 ? $row['s_money_point'] : 0;
}
$arr_static['year']['sum_totalmoney'] = number_format($arr_static['year']['sum_totalmoney']);
$arr_static['year']['sum_totalend'] = number_format($arr_static['year']['sum_totalend']);

// quý
$month = intval(nv_date('n', NV_CURRENTTIME));

if ($month == 1 or $month == 2 or $month == 3) {
    $static_from = mktime(0, 0, 0, 01, 01, nv_date('Y', NV_CURRENTTIME));
    $static_to = mktime(23, 59, 59, 03, 31, nv_date('Y', NV_CURRENTTIME));
} else if ($month == 4 or $month == 5 or $month == 6) {
    $static_from = mktime(0, 0, 0, 04, 01, nv_date('Y', NV_CURRENTTIME));
    $static_to = mktime(23, 59, 59, 06, 30, nv_date('Y', NV_CURRENTTIME));
} else if ($month == 7 or $month == 8 or $month == 9) {
    $static_from = mktime(0, 0, 0, 7, 01, nv_date('Y', NV_CURRENTTIME));
    $static_to = mktime(23, 59, 59, 9, 30, nv_date('Y', NV_CURRENTTIME));
} else {
    $static_from = mktime(0, 0, 0, 10, 01, nv_date('Y', NV_CURRENTTIME));
    $static_to = mktime(23, 59, 59, 12, 31, nv_date('Y', NV_CURRENTTIME));
}

$sql = 'SELECT SUM(money) as total_money, SUM(discount) as discount, SUM(total) as total, SUM(price_reduce) as price_reduce, SUM(total_end) as total_end FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE static_time >= ' . $static_from . ' AND static_time <=' . $static_to;
if (!empty($array_admin_except)) {
    $sql .= ' AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')';
}
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_static['quy']['link'] = $base_url . 'static&time_from=' . nv_date('d/m/Y', $static_from) . '&time_to=' . nv_date('d/m/Y', $static_to);
    $arr_static['quy']['total_money'] = $row['total_money'] > 0 ? number_format($row['total_money']) : 0;
    $arr_static['quy']['total_end'] = $row['total_end'] > 0 ? number_format($row['total_end']) : 0;
    $arr_static['quy']['sum_totalmoney'] = $row['total_money'] > 0 ? $row['total_money'] : 0;
    $arr_static['quy']['sum_totalend'] = $row['total_end'] > 0 ? $row['total_end'] : 0;
}

$sql = 'SELECT SUM(CASE WHEN status = -1 AND transaction_type = 1 THEN -money_total WHEN status = 1 THEN money_total END) as s_money_total, SUM(CASE WHEN status = -1 AND obj_id > 0 AND id_sale_static >= 0 THEN money_total END) as s_money_point FROM ' . $db_config['prefix'] . '_wallet_transaction WHERE transaction_status = 4 AND transaction_time >= ' . $static_from . ' AND transaction_time <=' . $static_to . ' AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')';
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_static['quy']['wallet'] = $row['s_money_total'] > 0 ? number_format($row['s_money_total']) : 0;
    $arr_static['quy']['point'] = $row['s_money_point'] > 0 ? number_format($row['s_money_point']) : 0;
    $arr_static['quy']['sum_totalmoney'] += $row['s_money_total'] > 0 ? $row['s_money_total'] : 0;
    $arr_static['quy']['sum_totalend'] += $row['s_money_point'] > 0 ? $row['s_money_point'] : 0;
}

$arr_static['quy']['sum_totalmoney'] = number_format($arr_static['quy']['sum_totalmoney']);
$arr_static['quy']['sum_totalend'] = number_format($arr_static['quy']['sum_totalend']);

// tháng hiện tại
$last_day = get_last_day($month, nv_date('Y', NV_CURRENTTIME));
$static_from = mktime(0, 0, 0, $month, 01, nv_date('Y', NV_CURRENTTIME));
$static_to = mktime(23, 59, 59, $month, $last_day, nv_date('Y', NV_CURRENTTIME));
$sql = 'SELECT SUM(money) as total_money, SUM(discount) as discount, SUM(total) as total, SUM(price_reduce) as price_reduce, SUM(total_end) as total_end FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE static_time >= ' . $static_from . ' AND static_time <=' . $static_to;
if (!empty($array_admin_except)) {
    $sql .= ' AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')';
}
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_static['month']['link'] = $base_url . 'static&time_from=' . nv_date('d/m/Y', $static_from) . '&time_to=' . nv_date('d/m/Y', $static_to);
    $arr_static['month']['total_money'] = $row['total_money'] > 0 ? number_format($row['total_money']) : 0;
    $arr_static['month']['total_end'] = $row['total_end'] > 0 ? number_format($row['total_end']) : 0;
    $arr_static['month']['sum_totalmoney'] = $row['total_money'] > 0 ? $row['total_money'] : 0;
    $arr_static['month']['sum_totalend'] = $row['total_end'] > 0 ? $row['total_end'] : 0;
}
$sql = 'SELECT SUM(CASE WHEN status = -1 AND transaction_type = 1 THEN -money_total WHEN status = 1 THEN money_total END) as s_money_total, SUM(CASE WHEN status = -1 AND obj_id > 0 AND id_sale_static >= 0 THEN money_total END) as s_money_point FROM ' . $db_config['prefix'] . '_wallet_transaction WHERE transaction_status = 4 AND transaction_time >= ' . $static_from . ' AND transaction_time <=' . $static_to . ' AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')';
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_static['month']['wallet'] = $row['s_money_total'] > 0 ? number_format($row['s_money_total']) : 0;
    $arr_static['month']['point'] = $row['s_money_point'] > 0 ? number_format($row['s_money_point']) : 0;
    $arr_static['month']['sum_totalmoney'] += $row['s_money_total'] > 0 ? $row['s_money_total'] : 0;
    $arr_static['month']['sum_totalend'] += $row['s_money_point'] > 0 ? $row['s_money_point'] : 0;
}
$arr_static['month']['sum_totalmoney'] = number_format($arr_static['month']['sum_totalmoney']);
$arr_static['month']['sum_totalend'] = number_format($arr_static['month']['sum_totalend']);

// tháng trước
if ($month == 1) {
    $month = 12;
    $year = nv_date('Y', NV_CURRENTTIME) - 1;
} else {
    $month = $month - 1;
    $year = nv_date('Y', NV_CURRENTTIME);
}
$last_day = get_last_day($month, $year);
$static_from = mktime(0, 0, 0, $month, 01, $year);
$static_to = mktime(23, 59, 59, $month, $last_day, $year);

$sql = 'SELECT SUM(money) as total_money, SUM(discount) as discount, SUM(total) as total, SUM(price_reduce) as price_reduce, SUM(total_end) as total_end FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE static_time >= ' . $static_from . ' AND static_time <=' . $static_to;
if (!empty($array_admin_except)) {
    $sql .= ' AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')';
}
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_static['lastmonth']['link'] = $base_url . 'static&time_from=' . nv_date('d/m/Y', $static_from) . '&time_to=' . nv_date('d/m/Y', $static_to);
    $arr_static['lastmonth']['total_money'] = $row['total_money'] > 0 ? number_format($row['total_money']) : 0;
    $arr_static['lastmonth']['total_end'] = $row['total_end'] > 0 ? number_format($row['total_end']) : 0;
    $arr_static['lastmonth']['sum_totalmoney'] = $row['total_money'] > 0 ? $row['total_money'] : 0;
    $arr_static['lastmonth']['sum_totalend'] = $row['total_end'] > 0 ? $row['total_end'] : 0;
}

$sql = 'SELECT SUM(CASE WHEN status = -1 AND transaction_type = 1 THEN -money_total WHEN status = 1 THEN money_total END) as s_money_total, SUM(CASE WHEN status = -1 AND obj_id > 0 AND id_sale_static >= 0 THEN money_total END) as s_money_point FROM ' . $db_config['prefix'] . '_wallet_transaction WHERE transaction_status = 4 AND transaction_time >= ' . $static_from . ' AND transaction_time <=' . $static_to . ' AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')';
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_static['lastmonth']['wallet'] = $row['s_money_total'] > 0 ? number_format($row['s_money_total']) : 0;
    $arr_static['lastmonth']['point'] = $row['s_money_point'] > 0 ? number_format($row['s_money_point']) : 0;
    $arr_static['lastmonth']['sum_totalmoney'] += $row['s_money_total'] > 0 ? $row['s_money_total'] : 0;
    $arr_static['lastmonth']['sum_totalend'] += $row['s_money_point'] > 0 ? $row['s_money_point'] : 0;
}

$arr_static['lastmonth']['sum_totalmoney'] = number_format($arr_static['lastmonth']['sum_totalmoney']);
$arr_static['lastmonth']['sum_totalend'] = number_format($arr_static['lastmonth']['sum_totalend']);

// Lấy các ID đơn hàng của SALES
$arrayOrderId = [];
foreach ($array_sales as $key => $value) {
    if ($array_search['admin_id'] != -1 && $key != 0 && !empty($value['order'])) {
        foreach ($value['order'] as $key => $value) {
            $arrayOrderId[] = $value;
        }
    } else {
        if (!empty($value['order'])) {
            foreach ($value['order'] as $key => $value) {
                $arrayOrderId[] = $value;
            }
        }
    }
}
$array_source_money = [];
if (!empty($arrayOrderId)) {
    // theo nguồn tiền
    // $sql = 'SELECT source_money, COUNT(id) as countid, SUM(total_end) as total_end, SUM(total) as total FROM ' . NV_PREFIXLANG . '_bidding_orders WHERE id IN (SELECT order_id FROM `nv4_vi_bidding_orders_general` WHERE static_time >= ' . $sfrom . ' AND static_time <=' . $sto . ' AND status = 4 AND is_expired = 0 GROUP BY order_id ) AND update_static = 1 GROUP BY source_money';

    $sql = 'SELECT source_money, COUNT(id) as countid, SUM(total_end) as total_end, SUM(total) as total FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE order_id IN (' . implode(',', $arrayOrderId) . ') AND static_time >= ' . $sfrom . ' AND static_time <=' . $sto . ' ';
    if (($data_config['view_static'] == 2 && !defined('NV_IS_SPADMIN')) || ($data_config['view_static'] == 1 && $array_search['group_id'] != -1)) {
        if (!empty($arr_admin_view)) {
            if ($array_search['display_confirm_the_order'] == 1) {
                $sql .= ' AND admin_id IN (' . implode(',', $arr_admin_view) . ')'; // Chỉ hiển thị các đơn hàng được tính chốt đơn
            } else {
                $sql .= ' AND (affiliate_userid IN (' . implode(',', $arr_admin_view) . ') OR caregiver_id IN (' . implode(',', $arr_admin_view) . ') OR admin_id IN (' . implode(',', $arr_admin_view) . '))';
            }
        } elseif ($array_search['admin_id'] != -1) {
            $sql .= ' AND (affiliate_userid = ' . $array_search['admin_id'] . ' OR caregiver_id = ' . $array_search['admin_id'] . ' OR admin_id = ' . $array_search['admin_id'] . ')';
        }
    } elseif ($array_search['admin_id'] != -1) {
        $sql .= ' AND (affiliate_userid = ' . $array_search['admin_id'] . ' OR caregiver_id = ' . $array_search['admin_id'] . ' OR admin_id = ' . $array_search['admin_id'] . ')';
    }
    $sql .= '  GROUP BY source_money';
    $result = $db->query($sql);

    while ($row = $result->fetch()) {
        $array_source_money[$row['source_money']] = $row;
    }
}

// Thống kê nguồn tiền nạp vào ví
$array_source_money_wallet_title = [];
$array_source_money_wallet_title[0] = $nv_Lang->getModule('source_money0');
$array_source_money_wallet_title[1] = $nv_Lang->getModule('source_money1');
$array_source_money_wallet_title[2] = $nv_Lang->getModule('source_money2');
$array_source_money_wallet_title[3] = $nv_Lang->getModule('source_money3');
$array_source_money_wallet_title[4] = $nv_Lang->getModule('source_money4');
$array_source_money_wallet_title[5] = $nv_Lang->getModule('source_money5');
$array_source_money_wallet_title[6] = $nv_Lang->getModule('source_money6');
$array_source_money_wallet_title[7] = $nv_Lang->getModule('source_money7');
$array_source_money_wallet_title[8] = $nv_Lang->getModule('source_money8');

$array_source_money_wallet = [];
$array_source_money_wallet_sum = [];
$array_source_money_wallet_sum['num'] = 0;
$array_source_money_wallet_sum['money_total'] = 0;
$array_source_money_wallet_sum['money_total_format'] = '';

$sq_wallet = '';
if (($data_config['view_static'] == 2 && !defined('NV_IS_SPADMIN')) || ($data_config['view_static'] == 1 && $array_search['group_id'] != -1)) {
    if (!empty($arr_admin_view)) { // sale leader
        $sq_wallet .= ' AND (adminid IN (' . implode(',', $arr_admin_view) . ') OR customer_id IN (' . implode(',', $arr_admin_view) . '))';
    } elseif ($array_search['admin_id'] > 0) { // sale thường
        $sq_wallet .= ' AND (adminid = ' . $array_search['admin_id'] . ' OR customer_id = ' . $array_search['admin_id'] . ')';
    } elseif ($array_search['admin_id'] == 0) { // admin_id tính cho mkt
        $sq_wallet .= ' AND (adminid = 0 AND customer_id > 0)';
    }
} elseif ($array_search['admin_id'] > 0) { // sale thường
    $sq_wallet .= ' AND (adminid = ' . $array_search['admin_id'] . ' OR customer_id = ' . $array_search['admin_id'] . ')';
} elseif ($array_search['admin_id'] == 0) { // admin_id tính cho mkt
    $sq_wallet .= ' AND (adminid = 0 AND customer_id > 0)';
}

// loại bỏ các giao dịch sale tự nạp cho mình
$sq_wallet .= ' AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')';

$db->sqlreset()
    ->select('source_money, COUNT(CASE WHEN status = 1 THEN id END) num, SUM(CASE WHEN status = -1 AND transaction_type = 1 THEN -money_total WHEN status = 1 THEN money_total END) money_total')
    ->from($db_config['prefix'] . '_wallet_transaction')
    ->where('transaction_status = 4 AND transaction_time >= ' . $sfrom . ' AND transaction_time <=' . $sto . ' ' . $sq_wallet)
    ->group('source_money');
$result = $db->query($db->sql());
while ($row = $result->fetch(2)) {
    $array_source_money_wallet_sum['num'] += $row['num'];
    $array_source_money_wallet_sum['money_total'] += $row['money_total'];
    $row['money_total_format'] = $row['money_total'] > 0 ? number_format($row['money_total'], 0, ',', '.') : 0;
    $row['title'] = $array_source_money_wallet_title[$row['source_money']] ?? 'N/A';
    $array_source_money_wallet[$row['source_money']] = $row;
}
$array_source_money_wallet_sum['money_total_format'] = number_format($array_source_money_wallet_sum['money_total'], 0, ',', '.');

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);

$xtpl->assign('ARR_STATIC', $arr_static);

$xtpl->assign('LINK_UPDATE', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=static_order&amp;time_from=' . $array_search['time_from'] . '&amp;time_to=' . $array_search['time_to'] . '&amp;admin_id=' . $array_search['admin_id']);
$xtpl->assign('LINK_UPDATE_LEADS', NV_BASE_SITEURL . 'crawls/static_groups_leads.php?date_from=' . $sfrom . '&date_to=' . $sto . '&t=' . NV_CURRENTTIME);

$xtpl->assign('LINK_CHART', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=static&amp;showheader=0&amp;showchart=1&amp;time_from=' . $array_search['time_from'] . '&amp;time_to=' . $array_search['time_to'] . '&amp;caregiver_id=' . $array_search['admin_id']);

if (defined('NV_IS_SPADMIN') or $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) {
    $xtpl->parse('main.update');
    $xtpl->parse('main.update_leads');
}

if ($array_search['admin_id'] == 0) {
    $xtpl->assign('marketing_selected', ' selected="selected"');
}

// Xuất danh sách các nhóm và ở chế độ xem là: Tất cả
if ($data_config['view_static'] == 1) {
    // Nếu chưa chọn nhóm mà chọn admin_id thì sẽ tự động chọn nhóm theo admin_id
    if ($array_search['admin_id'] != -1 && $array_search['group_id'] == -1) {
        $array_search['group_id'] = $array_groups_users[$array_search['admin_id']]['group_id'] ?? -1;
    }

    foreach ($array_groups as $group_id => $value) {
        $xtpl->assign('OPTION_GROUP', array(
            'key' => $group_id,
            'title' => $value['title'],
            'selected' => $array_search['group_id'] == $group_id ? ' selected="selected"' : ''
        ));
        $xtpl->parse('main.view_static.loop_admin_group');
    }
    // Chế độ xem
    $xtpl->assign('DISPLAY_CONFIRM_ORDER', $array_search['display_confirm_the_order'] == 1 ? 'checked="checked"' : '');
    $xtpl->parse('main.view_static');
}

$xtpl->assign('ARRAY_SEARCH', $array_search);

$a = 0;
$static_total = array();
$static_total['num_leads'] = 0;
$static_total['num_opportunities'] = 0;
$static_total['num_order'] = 0;
$static_total['num_vip'] = 0;
$static_total['money'] = 0;
$static_total['discount'] = 0;
$static_total['total'] = 0;
$static_total['price_reduce'] = 0;
$static_total['total_end'] = 0;
$static_total['discount_excess'] = 0;
$static_total['introduce_value'] = 0;
$static_total['successful_value'] = 0;
$static_total['caregiver_value'] = 0;
$static_total['total_percent'] = 0;

foreach ($array_sales as $key_userid => $userid) {
    if (!empty($userid['money']) or !empty($userid['num_leads']) or !empty($userid['introduce_value'])) {

        if (empty($userid['order'])) {
            $userid['order'] = [];
        }

        $static_total['num_leads'] += $userid['num_leads'];
        $static_total['num_opportunities'] += $userid['num_opportunities'];
        $static_total['num_order'] += sizeof($userid['order']);
        $static_total['num_vip'] += $userid['num_vip'];
        $static_total['money'] += $userid['money'];
        $static_total['discount'] += $userid['discount'];
        $static_total['total'] += $userid['total'];
        $static_total['price_reduce'] += $userid['price_reduce'];
        $static_total['total_end'] += $userid['total_end'];
        $userid['discount_excess'] = !empty($userid['discount_excess']) ? $userid['discount_excess'] : 0;
        $static_total['discount_excess'] += $userid['discount_excess'];
        $static_total['introduce_value'] += $userid['introduce_value'];
        $static_total['successful_value'] += $userid['successful_value'];
        $userid['caregiver_value'] = isset($userid['caregiver_value']) ? $userid['caregiver_value'] : 0;
        $static_total['caregiver_value'] += $userid['caregiver_value'];

        $userid['total_percent'] = $userid['introduce_value'] + $userid['successful_value'] + $userid['caregiver_value'];
        $static_total['total_percent'] += $userid['total_percent'];

        $userid['stt'] = ++$a;
        if ($key_userid == 0) {
            $userid['full_name'] = $nv_Lang->getModule('marketing');
        } else if ($key_userid == -1) {
            $userid['full_name'] = $nv_Lang->getModule('ctv');
        } else {
            $userinfo = get_user($key_userid);
            $userid['full_name'] = nv_show_name_user($userinfo['first_name'], $userinfo['last_name'], $userinfo['username']);
        }
        $userid['num_order'] = sizeof($userid['order']);
        $userid['money'] = number_format($userid['money']);
        $userid['discount'] = number_format($userid['discount']);
        $userid['total'] = number_format($userid['total']);
        $userid['price_reduce'] = number_format($userid['price_reduce']);
        $userid['total_end'] = number_format($userid['total_end']);
        $userid['discount_excess'] = number_format($userid['discount_excess']);

        $userid['introduce_value'] = number_format($userid['introduce_value']);
        $userid['successful_value'] = number_format($userid['successful_value']);
        $userid['caregiver_value'] = number_format($userid['caregiver_value']);
        $userid['total_percent'] = number_format($userid['total_percent']);

        $userid['link_leads'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads&amp;showheader=0&amp;time_from=' . $array_search['time_from'] . '&amp;time_to=' . $array_search['time_to'] . '&amp;caregiver_id=' . $key_userid;
        $userid['link_opportunities'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=opportunities&amp;showheader=0&amp;time_from=' . $array_search['time_from'] . '&amp;time_to=' . $array_search['time_to'] . '&amp;caregiver_id=' . $key_userid;

        if ($key_userid == -1) {
            $userid['link_vip'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=customs&amp;showheader=0&amp;time_static_from=' . $array_search['time_from'] . '&amp;time_static_to=' . $array_search['time_to'] . '&amp;show_order_admin=2';
            $userid['link_order'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=payment&amp;showheader=0&amp;static_time_from=' . $array_search['time_from'] . '&amp;static_time_to=' . $array_search['time_to'] . '&amp;show_order_admin=2';
        } else {
            $userid['link_vip'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=customs&amp;showheader=0&amp;time_static_from=' . $array_search['time_from'] . '&amp;time_static_to=' . $array_search['time_to'] . '&amp;s_admin=' . $key_userid;
            $userid['link_order'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=payment&amp;showheader=0&amp;static_time_from=' . $array_search['time_from'] . '&amp;static_time_to=' . $array_search['time_to'] . '&amp;show_order_admin=1&amp;caregiver_id=' . $key_userid;
        }
        $xtpl->assign('DATA_USERS', $userid);

        if ($userid['userid'] == $admin_info['userid'] || in_array($userid['userid'], $arr_staff_view) || defined('NV_IS_SPADMIN') || $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) {
            if ($userid['userid'] != $admin_info['userid'] && isset($array_groups_users[$admin_info['userid']]['config']['show_chart']) && $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) {
                $xtpl->parse('main.loop.has_link_without_lead');
            } else {
                $xtpl->parse('main.loop.has_link');
            }
        } else {
            $xtpl->parse('main.loop.havent_link');
        }
        $xtpl->parse('main.loop');
    }
}

// tổng kết
foreach ($static_total as $key => $value) {
    $static_total[$key] = number_format($value);
}
$xtpl->assign('STATIC_TOTAL', $static_total);

ksort($array_vip);
$a = 0;
$static_vip = array();
$static_vip['num'] = 0;
$static_vip['total'] = 0;
$static_vip['money'] = 0;
$static_vip['discount'] = 0;
$static_vip['price_reduce'] = 0;
$static_vip['total_end'] = 0;
$static_vip['discount_excess'] = 0;
$static_vip['affilicate_value'] = 0;
$static_vip['introduce_value'] = 0;
$static_vip['successful_value'] = 0;
$static_vip['caregiver_value'] = 0;
$static_vip['total_percent'] = 0;
$static_vip_renewal = $static_vip_new = $static_vip;

foreach ($array_vip as $v => $vip) {
    $data_vip = array();
    $data_vip['stt'] = ++$a;
    $data_vip['vip_title'] = $nv_Lang->getModule('vip' . $v);
    $data_vip['num'] = $vip['num'];
    $data_vip['total'] = number_format($vip['total']);
    $data_vip['discount'] = number_format($vip['discount']);
    $data_vip['money'] = number_format($vip['money']);
    $data_vip['price_reduce'] = number_format($vip['price_reduce']);
    $data_vip['total_end'] = number_format($vip['total_end']);

    $vip['caregiver_value'] = isset($vip['caregiver_value']) ? $vip['caregiver_value'] : 0;

    $vip['total_percent'] = $vip['introduce_value'] + $vip['successful_value'] + $vip['caregiver_value'];
    $static_vip['total_percent'] += $vip['total_percent'];

    $data_vip['affilicate_value'] = number_format($vip['affilicate_value']);
    $data_vip['introduce_value'] = number_format($vip['introduce_value']);
    $data_vip['successful_value'] = number_format($vip['successful_value']);
    $data_vip['caregiver_value'] = number_format($vip['caregiver_value']);
    $data_vip['total_percent'] = number_format($vip['total_percent']);

    $data_vip['link_order'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=payment&amp;showheader=0&amp;static_time_from=' . $array_search['time_from'] . '&amp;static_time_to=' . $array_search['time_to'] . '&amp;vip=' . $v;
    $data_vip['link_vip'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=customs&amp;showheader=0&amp;time_static_from=' . $array_search['time_from'] . '&amp;time_static_to=' . $array_search['time_to'] . '&amp;s_vip=' . $v;
    if ($array_search['admin_id'] != -1) {
        $data_vip['link_order'] .= '&amp;show_order_admin=1&amp;caregiver_id=' . $array_search['admin_id'];
        $data_vip['link_vip'] .= '&amp;show_order_admin=1&amp;s_admin=' . $array_search['admin_id'];
    }
    if ($v == 33 or $v == 44) {
        $data_vip['link_vip'] = $data_vip['link_order'];
    }
    $xtpl->assign('DATA_VIP', $data_vip);

    if ($admin_info['userid'] == $array_search['admin_id'] || in_array($array_search['admin_id'], $arr_staff_view) || defined('NV_IS_SPADMIN') || $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) {
        $xtpl->parse('main.loopvip.has_link');
    } else {
        $xtpl->parse('main.loopvip.havent_link');
    }
    $xtpl->parse('main.loopvip');

    $static_vip['num'] += $vip['num'];
    $static_vip['total'] += $vip['total'];
    $static_vip['money'] += $vip['money'];
    $static_vip['discount'] += $vip['discount'];
    $static_vip['price_reduce'] += $vip['price_reduce'];
    $static_vip['total_end'] += $vip['total_end'];

    $static_vip['affilicate_value'] += $vip['affilicate_value'];
    $static_vip['introduce_value'] += $vip['introduce_value'];
    $static_vip['successful_value'] += $vip['successful_value'];
    $static_vip['caregiver_value'] += $vip['caregiver_value'];
}
foreach ($static_vip as $key => $value) {
    $static_vip[$key] = number_format($value);
}
$xtpl->assign('STATIC_VIP', $static_vip);

$a = 0;
foreach ($array_vip_new as $v => $vip) {

    $vip['total'] = isset($vip['total']) ? $vip['total'] : 0;
    $vip['discount'] = isset($vip['discount']) ? $vip['discount'] : 0;
    $vip['money'] = isset($vip['money']) ? $vip['money'] : 0;
    $vip['price_reduce'] = isset($vip['price_reduce']) ? $vip['price_reduce'] : 0;
    $vip['total_end'] = isset($vip['total_end']) ? $vip['total_end'] : 0;

    $data_vip = array();
    $data_vip['stt'] = ++$a;
    $data_vip['vip_title'] = $nv_Lang->getModule('vip' . $v);
    $data_vip['num'] = $vip['num'];
    $data_vip['total'] = number_format($vip['total']);
    $data_vip['discount'] = number_format($vip['discount']);
    $data_vip['money'] = number_format($vip['money']);
    $data_vip['price_reduce'] = number_format($vip['price_reduce']);
    $data_vip['total_end'] = number_format($vip['total_end']);

    $vip['introduce_value'] = isset($vip['introduce_value']) ? $vip['introduce_value'] : 0;
    $vip['successful_value'] = isset($vip['successful_value']) ? $vip['successful_value'] : 0;
    $vip['caregiver_value'] = isset($vip['caregiver_value']) ? $vip['caregiver_value'] : 0;
    $vip['total_percent'] = $vip['introduce_value'] + $vip['successful_value'] + $vip['caregiver_value'];
    $static_vip_new['total_percent'] += $vip['total_percent'];

    $data_vip['affilicate_value'] = number_format($vip['affilicate_value']);
    $data_vip['introduce_value'] = number_format($vip['introduce_value']);
    $data_vip['successful_value'] = number_format($vip['successful_value']);
    $data_vip['caregiver_value'] = number_format($vip['caregiver_value']);
    $data_vip['total_percent'] = number_format($vip['total_percent']);

    $data_vip['link_order'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=payment&amp;showheader=0&amp;static_time_from=' . $array_search['time_from'] . '&amp;static_time_to=' . $array_search['time_to'] . '&amp;renewal=0&amp;vip=' . $v;
    $data_vip['link_vip'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=customs&amp;showheader=0&amp;time_static_from=' . $array_search['time_from'] . '&amp;time_static_to=' . $array_search['time_to'] . '&amp;renewal=2&amp;s_vip=' . $v;
    if ($array_search['admin_id'] != -1) {
        $data_vip['link_order'] .= '&amp;show_order_admin=1&amp;caregiver_id=' . $array_search['admin_id'];
        $data_vip['link_vip'] .= '&amp;show_order_admin=1&amp;s_admin=' . $array_search['admin_id'];
    }
    if ($v == 33 or $v == 44) {
        $data_vip['link_vip'] = $data_vip['link_order'];
    }
    $xtpl->assign('DATA_VIP_NEW', $data_vip);

    if ($admin_info['userid'] == $array_search['admin_id'] || in_array($array_search['admin_id'], $arr_staff_view) || defined('NV_IS_SPADMIN') || $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) {
        $xtpl->parse('main.loopvip_new.has_link');
    } else {
        $xtpl->parse('main.loopvip_new.havent_link');
    }
    $xtpl->parse('main.loopvip_new');

    $static_vip_new['num'] += $vip['num'];
    $static_vip_new['total'] += $vip['total'];
    $static_vip_new['money'] += $vip['money'];
    $static_vip_new['discount'] += $vip['discount'];
    $static_vip_new['price_reduce'] += $vip['price_reduce'];
    $static_vip_new['total_end'] += $vip['total_end'];
    $static_vip_new['affilicate_value'] += $vip['affilicate_value'];
    $static_vip_new['introduce_value'] += $vip['introduce_value'];
    $static_vip_new['successful_value'] += $vip['successful_value'];
    $static_vip_new['caregiver_value'] += $vip['caregiver_value'];
}
foreach ($static_vip_new as $key => $value) {
    $static_vip_new[$key] = number_format($value);
}
$xtpl->assign('STATIC_VIP_NEW', $static_vip_new);

$a = 0;
foreach ($array_vip_renewwal as $v => $vip) {
    $vip['total'] = isset($vip['total']) ? $vip['total'] : 0;
    $vip['discount'] = isset($vip['discount']) ? $vip['discount'] : 0;
    $vip['money'] = isset($vip['money']) ? $vip['money'] : 0;
    $vip['price_reduce'] = isset($vip['price_reduce']) ? $vip['price_reduce'] : 0;
    $vip['total_end'] = isset($vip['total_end']) ? $vip['total_end'] : 0;

    $data_vip = array();
    $data_vip['stt'] = ++$a;
    $data_vip['vip_title'] = $nv_Lang->getModule('vip' . $v);
    $data_vip['num'] = $vip['num'];
    $data_vip['total'] = number_format($vip['total']);
    $data_vip['discount'] = number_format($vip['discount']);
    $data_vip['money'] = number_format($vip['money']);
    $data_vip['price_reduce'] = number_format($vip['price_reduce']);
    $data_vip['total_end'] = number_format($vip['total_end']);

    $vip['introduce_value'] = isset($vip['introduce_value']) ? $vip['introduce_value'] : 0;
    $vip['successful_value'] = isset($vip['successful_value']) ? $vip['successful_value'] : 0;
    $vip['caregiver_value'] = isset($vip['caregiver_value']) ? $vip['caregiver_value'] : 0;

    $vip['total_percent'] = $vip['introduce_value'] + $vip['successful_value'] + $vip['caregiver_value'];
    $static_vip_renewal['total_percent'] += $vip['total_percent'];

    $data_vip['affilicate_value'] = number_format($vip['affilicate_value']);
    $data_vip['introduce_value'] = number_format($vip['introduce_value']);
    $data_vip['successful_value'] = number_format($vip['successful_value']);
    $data_vip['caregiver_value'] = number_format($vip['caregiver_value']);
    $data_vip['total_percent'] = number_format($vip['total_percent']);

    $data_vip['link_order'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=payment&amp;showheader=0&amp;static_time_from=' . $array_search['time_from'] . '&amp;static_time_to=' . $array_search['time_to'] . '&amp;renewal=1&amp;vip=' . $v;
    $data_vip['link_vip'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=customs&amp;showheader=0&amp;time_static_from=' . $array_search['time_from'] . '&amp;time_static_to=' . $array_search['time_to'] . '&amp;renewal=1&amp;s_vip=' . $v;
    if ($array_search['admin_id'] != -1) {
        $data_vip['link_order'] .= '&amp;show_order_admin=1&amp;caregiver_id=' . $array_search['admin_id'];
        $data_vip['link_vip'] .= '&amp;show_order_admin=1&amp;s_admin=' . $array_search['admin_id'];
    }
    if ($v == 33 or $v == 44) {
        $data_vip['link_vip'] = $data_vip['link_order'];
    }
    $xtpl->assign('DATA_VIP_RENEWAL', $data_vip);

    if ($admin_info['userid'] == $array_search['admin_id'] || in_array($array_search['admin_id'], $arr_staff_view) || defined('NV_IS_SPADMIN') || $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) {
        $xtpl->parse('main.loopvip_renewal.has_link');
    } else {
        $xtpl->parse('main.loopvip_renewal.havent_link');
    }
    $xtpl->parse('main.loopvip_renewal');

    $static_vip_renewal['num'] += $vip['num'];
    $static_vip_renewal['total'] += $vip['total'];
    $static_vip_renewal['money'] += $vip['money'];
    $static_vip_renewal['discount'] += $vip['discount'];
    $static_vip_renewal['price_reduce'] += $vip['price_reduce'];
    $static_vip_renewal['total_end'] += $vip['total_end'];
    $static_vip_renewal['affilicate_value'] += $vip['affilicate_value'];
    $static_vip_renewal['introduce_value'] += $vip['introduce_value'];
    $static_vip_renewal['successful_value'] += $vip['successful_value'];
    $static_vip_renewal['caregiver_value'] += $vip['caregiver_value'];
}

foreach ($static_vip_renewal as $key => $value) {
    $static_vip_renewal[$key] = number_format($value);
}
$xtpl->assign('STATIC_VIP_RENEWAL', $static_vip_renewal);

// thống kê nguồn đơn hàng
$arr_source_money = [
    0 => $nv_Lang->getModule('source_money0'),
    1 => $nv_Lang->getModule('source_money1'),
    2 => $nv_Lang->getModule('source_money2'),
    3 => $nv_Lang->getModule('source_money3'),
    4 => $nv_Lang->getModule('source_money4'),
    5 => $nv_Lang->getModule('source_money5'),
    6 => $nv_Lang->getModule('source_money6'),
    7 => $nv_Lang->getModule('source_money7'),
    8 => $nv_Lang->getModule('source_money8')
];
if ((isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) or defined('NV_IS_SPADMIN')) {
    // table 3
    $a = 0;
    $static_source_money = array();
    $static_source_money['num'] = 0;
    $static_source_money['total_end'] = 0;
    $static_source_money['total'] = 0;
    foreach ($array_source_money as $key => $source_money) {
        $static_source_money['num'] += $source_money['countid'];
        $static_source_money['total'] += $source_money['total'];
        $static_source_money['total_end'] += $source_money['total_end'];
        $source_money['stt'] = ++$a;
        $source_money['title'] = $arr_source_money[$key];
        $source_money['total_end'] = number_format($source_money['total_end']);
        $source_money['total'] = number_format($source_money['total']);
        $source_money['link_order'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=payment&amp;showheader=0&amp;static_time_from=' . $array_search['time_from'] . '&amp;static_time_to=' . $array_search['time_to'] . '&amp;source_money=' . $key . '&amp;admin_id=' . $array_search['admin_id'];
        $xtpl->assign('SOURCE_MONEY', $source_money);
        if ($admin_info['userid'] == $array_search['admin_id'] || in_array($array_search['admin_id'], $arr_staff_view) || defined('NV_IS_SPADMIN') || $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) {
            $xtpl->parse('main.source_money.loop.has_link');
        } else {
            $xtpl->parse('main.source_money.loop.havent_link');
        }
        $xtpl->parse('main.source_money.loop');
    }
    $static_source_money['total'] = number_format($static_source_money['total']);
    $static_source_money['total_end'] = number_format($static_source_money['total_end']);
    $xtpl->assign('STATIC_SOURCE_MONEY', $static_source_money);
    $xtpl->parse('main.source_money');
}

// thống kê giao dịch nạp tiền vào ví của sale
$stt_wallet = 0;
$static_wallet = [
    'total_recharge' => 0,
    'total_money' => 0,
    'total_bonus' => 0
];
foreach ($array_wallet as $key => $val) {
    if ($val['recharge_day'] <= 0) {
        continue;
    }
    if ($array_search['admin_id'] != -1 && $key == 0) {
        continue; // Bỏ qua nếu đang xem thông kê theo từng sale
    }
    $val['stt_wallet'] = ++$stt_wallet;
    $static_wallet['total_recharge'] += $val['recharge_day'];
    $static_wallet['total_money'] += $val['total_day'];
    $static_wallet['total_bonus'] += $val['bonus'];

    if ($key == 0) {
        $val['full_name'] = $nv_Lang->getModule('marketing');
    } elseif ($key == -1) {
        $val['full_name'] = 'N/A';
    } else {
        $userinfo = get_user($key);
        $val['full_name'] = nv_show_name_user($userinfo['first_name'], $userinfo['last_name'], $userinfo['username']);
    }
    $val['bonus'] = $val['bonus'] > 0 ? number_format($val['bonus']) : $val['bonus'];
    $val['total_day'] = $val['total_day'] > 0 ? number_format($val['total_day']) : $val['total_day'];
    $val['link_wallet'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=wallet&amp;' . NV_OP_VARIABLE . '=transaction&amp;ad_id_static=' . $key . '&amp;trf=' . str_replace('/', '.', $array_search['time_from']) . '&amp;trt=' . str_replace('/', '.', $array_search['time_to']) . '&amp;st=1&amp;tst=4&amp;per_page=50&amp;show_static_crm=1';

    $xtpl->assign('DATA_WALLET', $val);

    if ($admin_info['userid'] == $key || in_array($key, $arr_staff_view) || defined('NV_IS_SPADMIN') || $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) {
        $xtpl->parse('main.loop_wallet.has_link');
    } else {
        $xtpl->parse('main.loop_wallet.havent_link');
    }
    $xtpl->parse('main.loop_wallet');
}

$static_wallet['total_bonus'] = number_format($static_wallet['total_bonus']);
$static_wallet['total_recharge'] = number_format($static_wallet['total_recharge']);
$static_wallet['total_money'] = number_format($static_wallet['total_money']);
$xtpl->assign('STATIC_WALLET_TOTAL', $static_wallet);

// thống kê giao dịch nạp điểm của sale theo chính sách ở https://vinades.org/dauthau/dauthau.info/-/issues/2119
$stt_wallet_point = 0;
$static_wallet_point = [
    'money_point_num' => 0,
    'money_point' => 0,
    'money_point_bonus' => 0
];
foreach ($array_wallet_point as $key => $val) {
    if ($array_search['admin_id'] != -1 && $key == 0) {
        continue; // Bỏ qua nếu đang xem thông kê theo từng sale
    }
    $val['stt_wallet'] = ++$stt_wallet_point;
    $static_wallet_point['money_point_num'] += $val['money_point_num'];
    $static_wallet_point['money_point'] += $val['money_point'];
    $static_wallet_point['money_point_bonus'] += $val['money_point_bonus'];

    if ($key == 0) {
        $val['full_name'] = $nv_Lang->getModule('marketing');
    } elseif ($key == -1) {
        $val['full_name'] = 'N/A';
    } else {
        $userinfo = get_user($key);
        $val['full_name'] = nv_show_name_user($userinfo['first_name'], $userinfo['last_name'], $userinfo['username']);
    }
    $val['money_point_bonus'] = $val['money_point_bonus'] > 0 ? number_format($val['money_point_bonus']) : $val['money_point_bonus'];
    $val['money_point'] = $val['money_point'] > 0 ? number_format($val['money_point']) : $val['money_point'];
    $val['link_wallet'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=wallet&amp;' . NV_OP_VARIABLE . '=transaction&amp;ad_id_static=' . $key . '&amp;trf=' . str_replace('/', '.', $array_search['time_from']) . '&amp;trt=' . str_replace('/', '.', $array_search['time_to']) . '&amp;st=-1&amp;tst=4&amp;per_page=50&amp;show_static_crm=1';

    $xtpl->assign('DATA_WALLET_POINT', $val);

    if ($admin_info['userid'] == $key || in_array($key, $arr_staff_view) || defined('NV_IS_SPADMIN') || $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) {
        $xtpl->parse('main.loop_wallet_point.has_link');
    } else {
        $xtpl->parse('main.loop_wallet_point.havent_link');
    }
    $xtpl->parse('main.loop_wallet_point');
}

$static_wallet_point['money_point_num'] = number_format($static_wallet_point['money_point_num']);
$static_wallet_point['money_point'] = number_format($static_wallet_point['money_point']);
$static_wallet_point['money_point_bonus'] = number_format($static_wallet_point['money_point_bonus']);
$xtpl->assign('STATIC_WALLET_POINT_TOTAL', $static_wallet_point);

// thống kê nguồn ví tiền
if (!empty($array_source_money_wallet)) {
    $stt = 0;
    foreach ($array_source_money_wallet as $source_money_wallet) {
        $source_money_wallet['stt'] = ++$stt;
        $source_money_wallet['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=wallet&amp;' . NV_OP_VARIABLE . '=transaction&amp;source_money=' . $source_money_wallet['source_money'] . '&amp;trf=' . str_replace('/', '.', $array_search['time_from']) . '&amp;trt=' . str_replace('/', '.', $array_search['time_to']) . '&amp;st=1&amp;tst=4&amp;per_page=50&amp;show_static_crm=1&amp;admin_id=' . $array_search['admin_id'];
        $xtpl->assign('SOURCE_MONEY_WALLET', $source_money_wallet);

        if ($admin_info['userid'] == $array_search['admin_id'] || in_array($array_search['admin_id'], $arr_staff_view) || defined('NV_IS_SPADMIN') || $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) {
            $xtpl->parse('main.source_money_wallet.loop.has_link');
        } else {
            $xtpl->parse('main.source_money_wallet.loop.havent_link');
        }
        $xtpl->parse('main.source_money_wallet.loop');
    }
    $xtpl->assign('SOURCE_MONEY_WALLET_SUM', $array_source_money_wallet_sum);
    $xtpl->parse('main.source_money_wallet');
}

if ((isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1) or defined('NV_IS_SPADMIN')) {
    $xtpl->parse('main.static_all');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';

function CallAPI($infoAPI, $API, $module_name = 'bidding')
{
    if (isset($infoAPI['page'])) {
        $infoAPI['page'] = intval($infoAPI['page']);
    }

    if (isset($infoAPI['perpage'])) {
        $infoAPI['perpage'] = intval($infoAPI['perpage']);
    }

    foreach ($infoAPI as $key => $value) {
        $params_customs[$key] = $value;
    }

    $api = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
    $api->setModule($module_name)
        ->setLang('vi')
        ->setAction($API)
        ->setData($params_customs);
    $result = $api->execute();
    $error = $api->getError();
    $data = [];
    if (empty($error) and $result['status'] == 'success') {
        $data = $result;
    }
    return $data;
}
