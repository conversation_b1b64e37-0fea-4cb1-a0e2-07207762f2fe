<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdateOrdersGeneral implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'ordersgeneral';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $row = [];
        $error = '';
        $error_code = '0000';
        $id = $nv_Request->get_int('id', 'post', '0');
        $admin_id = $nv_Request->get_int('admin_id', 'post', '0');
        if ($id > 0) {
            $row_old = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE id=' . $id)->fetch();
            if (empty($row_old)) {
                return $this->result->setError()
                    ->setCode('2004')
                    ->setMessage('Row not exit')
                    ->getResult();
            }

            $data = $nv_Request->get_array('data', 'post');
            if (!is_array($data)) {
                return $this->result->setError()
                    ->setCode('2000')
                    ->setMessage('Param data invalid to json')
                    ->getResult();
            }
            $row = $data;

            $keys_check = [
                'siteid',
                'order_id',
                'vip',
                'type_export',
                'userid',
                'profileid',
                'affiliate_userid',
                'official_collaborator',
                'promo_userid',
                'admin_id',
                'caregiver_id',
                'money',
                'discount',
                'total',
                'price_reduce',
                'total_end',
                'affilicate_value',
                'introduce_value',
                'successful_value',
                'caregiver_value',
                'status',
                'is_expired',
                'static_time',
                'is_renewal',
                'source_leads',
                'invoice_number',
                'taxes_fees',
                'add_adminid',
                'source_money',
                'noi_dung',
                'transaction_id',
            ];

            if (!empty($data)) {
                // check $field
                $array_sql = [];
                foreach ($data as $key => $value) {
                    if (!in_array($key, $keys_check)) {
                        return $this->result->setError()
                            ->setCode('2002')
                            ->setMessage('Missing field ' . $key . ' in otherdata')
                            ->getResult();
                    }
                    $array_sql[$key] = $key . '=:' . $key;
                }

                if (empty($error)) {
                    try {
                        $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_bidding_orders_general SET ' . implode(',', $array_sql) . ' WHERE id=' . $id);
                        foreach ($row as $key => $value) {
                            $stmt->bindValue(':' . $key, $value, PDO::PARAM_STR);
                        }
                        $exc = $stmt->execute();
                        if ($exc) {
                            $nv_Cache->delMod($module_name);
                            nv_insert_logs(NV_LANG_DATA, $module_name, 'Update order', 'ID: ' . $id, $admin_id);
                            $this->result->setSuccess();
                            $this->result->set('id', $id);
                        }
                    } catch (PDOException $e) {
                        $this->result->setError()
                            ->setCode('3000')
                            ->setMessage(print_r($e, true));
                    }
                } else {
                    return $this->result->setError()
                        ->setCode($error_code)
                        ->setMessage($error)
                        ->getResult();
                }
            } else {
                return $this->result->setError()
                    ->setCode('2001')
                    ->setMessage('Param data empty')
                    ->getResult();
            }
        } else {
            return $this->result->setError()
                ->setCode('2003')
                ->setMessage('ID is integer, more than 0' . $id)
                ->getResult();
        }

        return $this->result->getResult();
    }
}
