<?php

/**
 * NukeViet Content Management System
 * @version 5.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2025 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */
namespace NukeViet\ElasticSearch;

use Elastic\Elasticsearch;

/**
 * NukeViet\ElasticSearch\Functions
 *
 * @package NukeViet\ElasticSearch
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2025 VINADES.,JSC. All rights reserved
 * @version 5.x
 * @access public
 */
class Functions
{

    private $_client;

    private $_index;

    /**
     *
     * @param mixed $elas_host,
     *            $elas_port, $elas_index
     *            Elasticsearch::__construct()
     */
    public function __construct($elas_host, $elas_port, $elas_index, $elas_user, $elas_pass)
    {
        $hosts = array(
            $elas_host . ':' . $elas_port
        );
        $this->_client = Elasticsearch\ClientBuilder::create()->setBasicAuthentication($elas_user, $elas_pass)
            ->setHosts($hosts)
            ->setRetries(0)
            ->build();
        $this->_index = $elas_index;
    }

    /**
     *
     * @param mixed $table,
     *            $id, $body
     * @return
     */
    public function insert_data($table, $id, $body)
    {
        $params = [
            'index' => $this->_index,
            // 'type' => $table,
            'id' => $id,
            'body' => $body
        ];
        $response = $this->_client->index($params)->asArray();

        return $response;
    }

    /**
     *
     * @param mixed $table,
     *            $id, $body
     * @return
     */
    public function update_data($table, $id, $body)
    {
        $params = array();
        $params['index'] = $this->_index;
        // $params['type'] = $table;
        $params['id'] = $id; // gan id= id cua rowcontent
        $params['body']['doc'] = $body;

        $response = $this->_client->update($params)->asArray();

        return $response;
    }

    /**
     *
     * @param mixed $table,
     *            $id, $body
     * @return
     */
    public function delete_data($table, $id)
    {
        $params = array();
        $params['index'] = $this->_index;
        // $params['type'] = $table;
        $params['id'] = $id;

        $response = $this->_client->delete($params);

        return $response;
    }

    /**
     *
     * @param mixed $table,
     *            $params
     * @return
     */
    public function search_data($table, $array_query_elastic)
    {
        $_start_time =microtime(true);
        $params = array();
        $params['index'] = $this->_index;
        // $params['type'] = $table;
        $params['body'] = $array_query_elastic;

        $return = $this->_client->search($params)->asArray();
        $_runtime = round(microtime(true) - $_start_time, 2);

        $sizeof = sizeof($return, 1);
        if ($_runtime > 0.5 or $sizeof > 5000) {
            $_arr_log = [];
            $_arr_log['index'] = $this->_index;
            $_arr_log['sizeof'] = $sizeof;
            $_arr_log['runtime'] = $_runtime;
            $_arr_log['start_time'] = $_start_time;
            $_arr_log['url'] = (empty($_SERVER['REQUEST_URI'])) ? $_SERVER['PHP_SELF'] . '?' . $_SERVER['QUERY_STRING'] : $_SERVER['REQUEST_URI'];

            $f = fopen(NV_ROOTDIR . '/data/slow_elasticsearch.csv', 'a+'); // w: Viết lại toàn bộ file; a+: Viết tiếp tục xuống cuối file
            fputs($f, (chr(0xEF) . chr(0xBB) . chr(0xBF))); // support unicode
            fputcsv($f, $_arr_log, escape: "\\");
            fclose($f);

            $_arr_log['query'] = $array_query_elastic;
            file_put_contents(NV_ROOTDIR . '/data/slow_elasticsearch.log', json_encode($_arr_log, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n\n", FILE_APPEND);
        }
        return $return;
    }

    /**
     *
     * @param mixed $query_sql
     * @return
     */
    public function query_sql($query_sql)
    {
        $sql = $this->_client->sql();
        $params = array();
        $params['format'] = 'json';
        $params['body']['query'] = $query_sql;
        return $sql->query($params)->asArray();
    }
}
