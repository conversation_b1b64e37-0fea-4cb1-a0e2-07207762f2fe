<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_MOD_UNSUBSCRIBE')) {
    exit('Stop!!!');
}

function nv_unsubscride_main_theme($type, $email, $reasons, $checkss) {
    global $module_info, $module_name, $global_config, $nv_Lang;

    $xtpl = new XTemplate('main.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('CHECKSS', $checkss);
    $xtpl->assign('TYPE', $type);
    $xtpl->assign('EMAIL', $email);

    foreach ($reasons as $r) {
        $xtpl->assign('REASON', $r);
        $xtpl->parse('main.reason');
    }

    $xtpl->parse('main');

    return $xtpl->text('main');
}
