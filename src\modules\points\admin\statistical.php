<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */
if (!defined('NV_IS_FILE_ADMIN'))
    die('Stop!!!');

$curent = nv_date('d/m/Y', NV_CURRENTTIME);
$curent_from = '01/' . nv_date('m/Y', NV_CURRENTTIME);
$array_search = [];
$array_search['time_from'] = $nv_Request->get_title('time_from', 'post,get', $curent_from);
$array_search['time_to'] = $nv_Request->get_title('time_to', 'post,get', $curent);

if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_from'], $m)) {
    $sfrom = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
} else {
    $sfrom = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
}
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_to'], $m)) {
    $sto = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
} else {
    $sto = mktime(23, 59, 59, nv_date('m', NV_CURRENTTIME), nv_date('d', NV_CURRENTTIME), nv_date('Y', NV_CURRENTTIME));
}

// loại trừ các gói của admin
$arr_admin = [];
$result_admin = $db->query('SELECT admin_id FROM nv4_authors');
while ($_admin_info = $result_admin->fetch()) {
    $arr_admin[$_admin_info['admin_id']] = $_admin_info['admin_id'];
}

// các tài khoản test mà k muốn thống kê đưa vào đây, lấy theo nhóm test https://id.dauthau.net/qtdtvnds/index.php?language=vi&nv=users&op=groups&userlist=19
$result = $db->query('SELECT userid FROM ' . NV_GROUPS_GLOBALTABLE . '_users WHERE group_id = 19');
while ($_gr_user_info = $result->fetch()) {
    $arr_admin[$_gr_user_info['userid']] = $_gr_user_info['userid'];
}

// top 10 tài khoản nạp điểm nhiều nhất
$sql = 'SELECT userid, SUM(point_in) as s_point_in FROM ' . $db_config['prefix'] . '_' . $module_data . '_customs_static WHERE date >= ' . $sfrom . ' AND date <=' . $sto . ' AND userid NOT IN (' . implode(',', $arr_admin) . ') GROUP by userid ORDER BY s_point_in DESC LIMIT 10';
$result = $db->query($sql);
$array_point_in = $array_user_id = [];
while ($row = $result->fetch()) {
    $array_point_in[$row['userid']] = $row;
    $array_user_id[$row['userid']] = $row['userid'];
}

// top 10 tài khoản sử dụng điểm nhiều nhất
$sql = 'SELECT userid, SUM(point_out) as s_point_out FROM ' . $db_config['prefix'] . '_' . $module_data . '_customs_static WHERE date >= ' . $sfrom . ' AND date <=' . $sto . ' AND userid NOT IN (' . implode(',', $arr_admin) . ') GROUP by userid ORDER BY s_point_out DESC LIMIT 10';
$result = $db->query($sql);
$array_point_out = [];
while ($row = $result->fetch()) {
    $array_point_out[$row['userid']] = $row;
    $array_user_id[$row['userid']] = $row['userid'];
}

// Xuất danh sách user
if (!empty($array_user_id)) {
    $sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . '  WHERE userid IN (' . implode(',', $array_user_id) . ')';
    $result = $db->query($sql);
    while ($_user_info = $result->fetch()) {
        $_user_info['fullname'] = nv_show_name_user($_user_info['first_name'], $_user_info['last_name'], $_user_info['userid']);
        $array_user_id[$_user_info['userid']] = $_user_info;
    }
}

// liệt kê theo tính năng sử dụng
$sql = 'SELECT type, SUM(count_point) as s_count_point, SUM(total_point) as s_total_point FROM ' . $db_config['prefix'] . '_' . $module_data . '_static WHERE date >= ' . $sfrom . ' AND date <=' . $sto . ' AND type >= 0 GROUP by type ORDER BY type ASC';

$result = $db->query($sql);
$array_type_transaction = [];
while ($row = $result->fetch()) {
    $array_type_transaction[$row['type']] = $row;
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('ARRAY_SEARCH', $array_search);

if (!empty($array_point_in)) {
    $stt = 1;
    foreach ($array_point_in as $point_in) {
        $point_in['stt'] = $stt;
        $point_in['fullname'] = !empty($array_user_id[$point_in['userid']]['fullname']) ? $array_user_id[$point_in['userid']]['fullname'] : $point_in['userid'];
        $point_in['link_point_in'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=transaction&amp;showheader=0&amp;time_from=' . $array_search['time_from'] . '&amp;time_to=' . $array_search['time_to'] . '&amp;status=1&amp;userid=' . $point_in['userid'];
        $point_in['s_point_in'] = number_format($point_in['s_point_in']);
        $xtpl->assign('POINT_IN', $point_in);
        $xtpl->parse('main.point_in');
        $stt++;
    }
}

if (!empty($array_point_out)) {
    $stt = 1;
    foreach ($array_point_out as $point_out) {
        $point_out['stt'] = $stt;
        $point_out['fullname'] = !empty($array_user_id[$point_out['userid']]['fullname']) ? $array_user_id[$point_out['userid']]['fullname'] : $point_out['userid'];
        $point_out['link_point_out'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=transaction&amp;showheader=0&amp;time_from=' . $array_search['time_from'] . '&amp;time_to=' . $array_search['time_to'] . '&amp;status=-1&amp;userid=' . $point_out['userid'];
        $point_out['s_point_out'] = number_format($point_out['s_point_out']);

        $xtpl->assign('POINT_OUT', $point_out);
        $xtpl->parse('main.point_out');
        $stt++;
    }
}

$type_transaction_max_use_sum = [
    'type' => 0,
    'point' => 0
];
$type_transaction_min_use_sum = [
    'type' => 0,
    'point' => PHP_INT_MAX
];
$type_transaction_max_use_count = [
    'type' => 0,
    'point' => 0
];
$type_transaction_min_use_count = [
    'type' => 0,
    'point' => PHP_INT_MAX
];
$sum_reward = 0;
$sum_expired = 0;
if (!empty($array_type_transaction)) {
    $stt = 1;
    foreach ($array_type_transaction as $type_transaction) {
        if ($type_transaction['type'] == 1000) { // điểm hệ thống tặng
            $sum_reward += $type_transaction['s_total_point'];
            continue;
        }
        if ($type_transaction['type'] == 0) { // điểm hệ thống hết hạn
            $sum_expired += $type_transaction['s_total_point'];
            continue;
        }
        if ($type_transaction['s_total_point'] > $type_transaction_max_use_sum['point']) {
            $type_transaction_max_use_sum['type'] = $type_transaction['type'];
            $type_transaction_max_use_sum['point'] = $type_transaction['s_total_point'];
        }
        if ($type_transaction['s_total_point'] < $type_transaction_min_use_sum['point']) {
            $type_transaction_min_use_sum['type'] = $type_transaction['type'];
            $type_transaction_min_use_sum['point'] = $type_transaction['s_total_point'];
        }

        if ($type_transaction['s_total_point'] > $type_transaction_max_use_count['point']) {
            $type_transaction_max_use_count['type'] = $type_transaction['type'];
            $type_transaction_max_use_count['point'] = $type_transaction['s_total_point'];
        }

        if ($type_transaction['s_total_point'] < $type_transaction_min_use_count['point']) {
            $type_transaction_min_use_count['type'] = $type_transaction['type'];
            $type_transaction_min_use_count['point'] = $type_transaction['s_total_point'];
        }

        $type_transaction['stt'] = $stt;
        $type_transaction['title'] = $nv_Lang->getModule('type_transaction' . $type_transaction['type']);
        $type_transaction['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=transaction&amp;showheader=0&amp;time_from=' . $array_search['time_from'] . '&amp;time_to=' . $array_search['time_to'] . '&amp;status=-1&amp;type_transaction=' . $type_transaction['type'] . '&amp;show_last=1';
        $type_transaction['s_total_point'] = number_format($type_transaction['s_total_point']);

        $xtpl->assign('TYPE_TRANSACTION', $type_transaction);
        $xtpl->parse('main.type_transaction');
        $stt++;
    }

    $xtpl->assign('TYPE_TRANSACTION_MAX_USE_SUM', $nv_Lang->getModule('type_transaction' . $type_transaction_max_use_sum['type']));
    $xtpl->assign('TYPE_TRANSACTION_MIN_USE_SUM', $nv_Lang->getModule('type_transaction' . $type_transaction_min_use_sum['type']));
    $xtpl->assign('TYPE_TRANSACTION_MAX_USE_COUNT', $nv_Lang->getModule('type_transaction' . $type_transaction_max_use_count['type']));
    $xtpl->assign('TYPE_TRANSACTION_MIN_USE_COUNT', $nv_Lang->getModule('type_transaction' . $type_transaction_min_use_count['type']));
    $xtpl->assign('SUM_REWARD', number_format($sum_reward));
    $xtpl->assign('SUM_EXPIRED', number_format($sum_expired));
}

$xtpl->parse('main');
$contents = $xtpl->text('main');
$page_title = $nv_Lang->getModule('statistical');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
