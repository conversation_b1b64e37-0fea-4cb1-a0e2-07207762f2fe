<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;
$showheader = $nv_Request->get_int('showheader', 'post,get', 1);
$full_name = $nv_Request->get_title('full_name', 'post,get', '');
$chapter = $nv_Request->get_title('chapter', 'post,get', 0);
$type_view = $nv_Request->get_int('type_view', 'post,get', 1);
$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('chapter', $chapter);
$xtpl->assign('full_name', $full_name);

$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&showheader=' . $showheader;
if (!empty($q)) {
    $base_url .= '&q=' . $q;
}
if (!empty($chapter)) {
    $base_url .= '&chapter=' . $chapter;
}
if (!empty($full_name)) {
    $base_url .= '&full_name=' . $full_name;
}
$base_url .= '&type_view=' . $type_view;
$per_page = 50;
$page = $nv_Request->get_int('page', 'post,get', 1);

// Trang danh sách BNI
$where = [];
$where[] = 'type > 0';
if ($chapter > 0) {
    $where[] = 'chapter =' . $chapter;
} 
if (!empty($full_name)) {
    $where[] = 'full_name LIKE ' . $db->quote('%' . $full_name . '%') . 'or company LIKE ' . $db->quote('%' . $full_name . '%') . 'or phone LIKE ' . $db->quote('%' . $full_name . '%') . 'or website LIKE ' . $db->quote('%' . $full_name . '%');
} 
$db->sqlreset()
        ->select('COUNT(*)')
        ->from('' . NV_PREFIXLANG . '_bidding_bni_members');
if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}
$sth = $db->prepare($db->sql());
$sth->execute();
$num_items = $sth->fetchColumn();

$db->sqlreset()
    ->select('*')
    ->from('' . NV_PREFIXLANG . '_bidding_bni_members')
    ->order('get_time DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}
$sth = $db->prepare($db->sql());
$sth->execute();

$number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;
$_tmp_phone_arr = [];
$array_bni = [];
while ($view = $sth->fetch()) {
    $array_bni[] = $view;
}
$arr_chapter = $db->query('SELECT id, name FROM ' . NV_PREFIXLANG . '_bidding_bni_chapters_url');
while ($row = $arr_chapter->fetch()) {
    $info_chapter[$row['id']] = $row['name'];
}
foreach ($array_bni as $view) {
    $view['get_time'] = nv_date('H:i d/m/Y', $view['get_time']);
    $view['update_time'] = nv_date('H:i d/m/Y', $view['update_time']);
    $view['chapter'] = isset($view['chapter']) && isset($info_chapter[$view['chapter']]) ? $info_chapter[$view['chapter']] : '';
    $view['number'] = $number++;
    $xtpl->assign('VIEW', $view);
    if ($view['update_leads'] > 0) {
        $xtpl->assign('LINK_LEADS', NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=' . 'leads_info&id=' . $view['update_leads'] . '&showheader=' . $showheader);
        $xtpl->parse('main.view.loop.link_leads');
    }
    if (!empty($view['profession'])) {
        $xtpl->parse('main.view.loop.profession');
    }
    if (!empty($view['field'])) {
        $xtpl->parse('main.view.loop.field_pro');
    }
    if (!empty($view['address'])) {
        $xtpl->parse('main.view.loop.address');
    }
    if (!empty($view['phone'])) {
        $xtpl->parse('main.view.loop.phone');
    }
    if (!empty($view['website'])) {
        $xtpl->parse('main.view.loop.website');
    }
    if (!empty($view['link_facebook'])) {
        $xtpl->parse('main.view.loop.facebook');
    }
    if (!empty($view['link_mail'])) {
        $xtpl->parse('main.view.loop.link_mail');
    }
    $xtpl->parse('main.view.loop');
    
}
$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
if (!empty($generate_page)) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.view.generate_page');
}
$xtpl->parse('main.view');
foreach ($info_chapter as $key => $value) {
    $xtpl->assign('chapter', array(
        'id' => $key,
        'title' => $value,
        'selected' => $key == $chapter ? 'selected="selected"' : ''
    ));
    $xtpl->parse('main.search.chapter');
}
if ($showheader) {
    $xtpl->parse('main.search');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');
$page_title = $nv_Lang->getModule('bni');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';

