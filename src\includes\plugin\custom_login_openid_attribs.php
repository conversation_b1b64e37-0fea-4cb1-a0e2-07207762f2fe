<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

$nv_hook_module = 'users'; // Module xảy ra event chứa data
$nv_receive_module = 'users'; // Module nhận và xử lý data

$callback = function($vars, $from_data, $receive_data) {
    $module_name = $receive_data['module_name'];
    $module_info = $receive_data['module_info'];
    $module_data = $module_info['module_data'];

    $server = $vars[0];
    $attribs = $vars[1];
    if ($server != 'google-identity') {
        return null;
    }

    global $nv_Request, $db;

    $id = $nv_Request->get_title('id', 'get', '');
    $hash = $nv_Request->get_title('hash', 'get', '');
    if (empty($id) or empty($hash)) {
        return null;
    }

    // Xóa các log quá 3 phút
    $db->query("DELETE FROM " . NV_USERS_GLOBALTABLE . "_gsi WHERE addtime<" . (NV_CURRENTTIME - 180));

    // Lấy ra log
    $sql = "SELECT attribs FROM " . NV_USERS_GLOBALTABLE . "_gsi WHERE uniqid=" . $db->quote($id);
    $db_attribs = $db->query($sql)->fetchColumn();
    if (empty($db_attribs)) {
        return null;
    }

    $db_attribs = json_decode($db_attribs, true);
    if (empty($db_attribs)) {
        return null;
    }

    $db->query("DELETE FROM " . NV_USERS_GLOBALTABLE . "_gsi WHERE uniqid=" . $db->quote($id));

    // Tính và kiểm tra hash
    // Tạo mã hash
    ksort($db_attribs);
    $hashdata = '';
    $i = 0;
    foreach ($db_attribs as $key => $value) {
        if ($i == 1) {
            $hashdata .= '&' . $key . '=' . $value;
        } else {
            $hashdata .= $key . '=' . $value;
            $i = 1;
        }
    }
    $secureHash = hash_hmac('sha512', $hashdata, SSO_REGISTER_SECRET);
    if (!hash_equals($secureHash, $hash)) {
        return null;
    }

    $nv_Request->set_Session('openid_attribs', json_encode($db_attribs));

    return $db_attribs;
};
nv_add_hook($module_name, 'custom_login_openid_attribs', $priority, $callback, $hook_module, $pid);
