<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<link type="text/css" href="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/jquery-ui/jquery-ui.min.js"></script>
<!-- BEGIN: error -->
<div class="alert alert-warning">{ERROR}</div>
<!-- END: error -->
<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <input type="hidden" name="userid" value="{USER_ID}" />
    <!-- BEGIN: config_for_user -->
    <div class="well">
        <div class="row">
            <div class="col-md-8">
                <input type="checkbox" name="config_for" {CHECKED_CONFIG} value="1" id="configForCheckbox"/>
                <label for="configForCheckbox">{LANG.config_for_user}</label>
                <div class="form-group hidden margintop10" id="show_user">
                    <div class="col-md-18">
                        <select class="form-control" name="user_id" style="width: 100%;">
                            <option value="">---</option>
                            <!-- BEGIN: select_user -->
                            <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                            <!-- END: select_user -->
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: config_for_user -->
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th></th>
                    <th></th>
                    <th>{LANG.vietnamese}</th>
                    <th>{LANG.english}</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <th>{LANG.prefix}</th>
                    <td><label><input type="checkbox" name="prefix_state" value="on" {DATA.prefix_state}> {LANG.prefix_state}</label></td>
                    <td><input type="text" value="{DATA.prefix_vi}" name="prefix_vi" class="form-control" /></td>
                    <td><input type="text" value="{DATA.prefix_en}" name="prefix_en" class="form-control" /></td>
                </tr>
                <tr>
                    <th>{LANG.position}</th>
                    <td><label><input type="checkbox" name="position_state" value="on" {DATA.position_state}> <small>{LANG.position_state}</small></label></td>
                    <td><input type="text" value="{DATA.position_vi}" name="position_vi" class="form-control" /></td>
                    <td><input type="text" value="{DATA.position_en}" name="position_en" class="form-control" /></td>
                </tr>
                <tr>
                    <th>{LANG.signature}</th>
                    <td><label><input type="checkbox" name="signature_state" value="on" {DATA.signature_state}> <small>{LANG.signature_state}</small></label></td>
                    <td>{SIGNATURE_VI}</td>
                    <td>{SIGNATURE_EN}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="text-center">
        <input class="btn btn-primary" name="submit" type="submit" value="{LANG.save}" />
    </div>
</form>
<script type="text/javascript">
    var configForCheckbox = document.getElementById('configForCheckbox');
    var showUser = document.getElementById('show_user');

    if (configForCheckbox && showUser) {
        var baseUrl = "{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&{NV_NAME_VARIABLE}={MODULE_NAME}&{NV_OP_VARIABLE}={OP}";
        configForCheckbox.checked ? showUser.classList.remove('hidden') : showUser.classList.add('hidden');
        configForCheckbox.addEventListener('change', function() {
            this.checked ? showUser.classList.remove('hidden') : showUser.classList.add('hidden');
            if (!this.checked) {
                window.location.href = baseUrl ;
            }
        });
        $("select[name='user_id']").select2();
        $("select[name='user_id']").on('select2:select', function (e) {
            var selectedOptionValue = e.params.data.id;
            var config_for = configForCheckbox.checked ? 1 : 0;
            var newUrl = baseUrl + "&userid=" + selectedOptionValue + "&config_for=" + config_for;
            window.location.href = newUrl;
        });
    }
</script>
<!-- END: main -->
