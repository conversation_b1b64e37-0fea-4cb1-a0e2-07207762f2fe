<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    exit('Stop!!!');
}

$page_title = $table_caption = $nv_Lang->getModule('stat_trivial');

$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op;
$method = $nv_Request->isset_request('method', 'post') ? $nv_Request->get_string('method', 'post', '') : ($nv_Request->isset_request('method', 'get') ? urldecode($nv_Request->get_string('method', 'get', '')) : '');
$not_activated = $nv_Request->get_int('not_activated', 'post,get', 0);

$methods = [
    'taxcode' => [
        'key' => 'taxcode',
        'sql' => ['tb3.mst'],
        'value' => $nv_Lang->getModule('stat_search_taxcode'),
        'selected' => ''
    ],
    'userid' => [
        'key' => 'userid',
        'sql' => ['tb1.userid'],
        'value' => $nv_Lang->getModule('stat_search_id'),
        'selected' => ''
    ],
    'username' => [
        'key' => 'username',
        'sql' => ['tb1.username'],
        'value' => $nv_Lang->getModule('stat_search_account'),
        'selected' => ''
    ],
    'fullname' => [
        'key' => 'fullname',
        'sql' => [$global_config['name_show'] == 0 ? "concat(tb1.last_name,' ',tb1.first_name)" : "concat(tb1.first_name,' ',tb1.last_name)"],
        'value' => $nv_Lang->getModule('stat_search_name'),
        'selected' => ''
    ],
    'email' => [
        'key' => 'email',
        'sql' => ['tb1.email'],
        'value' => $nv_Lang->getModule('stat_search_mail'),
        'selected' => ''
    ],
    'oauth' => [
        'key' => 'oauth',
        'sql' => ['tb2.id', 'tb2.email'],
        'value' => $nv_Lang->getModule('stat_search_oauth'),
        'selected' => ''
    ]
];
$methodvalue = $nv_Request->isset_request('value', 'post') ? $nv_Request->get_string('value', 'post') : ($nv_Request->isset_request('value', 'get') ? urldecode($nv_Request->get_string('value', 'get', '')) : '');

$ts_to = $nv_Request->get_title('to', 'post,get', '');
unset($m);
$time_to = (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $ts_to, $m)) ? mktime(23, 59, 59, $m[2], $m[1], $m[3]) : time();
$ts_from = $nv_Request->get_title('from', 'post,get', '');
unset($m);
$time_from = (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $ts_from, $m)) ? mktime(0, 0, 0, $m[2], $m[1], $m[3]) : $time_to - (30 * 86400);
$base_url .= '&amp;from=' . $ts_from . '&amp;to=' . $ts_to;

$orders = ['taxcode', 'userid', 'username', 'full_name', 'email', 'regdate'];
$orderby = $nv_Request->get_string('sortby', 'get', 'userid');
$ordertype = $nv_Request->get_string('sorttype', 'get', 'DESC');
if ($ordertype != 'ASC') {
    $ordertype = 'DESC';
}
$method = (!empty($method) and isset($methods[$method])) ? $method : '';
$join = '';

if (!empty($methodvalue)) {
    if (empty($method)) {
        $join = 'LEFT JOIN ' . NV_USERS_GLOBALTABLE . '_openid tb2 ON tb1.userid=tb2.userid';
    } elseif ($method == 'oauth') {
        $join = 'INNER JOIN ' . NV_USERS_GLOBALTABLE . '_openid tb2 ON tb1.userid=tb2.userid';
    }

    if (empty($method)) {
        $array_like = [];
        foreach ($methods as $method_i) {
            foreach ($method_i['sql'] as $method_sql) {
                $array_like[] = $method_sql . " LIKE '%" . $db->dblikeescape($methodvalue) . "%'";
            }
        }
        $_arr_where[] = '(' . implode(' OR ', $array_like) . ')';
    } else {
        $array_like = [];
        foreach ($methods[$method]['sql'] as $method_sql) {
            $array_like[] = $method_sql . " LIKE '%" . $db->dblikeescape($methodvalue) . "%'";
        }
        $_arr_where[] = '(' . implode(' OR ', $array_like) . ')';
        $methods[$method]['selected'] = ' selected="selected"';
    }
    $base_url .= '&amp;method=' . urlencode($method) . '&amp;value=' . urlencode($methodvalue);
    $table_caption = $nv_Lang->getModule('stat_search_page_title');
}

$join .= ' INNER JOIN ' . NV_USERS_GLOBALTABLE . '_info tb3 ON tb1.userid=tb3.userid';

// Default group is all
// $selgroup = $nv_Request->get_int('group', 'post,get', 6);
// if (!empty($selgroup) and $selgroup != 6) {
//     $_arr_where[] = '(FIND_IN_SET(' . $selgroup . ', tb1.in_groups) OR tb1.group_id = ' . $selgroup . ')';
//     $base_url .= '&amp;group=' . $selgroup;
// }

$page = $nv_Request->get_int('page', 'get', 1);
$per_page = 30;

$db->sqlreset()
    ->select('COUNT(*)')
    ->from(NV_USERS_GLOBALTABLE . ' tb1');

if (!empty($join)) {
    $db->join($join);
}

// Chỉ tìm những user có mã số thuế
// tạm thời tìm cả những user ko có mst theo yêu cầu của @danle
// $_arr_where[] = 'tb3.mst <> ""';
// tạm thời tìm cả những tk chưa active
if (empty($not_activated)) {
    $_arr_where[] = 'tb1.active = 1';
}

$_arr_where[] = 'tb1.regdate BETWEEN ' . $time_from . ' AND ' . $time_to;

if (!empty($_arr_where)) {
    $db->where(implode(' AND ', $_arr_where));
}

$num_items = $db->query($db->sql())->fetchColumn();

$db->select('tb1.userid, tb1.username, tb1.md5username, tb1.email, tb1.first_name, tb1.last_name, tb1.regdate, tb3.mst')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);

if (!empty($orderby) and in_array($orderby, $orders, true)) {
    $orderby_sql = $orderby != 'taxcode' ? ($orderby != 'full_name' ? 'tb1.' . $orderby : ($global_config['name_show'] == 0 ? "concat(tb1.first_name,' ',tb1.last_name)" : "concat(tb1.last_name,' ',tb1.first_name)")) : 'tb3.mst';
    $db->order($orderby_sql . ' ' . $ordertype);
    $base_url .= '&amp;sortby=' . $orderby . '&amp;sorttype=' . $ordertype;
}

$page_url = $base_url;
$result = $db->query($db->sql());

$users_list = [];

while ($row = $result->fetch()) {
    // $row['in_groups'] = array_map('intval', explode(',', $row['in_groups']));

    $users_list[$row['userid']] = [
        'taxcode' => $row['mst'],
        'userid' => $row['userid'],
        'username' => $row['username'],
        'full_name' => nv_show_name_user($row['first_name'], $row['last_name'], $row['username']),
        'email' => $row['email'],
        'regdate' => date('d/m/Y H:i', $row['regdate']),
        'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=memberlist/' . change_alias($row['username']) . '-' . $row['md5username'], true)
    ];

    if ($global_config['idsite'] > 0 and $row['idsite'] != $global_config['idsite']) {
        $users_list[$row['userid']]['is_edit'] = false;
        $users_list[$row['userid']]['is_delete'] = false;
    }
}

// Số lượng doanh nghiệp : các user có mã số thuế không trùng nhau
$db->select('COUNT(DISTINCT tb3.mst) as num_of_business')
    ->limit('')
    ->offset('')
    ->where(implode(' AND ', $_arr_where) . ' AND tb3.mst != ""');

$result = $db->query($db->sql());
$business_number = $result->fetch()['num_of_business'];

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);

$head_tds = [];
$head_tds['taxcode']['title'] = $nv_Lang->getModule('stat_taxcode');
$head_tds['taxcode']['href'] = $page_url . '&amp;sortby=taxcode&amp;sorttype=ASC';
$head_tds['userid']['title'] = $nv_Lang->getModule('stat_userid');
$head_tds['userid']['href'] = $page_url . '&amp;sortby=userid&amp;sorttype=ASC';
$head_tds['userid']['title'] = $nv_Lang->getModule('stat_userid');
$head_tds['userid']['href'] = $page_url . '&amp;sortby=userid&amp;sorttype=ASC';
$head_tds['username']['title'] = $nv_Lang->getModule('stat_account');
$head_tds['username']['href'] = $page_url . '&amp;sortby=username&amp;sorttype=ASC';
$head_tds['full_name']['title'] = $global_config['name_show'] == 0 ? $nv_Lang->getModule('stat_lastname_firstname') : $nv_Lang->getModule('stat_firstname_lastname');
$head_tds['full_name']['href'] = $page_url . '&amp;sortby=full_name&amp;sorttype=ASC';
$head_tds['email']['title'] = $nv_Lang->getModule('stat_email');
$head_tds['email']['href'] = $page_url . '&amp;sortby=email&amp;sorttype=ASC';
$head_tds['regdate']['title'] = $nv_Lang->getModule('stat_register_date');
$head_tds['regdate']['href'] = $page_url . '&amp;sortby=regdate&amp;sorttype=ASC';

foreach ($orders as $order) {
    if ($orderby == $order and $ordertype == 'ASC') {
        $head_tds[$order]['href'] = $page_url . '&amp;sortby=' . $order . '&amp;sorttype=DESC';
        $head_tds[$order]['title'] .= ' &darr;';
    } elseif ($orderby == $order and $ordertype == 'DESC') {
        $head_tds[$order]['href'] = $page_url . '&amp;sortby=' . $order . '&amp;sorttype=ASC';
        $head_tds[$order]['title'] .= ' &uarr;';
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('NV_LANG_INTERFACE', NV_LANG_INTERFACE);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('OP', $op);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('FORM_ACTION', NV_BASE_ADMINURL . 'index.php');
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('SEARCH_VALUE', nv_htmlspecialchars($methodvalue));
$xtpl->assign('TABLE_CAPTION', $table_caption);
$xtpl->assign('HEAD', $head_tds);
$xtpl->assign('CHECKSESS', md5(NV_CHECK_SESSION . '_' . $module_name . '_' . $op));
$xtpl->assign('time_from', date('d/m/Y', $time_from));
$xtpl->assign('time_to', date('d/m/Y', $time_to));

if (!empty($not_activated)) {
    $xtpl->assign('NOT_ACTIVATED', 'checked');
}

if (defined('NV_IS_USER_FORUM')) {
    $xtpl->parse('main.is_forum');
}

foreach ($methods as $m) {
    $xtpl->assign('METHODS', $m);
    $xtpl->parse('main.method');
}
$_bg = (defined('NV_CONFIG_DIR') and $global_config['idsite'] == 0) ? 3 : 1;

$view_user_allowed = nv_user_in_groups($global_config['whoviewuser']);
foreach ($users_list as $u) {
    $xtpl->assign('CONTENT_TD', $u);
    $xtpl->assign('NV_ADMIN_THEME', $global_config['admin_theme']);

    if ($view_user_allowed) {
        $xtpl->parse('main.xusers.view');
    } else {
        $xtpl->parse('main.xusers.show');
    }

    $xtpl->parse('main.xusers');
}

$has_footer = false;

if (!empty($generate_page)) {
    $xtpl->assign('GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.footer.generate_page');
    $has_footer = true;
}

$xtpl->assign('users_sum', $num_items);
$xtpl->assign('business_sum', $business_number);

if ($has_footer) {
    $xtpl->parse('main.footer');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
