<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use NukeViet\Dauthau\Condition;
use NukeViet\Dauthau\Order;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class ListAllMobifone implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'mobifone';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];

        $page = $nv_Request->get_int('page', 'post', '1');
        $perpage = $nv_Request->get_int('perpage', 'post', '50');
        if ($page <= 0 and $page > 9999) {
            return $this->result->setError()
                ->setCode('2005')
                ->setMessage('Page is integer, more than 0')
                ->getResult();
        }
        if ($perpage > 50 or $perpage <= 0) {
            return $this->result->setError()
                ->setCode('2002')
                ->setMessage('Perpage to larger')
                ->getResult();
        }

        $array_where = $nv_Request->get_array('where', 'post');
        if (!is_array($array_where)) {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Param where invalid to array')
                ->getResult();
        }

        $array_order = $nv_Request->get_array('order', 'post');
        if (!is_array($array_order)) {
            return $this->result->setError()
                ->setCode('2003')
                ->setMessage('Param order invalid to array')
                ->getResult();
        }

        // key cho phép trong where
        $keys_check = [
            'sdt',
            'phieu_ghi',
            'loai_cuoc_goi'
        ];

        // key cho phép hiển thị, cũng là key order
        $keys_view = [
            'call_id',
            'phieu_ghi',
            'sdt',
            'chuyen_vien',
            'loai_cuoc_goi',
            'nhanh',
            'thoi_gian_bat_dau',
            'thoi_gian_ket_thuc',
            'thoi_gian_cho',
            'thoi_gian_hold',
            'thoi_gian_dam_thoai',
            'trang_thai_cuoc_goi',
            'trang_thai_ket_thuc',
            'khao_sat',
            'kq_khao_sat',
            'link'
        ];

        $where = array();
        if (!empty($array_where)) {
            // check $field
            foreach ($array_where as $keys) {
                foreach ($keys as $key) {
                    $operator = array_key_first($key);
                    $field = array_key_first($key[$operator]);
                    if (!in_array($field, $keys_check)) {
                        return $this->result->setError()
                            ->setCode('2001')
                            ->setMessage('Missing field ' . $field . ' in data')
                            ->getResult();
                    }
                }
            }

            $condition = new Condition();
            $where[] = $condition->toSqlString($array_where);
        }

        $order_string = 'call_id DESC';
        if (!empty($array_order)) {
            // check $field
            foreach ($array_order as $field => $type) {
                if (!in_array($field, $keys_view)) {
                    return $this->result->setError()
                        ->setCode('2004')
                        ->setMessage('Missing field ' . $field . ' in data order')
                        ->getResult();
                }
            }

            $order = new Order();
            $order_string = $order->toSqlString($array_order);
        }
        try {
            $db->sqlreset()
                ->select('COUNT(*) as num')
                ->from('' . NV_PREFIXLANG . '_crmbidding_mobiphone');

            if (!empty($where)) {
                $db->where(implode(' AND ', $where));
            }

            $sth = $db->prepare($db->sql());
            $sth->execute();
            $total = $sth->fetchColumn();
            $db->select('*');
            $db->order($order_string);

            $db->limit($perpage)->offset(($page - 1) * $perpage);
            $sth = $db->prepare($db->sql());
            $sth->execute();
            $arr_data = [];
            while ($view = $sth->fetch()) {
                $arr_data[$view['call_id']] = $view;
            }
            if (!empty($arr_data)) {
                $this->result->setSuccess();
                $this->result->set('total', $total);
                $this->result->set('perpage', $perpage);
                $this->result->set('page', $page);
                $this->result->set('data', $arr_data);
            } else {
                $this->result->setSuccess()
                    ->setCode('4000')
                    ->setMessage($nv_Lang->getModule('api_error_400'));
            }
        } catch (PDOException $e) {
            $this->result->setError()
                ->setCode('3000')
                ->setMessage(print_r($e, true));
        }

        return $this->result->getResult();
    }
}
