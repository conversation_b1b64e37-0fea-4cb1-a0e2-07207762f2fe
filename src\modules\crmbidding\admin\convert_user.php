<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:39:33 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$row = [];
$error = [];
$id = $nv_Request->get_int('id', 'post,get', 0);
$params_op = [
    'page' => 1,
    'perpage' => 1
];
$where['AND'][] = [
    '=' => [
        'id' => $id
    ]
];
$params_op['where'] = $where;
$array_oppotunities = nv_local_api('ListAllOpportunities', $params_op, $admin_info['username'], 'crmbidding');
$array_oppotunities = json_decode($array_oppotunities, true);
$params_op = $where = [];
if (isset($array_oppotunities['code']) and $array_oppotunities['code'] == '0000') {
    $row = $array_oppotunities['data'][$id];
}
if (empty($row)) {
    die('Opportunities empty');
}

if ($nv_Request->isset_request('chosse_user', 'post,get')) {
    $userid = $nv_Request->get_int('userid', 'post,get', 0);
    $chosse = $nv_Request->get_int('chosse', 'post, get', 0);
    $data = [
        'user_id' => $userid
    ];
    $_params_update = [
        'opportunitiesid' => $id,
        'admin_id' => $admin_info['userid'],
        'data' => $data
    ];
    $status_update = nv_local_api('UpdateOpportunities', $_params_update, $admin_info['username'], 'crmbidding');
    $status_update = json_decode($status_update, true);
    $_params_update = $data = [];
    $err = 'ERR';
    if ($status_update) {
        $data_lead = [
            'user_id' => $userid
        ];
        $params = [
            'leadsid' => $row['leadsid'],
            'admin_id' => $admin_info['userid'],
            'data' => $data_lead
        ];

        $result_update = nv_local_api('UpdateLeads', $params, $admin_info['username'], $module_name);
        $result_update = json_decode($result_update, true);
        $params = $data_lead = [];
        if ($chosse == 1) {
            $log_data = [
                $nv_Lang->getModule('opportunities_info_user_set')
            ];
        } else {
            $log_data = [
                $nv_Lang->getModule('opportunities_info_user_add')
            ];
        }

        $params = [
            'userid' => $admin_info['admin_id'],
            'log_area' => 1,
            'log_key' => 'LOG_ADMIN_UPDATE_OPPORTUNITIES_INFO',
            'log_time' => NV_CURRENTTIME,
            'log_data' => $log_data,
            'leads_id' => $id
        ];

        // Ghi log bằng API
        $result = nv_local_api('CreateAllLogs', $params, $admin_info['username'], $module_name);

        /*
         * $sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (userid, log_area, log_key, log_time, log_data, oppotunities_id) VALUES (" . $admin_info['admin_id'] . ", 1, 'LOG_ADMIN_UPDATE_LEADS_INFO', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $id . ")";
         * $db->query($sql);
         */

        $nv_Cache->delMod($module_name);
        nv_insert_logs(NV_LANG_DATA, $module_name, 'Set user opportunities', ' ', $admin_info['userid']);
        $err = "OK";
    }
    if ($nv_Request->isset_request('requesturl', 'post,get')) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=opportunities_info&id=' . $id);
    } else {
        die($err);
    }
}

$row['sub_email'] = nv_htmlspecialchars(nv_br2nl($row['sub_email']));
if ($row['user_id'] == 0) {
    if (!empty($row['phone'])) {
        $array_user_phone = [];
        $sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . " as a INNER JOIN " . NV_USERS_GLOBALTABLE . "_info as b ON a.userid= b.userid WHERE b.phone= " . $db->quote($row['phone']);
        if (!empty($row['sub_phone'])) {
            $sql .= " OR phone IN (" . $db->quote($row['sub_phone']) . ") ";
        }
        $sql .= " ORDER BY b.userid DESC";
        $result = $db->query($sql);
        while ($_row = $result->fetch()) {
            $array_user_phone[$_row['userid']] = $_row;
        }
    }
    if (!empty($row['email'])) {
        $array_user_email = [];
        $sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . " as a INNER JOIN " . NV_USERS_GLOBALTABLE . "_info as b ON a.userid= b.userid WHERE a.email = " . $db->quote($row['email']);
        if (!empty($row['sub_email'])) {
            $sql .= " OR a.email IN (" . $db->quote($row['sub_email']) . ") ";
        }
        $sql .= " ORDER BY a.userid DESC";
        $result = $db->query($sql);
        while ($_row = $result->fetch()) {
            $array_user_email[$_row['userid']] = $_row;
        }
    }
} else {
    $array_user = [];
    $sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . " as a INNER JOIN " . NV_USERS_GLOBALTABLE . "_info as b ON a.userid= b.userid WHERE a.userid = " . $db->quote($row['user_id']);
    $result = $db->query($sql);
    while ($_row = $result->fetch()) {
        $array_user = $_row;
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('ID', $id);
$xtpl->assign('ROW', $row);
if ($row['user_id'] == 0) {
    if (!empty($row['phone'])) {
        if (sizeof($array_user_phone) > 0) {
            foreach ($array_user_phone as $user_email) {
                $user_email['title'] = nv_show_name_user($user_email['first_name'], $user_email['last_name'], $user_email['username']);
                $user_email['link_set'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $id . '&amp;userid=' . $user_email['userid'];
                $user_email['type'] = $nv_Lang->getModule('phone');
                $xtpl->assign('USERS', $user_email);
                $xtpl->parse('main.set.loop');
            }
        }
    }
    if (!empty($row['email'])) {
        if (sizeof($array_user_email) > 0) {
            foreach ($array_user_email as $user_email) {
                $user_email['title'] = nv_show_name_user($user_email['first_name'], $user_email['last_name'], $user_email['username']);
                $user_email['link_set'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $id . '&amp;userid=' . $user_email['userid'];
                $user_email['type'] = $nv_Lang->getModule('email');
                $xtpl->assign('USERS_EMAIL', $user_email);
                $xtpl->parse('main.set.loop_email');
            }
        }
    }
    $row['first_name'] = $row['name'];
    if ($row['prefix_lang'] == 1) {
        $row['nation'] = 8; // UK
    } else {
        $row['nation'] = 243; // việt nam 244 do key bên useradd là 243
    }
    $initdata = $row;
    $initdata = json_encode($initdata, true);
    $initdata = $crypt->encrypt($initdata, NV_CHECK_SESSION);
    $xtpl->assign('INITDATA', $initdata);
    $xtpl->assign('URL_BACK', nv_redirect_encrypt(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&id=' . $id . '&chosse_user=1&requesturl=1'));
    $xtpl->parse('main.set');
} else {
    $array_user['title'] = nv_show_name_user($array_user['first_name'], $array_user['last_name'], $array_user['username']);
    $array_user['link_view'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=users&amp;' . NV_OP_VARIABLE . '=edit&amp;userid=' . $array_user['userid'] . '&check_admin=' . md5(NV_CHECK_SESSION . $array_user['userid']);
    $array_user['regdate'] = nv_date('H:i d/m/Y', $array_user['regdate']);
    $array_user['gender'] = $global_array_genders[$array_user['gender']]['title'];
    $xtpl->assign('USERS', $array_user);
    $xtpl->parse('main.user');
}
if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo $contents;
include NV_ROOTDIR . '/includes/footer.php';
//die(nv_admin_theme($contents, false));
