<?php

/**
 * @Project POINTS 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */
use NukeViet\Core\Language;

if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

global $db_config;

// POINTS name
define('NV_POINTS_MODULE', "points");

// POINTS table
define('NV_POINTS_TABLE', $db_config['prefix'] . "_" . NV_POINTS_MODULE);

class nukeviet_points
{

    private $language = [];

    private $glanguage = [];

    private $db = null;

    private $nv_Cache = null;

    private $is_error = false;

    /**
     *
     * @var boolean true thì kiểm tra số dư không đủ trừ, false thì bỏ qua kiểm tra số dư.
     */
    private $check_balance = true;

    /**
     * nukeviet_points::__construct()
     *
     * @return
     */
    public function __construct()
    {
        global $db, $nv_Cache;

        $lang_module = [];
        $lang_file = NV_ROOTDIR . "/modules/points/language/" . NV_LANG_DATA . ".php";
        if (file_exists($lang_file)) {
            require $lang_file;
        } else {
            require NV_ROOTDIR . "/modules/points/language/vi.php";
        }

        $this->language = $lang_module;
        $this->glanguage = Language::$lang_global;
        $this->db = $db;
        $this->nv_Cache = $nv_Cache;
    }

    /**
     * nukeviet_points::lang()
     *
     * @param string $key
     * @return
     */
    public function lang($key = '')
    {
        $numargs = func_num_args();
        if ($numargs < 1) {
            return '';
        } elseif ($numargs == 1) {
            return isset($this->language[$key]) ? $this->language[$key] : $key;
        }
        if (!isset($this->language[$key])) {
            return $key;
        }
        $arg_list = func_get_args();
        unset($arg_list[0]);
        return vsprintf($this->language[$key], $arg_list);
    }

    /**
     * nukeviet_points::glang()
     *
     * @param mixed $key
     * @return
     */
    public function glang($key)
    {
        return isset($this->glanguage[$key]) ? $this->glanguage[$key] : $key;
    }

    /**
     * nukeviet_points::all_exchange()
     *
     * @return
     */
    public function all_exchange()
    {
        $sql = "SELECT * FROM " . NV_POINTS_TABLE . "_config";
        $result = $this->nv_Cache->db($sql, 'id', NV_POINTS_MODULE);

        $array = [];
        if (!empty($result)) {
            foreach ($result as $row) {
                $array[$row['exchange_point']] = $row['exchange_money'];
            }
        }
        return $array;
    }

    /**
     * nukeviet_points::init()
     *
     * @param mixed $userid
     * @return
     */
    private function init($userid)
    {
        if (empty($userid)) {
            return [];
        }

        $array_insert = [
            'userid' => $userid,
            'point_in' => 0,
            'point_out' => 0,
            'point_total' => 0,
            'status' => 1,
            'updatetime' => NV_CURRENTTIME
        ];

        $_sql = 'SELECT * FROM ' . NV_POINTS_TABLE . '_customs WHERE userid=' . $userid;
        $_query = $this->db->query($_sql);
        $row = $_query->fetch();
        if (empty($row)) {
            $sql = "INSERT INTO " . NV_POINTS_TABLE . "_customs(
                userid, point_in, point_out, point_total, status, updatetime
            ) VALUES (
                " . $array_insert['userid'] . "," . $array_insert['point_in'] . ",
                " . $array_insert['point_out'] . "," . $array_insert['point_total'] . ",
                " . $array_insert['status'] . "," . $array_insert['updatetime'] . "
            )";
            $this->db->query($sql);
        }

        $this->nv_Cache->delMod(NV_POINTS_MODULE);

        $array_insert['next_expired'] = 0;
        $array_insert['have_msg'] = 0;

        return $array_insert;
    }

    /**
     *
     * @param mixed $userid
     * @return
     */
    public function my_point($userid)
    {
        if (empty($userid)) {
            return [];
        }

        $sql = 'SELECT * FROM ' . NV_POINTS_TABLE . '_customs WHERE userid=' . $userid;
        $list = $this->nv_Cache->db($sql, 'userid', NV_POINTS_MODULE);

        if (!empty($list)) {
            $row = $list[$userid];
            $array = [
                'userid' => $userid,
                'point_in' => $row['point_in'],
                'point_out' => $row['point_out'],
                'point_total' => $row['point_total'],
                'status' => $row['status'],
                'updatetime' => $row['updatetime'],
                'next_expired' => $row['next_expired'],
                'have_msg' => $row['have_msg']
            ];
        } else {
            $array = $this->init($userid);
        }

        return $array;
    }

    /**
     * Trừ các điểm hết hạn k sử dụng
     *
     * @param mixed $userid
     * @return
     */
    public function expired($userid)
    {
        if (empty($userid)) {
            return false;
        }

        // các điểm đến hạn cần xử lý
        $_sql = 'SELECT * FROM ' . NV_POINTS_TABLE . '_log WHERE userid =' . $userid . ' AND expired > 0 AND expired < ' . NV_CURRENTTIME . ' AND check_expired = 0 ORDER BY expired ASC LIMIT 1';

        $totallogs = 0;

        $_query = $this->db->query($_sql);
        while ($_row = $_query->fetch()) {
            // Số điểm đã sử dụng ít hơn số điểm tặng
            if ($_row['point_use'] < $_row['point_total'] or $_row['point_total'] <= 0) {
                $point_tmp = $_row['point_total'] - $_row['point_use'];
                $message = sprintf($this->lang('expired_point'), $point_tmp, nv_date('d/m/Y', $_row['expired']));
                $this->update(0, $point_tmp, $userid, $message, false, 0, 0, 0, 0, 0);

                if (!$this->isError()) {
                    $this->db->exec("UPDATE " . NV_POINTS_TABLE . "_log SET check_expired = 1 WHERE id=" . $_row['id']);

                    // Tính toán thời điểm hết hạn tiếp theo
                    $this->nextExpired($userid);
                    $totallogs++;
                }
            }
        }

        // Không có log nào cũng tính toán lại thời điểm hết hạn nếu không có trường hợp cứ treo mãi
        if (empty($totallogs)) {
            $this->nextExpired($userid);
        }
    }

    /**
     *
     * @param int $userid
     */
    public function nextExpired($userid)
    {
        $currentPoints = $this->my_point($userid);

        // Xác định thời điểm hết hạn tiếp theo
        $sql = "SELECT expired FROM " . NV_POINTS_TABLE . "_log
        WHERE userid=" . $userid . " AND expired>0 AND check_expired=0 ORDER BY expired ASC LIMIT 1";
        $nextExpired = $this->db->query($sql)->fetchColumn() ?: 0;
        if ($currentPoints['next_expired'] != $nextExpired) {
            $sql = "UPDATE " . NV_POINTS_TABLE . "_customs
            SET next_expired=" . $nextExpired . ", updatetime=" . NV_CURRENTTIME . "
            WHERE userid=" . $userid;
            $this->db->query($sql);
            $this->nv_Cache->delMod(NV_POINTS_MODULE);
        }
    }

    /**
     *
     * @param int $site_id:
     *            ID site, 0 là site chính
     * @param mixed $point:
     *            Số tiền cộng hoặc trừ
     * @param mixed $userid:
     *            ID tài khoản bị tác động
     * @param string $message:
     *            Thông tin giao dịch
     * @param bool $status:
     *            Công hay trừ, true là cộng còn lại là trừ
     * @param bool $admin_id:
     *            quản trị tạo đơn
     * @param int $sub_status:
     *            1 là admin điều chỉnh số liệu lệch, 0 là admin giao dịch hộ khách hàng, 2 là tặng điểm cho nhau
     * @param int $type_transaction: các giao dịch trừ điểm
     *            =0 là giao dịch trừ điểm: hết hạn điểm do hệ thống
     *            =1 là giao dịch trừ điểm: tặng điểm cho người khác của khách hàng
     *            > 1 là các giao dịch trừ điểm của khách theo bảng giá tại https://id.dauthau.net/vi/points/#banggia1, khi bổ sung cần note lại ở đây
     *            2 - ✅ Số điểm trừ khi thêm 1 bộ lọc 50 điểm
     *            3 - ✅ Số điểm trừ cho tính năng gửi mail/ tháng/ bộ lọc 200 điểm
     *            4 - ✅ Số điểm trừ khi thêm 1 tin theo dõi 20 điểm
     *            5 - ✅ Số điểm trừ khi xuất dữ liệu cho mỗi block hàng hóa khi đã đăng ký 1 trong các gói VIP sau: VIP 1, VIP 2, VIP 3, VIP 4, VIP 5, VIP 7 1 điểm
     *            6 - ✅ Số điểm trừ khi xuất dữ liệu cho mỗi block hàng hóa khi chưa đăng ký gói VIP 1, VIP 2, VIP 3, VIP 4, VIP 5, VIP 7 10 điểm
     *            7 - ✅ Số điểm trừ khi xem giá một mặt hàng hoá 1 điểm
     *            8 - ✅ Xem chi tiết thông tin thầu (thông tin bất kỳ: TBMT, KHLCNT, KQMT, KQLCNT, TBMĐT.... gọi là 1 thông tin thầu) 1 điểm
     *            9 - ✅ Xem thông tin thầu nâng cao (các thông tin bị ẩn trong chi tiết thông tin thầu, bắt phải đăng ký VIEWEB/VIP) 1 điểm
     *            10 - ✅ Số điểm trừ khi tải file 10 + (5 * số MB / 10) điểm
     *            11 - ✅ Số điểm trừ khi ấn nút cập nhật lại mà không có dữ liệu bị thay đổi
     *            12 - ✅ Mua tính năng nâng cao gói X2 Tổng số lượng gói thầu tham gia trong từng năm của mỗi nhà thầu
     *            13 - ✅ Mua tính năng nâng cao gói X2 Tổng giá trị trúng thầu trong từng năm của mỗi nhà thầu
     *            14 - ✅ Mua tính năng nâng cao gói X2 Tổng giá trị phát sinh bảo lãnh dự thầu của mỗi nhà thầu trong từng năm
     *            15 - ✅ Mua tính năng nâng cao gói X2 Tổng giá gói thầu của mỗi nhà thầu trong từng năm
     *            16 - ✅ Mua tính năng nâng cao gói X2 Tổng giá dự thầu của mỗi nhà thầu trong từng năm
     *            25 - ✅ Mua tính năng nâng cao gói X2 Danh sách vi phạm đấu thầu
     *            17 - ✅ Phí xuất dữ liệu cơ bản (Tính theo block 10 nhà thầu 1 lần): Tài khoản thường
     *            18 - ✅ Phí xuất dữ liệu cơ bản (Tính theo block 10 nhà thầu 1 lần): Tài khoản dùng X2
     *            19 - ✅ Số điểm khi add thêm 1 tài khoản phụ
     *            20 - ✅ Tải file báo cáo của gói T100
     *            21 - ✅ Thay đổi chuyên viên hỗ trợ
     *            22 - ✅ Số điểm trừ khi tải file Giấy đăng ký kinh doanh
     *            23 - ✅ Số điểm trừ khi tải file Điều lệ hoạt động của doanh nghiệp
     *            24 - ✅ Số điểm trừ khi tải file Sơ đồ tổ chức
     *
     *
     *            > 100: https://id.dauthau.net/vi/points/#banggia2
     *            101 - Số điểm trừ khi thêm 1 bộ lọc 0 Điểm
     *            102 -Số điểm trừ khi thêm 1 tin theo dõi 20 Điểm
     *            103 -Số điểm dùng để lượt email nhận tin thầu 20 Điểm
     *            104 -Số điểm dùng để kiểm tra số nhà thầu nhận tin 10 Điểm
     *            105 -Số điểm dùng để mua thêm lượt gửi email mời thầu 20 Điểm
     *            106 -Gói dung lượng upload (Số điểm/1MB) 10 Điểm
     *            107 - Xem thông tin nâng cao của doanh nghiệp
     *            108 - Download file danh sách nhà thầu
     *            109 - Thay đổi chuyên viên hỗ trợ
     *
     *            1000 - Hệ thống tặng điểm: toàn bộ $is_reward = 1, giá trị này k set vào nv4_points_log, static_point tự đưa vào nv4_points_static
     *
     * @return
     */
    public function update($site_id, $point, $userid, $message = "", $status = false, $admin_id = 0, $is_reward = 0, $expired = 0, $sub_status = 0, $type_transaction = -1)
    {
        global $nv_Request;
        $this->is_error = false;
        $point = intval($point);
        $userid = abs(intval($userid));
        $status = ($status ? 1 : -1);

        $customerid = $userid;
        if ($admin_id > 0) {
            $customerid = 0;
        }

        // Kiểm tra site
        if (!empty($site_id) and !isset($this->getSites()[$site_id])) {
            $this->is_error = true;
            return $this->lang('paygate_error_site_id');
        }

        $my_point = $this->my_point($userid);

        if ($status != 1) {
            // Trừ
            // kiểm tra trước để tránh trường hợp trừ về số âm
            if ($point > $my_point['point_total'] and $this->check_balance) {
                $this->is_error = true;
                return $this->lang('payclass_error_balance');
            }
        }

        try {
            $this->db->beginTransaction();

            // Lưu thông tin giao dịch trước
            $sql = "INSERT INTO " . NV_POINTS_TABLE . "_log (
                site_id, userid, admin_id, customerid, point_total, created_time, status, sub_status, type_transaction, message, is_reward, expired
            ) VALUES (
                :site_id, :userid, :admin_id, :customerid, :point_total, :created_time, :status, :sub_status, :type_transaction, :message, :is_reward, :expired
            )";
            $sth = $this->db->prepare($sql);
            $sth->bindParam(':site_id', $site_id, PDO::PARAM_INT);
            $sth->bindParam(':userid', $userid, PDO::PARAM_INT);
            $sth->bindParam(':admin_id', $admin_id, PDO::PARAM_INT);
            $sth->bindParam(':customerid', $customerid, PDO::PARAM_INT);
            $sth->bindParam(':point_total', $point, PDO::PARAM_INT);
            $sth->bindValue(':created_time', NV_CURRENTTIME, PDO::PARAM_INT);
            $sth->bindParam(':status', $status, PDO::PARAM_INT);
            $sth->bindParam(':sub_status', $sub_status, PDO::PARAM_INT);
            $sth->bindParam(':type_transaction', $type_transaction, PDO::PARAM_INT);
            $sth->bindParam(':message', $message, PDO::PARAM_STR);
            $sth->bindParam(':is_reward', $is_reward, PDO::PARAM_INT);
            $sth->bindParam(':expired', $expired, PDO::PARAM_INT);
            $exe = $sth->execute();
            $tran_id = $this->db->lastInsertId();
            if (!$tran_id and !$exe) {
                $this->is_error = true;
                $this->db->rollBack();
                return $this->lang('payclass_error_save_transaction');
            } else if ($type_transaction > 1 AND $type_transaction < 1000 AND $point > 1) {
                // Lưu vào bảng lịch sử hoạt động của khách - từ 0h hnay đến 0h hôm sau
                $_tomorrow = strtotime('tomorrow'); // 0h hôm sau
                $_expired = $_tomorrow - NV_CURRENTTIME;
                $time_set = $nv_Request->get_int(NV_POINTS_MODULE . '_viewpoint_' . $userid, 'cookie', 0);
                if ($time_set == 0) {
                    $noi_dung = $this->lang('viewpoint');
                    $sql = 'INSERT INTO nv4_history_activity_custom (userid, content, time_click) VALUES
                        (' . $userid . ', ' . $this->db->quote($noi_dung) . ', ' . NV_CURRENTTIME . ')';
                    $exc = $this->db->query($sql);
                    if ($exc) {
                        $nv_Request->set_Cookie(NV_POINTS_MODULE . '_viewpoint_' . $userid, NV_CURRENTTIME, $_expired);
                    }
                }

            }

            if ($status != 1) {
                // Trừ
                $_point_total = $my_point['point_total'] - $point;
                // Điểm thấp nhất bằng 0
                if ($_point_total < 0) {
                    $_point_total = 0;
                }

                $sql = "UPDATE " . NV_POINTS_TABLE . "_customs SET point_out = point_out + " . $point . ", point_total = " . $_point_total . ",  updatetime= " . NV_CURRENTTIME . " WHERE userid=" . $userid;

                if (!$this->db->exec($sql)) {
                    $this->is_error = true;
                    $this->db->rollBack();
                    return $this->lang('payclass_error_update_account');
                }

                // ghi log vào điểm tặng đã sử dụng
                $this->insert_point_use($point, $userid, 0);
            } else {
                // Cộng
                $sql = "UPDATE " . NV_POINTS_TABLE . "_customs SET point_in = point_in + " . $point . ", point_total = point_total + " . $point . ", updatetime= " . NV_CURRENTTIME . " WHERE userid=" . $userid;
                if (!$this->db->exec($sql)) {
                    $this->is_error = true;
                    $this->db->rollBack();
                    return $this->lang('payclass_error_update_account');
                }
            }
            $this->db->commit();
        } catch (PDOException $e) {
            trigger_error(print_r($e, true));
            $this->db->rollBack();
            $this->is_error = true;
            return $this->lang('payclass_error_save_transaction');
        }

        $this->nv_Cache->delMod(NV_POINTS_MODULE);
        return $tran_id;
    }

    public function insert_point_use($point, $userid, $id)
    {
        // check_expired = 1, các log đã hết hạn,
        // check_expired = 0, log còn thời hạn
        // check_expired = 2, log đã sử dụng hết điểm
        $_sql = 'SELECT * FROM ' . NV_POINTS_TABLE . '_log WHERE userid =' . $userid . ' AND status = 1 AND expired > ' . NV_CURRENTTIME . ' AND check_expired = 0 ORDER BY expired ASC LIMIT 1';
        if ($id) {
            $_sql = 'SELECT * FROM ' . NV_POINTS_TABLE . '_log WHERE id!= ' . $id . ' AND userid =' . $userid . ' AND status = 1 AND expired > ' . NV_CURRENTTIME . ' AND check_expired = 0 ORDER BY expired ASC LIMIT 1';
        }
        $_query = $this->db->query($_sql);
        while ($_row = $_query->fetch()) {
            // số điểm dùng quá số điểm tổng dc tặng trong 1 log,
            // ghi 1 phần sử dụng vào log này
            // chạy tiếp và ghi vào log sau
            if (($_row['point_use'] + $point) > $_row['point_total']) {
                $this->db->exec("UPDATE " . NV_POINTS_TABLE . "_log SET point_use = " . $_row['point_total'] . ", check_expired = 2 WHERE id=" . $_row['id'] . "");
                $_point = ($point + $_row['point_use']) - $_row['point_total'];
                $this->insert_point_use($_point, $userid, $_row['id']);
            } elseif (($_row['point_use'] + $point) == $_row['point_total']) {
                // số điểm bằng nhau-> dùng hết
                $this->db->exec("UPDATE " . NV_POINTS_TABLE . "_log SET point_use = " . $_row['point_total'] . ", check_expired = 2 WHERE id=" . $_row['id'] . "");
            } else {
                // số điểm nhỏ hơn
                $this->db->exec("UPDATE " . NV_POINTS_TABLE . "_log SET point_use = " . ($_row['point_use'] + $point) . " WHERE id=" . $_row['id'] . "");
            }
        }
    }

    public function isError()
    {
        return $this->is_error;
    }

    /**
     *
     * @return mixed|array
     */
    private function getSites()
    {
        try {
            $sql = "SELECT * FROM nv4_wallet_sites";
            return $this->nv_Cache->db($sql, 'id', 'wallet');
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     *
     * @return nukeviet_points
     */
    public function setCheckBalance()
    {
        $this->check_balance = true;
        return $this;
    }

    /**
     *
     * @return nukeviet_points
     */
    public function setNoCheckBalance()
    {
        $this->check_balance = false;
        return $this;
    }
}
