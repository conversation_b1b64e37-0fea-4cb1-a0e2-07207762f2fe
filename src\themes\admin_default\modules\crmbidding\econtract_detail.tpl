<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>

<link type="text/css" href="{NV_BASE_SITEURL}themes/{TEMPLATE}/css/econtract.css" rel="stylesheet">

<div class="row" id="view-econtract-detail">
    <!-- BEGIN: preview -->
    <div class="col-md-17">
        <!-- BEGIN: show -->
        <div id="content-econtract-preview">
            <div class="preview-econtract-actions">
                <a href="{BACK_URL}" style="background-color: #abbae2;"><em class="fa fa-backward"></em> <span> Quay lại danh sách</span></a>
                <a href="{DETAIL_URL}" style="background-color: #1756ff;"><em class="fa fa-file-pdf-o"></em> <span>Xem chi tiết hợp đồng</span></a>
                <!-- BEGIN: show_version_actions -->
                <a href="{EDIT_URL}"><em class="fa fa-edit"></em> <span>Bổ sung thông tin</span></a>
                <a href="{UPLOAD_URL}" style="background-color: #d30000;"><em class="fa fa-cloud-upload"></em> <span>Tải lên hợp đồng đã ký</span></a>
                <a href="#" style="background-color: #0500c8;" id="btn_sign_preview"><em class="fa fa-paper-plane"></em> <span>Trình lãnh đạo ký</span></a>
                <!-- BEGIN: approve_button -->
                <a href="{APPROVE_URL}" style="background-color: green;"><em class="fa fa-check"></em> <span>Duyệt hợp đồng khách tải lên</span></a>
                <!-- END: approve_button -->
                <!-- END: show_version_actions -->
                <a href="{DOWNLOAD_URL}" style="background-color: #ff7600;"><em class="fa fa-cloud-download"></em> <span>Tải phiên bản đang xem</span></a>
                <!-- BEGIN: action_save_draft -->
                <a href="{SAVE_DRAFT}" style="background-color: #1c05ebe4;" id="btn_save_draft"><em class="fa fa-save"></em> <span>Lưu thay đổi</span></a>
                <!-- END: action_save_draft  -->
            </div>
            <!-- BEGIN: temp -->
            <div class="label-preview-alert">
                Đây chỉ là bản xem thử, dữ liệu chưa được lưu. Vui lòng chọn <strong>Lưu thay đổi</strong> để cập nhật vào dữ liệu.
            </div>
            <!-- END: temp -->
            <!-- BEGIN: show_label -->
            <div class="label-preview">Xem trước</div>
            <!-- END: show_label -->
            {ECONTRACT_CONTENT}
        </div>
        <!-- END: show -->
        <!-- BEGIN: empty -->
        <div class="alert alert-danger">Không thể tải xem trước. Quay lại <a href="{DETAIL_URL}"><strong>Chi tiết hợp đồng</strong></a>.</div>
        <!-- END: empty -->
    </div>
    <script type="text/javascript">
        $(document).ready(function () {
            // gọi ajax để lưu hợp đồng khi click vào btn_save_draft
            $('#btn_save_draft').on('click', function (event) {
                event.preventDefault();
                var contractId = parseInt('{ECONTRACT.id}');
                if (!contractId) {
                    alert('Không có hợp đồng để lưu!');
                    return;
                }
                $.ajax({
                    url: $('#btn_save_draft').attr('href'),
                    type: 'POST',
                    data: {
                        save_draft: '{TOKEN}',
                        contract_id: contractId,
                    },
                    success: function (response) {
                        if (response.status) {
                            window.location.href = response.url_reload;
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function () {
                        alert('Đã có lỗi xảy ra khi thực hiện lưu xem trước. Vui lòng báo phòng kỹ thuật kiểm tra lại!');
                    }
                });
            });
        });
    </script>
    <!-- END: preview -->

    <!-- BEGIN: upload -->
    <div class="col-md-17">
        <div class="row">
            <div class="col-md-24">
                <div class="top-action-buttons">
                    <a href="{BACK_URL}" class="btn btn-sm btn-default"><em class="fa fa-backward"></em> <span> Quay lại danh sách</span></a>
                    <a href="{DETAIL_URL}" class="btn btn-sm btn-success"><em class="fa fa-file-pdf-o"></em> Xem chi tiết</a>
                    <!-- BEGIN: upload_btn -->
                    <a href="{EDIT_URL}" class="btn btn-sm btn-info"><em class="fa fa-edit"></em> Bổ sung thông tin</a>
                    <!-- <button id="btn_sign" class="btn btn-sm btn-primary"><em class="fa fa-paper-plane"></em> Trình lãnh đạo ký</button> -->
                    <!-- END: upload_btn -->
                    <!-- BEGIN: show_act -->
                    <a href="{DOWNLOAD_URL}" class="btn btn-sm btn-warning"><em class="fa fa-cloud-download"></em> Tải phiên bản đang xem</a>
                    <a href="{PREVIEW_URL}" class="btn btn-sm btn-default pull-right"><em class="fa fa-eye"></em> Xem trước</a>
                    <!-- END: show_act -->
                </div>
            </div>
        </div>
        <form id="form-upload" method="post" enctype="multipart/form-data" action="{UPLOAD_URL}">
            <div id="content-econtract-upload">
                <!-- BEGIN: error -->
                <div class="alert alert-danger">{ERROR}</div>
                <!-- END: error -->

                <!-- BEGIN: upload_status -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <label class="control-label col-md-10">Tình trạng hợp đồng <span class="required">*</span></label>
                            <div class="col-md-14">
                                <select name="contract_status" class="form-control">
                                    <!-- BEGIN: status -->
                                    <option value="{STATUS.key}">{STATUS.value}</option>
                                    <!-- END: status -->
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END: upload_status -->
                <div class="text-danger">
                    Nếu tải lên file <strong>PDF</strong> bị lỗi. Vui lòng nén lại thành file <strong>ZIP</strong> rồi tải lên lại.
                </div>
                <div id="econtract-signed-drop-area">
                    <div class="upload-box ml-2" id="drop-zone">
                        <div class="upload-placeholder">
                            <i class="fa fa-cloud-upload"></i>
                            <p>Kéo và Thả File của bạn vào đây</p>
                            <p>Hoặc</p>
                            <button class="btn btn-info" type="button" id="select-file-button">Chọn File</button>
                        </div>
                    </div>
                    <input type="file" class="d-none hidden" id="contract-file" name="contract_path">
                    <div id="file-list"></div>
                </div>

            </div>

            <div class="bottom-action-buttons">
                <button type="submit" name="save" value="1" class="btn btn-primary"><em class="fa fa-cloud-upload"></em> Tiến hành tải lên</button>
            </div>
        </form>
    </div>

    <script type="text/javascript">
    $(document).ready(function() {
        // Hợp đồng điện tử đã ký
        initDropZone({
            dropZone: '#drop-zone',
            fileInput: '#contract-file',
            selectButton: '#select-file-button',
            fileList: '#file-list',
            defaultFilePath: '{ECONTRACT.contract_path}',
        });

        /**
         * Xử lý submit form:
         * - Bắt buộc phải tải lên ít nhất 1 trong 2 file
         * - Nếu có tải file hợp đồng thì bắt buộc chọn contract_status != 0
         */
        $('#form-upload').on('submit', function(event) {
            let contractFiles = $('#contract-file')[0].files;
            if (contractFiles.length === 0) {
                alert('{LANG.error_no_file_uploaded}');
                event.preventDefault();
                return;
            }
            // Nếu có tải file hợp đồng thì bắt buộc chọn contract_status != 0
            if (contractFiles.length > 0) {
                let statusVal = $('select[name="contract_status"]').val();
                if (statusVal === "0") {
                    alert('{LANG.status_not_select}');
                    event.preventDefault();
                    return;
                }
            }
        });

        function checkFileInputs() {
            let contractFiles = $('#contract-file')[0].files;
            if(contractFiles.length === 0) {
                $('#form-upload button[type="submit"]').prop('disabled', true);
            } else {
                $('#form-upload button[type="submit"]').prop('disabled', false);
            }
        }

        checkFileInputs();
        $('#contract-file').on('change', function() {
            checkFileInputs();
        });
    });
    </script>
    <!-- END: upload -->

    <!-- BEGIN: view_detail -->
    <div class="col-md-17">
        <div class="row">
            <div class="col-md-24">
                <div class="top-action-buttons">
                    <a href="{BACK_URL}" class="btn btn-sm btn-default"><em class="fa fa-backward"></em> <span> Quay lại danh sách</span></a>
                    <!-- BEGIN: view_detail_act -->
                    <!-- BEGIN: show_version_actions -->
                    <a href="{EDIT_URL}" class="btn btn-sm btn-info"><em class="fa fa-edit"></em> Bổ sung thông tin</a>
                    <button id="btn_sign_detail" class="btn btn-sm btn-primary"><em class="fa fa-paper-plane"></em> Trình lãnh đạo ký</button>
                    <a href="{UPLOAD_URL}" class="btn btn-sm btn-danger"><em class="fa fa-cloud-upload"></em> Tải lên hợp đồng đã ký</a>
                    <!-- END: show_version_actions -->
                    <!-- END: view_detail_act -->
                    <a href="{DOWNLOAD_URL}" class="btn btn-sm btn-warning"><em class="fa fa-cloud-download"></em> Tải phiên bản đang xem</a>
                    <!-- BEGIN: view_detail_act_change -->
                    <a href="javascript:void(0);" class="btn btn-sm btn-info" onclick="nv_change_type_econtract(0)"><em class="fa fa-eye"></em> Mẫu HĐ bản dài</a>
                    <a href="javascript:void(0);" class="btn btn-sm btn-info" onclick="nv_change_type_econtract(1)"><em class="fa fa-eye-slash"></em> Mẫu HĐ bản ngắn</a>
                    <!-- END: view_detail_act_change -->
                    <a href="{PREVIEW_URL}" class="btn btn-sm btn-default pull-right"><em class="fa fa-eye"></em>Xem trước hợp đồng</a>
                </div>
            </div>
        </div>
        <!-- Thông tin bên A -->
        <div class="section mb-4">
            <h2 class="section-title">Thông tin Bên A trên hợp đồng</h2>

            <div class="row econtract-form-info two-cols mt-5" id="thongtinbenA">
                <!-- BEGIN: customer_type_1 -->
                <div class="col-md-12" id="field-customer-name">
                    <div class="row">
                        <label class="control-label col-md-7">Tên công ty <span class="required">*</span></label>
                        <div class="control-label col-md-17">{ECONTRACT.c_name}</div>
                    </div>
                </div>
                <div class="col-md-12" id="field-tax-code">
                    <div class="row">
                        <label class="control-label col-md-7">MST <span class="required">*</span></label>
                        <div class="control-label col-md-17">{ECONTRACT.tax_code}</div>
                    </div>
                </div>
                <div class="col-md-12" id="field-representative">
                    <div class="row">
                        <label class="control-label col-md-7">Người đại diện <span class="required">*</span></label>
                        <div class="control-label col-md-17">{ECONTRACT.representative}</div>
                    </div>
                </div>
                <div class="col-md-12" id="field-jobtitle">
                    <div class="row">
                        <label class="control-label col-md-7">Chức vụ <span class="required">*</span></label>
                        <div class="control-label col-md-17">{ECONTRACT.jobtitle}</div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="row">
                        <label class="control-label col-md-7">Số điện thoại <span class="required">*</span></label>
                        <div class="control-label col-md-17">{ECONTRACT.phone}</div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="row">
                        <label class="control-label col-md-7">Email <span class="required">*</span></label>
                        <div class="control-label col-md-17 email-field">{ECONTRACT.email}</div>
                    </div>
                </div>
                <div class="col-md-24">
                    <div class="row">
                        <label class="control-label col-md-3">Địa chỉ <span class="required">*</span></label>
                        <div class="control-label col-md-21" style="padding-left: 20px;">{ECONTRACT.c_address}</div>
                    </div>
                </div>
                <div class="col-md-24">
                    <div class="row">
                        <label class="control-label col-md-3">Tài khoản số:</label>
                        <div class="control-label col-md-21" style="padding-left: 20px;">{ECONTRACT.bank_account}</div>
                    </div>
                </div>
                <!-- BEGIN: show_label_authorized -->
                <div class="col-md-24">
                    <div class="row">
                        <label class="control-label col-md-3">Thông tin ủy quyền</label>
                        <div class="control-label col-md-21" style="padding-left: 20px;">{ECONTRACT.authorization_letter}</div>
                    </div>
                </div>
                <!-- END: show_label_authorized -->
                <!-- END: customer_type_1 -->
                <!-- BEGIN: customer_type_0 -->
                <div class="col-md-24">
                    <div class="label label-info mb-0">Khách hàng cá nhân</div>
                </div>
                <div class="col-md-12" id="field-customer-name">
                    <div class="row">
                        <label class="control-label col-md-7">Tên khách hàng <span class="required">*</span></label>
                        <div class="control-label col-md-17">{ECONTRACT.c_name}</div>
                    </div>
                </div>
                <div class="col-md-12" id="field-cccd">
                    <div class="row">
                        <label class="control-label col-md-7">CCCD <span class="required">*</span></label>
                        <div class="control-label col-md-17">{ECONTRACT.cccd}</div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="row">
                        <label class="control-label col-md-7">Số điện thoại <span class="required">*</span></label>
                        <div class="control-label col-md-17">{ECONTRACT.phone}</div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="row">
                        <label class="control-label col-md-7">Email <span class="required">*</span></label>
                        <div class="control-label col-md-17">{ECONTRACT.email}</div>
                    </div>
                </div>
                <div class="col-md-24">
                    <div class="row">
                        <label class="control-label col-md-3">Địa chỉ <span class="required">*</span></label>
                        <div class="control-label col-md-21" style="padding-left: 20px;">{ECONTRACT.c_address}</div>
                    </div>
                </div>
                <!-- END: customer_type_0 -->
            </div>
        </div>

        <!-- Thông tin người nhận -->
        <div class="section mb-4">
            <h2 class="section-title">
                Thông tin người nhận hợp đồng
                <div class="help-text">Hãy điền thông tin người nhận hợp đồng nếu khách yêu cầu bản cứng</div>
            </h2>
            <div class="row econtract-form-info-2">
                <div class="col-md-12">
                    <div class="row">
                        <label class="control-label col-md-10">Họ tên người nhận</label>
                        <div class="control-label col-md-14">{ECONTRACT.receiver}</div>
                    </div>
                    <div class="row">
                        <label class="control-label col-md-10">SĐT người nhận</label>
                        <div class="control-label col-md-14">{ECONTRACT.receiver_phone}</div>
                    </div>
                    <div class="row">
                        <label class="control-label col-md-10">Địa chỉ người nhận</label>
                        <div class="control-label col-md-14">{ECONTRACT.receiver_address}</div>
                    </div>
                </div>
            </div>

        </div>

        <div class="section mb-4">
            <h2 class="section-title">Thông tin danh sách các sản phẩm, dịch vụ</h2>
            <!-- BEGIN: no_order_inlist -->
            <div class="alert alert-info">Không tìm thấy dữ liệu</div>
            <!-- END: no_order_inlist -->
            <!-- BEGIN: order_inlist -->
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Gói dịch vụ</th>
                        <th>Giá trên hợp đồng</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop_item -->
                    <tr>
                        <td>{ORDER_ITEM.title} {ORDER_ITEM.title_type}</td>
                        <!-- BEGIN: price -->
                        <td rowspan="{ORDER_ITEM.rowspan}">{ORDER_ITEM.vip_price_format}</td>
                        <!-- END: price -->
                    </tr>
                    <!-- END: loop_item -->
                </tbody>
            </table>
            <div class="sum-total-orders">
                <div class="row">
                    <div class="col-md-10 text-right"><strong>Tổng dịch vụ</strong></div>
                    <div class="col-md-14">{TOTAL}</div>
                </div>
                <div class="row">
                    <div class="col-md-10 text-right"><strong>Được giảm</strong></div>
                    <div class="col-md-14">{DISCOUNT}</div>
                </div>
                <div class="row">
                    <div class="col-md-10 text-right"><strong>Tổng thanh toán</strong></div>
                    <div class="col-md-14"><strong>{TOTAL_END}</strong></div>
                </div>
            </div>
            <!-- END: order_inlist -->

        </div>

        <!-- BEGIN: term_changed -->
        <div class="section">
            <h2 class="section-title">Hợp đồng đã thay đổi điều khoản</h2>
            <!-- Hiện tệp pdf ở đây -->
            <a href="{PREVIEW_URL}" class="btn btn-xs btn-info mb-3"><em class="fa fa-file-pdf-o"></em> Hợp đồng đính kèm</a>
            <pre>{ECONTRACT.term_changed_notes}</pre>
        </div>
        <!-- END: term_changed -->
    </div>
    <!-- END: view_detail -->

    <!-- BEGIN: report_document -->
    <div class="col-md-17">
        <div id="content-econtract-preview">
            <div class="preview-econtract-actions">
                <a href="{BACK_URL}" style="background-color: #abbae2;"><em class="fa fa-backward"></em> <span> Quay lại danh sách</span></a>
                <a href="{DETAIL_URL}" style="background-color: #1756ff;"><em class="fa fa-file-pdf-o"></em> <span>Xem chi tiết hợp đồng</span></a>
                <a href="{DOWNLOAD_QUOTE_URL}" style="background-color: #ff7600;"><em class="fa fa-cloud-download"></em> <span>Tải xuống báo giá</span></a>
                <a href="{DOWNLOAD_PROPOSAL_URL}" style="background-color: #04983b;"><em class="fa fa-cloud-download"></em> <span>Tải xuống đề nghị thanh toán</span></a>
                <!-- BEGIN: act_order -->
                <a href="{DOWNLOAD_PURCHASE_ORDER_URL}" style="background-color: #04983b;"><em class="fa fa-cloud-download"></em> <span>Tải xuống đơn đặt hàng</span></a>
                <!-- END: act_order -->
                <a href="{DOWNLOAD_CONTRACT_LUIDATION_URL}" style="background-color: #04983b;"><em class="fa fa-cloud-download"></em> <span>Tải biên bản thanh lý hợp đồng</span></a>
                <a href="{DOWNLOAD_ACCEPTANCE_REPORT_URL}" style="background-color: #04983b;"><em class="fa fa-cloud-download"></em> <span>Tải biên bản bàn giao</span></a>
            </div>
            <div class="label-preview">Xem trước</div>
            {ECONTRACT_REPORT_DOCUMENT}
        </div>
    </div>
    <!-- END: report_document -->

    <div class="col-md-7">
        <div class="right-sidebar">
            <div class="box-info-item">
                <h3 class="sidebar-title">
                    Số hợp đồng - Phiên bản
                    <!-- BEGIN: show_action_choose_version -->
                    <a href="{SETVERSION_URL}" class="btn btn-xs btn-default pull-right"><em class="fa fa-check"></em> Chọn làm phiên bản chính thức</a>
                    <!-- END: show_action_choose_version -->
                </h3>
                <div class="box-content">
                    {ECONTRACT.contract_no} - {ECONTRACT.version_data.code}
                    <!-- BEGIN: show_label_current_version -->
                    <span class="label label-success label-current-version">Ph.bản chính thức</span>
                    <!-- END: show_label_current_version -->
                </div>
                <div class="box-content text-muted">
                    {ECONTRACT.type_econtract}
                </div>
            </div>

            <!-- BEGIN: report_action -->
            <div class="report-download-all">
                <a href="{DOWNLOAD_ALL_URL}" class="btn btn-xs btn-primary"><i class="fa fa-download"></i> Tải về tất cả các File</a>
            </div>
            <!-- BEGIN: loop -->
            <div class="box-info-item">
                <h3 class="sidebar-title">
                    {TITLE_LABEL}
                    <!-- BEGIN: act_edit -->
                    <a href="javascript:;" onclick="changeEcontract(true);" class="btn btn-xs btn-success"><span>Chỉnh sửa <em class="fa fa-edit"></em></span></a>
                    <!-- END: act_edit -->
                </h3>
                <div class="box-content">
                    <!-- BEGIN: show_title -->
                    {TITLE_PREVEW_DETAIL}
                    <!-- END: show_title -->
                    <ul class="list-order-label mt-1">
                        <!-- BEGIN: show_action -->
                        <li class="label-order-title"><a href="{LINK_DETAIL}" class="btn btn-xs btn-success"><span>Xem trực tiếp <em class="fa fa-external-link"></em></span></a></li>
                        <li class="label-order-title"><a href="{LINK_DOWNLOAD}" class="btn btn-xs btn-info"><span>Tải về <em class="fa fa-cloud-download"></em></span></a></li>
                        <!-- END: show_action -->
                        <li class="label-order-title"><a href="{LINK_UPLOAD}" class="btn btn-xs btn-danger"><span>Tải lên <em class="fa fa-cloud-upload"></em></span></a></li>
                    </ul>
                </div>
            </div>
            <!-- END: loop -->
            <!-- END: report_action -->

            <div class="box-info-item">
                <h3 class="sidebar-title">Sale phụ trách</h3>
                <div class="box-content">{ECONTRACT.sale_name}</div>
            </div>
            <div class="box-info-item">
                <h3 class="sidebar-title">Thông tin khách hàng</h3>
                <div class="box-content">
                    <ul>
                        <li><strong>Tài khoản:</strong> <span>{ECONTRACT.username}</span></li>
                        <li><strong>Họ tên:</strong> <span>{ECONTRACT.fullname}</span></li>
                    </ul>
                </div>
            </div>
            <div class="box-info-item">
                <h3 class="sidebar-title">Đơn hàng</h3>
                <div class="box-content">
                    <!-- BEGIN: show_orders -->
                    <ul class="list-order-label">
                        <!-- BEGIN: loop_order -->
                        <li class="label-order-title"><a href="{ORDER.url_detail}" target="_blank" class="btn btn-xs btn-success"><span>{ORDER.code} <em class="fa fa-external-link"></em></span></a></li>
                        <!-- END: loop_order -->
                    </ul>
                    <!-- END: show_orders -->
                    <!-- BEGIN: merged_contract -->
                    <span class="text-danger">{MERGE_CONTRACT_TEXT}</span>
                    <!-- END: merged_contract -->
                </div>
            </div>

            <div class="box-info-item">
                <h3 class="sidebar-title">Trạng thái hợp đồng</h3>
                <div class="box-content">
                    {ECONTRACT.status_label}
                    <!-- BEGIN: show_label_term_changed -->
                    <div class="text-warning"><em class="fa fa-info-circle"></em> Điều khoản hợp đồng đã thay đổi</div>
                    <!-- END: show_label_term_changed -->
                </div>
            </div>
            <div class="box-info-item">
                <h3 class="sidebar-title">Giai đoạn hiện tại</h3>
                <div class="box-content">
                    {ECONTRACT.stage_label}
                </div>
            </div>
            <div class="box-info-item">
                <h3 class="sidebar-title">Giai đoạn tiếp theo</h3>
                <div class="box-content">
                    {ECONTRACT.stage_next_label}
                </div>
            </div>
            <div class="box-info-item">
                <h3 class="sidebar-title">Thời gian cập nhật</h3>
                <div class="box-content">{ECONTRACT.updated_at}</div>
            </div>
            <!-- BEGIN: show_version -->
            <div class="box-info-item">
                <h3 class="sidebar-title">Phiên bản</h3>
                <div class="box-content">
                    <ul class="list-versions">
                        <!-- BEGIN: loop_version -->
                        <li class="version-item {VERSION.is_current_class} {VERSION.is_viewing_class}">
                            <a href="{VERSION.link}">
                                <h4><em class="fa fa-file-pdf-o"></em> {VERSION.code} {VERSION.is_current_label} {VERSION.is_viewing_label}</h4>
                                <p class="text-muted"><em><em class="fa fa-clock-o"></em> {VERSION.created_at}</em> <span class="label label-info">{VERSION.status_label}</span></p>
                                <p class="text-muted">
                                    <a href="{VERSION.link_file}" class="text-muted">
                                        <em class="fa fa-paperclip"></em> Hợp đồng đính kèm
                                    </a>
                                </p>
                            </a>
                        </li>
                        <!-- END: loop_version -->
                    </ul>
                </div>
            </div>
            <!-- END: show_version -->


            <!-- BEGIN: show_logs -->
            <div class="box-info-item">
                <h3 class="sidebar-title">Nhật ký hệ thống</h3>
                <div class="box-content">
                    <ul class="list-logs">
                        <!-- BEGIN: loop_log -->
                        <li class="log-item">
                            <div class="text-muted"><strong>{LOG.fullname}</strong> - <em>{LOG.created_at}</em></div>
                            <div class="log-title">
                                {LOG.action_desc}
                                <!-- BEGIN: detail_log_label -->
                                &nbsp; <a data-toggle="collapse" href="#logOther{LOG.id}" aria-expanded="false" aria-controls="logOther{LOG.id}"><i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
                                <!-- END: detail_log_label -->
                            </div>
                            <!-- BEGIN: detail_log -->
                            <div class="collapse" id="logOther{LOG.id}">
                                <ul class="logotherlists">
                                    <!-- BEGIN: loop -->
                                    {CHANGED_KEY}
                                    <ul>
                                        <li>{LANG.old}: {CHANGED_DATA.old}</li>
                                        <li>{LANG.new}: {CHANGED_DATA.new}</li>
                                    </ul>
                                    <!-- END: loop -->
                                </ul>
                            </div>
                            <!-- END: detail_log -->
                        </li>
                        <!-- END: loop_log -->
                    </ul>
                </div>
            </div>
            <!-- END: show_logs -->
        </div>
    </div>

<!-- Modal Edit Contract -->
<div class="modal fade" id="modalEditContract" tabindex="-1" role="dialog" aria-labelledby="modalEditContractLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h3 class="modal-title"><em class="fa fa-edit"></em> <strong>CHỈNH SỬA THÔNG TIN HỢP ĐỒNG</strong></h3>
            </div>
            <div class="modal-body">

                <div class="table-responsive">
                    <table class="table table-bordered mb-0">
                        <tr>
                            <td>Thời gian lập biên bản</td>
                            <td>
                                <input type="text" class="form-control datepicker" name="acceptance_time" value="{ECONTRACT.acceptance_time}" autocomplete="off"/>
                            </td>
                        </tr>
                        <tr>
                            <td>Thời gian giao hàng</td>
                            <td>
                                <input type="text" class="form-control datepicker" name="delivery_time" value="{ECONTRACT.delivery_time}" autocomplete="off"/>
                            </td>
                        </tr>
                        <tr>
                            <td>Tài khoản</td>
                            <td><input type="text" name="account" value="{ECONTRACT.account}"></td>
                        </tr>
                        <tr>
                            <td>Mật khẩu</td>
                            <td><input type="text" name="password" value="{ECONTRACT.password}"></td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" onclick="saveEcontractEdit();">Lưu thay đổi</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function () {
        $('#btn_sign_preview, #btn_sign_upload, #btn_sign_detail').on('click', function () {
            var contractId = parseInt('{ECONTRACT.id}');
            if (!contractId) {
                alert('Không có hợp đồng để ký!');
                return;
            }
            $.ajax({
                url: '{FORM_ACTION}',
                type: 'POST',
                data: {
                    sign_contract: '{TOKEN}',
                    contract_id: contractId
                },
                success: function (response) {
                    if (response.status) {
                        alert(response.message);
                        $('#btn_sign_preview, #btn_sign_upload, #btn_sign_detail').addClass('disabled');
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                },
                error: function (xhr, status, error) {
                    var errorMessage = 'Đã xảy ra lỗi khi gửi yêu cầu!\n';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage += 'Chi tiết: ' + xhr.responseJSON.message;
                    } else if (xhr.responseText) {
                        errorMessage += 'Chi tiết: ' + xhr.responseText;
                    } else {
                        errorMessage += 'Mã lỗi: ' + xhr.status + ' - ' + error;
                    }
                    alert(errorMessage);
                }
            });
        });

        // Khởi tạo datepicker cho các input thời gian
        $('.datepicker').datepicker({
            dateFormat: 'dd/mm/yy',
            changeMonth: true,
            changeYear: true,
            showOtherMonths: true,
            selectOtherMonths: true
        });
    });

    function changeEcontract(showModal) {
        if (showModal) {
            $('#modalEditContract').modal('show');
        }
    }

    function saveEcontractEdit() {
        $.ajax({
            url: '{FORM_ACTION}',
            type: 'POST',
            data: {
                update_econtract: '{TOKEN}',
                econtract_id: '{ECONTRACT.id}',
                acceptance_time: $('input[name="acceptance_time"]').val(),
                delivery_time: $('input[name="delivery_time"]').val(),
                account: $('input[name="account"]').val(),
                password: $('input[name="password"]').val(),
            },
            success: function(response) {
                if (response.status) {
                    alert('Cập nhật thành công!');
                    $('#modalEditContract').modal('hide');
                    location.reload();
                } else {
                    alert(response.message || 'Có lỗi xảy ra!');
                }
            },
            error: function() {
                alert('Đã xảy ra lỗi khi gửi yêu cầu!');
            }
        });
    }

    function nv_change_type_econtract(newValue) {
        $.post(script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=econtract_detail&id={ECONTRACT.id}&version={ECONTRACT.current_version}&nocache=' + new Date().getTime(), 'change_type_econtract=1&econtract_id={ECONTRACT.id}&type_econtract=' + newValue, function(res) {
            var r_split = res.split('_');
            if (r_split[0] != 'OK') {
                alert(nv_is_change_act_confirm[2]);
            }
            window.location.href = script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=econtract_detail&id={ECONTRACT.id}&version={ECONTRACT.current_version}';
            return;
        });
        return;
    }

    // Function xử lý kéo thả và upload file
    (function($) {
        const MAX_FILE_SIZE = {MAX_FILE_ECONTRACT_SIZE}; // 10MB
        const ALLOWED_FILE_TYPES = ['application/pdf', 'application/zip', 'application/x-zip-compressed'];
        const isVietnamese = true;

        /**
         * Khởi tạo khu vực tải file với các tham số cấu hình
         * @param {object} options Các thiết lập bao gồm:
         *   dropZone: Khu vực kéo thả file,
         *   fileInput: selector của thẻ file input,
         *   selectButton: button chọn file tải lên,
         *   fileList: danh sách file,
         *   defaultFilePath: file đã tải lên,
         */
        function initDropZone(options) {
            let selectedFiles = [];
            const $dropZone = $(options.dropZone);
            const $fileInput = $(options.fileInput);
            const $selectButton = $(options.selectButton);
            const $fileList = $(options.fileList);
            const defaultFilePath = options.defaultFilePath || '';

            function processFile(file) {
                if (!file) return;
                if (!ALLOWED_FILE_TYPES.includes(file.type)) {
                    alert(isVietnamese
                        ? `File không hợp lệ. Vui lòng chọn file có định dạng pdf hoặc zip`
                        : `File is not valid. Please select a file in pdf or zip`);
                } else if (file.size > MAX_FILE_SIZE) {
                    alert(isVietnamese
                        ? `File quá lớn. Vui lòng chọn file dưới ${MAX_FILE_SIZE / 1024 / 1024} MB.`
                        : `File is too large. Please select a file under ${MAX_FILE_SIZE / 1024 / 1024} MB.`);
                } else {
                    selectedFiles = [file];
                }
            }

            function updateFileList() {
                $fileList.empty();
                if (selectedFiles.length > 0) {
                    const fileItem = $(`
                        <div class="file-item">
                            <span>${selectedFiles[0].name}</span>
                            <button type="button" class="btn btn-danger btn-sm ml-1 remove-file">x</button>
                        </div>
                    `);
                    $fileList.append(fileItem);
                } else if (defaultFilePath) {
                    const fileItem = $(`
                        <div class="file-item">
                            <span>${defaultFilePath}</span>
                        </div>
                    `);
                    $fileList.append(fileItem);
                }
            }

            // Xử lý xóa file
            $fileList.on('click', '.remove-file', function() {
                if (confirm(isVietnamese ? 'Bạn có muốn xóa file đã chọn?' : 'Do you want to remove the selected file?')) {
                    selectedFiles = [];
                    updateFileList();
                    $fileInput.val('');
                }
            });

            // Sự kiện nhấn nút chọn file
            $selectButton.on('click', function() {
                $fileInput.click();
            });

            // Sự kiện thay đổi input file
            $fileInput.on('change', function(event) {
                const file = event.target.files[0];
                processFile(file);
                updateFileList();
                // Đồng bộ lại với file input
                if (file) {
                    const dt = new DataTransfer();
                    dt.items.add(file);
                    $fileInput[0].files = dt.files;
                }
            });

            // Sự kiện kéo thả
            $dropZone.on('dragover', function(event) {
                event.preventDefault();
                $dropZone.addClass('dragging');
            });

            $dropZone.on('dragleave', function(event) {
                event.preventDefault();
                $dropZone.removeClass('dragging');
            });

            $dropZone.on('drop', function(event) {
                event.preventDefault();
                $dropZone.removeClass('dragging');
                const file = event.originalEvent.dataTransfer.files[0];
                processFile(file);
                updateFileList();
                if (file) {
                    const dt = new DataTransfer();
                    dt.items.add(file);
                    $fileInput[0].files = dt.files;
                    // Trigger change event để cập nhật checkFileInputs
                    $fileInput.trigger('change');
                }
            });

            $fileInput.data('selectedFiles', selectedFiles);
            $fileInput.on('change', function() {
                $fileInput.data('selectedFiles', selectedFiles);
            });
        }

        window.initDropZone = initDropZone;
    })(jQuery);
</script>
<!-- END: main -->
