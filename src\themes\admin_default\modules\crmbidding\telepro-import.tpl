<!-- BEGIN: main -->
<p>
    <a href="{LINK_TEMPLATE}">{LANG.teleimport_note}</a>
</p>
<!-- BEGIN: result -->
<div class="alert alert-success">
    <p>{LANG.import_result_1}:</p>
    <ul>
        <li>{LANG.import_result_2}: <strong>{NUM_READ}</strong></li>
        <li>{LANG.import_result_3}: <strong>{NUM_INSTALL}</strong></li>
        <li>{LANG.import_result_4}: <strong>{NUM_UPDATE}</strong></li>
        <li>{LANG.import_result_5}: <strong>{NUM_ERROR}</strong></li>
    </ul>
</div>
<!-- END: result -->
<div class="panel panel-default">
    <div class="panel-body">
        <form method="post" action="{FORM_ACTION}" enctype="multipart/form-data" class="form-horizontal">
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-6"><strong>{LANG.teleimport_to_job}:</strong></label>
                <div class="col-sm-14 col-md-18">
                    <select class="form-control" name="job_id">
                        <!-- BEGIN: job -->
                        <option value="{JOB.id}"{JOB.selected}>{JOB.title}</option>
                        <!-- END: job -->
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-6"><strong>{LANG.import_sel_existstype}:</strong></label>
                <div class="col-sm-14 col-md-18">
                    <select class="form-control" name="existstype">
                        <!-- BEGIN: existstype -->
                        <option value="{EXISTSTYPE.key}"{EXISTSTYPE.selected}>{EXISTSTYPE.title}</option>
                        <!-- END: existstype -->
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-6"><strong>{LANG.teleimport_errorhandler}:</strong></label>
                <div class="col-sm-14 col-md-18">
                    <select class="form-control" name="errorhandler">
                        <!-- BEGIN: errorhandler -->
                        <option value="{ERRORHANDLER.key}"{ERRORHANDLER.selected}>{ERRORHANDLER.title}</option>
                        <!-- END: errorhandler -->
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-6"><strong>{LANG.import_sel_excelfile}:</strong></label>
                <div class="col-sm-14 col-md-18">
                    <div class="input-group">
                        <input type="text" readonly="readonly" class="form-control" id="valExcelFile" />
                        <div class="input-group-btn">
                            <div class="btn btn-default" style="padding: 5px 10px;">
                                <div class="filetrigger">
                                    <span>{LANG.select}</span>
                                    <input type="file" name="excel">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-14 col-md-18 col-sm-offset-10 col-md-offset-6">
                    <input type="submit" name="save" value="{LANG.import_submit}" class="btn btn-primary" />
                </div>
            </div>
        </form>
    </div>
</div>
<!-- BEGIN: error -->
<div class="alert alert-danger">{ERROR}</div>
<!-- END: error -->
<script type="text/javascript">
$(function() {
    $('[name="excel"]').change(function() {
        $('#valExcelFile').val($(this).val());
    });
});
</script>
<!-- END: main -->
