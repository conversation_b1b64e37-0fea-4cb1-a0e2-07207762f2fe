<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CheckUserHotlead implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'leads';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_file = $module_info['module_file'];
        $module_data = $module_info['module_data'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/functions/hotlead.functions.php';

        $userid = $nv_Request->get_int('userid', 'post', 0);

        if (!empty($userid)) {
            try {
                $query = $db->query("SELECT tb1.email, tb2.phone FROM " . NV_USERS_GLOBALTABLE . " as tb1 INNER JOIN " . NV_USERS_GLOBALTABLE . "_info as tb2 ON tb1.userid=tb2.userid WHERE tb1.userid=" . $userid);
                if ($user = $query->fetch()) {
                    // tìm lead cơ hội gần nhất theo thông tin của users: sdt, email
                    $where = [];
                    if (!empty($user['email'])) {
                        $where[] = 'email=' . $db->quote($user['email']);
                        $where[] = 'FIND_IN_SET(' . $db->quote($user['email']) . ', sub_email)';
                    }
                    if (!empty($user['phone'])) {
                        $tmp_phone = $user['phone'];
                        if (preg_match('/(\d{9})$/', $user['phone'], $m)) {
                            $tmp_phone = $m[0];
                        }
                        $where[] = 'phone_search=' . $tmp_phone;
                        $where[] = 'FIND_IN_SET(' . $tmp_phone . ', sub_phone_search)';
                    }
                    if (!empty($where)) {
                        $query = $db->query("SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_opportunities WHERE " . implode(' OR ', $where) . " ORDER BY updatetime DESC");
                        while ($oppotunities = $query->fetch()) {
                            $check_current_oppotunities = check_current_oppotunities($oppotunities);
                            $hot_lead = $check_current_oppotunities['hotting'];
                            if ($hot_lead) {
                                // nếu là cơ hội nóng thì trả về thông tin cơ hội đó luôn
                                $this->result->setSuccess()->set('data', $oppotunities);
                                return $this->result->getResult();
                            }
                        }

                        $query = $db->query("SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_leads WHERE " . implode(' OR ', $where) . " ORDER BY updatetime DESC");
                        while ($leads = $query->fetch()) {
                            $check_current_lead = check_current_lead($leads);
                            $hot_lead = $check_current_lead['hotting'];
                            if ($hot_lead) {
                                // nếu là cơ hội nóng thì trả về thông tin lead đó luôn
                                $this->result->setSuccess()->set('data', $leads);
                                return $this->result->getResult();
                            }
                        }
                    }

                    // k xác định dc lead nào nóng
                    return $this->result->setError()
                        ->setCode('3001')
                        ->setMessage($nv_Lang->getModule('error_not_hotlead'))
                        ->getResult();
                } else {
                    return $this->result->setError()
                        ->setCode('3000')
                        ->setMessage($nv_Lang->getModule('error_users_not_exits'))
                        ->getResult();
                }
            } catch (PDOException $e) {
                return $this->result->setError()
                    ->setCode('2000')
                    ->setMessage(print_r($e, true))
                    ->getResult();
            }
        }
        return $this->result->getResult();
    }
}
