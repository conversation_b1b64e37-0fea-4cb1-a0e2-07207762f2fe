<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * author VINADES.,JSC <<EMAIL>>
 * copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

use ICal\ICal;
if (!defined('NV_IS_FILE_ADMIN')) {
    exit('Stop!!!');
}
$page_title = $nv_Lang->getModule('import_ical');

$error = [];
$array = [
    'calendar_id' => 0
];

$db->sqlreset()->select('id, name_calendar')->from(NV_PREFIXLANG . '_' . $module_data);
$result = $db->query($db->sql());
$arr_calendar = [];
while ($row = $result->fetch()) {
    $arr_calendar[$row['id']] = $row;
}

if ($nv_Request->get_title('save', 'post,get', '') === NV_CHECK_SESSION) {
    $array['calendar_id'] = $nv_Request->get_int('calendar_id', 'post', 0);
    $array['truncate_data'] = (int) $nv_Request->get_bool('truncate_data', 'post', false);

    if ($array['calendar_id'] <= 0) {
        $error[] = $nv_Lang->getModule('error_required_calendar');
    }

    if (isset($_FILES['import_file']) && is_uploaded_file($_FILES['import_file']['tmp_name'])) {
        $upload = new NukeViet\Files\Upload(['text'], $global_config['forbid_extensions'], $global_config['forbid_mimes'], $global_config['nv_max_size'], NV_MAX_WIDTH, NV_MAX_HEIGHT);
        $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
        $upload_info = $upload->save_file($_FILES['import_file'], NV_ROOTDIR . '/' . NV_TEMP_DIR, false);

        if (!empty($upload_info['error'])) {
            $error[] = $upload_info['error'];
        } else {
            $array['import_file'] = $upload_info['name'];
        }
    } else {
        $array['import_file'] = '';
    }

    if (empty($error)) {
        $ical = new ICal($upload_info['name']);
        $events = $ical->events();

        if ($array['truncate_data']) {
            $db->query('DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_event');
        }

        foreach ($events as $event) {
            $date_event = strtotime($event->dtstart);
            $time_event_start = timetoSecond('00:00:00');
            $time_event_end = timetoSecond('23:59:59');     
            $title = $event->summary . ' - ' . nv_date('Y', $date_event);
            $description = isset($event->description) ? explode("\n", $event->description)[0] : '';

            // Kiểm tra xem có sự kiện trùng lặp hay không
            $sql_check_event = "SELECT date_event, repeat_until_date FROM " . NV_PREFIXLANG . "_" . $module_data . "_event WHERE title = :title";
            $stmt_check_event = $db->prepare($sql_check_event);
            $stmt_check_event->bindParam(':title', $title, PDO::PARAM_STR);
            $stmt_check_event->execute();
            $events_existing = $stmt_check_event->fetchAll(PDO::FETCH_ASSOC);

            if (count($events_existing) > 0) {
                // Có sự kiện trùng lặp, tìm ngày bắt đầu sớm nhất và ngày kết thúc muộn nhất
                $min_date = $date_event;
                $max_date = $date_event;

                foreach ($events_existing as $event_existing) {
                    if ($event_existing['date_event'] < $min_date) {
                        $min_date = $event_existing['date_event'];
                    }
                    if ($event_existing['repeat_until_date'] > $max_date) {
                        $max_date = $event_existing['repeat_until_date'];
                    }
                }

                // Cập nhật sự kiện trùng lặp với ngày bắt đầu sớm nhất, ngày kết thúc muộn nhất, và đặt repeat_type = 1
                $sql_update_event = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_event SET date_event = :date_event, repeat_until_date = :repeat_until_date, repeat_type = 1 WHERE title = :title";
                $stmt_update_event = $db->prepare($sql_update_event);
                $stmt_update_event->bindParam(':date_event', $min_date, PDO::PARAM_INT);
                $stmt_update_event->bindParam(':repeat_until_date', $max_date, PDO::PARAM_INT);
                $stmt_update_event->bindParam(':title', $title, PDO::PARAM_STR);
                $stmt_update_event->execute();
            } else {
                // Không có sự kiện trùng lặp, chèn sự kiện mới vào cơ sở dữ liệu
                $sql = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_event (calendar_id, date_event, time_event_start, time_event_end, title, description, repeat_type, repeat_until_date, add_time, edit_time, status) VALUES (:calendar_id, :date_event, :time_event_start, :time_event_end, :title, :description, 0, 0, :add_time, :edit_time, 1)";
                $stmt = $db->prepare($sql);
                $stmt->bindParam(':calendar_id', $array['calendar_id'], PDO::PARAM_INT);
                $stmt->bindParam(':date_event', $date_event, PDO::PARAM_INT);
                $stmt->bindParam(':time_event_start', $time_event_start, PDO::PARAM_INT);
                $stmt->bindParam(':time_event_end', $time_event_end, PDO::PARAM_INT);
                $stmt->bindParam(':title', $title, PDO::PARAM_STR);
                $stmt->bindParam(':description', $description, PDO::PARAM_STR);
                $stmt->bindValue(':add_time', NV_CURRENTTIME, PDO::PARAM_INT);
                $stmt->bindValue(':edit_time', NV_CURRENTTIME, PDO::PARAM_INT);
                $stmt->execute();
                nv_insert_logs(NV_LANG_DATA, $module_name, 'log_add_event', 'event_id', $db->lastInsertId(), $admin_info['userid']);
            }
        }
        $nv_Cache->delMod($module_name);
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=main');
    }
}

$xtpl = new XTemplate('import-ical.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br>', $error));
    $xtpl->parse('main.error');
}

if (!empty($arr_calendar)) {
    foreach ($arr_calendar as $calendar) {
        $calendar['selected'] = $calendar['id'] == $array['calendar_id'] ? 'selected="selected"' : '';
        $xtpl->assign('CALENDAR', $calendar);
        $xtpl->parse('main.calendar');
    }
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
