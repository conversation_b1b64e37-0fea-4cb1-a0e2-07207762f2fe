<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Api;

use Exception;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class ListModuleConfig implements IApi
{
    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'config';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $module_config, $db_config, $nv_Lang;

        $name = $nv_Request->get_title('name', 'post', '');
        
        if (empty($name)) {
            return $this->result->setError()
                        ->setCode('2001')
                        ->setMessage('Module name is required')
                        ->getResult();
        } elseif (!isset($module_config[$name])) {
            return $this->result->setError()
                        ->setCode('2002')
                        ->setMessage('Module name does not exist')
                        ->getResult();
        }

        $data_config = [];
        $stmt = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_config WHERE lang = "vi" AND module = ' . $db->quote($name));
        while ($temp = $stmt->fetch()) {
            $data_config[$temp['config_name']] = $temp['config_value'];
        }
        
        if (!empty($data_config)) {
            $this->result->setSuccess();
            $this->result->set('data', $data_config);
        } else {
            $this->result->setSuccess()
                ->setCode('4000')
                ->setMessage($nv_Lang->getModule('api_error_400'));
        }
        
        return $this->result->getResult();
    }
}
