<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */

if (!defined('NV_SYSTEM')) {
    die('Stop!!!');
}

define('NV_IS_MOD_CRMBIDDING', true);
require NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

function validate_econtract_row($row_new)
{
    global $nv_Lang;

    $error = '';
    if (empty($row_new['c_name'])) {
        $error = $nv_Lang->getModule('error_empty_c_name');
    } elseif (empty($row_new['tax_code']) && $row_new['customer_type'] == 1) {
        $error = $nv_Lang->getModule('error_empty_tax_code');
    } elseif (taxcodecheck2($row_new['tax_code']) == false && $row_new['customer_type'] == 1) {
        $error = $nv_Lang->getModule('invalid_tax_code');
    } elseif (empty($row_new['cccd']) && $row_new['customer_type'] == 0) {
        $error = $nv_Lang->getModule('error_empty_cccd');
    } elseif (cccdCheck($row_new['cccd']) == false && $row_new['customer_type'] == 0) {
        $error = $nv_Lang->getModule('invalid_cccd');
    } elseif (empty($row_new['phone'])) {
        $error = $nv_Lang->getModule('error_empty_phone');
    } elseif (!phonecheck($row_new['phone'], 0)) {
        $error = $nv_Lang->getModule('error_phone_number');
    } elseif (empty($row_new['c_address'])) {
        $error = $nv_Lang->getModule('error_empty_c_address');
    } elseif (empty($row_new['email'])) {
        $error = $nv_Lang->getModule('error_empty_email');
    } elseif (nv_check_valid_email($row_new['email'])) {
        $error = $nv_Lang->getModule('error_invalid_email');
    } elseif ($row_new['receiver_phone'] != '' && !phonecheck($row_new['receiver_phone'], 0)) {
        $error = $nv_Lang->getModule('error_phone_number');
    }
    return $error;
}
