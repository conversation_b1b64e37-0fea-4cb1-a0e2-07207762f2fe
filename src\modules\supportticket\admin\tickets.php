<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

//Xóa ticket
if ($nv_Request->isset_request('delete_id', 'get') and $nv_Request->isset_request('delete_checkss', 'get')) {
    if (!defined('NV_IS_GODADMIN') && !in_array($admin_info['userid'], $special_admin_array)) {
        die('Stop!!!');
    }
    $id = $nv_Request->get_int('delete_id', 'get');
    $delete_checkss = $nv_Request->get_string('delete_checkss', 'get');
    if ($id > 0 and $delete_checkss == md5($id . NV_CACHE_PREFIX . $client_info['session_id'])) {
        $db->query('UPDATE ' . TB_TICKET_ROW . ' SET delete_time=' . NV_CURRENTTIME . ' WHERE id = ' . $db->quote($id));
        $db->query('UPDATE ' . TB_TICKET_LOG . ' SET delete_time=' . NV_CURRENTTIME . ' WHERE ticket_id = ' . $db->quote($id));
        $nv_Cache->delMod($module_name);
        nv_insert_logs(NV_LANG_DATA, $module_name, 'Delete Tickets', 'ID: ' . $id, $admin_info['userid']);
        nv_jsonOutput([
            'success' => true,
            'ticket_id' => $id,
        ]);
    }
}

//Khôi phục ticket
if ($nv_Request->isset_request('restore_id', 'get') and $nv_Request->isset_request('restore_checkss', 'get')) {
    if (!defined('NV_IS_GODADMIN') && !in_array($admin_info['userid'], $special_admin_array)) {
        die('Stop!!!');
    }
    $id = $nv_Request->get_int('restore_id', 'get');
    $restore_checkss = $nv_Request->get_string('restore_checkss', 'get');
    if ($id > 0 and $restore_checkss == md5($id . NV_CACHE_PREFIX . $client_info['session_id'])) {
        $db->query('UPDATE ' . TB_TICKET_ROW . ' SET delete_time=0 WHERE id = ' . $db->quote($id));
        $db->query('UPDATE ' . TB_TICKET_LOG . ' SET delete_time=0 WHERE ticket_id = ' . $db->quote($id));
        $nv_Cache->delMod($module_name);
        nv_insert_logs(NV_LANG_DATA, $module_name, 'Restore Tickets', 'ID: ' . $id, $admin_info['userid']);
        nv_jsonOutput([
            'success' => true,
            'ticket_id' => $id,
        ]);
    }
}

$error = [];
$array_search = [];
$where = [];

$array_search['type'] = $nv_Request->get_title('type', 'post,get', 'new');
$array_search['q'] = $nv_Request->get_title('q', 'post,get');
$array_search['cat'] = $nv_Request->get_int('cat', 'post,get', 0);
$array_search['label'] = $nv_Request->get_int('label', 'post,get', 0);
$array_search['add_user'] = $nv_Request->get_int('add_user', 'post,get', 0);
$array_search['assignee'] = $nv_Request->get_int('assignee', 'post,get', 0);
$array_search['status'] = $nv_Request->get_int('status', 'post,get', 0);
$array_search['vip_status'] = $nv_Request->get_int('vip_status', 'post,get', 0);
$array_search['time_type'] = $nv_Request->get_int('time_type', 'post,get', 0);
$array_search['time_from'] = $nv_Request->get_title('time_from', 'post,get', '');
$array_search['time_to'] = $nv_Request->get_title('time_to', 'post,get', '');
$array_search['customer'] = $nv_Request->get_int('customer', 'post,get', 0);
$array_search['search_expanded'] = $nv_Request->get_title('search_expanded', 'post,get', 'fasle');
$sfrom = nv_d2u_get($array_search['time_from'], 0, 0, 0);;
$sto = nv_d2u_get($array_search['time_to'], 23, 59, 59);

$page_url = $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
$base_url .= '&amp;type=' . $array_search['type'];

if ($array_search['type'] == 'done') {
    $where['AND'][] = [
        '=' => [
            'status' => TicketStatus::Close->value
        ]
    ];
    $where['AND'][] = [
        '=' => [
            'delete_time' => 0
        ]
    ];
} else if ($array_search['type'] == 'process') {
    $where['AND'][] = [
        'IN' => [
            'status' => '(' . TicketStatus::Done->value .',' . TicketStatus::RefuseToAddPoint->value . ')'
        ]
    ];
    $where['AND'][] = [
        '=' => [
            'delete_time' => 0
        ]
    ];
} else if ($array_search['type'] == 'deleted') {
    $where['AND'][] = [
        '>' => [
            'status' => 0 // Y/C đã tạm xóa
        ]
    ];
    $where['AND'][] = [
        '>' => [
            'delete_time' => 0
        ]
    ];
} else {
    $where['AND'][] = [
        'NOT IN' => [
            'status' => '(0, ' . TicketStatus::Close->value . ',' . TicketStatus::Done->value . ',' . TicketStatus::Draft->value . ')'
        ]
    ];
    $where['AND'][] = [
        '=' => [
            'delete_time' => 0
        ]
    ];
}

if (!empty($array_search['q'])) {
    $_q = $db->dblikeescape($array_search['q']);
    $base_url .= '&q=' . $array_search['q'];
    // Tìm kiếm theo id
    if (preg_match('/^#\d+$/', $_q)) {
        $where['AND'][] = [
            '=' => [
                'id' => ltrim($_q, '#')
            ]
        ];
    } else {
        $where['AND'][] = [
                'like' => [
                    'title' => '%' . $_q . '%'
                ]
            ];
    }
}

if (!empty($array_search['search_expanded'])) {
    $base_url .= '&search_expanded=' . $array_search['search_expanded'];
}

// Tìm và chọn một khách hàng theo: name, username, email, sđt, mã số thuế
if ($nv_Request->get_title('ajax_get_customer', 'post', '') === NV_CHECK_SESSION) {
    $respon = [
        'results' => [],
        'pagination' => [
            'more' => false
        ]
    ];

    $q = $nv_Request->get_title('q', 'post', '');
    $page = $nv_Request->get_absint('page', 'post', 1);
    $lang_all = $nv_Request->get_absint('all', 'post', 0);
    if (!empty($q) && strlen($q) >= 3) {
        $fullname = $global_config['name_show'] == 0 ? "CONCAT(tb1.last_name, ' ', tb1.first_name)" : "CONCAT(tb1.first_name, ' ', tb1.last_name)";
        $dbkey = $db->dblikeescape($q);

        if (ctype_digit($dbkey)) {
            $query = 'SELECT tb1.userid, ' . $fullname . ' AS full_name, tb1.username
                FROM ' . NV_USERS_GLOBALTABLE . ' tb1
                WHERE tb1.username = ' . $db->quote($dbkey) . '
                UNION ALL
                SELECT tb1.userid, ' . $fullname . ' AS full_name, tb1.username
                FROM ' . NV_USERS_GLOBALTABLE . ' tb1
                LEFT JOIN ' . NV_USERS_GLOBALTABLE . '_info tb2 ON tb1.userid = tb2.userid
                WHERE tb2.phone = ' . $db->quote($dbkey) . '
                UNION ALL
                SELECT tb1.userid, ' . $fullname . ' AS full_name, tb1.username
                FROM ' . NV_USERS_GLOBALTABLE . ' tb1
                LEFT JOIN ' . NV_USERS_GLOBALTABLE . '_info tb2 ON tb1.userid = tb2.userid
                WHERE tb2.mst = ' . $db->quote($dbkey) . '
                ORDER BY userid ASC LIMIT 20';
        } elseif (empty(nv_check_valid_email($dbkey))) {
            $query = 'SELECT tb1.userid, ' . $fullname . ' AS full_name, tb1.username
                FROM ' . NV_USERS_GLOBALTABLE . ' tb1
                WHERE tb1.email = ' . $db->quote($dbkey) . '
                ORDER BY tb1.userid ASC LIMIT 1';
        } else {
            $query = 'SELECT tb1.userid, ' . $fullname . ' AS full_name, tb1.username
                FROM ' . NV_USERS_GLOBALTABLE . ' tb1
                WHERE tb1.username LIKE \'' . $dbkey . '%\'
                ORDER BY userid ASC LIMIT 50';
        }

        $result = $db->query($query);
        while ($row = $result->fetch()) {
            $respon['results'][] = [
                'id' => $row['userid'],
                'text' => $row['full_name'] . ' (' . $row['username'] . ')'
            ];
        }
    }
    nv_jsonOutput($respon);
}

if ($array_search['customer'] > 0) {
    $where['AND'][] = [
        '=' => [
            'customer_id' => $array_search['customer']
        ]
    ];
}

if ($array_search['cat'] > 0) {
    $base_url .= '&cat=' . $array_search['cat'];
    $where['AND'][] = [
        '=' => [
            'cat_id' => $array_search['cat']
        ]
    ];
}

if ($array_search['label'] > 0) {
    $base_url .= '&label=' . $array_search['label'];
    $where['AND'][] = [
        'FIND_IN_SET' => [
            'label_ids' => $array_search['label']
        ]
    ];
}

// Tìm kiếm theo nhân viên tạo ticket
if ($array_search['add_user'] > 0) {
    $base_url .= '&add_user=' . $array_search['add_user'];
    $where['AND'][] = [
        '=' => [
            'add_userid' => $array_search['add_user']
        ]
    ];
}

if ($array_search['assignee'] > 0) {
    $base_url .= '&assignee=' . $array_search['assignee'];
    $where['AND'][] = [
        'FIND_IN_SET' => [
            'assignee_to' => $array_search['assignee']
        ]
    ];
}

if ($array_search['status'] > 0 && in_array($array_search['status'], $array_ticket_status)) {
    $base_url .= '&status=' . $array_search['status'];
    $where['AND'][] = [
        '=' => [
            'status' => $array_search['status']
        ]
    ];
}

// if ($array_search['vip_status'] > 0) {
//     $base_url .= '&vip_status=' . $array_search['vip_status'];
//     if ($array_search['vip_status'] == 1) {
//         $where['AND'][] = [
//             '>' => [
//                 'vip_id' => 0
//             ]
//         ];
//     } else {
//         $where['AND'][] = [
//             '=' => [
//                 'vip_id' => 0
//             ]
//         ];
//     }
// }

if ($sfrom > 0) {
    $base_url .= '&amp;time_from=' . urlencode($array_search['time_from']);
    if ($array_search['time_type'] == 1) {
        $where['AND'][] = [
            '>=' => [
                'edit_time' => $sfrom
            ]
        ];
    } else {
        $where['AND'][] = [
            '>=' => [
                'add_time' => $sfrom
            ]
        ];
    }
}

if ($sto > 0) {
    $base_url .= '&amp;time_to=' . urlencode($array_search['time_to']);
    if ($array_search['time_type'] == 1) {
        $where['AND'][] = [
            '<=' => [
                'edit_time' => $sto
            ]
        ];
    } else {
        $where['AND'][] = [
            '<=' => [
                'add_time' => $sto
            ]
        ];
    }
}

/**
 * Nếu không phải spadmin thì
 * - chỉ xem được danh sách của bản thân đang xử lý, do mình tạo
 * - thuộc bộ phận của mình
 * Tab hiển thị
 * ## 1/6/24 Trí sửa hiển thị danh sách
 * - new (Yc chờ phản hồi): Những ticket được khách hàng gửi đi xử lý và có yêu cầu bổ sung (ticket paid)
 * - process (YC đã phản hồi): Những ticket đã được Admin/Chuyên Gia/Ai phản hồi
 * - done (YC đã đóng ): Những ticket đã đóng
 */
if (!defined('NV_IS_SPADMIN') && !in_array($admin_info['userid'], $special_admin_array)) {
    $where['AND_OR'][] = [
        'FIND_IN_SET' => [
            'assignee_to' => $admin_info['userid']
        ]
    ];
    $where['AND_OR'][] = [
        'FIND_IN_SET' => [
            'add_userid' => $admin_info['userid']
        ]
    ];
    //Nếu là trưởng nhóm sale thì xem đc tất cả ticket của thành viên tạo hoặc đang chăm
    $members = get_member_by_leader($admin_info['userid']);
    if (!empty($members)) {
        foreach ($members as $member) {
            $where['AND_OR'][] = [
                'FIND_IN_SET' => [
                    'assignee_to' => $member
                ]
            ];
            $where['AND_OR'][] = [
                'FIND_IN_SET' => [
                    'add_userid' => $member
                ]
            ];
        }
    }
    // Xem các ticket mới: thuộc bộ phận của mình và chưa có người xử lý
    $allow_view = get_cat_by_userid();
    if (!empty($allow_view)) {
        $where['AND_OR'][] = [
            'IN' => [
                'cat_id' => '(' . implode(',', $allow_view) . ')'
            ]
        ];
    }
}
// Fetch Limit
$show_view = false;
if (!$nv_Request->isset_request('id', 'post,get')) {
    $show_view = true;
    $per_page = 20;
    $page = $nv_Request->get_int('page', 'post,get', 1);

    $params = [
        'page' => $page,
        'perpage' => $per_page
    ];

    // Nếu có điều kiện where thì gán
    if (!empty($where)) {
        $params['where'] = $where;
    }

    // GỌI API
    $List = nv_local_api('ListAllTicket', $params, $admin_info['username'], $module_name);
    $ListAllTicket = json_decode($List, true);
}

$xtpl = new XTemplate('tickets.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('LINK_ADD', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=ticket_add');
$xtpl->assign('SEARCH', $array_search);
$xtpl->assign('URL_NEW', $page_url . '&amp;type=new');
$xtpl->assign('URL_PROCESS', $page_url . '&amp;type=process');
$xtpl->assign('URL_DONE', $page_url . '&amp;type=done');
$xtpl->assign('URL_DELETED', $page_url .'&amp;type=deleted');
$xtpl->assign('URL', $base_url);
$xtpl->assign('SEARCH_EXPANDED', $array_search['search_expanded']);

$xtpl->assign('edit_time_selected', $array_search['time_type'] == 1 ? 'selected' : '');
$xtpl->assign('add_time_selected', $array_search['time_type'] == 2 ? 'selected' : '');

if ($array_search['type'] == 'new') {
    $xtpl->assign('TAB_NEW', 'active');
    $xtpl->assign('TAB_PROCESS', '');
    $xtpl->assign('TAB_DONE', '');
    $xtpl->assign('TAB_DELETED', '');
} elseif($array_search['type'] == 'process') {
    $xtpl->assign('TAB_NEW', '');
    $xtpl->assign('TAB_PROCESS', 'active');
    $xtpl->assign('TAB_DONE', '');
    $xtpl->assign('TAB_DELETED', '');
} elseif($array_search['type'] == 'deleted') {
    $xtpl->assign('TAB_NEW', '');
    $xtpl->assign('TAB_PROCESS', '');
    $xtpl->assign('TAB_DONE', '');
    $xtpl->assign('TAB_DELETED', 'active');
} else {
    $xtpl->assign('TAB_NEW', '');
    $xtpl->assign('TAB_PROCESS', '');
    $xtpl->assign('TAB_DONE', 'active');
    $xtpl->assign('TAB_DELETED', '');
}

if ($show_view) {
    if ($ListAllTicket['status']  == 'success' and !empty($ListAllTicket['data'])) {
        $generate_page = nv_generate_page($base_url, $ListAllTicket['total'], $ListAllTicket['perpage'], $ListAllTicket['page']);
        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.view.generate_page');
        }
        $number = $ListAllTicket['page'] > 1 ? ($ListAllTicket['perpage'] * ($ListAllTicket['page'] - 1)) + 1 : 1;
        foreach ($ListAllTicket['data'] as $key => $view) {
            $view['number'] = $number++;
            $view['link_edit'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=ticket_add&amp;id=' . $view['id'];
            $view['add_time'] = nv_date('H:i d/m/Y', $view['add_time']);
            $view['edit_time'] = $view['edit_time'] > 0 ? nv_date('H:i d/m/Y', $view['edit_time']) : '';
            if (!empty($view['assignee_to'])) {
                $assignee_fullname = [];
                foreach ($view['assignee'] as $assignee) {
                    if (!empty($assignee['fullname'])) {
                        $assignee_fullname[] = $assignee['fullname'];
                    }
                }
                $view['assignee'] = implode(', ', $assignee_fullname);
            } else {
                $view['assignee'] = '';
            }
            if (!empty($view['label_ids'])) {
                foreach ($view['label'] as $label) {
                    $label['title'] = $label['title_' . NV_LANG_DATA];
                    $label['color'] = $label['color'];
                    $xtpl->assign('LABEL', $label);
                    $xtpl->parse('main.view.loop.label');
                }
            }
            if (defined('NV_IS_GODADMIN') || in_array($admin_info['userid'], $special_admin_array)) {
                if ($array_search['type'] == 'deleted') {
                    $xtpl->assign('LINK_RESTORE', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;restore_id=' . $view['id'] . '&amp;restore_checkss=' . md5($view['id'] . NV_CACHE_PREFIX . $client_info['session_id']));
                    $xtpl->parse('main.view.loop.can_restore');
                } else {
                    $xtpl->assign('LINK_DELETE', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;delete_id=' . $view['id'] . '&amp;delete_checkss=' . md5($view['id'] . NV_CACHE_PREFIX . $client_info['session_id']));
                    $xtpl->parse('main.view.loop.can_delete');
                }
            }
            $xtpl->assign('VIEW', $view);
            $xtpl->parse('main.view.loop');
        }
        $xtpl->parse('main.view');
    }
}

foreach ($array_ticket_cats as $value) {
    $xtpl->assign('OPTION', [
        'key' => $value['cat_id'],
        'title' => NV_LANG_DATA == 'vi' ? $value['title_vi'] : $value['title_en'],
        'selected' => ($value['cat_id'] == $array_search['cat']) ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.select_cat_id');
}

foreach ($array_ticket_labels as $value) {
    $xtpl->assign('OPTION', [
        'key' => $value['label_id'],
        'title' => NV_LANG_DATA == 'vi' ? $value['title_vi'] : $value['title_en'],
        'selected' => $value['label_id'] == $array_search['label'] ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.select_label_ids');
}

foreach ($array_admin_listall as $admin_user) {
    $fullname = nv_show_name_user($admin_user['first_name'], $admin_user['last_name'], $admin_user['username']);
    $optionData = [
        'key' => $admin_user['userid'],
        'title' => $admin_user['username'] . ' (' . $fullname . ')',
    ];

    $optionData['selected'] = $admin_user['userid'] == $array_search['assignee'] ? ' selected="selected"' : '';
    $xtpl->assign('OPTION_ASSIGN', $optionData);
    $xtpl->parse('main.assignee_user.select_assignee');

    $optionData['selected_add'] = $admin_user['userid'] == $array_search['add_user'] ? ' selected="selected"' : '';
    $xtpl->assign('OPTION_ADD', $optionData);
    $xtpl->parse('main.select_add_user');
}
$xtpl->parse('main.assignee_user');

// Lấy và xuất ra khách hàng
if (!empty($array_search['customer'])) {
    $fullname = $global_config['name_show'] == 0 ? "concat(last_name,' ',first_name)" : "concat(first_name,' ',last_name)";

    $sql = "SELECT userid, " . $fullname . " full_name, username FROM " . NV_USERS_GLOBALTABLE . " WHERE userid=" . $array_search['customer'];
    $rows = $db->query($sql)->fetchAll();
    foreach ($rows as $row) {
        $row['title'] = $row['full_name'] . ' (' . $row['username'] . ')';
        $xtpl->assign('CUSTOMER', $row);
        $xtpl->parse('main.select_customer');
    }
}

if ($array_search['type'] != 'done') {
    if ($array_search['type'] == 'new') {
        $array_ticket_status = [TicketStatus::Open->value, TicketStatus::Process->value];
    } elseif ($array_search['type'] == 'process') {
        $array_ticket_status = [TicketStatus::Done->value, TicketStatus::RefuseToAddPoint->value];
    }
    foreach ($array_ticket_status as $key) {
        $xtpl->assign('OPTION', [
            'key' => $key,
            'title' => TicketStatus::tryFrom($key)->getLabel(),
            'selected' => $key == $array_search['status'] ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.type.select_status');
    }
    $xtpl->parse('main.type');
}

if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

if (defined('NV_IS_GODADMIN') || in_array($admin_info['userid'], $special_admin_array)) {
    $xtpl->parse('main.can_delete');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('tickets');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
