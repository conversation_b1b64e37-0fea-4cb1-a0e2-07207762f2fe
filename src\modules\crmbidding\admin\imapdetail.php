<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:39:33 GMT
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Module\crmbidding\Log;
use NukeViet\Module\crmbidding\LogRow;
use NukeViet\Module\crmbidding\ImapMail;
use NukeViet\Module\crmbidding\Opportunity;

$page_title = $nv_Lang->getModule('imaplist_process');
$id = $nv_Request->get_absint('id', 'get,post', 0);
$url_back = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=imaplist';

// Lấy email chưa bị xóa
$sql = "SELECT tb1.*, tb2.text_plain FROM " . NV_PREFIXLANG . "_" . $module_data . "_emails tb1
INNER JOIN " . NV_PREFIXLANG . "_" . $module_data . "_emails_detail tb2 ON tb1.email_id=tb2.email_id
WHERE tb1.email_id=" . $id . ' AND tb1.status!=' . ImapMail::STATUS_DELETED;
$email = $db->query($sql)->fetch();
if (empty($email)) {
    nv_redirect_location($url_back);
}

// Kiểm tra quyền sửa
$error = '';
if (in_array($email['status'], [
    ImapMail::STATUS_EXCLUDE,
    ImapMail::STATUS_CONVERT_TO_OPPORTUNITY
])) {
    $error = sprintf($nv_Lang->getModule('imaplist_info4'), $nv_Lang->getModule('imaplist_status' . $email['status']), $url_back);

    // Xem cơ hội
    if ((defined('NV_IS_SPADMIN') or $email['assign_to'] == $admin_info['admin_id']) and !empty($email['to_opportunitie'])) {
        $url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=opportunities_info&id=' . $email['to_opportunitie'] . '&showheader=1';
        $error .= sprintf($nv_Lang->getModule('imaplist_info5'), $url);
    }
} elseif (!empty($email['assign_to']) and $email['assign_to'] != $admin_info['admin_id']) {
    // Đã giao cho người khác
    $error = sprintf($nv_Lang->getModule('imaplist_info6'), $url_back);
}

if (empty($error)) {
    // Kiểm tra có đang xử lý các email khác không
    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_emails WHERE email_id!=" . $id . ' AND status=' . ImapMail::STATUS_PROCESSING . "
    AND assign_to=" . $admin_info['admin_id'];
    $email_other = $db->query($sql)->fetch();

    if (!empty($email_other)) {
        $error = sprintf($nv_Lang->getModule('imaplist_info7'), $url_back);
    }
}

if (!empty($error)) {
    $contents = nv_theme_alert($nv_Lang->getModule('info'), $error, 'danger');
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_admin_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

// Cập nhật trạng thái đang sửa
if ($email['status'] == ImapMail::STATUS_NEW) {
    $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_emails SET
        assign_to=" . $admin_info['admin_id'] . ",
        status=" . ImapMail::STATUS_PROCESSING . ",
        status_time=" . NV_CURRENTTIME . "
    WHERE email_id=" . $id;
    $db->query($sql);
}

// Loại bỏ email
if ($nv_Request->get_title('emailexclude', 'post', '') === NV_CHECK_SESSION) {
    if (!defined('NV_IS_AJAX')) {
        die('Wrong URL!!!');
    }

    $respon = [
        'title' => $nv_Lang->getModule('error'),
        'text' => '',
        'classcolor' => 'danger',
        'success' => false
    ];

    $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_emails SET
        status=" . ImapMail::STATUS_EXCLUDE . ",
        status_time=" . NV_CURRENTTIME . "
    WHERE email_id=" . $email['email_id'];
    $db->query($sql);

    $respon['title'] = $nv_Lang->getModule('success');
    $respon['success'] = true;
    $respon['classcolor'] = 'success';
    $respon['text'] = $nv_Lang->getModule('success');

    nv_insert_logs(NV_LANG_DATA, $module_name, 'EMAIL_EXCLUDE', json_encode($email), $admin_info['userid']);
    nv_jsonOutput($respon);
}

// Chuyển thành cơ hội
if ($nv_Request->get_title('emailtoopportunity', 'post', '') === NV_CHECK_SESSION) {
    if (!defined('NV_IS_AJAX')) {
        die('Wrong URL!!!');
    }

    $respon = [
        'title' => $nv_Lang->getModule('error'),
        'text' => '',
        'classcolor' => 'danger',
        'success' => false
    ];

    // Tạo cơ hội
    $name = $email['from_name'];
    if (empty($name)) {
        $name = $email['from_host'];
    }
    if (empty($name)) {
        $name = $email['from_address'];
    }
    if (empty($name)) {
        $name = $email['sender_name'];
    }
    if (empty($name)) {
        $name = $email['sender_host'];
    }
    if (empty($name)) {
        $name = $email['sender_address'];
    }
    $opportunity = new Opportunity([
        'name' => $name,
        'email' => $email['from_address'],
        'phone' => '',
        'status' => 1,
        'caregiver_id' => $admin_info['admin_id'],
        'timecreate' => NV_CURRENTTIME,
        'updatetime' => NV_CURRENTTIME,
        'active' => 1
    ]);
    $opportunity_id = $opportunity->save();

    $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_emails SET
        status=" . ImapMail::STATUS_CONVERT_TO_OPPORTUNITY . ",
        to_opportunitie=" . $opportunity_id . ",
        status_time=" . NV_CURRENTTIME . "
    WHERE email_id=" . $email['email_id'];
    $db->query($sql);

    $respon['title'] = $nv_Lang->getModule('success');
    $respon['success'] = true;
    $respon['classcolor'] = 'success';
    $respon['text'] = $nv_Lang->getModule('success');
    $respon['redirect'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=opportunities_info&id=' . $opportunity_id . '&showheader=1';

    // Log chuyển email thành cơ hội kinh doanh
    $log = new Log($nv_Lang->getModule('imaplist_tooop_log'));
    $logRow = new LogRow();
    $logRow->setMessage($email['subject']);
    $log->add($logRow);
    $logRow->setMessage($nv_Lang->getModule('log_sendto_time'), $email['mail_date'] ? nv_date('H:i d/m/Y', $email['mail_date']) : 'N/A');
    $log->add($logRow);

    $log = $log->toString();
    $sql = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_alllogs (
        userid, log_area, log_key, log_time, log_data, leads_id, oppotunities_id
    ) VALUES (
        " . $admin_info['admin_id'] . ", 1, 'EMAIL_TO_OPPORTUNITY', " . NV_CURRENTTIME . ",
       " . $db->quote($log) . ", 0, " . $opportunity_id . "
    )";
    $db->query($sql);

    nv_insert_logs(NV_LANG_DATA, $module_name, 'EMAIL_TO_OPPORTUNITY', json_encode($email) . '|' . $opportunity_id, $admin_info['userid']);
    nv_jsonOutput($respon);
}

$xtpl = new XTemplate('imapdetail.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('TOKEND', NV_CHECK_SESSION);
$xtpl->assign('URL_BACK', $url_back);
$xtpl->assign('LINK_WORK', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);

$email['has_attachments'] = empty($email['has_attachments']) ? $nv_Lang->getModule('no') : $nv_Lang->getModule('yes');
$email['from_name'] = empty($email['from_name']) ? nv_EncodeEmail($email['from_address']) : ($email['from_name'] . ' &lt;' . nv_EncodeEmail($email['from_address']) . '&gt;');
$email['sender_name'] = empty($email['sender_name']) ? nv_EncodeEmail($email['sender_address']) : ($email['sender_name'] . ' &lt;' . nv_EncodeEmail($email['sender_address']) . '&gt;');
$email['text_plain'] = nv_nl2br($email['text_plain']);

$keys = ['send_to', 'send_cc', 'send_bcc'];
foreach ($keys as $key) {
    $value = explode(',', $email[$key]);
    $email[$key] = [];
    foreach ($value as $value_i) {
        if (!empty($value_i)) {
            $email[$key][] = nv_EncodeEmail($value_i);
        }
    }
    $email[$key] = implode(', ', $email[$key]);
}

$email['reply_to'] = empty($email['reply_to']) ? [] : json_decode($email['reply_to'], true);
$reply_to = [];
foreach ($email['reply_to'] as $k => $v) {
    $reply_to[] = empty($v) ? nv_EncodeEmail($k) : ($v . ' &lt;' . nv_EncodeEmail($k) . '&gt;');
}
$email['reply_to'] = implode('<br />', $reply_to);

$xtpl->assign('EMAIL', $email);

// Chuyển thành cơ hội kinh doanh nếu chưa chuyển
if (empty($email['to_opportunitie'])) {
    $xtpl->parse('main.to_opportunitie');
} else {
    $url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=opportunities_info&id=' . $email['to_opportunitie'] . '&showheader=1';
    $xtpl->assign('VIEW_OPPORTUNITIE', sprintf($nv_Lang->getModule('imaplist_view_opportunitie'), $url));
    $xtpl->parse('main.view_opportunitie');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
