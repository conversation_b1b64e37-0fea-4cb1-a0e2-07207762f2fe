<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\crmbidding;

use Exception;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

/**
 * <AUTHOR>
 *
 */
class LogRow
{
    /**
     * @var NULL|string|array
     */
    private $array;

    /**
     * @param string|number $message0
     * @param string|number $message1
     * @throws Exception
     * @return \NukeViet\Module\crmbidding\LogRow
     */
    public function setMessage($message0, $message1 = '')
    {
        // Không cho thêm item trống vào
        if (empty($message0)) {
            throw new Exception('Log message can not empty!');
        }
        if (!is_string($message0) and !is_numeric($message0)) {
            throw new Exception('Log message must is string or number!');
        }
        if (!empty($message1) and !is_string($message1) and !is_numeric($message1)) {
            throw new Exception('Log message must is string or number!');
        }

        if (!empty($message1)) {
            $this->array = [$message0, $message1];
            return $this;
        }

        $this->array = $message0;
        return $this;
    }

    /**
     * @param string|number $disp_text text hiển thị
     * @param string $to_module module liên kết đến
     * @param string $to_op op liên kết đến ví dụ detail, detail/alias
     * @param string $query_str có dạng var1=a&var2=b&var3=c...
     * @param number $admin_area 1 nếu liên kết trong module, 2 nếu liên kết ra ngoài site
     * @param string $group_view nhóm show link có dạng 1,2,3,4,5
     * @param number $id_view id user show link
     * @throws Exception
     * @return \NukeViet\Module\crmbidding\LogRow
     */
    public function setLink($disp_text, $to_module, $to_op = '', $query_str = '', $admin_area = 1, $group_view = '3', $id_view = 0)
    {
        if (empty($disp_text)) {
            throw new Exception('Log message can not empty!');
        }
        if (!is_string($disp_text) and !is_numeric($disp_text)) {
            throw new Exception('Log message must is string or number!');
        }
        if (empty($to_module) or !preg_match('/^[a-zA-Z0-9\-\_]+$/', $to_module)) {
            throw new Exception('Module invalid!');
        }
        if (!empty($to_op) and !preg_match('/^[a-zA-Z0-9\-\_\/]+$/', $to_op)) {
            throw new Exception('Op invalid!');
        }

        $this->array = [
            'type' => 'link',
            'text' => $disp_text,
            'module' => $to_module,
            'op' => $to_op,
            'query_str' => $query_str,
            'admin' => $admin_area,
            'group_view' => $group_view,
            'id_view' => $id_view
        ];

        return $this;
    }

    /**
     * @return \NukeViet\Module\crmbidding\LogRow
     */
    public function reset()
    {
        $this->array = null;
        return $this;
    }

    /**
     * @return NULL|string|array
     */
    public function getData()
    {
        return $this->array;
    }
}
