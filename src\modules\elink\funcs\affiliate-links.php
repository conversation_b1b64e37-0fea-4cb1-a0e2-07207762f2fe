<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Apr 20, 2010 10:47:41 AM
 */

if (!defined('NV_IS_MOD_ELINK')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('affiliate_links');
$description = $keywords = 'no';

if (!defined('NV_IS_USER')) {
    $link_redirect = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    nv_redirect_location($link_redirect);
}

$array = [];
$array['affiliate_enabled'] = nv_user_in_groups($global_array_config['affiliate_code_groups']);
$array['promotion_enabled'] = nv_user_in_groups($global_array_config['groups_allowed_string']);

if (!$array['affiliate_enabled'] and !empty($array['promotion_enabled'])) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}

$array_mod_title[] = array(
    'catid' => 0,
    'title' => $nv_Lang->getModule('affiliate_links'),
    'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op
);

$array_search = [];
$array_search['cat'] = $nv_Request->get_int('c', 'get', 0); // 0 tất cả 1 Affiliate 2 Promotion
$array_search['type'] = $nv_Request->get_int('t', 'get', 0); // 0 tất cả 1 đích 2 nguồn
$array_search['promotion_code'] = nv_strtoupper($nv_Request->get_title('p', 'get', '')); // Xem theo mã khuyến mãi
$array_search['site_id'] = $nv_Request->get_absint('s', 'get', 0);
if ($array_search['cat'] < 0 or $array_search['cat'] > 2) {
    $array_search['cat'] = 0;
}
if ($array_search['type'] < 0 or $array_search['type'] > 2) {
    $array_search['type'] = 0;
}
if (!isset($global_array_sites[$array_search['site_id']])) {
    $array_search['site_id'] = 0;
}
// Kiểm tra thống kê theo mã khuyến mãi
$array_promotion_code = [];
if (!empty($array_search['promotion_code'])) {
    $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE userid=" . $user_info['userid'] . " AND promo_code=:promo_code";
    $sth = $db->prepare($sql);
    $sth->bindParam(':promo_code', $array_search['promotion_code'], PDO::PARAM_STR);
    $sth->execute();
    $array_promotion_code = $sth->fetch();

    if (!empty($array_promotion_code)) {
        $array_search['cat'] = 2;
    } else {
        $array_search['promotion_code'] = '';
    }
}

$array['affiliate_code'] = $user_info['username'];

$page = 1;
$per_page = 40;
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
$page = $nv_Request->get_int('page', 'get', 1);
if (isset($array_op[1])) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
}
if ($page > 99999999 or $page < 1) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
}
$link_dissmiss_promotion = $base_url;

// Thống kê theo điều kiện
$where = [];
$where[] = "islink!=''";

if (!empty($array_search['cat'])) {
    $base_url .= '&amp;c=' . $array_search['cat'];
    if ($array_search['cat'] == 1) {
        $where[] = "(stype=0 AND sval=" . $db->quote($array['affiliate_code']) . ")";
    } else {
        $where[] = "(stype=1 AND sval IN(SELECT promo_code FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE userid=" . $user_info['userid'] . "))";
    }
} else {
    $where[]  = "((stype=0 AND sval=" . $db->quote($array['affiliate_code']) . ")
    OR (stype=1 AND sval IN(SELECT promo_code FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE userid=" . $user_info['userid'] . ")))";
}

if (!empty($array_search['type'])) {
    $base_url .= '&amp;t=' . $array_search['type'];
    $link_dissmiss_promotion .= '&amp;t=' . $array_search['type'];
    if ($array_search['type'] == 1) {
        $where[] = 'referornot=0';
    } else {
        $where[] = 'referornot=1';
    }
}
if (!empty($array_search['promotion_code'])) {
    $base_url .= '&amp;p=' . $array_search['promotion_code'];
    $where[] = 'sval=' . $db->quote($array_search['promotion_code']);
}
if (!empty($array_search['site_id'])) {
    $base_url .= '&amp;s=' . $array_search['site_id'];
    $where[] = 'site_id=' . $array_search['site_id'];
}

// Sắp xếp
$array_field_order = [
    'lastupdate' => 'last_update',
    'hits' => 'scount',
];
$sql_order_by = $nv_Request->get_title('ob', 'get', '');
$sql_order_val = strtoupper($nv_Request->get_title('ov', 'get', ''));
$sql_order_base = $base_url;

if (!empty($sql_order_by) and !isset($array_field_order[$sql_order_by])) {
    $sql_order_by = '';
}
if (!empty($sql_order_val) and $sql_order_val != 'ASC' and $sql_order_val != 'DESC') {
    $sql_order_val = '';
}

$db->sqlreset()->from($db_config['prefix'] . "_" . $module_data . "_statistics");
$db->where(implode(" AND ", $where));
$db->select('COUNT(sval)');

$num_items = $db->query($db->sql())->fetchColumn();

$db->limit($per_page)->offset(($page - 1) * $per_page);
$db->select('referornot, slink, sreflink, scount, last_update');

$order_val = empty($sql_order_val) ? 'DESC' : $sql_order_val;
$order_field = empty($sql_order_by) ? current($array_field_order) : $array_field_order[$sql_order_by];
$db->order('last_update ' . $order_val);
if (!empty($sql_order_by)) {
    $base_url .= '&amp;ob=' . $sql_order_by;
    $link_dissmiss_promotion .= '&amp;ob=' . $sql_order_by;
}
if (!empty($sql_order_val)) {
    $base_url .= '&amp;ov=' . strtolower($sql_order_val);
    $link_dissmiss_promotion .= '&amp;ov=' . strtolower($sql_order_val);
}

$result = $db->query($db->sql());

$array['data'] = [];
$stt = (($page - 1) * $per_page);
while ($row = $result->fetch()) {
    if ($row['referornot']) {
        $row['slink'] = $row['sreflink'];
    }
    $row['slink'] = nv_htmlspecialchars($row['slink']);
    $stt++;
    $row['stt'] = $stt;
    $array['data'][] = $row;
}

if ($page > 1 and empty($array['data'])) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
}
if ($page > 1) {
    $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . number_format($page, 0, ',', '.');
}

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
$contents = nv_elink_theme_affiliate_links($array, $generate_page, $num_items, $array_search, $sql_order_by, $sql_order_val, $sql_order_base);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
