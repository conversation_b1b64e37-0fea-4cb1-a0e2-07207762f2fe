<!-- BEGIN: main -->
<div class="col-md-10">
    <div class="panel panel-default" id="customer-panel">
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="row">
                    <h2 class="text-info col-md-8">
                        {LANG.customer_info}
                    </h2>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-12 col-sm-24">
                        <div class="form-group ">
                            <div class="col-sm-6 col-md-8 text-right">{LANG.fullname}</div>
                            <div class="col-sm-18 col-md-16">
                                <strong class="text-primary textinfo">{USER.name}</strong>
                            </div>
                        </div>
                        <div class="form-group ">
                            <div class="col-sm-6 col-md-8 text-right">{LANG.uniqueid}</div>
                            <div class="col-sm-18 col-md-16">
                                <strong class="text-primary textinfo">{USER.uniqueid}</strong>
                            </div>
                        </div>
                        <div class="form-group ">
                            <div class="col-sm-6 col-md-8 text-right">
                                {LANG.phone}
                            </div>
                            <div class="col-sm-18 col-md-16">
                                <strong id="phone" class="text-primary textinfo">{USER.phone}</strong>
                            </div>
                        </div>
                        <div class="form-group ">
                            <div class="col-sm-6 col-md-8 text-right">Email</div>
                            <div class="col-sm-18 col-md-16">
                                <strong id="email" class="text-primary textinfo text-break">{USER.email}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-14">
    <div class="panel panel-default" id="leads-panel">
        <div class="panel-body">
            <div class="form-horizontal">
                <div class="row">
                    <h2 class="text-info col-md-8">
                        {LANG.leads_info}
                    </h2>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-12 col-sm-24">
                        <div class="form-group ">
                            <div class="col-sm-6 col-md-8 text-right">{LANG.status}</div>
                            <div class="col-sm-18 col-md-16">
                                <!-- BEGIN: status_leads -->
                                {STATUS_LEADS}
                                <!-- END: status_leads -->
                            </div>
                        </div>
                        <div class="form-group ">
                            <div class="col-sm-6 col-md-8 text-right">{LANG.leads_trung}</div>
                            <div class="col-sm-18 col-md-16">
                                <!-- BEGIN: link_leads -->
                                <a href="{LINK_LEADS}" target="_blank">{NAME_LEADS}</a>;
                                <!-- END: link_leads -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<form>
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
            <tr>
                <th style="width: 1%" class="text-center">
                    {LANG.number}
                </th>
                <th style="width: 10%" class="text-nowrap">
                    <a href="{ORDER_BY_EMAIL}">{LANG.user_message}</a>
                </th>
                <th style="width: 15%" class="text-nowrap">
                    <a href="{ORDER_BY_EMAIL}">{LANG.ai_message}</a>
                </th>
                <th style="width: 7%" class="text-nowrap text-center">
                    <a href="{ORDER_BY_TIME}">{LANG.thoi_gian}</a>
                </th>
            </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td>{VIEW.number}</td>
                    <td>{VIEW.message_user}</td>
                    <td style="white-space: pre-line">{VIEW.message_ai}</td>
                    <td class="text-center">{VIEW.addtime}</td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
</form>
<script>
    function equalizeHeight() {
        var customerPanel = document.getElementById('customer-panel');
        var leadsPanel = document.getElementById('leads-panel');

        customerPanel.style.height = 'auto';
        leadsPanel.style.height = 'auto';

        var customerHeight = customerPanel.offsetHeight;
        var leadsHeight = leadsPanel.offsetHeight;

        var maxHeight = Math.max(customerHeight, leadsHeight);
        customerPanel.style.height = maxHeight + 'px';
        leadsPanel.style.height = maxHeight + 'px';
    }

    window.addEventListener('load', equalizeHeight);
    window.addEventListener('resize', equalizeHeight);
</script>

<!-- END: main -->
