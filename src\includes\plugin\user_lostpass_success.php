<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

use NukeViet\Dauthau\LangMulti;
use NukeViet\InForm\InForm;

$nv_hook_module = 'users'; // Module xảy ra event chứa data

$callback = function($vars, $from_data, $receive_data) {
    $user_id = $vars[0]['userid'];

    $request = [];
    $request['receiver_ids'] = $user_id;
    $request['isdef'] = 'vi';

    $request['message'] = [
        'vi' => LangMulti::get('vi', 'user_lostpass_success'),
        'en' => LangMulti::get('en', 'user_lostpass_success'),
    ];
    $request['link'] = [
        'vi' => NV_MY_DOMAIN . '/vi/users/',
        'en' => NV_MY_DOMAIN. '/en/users/',
    ];
    InForm::creat($request);
};
nv_add_hook($module_name, 'user_lostpass_success', $priority, $callback, $hook_module, $pid);
