<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    exit('Stop!!!');
}

if ($nv_Request->isset_request('del', 'post, get')) {
    $error = '';
    if (md5(NV_CHECK_SESSION . '_' . $module_name . '_' . $op) == $nv_Request->get_string('checkss', 'post')) {
        $id = $nv_Request->get_int('id', 'post, get', 0);
        $stm = $db->prepare('DELETE FROM ' . $db_config['prefix'] . '_' . $module_data . ' WHERE id = :id');
        $stm->bindParam(':id', $id, PDO::PARAM_INT);
        if (!$stm->execute()) {
            $error = 'Đã có lỗi xảy ra.';
        }
        $nv_Cache->delMod($module_name);
    } else {
        $error = 'Đã có lỗi xảy ra.';
    }
    
    if ($error) {
        nv_htmlOutput('ERROR_' . $error);
    }
    
    nv_htmlOutput('OK');
    
}

$page_title = $table_caption = $nv_Lang->getModule('main');
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_LANG_VARIABLE . '=' . $module_name;
$per_page = 30;
$page = $nv_Request->get_int('page', 'get', 1);
$method = $nv_Request->isset_request('method', 'post') ? $nv_Request->get_string('method', 'post', '') : ($nv_Request->isset_request('method', 'get') ? urldecode($nv_Request->get_string('method', 'get', '')) : '');
$methodvalue = $nv_Request->get_title('value', 'get', '');
$methods = [
    'userid' => [
        'key' => 'userid',
        'sql' => 'userid',
        'value' => $nv_Lang->getModule('search_id'),
        'selected' => ''
    ],
    'username' => [
        'key' => 'username',
        'sql' => 'username',
        'value' => $nv_Lang->getModule('search_account'),
        'selected' => ''
    ],
    'fullname' => [
        'key' => 'fullname',
        'sql' => $global_config['name_show'] == 0 ? "concat(last_name,' ',first_name)" : "concat(first_name,' ',last_name)",
        'value' => $nv_Lang->getModule('search_name'),
        'selected' => ''
    ],
    'email' => [
        'key' => 'email',
        'sql' => 'email',
        'value' => $nv_Lang->getModule('search_mail'),
        'selected' => ''
    ],
];
$_arr_where = [];
if (!empty($methodvalue)) {
    if (!empty($method) && in_array($method, array_keys($methods))) {
        $array_like = [];
        $methods[$method]['selected'] = 'selected="selected"';
        if ($method != 'userid') {
            $array_like[] = "userid IN (SELECT userid FROM " . NV_USERS_GLOBALTABLE . " WHERE " . $methods[$method]['sql'] .  " LIKE '%" . $db->dblikeescape($methodvalue) . "%')";
        } else {
            $array_like[] = $methods[$method]['sql'] . "=" . $db->quote($methodvalue);
        }
        $_arr_where[] = '(' . implode(' OR ', $array_like) . ')';
    }
    $base_url .= '&amp;method=' . urlencode($method) . '&amp;value=' . urlencode($methodvalue);
}

$type_search = $nv_Request->get_title('type', 'get', '');
$list_type = $db->query('SELECT type FROM ' . $db_config['prefix'] . '_' . $module_data . ' GROUP BY type')->fetchAll(PDO::FETCH_COLUMN);
if (!empty($type_search) && in_array($type_search, $list_type)) {
    $_arr_where[] = 'type = ' . $db->quote($type_search);
    $base_url .= '&amp;type=' . $type_search;
}

$active_time_from = $nv_Request->get_string('active_time_from', 'get', '');
$active_time_to = $nv_Request->get_string('active_time_to', 'get', '');
$a_time_from = 0;
$a_time_to = 0;

//active time từ
if(!empty($active_time_from)) {
    if (preg_match('/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/', $active_time_from, $m)) {
        $a_time_from = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
    } else {
        $active_time_from = '';
    }
}
//active time đến
if (!empty($active_time_to)) {
    if (preg_match('/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/', $active_time_to, $m)) {
        $a_time_to = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
    } else {
        $active_time_to = '';
    }
}

if (!empty($a_time_from) && !empty($a_time_to)) {
    $_arr_where[] = 'time_add >= ' . $a_time_from . ' AND time_add <= ' . $a_time_to;
    $base_url .= '&amp;active_time_from=' . $active_time_from;
    $base_url .= '&amp;active_time_to=' . $active_time_to;
}

$sql = 'SELECT COUNT(*) FROM ' . $db_config['prefix'] . '_' . $module_data . (empty($_arr_where) ? '' : ' WHERE ' . implode(' AND ', $_arr_where));
$num_items = $db->query($sql)->fetchColumn();

$sql = 'SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . (empty($_arr_where) ? '' : ' WHERE ' . implode(' AND ', $_arr_where));
$result = $db->query($sql);
$array_data = $array_userid = [];
while ($row = $result->fetch()) {
    $row['time_add'] = date('d/m/Y H:i', $row['time_add']);
    $array_userid[] = $row['userid'];
    $array_data[$row['id']] = $row;
}

if (!empty($array_userid)) {
    $sql = 'SELECT userid, username, first_name, last_name, email FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $array_userid) . ')';
    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        foreach ($array_data as $k => $v) {
            if ($v['userid'] == $row['userid']) {
                $array_data[$k]['username'] = $row['username'];
                $array_data[$k]['fullname'] = nv_show_name_user($row['first_name'], $row['last_name']);
                $array_data[$k]['email'] = $row['email'];
            }
        }
    }
}

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
$head_tds = [];
$head_tds['userid']['title'] = $nv_Lang->getModule('userid');
$head_tds['username']['title'] = $nv_Lang->getModule('account');
$head_tds['full_name']['title'] = $global_config['name_show'] == 0 ? $nv_Lang->getModule('lastname_firstname') : $nv_Lang->getModule('firstname_lastname');
$head_tds['email']['title'] = $nv_Lang->getModule('email');
$head_tds['adddate']['title'] = $nv_Lang->getModule('add_date');
$head_tds['reason']['title'] = $nv_Lang->getModule('reason');
$head_tds['type']['title'] = $nv_Lang->getModule('search_type');

$xtpl = new XTemplate('main.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
// $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('FORM_ACTION', NV_BASE_ADMINURL . 'index.php');
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('SEARCH_VALUE', nv_htmlspecialchars($methodvalue));
$xtpl->assign('TABLE_CAPTION', $table_caption);
$xtpl->assign('HEAD', $head_tds);
$xtpl->assign('CHECKSESS', md5(NV_CHECK_SESSION . '_' . $module_name . '_' . $op));

if (!empty($a_time_from)) {
    $xtpl->assign('active_time_from', $active_time_from);
}
if (!empty($a_time_to)) {
    $xtpl->assign('active_time_to', $active_time_to);
}

// if (defined('NV_IS_USER_FORUM')) {
//     $xtpl->parse('main.is_forum');
// }

foreach ($methods as $m) {
    $xtpl->assign('METHODS', $m);
    $xtpl->parse('main.method');
}
foreach ($list_type as $t) {
    $xtpl->assign('TYPE', $t);
    $xtpl->assign('TYPE_SELECTED', ($t == $type_search ? 'selected="selected"' : ''));
    $xtpl->parse('main.type');
}

foreach ($array_data as $u) {
    $u['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&method=username&value=' . $u['username'] . (empty($type_search) ? '' : '&type=' . $type_search);
    $xtpl->assign('CONTENT_TD', $u);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->parse('main.xusers');
}

if (!empty($generate_page)) {
    $xtpl->assign('GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.footer.generate_page');
    $has_footer = true;
}

if ($has_footer) {
    $xtpl->parse('main.footer');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';