<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use Exception;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdateGlobalConfig implements IApi
{
    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'config';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request;

        $row = [];
        $row['lang'] = $nv_Request->get_title('lang', 'post', '');
        $row['module_name'] = $nv_Request->get_title('module_name', 'post', '');
        $row['config_name'] = $nv_Request->get_title('config_name', 'post', '');
        $row['config_value'] = $nv_Request->get_title('config_value', 'post', '');

        if (empty($row['lang'])) {
            return $this->result->setError()
                        ->setCode('2001')
                        ->setMessage('lang is required')
                        ->getResult();
        } elseif (empty($row['module_name'])) {
            return $this->result->setError()
                        ->setCode('2002')
                        ->setMessage('module_name is required')
                        ->getResult();
        } elseif (empty($row['config_name'])) {
            return $this->result->setError()
                        ->setCode('2003')
                        ->setMessage('config_name is required')
                        ->getResult();
        } elseif ($row['config_value'] == '') {
            return $this->result->setError()
                        ->setCode('2004')
                        ->setMessage('config_value is required')
                        ->getResult();
        }

        $row_old = $db->query('SELECT * FROM ' . NV_CONFIG_GLOBALTABLE . ' WHERE lang=' . $db->quote($row['lang']) . ' AND module=' . $db->quote($row['module_name']) . ' AND config_name=' . $db->quote($row['config_name']))->fetch();
        if (empty($row_old)) {
            return $this->result->setError()
                ->setCode('2005')
                ->setMessage('global config not exist')
                ->getResult();
        }

        try {
            $db->exec("UPDATE " . NV_CONFIG_GLOBALTABLE . " SET config_value = " . $db->quote($row['config_value']) . " WHERE lang = " . $db->quote($row['lang']) . " AND module = " . $db->quote($row['module_name']) . " AND config_name = " . $db->quote($row['config_name']));

            $this->result->setSuccess();
            $this->result->set('config', $row);
        } catch (PDOException $e) {
            $this->result->setError()
                ->setCode('3000')
                ->setMessage(print_r($e, true));
        }

        return $this->result->getResult();
    }
}
