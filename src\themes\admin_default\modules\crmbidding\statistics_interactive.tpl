<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<div class="well">
    <form action="{FORM_ACTION}" method="get">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" /> 
        <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" /> 
        <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
        <div class="row">
            <div class="col-xs-24 col-md-7">
                <div class="form-group">
                    <input class="form-control" type="text" name="email" value="{ARRAY_SEARCH.email}" placeholder="Email" />
                </div>
            </div>
            <div class="form-group col-xs-24 col-md-7">
                <div class="input-group">
                    <input class="form-control" type="text" placeholder="{LANG.stat_from}" id="stat_from" name="stat_from" value="{ARRAY_SEARCH.stat_from}"/>
                    <span class="input-group-btn">
                        <button class="btn btn-default" type="button" id="stat_from_btn">
                            <em class="fa fa-calendar fa-fix">&nbsp;</em>
                        </button>
                    </span>
                </div>
            </div>
            <div class="form-group col-xs-24 col-md-7">
                <div class="input-group">
                    <input class="form-control" type="text" placeholder="{LANG.stat_to}" id="stat_to" name="stat_to" value="{ARRAY_SEARCH.stat_to}"/>
                    <span class="input-group-btn">
                        <button class="btn btn-default" type="button" id="stat_to_btn">
                            <em class="fa fa-calendar fa-fix">&nbsp;</em>
                        </button>
                    </span>
                </div>
            </div>
            <div class="col-xs-12 col-md-3">
                <div class="form-group">
                    <input class="btn btn-primary" type="submit" value="{LANG.stat_submit}" />
                </div>
            </div>
        </div>
    </form>
</div>
<div>
    <p><strong>{LANG.sum_click_count}</strong>: {sum_click_count} </p>
</div>
<div class="table-responsive">
    <table class="table table-striped table-bordered table-hover">
        <thead>
            <tr>
                <th class="w50 text-center">{LANG.number}</th>
                <th class="w150 text-center">{LANG.day_send_msg}</th>
                <th class="w200 text-center">{LANG.taikhoan}</th>
                <th class="text-center">Email</th>
                <th class="w150 text-center">{LANG.click_count}</th>
            </tr>
        </thead>
        <!-- BEGIN: generate_page -->
        <tfoot>
            <tr>
                <td class="text-center" colspan="5">{NV_GENERATE_PAGE}</td>
            </tr>
        </tfoot>
        <!-- END: generate_page -->
        <tbody>
            <!-- BEGIN: loop -->
            <tr>
                <td class="text-right">{VIEW.number}</td>
                <td class="text-right">{VIEW.addtime_fm}</td>
                <td>{VIEW.full_name}</td>
                <td>{VIEW.main_mail}</td>
                <td class="text-right">{VIEW.click_count}</td>
            </tr>
            <!-- END: loop -->
        </tbody>
    </table>
</div>

<script type="text/javascript">
    $(document).ready(function($) {
        $('select[name="a"], select[name="chot_don"]').select2();
    });
    $("#stat_from,#stat_to").datepicker({
        dateFormat : "dd/mm/yy",
        changeMonth : true,
        changeYear : true,
        showOtherMonths : true,
        yearRange: '2018:2030',
        showOn : 'focus'
    });

    $('#stat_from_btn').click(function() {
        $("#stat_from").datepicker('show');
    });

    $('#stat_to_btn').click(function() {
        $("#stat_to").datepicker('show');
    });

</script>
<!-- END: main -->
