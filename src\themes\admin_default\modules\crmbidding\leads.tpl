<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<script type="text/javascript">
    $(document).ready(function($) {
        $("select[name='affilacate_id']").select2();
        $("select[name='caregiver_id']").select2();
    });
</script>
<!-- BEGIN: search -->
<div class="well">
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" />
        <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" />
        <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
        <div class="row">
            <div class="form-group">
                <a class="btn btn-primary" href="{LINK_ADD}" title="{LANG.add}"><i class="fa fa-plus"></i></a>
            </div>
            <div class="form-group">
                <a class="btn btn-primary" href="{LINK_DUPLICATE}" title="{LANG.merger}"><i class="fa fa-object-ungroup"></i></a>
            </div>
            <div class="form-group">
                <a class="btn btn-primary" href="{LINK_UNDO_MERGE}" title="{LANG.merger}"><i class="fa fa-undo" aria-hidden="true"></i></a>
            </div>
            <div class="form-group">
                <input class="form-control" type="text" value="{Q}" name="q" maxlength="255" placeholder="{LANG.search_title}" />
            </div>
            <div class="form-group">
                <select class="form-control" name="group_leads">
                    <option value="0">{LANG.search_group_leads}</option>
                    <!-- BEGIN: group_leads -->
                    <option value="{GROUPS_LEADS.id}"{GROUPS_LEADS.selected}>{GROUPS_LEADS.title}</option>
                    <!-- END: group_leads -->
                </select>
            </div>
            <div class="form-group" style="max-width: 150px;">
                <select class="form-control" name="status" style="width: 100%;">
                    <option value="-1">{LANG.search_status}</option>
                    <!-- BEGIN: status -->
                    <option value="{STATUS.id}"{STATUS.selected}>{STATUS.title}</option>
                    <!-- END: status -->
                </select>
            </div>
            <div class="form-group">
                <select class="form-control" name="label">
                    <option value="-1">{LANG.labelall}</option>
                    <!-- BEGIN: label -->
                    <option value="{LABEL.id}"{LABEL.selected}>{LABEL.title}</option>
                    <!-- END: label -->
                </select>
            </div>
            <div class="form-group" style="min-width: 208px;">
                <select class="form-control" name="caregiver_id" style="width: 100%">
                    <option value="0">{LANG.search_caregiver_id}</option>
                    <option value="-2"{NOT_CAREGIVERID}>{LANG.search_not_caregiver_id}</option>
                    <!-- BEGIN: caregiver_id -->
                    <option value="{CAREGIVER_ID.userid}"{CAREGIVER_ID.selected}>{CAREGIVER_ID.title}</option>
                    <!-- END: caregiver_id -->
                </select>
            </div>
            <div class="form-group {HIDDEN_BTN}" id="btn_advance_search">
                <a class="btn btn-primary" href="#" onclick="show_advance_search();" title="{LANG.advance_search}"><i class="fa fa-ellipsis-h"></i></a>
            </div>
            <div class="form-group">
                <input class="btn btn-primary" type="submit" value="{LANG.search_submit}" />
            </div>
        </div>
        <div class="row margintop10 {ADVANDE_HIDDEN}" id="advance_search">
            <div class="form-group">
                <select class="form-control" name="search_time_type">
                    <option value="0"{time_update_selected}>{LANG.date_update}</option>
                    <option value="1"{time_create_selected}>{LANG.date_create}</option>
                    <option value="2"{shedule_selected} >{LANG.schedule}</option>
                    <option value="3"{comment_selected} >Thống kê theo ghi chú</option>
                </select>
            </div>
            <div class="form-group">
                <label>{LANG.from}:</label> <input class="form-control w100 uidatepicker" type="text" value="{TIME_FROM}" name="time_from" maxlength="10" autocomplete="off">
            </div>
            <div class="form-group">
                <label>{LANG.to}:</label> <input class="form-control w100 uidatepicker" type="text" value="{TIME_TO}" name="time_to" maxlength="10" autocomplete="off">
            </div>
            <div class="form-group">
                <select class="form-control" name="care_time">
                    <option value="0">{LANG.search_care_time}</option>
                    <!-- BEGIN: care_time -->
                    <option value="{CARE_TIME.id}"{CARE_TIME.selected}>{CARE_TIME.title}</option>
                    <!-- END: care_time -->
                </select>
            </div>

            <div class="form-group" style="min-width: 225px;">
                <select class="form-control" name="siteid">
                    <option value="-1">Tất cả website</option>
                    <!-- BEGIN: select_siteid -->
                    <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                    <!-- END: select_siteid -->
                </select>
            </div>
            <div class="form-group" style="min-width: 225px;">
                <select class="form-control" name="prefix_lang">
                    <option value="-1">{LANG.prefix_lang_all}</option>
                    <!-- BEGIN: select_prefix_lang -->
                    <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                    <!-- END: select_prefix_lang -->
                </select>
            </div>
        </div>
        <input id="page-search" type="hidden" value="{page}" name="page" />
    </form>
</div>
<!-- END: search -->

<!-- BEGIN: success -->
    <div class="alert alert-success">{SUCCESS}</div>
    <script type="text/javascript">
       $(document).ready(function($) {
            $(".alert-success").fadeOut(3000);
            setTimeout(function() {
                location.href = location.href;
            }, 1000);
       });

    </script>
<!-- END: success -->

<div class="box__title">
    <h2 class="title__page title__page_organization">{LANG.list_leads}
        <!-- BEGIN: show_label_lead_new -->
        <label for="auto_up">| <input type="checkbox" class="checkbox_custom" id="auto_up" value="1">Tự động cập nhật dữ liệu của Leads mới nhất</label>
        <!-- END: show_label_lead_new -->
    </h2>
    <!-- BEGIN: show_label_lead_new1 -->
    <p for="">
        Thời gian cập nhật dữ liệu
        <select class="form-control" id="time_update">
            <option value="10000">10s</option>
            <option value="15000">15s</option>
            <option value="20000">20s</option>
            <option value="25000">25s</option>
            <option value="30000">30s</option>
            <option value="35000">35s</option>
            <option value="40000">40s</option>
            <option value="45000">45s</option>
            <option value="50000">50s</option>
            <option value="60000">1 phút</option>
            <option value="120000">2 phút</option>
            <option value="180000">3 phút</option>
        </select>
    </p>
    <!-- END: show_label_lead_new1 -->
</div>

<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <section class="table-responsive" id="main_table">
        {TABLE}
        <!-- BEGIN: error -->
            <div class="alert alert-warning">{ERROR}</div>
        <!-- END: error -->
    </section>
</form>
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<script>
    function show_advance_search() {
        if ($("#advance_search").hasClass("hidden")) {
            $("#advance_search").removeClass("hidden");
        } else {
            $("#advance_search").addClass("hidden");
        }
    }
    $(document).ready(function() {
        $('.uidatepicker').datepicker({ showOn : "both", dateFormat : "dd/mm/yy", changeMonth : true, changeYear : true, showOtherMonths : true, buttonImage : nv_base_siteurl + "assets/images/calendar.gif", buttonImageOnly : true });

    });
    $("#page").change(function() {
        var value = $(this).val();
        var page = $('#page-search').val(value);
        if (page.length == 1) {
            $(".well input[type='submit']").click();
        }
    });
</script>

<!-- BEGIN: save_time_new -->
<script type="text/javascript">
    let time_new = "{UPDATE_TIME_NEW}";
    sessionStorage.setItem('update_time_new', time_new);
</script>
<!-- END: save_time_new -->

<!-- BEGIN: show_scr_lead_new -->
<script type="text/javascript">
    $(document).ready(function($) {
        let autoLoad = sessionStorage.getItem('auto_load_lead_new') ?? 0;
        time_new = sessionStorage.getItem('update_time_new');
        let time_load = sessionStorage.getItem('time_update_new') ?? 10000;
        time_load = parseInt(time_load);
        $("#time_update").val(time_load).trigger('change');

        if (parseInt(autoLoad)) {
            $(".checkbox_custom").prop("checked", true);

            var loadFn = setInterval(function(){
                $.ajax({
                    url: location.href,
                    type: 'POST',
                    data: {
                        'autoLoad' : 1,
                        'time_new' : time_new
                    },
                }) .done(function(a) {
                    // kiểm tra html trả về có id table_lead không thì dừng load
                    if (a.includes('id="table_lead"')) {
                        $("#main_table").html(a);
                    } else {
                        clearInterval(loadFn);
                        autoLoad = 0;
                        sessionStorage.setItem('auto_load_lead_new', autoLoad);
                        $(".checkbox_custom").prop("checked", false);
                    }
                })
            }, time_load);
        }

        $(".checkbox_custom").change(function() {
            autoLoad = $(".checkbox_custom:checked").val() ?? 0;
            sessionStorage.setItem('auto_load_lead_new', autoLoad);
            location.href = location.href;
        });

        $("#time_update").change(function() {
            let time = parseInt($(this).val());
            if (parseInt(time) < 5000) {
                time = 5000;
            }

            sessionStorage.setItem('time_update_new', time);
            location.href = location.href;
        });
    });
</script>
<!-- END: show_scr_lead_new -->
<!-- END: main -->

<!-- BEGIN: table_lead_new -->
<table class="table table-striped table-bordered table-hover" id="table_lead">
    <thead>
        <tr>
            <th class="w50">{LANG.number}</th>
            <th class="w100">
                <div class="inlineblock">
                    <a href="{ORDER_BY_SITEID}">{LANG.siteid}</a>
                </div> <!-- BEGIN: siteid --> <!-- BEGIN: asc --> <a href="{ORDER_BY_SITEID_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_SITEID_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: siteid -->
            </th>
            <th class="w100">
                <div class="inlineblock">
                    <a href="{ORDER_BY_SOURCE_LEAD}">{LANG.source_leads}</a>
                </div> <!-- BEGIN: source_lead --> <!-- BEGIN: asc --> <a href="{ORDER_BY_SOURCE_LEAD_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_SOURCE_LEAD_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: source_lead -->
            </th>
            <th class="w150">
                <div class="inlineblock">
                    <a href="{ORDER_BY_NAME}">{LANG.name}</a>
                </div> <!-- BEGIN: name --> <!-- BEGIN: asc --> <a href="{ORDER_BY_NAME_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_NAME_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: name -->
            </th>
            <th class="w100">
                <div class="inlineblock">
                    <a href="{ORDER_BY_PHONE}">{LANG.phone}</a>
                </div> <!-- BEGIN: phone --> <!-- BEGIN: asc --> <a href="{ORDER_BY_PHONE_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_PHONE_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: phone -->
            </th>
            <th>
                <div class="inlineblock">
                    <a href="{ORDER_BY_EMAIL}">{LANG.email}</a>
                </div> <!-- BEGIN: email --> <!-- BEGIN: asc --> <a href="{ORDER_BY_EMAIL_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_EMAIL_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: email -->
            </th>
            <th class="">
                <div class="inlineblock">
                    <a href="{ORDER_BY_STATUS}">{LANG.status}</a><!-- BEGIN: status --> <!-- BEGIN: asc --> <a href="{ORDER_BY_STATUS_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_STATUS_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: status --><br/>
                    <a href="{ORDER_BY_SCHEDULE}">{LANG.schedule}</a> <!-- BEGIN: schedule --> <!-- BEGIN: asc --> <a href="{ORDER_BY_SCHEDULE_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_SCHEDULE_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: schedule -->
                </div>

            </th>
            <th class="w150 text-center">
                <div class="inlineblock">
                    <a href="{ORDER_BY_TIME}">{LANG.timecreate}/ <br />{LANG.updatetime} <br />{LANG.update_lasttime}
                    </a>
                </div> <!-- BEGIN: updatetime --> <!-- BEGIN: asc --> <a href="{ORDER_BY_TIME_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_TIME_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: updatetime -->
            </th>
            <th class="w100">
                <div class="inlineblock">
                    <a href="{ORDER_BY_AFFC}">{LANG.affilacate_id}</a>
                </div> <!-- BEGIN: affilacate --> <!-- BEGIN: asc --> <a href="{ORDER_BY_AFFC_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_AFFC_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: affilacate -->
            </th>
            <th class="w100">
                <div class="inlineblock">
                    <a href="{ORDER_BY_CARE}">{LANG.caregiver_id}</a>
                </div> <!-- BEGIN: care --> <!-- BEGIN: asc --> <a href="{ORDER_BY_CARE_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_CARE_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: care -->
            </th>
        </tr>
    </thead>
    <!-- BEGIN: generate_page -->
    <tfoot>
        <tr>
            <td colspan="2">
                <label for="page">{LANG.page}</label> <input id="page" class="form-control" type="number" value="{page}" min="1" />
            </td>
            <td class="text-center" colspan="10">{NV_GENERATE_PAGE}</td>
        </tr>
    </tfoot>
    <!-- END: generate_page -->
    <tbody>
        <!-- BEGIN: loop -->
        <tr class="{VIEW.class_lead_new}">
            <td class="text-center">{VIEW.number}</td>
            <td>{VIEW.siteid} {VIEW.prefix_lang_letter}</td>
            <td>{VIEW.source_leads}</td>
            <td><a href="{VIEW.link_view}">{VIEW.name}</a></td>
            <td>{VIEW.phone}</td>
            <td>{VIEW.email}</td>
            <td>{VIEW.status} <br /> <!-- BEGIN: label --> <span style="background-color: {LABEL.color}"><i class="fa fa-flag"></i> {LABEL.title}</span> <!-- END: label --> <!-- BEGIN: schedule --> <br />
            <span style="color: red;"><i class="fa fa-bell"></i> {LANG.schedule}: {SCHEDULE} </span> <br /> <!-- END: schedule -->
            </td>
            <td class="text-center">{VIEW.timecreate}<br />{VIEW.updatetime} <br /><span class="label label-success" style="font-size: 12px;">{VIEW.activity_time}</span>
            </td>
            <td>{VIEW.affilacate_id}</td>
            <td>{VIEW.caregiver_id}</td>
        </tr>
        <!-- END: loop -->
    </tbody>
</table>
<!-- END: table_lead_new -->
