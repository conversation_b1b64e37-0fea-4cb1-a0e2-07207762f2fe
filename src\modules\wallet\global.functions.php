<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */

if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

require NV_ROOTDIR . '/modules/' . $module_file . '/init.php';

/**
 * Chức năng lấy ngôn ngữ cả vi và en
 */
function get_both_lang($lang_alias)
{
    global $nv_Lang, $module_file;
    $message_log = [
        'vi' => '',
        'en' => ''
    ];
    $nv_Lang->changeLang('vi');
    $nv_Lang->loadModule($module_file, false, true);
    $message_log['vi'] = $nv_Lang->getModule($lang_alias);
    $nv_Lang->changeLang('en');
    $nv_Lang->loadModule($module_file, false, true);
    $message_log['en'] = $nv_Lang->getModule($lang_alias);
    $nv_Lang->changeLang(NV_LANG_INTERFACE);
    return $message_log;
}

/**
 * Lấy dữ liệu theo lang từ dữ liệu đưa vào là một json chứa thông tin theo lang. 
 * @param string $json_message dữ liệu dạng json như {"vi":"Mua th\u00eam \u0111i\u1ec3m","en":"Buy points"}
 * @return string
 */
function show_lang_title_from_json($json_message)
{
    $str_return = '';
    $_aray_mess = json_decode($json_message, true);
    if (is_array($_aray_mess)) {
        if (isset($_aray_mess[NV_LANG_DATA])) {
            $str_return = $_aray_mess[NV_LANG_DATA];
        } else {
            $str_return = array_shift(array_values($_aray_mess));
        }
    } else {
        $str_return = $json_message;
    }
    return $str_return;
}

/**
 * @param mixed $amount
 * @param integer $digis
 * @param string $dec_point
 * @param string $thousan_step
 * @return
 */
function get_display_money($amount, $digis = 2, $dec_point = ',', $thousan_step = '.')
{
    $amount = number_format($amount, intval($digis), $dec_point, $thousan_step);
    $amount = rtrim($amount, '0');
    $amount = rtrim($amount, $dec_point);
    return $amount;
}

/**
 * Hàm hiển thị số tiền đã định dạng theo ngôn ngữ giao diện
 *
 * @param float $amount
 * @return string
 */
function display_money($amount)
{
    if (NV_LANG_INTERFACE == 'vi') {
        $dec_point = ',';
        $thousan_step = '.';
    } else {
        $dec_point = '.';
        $thousan_step = ',';
    }
    return get_display_money($amount, 2, $dec_point, $thousan_step);
}

/**
 * get_db_money()
 *
 * @param mixed $amount
 * @param mixed $currency
 * @return
 */
function get_db_money($amount, $currency)
{
    if ($currency == 'VND') {
        return round($amount);
    } else {
        return round($amount, 2);
    }
}

/**
 * Cập nhật hết hạn các giao dịch
 * @return boolean
 */
function nvUpdateTransactionExpired()
{
    global $module_config, $module_name, $db, $module_data, $nv_Cache, $db_config;
    $exp_setting = $module_config[$module_name]['transaction_expiration_time'];
    if (empty($exp_setting)) {
        return true;
    }
    $since_timeout = NV_CURRENTTIME - ($exp_setting * 3600);

    // Cho hết hạn các đơn hàng đã quá hạn
    $db->query("UPDATE " . $db_config['prefix'] . "_" . $module_data . "_transaction SET is_expired=1 WHERE (transaction_status=0 OR transaction_status=1) AND created_time<=" . $since_timeout);

    // Tìm kiếm thời gian hết hạn tiếp theo
    $next_update_time = $db->query("SELECT MIN(created_time) FROM " . $db_config['prefix'] . "_" . $module_data . "_transaction WHERE (transaction_status=0 OR transaction_status=1) AND created_time>" . $since_timeout)->fetchColumn();
    if ($next_update_time > 0) {
        $next_update_time += ($exp_setting * 3600);
    }
    $db->query("UPDATE " . NV_CONFIG_GLOBALTABLE . " SET config_value=" . $db->quote((string) $next_update_time) . " WHERE lang=" . $db->quote(NV_LANG_DATA) . " AND module=" . $db->quote($module_name) . " AND config_name='next_update_transaction_time'");

    $nv_Cache->delMod($module_name);
    $nv_Cache->delMod('settings');
}

$global_array_color_month = array(
    1 => '#DC143C',
    2 => '#8B4789',
    3 => '#4B0082',
    4 => '#27408B',
    5 => '#33A1C9',
    6 => '#2F4F4F',
    7 => '#008B45',
    8 => '#556B2F',
    9 => '#CD950C',
    10 => '#CD6600',
    11 => '#EE5C42',
    12 => '#EE0000',
);

$global_array_transaction_status = [
    0 => $nv_Lang->getModule('transaction_status0'),
    1 => $nv_Lang->getModule('transaction_status1'),
    2 => $nv_Lang->getModule('transaction_status2'),
    3 => $nv_Lang->getModule('transaction_status3'),
    4 => $nv_Lang->getModule('transaction_status4'),
    5 => $nv_Lang->getModule('transaction_status5'),
    6 => $nv_Lang->getModule('transaction_status6')
];

$global_array_transaction_type = [
    '0' => $nv_Lang->getModule('status_sub0'),
    '1' => $nv_Lang->getModule('status_sub1'),
    '2' => $nv_Lang->getModule('status_sub2'),
    '4' => $nv_Lang->getModule('status_sub4')
];

if (!empty($module_config[$module_name]['next_update_transaction_time']) and $module_config[$module_name]['next_update_transaction_time'] <= NV_CURRENTTIME) {
    // Cập nhật lại trạng thái hết hạn các giao dịch
    nvUpdateTransactionExpired();
}

// Tất cả quản trị của site
global $array_user_id_users;
$_sql = 'SELECT tb1.userid, tb1.first_name, tb1.last_name, tb1.username, tb1.email FROM ' . NV_USERS_GLOBALTABLE . ' tb1 INNER JOIN ' . $db_config['prefix'] . '_authors tb2 ON tb1.userid = tb2.admin_id WHERE tb1.userid IN (SELECT `admin_id` FROM ' . NV_AUTHORS_GLOBALTABLE . ' ORDER BY lev ASC) AND tb1.active = 1 AND tb2.is_suspend = 0';
$array_user_id_users = $nv_Cache->db($_sql, 'userid', 'users');

//lấy tên thành phố
$sql = 'SELECT id, title, alias FROM ' . NV_PREFIXLANG . '_location_province ORDER BY weight ASC';
$province_list = $nv_Cache->db($sql, 'id', 'location');

$arr_source_money = [];
$arr_source_money[0] = $nv_Lang->getModule('source_money0');
$arr_source_money[1] = $nv_Lang->getModule('source_money1');
$arr_source_money[2] = $nv_Lang->getModule('source_money2');
$arr_source_money[3] = $nv_Lang->getModule('source_money3');
$arr_source_money[4] = $nv_Lang->getModule('source_money4');
$arr_source_money[5] = $nv_Lang->getModule('source_money5');
$arr_source_money[6] = $nv_Lang->getModule('source_money6');
$arr_source_money[7] = $nv_Lang->getModule('source_money7');
$arr_source_money[8] = $nv_Lang->getModule('source_money8');
