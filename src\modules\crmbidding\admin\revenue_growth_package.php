<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

$page_title = $nv_Lang->getModule('revenue_growth_package');

$type_chart = $nv_Request->get_int('type_chart', 'post,get', 1); // 1: Tháng, 2: Năm
$num_months = $nv_Request->get_int('num_months', 'post,get', 12);
$num_years = $nv_Request->get_int('num_years', 'post,get', 5);
$selected_vips = $nv_Request->get_typed_array('vip', 'post,get', 'string', []);

$num_months = min(max($num_months, 1), 24);
$num_years = min(max($num_years, 1), 10);

$param = [
    'type_chart' => $type_chart,
    'num_months' => $num_months,
    'num_years' => $num_years,
    'vip' => $selected_vips
];

$return = nv_local_api('GetRevenueGrowthChartPackage', $param, $admin_info['username'], $module_name);
$data = json_decode($return, true);

$names = [];
if (!empty($data['data']['series'])) {
    foreach ($data['data']['series'] as $series) {
        $names[] = [
            'id_vip' => $series['id_vip'],
            'name' => $series['name']
        ];
    }
}

$series = $categories = [];
if (!empty($data['data'])) {
    $series = $data['data']['series'];
    $categories = $data['data']['categories'];
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_FILE', $module_file);
$xtpl->assign('TEMPLATE', $global_config['module_theme']);
$xtpl->assign('OP', $op);

if (!empty($global_arr_package)) {
    foreach ($global_arr_package as $key => $value) {
        $xtpl->assign('VIP', [
            'key' => $key,
            'value' => $value,
            'selected' => in_array($key, $selected_vips) ? 'selected="selected"' : ''
        ]);
        $xtpl->parse('main.vip');
    }
}

for ($i = 1; $i <= 24; $i++) {
    $xtpl->assign('MONTH', $i);
    $xtpl->assign('SELECTED', $i == $num_months ? 'selected="selected"' : '');
    $xtpl->parse('main.month');
}

for ($i = 1; $i <= 10; $i++) {
    $xtpl->assign('YEAR', $i);
    $xtpl->assign('SELECTED', $i == $num_years ? 'selected="selected"' : '');
    $xtpl->parse('main.year');
}

$array_search = [
    '1' => $nv_Lang->getModule('month'),
    '2' => $nv_Lang->getModule('year')
];
foreach ($array_search as $key => $value) {
    $xtpl->assign('TYPE', [
        'key' => $key,
        'value' => $value,
        'selected' => $key == $type_chart ? 'selected="selected"' : ''
    ]);
    $xtpl->parse('main.type');
}

$xtpl->assign('SERIES', json_encode($series));
$xtpl->assign('CATEGORIES', json_encode($categories));

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
