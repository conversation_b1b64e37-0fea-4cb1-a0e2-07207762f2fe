<?php

/**
 * @Project SSO
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @Createdate 18/7/2020, 9:40
 */

if (!defined('NV_IS_MOD_USER')) {
    die('Stop!!!');
}

$nv_redirect = nv_redirect_decrypt($nv_redirect);
if (empty($nv_redirect)) {
    $nv_redirect = NV_MY_DOMAIN;
}

$iv = substr(SSO_REGISTER_SECRET, 0, 16);
$sso_redirect = openssl_encrypt($nv_redirect, 'aes-256-cbc', SSO_REGISTER_SECRET, 0, $iv);
$sso_redirect = strtr($sso_redirect, '+/=', '-_,');

setcookie('sso_token_dauthauinfo', '', 1, '/');
unset($_COOKIE['sso_token_dauthauinfo']);

Header('Location: ' . SSO_REGISTER_DOMAIN . '/users/oauth/?server=' . $server . '&sso_redirect=' . $sso_redirect . '&client=' . urlencode(NV_MY_DOMAIN));
die();
