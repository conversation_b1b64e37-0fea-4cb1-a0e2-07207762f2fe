<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_MOD_CALENDAR')) {
    exit('Stop!!!');
}

if ($nv_Request->isset_request('update_status', 'post')) {
    $calendar_id = $nv_Request->get_int('calendar_id', 'post', 0);
    $status = $nv_Request->get_int('status', 'post', 0);
    // Ki<PERSON>m tra user đã login và calendar_id
    if ($calendar_id > 0 && defined('NV_IS_USER')) {
        // Nếu có bản ghi của user_id và calendar_id này thì update, chưa có thì tạo mới
        if ($db->query('SELECT COUNT(*) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_user WHERE calendar_id=' . $calendar_id . ' AND user_id=' . $user_info['userid'])->fetchColumn() > 0) {
            // Update status
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_user SET status=' . $status . ' WHERE calendar_id=' . $calendar_id . ' AND user_id=' . $user_info['userid']);
        } else {
            // Tạo bản ghi mới
            $db->query('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_user (calendar_id, user_id, status) VALUES (' . $calendar_id . ', ' . $user_info['userid'] . ', ' . $status . ')');
        }
    }
    nv_htmlOutput($calendar_id . '_' . $status . '_OK');
}

// Lưu ý: Nếu nhận bất cứ array_op nào hãy thiết đặt $home = 0
$home = defined('IS_DEV_LOCAL') ? 0 : 1;

$page_title = $module_info['site_title'];
$key_words = $module_info['keywords'];
$description = $module_info['description'];

$page_url = $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name;

$events = [];
$reddays = [];
$all_event = [];
$groupedReddays = [];
$calendar_id = [];
$redday_calendar_id = 0;
$user_id = [];
$name_calendar = [];
$cal_colors = [];
$range = getMonthRange();

$db->sqlreset()->select('*')->from(NV_PREFIXLANG . "_" . $module_data)->where('status = 1');
$result = $db->query($db->sql());
$global_calendar = [];
$calendar_ids = [];
$cal_ids = [];
while ($row = $result->fetch()) {
    $global_calendar[$row['id']] = $row;
    $calendar_ids[$row['id']] = $row['id'];
    $cal_ids[$row['is_calendar']] = $row['is_calendar'];
    $cal_colors[$row['is_calendar']] = $row['color'];
    if ($row['is_calendar'] == 2) {
        $redday_calendar_id = $row['id'];
    }
}

if ($nv_Request->isset_request('timeFrom', 'post') && $nv_Request->isset_request('timeTo', 'post')) {
    $timeFrom = $nv_Request->get_int('timeFrom', 'post', 0);
    $timeTo = $nv_Request->get_int('timeTo', 'post', 0);
    $range = [
        0 => $timeFrom,
        1 => $timeTo
    ];
}

if (!empty($calendar_ids)) {
    // Nếu kiểu lịch là Calendar thì hiện ra sự kiện Calendar
    if (in_array(1, $cal_ids)) {
        // Lấy các sự kiện từ bảng events
        $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_event WHERE calendar_id IN (' . implode(',', $calendar_ids) . ') AND status = 1 AND ((date_event >= ' . $range[0] . ' AND date_event <= ' . $range[1] . '))';
        $result = $db->query($sql);
        $event_data = [];
        $event_ids = [];
        
        while ($row = $result->fetch()) {
            $calendar_id[] = $row['calendar_id'];
            $event_ids[] = $row['id'];
            $event_data[$row['id']] = [
                'id' => $row['id'],
                'calendar_id' => $row['calendar_id'],
                'title' => $row['title'],
                'description' => $row['description'],
                'time_event_start' => $row['time_event_start'],
                'time_event_end' => $row['time_event_end'],
                'type' => 'calendar'
            ];
        }
        
        if (!empty($event_ids)) {
            $date_patterns = [];
            $current = $range[0];
            while ($current <= $range[1]) {
                $date_patterns[] = "'" . date('d/m/Y', $current) . "'";
                $current = strtotime('+1 day', $current);
            }
            
            if (!empty($date_patterns)) {
                // Lấy các ngày lặp cho sự kiện đã tìm thấy
                $sql = 'SELECT event_id, date_pattern FROM ' . NV_PREFIXLANG . '_' . $module_data . '_repeats WHERE event_id IN (' . implode(',', $event_ids) . ') AND date_pattern IN (' . implode(',', $date_patterns) . ')';
                $result = $db->query($sql);
                
                while ($row = $result->fetch()) {
                    $event_id = $row['event_id'];
                    $date_pattern = $row['date_pattern'];
                    
                    if (isset($event_data[$event_id])) {
                        $event = $event_data[$event_id];
                        $calendar = $global_calendar[$event['calendar_id']];
                        
                        if ($calendar['is_calendar'] == 1) {
                            $date_parts = explode('/', $date_pattern);
                            $event_date = $date_parts[2] . '-' . $date_parts[1] . '-' . $date_parts[0];
                            
                            if ($event['time_event_start'] == 0 && $event['time_event_end'] == 86340) {
                                $event_entry = [
                                    'id' => $event['id'],
                                    'calendar_id' => $event['calendar_id'],
                                    'title' => $event['title'],
                                    'start' => $event_date,
                                    'end' => $event_date,
                                    'hour_date' => gmdate('H:i', $event['time_event_start']) . ' - ' . gmdate('H:i', $event['time_event_end']),
                                    'description' => $event['description'],
                                    'type' => 'calendar'
                                ];
                            } else {
                                $event_entry = [
                                    'id' => $event['id'],
                                    'calendar_id' => $event['calendar_id'],
                                    'title' => $event['title'],
                                    'start' => $event_date . ' ' . gmdate('H:i', $event['time_event_start']),
                                    'end' => $event_date . ' ' . gmdate('H:i', $event['time_event_end']),
                                    'hour_date' => gmdate('H:i', $event['time_event_start']) . ' - ' . gmdate('H:i', $event['time_event_end']),
                                    'description' => $event['description'],
                                    'type' => 'calendar'
                                ];
                            }
                            
                            $events[] = $event_entry;
                            
                            if (isset($calendar[$event['calendar_id']])) {
                                $user_id[$calendar[$event['calendar_id']]['user_id']] = $calendar[$event['calendar_id']]['user_id'];
                            }
                        }
                    }
                }
            }
        }
        if (!empty($calendar_id)) {
            $unique_calendar_id = array_unique($calendar_id);
            $color = [];
            foreach ($unique_calendar_id as $id) {
                if (isset($global_calendar[$id])) {
                    $calendar = $global_calendar[$id];
                    $color[$id] = $calendar['color'];
                    $user_id[$calendar['user_id']] = $calendar['user_id'];
                    $name_calendar[$id] = $calendar['name_calendar'];
                }
            }

            if (!empty($user_id)) {
                $db->sqlreset()->select('userid, username, first_name, last_name')->from(NV_USERS_GLOBALTABLE)->where('userid IN (' . implode(',', $user_id) . ')');
                $result = $db->query($db->sql());
                $user = [];
                while ($row = $result->fetch()) {
                    $user[$row['userid']] = nv_show_name_user($row['first_name'], $row['last_name'], $row['username']);
                }
            }

            foreach ($events as $key => $event) {
                $events[$key]['color'] = isset($color[$event['calendar_id']]) ? $color[$event['calendar_id']] : '';
                $events[$key]['name_calendar'] = isset($name_calendar[$event['calendar_id']]) ? $name_calendar[$event['calendar_id']] : '';
                $events[$key]['user'] = isset($user[$global_calendar[$event['calendar_id']]['user_id']]) ? $user[$global_calendar[$event['calendar_id']]['user_id']] : '';
            }
        }
    }

    // Nếu kiểu lịch là Redday thì hiện ra sự kiện Redday
    if (in_array(2, $cal_ids)) {
        $db->sqlreset()->select('*')->from(NV_MOD_REDDAY . '_cats')->where('status = 1');
        $result = $db->query($db->sql());
        while ($row = $result->fetch()) {
            $redday_cats[$row['id']] = $row['id'];
        }

        if (!empty($redday_cats)) {
            $where = [];
            $where[] = 'catid IN (' . implode(',', $redday_cats) . ')';
            $where[] = 'status = 1';

            if (date('Y', $range[0]) != date('Y', $range[1])) {
                // Trường hợp qua năm sau thì lấy đến 31-12 và ngày 1-1 trở đi
                $where[] = "((
                    CAST(CONCAT(LPAD(month, 2, '0'), LPAD(day, 2, '0')) AS UNSIGNED) >= " . intval(date('md', $range[0])) . " AND
                    CAST(CONCAT(LPAD(month, 2, '0'), LPAD(day, 2, '0')) AS UNSIGNED) <= 1231
                ) OR (
                    CAST(CONCAT(LPAD(month, 2, '0'), LPAD(day, 2, '0')) AS UNSIGNED) >= 101 AND
                    CAST(CONCAT(LPAD(month, 2, '0'), LPAD(day, 2, '0')) AS UNSIGNED) <= " . intval(date('md', $range[1])) . "
                ))";
            } else {
                // Trường hợp trong năm thì cứ lấy đến
                $where[] = "CAST(CONCAT(LPAD(month, 2, '0'), LPAD(day, 2, '0')) AS UNSIGNED) >= " . intval(date('md', $range[0]));
                $where[] = "CAST(CONCAT(LPAD(month, 2, '0'), LPAD(day, 2, '0')) AS UNSIGNED) <= " . intval(date('md', $range[1]));
            }

            $db->sqlreset()
                ->select('*')
                ->from(NV_MOD_REDDAY . '_rows')
                ->where(implode(' AND ', $where))
                ->order('month, day, id DESC');

            $result = $db->query($db->sql());
            $reddays = [];
            $catid = [];

            while ($row = $result->fetch()) {
                for ($year = date('Y', $range[0]); $year <= date('Y', $range[1]); $year++) {
                    $time = $year . '-' . str_pad($row['month'], 2, '0', STR_PAD_LEFT) . '-' . str_pad($row['day'], 2, '0', STR_PAD_LEFT);
                    $reddays[$time][$row['catid']][] = [
                        'title' => $row['content'],
                        'start' => $time,
                        'end' => $time,
                        'color' => $cal_colors[2] ?? '#7986CB',
                        'textColor' => '#ffffff',
                        'catid' => $row['catid'],
                    ];
                }
                $catid[$row['catid']] = $row['catid'];
            }

            if (!empty($catid)) {
                $db->sqlreset()->select('id, title')->from(NV_MOD_REDDAY . '_cats')->where('id IN (' . implode(',', $catid) . ')');
                $result = $db->query($db->sql());
                $redday_cat = [];
                while ($row = $result->fetch()) {
                    $redday_cat[$row['id']] = $row;
                }

                foreach ($reddays as $date => $reddayListByCatid) {
                    foreach ($reddayListByCatid as $catid => $reddayList) {
                        foreach ($reddayList as $key => $redday) {
                            $reddays[$date][$catid][$key]['namecat'] = isset($redday_cat[$redday['catid']]) ? $redday_cat[$redday['catid']]['title'] : '';
                        }
                    }
                }
            }

            foreach ($reddays as $date => $reddayListByCatid) {
                $random_event = [];
                $events_by_catid = [];
                $events_names = [];
                foreach ($reddayListByCatid as $catid => $reddayList) {
                    $event_redday = [];
                    foreach ($reddayList as $redday) {
                        if (empty($random_event)) {
                            $random_event = $redday;
                        }
                        $event_redday[] = $redday['title'];
                    }
                    $events_by_catid[$catid] = $event_redday;
                    $events_names[$catid] = isset($redday_cat[$catid]) ? $redday_cat[$catid]['title'] : '';
                }

                $groupedReddays[$date] = [
                    'calendar_id' => $redday_calendar_id,
                    'title' => $random_event['title'],
                    'start' => $date,
                    'end' => $date,
                    'color' => $random_event['color'],
                    'description' => $random_event['description'] ?? '',
                    'textColor' => $random_event['textColor'] ?? '#000000'
                ];

                $i = 1;
                foreach ($events_by_catid as $catid => $event_redday) {
                    $groupedReddays[$date]['namecat_' . $i] = $events_names[$catid];
                    $groupedReddays[$date]['events_' . $i] = $event_redday;
                    $i++;
                }
            }
        }
    }
}

$all_event = array_merge($events, array_values($groupedReddays));
if ($nv_Request->isset_request('timeFrom', 'post') && $nv_Request->isset_request('timeTo', 'post')) {
    $res = [
        'res' => 'success',
        'data' => $all_event,
    ];
    nv_jsonOutput($res);
}
$canonicalUrl = getCanonicalUrl($page_url);

$is_call_ajax = defined('NV_IS_USER');
// Lấy trạng thái lịch của user: ds lịch đang bật => tích vào hộp kiểm
$on_calendars = [];
if (defined('NV_IS_USER')) {
    $on_calendars = $db->query('SELECT calendar_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_user WHERE status=1 AND user_id=' . $user_info['userid'])->fetchAll();
    if ($on_calendars) {
        $on_calendars = array_map(function ($c_item) {
            return $c_item['calendar_id'];
        }, $on_calendars);
    }
}
$contents = nv_theme_main($all_event, $is_call_ajax, $on_calendars);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
