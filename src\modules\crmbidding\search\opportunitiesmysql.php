<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:39:33 GMT
 */
if (!defined('NV_IS_API_OPPORTUNITIES')) {
    die('Stop!!!');
}

$db->sqlreset()
    ->select('COUNT(id) as num')
    ->from('' . NV_PREFIXLANG . '_' . $module_data . '_opportunities');

if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}

if (!empty($group_string)) {
    $db->group($group_string);
    $keys_view = [];
    foreach ($array_groups as $field_group) {
        $keys_view[] = $field_group;
    }
    $keys_view[] = 'count(id) as num';
}

$sth = $db->prepare($db->sql());
$sth->execute();
$total = $sth->fetchColumn();
$db->select(implode(',', $keys_view));

$db->order($order_string);

$db->limit($perpage)->offset(($page - 1) * $perpage);
$sth = $db->prepare($db->sql());
$sth->execute();
$arr_data = [];
while ($view = $sth->fetch()) {
    $view['timecreate_display'] = $view['timecreate'] > 0 ? nv_date('H:i:s d/m/Y', $view['timecreate']) : '';
    $view['updatetime_display'] = $view['updatetime'] > 0 ? nv_date('H:i:s d/m/Y', $view['updatetime']) : '';
    $view['schedule_display'] = $view['schedule'] > 0 ? nv_date('H:i:s d/m/Y', $view['schedule']) : '';
    $view['status_display'] = $nv_Lang->getModule('status' . $view['status']);
    $view['source_leads_display'] = isset($array_groups_leads[$view['source_leads']]['title']) ? $array_groups_leads[$view['source_leads']]['title'] : '';

    $affilacate_id = isset($array_user_id_users[$view['affilacate_id']]) ? $array_user_id_users[$view['affilacate_id']] : 0;
    if ($affilacate_id > 0) {
        $view['affilacate_id_fullname'] = nv_show_name_user($affilacate_id['first_name'], $affilacate_id['last_name']);
        $view['affilacate_id_show_name'] = $affilacate_id['username'];
    }
    $caregiver_id = isset($array_user_id_users[$view['caregiver_id']]) ? $array_user_id_users[$view['caregiver_id']] : 0;
    if ($caregiver_id > 0) {
        $view['caregiver_id_fullname'] = nv_show_name_user($caregiver_id['first_name'], $caregiver_id['last_name']);
        $view['caregiver_id_show_name'] = $caregiver_id['username'];
    }

    if ($view['label'] != '') {
        $view['label_arr'] = explode(',', $view['label']);
        foreach ($view['label_arr'] as $key =>  $label) {
            if (isset($array_label[$label])) {
                $view['label_arr'][$key] = $array_label[$label];
            }
        }
    }

    $arr_data[$view['id']] = $view;
}

return $arr_data;
