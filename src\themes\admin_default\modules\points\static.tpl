<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<div class="panel">
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}"> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}"> <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}">
        <div class="form-group">
            <label>{LANG.time_from}:</label> <input class="form-control w100 uidatepicker" type="text" value="{sfrom}" name="time_from" maxlength="10" autocomplete="off">
        </div>
        <div class="form-group">
            <label>{LANG.to}:</label> <input class="form-control w100 uidatepicker" type="text" value="{sto}" name="time_to" maxlength="10" autocomplete="off">
        </div>
        <div class="form-group">
            <select class="form-control" name="s_vip" id="vip">
                <option value="0">{LANG.select_vip}</option>
                <!-- BEGIN: loop_vip -->
                <option value="{OPTION.key}" {OPTION.selected}>{OPTION.title}</option>
                <!-- END: loop_vip -->
            </select>
        </div>
        <div class="form-group">
            <input class="btn btn-primary" type="submit" value="{LANG.search_submit}">
        </div>
    </form>
</div>
<div class="alert alert-info">{title_static}</div>
<div class="row">
    <!-- doanh số nạp điểm -->
    <div class="col-md-24">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover text-center">
                <caption>{LANG.statistical}</caption>
                <thead>
                    <tr>
                        <th class=" text-center">{LANG.name_stt}</th>
                        <th class=" text-center">{LANG.username}</th>
                        <th class=" text-center">{LANG.VIP}</th>
                        <th class=" text-center">{LANG.point_in}</th>
                        <th class=" text-center">{LANG.total_point}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop_listacount -->
                    <tr>
                        <td>{ACOUNT.stt}</td>
                        <td>{ACOUNT.username}</td>
                        <td>{ACOUNT.vip}</td>
                        <td>{ACOUNT.point_total}</td>
                        <td>{ACOUNT.point_total}</td>
                    </tr>
                    <!-- END: loop_listacount -->
                </tbody>
                <!-- BEGIN: generate_page -->
                <tfoot class="text-center">
                    <tr>
                        <td colspan="4">{GENERATE_PAGE}</td>
                    </tr>
                </tfoot>
                <!-- END: generate_page -->
            </table>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function() {
        $('.uidatepicker').datepicker({
            showOn : "both",
            dateFormat : "dd/mm/yy",
            changeMonth : true,
            changeYear : true,
            showOtherMonths : true,
            buttonImage : nv_base_siteurl + "assets/images/calendar.gif",
            buttonImageOnly : true
        });
        $("#vip").select2();
    });
</script>
<!-- END: main -->
