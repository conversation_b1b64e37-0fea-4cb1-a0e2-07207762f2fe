<?php

/**
 * @Project NUKEVIET 4.x
 * <PERSON><PERSON>y dữ liệu biểu đồ tăng trưởng doanh thu theo gói VIP
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\crmbidding\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use PDOException;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetRevenueGrowthChartPackage implements IApi
{
    private $result;

    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    public static function getCat()
    {
        return 'GetChartPackage';
    }

    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    public function execute()
    {
        global $db, $nv_Lang, $module_data, $nv_Request;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $type_chart = $nv_Request->get_int('type_chart', 'post,get', 1); // 1: Tháng, 2: Năm
        $num_months = $nv_Request->get_int('num_months', 'post,get', 12);
        $num_years = $nv_Request->get_int('num_years', 'post,get', 5);
        $selected_vips = $nv_Request->get_typed_array('vip', 'post,get', 'string', []);

        $num_months = min(max($num_months, 1), 24);
        $num_years = min(max($num_years, 1), 10);

        try {
            // Lấy thông tin các gói VIP
            $db->sqlreset()
                ->select('DISTINCT name_vip, vip as id_vip')
                ->from(NV_PREFIXLANG . '_' . $module_data . '_revenue_growth_package');

            if (!empty($selected_vips)) {
                $quoted_vips = [];
                foreach ($selected_vips as $vip) {
                    $quoted_vips[] = $db->quote($vip);
                }
                $selected_vips_sql = implode(',', $quoted_vips);
                $db->where('vip IN (' . $selected_vips_sql . ')');
            }

            $result = $db->query($db->sql());
            $name_vip = [];
            $ids_vip = [];
            while ($row = $result->fetch()) {
                $ids_vip[] = $row['id_vip'];
                $name_vip[] = $row['name_vip'];
            }

            // Lấy thông tin các tháng/năm
            $db->sqlreset()
                ->select('DISTINCT month, year')
                ->from(NV_PREFIXLANG . '_' . $module_data . '_revenue_growth_package')
                ->order('year DESC, month DESC');
            $result = $db->query($db->sql());
            $month_year = [];
            while ($row = $result->fetch()) {
                $month_year[] = [
                    'month' => $row['month'],
                    'year' => $row['year'],
                    'display' => $nv_Lang->getModule('month') . ' ' . $row['month'] . '/' . $row['year']
                ];
            }

            $filtered_month_year = [];
            if ($type_chart == 1) {
                $filtered_month_year = array_slice($month_year, 0, $num_months);
            } elseif ($type_chart == 2) {
                // Lọc theo số năm
                $years = array_unique(array_column($month_year, 'year'));
                $filtered_years = array_slice($years, 0, $num_years);

                foreach ($month_year as $entry) {
                    if (in_array($entry['year'], $filtered_years)) {
                        $filtered_month_year[] = $entry;
                    }
                }
            } else {
                $filtered_month_year = array_slice($month_year, 0, 12);
            }

            // Lấy dữ liệu doanh thu
            $db->sqlreset()
                ->select('*')
                ->from(NV_PREFIXLANG . '_' . $module_data . '_revenue_growth_package');
            $result = $db->query($db->sql());
            $revenue_growth = [];
            while ($row = $result->fetch()) {
                $revenue_growth[$row['name_vip']][$row['year']][$row['month']] = $row['revenue'];
            }

            $series = [];
            foreach ($name_vip as $index => $name) {
                $data = [];
                foreach ($filtered_month_year as $value) {
                    $month = $value['month'];
                    $year = $value['year'];
                    $data[] = $revenue_growth[$name][$year][$month] ?? 0;
                }
                $series[] = [
                    'name' => $name,
                    'id_vip' => $ids_vip[$index],
                    'data' => $data
                ];
            }

            // Tổng hợp dữ liệu theo năm nếu biểu đồ là kiểu năm
            if ($type_chart == 2) {
                $aggregated_series = [];
                foreach ($series as $s) {
                    $name = $s['name'];
                    $id_vip = $s['id_vip'];
                    $yearly_data = [];
                    foreach ($filtered_years as $year) {
                        $yearly_revenue = 0;
                        if (isset($revenue_growth[$name][$year])) {
                            foreach ($revenue_growth[$name][$year] as $month_revenue) {
                                $yearly_revenue += $month_revenue;
                            }
                        }
                        $yearly_data[] = $yearly_revenue;
                    }
                    $aggregated_series[] = [
                        'name' => $name,
                        'id_vip' => $id_vip,
                        'data' => $yearly_data
                    ];
                }
                $series = $aggregated_series;
            }

            $categories = [];
            if ($type_chart == 1) {
                foreach ($filtered_month_year as $value) {
                    $categories[] = $value['display'];
                }
            } elseif ($type_chart == 2) {
                foreach ($filtered_years as $year) {
                    $categories[] = $nv_Lang->getModule('year') . ' ' . $year;
                }
            }

            $this->result->setSuccess();
            $this->result->set('data', [
                'series' => $series,
                'categories' => $categories
            ]);
        } catch (PDOException $e) {
            $this->result->setError()
                ->setCode('3000')
                ->setMessage($e->getMessage());
        }

        return $this->result->getResult();
    }
}
