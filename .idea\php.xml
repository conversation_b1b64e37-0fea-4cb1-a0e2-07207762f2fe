<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="highlightLevel" value="WARNING" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/src/includes/vendor/smarty/smarty" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/src/includes/vendor/composer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/src/includes/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/src/includes/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/src/includes/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/src/includes/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/src/includes/vendor/vinades/pclzip" />
      <path value="$PROJECT_DIR$/src/includes/vendor/vinades/nukeviet" />
      <path value="$PROJECT_DIR$/src/includes/vendor/tecnickcom/tc-lib-color" />
      <path value="$PROJECT_DIR$/src/includes/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/src/includes/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/src/includes/vendor/zaloplatform/zalo-php-sdk" />
      <path value="$PROJECT_DIR$/src/includes/vendor/tecnickcom/tc-lib-barcode" />
      <path value="$PROJECT_DIR$/src/includes/vendor/phpmailer/phpmailer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/league/oauth2-client" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/src/includes/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/src/vendor/desarrolla2/cache" />
      <path value="$PROJECT_DIR$/src/vendor/johngrogg/ics-parser" />
      <path value="$PROJECT_DIR$/src/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/src/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/src/vendor/composer" />
      <path value="$PROJECT_DIR$/src/vendor/mtdowling/jmespath.php" />
      <path value="$PROJECT_DIR$/src/vendor/aws/aws-sdk-php-resources" />
      <path value="$PROJECT_DIR$/src/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/src/vendor/aws/aws-crt-php" />
      <path value="$PROJECT_DIR$/src/vendor/psr/log" />
      <path value="$PROJECT_DIR$/src/vendor/aws/aws-sdk-php" />
      <path value="$PROJECT_DIR$/src/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/src/vendor/elastic/transport" />
      <path value="$PROJECT_DIR$/src/vendor/maennchen/zipstream-php" />
      <path value="$PROJECT_DIR$/src/vendor/php-http/discovery" />
      <path value="$PROJECT_DIR$/src/vendor/paypal/PayPal" />
      <path value="$PROJECT_DIR$/src/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/src/vendor/php-http/promise" />
      <path value="$PROJECT_DIR$/src/vendor/elasticsearch/elasticsearch" />
      <path value="$PROJECT_DIR$/src/vendor/php-http/httplug" />
      <path value="$PROJECT_DIR$/src/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/src/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/src/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/src/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/src/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/src/vendor/markbaker/complex" />
      <path value="$PROJECT_DIR$/src/vendor/jasny/validation-result" />
      <path value="$PROJECT_DIR$/src/vendor/jasny/sso" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/src/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/src/vendor/ezyang/htmlpurifier" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="8.1">
    <option name="suggestChangeDefaultLanguageLevel" value="false" />
  </component>
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>