<!-- BEGIN: main -->
<!-- BEGIN: search -->
<div class="row">
    <div class="col-lg-18">
        <form method="get" action="{NV_BASE_ADMINURL}index.php">
            <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}">
            <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
            <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label for="element_q">{LANG.search_title}</label>
                        <input class="form-control" type="text" value="{Q}" name="q" maxlength="255"
                               placeholder="{LANG.search_title}"/>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label for="element_status">{LANG.status}</label>
                        <select class="form-control" name="status">
                            <option value="-1">{LANG.search_status}</option>
                            <!-- BEGIN: status -->
                            <option value="{STATUS.id}" {STATUS.selected}>{STATUS.title}</option>
                            <!-- END: status -->
                        </select>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="visible-sm-block visible-md-block visible-lg-block">&nbsp;</label>
                        <button class="btn btn-primary" type="submit">
                            <i class="fa fa-search" aria-hidden="true"></i> {LANG.search_submit}
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<!-- END: search -->
<form>
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
            <tr>
                <th style="width: 1%" class="text-center">
                    {LANG.number}
                </th>
                <th style="width: 12%" class="text-nowrap">
                    <a href="{ORDER_BY_HOTEN}">{LANG.ho_ten_khach_chatgpt}</a>
                </th>
                <th style="width: 12%" class="text-nowrap">
                    <a href="{ORDER_BY_UNIQUEID}">{LANG.uniqueid}</a>
                </th>
                <th style="width: 10%" class="text-nowrap">
                    <a href="{ORDER_BY_PHONE}">{LANG.phone}</a>
                </th>
                <th style="width: 10%" class="text-nowrap">
                    <a href="{ORDER_BY_EMAIL}">{LANG.email}</a>
                </th>
                <th style="width: 8%" class="text-nowrap">
                    {LANG.status}
                </th>
                <th style="width: 10%" class="text-nowrap">
                    {LANG.leads_trung}
                </th>
                <th style="width: 7%" class="text-nowrap text-center">
                    <a href="{ORDER_BY_FIRST_TIME}">{LANG.first_time}</a>
                </th>
                <th style="width: 7%" class="text-nowrap text-center">
                    <a href="{ORDER_BY_LAST_TIME}">{LANG.last_activity}</a>
                </th>
            </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td>{VIEW.number}</td>
                    <td><a href="{LINK_DETAIL}">{VIEW.hoten}</a></td>
                    <td><a href="{LINK_DETAIL}">{VIEW.uniqueid}</a></td>
                    <td>{VIEW.phone}</td>
                    <td>{VIEW.email}</td>
                    <td>{VIEW.status}
                        <!-- BEGIN: status_leads -->
                        {STATUS_LEADS}
                        <!-- END: status_leads -->
                    </td>
                    <td class="text-center">
                        <!-- BEGIN: link_leads -->
                        <a href="{LINK_LEADS}" target="_blank">{NAME_LEADS}</a>;
                        <!-- END: link_leads -->
                    </td>
                    <td class="text-center">{VIEW.first_time}</td>
                    <td class="text-center">{VIEW.last_activity}</td>
                </tr>
                <!-- END: loop -->
            </tbody>
            <!-- BEGIN: generate_page -->
            <tfoot>
            <tr>
                <td colspan="6">
                    {NV_GENERATE_PAGE}
                </td>
            </tr>
            </tfoot>
            <!-- END: generate_page -->
        </table>
    </div>
</form>
<!-- END: main -->
