<?php

/**
 * @Project    NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License    : Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 16 Jan 2018 07:25:26 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

$page_title = $table_caption = $nv_Lang->getModule('acountuser');

$page = $nv_Request->get_int('page', 'get', 1);
$per_page = 50;
$base_url = NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=static";

$_from = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
$from = nv_date('d/m/Y', $_from); // Mặc định ngày 01 của tháng
$to = nv_date('d/m/Y', NV_CURRENTTIME);
$sfrom = nv_substr($nv_Request->get_title('time_from', 'get', $from), 0, 10);
$sto = nv_substr($nv_Request->get_title('time_to', 'get', $to), 0, 10);
//Tách ngày tháng thành mảng để xử lý
$_arr_sfrom = explode('/', $sfrom);
$_arr_sto = explode('/', $sto);

$start = mktime(0, 0, 0, $_arr_sfrom[1], $_arr_sfrom[0], $_arr_sfrom[2]);
$end = mktime(23, 59, 59, $_arr_sto[1], $_arr_sto[0], $_arr_sto[2]);

$s_vip = $nv_Request->get_int('s_vip', 'get', 0);

$db->sqlreset()
    ->select('COUNT(userid) as total_users, sum(point_in) as total_points')
    ->from(NV_USERS_GLOBALTABLE . "_info");

$_sql[] = 'point_in > 0';
if ($s_vip > 0) {
    $base_url .= '&vip=' . $s_vip;
    $_sql[] = 'FIND_IN_SET(' . $s_vip . ', vip)';
}

$query = 'SELECT DISTINCT userid FROM ' . $db_config['prefix'] . '_' . $module_data . '_customs_static';
$where = [];
if (!empty($sfrom)) {
    $base_url .= '&time_from=' . $sfrom;
    $where[] = 'date >=' . $start;
}
if (!empty($sto)) {
    $base_url .= '&time_to=' . $sto;
    $where[] = 'date <=' . $end;
}

if (!empty($where)) {
    $query .= ' WHERE ' . implode(' AND ', $where);
    $result = $db->query($query);
    $arr_userid = [];
    while (list($userid) = $result->fetch(3)) {
        $arr_userid[$userid] = $userid;
    }

    if (!empty($arr_userid)) {
        $_sql[] = 'userid IN (' . implode(',', $arr_userid) . ')';
    }
}

// loại trừ các gói của admin
$arr_admin = [];
$result_admin = $db->query('SELECT admin_id FROM nv4_authors');
while ($_admin_info = $result_admin->fetch()) {
    $arr_admin[$_admin_info['admin_id']] = $_admin_info['admin_id'];
}

// các tài khoản test mà k muốn thống kê đưa vào đây, lấy theo nhóm test https://id.dauthau.net/qtdtvnds/index.php?language=vi&nv=users&op=groups&userlist=19
$result = $db->query('SELECT userid FROM ' . NV_GROUPS_GLOBALTABLE . '_users WHERE group_id = 19');
while ($_gr_user_info = $result->fetch()) {
    $arr_admin[$_gr_user_info['userid']] = $_gr_user_info['userid'];
}

if (!empty($arr_admin)) {
    $_sql[] = 'userid NOT IN (' . implode(',', $arr_admin) . ')';
}

$db->where(implode(' AND ', $_sql));
$result = $db->query($db->sql())->fetch();
$total_users = $result['total_users'] ? $result['total_users'] : 0;
$total_points = $result['total_points'] ? $result['total_points'] : 0;

$array_data = [];
$array_users = [];
$db->select('*')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$result = $db->query($db->sql());
while ($row = $result->fetch()) {
    $array_data[$row['userid']] = $row;
    $array_users[$row['userid']] = $row['userid'];
}
$username = [];
if (!empty($array_users)) {
    //lấy username
    $username = array_column($db->query("SELECT userid, username FROM " . NV_USERS_GLOBALTABLE . " WHERE userid IN(" . implode(',', array_keys($array_users)) . ")")
        ->fetchAll(), 'username', 'userid');
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('TABLE_CAPTION', $table_caption);
$xtpl->assign('sfrom', $sfrom);
$xtpl->assign('sto', $sto);

$num_user_not_vip = 0;
$arr_vip = [];
$arr_tilte_vip = [];
if (!empty($array_data)) {
    $stt = $per_page * ($page - 1);
    foreach ($array_data as $_userid => $data) {
        $row = [];
        $row['stt'] = ++$stt;
        $row['username'] = isset($username[$_userid]) ? $username[$_userid] : '';
        $row['point_total'] = number_format($data['point_in'], 0, '.', '.');

        if ($data['vip'] != '') {
            $data['vip'] = explode(',', $data['vip']);
            foreach ($data['vip'] as $val) {
                $row['vip'][$val] = $global_arr_vip[$val];

                if (!empty($arr_vip[$val])) {
                    $arr_vip[$val]++;
                } else {
                    $arr_vip[$val] = 1;
                }
                $arr_tilte_vip[$val] = sprintf($nv_Lang->getModule('vip_user'), $arr_vip[$val], $global_arr_vip[$val]);
            }
            $row['vip'] = implode(', ', $row['vip']);
        } else {
            $num_user_not_vip++;
        }

        $xtpl->assign('ACOUNT', $row);
        $xtpl->parse('main.loop_listacount');
    }
}

$title_static = sprintf($nv_Lang->getModule('title_static'), $total_users, number_format($total_points, 0, '.', '.'));
$title_static .= sprintf($nv_Lang->getModule('normal_user'), $num_user_not_vip);
$title_static .= implode('', $arr_tilte_vip);
$xtpl->assign('title_static', $title_static);

$generate_page = nv_generate_page($base_url, $total_users, $per_page, $page);
if ($generate_page) {
    $xtpl->assign('GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}

foreach ($global_arr_vip as $key => $value) {
    $xtpl->assign('OPTION', array(
        'key' => $key,
        'title' => $value,
        'selected' => $s_vip == $key ? ' selected="selected"' : ''
    ));
    $xtpl->parse('main.loop_vip');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
