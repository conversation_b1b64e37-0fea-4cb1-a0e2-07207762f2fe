<!-- BEGIN: main -->
<!-- BEGIN: pay_log -->
{PAY_LOG.pay_id} <br>
{PAY_LOG.vip} <br>
{PAY_LOG.numbers_year} <br>
<!-- END: pay_log -->
<table class="table table-bordered">
    <caption>{LANG.order_dtnet}</caption>
    <thead>
        <tr>
            <th class="text-center">{LANG.stt}</th>
            <th width="25%">{LANG.log_data}</th>
            <th class="text-center">{LANG.from_time}</th>
            <th class="text-center">{LANG.status}</th>
            <th >{LANG.name_caregiver_id}</th>
            <th class="text-center" width="200px"><a class="show_detail_order_dtnet">{LANG.showall} <i class="fa fa-angle-double-right" aria-hidden="true"></i></a></th>
        </tr>
    </thead>
    <tbody>
        <!-- BEGIN: loop -->
        <tr>
            <td data-label="{LANG.stt}" class="text-center">{ORDER.stt}</td>
            <td data-label="{LANG.log_data}">
                <span class="ttnddh">{ORDER.title}: 
                    <strong class="text-danger">{ORDER.money}</strong>
                    <span class="olist-promo text-muted">{ORDER.promo_code}</span>
                </span> 
            </td>
            <td class="text-center" data-label="{LANG.from_time}">
                <span class="text--weight span__data">{ORDER.add_time}</span>
            </td>
            <td class="text-center" data-label="{LANG.status}">
                <span class="text--weight span__data {ORDER.class_expired}">{ORDER.name_status}</span>
                <!-- <span class="text--weight span__data {ORDER.class_expired}">{ORDER.is_expired}</span> -->
            </td>
            <td data-label="{LANG.name_caregiver_id}">
                <span>{ORDER.name_caregiver_id}</span>
            </td>
            <td class="text-center">
                <p>
                    <a href="#" class="viewmore_payment">{LANG.viewmore} <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
                </p>
                <div class="view_ordervip text-left">
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.name_tthd}:</span> <span class="text--weight">{ORDER.money}</span>
                    </p>

                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.source_money}:</span> <span class="text--weight label label-success">{ORDER.name_source_money}</span>
                    </p>

                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.promo_code}:</span> <span class="text--weight span__data">{ORDER.promo_code}</span>
                    </p>
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.promo_type}:</span> <span class="text--weight span__data">{ORDER.promo_type}</span>
                    </p>
                    
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.discount}:</span> <span class="text--weight span__data">{ORDER.discount}</span>
                    </p>
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.price_reduce}:</span> <span class="text--weight span__data">{ORDER.price_reduce}</span>
                    </p>
                    
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.total_end}:</span> <span class="text--weight span__data">{ORDER.total_end}</span>
                    </p>
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.name_admin_id}:</span> <span class="text--weight span__data">{ORDER.name_admin_id}</span>
                    </p>
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.name_caregiver_id}:</span> <span class="text--weight span__data">{ORDER.name_caregiver_id}</span>
                    </p>
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.name_affiliate_userid}:</span> <span class="text--weight span__data">{ORDER.name_affiliate_userid}</span>
                    </p>
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.name_promo_userid}:</span> <span class="text--weight span__data">{ORDER.name_promo_userid}</span>
                    </p>
                    
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.edit_time_bill}:</span> <span class="text--weight span__data">{ORDER.edit_time}</span>
                    </p>
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.total}:</span> <span class="text--weight span__data">{ORDER.total}</span>
                    </p>
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.status}:</span> <span class="text--weight label label-warning {ORDER.status_bg}">{ORDER.name_status}</span>
                    </p>
                    
                    <a href="{ORDER.link_order}" class="btn btn-warning btn--social btn-xs" ><i class="fa fa-eye" aria-hidden="true"></i> {LANG.detail_order}</a>
                </div>
            </td>
        </tr>
        <!-- END: loop -->
    </tbody>
</table>              
<!-- BEGIN: generate_page -->
<div class="text-center" id="payment_pagination">{NV_GENERATE_PAGE}</div>
<!-- END: generate_page -->

<!-- BEGIN: generate_page_js -->
<script type="text/javascript">
	$(document).ready(function($) {
        var listPayment_page_selector = '#payment_pagination';
        //phân trang API ListPayment
        $(listPayment_page_selector + ' .page-item').not('.active, .disabled').on('click', function (e) {
            e.preventDefault();
            var $this = $(this);
		    ajax_API_pagination($this, callAPI, 'ListPayment', '#show_api_list_payment', '{LANG.err_list_profile_dtnet}')
        });
        
	});
    
    function ajax_API_pagination(selector, callBackAPI, apiName, resultLocation, errorMess) {
        //phân trang API 
        var page_href = selector.children(".page-link").attr('href');
        if (page_href.includes('page=')) {
            var start = page_href.lastIndexOf('page=');
            var page_string = page_href.substring(start);
            var page = page_string.split('=');
            callBackAPI(apiName, resultLocation, errorMess, page[1]);
        } else {
            callBackAPI(apiName, resultLocation, errorMess, 1);
        }
    }
</script>
<!-- END: generate_page_js -->
<script>
span__data = $(".span__data");
for (var i = 0; i < span__data.length; i++) {
    if (span__data.eq(i).text() === '' || span__data.eq(i).text() == 0) {
        span__data.eq(i).parent().hide();
    }
}
$(".view_ordervip").hide();
$(".show_detail_order_dtnet").click(function(event) {
    $(this).parent().find('i').toggleClass('iconUp');
    if ($(this).parent().find('i').attr('class').split(' ')[2]) {
        $(this).parent().parent().parent().parent().find('.view_ordervip').hide();
        $(this).parent().parent().parent().parent().find('.view_ordervip').slideToggle(500);
    } else {
        $(this).parent().parent().parent().parent().find('.view_ordervip').show();
        $(this).parent().parent().parent().parent().find('.view_ordervip').slideToggle(500);
    }
});
$(".viewmore_payment").click(function(event) {
    $(this).closest('tr').find(".view_ordervip").slideToggle(500);
});

</script>
<!-- END: main -->
