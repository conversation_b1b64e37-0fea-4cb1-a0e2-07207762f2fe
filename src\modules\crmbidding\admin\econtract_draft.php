<?php

/**
 * T<PERSON><PERSON> năng quản lý hợp đồng điện tử
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2024 Hu<PERSON><PERSON> Đ<PERSON>. All rights reserved
 * @createdAt Mon, 15 Apr 2024 11:55:00
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

$api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);

// Lấy phân loại nhóm của user
$my_group_type = 'sale';
$sql = 'SELECT t1.* FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups as t1 LEFT JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users as t2 ON t1.group_id=t2.group_id WHERE t2.userid = ' . $admin_info['userid'];
$_my_groups = $db->query($sql)->fetchAll();
if ($_my_groups) {
    foreach ($_my_groups as $_my_group) {
        $_my_group['config'] = unserialize($_my_group['config']);
        if (
            $my_group_type == 'sale' &&
            isset($_my_group['config']['type']) &&
            !empty($_my_group['config']['type']) &&
            $_my_group['config']['type'] != 'sale'
        ) {
            $my_group_type = $_my_group['config']['type'];
        }
    }
}

// Xử lý trình lãnh đạo ký
require_once NV_ROOTDIR . '/modules/' . $module_file . '/admin/econtract_sign.php';

$admin_config = [];
// Lấy danh sách user mà mình đang quản lý (chung team)
$filter_uploader_ids = [];
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE userid = ' . $admin_info['userid'];
$result = $db->query($sql);
while ($row_groups_users = $result->fetch()) {
    $_admin_config = json_decode($row_groups_users['config'], true) ?? [];
    $admin_config = array_merge($admin_config, $_admin_config);
    if ($row_groups_users['is_leader'] == 1) {
        $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $row_groups_users['group_id'] . ' AND userid != ' . $admin_info['userid'];
        $_result = $db->query($_sql);
        while ($_row_groups_users = $_result->fetch()) {
            $filter_uploader_ids[$_row_groups_users['userid']] = $_row_groups_users['userid'];
        }
    }
}
$filter_uploader_ids[$admin_info['userid']] = $admin_info['userid'];
$is_manage_econtract = isset($admin_config['manage_econtract']) && ($admin_config['manage_econtract'] == 1); // Phân quyền: Quản lý TẤT CẢ hợp đồng

// TODO: HIỂN THỊ GIAO DIỆN TRANG DANH SÁCH HỢP ĐỒNG ĐIỆN TỬ
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op;
$per_page = 20;
$page = $nv_Request->get_int('page', 'post,get', 1);

$array_search = array();
$array_search['q'] = $nv_Request->get_title('q', 'post,get', '');
$array_search['vip_id'] = $nv_Request->get_typed_array('vip_id', 'post,get', 'int', []);
$array_search['uploader_id'] = $nv_Request->get_typed_array('uploader_id', 'post,get', 'int', []);
$array_search['status'] = $nv_Request->get_typed_array('status', 'post,get', 'int', []);
$array_search['stage'] = $nv_Request->get_typed_array('stage', 'post,get', 'int', []);
$array_search['term_changed'] = $nv_Request->get_int('term_changed', 'post,get', 0);
$array_search['hard_copy'] = $nv_Request->get_int('hard_copy', 'post,get', 0);

// Lấy danh sách hợp đồng điện tử để hiển thị
$econtract_rows = [];
$where = [];

if (!empty($array_search['q'])) {
    $where_q = [];
    // Tìm theo username
    $username_econtract_ids = [];
    $_econtract_with_usernames = $db->query('SELECT econtract_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE username LIKE ' . $db->quote('%' . $array_search['q'] . '%'))->fetchAll();
    if ($_econtract_with_usernames && count($_econtract_with_usernames)) {
        foreach ($_econtract_with_usernames as $_econtract_with_username) {
            $username_econtract_ids[] = $_econtract_with_username['econtract_id'];
        }
    }
    $username_econtract_ids = array_unique($username_econtract_ids);
    if (count($username_econtract_ids)) {
        $where_q[] = 'id IN (' . implode(',', $username_econtract_ids) . ')';
    }
    // Tìm theo MST
    $where_q[] = 'tax_code LIKE ' . $db->quote($array_search['q'] . '%');
    // Tìm theo Số hợp đồng
    $where_q[] = 'contract_no LIKE ' . $db->quote('%' . $array_search['q'] . '%');
    // Tìm theo Tên công ty
    $where_q[] = 'c_name LIKE ' . $db->quote('%' . $array_search['q'] . '%');

    $where[] = '(' . implode(' OR ', $where_q) . ')';
    $base_url .= '&amp;q=' . $array_search['q'];
}

if (!empty($array_search['vip_id'])) {
    $where_vip = [];
    foreach ($array_search['vip_id'] as $svid) {
        $where_vip[] = 'FIND_IN_SET(' . $svid . ', vips_vi)';
        $where_vip[] = 'FIND_IN_SET(' . $svid . ', vips_en)';
        $base_url .= '&amp;vip_id[]=' . $svid;
    }
    $where[] = '(' . implode(' OR ', $where_vip) . ')';
}

if (!empty($array_search['uploader_id'])) {
    foreach ($array_search['uploader_id'] as $suid) {
        $base_url .= '&amp;uploader_id[]=' . $suid;
    }
    if (!(defined(constant_name: 'NV_IS_SPADMIN') || $my_group_type == 'marketing' || $is_manage_econtract)) {
        foreach ($array_search['uploader_id'] as $_isuid => $_suid) {
            if (!in_array($_suid, array_values($filter_uploader_ids))) {
                unset($array_search['uploader_id'][$_isuid]);
            }
        }
        if (!empty($array_search['uploader_id'])) {
            $where[] = 'uploader_id IN (' . implode(',', $array_search['uploader_id']) . ')';
        } else {
            $where[] = 'uploader_id IN (' . implode(',', array_values($filter_uploader_ids)) . ')';
        }
    } else {
        $where[] = 'uploader_id IN (' . implode(',', $array_search['uploader_id']) . ')';
    }
} elseif (!(defined('NV_IS_SPADMIN') || $my_group_type == 'marketing' || $is_manage_econtract)) {
    $where[] = 'uploader_id IN (' . implode(',', array_values($filter_uploader_ids)) . ')';
}

if (!empty($array_search['status'])) {
    foreach ($array_search['status'] as $status) {
        $base_url .= '&amp;status[]=' . $status;
    }
    $where[] = 'status IN (' . implode(', ', $array_search['status']) . ')';
}

if (!empty($array_search['stage'])) {
    foreach ($array_search['stage'] as $stage) {
        $base_url .= '&amp;stage[]=' . $stage;
    }
    $where[] = 'stage IN (' . implode(', ', $array_search['stage']) . ')';
}

if ($array_search['term_changed'] == 1) {
    $where[] = 'term_changed=' . $array_search['term_changed'];
    $base_url .= '&amp;term_changed=' . $array_search['term_changed'];
}

if (!empty($array_search['hard_copy'])) {
    $where[] = "(receiver <> '' AND receiver_phone <> '' AND receiver_address <> '')";
    $base_url .= '&amp;hard_copy=' . $array_search['hard_copy'];
}

$page_title = $nv_Lang->getModule('econtract_draft_title');

/**
 * https://vinades.org/dauthau/dauthau.info/-/issues/3272
 * Hiện tại các hợp đồng nháp sau khi có 1 bên ký, cập nhật trạng thái tương ứng vẫn đang là hợp đồng nháp.
 * => Cần điều chỉnh nó trở thành hợp đồng chính thức (vì trên thực tế khi 1 bên ký thì đó đã là bản chính thức rồi).
 */
$status_done = [
    EcontractStatus::Done->value,
    EcontractStatus::HSTDTSigned->value,
    EContractStatus::CustomerSigned->value
];
$where[] = 'status NOT IN (' . implode(',', $status_done) . ')';

// Lấy tổng số lượng rows
$db->sqlreset()
    ->select('COUNT(*)')
    ->from(NV_PREFIXLANG . '_' . $module_data . '_econtracts');

if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}
$sth = $db->prepare($db->sql());
$sth->execute();
$num_items = $sth->fetchColumn();

$db->select('*')
    ->from(NV_PREFIXLANG . '_' . $module_data . '_econtracts')
    ->order('created_at DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);

$sth = $db->prepare($db->sql());
$sth->execute();

$xtpl = new XTemplate('econtract_draft.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('Q', $array_search['q']);
$xtpl->assign('OP_BASE_URL', $base_url);
$xtpl->assign('AJAX_GETCOMINFO', $base_url . '&getcominfo=1&tax_code=');
$xtpl->assign('AJAX_SUBMIT_UPLOAD', $base_url . '&submit=1');
$xtpl->assign('NUM_ITEMS', $num_items);
$xtpl->assign('CREATE_CONTRACT_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_content');
$xtpl->assign('DONE_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract');
$xtpl->assign('DRAFT_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
$xtpl->assign('TOKEN', NV_CHECK_SESSION);

// Nếu Marketing thì ẩn nút Tải lên hợp đồng
if ($my_group_type == 'sale' || $is_manage_econtract || defined('NV_IS_SPADMIN')) {
    $xtpl->parse('main.allow_add');
    $xtpl->parse('main.allow_add_modal');
}
// Hiển thị danh sách chọn gói VIP tìm kiếm
foreach ($global_arr_vip as $vip_id => $vip_title) {
    $xtpl->assign('VIP', [
        'id' => $vip_id,
        'title' => $vip_title,
        'selected' => in_array($vip_id, $array_search['vip_id']) ? 'selected="selected"' : ''
    ]);
    $xtpl->parse('main.search_vip');
}

// Tìm kiếm theo trang thái
$status_remaining = array_diff($array_econtract_status, $status_done);
foreach ($status_remaining as $status_key) {
    $xtpl->assign('STATUS', [
        'id' => $status_key,
        'title' => EContractStatus::tryFrom($status_key)?->getLabel() ?? '',
        'selected' => in_array($status_key, $array_search['status']) ? 'selected="selected"' : ''
    ]);
    $xtpl->parse('main.search_status');
}

// Tìm kiếm theo giai đoạn
foreach ($array_econtract_stage as $stage_key) {
    $xtpl->assign('STAGE', [
        'id' => $stage_key,
        'title' => EContractStage::tryFrom($stage_key)?->getLabel() ?? '',
        'selected' => in_array($stage_key, $array_search['stage']) ? 'selected="selected"' : ''
    ]);
    $xtpl->parse('main.search_stage');
}

// Tìm theo điều khoản thay đổi
foreach ([0, 1] as $term_changed) {
    $xtpl->assign('TERM_CHANGED', [
        'id' => $term_changed,
        'title' => $term_changed ? $nv_Lang->getModule('term_changed_yes') : $nv_Lang->getModule('term_changed_no'),
        'selected' => $term_changed == $array_search['term_changed'] ? 'selected="selected"' : ''
    ]);
    $xtpl->parse('main.search_term_changed');
}

// Lọc theo hợp đồng có in bản cứng
foreach ([0, 1] as $hard_copy) {
    $xtpl->assign('HARD_COPY', [
        'id' => $hard_copy,
        'title' => $hard_copy ? $nv_Lang->getModule('print_hardcopy_yes') : $nv_Lang->getModule('print_hardcopy_no'),
        'selected' => $hard_copy == $array_search['hard_copy'] ? 'selected="selected"' : ''
    ]);
    $xtpl->parse('main.search_hard_copy');
}

// Hiển thị danh sách người tải lên
if (defined('NV_IS_SPADMIN') || $my_group_type == 'marketing' || $is_manage_econtract) {
    foreach ($all_array_user_id_users as $value) {
        $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
        $value['selected'] = in_array($value['userid'], $array_search['uploader_id']) ? 'selected="selected"' : '';
        $value['title'] = $value['username'] . ' (' . $fullname . ')';
        $xtpl->assign('UPLOADER_ID', $value);
        $xtpl->parse('main.search_uploader_id');
    }
} else {
    foreach ($all_array_user_id_users as $value) {
        $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
        $value['selected'] = in_array($value['userid'], $array_search['uploader_id']) ? 'selected="selected"' : '';
        $value['title'] = $value['username'] . ' (' . $fullname . ')';
        $xtpl->assign('UPLOADER_ID', $value);
        if (isset($filter_uploader_ids[$value['userid']])) {
            $xtpl->parse('main.search_uploader_id');
        }
    }
}

$stt = ($page - 1) * $per_page;
while ($row = $sth->fetch()) {
    $row['orders'] = '';
    $row['vips'] = '';
    $row['status_label'] = EContractStatus::tryFrom($row['status'])?->getLabel() ?? '';
    $row['stage_label'] = EContractStage::tryFrom($row['stage'])?->getLabel() ?? '';
    $arr_filename_contract = explode('/', $row['contract_path']);
    $row['contract_attachment'] = end($arr_filename_contract);
    $row['url_download_contract'] = !empty($row['contract_path']) ? NV_MAIN_DOMAIN . '/' . $row['contract_path'] : NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=download&id=' . $row['id'];
    if ($row['status'] != EContractStatus::Cancel->value) {
        $row_act_link['url_detail'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $row['id'];
        $row_act_link['url_preview'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=preview&id=' . $row['id'];
        $row_act_link['url_upload'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=upload&id=' . $row['id'];
        $row_act_link['url_download'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=download&id=' . $row['id'];
        $xtpl->assign('ROW_LINK', $row_act_link);
        $xtpl->parse('main.loop_row.action');
        $xtpl->assign('ROW_ID', $row['id']);
        $xtpl->parse('main.loop_row.action_detail');
    }

    $order_rows = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id=' . $row['id'] . ' ORDER BY order_id ASC')->fetchAll();
    $order_vips = [];
    if ($order_rows) {
        foreach ($order_rows as $order_row) {
            $row['orders'] .= '<a href="' . URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $order_row['order_id'] . '" target="_blank"><strong class="label label-success">' .  sprintf('BDH%010s', $order_row['order_id']) . '</strong> <small>(' . $order_row['username'] . ')</small></a><br>';
            if (!empty($order_row['vips_vi'])) {
                $order_vips = array_merge($order_vips, explode(',', $order_row['vips_vi']));
            }
            if (!empty($order_row['vips_en'])) {
                $order_vips = array_merge($order_vips, explode(',', $order_row['vips_en']));
            }
        }
    }
    $order_vips = array_unique($order_vips);
    if (count($order_vips)) {
        foreach ($order_vips as $order_vip) {
            $row['vips'] .= ' ' . $global_arr_vip[$order_vip] . ';';
        }
        $row['vips'] = trim($row['vips'], ';');
    }

    $row['del_url'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;delete_id=' . $row['id'] . '&amp;delete_checkss=' . md5($row['id'] . NV_CACHE_PREFIX . $client_info['session_id']);

    $row['stt'] = ++$stt;
    // Lấy tên người tải lên (thường là sale)
    $row['uploader'] = 'N/A';
    if (isset($all_array_user_id_users[$row['uploader_id']])) {
        $uploader = $all_array_user_id_users[$row['uploader_id']];
        $row['uploader'] = nv_show_name_user($uploader['first_name'], $uploader['last_name'], $uploader['userid']);
    }
    $row['updated_at'] = nv_date('d/m/Y H:i', $row['updated_at']);

    $xtpl->assign('ROW', $row);
    if (defined('NV_IS_SPADMIN') || $is_manage_econtract || $my_group_type == 'sale') {
        $xtpl->parse('main.loop_row.allow_del_econtract');
    }
    if (defined('NV_IS_SPADMIN') || $is_manage_econtract || $my_group_type == 'sale') {
        $xtpl->parse('main.loop_row.allow_edit_econtract');
    }
    if ($row['term_changed'] != 0) {
        $xtpl->parse('main.loop_row.show_label_term_changed');
    }
    if ($row['merged_econtract_id'] && !count($order_rows)) {
        $xtpl->parse('main.loop_row.show_label_merged_contract');
    }
    if ($row['status'] == EContractStatus::Cancel->value) {
        $xtpl->assign('ORDER_CANCEL_LABEL', EContractStatus::Cancel->getLabel());
        $xtpl->parse('main.loop_row.show_label_order_cancel');
    }
    $xtpl->parse('main.loop_row');
}

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
if (!empty($generate_page)) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
