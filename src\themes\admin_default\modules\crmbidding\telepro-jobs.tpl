<!-- BEGIN: main -->
<div class="table-responsive">
    <table class="table table-striped table-bordered table-hover">
        <colgroup>
            <col class="w100">
        </colgroup>
        <thead>
            <tr>
                <th style="width: 10%" class="text-center text-nowrap">{LANG.telejob_id1}</th>
                <th style="width: 40%" class="text-nowrap">{LANG.telejob_title}</th>
                <th style="width: 35%" class="text-nowrap">{LANG.telejob_code}</th>
                <th style="width: 15%" class="text-center text-nowrap">{LANG.telejob_function}</th>
            </tr>
        </thead>
        <tbody>
            <!-- BEGIN: loop -->
            <tr>
                <td class="text-center">{ROW.id}</td>
                <td>{ROW.title}</td>
                <td>{ROW.job_code}</td>
                <td class="text-center text-nowrap">
                    <a class="btn btn-sm btn-default" href="{ROW.url_edit}"><i class="fa fa-edit"></i> {GLANG.edit}</a>
                    <!-- BEGIN: delete -->
                    <a class="btn btn-sm btn-danger" href="javascript:void(0);" onclick="nv_delele_telepro_jobs('{ROW.id}');"><i class="fa fa-trash"></i> {GLANG.delete}</a>
                    <!-- END: delete -->
                </td>
            </tr>
            <!-- END: loop -->
        </tbody>
    </table>
</div>
<!-- BEGIN: error -->
<div class="alert alert-danger">{ERROR}</div>
<!-- END: error -->

<h2><i class="fa fa-th-large" aria-hidden="true"></i> {CAPTION}</h2>
<div class="panel panel-default">
    <div class="panel-body">
        <form method="post" action="{FORM_ACTION}" class="form-horizontal">
            <div class="form-group">
                <label class="col-sm-6 control-label" for="ipt_id">{LANG.telejob_id} <span class="fa-required text-danger">(<em class="fa fa-asterisk"></em>)</span>:</label>
                <div class="col-sm-18 col-lg-10">
                    <input type="number" id="ipt_id" name="telepro_id" value="{DATA.telepro_id}" class="form-control">
                    <span class="help-block mb-0">{LANG.telejob_id_note}</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-6 control-label" for="ipt_title">{LANG.telejob_title} <span class="fa-required text-danger">(<em class="fa fa-asterisk"></em>)</span>:</label>
                <div class="col-sm-18 col-lg-10">
                    <input type="text" id="ipt_title" name="title" value="{DATA.title}" class="form-control">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-6 control-label" for="ipt_job_code">{LANG.telejob_code} <span class="fa-required text-danger">(<em class="fa fa-asterisk"></em>)</span>:</label>
                <div class="col-sm-18 col-lg-10">
                    <input type="text" id="ipt_job_code" name="job_code" value="{DATA.job_code}" class="form-control">
                    <span class="help-block mb-0">{LANG.telejob_code_note}</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-6 control-label" for="ipt_api_key">{LANG.teleapi_key} <span class="fa-required text-danger">(<em class="fa fa-asterisk"></em>)</span>:</label>
                <div class="col-sm-18 col-lg-10">
                    <input type="text" id="ipt_api_key" name="api_key" value="{DATA.api_key}" class="form-control">
                    <span class="help-block mb-0">{LANG.teleapi_key_note}</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-6 control-label" for="int_data_fields">{LANG.telejob_fields} <span class="fa-required text-danger">(<em class="fa fa-asterisk"></em>)</span>:</label>
                <div class="col-sm-18 col-lg-10">
                    <span class="help-block mb-0">{LANG.telejob_fields_note}</span>
                    <textarea class="form-control" rows="15" id="int_data_fields" name="data_fields">{DATA.data_fields}</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-6 control-label">{LANG.show}:</label>
                <div class="col-sm-18 col-lg-10">
                    <input type="checkbox" value="1" class="form-control" {DATA.active} name="active" style="margin-top: 7px;"/>
                </div>
            </div>
            
            <div class="row">
                <div class="col-sm-18 col-sm-offset-6">
                    <button type="submit" name="submit" value="submit" class="btn btn-primary">{GLANG.submit}</button>
                </div>
            </div>
        </form>
    </div>
</div>
<!-- END: main -->
