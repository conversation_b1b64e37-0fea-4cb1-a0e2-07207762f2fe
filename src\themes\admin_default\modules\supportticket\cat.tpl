<!-- BEGIN: main -->
<!-- BEGIN: view -->
<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th class="w100">{LANG.weight}</th>
                    <th>{LANG.title_vi}</th>
                    <th>{LANG.title_en}</th>
                    <th class="text-center">{LANG.is_customer}</th>
                    <th>{LANG.is_point}</th>
                    <th class="text-center">{LANG.point_price}</th>
                    <th class="text-center">{LANG.point_ai}</th>
                    <th class="w100 text-center">{LANG.bonus_point}</th>
                    <th class="w100 text-center">{LANG.active}</th>
                    <th class="w250">&nbsp;</th>
                </tr>
            </thead>
            <!-- BEGIN: generate_page -->
            <tfoot>
                <tr>
                    <td class="text-center" colspan="6">{NV_GENERATE_PAGE}</td>
                </tr>
            </tfoot>
            <!-- END: generate_page -->
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td>
                        <select class="form-control" id="id_weight_{VIEW.cat_id}" onchange="nv_change_weight('{VIEW.cat_id}');">
                        <!-- BEGIN: weight_loop -->
                            <option value="{WEIGHT.key}"{WEIGHT.selected}>{WEIGHT.title}</option>
                        <!-- END: weight_loop -->
                    </select>
                </td>
                    <td> <a href="{VIEW.link_user}"> {VIEW.title_vi}</a> </td>
                    <td> <a href="{VIEW.link_user}"> {VIEW.title_en}</a> </td>
                    <td class="text-center"> {VIEW.is_customer} </td>
                    <td> {VIEW.is_point} </td>
                    <td class="text-center"> {VIEW.point_price} </td>
                    <td class="text-center"> {VIEW.point_ai} </td>
                    <td class="text-center"> {VIEW.bonus_point} </td>
                    <td class="text-center"><input type="checkbox" name="active" id="change_status_{VIEW.cat_id}" value="{VIEW.cat_id}" {CHECK} onclick="nv_change_status({VIEW.cat_id});" /></td>
                    <td class="text-center">
                        <em class="fa fa-cog fa-lg"></em> <a href="{VIEW.link_user}">{LANG.user_extend}</a> -
                        <i class="fa fa-edit fa-lg"></i> <a href="{VIEW.link_edit}#edit">{LANG.edit}</a> -
                        <em class="fa fa-trash-o fa-lg"></em> <a href="{VIEW.link_delete}" onclick="return confirm(nv_is_del_confirm[0]);">{LANG.delete}</a>
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
</form>
<!-- END: view -->

<!-- BEGIN: error -->
<div class="alert alert-warning">{ERROR}</div>
<!-- END: error -->
<div class="panel panel-default">
<div class="panel-body">
<form class="form-horizontal" action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <input type="hidden" name="cat_id" value="{ROW.cat_id}" />
    <div class="form-group">
        <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.title_vi}</strong> <span class="red">(*)</span></label>
        <div class="col-sm-19 col-md-20">
            <input class="form-control" type="text" name="title_vi" value="{ROW.title_vi}" />
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.title_en}</strong> <span class="red">(*)</span></label>
        <div class="col-sm-19 col-md-20">
            <input class="form-control" type="text" name="title_en" value="{ROW.title_en}" />
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.is_customer}</strong> <span class="red">(*)</span></label>
        <div class="col-sm-19 col-md-20">
            <div class="checkbox">
            <!-- BEGIN: radio_is_customer -->
                <label><input class="form-control" type="radio" name="is_customer" value="{OPTION_CUTOMER.key}" {OPTION_CUTOMER.checked}>{OPTION_CUTOMER.title} &nbsp;</label>
            <!-- END: radio_is_customer -->
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.is_point}</strong> <span class="red">(*)</span></label>
        <div class="col-sm-19 col-md-20">
            <div class="checkbox">
            <!-- BEGIN: radio_is_point -->
                <label><input class="form-control" type="radio" name="is_point" value="{OPTION.key}" {OPTION.checked}>{OPTION.title} &nbsp;</label>
            <!-- END: radio_is_point -->
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.point_price}</strong></label>
        <div class="col-sm-19 col-md-20">
            <input class="form-control" type="number" name="point_price" value="{ROW.point_price}" />
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.point_ai}</strong></label>
        <div class="col-sm-19 col-md-20">
            <input class="form-control" type="number" name="point_ai" value="{ROW.point_ai}" />
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.bonus_point}</strong></label>
        <div class="col-sm-19 col-md-20">
            <input class="form-control" type="number" name="bonus_point" value="{ROW.bonus_point}" />
        </div>
    </div>
    <div class="form-group" style="text-align: center"><input class="btn btn-primary" name="submit" type="submit" value="{LANG.save}" /></div>
</form>
</div></div>

<script type="text/javascript">
//<![CDATA[
    function nv_change_weight(id) {
        var nv_timer = nv_settimeout_disable('id_weight_' + id, 5000);
        var new_vid = $('#id_weight_' + id).val();
        $.post(script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=cat&nocache=' + new Date().getTime(), 'ajax_action=1&cat_id=' + id + '&new_vid=' + new_vid, function(res) {
            var r_split = res.split('_');
            if (r_split[0] != 'OK') {
                alert(nv_is_change_act_confirm[2]);
            }
            window.location.href = script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=cat';
            return;
        });
        return;
    }


    function nv_change_status(id) {
        var new_status = $('#change_status_' + id).is(':checked') ? true : false;
        if (confirm(nv_is_change_act_confirm[0])) {
            var nv_timer = nv_settimeout_disable('change_status_' + id, 5000);
            $.post(script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=cat&nocache=' + new Date().getTime(), 'change_status=1&cat_id='+id, function(res) {
                var r_split = res.split('_');
                if (r_split[0] != 'OK') {
                    alert(nv_is_change_act_confirm[2]);
                }
            });
        }
        else{
            $('#change_status_' + id).prop('checked', new_status ? false : true);
        }
        return;
    }


//]]>
</script>
<!-- END: main -->
