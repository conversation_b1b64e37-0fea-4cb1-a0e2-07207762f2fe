<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<div class="clearfix">
    <div class="pull-right form-group">
        <input type="button" onclick="javascript:history.back();" value="{LANG.goback}" class="btn btn-info"/>
        <!-- BEGIN: link_opportunities -->
        <a class="btn btn-info" href="{CONTENT.link_opportunities}">{LANG.link_opportunities}</a>
        <!-- END: link_opportunities -->
    </div>
    <h1>{LANG.detailtransaction} {CONTENT.code}</h1>
</div>
<div class="table-responsive">
    <table class="table table-striped table-bordered table-hover">
        <tbody>
            <tr>
                <td class="w200">{LANG.transaction_created_time}</td>
                <td>{CONTENT.created_time}</td>
            </tr>
            <tr>
                <td>{LANG.account}</td>
                <td>{CONTENT.accounttran}</td>
            </tr>
            <tr>
                <td>{LANG.typetransaction}</td>
                <td>{CONTENT.status_title}</td>
            </tr>
            <tr>
                <td>{LANG.moneytransaction}</td>
                <td><strong class="text-danger">{CONTENT.money_total} {CONTENT.money_unit}</strong></td>
            </tr>
            <tr>
                <td>{LANG.money_net}</td>
                <td><strong class="text-danger">{CONTENT.money_net} {CONTENT.money_unit}</strong></td>
            </tr>
            <tr>
                <td>{LANG.money_fee}</td>
                <td><strong class="text-danger">{CONTENT.money_discount} {CONTENT.money_unit}</strong></td>
            </tr>
            <tr>
                <td>{LANG.num_money_collection}</td>
                <td><strong class="text-danger">{CONTENT.money_revenue} {CONTENT.money_unit}</strong></td>
            </tr>
            <tr>
                <td>{LANG.transaction_status}</td>
                <td><strong class="text-info">{CONTENT.transaction_status}</strong></td>
            </tr>
            <tr>
                <td>{LANG.transaction_id}</td>
                <td>{CONTENT.transaction_id}</td>
            </tr>
            <tr>
                <td>{LANG.user_payment}</td>
                <td>{CONTENT.transaction_uname}</td>
            </tr>
            <tr>
                <td>{LANG.transaction_type}</td>
                <td>{CONTENT.transaction_type}</td>
            </tr>
            <tr>
                <td>{LANG.datetransaction_admin}</td>
                <td>{CONTENT.transaction_time}</td>
            </tr>
            <tr>
                <td>{LANG.customer_name}</td>
                <td>{CONTENT.customer_name}</td>
            </tr>
            <tr>
                <td>{LANG.customer_email}</td>
                <td>{CONTENT.customer_email}</td>
            </tr>
            <tr>
                <td>{LANG.customer_phone}</td>
                <td>{CONTENT.customer_phone}</td>
            </tr>
            <tr>
                <td>{LANG.customer_address}</td>
                <td>
                    <span id="cus_add">{CONTENT.address_full}</span>
                    <a  class="btn btn-success" data-toggle="modal" data-target="#modaleditlocation">{LANG.edit}</a>
                </td>
            </tr>
            <tr>
                <td>{LANG.customer_info}</td>
                <td>{CONTENT.customer_info}</td>
            </tr>
            <tr>
                <td>{LANG.infotransaction}</td>
                <td>{CONTENT.transaction_info}</td>
            </tr>
            <tr>
                <td>{LANG.payment}</td>
                <td>{CONTENT.paymentname} ({CONTENT.payment})</td>
            </tr>
            <!-- BEGIN: source_money -->
            <tr>
                <td>{LANG.source_money}</td>
                <td>{CONTENT.source_money_title}</td>
            </tr>
            <!-- END: source_money -->
        </tbody>
    </table>
</div>
<!-- BEGIN: transaction_data -->
<h1>{LANG.transaction_data}</h1>
<div class="table-responsive">
    <table class="table table-striped table-bordered table-hover">
        <tbody>
            <!-- BEGIN: loop -->
            <tr>
                <td class="w200">{OTHER_KEY}</td>
                <td>
                    <!-- BEGIN: link -->
                    <a href="{OTHER_LINK}">{OTHER_VAL}</a>
                    <!-- END: link -->
                    <!-- BEGIN: text -->
                    {OTHER_VAL}
                    <!-- END: text -->
                </td>
            </tr>
            <!-- END: loop -->
        </tbody>
    </table>
</div>
<!-- END: transaction_data -->
<div class="form-group clearfix">
    <div class="pull-right">
        <input type="button" onclick="javascript:history.back();" value="{LANG.goback}" class="btn btn-info"/>
    </div>
</div>

<div class="modal fade" id="modaleditlocation" tabindex="-1" role="dialog" aria-labelledby="modallocationLabel" data-backdrop="static">
    <div class="modal-dialog modal-lg" role="document">
        <form id="frommodaleditlocation" class="form-horizontal">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h3 class="modal-title" id="modallocationLabel"><em class="fa fa-edit"></em> <strong>{LANG.edit_location}</strong></h3>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="control-label col-sm-6" for="customer_address">
                            {LANG.customer_address_order}<i class="text-danger">(*)</i>:
                        </label>
                        <div class="col-sm-18">
                            <input class="form-control" type="text" id="customer_address" name="customer_address" value="{CONTENT.customer_address}" placeholder="{LANG.address_home}"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-6"></label>
                        <div class="col-md-18">
                            <div class="col-md-8">
                                <select name="province" id="province" class="form-control" data-toggle="valueLocation">
                                    <option value="0">{LANG.province_id}</option>
                                    <!-- BEGIN: province -->
                                    <option value="{PROVINCE.key}" {PROVINCE.selected}>{PROVINCE.title}</option>
                                    <!-- END: province -->
                                </select>
                            </div>
                            <div class="col-md-8">
                                <select name="district" id="district" class="form-control" data-toggle="valueLocation">
                                    <option value="0">{LANG.iddistrict}</option>
                                </select>
                            </div>
                            <div class="col-md-8">
                                <select name="ward" id="ward" class="form-control select2">
                                    <option value="0">{LANG.idward}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer text-center">
                    <input type="hidden" name="save_edit_location" value="{NV_CHECK_SESSION}">
                    <input type="hidden" name="id" value="{CONTENT.id}">
                    <div class="text-center">
                        <button type="button" id="save_edit_location" class="btn btn-primary" name="save_edit_location"><em class="fa fa-edit"></em> {LANG.save}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
    #edit_cus_add {
        margin-left: 10px;
    }

    #cus_add[contenteditable="true"] {
        outline: 2px solid green;
    }
</style>

<script>
    $(function() {
        var add = $('#cus_add').text().trim();

        $('#edit_cus_add').click(function(event){
            event.preventDefault();

            var t = $('#cus_add');
            var isEditable = t.attr('contenteditable') === 'true';

            if (isEditable) {
                var newAdd =  $('#cus_add').text().trim();
                if (newAdd !== add) {
                    var href = $(this).attr('href');
                    $.ajax({
                        type : 'POST',
                        url : href,
                        data : 'address=' + newAdd,
                        success : function(data) {
                            if (data.startsWith('OK_')) {
                                add = newAdd;
                                alert('{LANG.update_add_success}');
                            } else {
                                alert('{LANG.update_add_error}');
                            }
                        },
                        error : function() {
                            alert('{LANG.update_add_error}');
                        },
                    });
                }
                t.attr('contenteditable', false);
                $(this).text('{LANG.edit}');
            } else {
                t.attr('contenteditable', true);
                $(this).text('OK');
            }
        });

        $('#save_edit_location').click(function(e) {
            var $form = $('#frommodaleditlocation');
            $.ajax({
                type: 'POST',
                url: script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=viewtransaction&id=' + $form.find('input[name="id"]').val(),
                data: $form.serialize(),
            }).done(function(data) {
                if (data['res'] === 'success') {
                    $('#modaleditlocation').modal('hide');
                    location.reload();
                } else {
                    alert(data['mess']);
                }
            });
        });

        $('#province, #district, #ward').select2({
            width: '100%'
        });

        function processLocation(changedField, initialLoad = false) {
            var provinceId = initialLoad ? '{CONTENT.province_id}' : $('#province').val();
            var districtId = initialLoad ? '{CONTENT.district_id}' : $('#district').val();
            var wardId = initialLoad ? '{CONTENT.ward_id}' : $('#ward').val();

            $.ajax({
                type: 'POST',
                url: nv_base_siteurl + 'index.php?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=wallet&' + nv_fc_variable + '=recharge/ATM',
                data: {
                    province_id: provinceId,
                    district_id: districtId,
                    ward_id: wardId,
                    loadlocation: 1
                },
                dataType: 'json',
                success: function(res){
                    if (initialLoad || changedField === 'province') {
                        if (res.html_district) {
                            $('#district').html(res.html_district);
                            $('#district').val(initialLoad ? '{CONTENT.district_id}' : '0');
                        }
                        $('#ward').html('<option value="0">{LANG.idward}</option>');
                    }

                    if (initialLoad || changedField === 'district') {
                        if (res.html_ward) {
                            $('#ward').html(res.html_ward);
                            $('#ward').val(initialLoad ? '{CONTENT.ward_id}' : '0');
                        }
                    }
                }
            });
        }

        $('#province').on('change', function(){
            $('#district').html('<option value="0">{LANG.iddistrict}</option>');
            $('#ward').html('<option value="0">{LANG.idward}</option>');
            processLocation('province');
        });

        $('#district').on('change', function(){
            $('#ward').html('<option value="0">{LANG.idward}</option>');
            processLocation('district');
        });

        $('#province').val('{CONTENT.province_id}');
        processLocation('', true);

        $('[data-toggle="valueLocation"]').on('change', function() {
            processLocation();
        });
        setTimeout(() => {
            processLocation();
        });
    })
</script>
<!-- END: main -->
