<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdateContact implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'contact';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $error = '';
        $error_code = '0000';
        $contactid = $nv_Request->get_int('contactid', 'post', '0');
        $admin_id = $nv_Request->get_int('admin_id', 'post', '0');

        if ($contactid > 0) {
            $row_old = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_contact WHERE id=' . $contactid)->fetch();
            if (empty($row_old)) {
                return $this->result->setError()
                    ->setCode('2004')
                    ->setMessage('Contact not exit')
                    ->getResult();
            }

            $data = $nv_Request->get_array('data', 'post');
            if (!is_array($data)) {
                return $this->result->setError()
                    ->setCode('2000')
                    ->setMessage('Param data invalid to json')
                    ->getResult();
            }
            $row = $data;

            $keys_check = [
                'contactname',
                'shortname',
                'primaryphone',
                'secondaryphone',
                'primaryemail',
                'secondaryemail',
                'address',
                'description',
                'organizationsid',
                'active',
                'convert_leads'
            ];
            if (!empty($data)) {
                // check $field
                $array_sql = [];
                foreach ($data as $key => $value) {
                    if (!in_array($key, $keys_check)) {
                        return $this->result->setError()
                            ->setCode('2002')
                            ->setMessage('Missing field ' . $key . ' in otherdata')
                            ->getResult();
                    }
                    $array_sql[$key] = $key . '=:' . $key;
                }

                if (isset($row['contactname']) and $row['contactname'] == '') {
                    $error_code = '2003';
                    $error = $nv_Lang->getModule('error_required_name');
                } else if ((isset($row['primaryphone']) or isset($row['primaryemail'])) and ($row['primaryphone'] == '' and $row['primaryemail'] == '')) {
                    $error_code = '2004';
                    $error = $nv_Lang->getModule('error_required_phone');
                } elseif ($row['primaryphone'] != '' && !phonecheck($row['primaryphone'])) {
                    $error_code = '2005';
                    $error = $nv_Lang->getModule('error_phone_number');
                } elseif ($row['primaryemail'] != '' && nv_check_valid_email($row['primaryemail']) != '') {
                    $error_code = '2006';
                    $error = $nv_Lang->getModule('error_email');
                } else if (($admin_id > 0 and !isset($array_user_id_users[$admin_id])) or empty($admin_id)) {
                    $error_code = '2011';
                    $error = $nv_Lang->getModule('admin_id_not_defind');
                } else if (isset($row['organizationsid']) and $row['organizationsid'] == 0) {
                    $error_code = '2010';
                    $error = $nv_Lang->getModule('organizationsid_not_defined');
                }

                if (isset($row['secondaryemail']) and $row['secondaryemail'] != '') {
                    $row['secondaryemail'] = str_replace(';', ',', $row['secondaryemail']);
                    $row['secondaryemail'] = str_replace("\n", ',', $row['secondaryemail']);
                    $_arr_email = array();
                    $list_mail = explode(',', $row['secondaryemail']);
                    foreach ($list_mail as $_mail) {
                        $_mail = trim($_mail);
                        if (($check_valid_email = nv_check_valid_email($_mail)) != '') {
                            $error_code = '2008';
                            $error = sprintf($nv_Lang->getModule('error_subemail'), $_mail);
                        } elseif (!in_array($_mail, $_arr_email)) {
                            $_arr_email[] = $_mail;
                        }
                    }
                    $row['secondaryemail'] = implode(',', $_arr_email);
                }

                if (isset($row['secondaryphone']) and $row['secondaryphone'] != '') {
                    $row['secondaryphone'] = str_replace(';', ',', $row['secondaryphone']);
                    $row['secondaryphone'] = str_replace("\n", ',', $row['secondaryphone']);
                    $_arr_phone = array();
                    $list_phone = explode(',', $row['secondaryphone']);
                    foreach ($list_phone as $_phone) {
                        $_phone = trim($_phone);
                        $_arr_phone[] = $_phone;
                        if (!phonecheck($_phone)) {
                            $error_code = '2007';
                            $error = sprintf($nv_Lang->getModule('error_sub_phone'), $_phone);
                        }
                    }
                    $row['secondaryphone'] = implode(',', $_arr_phone);
                }

                if (empty($error)) {
                    try {
                        $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_contact SET ' . implode(',', $array_sql) . ', updatetime=' . NV_CURRENTTIME . '  WHERE id=' . $contactid);

                        foreach ($row as $key => $value) {
                            $stmt->bindValue(':' . $key, $value, PDO::PARAM_STR);
                        }
                        $exc = $stmt->execute();
                        if ($exc) {
                            // ghi log
                            $log_data = [
                                $nv_Lang->getModule('log_update_contact')
                            ];

                            foreach ($row as $key => $value) {
                                if ($row_old[$key] != $row[$key]) {
                                    $log_data[] = [
                                        $nv_Lang->getModule($key),
                                        $row_old[$key] . ' =&gt; ' . $row[$key]
                                    ];
                                }
                            }

                            if (sizeof($log_data) > 1) { // không có gì thay đổi thì k ghi log
                                $sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (userid, log_area, log_key, log_time, log_data, contactid) VALUES (" . $admin_id . ", 1, 'LOG_ADMIN_UPDATE_CONTACT', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $contactid . ")";
                                $db->query($sql);
                            }
                            $nv_Cache->delMod($module_name);
                            nv_insert_logs(NV_LANG_DATA, $module_name, 'Edit Contact Info', 'ID: ' . $contactid, $admin_id);
                            $this->result->setSuccess();
                            $this->result->set('contactid', $contactid);
                        }
                    } catch (PDOException $e) {
                        $this->result->setError()
                            ->setCode('3000')
                            ->setMessage(print_r($e, true));
                    }
                } else {
                    return $this->result->setError()
                        ->setCode($error_code)
                        ->setMessage($error)
                        ->getResult();
                }
            } else {
                return $this->result->setError()
                    ->setCode('2001')
                    ->setMessage('Param data empty')
                    ->getResult();
            }
        } else {
            return $this->result->setError()
                ->setCode('2003')
                ->setMessage('Contactid is integer, more than 0' . $contactid)
                ->getResult();
        }

        return $this->result->getResult();
    }
}
