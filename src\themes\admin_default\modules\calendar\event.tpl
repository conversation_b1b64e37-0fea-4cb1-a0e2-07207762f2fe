<!-- BEGIN: main -->
<link rel="stylesheet" href="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/select2/select2.min.css">
<script type="text/javascript" src="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
<link type="text/css" href="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<link rel="stylesheet" href="{NV_STATIC_URL}themes/admin_default/css/jquery.datepicker.lunar.css">
<script type="text/javascript" src="{NV_STATIC_URL}themes/admin_default/js/jquery.datepicker.lunar.js"></script>
<!-- BEGIN: success -->
<div class="alert alert-success">{SUCCESS_MSG}</div>
<!-- END: success -->
<p class="text-info"><span class="fa-required text-danger">(<em class="fa fa-asterisk"></em>)</span> {LANG.is_required}</p>
<!-- BEGIN: error -->
<div class="alert alert-danger">{ERROR}</div>
<!-- END: error -->

<div class="panel panel-default">
    <div class="panel-body">
        <form method="post" action="{FORM_ACTION}" class="form-horizontal">
            <div class="form-group">
                <label class="col-sm-6 control-label">{LANG.date_event}:</label>
                <div class="col-sm-18 col-lg-10">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="input-group">
                                <input type="text" class="form-control" id="element_date_event" name="date_event" value="{DATA.date_event}" autocomplete="off">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-xs-24">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="is_lunar" id="is_lunar" value="1"{IS_LUNAR}> {LANG.is_lunar_calendar}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-6 control-label">{LANG.time}:</label>
                <div class="col-sm-18 col-lg-10">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="input-group">
                                <span class="input-group-addon">{LANG.start}</span>
                                <input type="time" class="form-control" id="element_time_event_start" name="time_event_start" value="{DATA.time_event_start}">
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="input-group">
                                <span class="input-group-addon">{LANG.end}</span>
                                <input type="time" class="form-control" id="element_time_event_end" name="time_event_end" value="{DATA.time_event_end}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-6 control-label">{LANG.status_repeat}</label>
                <div class="col-sm-18 col-lg-10">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="input-group">
                                <select class="form-control" name="repeat_type" id="repeatType">
                                    <!-- BEGIN: repeat -->
                                    <option value="{REPEAT.key}"{REPEAT.selected}>{REPEAT.value}</option>
                                    <!-- END: repeat -->
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group" id="repeatDetail">
                <label class="col-sm-6 control-label">{LANG.end_date_repeat}</label>
                <div class="col-sm-18 col-lg-10">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="input-group">
                                <input type="text" class="form-control" id="element_repeat_until_date" name="repeat_until_date" value="{DATA.repeat_until_date}" autocomplete="off">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                            </div>
                            <div class="help-block text-info" id="repeat_limit_info"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-6 control-label" for="element_cat">{LANG.calendar_select} <span class="fa-required text-danger">(<em class="fa fa-asterisk"></em>)</span>:</label>
                <div class="col-sm-18 col-lg-10">
                    <select id="element_cat" name="calendar_id" class="form-control">
                        <option value="0">----</option>
                        <!-- BEGIN: calendar -->
                        <option value="{CALENDAR.id}"{CALENDAR.selected}>{CALENDAR.name_calendar}</option>
                        <!-- END: calendar -->
                    </select>
                </div>
            </div>
           <div class="form-group">
               <label class="col-sm-6 control-label" for="element_title">{LANG.name_event} <span class="fa-required text-danger">(<em class="fa fa-asterisk"></em>)</span>:</label>
                <div class="col-sm-18 col-lg-10">
                     <input type="text" class="form-control" name="title" id="element_title" value="{DATA.title}">
                </div>
           </div>
              <div class="form-group">
                <label class="col-sm-6 control-label" for="element_description">{LANG.description}:</label>
                  <div class="col-xs-24">
                      <div class="mt-1">
                          {DATA.description}
                      </div>
                  </div>
              </div>
            <div class="row">
                <div class="col-sm-18 col-sm-offset-6">
                    <input type="hidden" name="save" value="{NV_CHECK_SESSION}">
                    <button type="submit" class="btn btn-primary">{GLANG.submit}</button>
                </div>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function() {
        $('select').select2();
        
        var solarDate = ''; // Biến lưu ngày dương lịch
        
        $('#element_date_event').datepicker({
            dateFormat: 'dd/mm/yy',
            changeMonth: true,
            changeYear: true,
            showOtherMonths: true,
            selectOtherMonths: true,
            yearRange: "2000:2050",
            beforeShow: function(input, inst) {
                if ($('#is_lunar').is(':checked')) {
                    var currentDate = $(input).val().split('/');
                    if (currentDate.length === 3) {
                        $.ajax({
                            url: script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=event',
                            method: 'POST',
                            async: false,
                            data: {
                                action: 'lunar2solar',
                                day: parseInt(currentDate[0], 10),
                                month: parseInt(currentDate[1], 10),
                                year: parseInt(currentDate[2], 10),
                                lunarLeap: $('#is_leap_month').is(':checked') ? 1 : 0
                            },
                            success: function(response) {
                                if (response.status === 'success') {
                                    solarDate = response.data;
                                    setTimeout(function() {
                                        $(input).datepicker('setDate', response.data);
                                    }, 0);
                                }
                            }
                        });
                    }
                }
                return true;
            },
            onSelect: function(dateText, inst) {
                if ($('#is_lunar').is(':checked')) {
                    var dateParts = dateText.split('/');
                    convertDates('solar2lunar', dateParts);
                } else {
                    solarDate = dateText;
                }
            },
            onClose: function(dateText, inst) {
                if ($('#is_lunar').is(':checked') && solarDate) {
                    $(this).val($('#element_date_event').data('lunar-date'));
                }
            }
        });

        $('#element_repeat_until_date').datepicker({
            dateFormat: 'dd/mm/yy',
            changeMonth: true,
            changeYear: true,
            showOtherMonths: true,
            selectOtherMonths: true,
            yearRange: "2000:2050",
            beforeShow: function(input, inst) {
                return true;
            },
            onSelect: function(dateText, inst) {
                if ($('#is_lunar').is(':checked')) {
                    var dateParts = dateText.split('/');
                    var currentDateParts = $('#element_date_event').val().split('/');
                    convertDates('solar2lunar', currentDateParts, dateParts);
                }
            }
        });

        function convertDates(action, mainDate, repeatDate = null) {
            if (!mainDate || mainDate.length !== 3) {
                return;
            }

            $.ajax({
                url: script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=event',
                method: 'POST',
                data: {
                    action: action,
                    day: parseInt(mainDate[0], 10),
                    month: parseInt(mainDate[1], 10), 
                    year: parseInt(mainDate[2], 10),
                    lunarLeap: $('#is_leap_month').is(':checked') ? 1 : 0
                },
                success: function(response) {
                    if (response.status === 'success') {
                        if (action === 'solar2lunar') {
                            $('#element_date_event').val(response.data);
                            $('#element_date_event').data('lunar-date', response.data);
                            if (response.is_leap !== undefined) {
                                $('#is_leap_month').prop('checked', response.is_leap);
                            }
                        } else {
                            solarDate = response.data;
                        }
                    }
                }
            });
        }

        $('#is_lunar').change(function() {
            var isLunar = $(this).is(':checked');
            var currentDate = $('#element_date_event').val().split('/');
            
            if (currentDate.length === 3) {
                if (isLunar) {
                    // Chuyển từ dương sang âm
                    convertDates('solar2lunar', currentDate);
                } else {
                    // Chuyển từ âm sang dương
                    convertDates('lunar2solar', currentDate);
                    setTimeout(function() {
                        $('#element_date_event').val(solarDate);
                    }, 100);
                }
            }
        });

        if ($('#repeatType').val() == 0) {
            $('#repeatDetail').hide();
            $('#element_repeat_until_date').prop('disabled', true);
        } else {
            $('#repeatDetail').show();
            $('#element_repeat_until_date').prop('disabled', false);
            updateRepeatLimitInfo($('#repeatType').val());
        }
        
        $('#repeatType').change(function() {
            if ($(this).val() == 0) {
                $('#repeatDetail').hide();
                $('#element_repeat_until_date').prop('disabled', true);
                $('#repeat_limit_info').html('');
            } else {
                $('#repeatDetail').show();
                $('#element_repeat_until_date').prop('disabled', false);
                updateRepeatLimitInfo($(this).val());
            }
        });
        
        function updateRepeatLimitInfo(repeatType) {
            var limitInfo = '';
            switch(parseInt(repeatType)) {
                case 1:
                    limitInfo = '{LANG.warning_repeat_day}';
                    break;
                case 2:
                    limitInfo = '{LANG.warning_repeat_week}';
                    break;
                case 3:
                    limitInfo = '{LANG.warning_repeat_month}';
                    break;
                case 4:
                    limitInfo = '{LANG.warning_repeat_year}';
                    break;
                default:
                    limitInfo = '';
            }
            $('#repeat_limit_info').html(limitInfo);
        }
    });
</script>
<!-- END: main -->
