<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:39:33 GMT
 */
if (!defined('NV_IS_API_OPPORTUNITIES')) {
    die('Stop!!!');
}

// kết nối tới ElasticSearh
$nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_host'], $module_config[$module_name]['elas_port'], 'crm_opportunities', $module_config[$module_name]['elas_user'], $module_config[$module_name]['elas_pass'] );
$search_elastic = [];

if (!empty($array_where)) {
    $search_elastic = QueryMysqlAPI_to_ES($array_where);
    $search_elastic['must'][] = [
        "match" => [
            "active" => 1
        ]
    ];
}
$array_query_elastic = [];
if (!empty($search_elastic) && isset($search_elastic['should'])) {
    $search_elastic['minimum_should_match'] = '1';
    $search_elastic['boost'] = '1.0';
}

$array_query_elastic = [];
if (!empty($search_elastic)) {
    $array_query_elastic['query']['bool'] = $search_elastic;
}
$array_query_elastic['track_total_hits'] = 'true';
$array_query_elastic['size'] = $perpage;
$array_query_elastic['from'] = ($page - 1) * $perpage;

$sort = [];
if (!empty($array_order))  {
    foreach ($array_order as $key => $value) {
        /* if ($key != 'caregiver_id' && $key != 'status' && $key != 'updatetime' && $key != 'affilacate_id' && $key != 'caregiver_id' && $key != 'active') {
            $key = $key . '.keyword';
        } */
        if ($key == 'name' || $key == 'email' || $key == 'phone') {
            $key = $key . '.keyword';
        }
        $sort[] = [
            $key => [
                "order" => $value
            ]
        ];

    }
}

if (!empty($sort)) {
    $array_query_elastic['sort'] = $sort;
}

$response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_opportunities', $array_query_elastic);
$total = $response['hits']['total']['value'];
$arr_data = [];
foreach ($response['hits']['hits'] as $value) {
    if (!empty($value['_source'])) {
        $view = $value['_source'];
        $view['timecreate_display'] = $view['timecreate'] > 0 ? nv_date('H:i:s d/m/Y', $view['timecreate']) : '';
        $view['updatetime_display'] = $view['updatetime'] > 0 ? nv_date('H:i:s d/m/Y', $view['updatetime']) : '';
        $view['schedule_display'] = $view['schedule'] > 0 ? nv_date('H:i:s d/m/Y', $view['schedule']) : '';
        $view['status_display'] = $nv_Lang->getModule('status' . $view['status']);
        $view['source_leads_display'] = isset($array_groups_leads[$view['source_leads']]['title']) ? $array_groups_leads[$view['source_leads']]['title'] : '';

        $affilacate_id = isset($array_user_id_users[$view['affilacate_id']]) ? $array_user_id_users[$view['affilacate_id']] : 0;
        if ($affilacate_id > 0) {
            $view['affilacate_id_fullname'] = nv_show_name_user($affilacate_id['first_name'], $affilacate_id['last_name']);
            $view['affilacate_id_show_name'] = $affilacate_id['username'];
        }
        $caregiver_id = isset($array_user_id_users[$view['caregiver_id']]) ? $array_user_id_users[$view['caregiver_id']] : 0;
        if ($caregiver_id > 0) {
            $view['caregiver_id_fullname'] = nv_show_name_user($caregiver_id['first_name'], $caregiver_id['last_name']);
            $view['caregiver_id_show_name'] = $caregiver_id['username'];
        }

        if ($view['label'] != '') {
            $view['label_arr'] = explode(',', $view['label']);
            foreach ($view['label_arr'] as $key => $label) {
                $label = intval(trim($label));
                if (isset($array_label[$label])) {
                    $view['label_arr'][$key] = $array_label[$label];
                }
            }
        }

        $arr_data[$view['id']] = $view;
    }
}

return $arr_data;
