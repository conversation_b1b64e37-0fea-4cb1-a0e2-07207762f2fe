<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Module\crmbidding\Group\Group;

global $db, $nv_Cache;

$allow_func = [
    'main',
    'config',
    'groups_leads',
    'leads',
    'leadsnew',
    'add',
    'import',
    'bni',
    'leads_info',
    'chatgpt_detail',
    'opportunities',
    'opportunities_add',
    'opportunities_info',
    'convert_user',
    'groups',
    'static',
    'telepro',
    'label',
    'duplicate',
    'chart',
    'growth_chart',
    'revenue_growth_package',
    'payment',
    'customs',
    'mobiphone',
    'static_renewal',
    'static_tytrong_doanhthu',
    'static_groups_lead',
    'recovery_admin_leads',
    'config_sale',
    'tawkto',
    'chatgpt',
    'detail_mobiphone',
    'imapdetail',
    'imaplist',
    'recovery_admin_vip',
    'manage_customer',
    'detail_customer',
    'detail_customer_new',
    'static_order',
    'static_renewal_chart',
    'contact',
    'organizations',
    'check_duplicate',
    'unsubscribe',
    'payment',
    'undo_merge',
    'export',
    'email_report_spam',
    'list-zalo'
];

$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_crmbidding_groups_users';
$result = $db->query($sql);
$array_groups_users = $array_user_gr = [];
while ($row = $result->fetch()) {
    $row['config'] = json_decode($row['config'], true);
    $row['config_percent'] = json_decode($row['config_percent'], true);
    $array_groups_users[$row['userid']] = $row;
    $array_user_gr[$row['userid']][$row['group_id']] = $row;
    $arr_group_user2[$row['group_id']][$row['userid']] = $row; // Danh sách user theo nhóm
}

$crmbidding_admin_info = [];
$crmbidding_admin_info['in_groups'] = [];
if (!empty($array_user_gr[$admin_info['admin_id']])) {
    $crmbidding_admin_groups = $array_user_gr[$admin_info['admin_id']];
    $crmbidding_admin_info = $array_groups_users[$admin_info['admin_id']];
    foreach ($crmbidding_admin_groups as $group_id => $info) {
        $crmbidding_admin_info['in_groups'][] = $group_id;
    }
}

// Quyền xem danh sách telepro
$crmbidding_admin_info['allowed_telepro'] = in_array(Group::GROUP_MARKETING, $crmbidding_admin_info['in_groups']);

if (defined('NV_IS_SPADMIN') or $crmbidding_admin_info['allowed_telepro']) {
    $allow_func[] = 'telepro-import';
}

if (defined('NV_IS_SPADMIN')) {
    $allow_func[] = 'imapstat';
    $allow_func[] = 'telepro-jobs';
    $allow_func[] = 'telepro-logs';
}

$allow_func[] = 'econtract';
$allow_func[] = 'econtract_content';
$allow_func[] = 'econtract_detail';
$allow_func[] = 'econtract_draft';
$allow_func[] = 'einvoice';

$submenu['add'] = $nv_Lang->getModule('add');
$submenu['leads'] = $nv_Lang->getModule('leads');
$submenu['leadsnew'] = $nv_Lang->getModule('leadsnew');
$submenu['opportunities'] = $nv_Lang->getModule('opportunities');

$submenu['organizations'] = $nv_Lang->getModule('organizations');
$submenu['contact'] = $nv_Lang->getModule('contact');

$submenu['payment'] = $nv_Lang->getModule('payment_crm');
$submenu['econtract'] = [
    'title' => $nv_Lang->getModule('econtract'),
    'submenu' => [
        'econtract_draft' => $nv_Lang->getModule('econtract_draft_title')
    ]
];
$submenu['einvoice'] = $nv_Lang->getModule('einvoice');
$submenu['customs'] = $nv_Lang->getModule('customs');
$submenu['label'] = $nv_Lang->getModule('menu_label');
$submenu['manage_customer'] = $nv_Lang->getModule('manage_customer');

$menu_setting = [];
$menu_setting['static'] = $nv_Lang->getModule('static');
$menu_setting['static_renewal'] = $nv_Lang->getModule('static_renewal');
$menu_setting['static_groups_lead'] = $nv_Lang->getModule('static_groups_lead');
$menu_setting['static_tytrong_doanhthu'] = $nv_Lang->getModule('static_tytrong_doanhthu');
$menu_setting['static_renewal_chart'] = $nv_Lang->getModule('static_renewal_chart');
$menu_setting['chart'] = $nv_Lang->getModule('chart');
$menu_setting['growth_chart'] = $nv_Lang->getModule('growth_chart');
$menu_setting['revenue_growth_package'] = $nv_Lang->getModule('revenue_growth_package');

$submenu['static'] = [
    'title' => $nv_Lang->getModule('static'),
    'submenu' => $menu_setting
];

if (defined('NV_IS_SPADMIN')) {
    $menu_setting = [];
    $menu_setting['import'] = $nv_Lang->getModule('import');
    $menu_setting['export'] = $nv_Lang->getModule('export');
    $menu_setting['telepro'] = $nv_Lang->getModule('telepro');
    $menu_setting['mobiphone'] = $nv_Lang->getModule('mobiphone');
    $menu_setting['bni'] = $nv_Lang->getModule('bni');
    $submenu['import'] = [
        'title' => $nv_Lang->getModule('database'),
        'submenu' => $menu_setting
    ];

    $menu_setting = [];
    $menu_setting['groups'] = $nv_Lang->getModule('groups');
    $menu_setting['groups_leads'] = $nv_Lang->getModule('groups_leads');
    // chỉ tuyenhv chỉnh sửa thứ tự chia đơn
    // bổ sung đại và giang, xóa trên site chính đi
    if ($admin_info['userid'] == '8223' or $admin_info['userid'] == '15304' or $admin_info['userid'] == '35704' or $admin_info['userid'] == '47866') {
        $menu_setting['config_sale'] = $nv_Lang->getModule('config_sale');
        if ($admin_info['userid'] == '8223') {
            $menu_setting['recovery_admin_leads'] = $nv_Lang->getModule('recovery_admin_leads');
            $menu_setting['recovery_admin_vip'] = $nv_Lang->getModule('recovery_admin_vip');
        }
    }

    $submenu['config'] = [
        'title' => $nv_Lang->getModule('config'),
        'submenu' => $menu_setting
    ];
} elseif ($crmbidding_admin_info['allowed_telepro']) {
    // Sale thường có quyền xem telepro
    $submenu['telepro'] = $nv_Lang->getModule('telepro');
}

$imaplist_sub = [
    'imaplist&amp;viewlist=1' => $nv_Lang->getModule('imaplist_all')
];
if (defined('NV_IS_SPADMIN')) {
    $imaplist_sub['imapstat'] = $nv_Lang->getModule('imapstat');
}

$submenu['imaplist'] = [
    'title' => $nv_Lang->getModule('imaplist'),
    'submenu' => $imaplist_sub
];
$submenu['tawkto'] = $nv_Lang->getModule('khach_tawkto');
$submenu['list-zalo'] = $nv_Lang->getModule('list_zalo');
$submenu['chatgpt'] = $nv_Lang->getModule('khach_chatgpt');

$is_leader = 0;
if (!empty($crmbidding_admin_info['is_leader']) and $crmbidding_admin_info['is_leader'] == 1) {
    $is_leader = 1;
}

$submenu['voice_cloud'] = $nv_Lang->getModule('voicecloud');
$allow_func[] = 'voice_cloud';

if (defined('NV_IS_SPADMIN') or $is_leader == 1) {
    $submenu['list_mobiphone'] = $nv_Lang->getModule('khach_mobiphone');
    $allow_func[] = 'list_mobiphone';
}

if (defined('NV_IS_SPADMIN')) {
    $allow_func[] = 'statistics_interactive';
    $allow_func[] = 'stat_trivial';
    $allow_func[] = 'stat_login';
    $sub_statistics = [];
    $sub_statistics['stat_trivial'] = $nv_Lang->getModule('stat_menu');
    $sub_statistics['stat_login'] = $nv_Lang->getModule('stat_login');
    $sub_statistics['statistics_interactive'] = $nv_Lang->getModule('statistics_interactive');
    $submenu['stat_trivial'] = [
        'title' => $nv_Lang->getModule('imapstat'),
        'submenu' => $sub_statistics
    ];
}

$submenu['leads_log'] = $nv_Lang->getModule('leads_log');
$allow_func[] = 'leads_log';

$submenu['statics_click_banners'] = $nv_Lang->getModule('statics_click_banners');
$allow_func[] = 'statics_click_banners';
