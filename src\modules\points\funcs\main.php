<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Tue, 23 Jan 2018 03:17:50 GMT
 */
if (!defined('NV_IS_MOD_POINTS')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;
use NukeViet\InForm\InForm;
use NukeViet\Dauthau\LangMulti;

$nv_BotManager->setPrivate();

if (!defined("NV_IS_USER")) {
    $url = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']), true);
    $contents = nv_theme_info($nv_Lang->getModule('login_require'), 'danger', $url);
    include (NV_ROOTDIR . "/includes/header.php");
    echo nv_site_theme($contents);
    include (NV_ROOTDIR . "/includes/footer.php");
}

if (!class_exists('nukeviet_wallet')) {
    require_once NV_ROOTDIR . '/modules/wallet/wallet.class.php';
}
$wallet = new nukeviet_wallet();
$user_money = $wallet->my_money($user_info['userid']);

if (!class_exists('nukeviet_points')) {
    require_once NV_ROOTDIR . '/modules/' . $module_file . '/points.class.php';
}
$nv_points = new nukeviet_points();

$customs_points = $nv_points->my_point($user_info['userid']);
$config_point = $nv_points->all_exchange();

$nv_points->expired($user_info['userid']);

if ($nv_Request->isset_request('checkss', 'post, get') and $nv_Request->isset_request('userid', 'post, get') and $nv_Request->isset_request('exchange', 'post, get')) {
    $checkss = $nv_Request->get_title('checkss', 'post, get', '');
    $userid = $nv_Request->get_int('userid', 'post, get', 0);
    $exchange = $nv_Request->get_int('exchange', 'post, get', 0);
    if ($checkss == md5($userid . NV_CACHE_PREFIX . $client_info['session_id'])) {
        $money_pay = $config_point[$exchange];
        if ($user_money['money_current'] < $money_pay) {
            die('ERROR_MONEY');
        }

        $message_log = get_both_lang('exchange_point');

        // thanh toán trước, cộng điểm sau
        $trans_id = $wallet->update($money_pay, 'VND', 0, $userid, json_encode($message_log), false, 0);
        if ($trans_id > 0 and $wallet->isError() == false) {
            // điểm khách mua thì theo thời hạn dc cấu hình
            $expired = NV_CURRENTTIME + ($module_config[$module_name]['expired'] * 86400);
            if ($module_config[$module_name]['expired'] > 0) {
                $message_log = [];
                $nv_Lang->changeLang('vi');
                $nv_Lang->loadModule($module_file, false, true);
                $nv_Lang->setModule('exchange_point', sprintf($nv_Lang->getModule('exchange_point_expired'), nv_date('d/m/Y', $expired)));
                $message_log['vi'] = $nv_Lang->getModule('exchange_point');

                $nv_Lang->changeLang('en');
                $nv_Lang->loadModule($module_file, false, true);
                $nv_Lang->setModule('exchange_point', sprintf($nv_Lang->getModule('exchange_point_expired'), nv_date('d/m/Y', $expired)));
                $message_log['en'] = $nv_Lang->getModule('exchange_point');
            }

            $id_trans_point = $nv_points->update(0, $exchange, $userid, json_encode($message_log), 1, 0, 0, $expired, 0, 0);

            if ($id_trans_point > 0 and $nv_points->isError() == false) {
                $id_sale_static = 0;
                // kiểm tra lại giao dịch và thống kê cho sale doanh số
                $params = [
                    'userid' => $user_info['userid']
                ];
                $CheckUserHotlead = nv_local_api('CheckUserHotlead', $params, 1, 'crmbidding');
                $CheckUserHotlead = json_decode($CheckUserHotlead, true);
                if ($CheckUserHotlead['status'] == 'success' and !empty($CheckUserHotlead['data'])) {
                    $Hotlead = $CheckUserHotlead['data'];
                    $id_sale_static = $Hotlead['caregiver_id'];
                }
                $wallet->update_transaction($trans_id, $id_trans_point, 0, $id_sale_static);
                die('OK');
            }
        }
    } else {
        die('ERROR');
    }
}
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;

$default_sto = nv_date('d/m/Y', NV_CURRENTTIME);
$default_sfrom = nv_date('d/m/Y', NV_CURRENTTIME - 7776000); //3 tháng

$array_search['search_from'] = $nv_Request->get_title('search_from', 'post,get', $default_sfrom);
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['search_from'], $m)) {
    $sfrom = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
    $base_url .= '&amp;search_from=' . $array_search['search_from'];
} else {
    $sfrom = NV_CURRENTTIME - 7776000;
    $array_search['search_from'] = $default_sfrom;
}

$array_search['search_to'] = $nv_Request->get_title('search_to', 'post,get', $default_sto);
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['search_to'], $m)) {
    $sto = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
    $base_url .= '&amp;search_to=' . $array_search['search_to'];
} else {
    $sto = NV_CURRENTTIME;
    $array_search['search_to'] = $default_sto;
}

$page = $nv_Request->get_int('page', 'get', 1);
$per_page = 20;

if (($sto - $sfrom) <= 31536000) { //1 năm
    $db->sqlreset()
        ->select('COUNT(*)')
        ->from($db_config['prefix'] . '_' . $module_data . '_log')
        ->where('userid =' . $user_info['userid'] . ' AND created_time >' . $sfrom . ' AND created_time <=' . $sto);
    $all_page = $db->query($db->sql())
        ->fetchColumn();

    $db->select('*')
        ->order('id DESC')
        ->limit($per_page)
        ->offset(($page - 1) * $per_page);
    $result = $db->query($db->sql());
    $i = $page > 1 ? (($page - 1) * $per_page) + 1 : 1;

    $arrray_log = [];
    while ($_row = $result->fetch()) {
        $_row['stt'] = $i;

        $_aray_mess = json_decode($_row['message'], true);
        if (is_array($_aray_mess)) {
            if (isset($_aray_mess[NV_LANG_DATA])) {
                $_row['message'] = $_aray_mess[NV_LANG_DATA];
            } else {
                $_row['message'] = array_shift(array_values($_aray_mess));
            }
        } else {
            $_row['message'] = $_row['message'];
        }

        $arrray_log[] = $_row;
        ++$i;
    }
} else {
    $_error = $nv_Lang->getModule('search_error');
}

// Danh sách điểm hết hạn trong tháng - Thời hạn sử dụng điểm
$page1 = $nv_Request->get_int('page1', 'post,get', 1);
$tab_active = ($nv_Request->isset_request('page1', 'get')) ? 1 : 2;
$arrray_point_expired = [];
if ($nv_Request->isset_request('ajax', 'post, get')) {
    $_start_time = strtotime(date('Y-m-01 00:00:00'));
    $_end_time = strtotime(date('Y-m-t 23:59:59'));
    $month = date('m');
    $year = date('Y');
    $db->sqlreset()
    ->select('COUNT(*)')
    ->from($db_config['prefix'] . '_' . $module_data . '_log')
    ->where('userid =' . $user_info['userid'] . ' AND status = 1 AND check_expired = 0 AND expired >= ' . $_start_time . ' AND expired <= ' . $_end_time);
    $all_page1 = $db->query($db->sql())
    ->fetchColumn();

    $db->select('*')
    ->order('id DESC')
    ->limit($per_page)
    ->offset(($page1 - 1) * $per_page);
    $result_exp = $db->query($db->sql());
    $j = $page1 > 1 ? (($page1 - 1) * $per_page) : 1;

    while ($_row_exp = $result_exp->fetch()) {
        $_row_exp['stt'] = $j;
        $_aray_mess = json_decode($_row_exp['message'], true);
        if (is_array($_aray_mess)) {
            if (isset($_aray_mess[NV_LANG_DATA])) {
                $_row_exp['message'] = $_aray_mess[NV_LANG_DATA];
            } else {
                $_row_exp['message'] = array_shift(array_values($_aray_mess));
            }
        } else {
            $_row_exp['message'] = $_row_exp['message'];
        }

        $arrray_point_expired[] = $_row_exp;
        ++$j;
    }
    $page_url_1 = $base_url;
    if ($page1 > 1) {
        $page_url_1 .= '&amp;page1=' . $page;
    }
    $base_url = [
        'link' => $page_url_1,
        'amp' => '&page1='
    ];
    betweenURLs($page1, ceil($all_page1 / $per_page), $base_url, '&amp;page1=', $prevPage, $nextPage);
    $generate_page_ex = nv_generate_page($base_url, $all_page1, $per_page, $page1);

    $contents = nv_ajax_expired_point($arrray_point_expired, $generate_page_ex, $tab_active, $page1, $per_page);
    include (NV_ROOTDIR . "/includes/header.php");
    echo $contents;
    include (NV_ROOTDIR . "/includes/footer.php");
}
// Tặng điểm
if ($nv_Request->isset_request('checkss', 'post, get') and $nv_Request->isset_request('userid', 'post, get') and $nv_Request->isset_request('num_point_give', 'post, get') and $nv_Request->isset_request('user_receive', 'post, get')) {
    $checkss = $nv_Request->get_title('checkss', 'post, get', '');
    $userid = $nv_Request->get_int('userid', 'post, get', 0);
    $num_point_give = $nv_Request->get_int('num_point_give', 'post, get', 0);
    $user_receive = $nv_Request->get_title('user_receive', 'post, get', '');
    $point_received = 0;
    if ($checkss == md5($userid . NV_CACHE_PREFIX . $client_info['session_id'])) {
        // Kiểm tra số điểm có hợp lệ hay không : nhỏ hơn số điểm tối thiểu, lớn hơn số tiền trong tài khoản
        if ($num_point_give < $module_config['points']['limit_give_points'] or $num_point_give > $customs_points['point_total']) {
            die('ERROR_POINT_INVALID');
        }
        // Kiểm tra xem có đang bật chế độ tặng điểm hay không
        if (empty($module_config['points']['use_give_points'])) {
            die('ERROR_NOT_ACTIVE');
        }
        // Kiểm tra xem email có hợp lệ hay không
        $check_email = nv_check_valid_email($user_receive, true);
        if (!empty($check_email[0])) {
            die('ERROR_EMAIL_INVALID');
        }
        $email = $check_email[1];
        // Kiểm tra người nhận có tồn tại hay không
        $sth = $db->prepare('SELECT userid FROM ' . NV_USERS_GLOBALTABLE . ' WHERE email = :email');
        $sth->bindParam(':email', $email, PDO::PARAM_STR);
        $sth->execute();
        $receive_userid = $sth->fetch();
        if (empty($receive_userid['userid'])) {
            die('ERROR_EMAIL_NOT_FOUND');
        }
        // Cập nhật điểm nhận theo tỉ lệ
        $point_received = round(($num_point_give * $module_config['points']['ratio_give_points']) / 100, 0);
        $message_give_point = sprintf($nv_Lang->getModule('message_give_point'), $email);
        $message_received_point = sprintf($nv_Lang->getModule('message_received_point'), $user_info['email']);

        // Trừ điểm người tặng
        $sub_point = $nv_points->update(0, $num_point_give, $user_info['userid'], $message_give_point, false, 0, 0, 0, 2, 1);
        // Cộng điểm người nhận
        $expired = NV_CURRENTTIME + ($module_config[$module_name]['expired'] * 86400); //tạm thời bằng thời gian của điểm nạp
        $add_point = $nv_points->update(0, $point_received, $receive_userid['userid'], $message_received_point, true, 0, 0, $expired, 2, 0);

        // Lưu thông tin người nhận vào bảng mới
        $arrInsert = [
            'site_id' => 0,
            'userid ' => $receive_userid['userid'],
            'admin_id ' => $user_info['userid'],
            'point_total' => $num_point_give, // số điểm người gửi
            'point_receive' => $point_received, // số điểm người nhận được
            'created_time' => NV_CURRENTTIME,
            'message_sender' => $message_give_point, // message nguoi gui diem
            'message_give' => $message_received_point // message nguoi nhan diem
        ];
        nv_custom_insert($arrInsert, $table = NV_POINTS_TABLE . "_log_give_points");

        // Thông báo cho người nhận điểm
        $arrMess = [
            'vi' => sprintf(LangMulti::get('vi', 'give_point'), $user_info['username'], number_format($point_received, 0, ',', '.')),
            'en' => sprintf(LangMulti::get('en', 'give_point'), number_format($point_received, 0), $user_info['username'])
        ];
        $res = InForm::insertInfrom($receive_userid['userid'], $arrMess, 'points/#log/');
        if (!$res) {
            die('ERROR');
        }

        // Gửi email cho người nhận
        $link_view_point = urlRewriteWithDomain(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name, NV_MY_DOMAIN);
        $message_mail = sprintf($nv_Lang->getModule('content_send_point'), $point_received, $user_info['email'], $link_view_point);
        $send_mail = nv_sendmail([
            $global_config['site_name'],
            $global_config['site_email']
        ], $email, $nv_Lang->getModule('subject_send_point'), $message_mail);
        if ($sub_point and $add_point) {
            die('OK');
        }
    } else {
        die('ERROR');
    }
}

// Lấy bảng giá từ các site
$cacheFile = NV_LANG_DATA . '_pointtable_' . NV_CACHE_PREFIX . '.cache';
$cacheTTL = 3600 * 6; // 6 tiếng cache
if (($cache = $nv_Cache->getItem($module_name, $cacheFile, $cacheTTL)) != false) {
    $error_message = [];
    $price_list = json_decode($cache, true);
} else {
    $price_list = [];
    $error_message = [];

    // Load site DauThau.info - Bảng giá hiển thị theo ngôn ngữ
    $site = $global_array_sites[1];

    $api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);

    $api->setModule('bidding')
        ->setLang(NV_LANG_DATA)
        ->setAction('GetPointPriceTables');
    $result_api = $api->execute();
    $error = $api->getError();

    if (!empty($error)) {
        $error_message[1] = $error;
    }

    if ($result_api['status'] != 'success') {
        $error_message[1] = $result_api['message'] ?: 'Api error with no respon';
    }

    if (NV_LANG_DATA == 'en') {
        $URL_LANG_DATA = 'en/';
        $LANG_DATA = '_en';
    } else {
        $URL_LANG_DATA = '';
        $LANG_DATA = '';
    }

    if (empty($error_message[1])) {
        $price_list[1] = $result_api['data'];
        // By Lâm: thêm đoạn lấy cấu hình điểm trừ khi bóc tin
        $point_crawl_mintus = $db->query("SELECT config_value FROM " . NV_CONFIG_GLOBALTABLE . " WHERE config_name = 'point_crawl_mintus'")->fetchcolumn();
        $price_list[1][sizeof($price_list[1])] = [
            'group' => $nv_Lang->getModule('group_feature_other' . $LANG_DATA),
            'title' => $nv_Lang->getModule('point_crawl_mintus'),
            'value' => $point_crawl_mintus . ' ' . $nv_Lang->getModule('points_1'),
            'link' => (NV_LANG_DATA == 'en')
            ? URL_DTINFO .  $URL_LANG_DATA . 'news/general-information/new-feature-of-dauthau-info-automatic-add-deduct-points-when-users-request-data-updates-289.html?ml=art-menu-4'
            : URL_DTINFO .  $URL_LANG_DATA . 'news/tin-tuc/tinh-nang-moi-cua-dauthau-info-tinh-diem-khi-nguoi-dung-yeu-cau-cap-nhat-lai-du-lieu-thau-925.html?ml=art-menu-4'
        ];
    }

    // Load site DauThau.Net - Bảng giá hiển thị theo ngôn ngữ
    $site = $global_array_sites[2];
    $api = new DoApi(NV_SERVER_PROTOCOL . '://' . $site['sitedomain'] . '/api.php', $site['api_key'], $site['api_secret']);
    $api->setModule(NV_LANG_DATA == 'vi' ? 'dn' : 'bids-profile')
        ->setLang(NV_LANG_DATA)
        ->setAction('GetPointPriceTables');
    $result_api = $api->execute();
    $error = $api->getError();

    if (!empty($error)) {
        $error_message[2] = $error;
    }
    if ($result_api['status'] != 'success') {
        $error_message[2] = $result_api['message'] ?: 'Api error with no respon';
    }
    if (empty($error_message[2])) {
        $price_list[2] = $result_api['data'];
    }

    // Ghi cache nếu gọi hết API thành công
    if (empty($error_message)) {
        $nv_Cache->setItem($module_name, $cacheFile, json_encode($price_list), $cacheTTL);
    }
}

$generate_page = nv_generate_page($base_url, $all_page, $per_page, $page);
$page_url = $base_url;
if ($page1 > 1) {
    $page_url .= '&amp;page1=' . $page;
}
$canonicalUrl = getCanonicalUrl($page_url);
$nv_Lang->setModule('note_points', sprintf($nv_Lang->getModule('note_give_point'), $module_config['points']['limit_give_points'], $module_config['points']['ratio_give_points'] . '%'));
$contents = nv_theme_point($customs_points, $user_money, $config_point, $arrray_log, $generate_page, $price_list, $error_message, $arrray_point_expired, $generate_page_ex, $tab_active, $array_search, $_error);
$page_title = $nv_Lang->getModule('point_info');
include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';

function nv_custom_insert($data, $table)
{
    global $db;
    $id = 0;
    if (!empty($data)) {
        foreach ($data as $key => $value) {
            $arrayKey[] = $key;
            $arrayInsert[] = $db->quote($value);
        }
        try {
            $sqlInsert = "INSERT INTO $table (" . implode(",", $arrayKey) . ") VALUES ( " . implode(",", $arrayInsert) . " )";

            $db->query($sqlInsert);
            $id = $db->lastInsertId();
        } catch (PDOException $e) {
            trigger_error($e);
            $id = -1;
        }
    }
    return $id;
}
