<!-- BEGIN: main -->
<div class="panel panel-default panel__success_left panel__custom">
    <div class="panel-heading">
        <h3 class="panel-title" id="ttdhkh_profile"><i class="fa fa-bookmark" aria-hidden="true"></i>  {LANG.list_profile_panel_title} <i class="fa fa-angle-double-right" aria-hidden="true"></i></h3>
    </div>

    <div class="panel-body">
        <div class="info_product info_profile">
            
                <h2 class="title_list_profile listProfile">{LANG.list_profile_title} <i class="fa fa-angle-double-right" aria-hidden="true"></i></h2>
                <div id="listProfile">
                    <table class="table table-bordered" id="table1">
                        <thead>
                            <tr>
                                <th class="text-center" width="3%">{LANG.stt}</th>
                                <th width="13%">{LANG.prof_name}</th>
                                <th width="18%">{LANG.add_day}</th>
                                <th width="18%">{LANG.active}</th>
                                <th class="text-center" width="12%">{LANG.day_expired}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- BEGIN: loop -->
                            <tr>
                                <td class="text-center">{PROFILE.stt}</td>
                                <!-- BEGIN: unconfirmed -->
                                <td><strong>{PROFILE.prof_name}</strong>
                                    {PROFILE.prof_code_view}
                                    {PROFILE.info_phone_view}
                                    {PROFILE.info_email_view}
                                </td>
                                <!-- END: unconfirmed -->
                                <!-- BEGIN: confirmed -->
                                <td>
                                    <a href="{PROFILE.link_view}" title="{PROFILE.prof_name}" target="_blank"><strong>{PROFILE.prof_name}</strong></a>
                                    {PROFILE.prof_code_view}
                                    {PROFILE.info_phone_view}
                                    {PROFILE.info_email_view}
                                </td>
                                <!-- END: confirmed -->
                                <td> {PROFILE.add_time}</td>
                                <td > {PROFILE.status_txt}</td>
                                <td class="text-center" data-label="{LANG.status}"> 
                                    <span class="label-name">{LANG.day_expired}:</span>{PROFILE.fee_expired_parse} <br /> {PROFILE.fee_status_parse}
                                </td>
                                
                            </tr>
                            <!-- END: loop -->
                        </tbody>
                        <!-- BEGIN: generate_page -->
                        <tfoot>
                            <tr>
                                <td colspan="5"><div class="text-center pagination">{NV_GENERATE_PAGE}</div></td>
                            </tr>
                        </tfoot>
                        <!-- END: generate_page -->
                    </table>
                </div>
        </div>					
    </div>
</div>

<!-- BEGIN: generate_page_js -->
<script type="text/javascript">
	$(document).ready(function($) {
        var listProfile_page_selector = '.info_profile .pagination';
        //phân trang API ListProfile 
        $(listProfile_page_selector + ' .page-item').not('.active, .disabled').on('click', function (e) {
            e.preventDefault();
            var $this = $(this);
		    ajax_API_pagination($this, callAPI, 'ListProfile', '#show_api_list_profile', '{LANG.err_list_profile_dtnet}')
        })
	});
    function ajax_API_pagination(selector, callBackAPI, apiName, resultLocation, errorMess) {
        //phân trang API 
        var page_href = selector.children(".page-link").attr('href');
        if (page_href.includes('page=')) {
            var start = page_href.lastIndexOf('page=');
            var page_string = page_href.substring(start);
            var page = page_string.split('=');
            callBackAPI(apiName, resultLocation, errorMess, page[1]);
        } else {
            callBackAPI(apiName, resultLocation, errorMess, 1);
        }
    }
</script>
<!-- END: generate_page_js -->
<script>
$(document).ready(function($) {
    $(".title_list_profile").click(function(event) {
        const id = $(this).attr('class').split(' ')[1];
        $('#' + id).slideToggle(500);
        // if (id == 'listOrder') {
        //     $(this).find('i').toggleClass('iconUp');
        // } else {
            $(this).find('i').toggleClass('iconDown');
        // }
    });
    $("#ttdhkh_profile").click(function(event) {
        $(this).find('.fa-angle-double-right').toggleClass('iconDown');
        console.log( $(this).parent().parent().find('.panel-body').slideToggle(400));
    });
})
</script>
<!-- END: main -->
