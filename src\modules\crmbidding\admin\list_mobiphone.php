<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$showheader = $nv_Request->get_int('showheader', 'post,get', 1);
$q = $nv_Request->get_title('q', 'post,get', '');

// Fetch Limit
$per_page = 50;
$page = $nv_Request->get_title('page', 'post,get', 1);
$db->sqlreset()
    ->group('sdt')
    ->from('' . NV_PREFIXLANG . '_' . $module_data . '_mobiphone');
$where = "";
if (!empty($q)) {
    $where = 'sdt LIKE ' . $db->quote('%' . $q . '%') . '';
}

if (!empty($where)) {
    $db->where($where);
}

$order = 'thoi_gian_bat_dau DESC';
$db->select('*, MIN(thoi_gian_bat_dau) as thoi_gian_bat_dau')
    ->group('sdt')
    ->order($order)
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$sth = $db->prepare($db->sql());

$_sql = "SELECT sdt FROM " . NV_PREFIXLANG . "_" . $module_data . "_mobiphone ";
if (!empty($where)) {
    $_sql .= " WHERE " . $where;
}
$_sql .= " GROUP BY sdt";

$num_items_arr = $db->query($_sql)->fetchAll();
$num_items = sizeof($num_items_arr);

$sth->execute();
$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('Q', $q);

$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&showheader=' . $showheader;
if (!empty($q)) {
    $base_url .= '&q=' . $q;
}
$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
if (!empty($generate_page)) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}
$number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;
while ($view = $sth->fetch()) {
    if (!empty($view['sdt']) or !empty($view['email'])) {
        $_sql = "SELECT chuyen_vien FROM " . NV_PREFIXLANG . "_" . $module_data . "_mobiphone WHERE sdt = " . $view['sdt'];
        $result = $db->query($_sql);
        $list_chuyen_vien = $result->fetchAll();
        if (!empty($list_chuyen_vien)) {
            foreach ($list_chuyen_vien as $chuyen_vien) {
                $xtpl->assign('CHUYEN_VIEN', $chuyen_vien['chuyen_vien']);
                $xtpl->parse('main.loop.chuyen_vien');
            }
        }
        $xtpl->parse('main.loop.status_leads');
    }
    $view['thoi_gian_bat_dau'] = nv_date('H:i d/m/Y', $view['thoi_gian_bat_dau']);
    $view['link_detail'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=detail_mobiphone&showheader=' . $showheader . '&sdt=' . $view['sdt'];
    $view['number'] = $number++;
    $xtpl->assign('VIEW', $view);
    $xtpl->parse('main.loop');
}

if ($showheader) {
    $xtpl->parse('main.search');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');
$page_title = $nv_Lang->getModule('khach_mobiphone');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';
