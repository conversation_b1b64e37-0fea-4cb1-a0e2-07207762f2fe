<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\crmbidding\Api;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

class CreateLogActivityCustom implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'history';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $row['userid'] = $nv_Request->get_int('userid', 'post', 0);
        $row['bid'] = $nv_Request->get_int('bid', 'post', 0);
        $row['content'] = $nv_Request->get_title('content', 'post', '');
        $row['time_click'] = $nv_Request->get_int('time_click', 'post', NV_CURRENTTIME);
        $row['type'] = $nv_Request->get_int('type', 'post', 1);
        $row['url'] = $nv_Request->get_title('url', 'post', '');
        if (!empty($row['userid']) and !empty($row['content']) and !empty($row['time_click'])) {
            try {
                $stmt = $db->prepare("INSERT INTO nv4_history_activity_custom (
                    userid, bid, content, time_click, type, url
                ) VALUES (
                    :userid, :bid, :content, :time_click, :type, :url
                )");
                $stmt->bindParam(':userid', $row['userid'], PDO::PARAM_INT);
                $stmt->bindParam(':bid', $row['bid'], PDO::PARAM_INT);
                $stmt->bindParam(':content', $row['content'], PDO::PARAM_STR);
                $stmt->bindParam(':time_click', $row['time_click'], PDO::PARAM_INT);
                $stmt->bindParam(':type', $row['type'], PDO::PARAM_INT);
                $stmt->bindParam(':url', $row['url'], PDO::PARAM_STR);
                $exc = $stmt->execute();
                $id = $db->lastInsertId();
                if ($exc and $id > 0) {
                    $this->result->setSuccess();
                    $this->result->set('logid', $id);
                } else {
                    $this->result->setError()
                        ->setCode('4001')
                        ->setMessage('Not response');
                }
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode('3000')
                ->setMessage('Thông tin không đầy đủ')
                ->getResult();
        }
        return $this->result->getResult();
    }
}
