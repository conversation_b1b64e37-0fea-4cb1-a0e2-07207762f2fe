<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_SITEINFO')) {
    die('Stop!!!');
}
$data['is_link'] = 0;
if (isset($data['content']['type']) and $data['content']['type'] == 1) {
    $data['title'] = sprintf($nv_Lang->getModule('notification_new_opportunities'), $data['content']['title']);
    $data['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $data['module'] . '&amp;' . NV_OP_VARIABLE . '=opportunities_info&amp;id=' . $data['obid'];
} elseif (isset($data['content']['type']) and $data['content']['type'] == 2) {
    $data['title'] = sprintf($nv_Lang->getModule('notification_chatgpt'), $data['content']['title']);
    $data['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $data['module'] . '&amp;' . NV_OP_VARIABLE . '=chatgpt_detail&amp;id=' . $data['obid'];
} elseif (isset($data['content']) and $data['type'] == 'activity_user') {
    if ($data['content']['type'] == 'leads_info') {
        $type = 'leads';
        $data['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $data['module'] . '&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $data['content']['leads'];
    } else {
        $type =  'cơ hội';
        $data['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $data['module'] . '&amp;' . NV_OP_VARIABLE . '=opportunities_info&amp;id=' . $data['content']['opportunities'];
    }
    $data['is_link'] = 1;
    $data['title'] = sprintf($nv_Lang->getModule('notification_sale'), $data['content']['title'], $type);
    $data['title_noti'] = sprintf($nv_Lang->getModule('notification_sale_link'), $data['content']['title'], $data['link'], $type);
} elseif (isset($data['content']['type']) and $data['content']['type'] == 3) {
    $data['title'] = $data['content']['content'];
    $data['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $data['module'] . '&amp;' . NV_OP_VARIABLE . '=econtract_detail&amp;id=' . $data['obid'];
} else {
    $data['title'] = sprintf($nv_Lang->getModule('notification_new_leads'), $data['content']['title']);
    $data['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $data['module'] . '&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $data['obid'];
}
