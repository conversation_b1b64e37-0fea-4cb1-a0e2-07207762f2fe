<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\elink\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdatePromotionCode implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'promotion';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        require_once NV_ROOTDIR . '/modules/' . $module_file . '/init.php';

        $array = [];

        // Lấy các biến để xác định được đơn hàng
        $array['id'] = $nv_Request->get_absint('id', 'post', 0);
        $array['site_id'] = $nv_Request->get_absint('site_id', 'post', 0);
        $array['order_mod'] = $nv_Request->get_title('order_mod', 'post', '');
        $array['order_id'] = $nv_Request->get_title('order_id', 'post', '');

        // Xác định đơn hàng cần cập nhật
        if (!empty($array['id'])) {
            $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_orders WHERE id=" . $array['id'];
        } else {
            $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_orders
            WHERE site_id=" . $array['site_id'] . " AND order_mod=" . $db->quote($array['order_mod']) . " AND
            order_id=" . $db->quote($array['order_id']);
        }
        $order = $db->query($sql)->fetch();
        if (empty($order)) {
            return $this->result->setCode('1001')->setMessage($nv_Lang->getModule('apierr_exists_order'))->getResult();
        }

        $fields = [
            'money_amount' => 'double',
        ];

        // Dữ liệu cập nhật
        $data = [];
        foreach ($fields as $field => $field_type) {
            if ($nv_Request->isset_request($field, 'post')) {
                if ($field_type == 'double') {
                    $data[$field] = $nv_Request->get_float($field, 'post', 0);
                    if ($data[$field] < 0) {
                        $data[$field] = 0;
                    }
                }
            }
        }
        if (empty($data)) {
            return $this->result->setCode('1002')->setMessage($nv_Lang->getModule('apierr_update_datar'))->getResult();
        }

        try {
            $update = [];
            foreach ($data as $field => $value) {
                if ($fields[$field] == 'double') {
                    $update[] = $field . '=' . $value;
                } else {
                    $update[] = $field . '=' . $db->quote($value);
                }
            }

            $sql = "UPDATE " . $db_config['prefix'] . "_" . $module_data . "_orders SET " . implode(', ', $update) . " WHERE id=" . $order['id'];
            $db->query($sql);

            $this->result->setSuccess();
        } catch (\Exception $e) {
            trigger_error(print_r($e, true));
            return $this->result->setCode('1003')->setMessage($e->getMessage())->getResult();
        }

        return $this->result->getResult();
    }
}
