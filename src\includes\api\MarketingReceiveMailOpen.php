<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Api;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class MarketingReceiveMailOpen implements IApi
{
    private $result;

    public $contractor = [];

    public $bid_result = [];

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'marketing';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $nv_Request, $db, $nv_Lang;

        $nv_Lang->changeLang('vi');
        $nv_Lang->loadModule('crmbidding', false, true);

        $array = [];

        // ID bản ghi sự kiện này bên marketing ghi nhận
        // $array['id'] = $nv_Request->get_absint('id', 'post', 0);

        // Sự kiện: 1 trong 2 giá trị này mailopen:every và mailopen:first
        // $array['hook'] = $nv_Request->get_title('hook', 'post', '');

        // ID bản ghi email bên marketing lưu
        // $array['email_id'] = $nv_Request->get_absint('email_id', 'post', 0);

        // ID chiến dịch đã chạy
        // $array['campaign_id'] = $nv_Request->get_absint('campaign_id', 'post', 0);

        // Thời gian sự kiện này xảy ra
        // $array['time'] = $nv_Request->get_absint('time', 'post', 0);

        // Thông tin liên hệ trước đó đã đẩy qua cho marketing, dạng array
        $array['contact'] = $nv_Request->get_string('contact', 'post', '', true, false);
        $array['contact'] = json_decode($array['contact'], true) ?: [];

        $contact_details = json_decode($array['contact']['details'], true) ?: [];
        $this->contractor = $contact_details;
        $contact_email = $contact_details['org_email'];
        $winning_histories = json_decode($contact_details['winning_history'], true) ?: [];
        foreach ($winning_histories as $history) {
            if ($history['winning'] == $contact_details['winning']) {
                $this->bid_result = $history;
            }
        }

        $source_leads = 12; //Marketing
        $admin_id = 5; //admin
        $module = 'crmbidding';

        /*Kiểm tra leads có hay chưa*/
        $where = [];
        $where['OR'][] = [
            '=' => [
                'email' => $contact_email
            ]
        ];
        $where['OR'][] = [
            'FIND_IN_SET' => [
                'sub_email' => $contact_email
            ]
        ];

        $order = [];
        $order['updatetime'] = 'DESC';

        $params_read = [
            'where' => $where,
            'order' => $order
        ];

        $read = nv_local_api('ListAllLeads', $params_read, $admin_id, $module);
        $readLeads = json_decode($read, true);
        if ($readLeads['status'] == 'success' && !empty($contact_details)) {
            if ($readLeads['code'] == 4000) {
                //Tạo lead
                $taxcode = str_replace("vnz", "", $contact_details['org_code']);
                $taxcode = str_replace("vn", "", $taxcode);
                $tax_code = empty($contact_details['tax_code']) ? $taxcode : $contact_details['tax_code'];
                $params_create = [
                    'source_leads' => $source_leads,
                    'name' => $contact_details['org_fullname'],
                    'email' => $contact_email,
                    'otherdata' => [
                        'tax' => $tax_code
                    ]
                ];
                $create = nv_local_api('CreateLeads', $params_create, $admin_id, $module);
                $createLeads = json_decode($create, true);
                if ($createLeads['status'] == 'success') {
                    $this->insert_log($createLeads['leadsid'], 1);
                    $this->result->setSuccess()->setMessage('Create success');
                } else {
                    $this->result->setError()->setCode('1000')->setMessage($createLeads['message']);
                }
            } else {
                //Update lead
                $dataLeads = array_shift($readLeads['data']);
                $data_update = [
                    'status' => $dataLeads['status']
                ];
                if ($dataLeads['status'] == 3) {
                    $data_update['status'] = 1;
                }
                $params_update = [
                    'leadsid' => $dataLeads['id'],
                    'admin_id' => $admin_id,
                    'data' => $data_update
                ];
                $update = nv_local_api('UpdateLeads', $params_update, $admin_id, $module);
                $updateLeads = json_decode($update, true);
                if ($updateLeads['status'] == 'success') {
                    if ($dataLeads['opportunities_id'] != 0) {
                        $this->update_opportunities($dataLeads['opportunities_id']);
                    } elseif ($dataLeads['caregiver_id'] != 0) {
                        $this->notify_to_caregiver($dataLeads, 0);
                        $this->insert_log($dataLeads['id'], 2);
                    } else {
                        $this->insert_log($dataLeads['id'], 2);
                    }
                    $this->result->setSuccess()->setMessage('Update success');
                } else {
                    $this->result->setError()->setCode('2000')->setMessage($updateLeads['message']);
                }
            }
        } else {
            $this->result->setError()->setCode('3000')->setMessage($readLeads['message']);
        }

        $nv_Lang->changeLang(NV_LANG_INTERFACE);
        return $this->result->getResult();
    }

    /**
     *
     * @return null
     */
    public function update_opportunities($opportunitiesid)
    {
        $admin_id = 5; //admin
        $module = 'crmbidding';

        $where = [];
        $where['AND'][] = [
            '=' => [
                'id' => $opportunitiesid
            ]
        ];

        $params_read = [
            'where' => $where
        ];

        $read = nv_local_api('ListAllOpportunities', $params_read, $admin_id, $module);
        $readOps = json_decode($read, true);
        if ($readOps['status'] == 'success' && $readOps['code'] != 4000) {
            $dataOps = array_shift($readOps['data']);
            $data_update = [
                'status' => $dataOps['status']
            ];
            if ($dataOps['status'] == 3) {
                $data_update['status'] = 1;
            }
            $params_update = [
                'opportunitiesid' => $opportunitiesid,
                'admin_id' => $admin_id,
                'data' => $data_update
            ];
            $update = nv_local_api('UpdateOpportunities', $params_update, $admin_id, $module);
            $updateOps = json_decode($update, true);
            if ($updateOps['status'] == 'success' && $dataOps['caregiver_id'] != 0) {
                $this->notify_to_caregiver($dataOps, 1);
                $this->insert_log($opportunitiesid, 3);
            }
        }
    }

    /**
     *
     * @return null
     */
    public function notify_to_caregiver($data, $type)
    {
        global $global_config;

        $global_config['module_theme'] = $global_config['site_theme'] ?? 'dauthau';
        include NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/theme.php';

        $admin_id = 5; //admin
        $notify_title = $data['name'] ?: '';
        $notify_obid = $data['id'] ?: 0;
        $caregiver_id = $data['caregiver_id'] ?: 0;
        $bid_result = $this->bid_result;
        $contractor = $this->contractor;

        if ($type == 1) {
            $notify_type = 'new_opportunities';
            $type_title = "Cơ hội";
            $link_leads = NV_MY_DOMAIN . NV_BASE_ADMINURL . 'index.php?language=vi&nv=crmbidding&op=opportunities_info&id=' . $data['id'] . '&showheader=1';
        } else {
            $notify_type = 'new_leads';
            $type_title = "Thành viên";
            $link_leads = NV_MY_DOMAIN . NV_BASE_ADMINURL . 'index.php?language=vi&nv=crmbidding&op=leads_info&id=' . $data['id'] . '&showheader=1';
        }

        $subject = sprintf("Nhà thầu %s vừa trúng thầu và được giao cho bạn chăm sóc", $contractor['org_fullname']);
        $message = sprintf(
            "<p>%s vừa trúng thầu gói %s với giá %s, Hệ thống tự động chuyển đổi thành lead và giao cho bạn chăm sóc vì nhận thấy trước đây bạn đã từng liên hệ với khách hàng. Hãy kiểm tra và chăm sóc khách ngay nhé!</p>
            <p>Xem kết quả gói thầu của khách tại: %s</p>
            <p>Kiểm tra lại lead trên CRM của bạn tại đây: %s</p>",
            $contractor['org_fullname'],
            $bid_result['ten_goi_thau'],
            $bid_result['gia_trung_thau'],
            $bid_result['link_kqlcnt'],
            $link_leads
        );

        //Lấy thông tin người chăm sóc
        $params = [
            'userid' => $caregiver_id
        ];
        $caregiver = nv_local_api('GetUser', $params, $admin_id, 'users');
        $caregiverData = json_decode($caregiver, true);
        $caregiver_email = $caregiverData['userinfo']['email'] ?: $global_config['site_email'];
        // $caregiver_email = '<EMAIL>';

        //Gửi email cho người chăm sóc
        @nv_sendmail([
            $global_config['site_name'],
            $global_config['site_email']
        ], $caregiver_email, $subject, $message);

        //Gửi thông báo cho người chăm sóc
        nv_insert_notification('crmbidding', $notify_type, array(
            'title' => $notify_title,
            'type' => $type
        ), $notify_obid, $caregiver_id, 0, 1, 0);
    }

    /**
     *
     * @return null
     */
    public function insert_log($id, $type)
    {
        $admin_id = 5; //admin
        $contractor = $this->contractor;
        $bid_result = $this->bid_result;

        if ($type == 1) {
            $log_key = 'LOG_ADMIN_UPDATE_LEADS_INFO';
            $leads_id = $id;
            $oppotunities_id = 0;
        } elseif ($type == 2) {
            $log_key = 'LOG_ADMIN_UPDATE_LEADS_INFO';
            $leads_id = $id;
            $oppotunities_id = 0;
        } elseif ($type == 3) {
            $log_key = 'LOG_ADMIN_UPDATE_OPPOTUNITIES_INFO';
            $leads_id = 0;
            $oppotunities_id = $id;
        }

        $log_data = [
            'Tìm thấy thông tin tham gia thầu trên MSC'
        ];
        $log_data[] = [
            'Nhà thầu ' . $contractor['org_fullname'] . ' có email ' . $contractor['org_email'] . ' đã trúng gói thầu ' . $bid_result['ten_goi_thau']
        ];

        $log = [];
        $log['log_area'] = 1;
        $log['log_key'] = $log_key;
        $log['log_time'] = NV_CURRENTTIME;
        $log['log_data'] = $log_data;
        $log['leads_id'] = $leads_id;
        $log['oppotunities_id'] = $oppotunities_id;

        nv_local_api('CreateAllLogs', $log, $admin_id, 'crmbidding');
    }

    public function debug($debug_data)
    {
        file_put_contents(NV_ROOTDIR . '/debug.log', print_r($debug_data, true), LOCK_EX);
    }
}
