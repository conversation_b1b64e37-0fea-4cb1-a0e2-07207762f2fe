<?php
/*
 * thống kê các thông số chăm sóc, n<PERSON><PERSON> tiền, điểm của sale
 * Chạy 1 ngày 1 lần vào 23h50
 */
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);

define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));
require NV_CONSOLE_DIR . '/server.php';
require NV_ROOTDIR . '/includes/mainfile.php';

ob_end_clean();

define('NV_ADMIN', true);
if ($sys_info['ini_set_support']) {
    set_time_limit(0);

    if ((int) ini_get('memory_limit') < 512) {
        ini_set('memory_limit', '512M');
    }
}
$static_sale_log = NV_ROOTDIR . '/data/logs/static_sale/' . date('Ymd') . '.log';
$static_sale_file = NV_ROOTDIR . '/data/logs/static_sale.txt';

try {
    $userid_run = 0;
    if (file_exists(NV_CONSOLE_DIR . '/static_sale.txt')) {
        $date_from_string = file_get_contents(NV_CONSOLE_DIR . '/static_sale.txt'); // 03/06/2023
        $date_from_string = explode('/', $date_from_string);
        $date_from = mktime(0, 0, 0, $date_from_string[1], $date_from_string[0], $date_from_string[2]);
        $date_to = mktime(0, 0, 0, date('m'), date('j'), date('Y'));
    } else if (file_exists($static_sale_file)) {
        // file cấu hình muốn chạy lại từng ngày, từng tháng, từng sale,
        // được tạo ở trang thống kê bên crm phục vụ nhu cầu muốn chạy lại 1 khoảng thời gian nào đó
        // có dạng json
        /*
         * $array['from'] = 0;
         * $array['to'] = 0;
         * $array['userid'] = 0;
         */
        $static_sale_file_content = file_get_contents($static_sale_file);
        $static_sale_file_content = json_decode($static_sale_file_content, true);

        if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $static_sale_file_content['time_from'], $m)) {
            $date_from = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
        } else {
            $date_from = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
        }
        if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $static_sale_file_content['time_to'], $m)) {
            $date_to = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
        } else {
            $date_to = mktime(23, 59, 59, nv_date('m', NV_CURRENTTIME), nv_date('d', NV_CURRENTTIME), nv_date('Y', NV_CURRENTTIME));
        }

        $userid_run = $static_sale_file_content['admin_id'] > 0 ? $static_sale_file_content['admin_id'] : 0;
    } else {
        $date_from = mktime(0, 0, 0, date('m'), date('d'), date('Y'));
        $date_to = mktime(0, 0, 0, date('m'), date('j'), date('Y'));
    }

    $sql_sale = 'SELECT admin_id FROM nv4_authors WHERE is_suspend = 0';
    $result_sale = $db->query($sql_sale);
    $list_id_sale = [];
    while ($row_sale = $result_sale->fetch()) {
        $list_id_sale[] = $row_sale['admin_id'];
    }
    $array_admin = $list_id_sale;

    // các tài khoản test mà k muốn thống kê đưa vào đây, lấy theo nhóm test https://id.dauthau.net/qtdtvnds/index.php?language=vi&nv=users&op=groups&userlist=19
    $sql = 'SELECT userid FROM ' . NV_GROUPS_GLOBALTABLE . '_users WHERE group_id = 19';
    $result = $db->query($sql);
    while ($_gr_user_info = $result->fetch()) {
        $list_id_sale[$_gr_user_info['userid']] = $_gr_user_info['userid'];
    }

    $list_id_sale = implode(',', $list_id_sale);

    $array_order = [
        'weight' => 'ASC'
    ];
    $params = [
        'order' => $array_order
    ];

    $arr_user = [];
    $result_api = nv_local_api('ListGroupsUsers', $params, 1, 'crmbidding');
    $result_api = json_decode($result_api, true);
    if ($result_api['status'] == 'success') {
        if ($result_api['code'] != 4000) {
            $_rows = $result_api['data'];
            foreach ($_rows as $row) {
                if ($userid_run > 0 and $row['userid'] != $userid_run) { // chỉ chạy thống kê sale dc chỉ định
                    continue;
                }
                if (in_array($row['userid'], $array_admin)) { // chỉ tính các admin còn hạn
                    $arr_user[] = $row;
                }
            }
        }
    } else {
        // Hiển thị lỗi
        file_put_contents($static_sale_log, "\n\n ---------- [" . date('d-m-Y H:i:s') . "] Lỗi API ListGroupsUsers: \n" . print_r($result_api, true) . "\n", FILE_APPEND);
        die();
    }
    $i = $date_to;
    while ($i >= $date_from) {
        $date = mktime(0, 0, 0, date('m', $i), date('d', $i), date('Y', $i));
        $todate = mktime(23, 59, 59, date('m', $date), date('d', $date), date('Y', $date));
        $i = $i - 86400;
        echo "\n Bắt đầu chạy thống kê từ ngày: " . date('d/m/Y H:i:s', $date) . '-' . date('d/m/Y H:i:s', $todate);
        file_put_contents($static_sale_log, "\n Bắt đầu chạy thống kê từ ngày: " . date('d/m/Y H:i:s', $date) . '-' . date('d/m/Y H:i:s', $todate), FILE_APPEND);
        if (!empty($arr_user)) {
            foreach ($arr_user as $row) {
                echo "\n Userid: " . $row['userid'];
                file_put_contents($static_sale_log, "\n Userid: " . $row['userid'], FILE_APPEND);
                $row['config_percent'] = json_decode($row['config_percent'], true);
                $row['total_leads'] = 0;
                $row['total_opportunities'] = 0;
                $where_leads = [];
                $where_leads['AND'][] = [
                    '>=' => [
                        'updatetime' => $date
                    ]
                ];
                $where_leads['AND'][] = [
                    '=' => [
                        'caregiver_id' => $row['userid']
                    ]
                ];
                $where_leads['AND'][] = [
                    '<=' => [
                        'updatetime' => $todate
                    ]
                ];
                $params_leads = [
                    'page' => 1,
                    'perpage' => 1,
                    'where' => $where_leads
                ];

                $total_leads = nv_local_api('ListAllLeads', $params_leads, 1, 'crmbidding');
                $total_leads = json_decode($total_leads, true);
                if (!empty($total_leads['total'])) {
                    $row['total_leads'] = intval($total_leads['total']);
                }

                // $total_opportunities = $db->query('SELECT COUNT(id) as total_opportunities FROM ' . NV_PREFIXLANG . '_crmbidding_opportunities WHERE active=1 AND updatetime >= ' . $date . ' AND updatetime <=' . $todate . ' AND caregiver_id = ' . $row['userid'])->fetchColumn();
                $where_opportunities = [];
                $where_opportunities['AND'][] = [
                    '>=' => [
                        'updatetime' => $date
                    ]
                ];
                $where_opportunities['AND'][] = [
                    '<=' => [
                        'updatetime' => $todate
                    ]
                ];
                $where_opportunities['AND'][] = [
                    '=' => [
                        'caregiver_id' => $row['userid']
                    ]
                ];
                $params_opportunities = [
                    'page' => 1,
                    'perpage' => 1,
                    'where' => $where_opportunities
                ];

                $total_opportunities = nv_local_api('ListAllOpportunities', $params_opportunities, 1, 'crmbidding');
                $total_opportunities = json_decode($total_opportunities, true);
                if (!empty($total_opportunities['total'])) {
                    $row['total_opportunities'] = intval($total_opportunities['total']);
                }

                // lây ra các giao dịch nằm trong ngày đó và có người giao dịch tương ứng
                $data = [
                    'from' => $date,
                    'to' => $todate,
                    'userid' => $row['userid'],
                    'except_list' => $list_id_sale
                ];
                $result_api_wallet = nv_local_api('CalculateDepositAmount', $data, 1, 'wallet');
                $result_api_wallet = json_decode($result_api_wallet, true);
                $total_wallet = [];
                if ($result_api_wallet['status'] == 'success') {
                    $total_wallet = $result_api_wallet['data'];
                } else {
                    file_put_contents($static_sale_log, "\n\n ---------- [" . date('d-m-Y H:i:s') . "] Lỗi API calculateDepositAmount: \n" . $result_api_wallet . "\n", FILE_APPEND);
                    die();
                }
                // LẤY RA tổng số tiền có trạng thái Xử lí số liệu lệch
                $data = [
                    'from' => $date,
                    'to' => $todate,
                    'userid' => $row['userid'],
                    'except_list' => $list_id_sale
                ];
                $result_api_wallet1 = nv_local_api('CalculateHandleSkewedDataAmount', $data, 1, 'wallet');
                $total_wallet1 = [];
                $result_api_wallet1 = json_decode($result_api_wallet1, true);
                if ($result_api_wallet1['status'] == 'success') {
                    $total_wallet1 = $result_api_wallet1['data'];
                } else {
                    file_put_contents($static_sale_log, "\n\n ---------- [" . date('d-m-Y H:i:s') . "] Lỗi API calculateDepositAmount: \n" . $result_api_wallet1 . "\n", FILE_APPEND);
                    die();
                }

                $row['recharge_day'] = intval($total_wallet['recharge_day']);
                $row['total_day'] = intval($total_wallet['total_day']) - intval($total_wallet1['total_day']);
                $row['percent_bonus'] = 10;
                if (!empty($row['config_percent'])) {
                    foreach ($row['config_percent'] as $config_percent) {
                        if ($date_to >= $config_percent['date_from'] and $date_to <= $config_percent['date_to']) {
                            if (!empty($config_percent['percent_wallet']) and $config_percent['percent_wallet'] > 0) {
                                $row['percent_bonus'] = $config_percent['percent_wallet'];
                                break;
                            }
                        }
                    }
                }
                $row['money_bonus'] = ($row['total_day'] * $row['percent_bonus']) / 100;

                // Thống kê nạp điểm: bằng các giao dịch nạp điểm trên ví tiền của khách và sale
                // https://vinades.org/dauthau/dauthau.info/-/issues/2119
                $row['money_point_num'] = 0;
                $row['money_point'] = 0;
                $row['money_point_bonus'] = 0;
                $money_point_query = $db->query("SELECT count(*) as num, SUM(money_total) as total FROM nv4_wallet_transaction WHERE id_sale_static = " . $row['userid'] . " AND transaction_time >= " . $date . " AND transaction_time <=" . $todate . " AND userid NOT IN (" . $list_id_sale . ")");
                if ($money_point = $money_point_query->fetch()) {
                    $row['money_point_num'] = $money_point['num'];
                    $row['money_point'] = $money_point['total'] > 0 ? $money_point['total'] : 0;
                    $row['money_point_bonus'] = ($money_point['total'] * $row['percent_bonus']) / 100;
                }

                // đoạn này bên dauthau.info xử lý
                /*
                 * $sql_order = 'SELECT * FROM ' . BID_PREFIX_GLOBAL . '_orders WHERE admin_id = ' . $row['userid'] . ' AND (static_time >= ' . $date . ' AND static_time <=' . $todate . ') AND status = 4 AND is_expired = 0';
                 * $result_order = $db->query($sql_order);
                 * $array_order = array();
                 * $row['money'] = 0;
                 * $row['discount'] = 0;
                 * $row['total'] = 0;
                 * $row['price_reduce'] = 0;
                 * $row['total_end'] = 0;
                 * while ($_order = $result_order->fetch()) {
                 * $array_order[$_order['id']] = $_order;
                 * $row['money'] += intval($_order['money']);
                 * $row['discount'] += intval($_order['discount']);
                 * $row['total'] += intval($_order['total']);
                 * $row['price_reduce'] += intval($_order['price_reduce']);
                 * $row['total_end'] += intval($_order['total_end']);
                 * }
                 *
                 * $row['num_order'] = sizeof($array_order);
                 * $row['total_vips'] = 0;
                 * if (!empty($array_order)) {
                 * $total_vips = $db->query('SELECT COUNT(a.id) as total_vips FROM ' . BID_PREFIX_GLOBAL . '_customs_log a INNER JOIN ' . BID_PREFIX_GLOBAL . '_customs b ON a.user_id=b.user_id AND a.vip=b.vip WHERE a.order_id IN (' . implode(',', array_keys($array_order)) . ') AND a.status = 1 AND b.admin_id = ' . $row['userid'])->fetchColumn();
                 * $row['total_vips'] = intval($total_vips);
                 * }
                 */

                // lưu bảng thống kê
                /* Gọi API ListAllSaleStatic */
                $where_api = [];
                $where_api['AND'][] = [
                    '=' => [
                        'date' => $date
                    ]
                ];
                $where_api['AND'][] = [
                    '=' => [
                        'userid' => $row['userid']
                    ]
                ];
                $params = [
                    'page' => 1,
                    'perpage' => 1,
                    'where' => $where_api
                ];

                $result_api = nv_local_api('ListAllSaleStatic', $params, 1, 'crmbidding');
                $result_api = json_decode($result_api, true);
                $check = [];
                if ($result_api['status'] == 'success') {
                    if ($result_api['code'] != 4000) {
                        $check = $result_api['data'];
                    }
                } else {
                    // Hiển thị lỗi
                    file_put_contents($static_sale_log, "\n\n ---------- [" . date('d-m-Y H:i:s') . "] Lỗi API ListAllSaleStatic: \n" . print_r($result_api, true) . "\n", FILE_APPEND);
                    die();
                }

                if (empty($check)) {
                    /* Gọi API CreateSaleStatic */

                    $otherdata = [
                        'num_leads' => $row['total_leads'],
                        'num_opportunities' => $row['total_opportunities'],
                        /* 'num_order' => $row['num_order'],
                        'num_vip' => $row['total_vips'],
                        'money' => $row['money'],
                        'discount' => $row['discount'],
                        'total' => $row['total'],
                        'price_reduce' => $row['price_reduce'],
                        'total_end' => $row['total_end'], */
                        'recharge_day' => $row['recharge_day'],
                        'total_day' => $row['total_day'],
                        'bonus' => $row['money_bonus'],
                        'money_point_num' => $row['money_point_num'],
                        'money_point' => $row['money_point'],
                        'money_point_bonus' => $row['money_point_bonus']
                    ];
                    $params = [
                        'date' => $date,
                        'userid' => $row['userid'],
                        'otherdata' => $otherdata
                    ];

                    $result_api = nv_local_api('CreateSaleStatic', $params, 1, 'crmbidding');
                    $result_api = json_decode($result_api, true);
                    if ($result_api['status'] != 'success') {
                        // Hiển thị lỗi
                        file_put_contents($static_sale_log, "\n\n ---------- [" . date('d-m-Y H:i:s') . "] Lỗi API CreateSaleStatic: \n" . print_r($result_api, true) . "\n", FILE_APPEND);
                        die();
                    }
                    echo ("\n Insert userID: " . $row['userid'] . "\n");
                    file_put_contents($static_sale_log, "\t Insert userID: " . $row['userid'], FILE_APPEND);
                } else {
                    $data = [
                        'num_leads' => $row['total_leads'],
                        'num_opportunities' => $row['total_opportunities'],
                        /* 'num_order' => $row['num_order'],
                        'num_vip' => $row['total_vips'],
                        'money' => $row['money'],
                        'discount' => $row['discount'],
                        'total' => $row['total'],
                        'price_reduce' => $row['price_reduce'],
                        'total_end' => $row['total_end'], */
                        'recharge_day' => $row['recharge_day'],
                        'total_day' => $row['total_day'],
                        'bonus' => $row['money_bonus'],
                        'money_point_num' => $row['money_point_num'],
                        'money_point' => $row['money_point'],
                        'money_point_bonus' => $row['money_point_bonus']
                    ];

                    $params = [
                        'date' => $date,
                        'userid' => $row['userid'],
                        'data' => $data
                    ];

                    $result_api = nv_local_api('UpdateSaleStatic', $params, 1, 'crmbidding');
                    $result_api = json_decode($result_api, true);
                    if ($result_api['status'] != 'success') {
                        // Hiển thị lỗi
                        file_put_contents($static_sale_log, "\n\n ---------- [" . date('d-m-Y H:i:s') . "] Lỗi API UpdateSaleStatic: \n" . print_r($result_api, true) . "\n", FILE_APPEND);
                        die();
                    }
                    echo ("\n Update userID: " . $row['userid'] . "\n");
                    file_put_contents($static_sale_log, "\t Update userID: " . $row['userid'], FILE_APPEND);
                }
            }
        }
    }
} catch (PDOException $e) {
    print_r($e);
    die($e->getMessage());
}

if (file_exists($static_sale_file)) {
    unlink($static_sale_file); // xóa file thống kê lại theo ngày
}

echo "\n<br><br>Cập nhật trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
die("Thống kê xong");
