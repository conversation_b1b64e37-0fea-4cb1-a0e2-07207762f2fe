<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Mon, 29 Jan 2018 02:31:33 GMT
 */

if (!defined('NV_IS_FILE_ADMIN'))
    die('Stop!!!');

// Xóa người giới thiệu của tài khoản
if ($nv_Request->isset_request('delete', 'post')) {
    if (!defined('NV_IS_AJAX')) {
        die('Wrong URL!!!');
    }

    $pri_uid = $nv_Request->get_int('pri_uid', 'post', 0);
    $pre_uid = $nv_Request->get_int('pre_uid', 'post', 0);

    $exists_pre = $exists_pri = 'N/A';
    if (!empty($pre_uid)) {
        $sql = "SELECT username FROM " . NV_USERS_GLOBALTABLE . " WHERE userid=" . $pre_uid;
        $exists_pre = $db->query($sql)->fetchColumn();
    }
    if (!empty($pri_uid)) {
        $sql = "SELECT username FROM " . NV_USERS_GLOBALTABLE . " WHERE userid=" . $pri_uid;
        $exists_pri = $db->query($sql)->fetchColumn();
    }

    $sql = "DELETE FROM " . $db_config['prefix'] . "_" . $module_data . "_affiliate_set WHERE pre_uid=" . $pre_uid . " AND pri_uid=" . $pri_uid;
    $db->query($sql);
    $db->query("UPDATE " . NV_USERS_GLOBALTABLE . " SET num_aff_user = num_aff_user - 1 WHERE userid = " . $pri_uid);
    nv_insert_logs(NV_LANG_DATA, $module_name, 'Gỡ bỏ người giới thiệu', $pre_uid . ':' . $exists_pre . ' được giới thiệu bởi ' . $pri_uid . ':' . $exists_pri, $admin_info['userid']);
    $nv_Cache->delMod($module_name);

    nv_htmlOutput('OK');
}

$page_title = $nv_Lang->getModule('affiliate_full');

$q = $nv_Request->get_title('q', 'get', '');
$per_page = 50;
$page = $nv_Request->get_int('page', 'get', 1);
$db->sqlreset()
    ->select('COUNT(*)')
    ->from($db_config['prefix'] . '_' . $module_data . '_affiliate_set tb1, ' . NV_USERS_GLOBALTABLE . ' tb2, ' . NV_USERS_GLOBALTABLE . ' tb3');

$where = [];
$where[] = 'tb1.pri_uid=tb2.userid';
$where[] = 'tb1.pre_uid=tb3.userid';
if (!empty($q)) {
    $where[] = 'CONCAT(tb2.username, tb3.username, tb2.email, tb3.email, tb2.first_name, tb2.last_name, tb3.first_name, tb3.last_name) LIKE :q_name';
}
if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}
$sth = $db->prepare($db->sql());

if (!empty($q)) {
    $sth->bindValue(':q_name', '%' . $q . '%');
}
$sth->execute();
$num_items = $sth->fetchColumn();

$db->select('tb1.pri_uid, tb1.pre_uid, tb2.username pri_username, tb3.username pre_username, tb2.first_name pri_first_name, tb2.last_name pri_last_name, tb3.first_name pre_first_name, tb3.last_name pre_last_name, tb2.email pri_email, tb3.email pre_email, tb3.regdate')
    ->order('tb3.regdate DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$sth = $db->prepare($db->sql());

if (!empty($q)) {
    $sth->bindValue(':q_name', '%' . $q . '%');
}
$sth->execute();

$array = [];
$error = '';
$array['pri_uid'] = $array['pri_uid'] = '';
if ($nv_Request->isset_request('submit', 'post')) {
    $array['pre_uid'] = $nv_Request->get_title('pre_uid', 'post', '');
    $array['pri_uid'] = $nv_Request->get_title('pri_uid', 'post', '');

    // Kiểm tra tồn tại dữ liệu nhập
    $exists_pre = $exists_pri = 0;
    if (!empty($array['pre_uid'])) {
        $sql = "SELECT userid FROM " . NV_USERS_GLOBALTABLE . " WHERE username=" . $db->quote($array['pre_uid']);
        $exists_pre = $db->query($sql)->fetchColumn();
    }
    if (!empty($array['pri_uid'])) {
        $sql = "SELECT userid FROM " . NV_USERS_GLOBALTABLE . " WHERE username=" . $db->quote($array['pri_uid']);
        $exists_pri = $db->query($sql)->fetchColumn();
    }

    // Kiểm tra pri và pre theo chiều ngược lại
    $vaild_pripre = true;
    if ($exists_pre and $exists_pri) {
        $sql = "SELECT COUNT(*) FROM " . $db_config['prefix'] . "_" . $module_data . "_affiliate_set WHERE pri_uid=" . $exists_pre . " AND pre_uid=" . $exists_pri;
        if ($db->query($sql)->fetchColumn()) {
            $vaild_pripre = false;
        }
    }

    if (empty($array['pre_uid']) or empty($array['pri_uid'])) {
        $error = 'Vui lòng nhập tài khoản và người giới thiệu';
    } elseif (!$exists_pre) {
        $error = 'Tài khoản ngày không tồn tại';
    } elseif (!$exists_pri) {
        $error = 'Người giới thiệu không tồn tại';
    } elseif (!$vaild_pripre) {
        $error = 'Tài khoản mà bạn chọn đã là người giới thiệu của người giới thiệu mà bạn chọn, dữ liệu này không được chấp nhận';
    } else {
        // Thêm vào CSDL và bỏ qua tồn tại
        $sql = "INSERT IGNORE INTO " . $db_config['prefix'] . "_" . $module_data . "_affiliate_set (pri_uid, pre_uid) VALUES (" . $exists_pri . ", " . $exists_pre . ")";
        $insert_aff = $db->query($sql);
        nv_insert_logs(NV_LANG_DATA, $module_name, 'Thêm người giới thiệu', $array['pre_uid'] . ' được giới thiệu bởi ' . $array['pri_uid'], $admin_info['userid']);
        $nv_Cache->delMod($module_name);
        if ($insert_aff->rowCount() > 0) {
            $db->query("UPDATE " . NV_USERS_GLOBALTABLE . " SET num_aff_user = (SELECT COUNT(*) FROM " . $db_config['prefix'] . "_" . $module_data . "_affiliate_set WHERE pri_uid = " . $exists_pri . ") WHERE userid = " . $exists_pri);
        }
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('ROW', $row);
$xtpl->assign('DATA', $array);
$xtpl->assign('Q', $q);

$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
if (!empty($q)) {
    $base_url .= '&q=' . urlencode($q);
}
$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
if (!empty($generate_page)) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}
$number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;

$array_data = array();
while ($view = $sth->fetch()) {
    $view['pri_full_name'] = nv_show_name_user($view['pri_first_name'], $view['pri_last_name']);
    $view['pre_full_name'] = nv_show_name_user($view['pre_first_name'], $view['pre_last_name']);
    $view['regdate'] = nv_date('d/m/Y H:i', $view['regdate']);

    $xtpl->assign('VIEW', $view);
    $xtpl->parse('main.loop');
}

if (!empty($error)) {
    $xtpl->assign('ERROR', $error);
    $xtpl->parse('main.error');
    $xtpl->assign('HTML_FORM1', '');
    $xtpl->assign('HTML_FORM2', 'true');
    $xtpl->assign('HTML_FORM3', ' in');
} else {
    $xtpl->assign('HTML_FORM1', ' collapsed');
    $xtpl->assign('HTML_FORM2', 'false');
    $xtpl->assign('HTML_FORM3', '');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
