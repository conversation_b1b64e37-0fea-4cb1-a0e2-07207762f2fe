<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$showheader = $nv_Request->get_int('showheader', 'post,get', 1);
$error = [];

$q = $nv_Request->get_title('q', 'post,get');
$status = $nv_Request->get_title('status', 'post,get', -1);
$orderby_hoten = $nv_Request->get_int('orderby_hoten', 'post,get', 0);
$orderby_phone = $nv_Request->get_int('orderby_phone', 'post,get', 0);
$orderby_email = $nv_Request->get_int('orderby_email', 'post,get', 0);
$orderby_uniqueid = $nv_Request->get_int('orderby_uniqueid', 'post,get', 0);
$orderby_first_time = $nv_Request->get_int('orderby_first_time', 'post,get', 0);
$orderby_last_time = $nv_Request->get_int('orderby_last_time', 'post,get', 0);
// Fetch Limit
$per_page = 50;
$page = $nv_Request->get_int('page', 'post,get', 1);

//COUNT NUMBER RECORDS
$db->sqlreset()
    ->select('COUNT(*)')
    ->from(NV_PREFIXLANG . '_' . $module_data . '_chatgpt_users tb1 
        LEFT JOIN ' . NV_USERS_GLOBALTABLE . ' tb2 ON tb2.userid = tb1.userid
        LEFT JOIN ' . NV_USERS_GLOBALTABLE . '_info tb3 ON tb1.userid = tb3.userid
    ');

$where = [];
$wherePhone = '';
if (!empty($q)) {
    //chuẩn hóa số điện thoại
    $_tmp_phone = null;
    $wherePhone = '';
    if (phonecheck($q)) {
        $_tmp_phone = $q;
        if (preg_match('/(\d{9})$/', $q, $m)) {
            $_tmp_phone = $m[0];
        }
        $_tmp_phone = preg_replace('/[^0-9]/', '', $_tmp_phone);
        $_tmp_phone = (int)$_tmp_phone;
        $wherePhone = ' OR phone LIKE :q_phone_search ';
    }
    $where[] = '(first_name LIKE :q_first_name OR last_name LIKE :q_last_name OR phone LIKE :q_phone ' . $wherePhone . ' OR email LIKE :q_email)';
}
if (!empty($status)) {
    if ($status == 1) {
        $where[] = "EXISTS (SELECT id FROM " . NV_PREFIXLANG . "_crmbidding_leads tb4 WHERE (tb4.email = tb2.email AND tb4.email != '') OR (tb3.phone = tb4.phone))";
    }
    if ($status == 2) {
        $where[] = "NOT EXISTS (SELECT id FROM " . NV_PREFIXLANG . "_crmbidding_leads tb4 WHERE (tb4.email = tb2.email AND tb4.email != '') OR (tb3.phone = tb4.phone))";
    }
}
if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}
$sth = $db->prepare($db->sql());
if (!empty($q)) {
    $sth->bindValue(':q_first_name', '%' . $q . '%');
    $sth->bindValue(':q_last_name', '%' . $q . '%');
    $sth->bindValue(':q_phone', '%' . $q . '%');
    if ($_tmp_phone) {
        $sth->bindValue(':q_phone_search', '%' . $_tmp_phone . '%');
    }
    $sth->bindValue(':q_email', '%' . $q . '%');
}
$sth->execute();
$num_items = $sth->fetchColumn(); //COUNT ROWS

$order = 'first_time DESC';
if ($orderby_hoten > 0) {
    $order = $orderby_hoten == 1 ? 'first_name ASC, last_name ASC' : 'first_name DESC, last_name DESC';
}
if ($orderby_phone > 0) {
    $order = $orderby_phone == 1 ? 'phone ASC' : 'phone DESC';
}
if ($orderby_email > 0) {
    $order = $orderby_email == 1 ? 'email ASC' : 'email DESC';
}
if ($orderby_uniqueid > 0) {
    $order = $orderby_uniqueid == 1 ? 'uniqueid ASC' : 'uniqueid DESC';
}
if ($orderby_first_time > 0) {
    $order = $orderby_first_time == 1 ? 'first_time ASC' : 'first_time DESC';
}
if ($orderby_last_time > 0) {
    $order = $orderby_last_time == 1 ? 'last_activity ASC' : 'last_activity DESC';
}

//SELECT DATA
$db->select('
        tb1.*,
	    tb2.first_name,
	    tb2.last_name,
	    tb2.username,
	    tb2.email,
	    tb3.phone
	')
    ->from(NV_PREFIXLANG . '_' . $module_data . '_chatgpt_users tb1
        LEFT JOIN ' . NV_USERS_GLOBALTABLE . ' tb2 ON tb2.userid = tb1.userid
        LEFT JOIN ' . NV_USERS_GLOBALTABLE . '_info tb3 ON tb1.userid = tb3.userid
    ')
    ->order($order)
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);

$sth = $db->prepare($db->sql());

if (!empty($q)) {
    $sth->bindValue(':q_first_name', '%' . $q . '%');
    $sth->bindValue(':q_last_name', '%' . $q . '%');
    $sth->bindValue(':q_phone', '%' . $q . '%');
    if ($_tmp_phone) {
        $sth->bindValue(':q_phone_search', '%' . $_tmp_phone . '%');
    }
    $sth->bindValue(':q_email', '%' . $q . '%');
}

$sth->execute();
$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('Q', $q);
$xtpl->assign('LINK_DUPLICATE', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=duplicate&type=2');

$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&showheader=' . $showheader;
if (!empty($q)) {
    $base_url .= '&q=' . $q;
}
if (!empty($status)) {
    $base_url .= '&status=' . $status;
}

//PROCESS ORDER BY
$link_orderby_hoten = $base_url . '&orderby_hoten=1';
$link_orderby_phone = $base_url . '&orderby_phone=1';
$link_orderby_email = $base_url . '&orderby_email=1';
$link_orderby_uniqueid = $base_url . '&orderby_uniqueid=1';
$link_orderby_first_time = $base_url . '&orderby_first_time=1';
$link_orderby_last_time = $base_url . '&orderby_last_time=1';

//LINK ORDER HO TEN
if ($orderby_hoten > 0) {
    if ($orderby_hoten == 1) {
        $link_orderby_hoten_desc = $base_url . '&orderby_hoten=2' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_HOTEN_DESC', $link_orderby_hoten_desc);
        $xtpl->assign('ORDER_BY_HOTEN', $link_orderby_hoten_desc);
    } else {
        $link_orderby_hoten_asc = $base_url . '&orderby_hoten=1' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_HOTEN_ASC', $link_orderby_hoten_asc);
        $xtpl->assign('ORDER_BY_HOTEN', $link_orderby_hoten_asc);
    }
    $base_url .= '&orderby_hoten=' . $orderby_hoten;
} else {
    $xtpl->assign('ORDER_BY_HOTEN', $link_orderby_hoten . '&page=' . $page);
}

//LINK ORDER PHONE
if ($orderby_phone > 0) {
    if ($orderby_phone == 1) {
        $link_orderby_phone_desc = $base_url . '&orderby_phone=2' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_PHONE_DESC', $link_orderby_phone_desc);
        $xtpl->assign('ORDER_BY_PHONE', $link_orderby_phone_desc);
    } else {
        $link_orderby_phone_asc = $base_url . '&orderby_phone=1' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_PHONE_ASC', $link_orderby_phone_asc);
        $xtpl->assign('ORDER_BY_PHONE', $link_orderby_phone_asc);
    }
    $base_url .= '&orderby_phone=' . $orderby_phone;
} else {
    $xtpl->assign('ORDER_BY_PHONE', $link_orderby_phone . '&page=' . $page);
}

//LINK ORDER EMAIL
if ($orderby_email > 0) {
    if ($orderby_email == 1) {
        $link_orderby_email_desc = $base_url . '&orderby_email=2' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_EMAIL_DESC', $link_orderby_email_desc);
        $xtpl->assign('ORDER_BY_EMAIL', $link_orderby_email_desc);
    } else {
        $link_orderby_email_asc = $base_url . '&orderby_email=1' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_EMAIL_ASC', $link_orderby_email_asc);
        $xtpl->assign('ORDER_BY_EMAIL', $link_orderby_email_asc);
    }
    $base_url .= '&orderby_email=' . $orderby_email;
} else {
    $xtpl->assign('ORDER_BY_EMAIL', $link_orderby_email . '&page=' . $page);
}

//LINK ORDER UNIQUEID
if ($orderby_uniqueid == 1 or $orderby_uniqueid == 0) {
    $link_orderby_uniqueid_desc = $base_url . '&orderby_uniqueid=2' . '&page=' . $page;
    $xtpl->assign('ORDER_BY_UNIQUEID_DESC', $link_orderby_uniqueid_desc);
    $xtpl->assign('ORDER_BY_UNIQUEID', $link_orderby_uniqueid_desc);
} else {
    $link_orderby_uniqueid_asc = $base_url . '&orderby_uniqueid=1' . '&page=' . $page;
    $xtpl->assign('ORDER_BY_UNIQUEID_ASC', $link_orderby_uniqueid_asc);
    $xtpl->assign('ORDER_BY_UNIQUEID', $link_orderby_uniqueid_asc);
}

//LINK ORDER FIRST TIME
if ($orderby_first_time == 1 or $orderby_first_time == 0) {
    $link_orderby_first_time_desc = $base_url . '&orderby_first_time=2' . '&page=' . $page;
    $xtpl->assign('ORDER_BY_FIRST_TIME_DESC', $link_orderby_first_time_desc);
    $xtpl->assign('ORDER_BY_FIRST_TIME', $link_orderby_first_time_desc);
} else {
    $link_orderby_first_time_asc = $base_url . '&orderby_first_time=1' . '&page=' . $page;
    $xtpl->assign('ORDER_BY_FIRST_TIME_ASC', $link_orderby_first_time_asc);
    $xtpl->assign('ORDER_BY_FIRST_TIME', $link_orderby_first_time_asc);
}

//LINK ORDER LAST ACTIVITY
if ($orderby_last_time == 1 or $orderby_last_time == 0) {
    $link_orderby_last_time_desc = $base_url . '&orderby_last_time=2' . '&page=' . $page;
    $xtpl->assign('ORDER_BY_LAST_TIME_DESC', $link_orderby_last_time_desc);
    $xtpl->assign('ORDER_BY_LAST_TIME', $link_orderby_last_time_desc);
} else {
    $link_orderby_last_time_asc = $base_url . '&orderby_last_time=1' . '&page=' . $page;
    $xtpl->assign('ORDER_BY_LAST_TIME_ASC', $link_orderby_last_time_asc);
    $xtpl->assign('ORDER_BY_LAST_TIME', $link_orderby_last_time_asc);
}

$base_url .= '&orderby_first_time=' . $orderby_first_time;
$xtpl->parse('main.status');
$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
if (!empty($generate_page)) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}

//COMPARE DATA ROWS
$number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;
while ($view = $sth->fetch()) {
    if (!empty($view['phone']) or !empty($view['email'])) {
        $_sql = "SELECT id,name,phone,email FROM " . NV_PREFIXLANG . "_" . $module_data . "_leads";
        $where_duplicate = [];
        $num_items_duplicate = "";
        if (!empty($view['email'])) {
            $where_duplicate[] = "email = " . $db->quote($view['email']) . "";
        }
        if (!empty($view['phone'])) {
            $where_duplicate[] = "phone = " . $db->quote($view['phone']);
            $_tmp_phone = $view['phone'];
            if (preg_match('/(\d{9})$/', $_tmp_phone, $m)) {
                $_tmp_phone = $m[0];
            }
            $where_duplicate[] = "phone_search = " . $db->quote($_tmp_phone);
        }

        $_sql .= " WHERE (" . implode(' OR ', $where_duplicate) . ") ORDER BY id DESC";
        $result = $db->query($_sql);
        $items_duplicate = $result->fetchAll();
        if (sizeof($items_duplicate) > 0) {
            $xtpl->assign('STATUS_LEADS', $nv_Lang->getModule('da_tao_lead'));
            foreach ($items_duplicate as $item) {
                $xtpl->assign('LINK_LEADS', NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=' . 'leads_info&id=' . $item['id'] . '&showheader=' . $showheader);
                $xtpl->assign('NAME_LEADS', $item['name']);
                $xtpl->parse('main.loop.link_leads');
            }
        } else {
            $xtpl->assign('STATUS_LEADS', $nv_Lang->getModule('chua_tao_lead'));
        }
        $xtpl->parse('main.loop.status_leads');
    }
    $xtpl->assign('LINK_DETAIL', NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=' . 'chatgpt_detail&id=' . $view['id']);

    $view['hoten'] = nv_show_name_user($view['first_name'], $view['last_name'], $view['username']);

    $view['number'] = $number++;
    $view['first_time'] = nv_date("H:m:s d/m/y", $view['first_time']);
    $view['last_activity'] = nv_date("H:m:s d/m/y", $view['last_activity']);
    $xtpl->assign('VIEW', $view);
    $xtpl->parse('main.loop');
}

//STATUS LIST
$array_status_chatgpt = [
    '1' => $nv_Lang->getModule('da_tao_lead'),
    '2' => $nv_Lang->getModule('chua_tao_lead')
];
foreach ($array_status_chatgpt as $key => $value) {
    $xtpl->assign('STATUS', array(
        'id' => $key,
        'title' => $value,
        'selected' => $key == $status ? 'selected="selected"' : ''
    ));
    $xtpl->parse('main.search.status');
}

if ($showheader) {
    $xtpl->parse('main.search');
}

//ERROR LIST
if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');
$page_title = $nv_Lang->getModule('ds_khach_chat_gpt');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';
