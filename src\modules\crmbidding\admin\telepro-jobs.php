<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('telejob');
$set_active_op = 'import';

// Xóa
if ($nv_Request->isset_request('delete', 'post')) {
    $id = $nv_Request->get_int('id', 'post', 0);

    // Kiểm tra tồn tại
    $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_jobs WHERE id=' . $id;
    $array = $db->query($sql)->fetch();
    if (empty($array)) {
        nv_htmlOutput('NO_' . $id);
    }

    // Xóa
    $sql = 'DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_jobs WHERE id=' . $id;
    $db->query($sql);

    // Xoá dữ liệu telepro
    $sql = 'DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro WHERE job_id=' . $id;
    $db->query($sql);
    
    // Xoá logs
    $sql = 'DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_logs WHERE job_id=' . $id;
    $db->query($sql);

    nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_DELETE_TELEPRO_JOBS', json_encode($array), $admin_info['admin_id']);
    $nv_Cache->delMod($module_name);

    nv_htmlOutput("OK");
}

$array = [];
$error = [];

$id = $nv_Request->get_int('id', 'get', 0);

if (!empty($id)) {
    $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_jobs WHERE id = ' . $id;
    $result = $db->query($sql);
    $array = $result->fetch();

    if (empty($array)) {
        nv_info_die($nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_content'));
    }

    $data_fields = empty($array['data_fields']) ? [] : json_decode($array['data_fields'], true);
    $array['data_fields'] = [];
    foreach ($data_fields as $fi_key => $fi_val) {
        $array['data_fields'][] = $fi_key . ':' . $fi_val;
    }
    $array['data_fields'] = implode("\n", $array['data_fields']);
    $array['telepro_id'] = $id;

    $caption = $nv_Lang->getModule('telejob_edit');
    $form_action = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $id;
} else {
    $array = [
        'id' => 0,
        'telepro_id' => 0,
        'title' => '',
        'job_code' => '',
        'data_fields' => '',
        'api_key' => '',
    ];

    $caption = $nv_Lang->getModule('telejob_add');
    $form_action = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
}

// Lấy danh sách columns
$sql = "SHOW COLUMNS FROM " . NV_PREFIXLANG . "_" . $module_data . "_telepro";
$array_columns = $db->query($sql)->fetchAll(PDO::FETCH_COLUMN);

if ($nv_Request->isset_request('submit', 'post')) {
    $array['telepro_id'] = $nv_Request->get_absint('telepro_id', 'post', 0);
    $array['title'] = $nv_Request->get_title('title', 'post', '');
    $array['job_code'] = $nv_Request->get_title('job_code', 'post', '');
    $array['api_key'] = $nv_Request->get_title('api_key', 'post', '');
    $array['active'] = (int) $nv_Request->get_bool('active', 'post', false);

    $data_fields = $nv_Request->get_string('data_fields', 'post', '');
    $array['data_fields'] = nv_htmlspecialchars($data_fields);

    // Kiểm tra trùng tiêu đề
    $is_exists = false;
    $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_jobs WHERE title = :title' . ($id ? ' AND id != ' . $id : '');
    $sth = $db->prepare($sql);
    $sth->bindParam(':title', $array['title'], PDO::PARAM_STR);
    $sth->execute();
    if ($sth->fetchColumn()) {
        $is_exists = true;
    }

    // Kiểm tra trùng ID
    $id_exists = false;
    $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_jobs WHERE id = :telepro_id' . ($id ? ' AND id != ' . $id : '');
    $sth = $db->prepare($sql);
    $sth->bindParam(':telepro_id', $array['telepro_id'], PDO::PARAM_INT);
    $sth->execute();
    if ($sth->fetchColumn()) {
        $id_exists = true;
    }

    if (empty($array['telepro_id'])) {
        $error[] = $nv_Lang->getModule('telejob_error_id');
    } elseif ($id_exists) {
        $error[] = $nv_Lang->getModule('telejob_error_id1');
    }
    if (empty($array['title'])) {
        $error[] = $nv_Lang->getModule('telejob_error_title');
    } elseif ($is_exists) {
        $error[] = $nv_Lang->getModule('telejob_error_exists');
    }
    if (empty($array['job_code'])) {
        $error[] = $nv_Lang->getModule('telejob_error_code');
    }
    if (empty($array['api_key'])) {
        $error[] = $nv_Lang->getModule('telejob_error_api_key');
    }

    $fields = [];
    $data_fields = array_map('trim', explode('||||', nv_nl2br($data_fields, '||||')));

    $stt = 0;
    $error_fields = 0;
    foreach ($data_fields as $fv) {
        $stt++;
        // Bỏ qua dòng trống
        if (trim($fv) == '') {
            continue;
        }
        $fv = trim($fv);
        if (!preg_match('/^([a-zA-Z0-9\_\-]+)[\s]*\:[\s]*([a-zA-Z0-9\_\-]*)$/', $fv, $m)) {
            $error[] = sprintf($nv_Lang->getModule('telejob_fields_error'), $stt);
            $error_fields++;
            continue;
        }
        if (isset($fields[$m[1]])) {
            continue;
        }
        if (!empty($m[2]) and !in_array($m[2], $array_columns)) {
            $error[] = sprintf($nv_Lang->getModule('telejob_fields_error1'), $stt, $m[2]);
            $error_fields++;
            continue;
        }
        $fields[$m[1]] = $m[2] ?: '';
    }
    if (!$error_fields and empty($fields)) {
        $error[] = $nv_Lang->getModule('telejob_fields_error2');
    }
    $fields = json_encode($fields);

    if (empty($error)) {
        if (!$id) {
            $sql = 'INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_jobs (
                id, title, job_code, data_fields, api_key, add_time, edit_time, active
            ) VALUES (
                :telepro_id, :title, :job_code, :data_fields, :api_key, ' . NV_CURRENTTIME . ', 0, :active
            )';
        } else {
            $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_jobs SET
                id=:telepro_id, title = :title, job_code = :job_code, data_fields=:data_fields,
                api_key=:api_key, edit_time = ' . NV_CURRENTTIME . ', active=:active 
            WHERE id = ' . $id;
        }

        try {
            $sth = $db->prepare($sql);
            $sth->bindParam(':telepro_id', $array['telepro_id'], PDO::PARAM_INT);
            $sth->bindParam(':title', $array['title'], PDO::PARAM_STR);
            $sth->bindParam(':job_code', $array['job_code'], PDO::PARAM_STR);
            $sth->bindParam(':data_fields', $fields, PDO::PARAM_STR);
            $sth->bindParam(':api_key', $array['api_key'], PDO::PARAM_STR);
            $sth->bindParam(':active', $array['active'], PDO::PARAM_INT);
            $sth->execute();

            if ($sth->rowCount()) {
                if ($id) {
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_EDIT_TELEPRO_JOBS', 'ID: ' . $id . ':' . $array['title'], $admin_info['userid']);
                } else {
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_ADD_TELEPRO_JOBS', $array['title'], $admin_info['userid']);
                }

                $nv_Cache->delMod($module_name);
                nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
            } else {
                $error[] = $nv_Lang->getModule('errorsave');
            }
        } catch (PDOException $e) {
            $error[] = $nv_Lang->getModule('errorsave');
        }
    }
}

$array['telepro_id'] = $array['telepro_id'] ?: '';
$array['active'] = empty($array['active']) ? '' : ' checked="checked"';

$xtpl = new XTemplate('telepro-jobs.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('CAPTION', $caption);
$xtpl->assign('FORM_ACTION', $form_action);
$xtpl->assign('DATA', $array);

$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_jobs ORDER BY id DESC';
$array_telepro_jobs = $db->query($sql)->fetchAll();
$num = sizeof($array_telepro_jobs);

foreach ($array_telepro_jobs as $row) {
    $row['url_edit'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $row['id'];
    
    $xtpl->assign('ROW', $row);
    if (defined('NV_IS_GODADMIN') || $admin_info['userid'] == 8223) {
        $xtpl->parse('main.loop.delete');
    } 
    $xtpl->parse('main.loop');
}

if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
