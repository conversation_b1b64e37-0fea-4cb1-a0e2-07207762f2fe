<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */

if (!defined('NV_IS_MOD_WALLET')) {
    die('Stop!!!');
}

$request_data = [];
$request_data['code'] = $nv_Request->get_title('code', 'get', '');
$request_data['money'] = $nv_Request->get_title('money', 'get', '');
$request_data['unit'] = $nv_Request->get_title('unit', 'get', '');
$request_data['info'] = $nv_Request->get_string('info', 'get', '');
$request_data['checksum'] = $nv_Request->get_title('checksum', 'get', '');

$pay_dauthau = false;
$_transaction_info= '';
if (preg_match('/^(GD|WP|BDH)([0-9]{10})$/', $request_data['code'], $m) ) {
    if ($m[1] == 'BDH') {
        $pay_dauthau= true;
        $_transaction_info = $array_op[1];
    }
    // Loại giao dịch
    $responseData['ordertype'] = ($m[1] == 'WP' or $pay_dauthau) ? 'pay' : 'recharge';
}

// ID giao dịch nếu nạp tiền hoặc là ID đơn hàng nếu thanh toán cho các module khác
$responseData['orderid'] = intval(str_replace('GD', '', str_replace('WP', '', $request_data['code'])));

// Thời điểm giao dịch
$responseData['transaction_time'] = NV_CURRENTTIME;

/*
 * Xác định giao dịch đã lưu trước trong CSDL
 */
if ($pay_dauthau) {
    $stmt = $db->prepare("SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_transaction WHERE transaction_info = :transaction_info");
    $stmt->bindParam(':transaction_info', $_transaction_info, PDO::PARAM_STR);
} else {
    $stmt = $db->prepare("SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_transaction WHERE id = :id");
    $stmt->bindParam(':id', $responseData['orderid'], PDO::PARAM_INT);
}

$stmt->execute();
$__order_info = $stmt->fetch();

if (empty($__order_info)) {
    $error = $nv_Lang->getModule('transition_no_exists');
} else {
    // Lấy lại transaction_data từ CSDL
    $responseData['transaction_data'] = $__order_info['transaction_data'];

    // Tính lại checksum để đối chiếu
    $checksum_str = $request_data['code'] . $request_data['money'] . $request_data['unit'] . $request_data['info'] . $__order_info['tokenkey'];
    $checksum = hash('sha256', $checksum_str);

    if ($checksum === $request_data['checksum']) {
        if ($pay_dauthau) {
            $responseData['orderid'] = $responseData['id'];
        }
        $responseData['transaction_status'] = $__order_info['transaction_status'];
    } else {
        $responseData['transaction_status'] = 5;
    }
}

