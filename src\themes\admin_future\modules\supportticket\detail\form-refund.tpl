<!-- BEGIN: main -->
<div class="col-md-12">
    <div class="container-refund p-4 border rounded bg-light">
        <form id="refundForm_{COMMENT.log_id}" action="">
            <input type="hidden" name="comment_refund_id" value="{COMMENT.log_id}">
            <input type="hidden" name="reply_role" value="{COMMENT.comment_type}">
            <input type="hidden" name="refund_token" value="{TOKEN}">
            <div class="mb-3">
                <div class="form-check form-check-inline">
                    <!-- BEGIN: loop -->
                    <div class="form-check me-3">
                        <input class="form-check-input" type="radio" id="agree_{OPTION.key}" name="refund_status" value="{OPTION.key}">
                        <label class="form-check-label" for="agree_{OPTION.key}">{OPTION.title}</label>
                    </div>
                    <!-- END: loop -->
                </div>
            </div>
            <div class="mb-3">
                <label for="rating_reply_{COMMENT.log_id}" class="form-label">{LANG.reply_refund_by_admin}</label>
                <textarea class="form-control" id="rating_reply_{COMMENT.log_id}" rows="4" name="rating_reply" data-editor="true"></textarea>
            </div>
            <div class="text-end">
                <button type="submit" class="btn btn-primary" id="refundSubmit">
                    <i class="fa fa-paper-plane" aria-hidden="true"></i> {LANG.send}
                </button>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript">
    (async () => {
        await ClassicEditor
        .create(document.getElementById("rating_reply_{COMMENT.log_id}"), {
            language: '{NV_LANG_INTERFACE}',
            removePlugins: ["NVBox"],
            image: {insert: {integrations: ["url"]}},
            nvmedia: {insert: {integrations: ["url"]}},
            toolbar: {
                items: [
                'undo','redo','selectAll','|',
                    'link','imageInsert','nvmediaInsert','insertTable','code','codeBlock','horizontalLine','specialCharacters','pageBreak','|',
                    'findAndReplace','showBlocks','|',
                    'bulletedList','numberedList','outdent','indent','blockQuote','heading','fontSize','fontFamily',
                    'fontColor','fontBackgroundColor','highlight','alignment','|',
                    'bold','italic','underline','strikethrough','subscript','superscript','|',
                    'sourceEditing','restrictedEditingException','removeFormat'
                ],
                shouldNotGroupWhenFull: false
            }
        })
        .then(editor => {
            window.nveditor = window.nveditor || [];
            window.nveditor["rating_reply_{COMMENT.log_id}"] = editor;
            if (editor.sourceElement && editor.sourceElement instanceof HTMLTextAreaElement && editor.sourceElement.form) {
                editor.sourceElement.dataset.editorname = "rating_reply_{COMMENT.log_id}";
                editor.sourceElement.form.addEventListener("submit", event => {
                    // Xử lý khi submit form thông thường
                    editor.sourceElement.value = editor.getData();
                });
            }
        })
        .catch(error => {
            console.error(error);
        });
    })();
    $(document).ready(function() {
        $('#refundForm_{COMMENT.log_id}').on('submit', function(e) {
            e.preventDefault();

            var refundSubmit = $('#refundSubmit');
            // Check điều kiện
            var refundStatus = $('input[name="refund_status"]:checked').val();
            if (!refundStatus) {
                alert('{LANG.error_refund_status}');
                return;
            }
            var isEditor = (typeof CKEDITOR !== "undefined" && $('#rating_reply_{COMMENT.log_id}').data('editor') == true) ? true : false;
            if (isEditor) {
                var instance = CKEDITOR.instances['rating_reply_{COMMENT.log_id}'];
                if (instance) {
                    $('#rating_reply_{COMMENT.log_id}').val(instance.getData());
                }
            }
            var ratingReply = $('#rating_reply_{COMMENT.log_id}').val().trim();
            if (!ratingReply) {
                alert('{LANG.error_refund_content}');
                return;
            }
            // disable button submit đi tránh trường hợp bấm nhầm spam nhiều câu trả lời
            refundSubmit.prop('disabled', true);

            var formData = $(this).serialize();
            var actionUrl = $('#detail_paid_form_action').attr('action');
            var ratingToken = $('#token_selection').val();
            $.ajax({
                url: actionUrl,
                method: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message);
                        refundSubmit.prop('disabled', false);
                    }
                },
                error: function(xhr, status, error) {
                    alert(response.message);
                    refundSubmit.prop('disabled', false);
                }
            });
        });
    });
</script>
<style>
    .ck-editor__editable_inline {
        min-height: 120px;
    }
</style>
<!-- END: main -->
