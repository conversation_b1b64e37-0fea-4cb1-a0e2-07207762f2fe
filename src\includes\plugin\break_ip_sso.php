<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_MAINFILE')) {
    exit('Stop!!!');
}

nv_add_hook($module_name, 'sector2', $priority, function () {
    global $client_info, $nv_Request;

    $array_ip_no_sso = [];
    $array_ip_no_sso[] = '**************'; // VNPAY GATEWAY/2.0 SANDBOX
    $array_ip_no_sso[] = '************'; // VNPAY GATEWAY/2.0
    $array_ip_no_sso[] = '**************'; // VNPAY GATEWAY/2.0
    $array_ip_no_sso[] = '*************'; // VNPAY GATEWAY/2.0
    $array_ip_no_sso[] = '**************'; // VNPAY GATEWAY/2.0
    $array_ip_no_sso[] = '**************'; // VNPAY GATEWAY/2.0
    $array_ip_no_sso[] = '************'; // VNPAY GATEWAY/2.0
    $array_ip_no_sso[] = '***************'; // DauThau.info
    $array_ip_no_sso[] = '************'; // DauThau.net
    $array_ip_no_sso[] = '***************'; // Telepro callback

    if (in_array(NV_CLIENT_IP, $array_ip_no_sso) or (isset($_GET[NV_OP_VARIABLE]) and strpos((string) $_GET[NV_OP_VARIABLE], 'opensearch') !== false) or strpos(NV_CLIENT_IP, '128.30.52.') === 0) {
        $client_info['is_bot'] = true;
    }

    // Cho phép ajax lấy thông tin doanh nghiệp
    if (
        $nv_Request->get_title(NV_NAME_VARIABLE, 'post,get', '') == 'businesslistings' and
        $nv_Request->get_title('getprofdata', 'post', '') != '' and
        $nv_Request->get_title('checksum', 'post', '') != ''
    ) {
        $client_info['is_bot'] = true;
    } elseif (
        $nv_Request->get_title(NV_NAME_VARIABLE, 'post,get', '') == 'rss' or
        $nv_Request->get_title(NV_OP_VARIABLE, 'post,get', '') == 'rss'
    ) {
        $client_info['is_bot'] = true;
    }
});
