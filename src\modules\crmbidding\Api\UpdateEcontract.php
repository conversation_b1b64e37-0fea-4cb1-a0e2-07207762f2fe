<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jan 20, 2025 8:59:32 PM
 */

namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use NukeViet\Dauthau\Condition;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdateEcontract implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'econtracts';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $module_config, $module_name, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        // Kiểm tra nếu như truyền order_id
        if ($nv_Request->isset_request('order_id', 'post')) {
            $order_id = $nv_Request->get_int('order_id', 'post', 0);
            if ($order_id <= 0) {
                return $this->result->setError()
                    ->setCode('2003')
                    ->setMessage('Order ID must be an integer greater than 0')
                    ->getResult();
            }

            $sql_order = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE order_id = :order_id';
            $stmt_order = $db->prepare($sql_order);
            $stmt_order->bindParam(':order_id', $order_id, PDO::PARAM_INT);
            $stmt_order->execute();
            $econtract_order = $stmt_order->fetch();

            if (empty($econtract_order)) {
                return $this->result->setError()
                    ->setCode('2004')
                    ->setMessage('Order does not exist')
                    ->getResult();
            }

            $econtract_id = $econtract_order['econtract_id'];
        } else {
            $econtract_id = $nv_Request->get_int('econtract_id', 'post', 0);
        }

        if ($econtract_id <= 0) {
            return $this->result->setError()
                ->setCode('2003')
                ->setMessage('Econtract is integer, more than 0' . $econtract_id)
                ->getResult();
        }

        $customer_id = $nv_Request->get_int('customer_id', 'post', 0);
        if ($customer_id <= 0) {
            return $this->result->setError()
                ->setCode('2003')
                ->setMessage('Customer is integer, more than 0' . $customer_id)
                ->getResult();
        }

        $sql_row = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id = :econtract_id AND customer_id = :customer_id';
        $stmt_row = $db->prepare($sql_row);
        $stmt_row->bindParam(':econtract_id', $econtract_id, PDO::PARAM_INT);
        $stmt_row->bindParam(':customer_id', $customer_id, PDO::PARAM_INT);
        $stmt_row->execute();
        $row_old = $stmt_row->fetch();

        if (empty($row_old)) {
            return $this->result->setError()
                ->setCode('2004')
                ->setMessage('Econtract not exist')
                ->getResult();
        }

        // Kiểm tra where
        $array_where = $nv_Request->get_array('where', 'post');
        if (!is_array($array_where)) {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Param where invalid to array')
                ->getResult();
        }

        $where = [];
        $keys_where = [
            'customer_id',
            'customer_type',
            'status',
            'stage',
            'stage_next',
            'created_at',
            'tax_code',
            'cccd',
            'email',
            'phone',
        ];
        if (!empty($array_where)) {
            foreach ($array_where as $keys) {
                if (!empty($keys)) {
                    foreach ($keys as $key) {
                        $operator = array_key_first($key);
                        $field = array_key_first($key[$operator]);
                        if (!in_array($field, $keys_where)) {
                            return $this->result->setError()
                                ->setCode('2001')
                                ->setMessage('Missing field ' . $field . ' in data')
                                ->getResult();
                        }
                    }
                }
            }

            $condition = new Condition();
            $where[] = $condition->toSqlString($array_where);
        }

        // Xử lý dữ liệu
        $data = $nv_Request->get_array('data', 'post');
        if (!is_array($data)) {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Param data invalid to json')
                ->getResult();
        }
        if (empty($data)) {
            return $this->result->setError()
                ->setCode('2001')
                ->setMessage('Param data empty')
                ->getResult();
        }

        $keys_update = [
            'tax_code',
            'c_name',
            'representative',
            'jobtitle',
            'c_address',
            'phone',
            'email',
            'authorization_letter',
            'cccd',
            'receiver',
            'receiver_phone',
            'receiver_address',
            'customer_type',
            'contract_path',
            'status',
            'term_changed',
            'term_changed_notes',
            'stage',
            'stage_next',
            'contract_data',
            'vips_vi',
            'vips_en',
            'uploader_id',
        ];
        $valid_keys = array_flip($keys_update);
        $invalid_keys = $changed_data = [];

        // Kiểm tra các key không hợp lệ
        foreach ($data as $key => $value) {
            if (!isset($valid_keys[$key])) {
                $invalid_keys[] = $key;
            }
        }
        if (!empty($invalid_keys)) {
            return $this->result->setError()
                ->setCode('2002')
                ->setMessage('Missing fields: ' . implode(', ', $invalid_keys) . ' in otherdata')
                ->getResult();
        }

        // Xử lý thay đổi dữ liệu
        $array_sql = [];
        foreach ($data as $key => $newValue) {
            if (isset($row_old[$key]) && $row_old[$key] != $newValue) {
                $array_sql[$key] = $key . '=:' . $key;
                $changed_data[$key] = [
                    'old' => $row_old[$key],
                    'new' => $newValue
                ];
            }
        }
        if (!empty($array_sql)) {
            try {
                $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET ' . implode(', ', $array_sql) . ', updated_at=' . NV_CURRENTTIME . ' WHERE id=' . $econtract_id;
                if (!empty($where)) {
                    $sql .= ' AND ' . implode(' AND ', $where);
                }
                $stmt = $db->prepare($sql);

                foreach ($data as $key => $value) {
                    $paramType = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
                    $stmt->bindValue(':' . $key, $value, $paramType);
                }
                $exc = $stmt->execute();

                if ($exc) {
                    // Lưu log cập nhật
                    create_log_econtract([
                        'econtract_id' => $row_old['id'],
                        'version_id' => $row_old['current_version'],
                        'action' => 1,
                        'user_id' => $customer_id,
                        'action_desc_vi' => '<strong>Cập nhật</strong> thông tin hợp đồng',
                        'action_desc_en' => '<strong>Update</strong> information contract',
                        'changed_data' => json_encode($changed_data, JSON_UNESCAPED_UNICODE),
                        'log_visible' => 1,
                        'created_at' => NV_CURRENTTIME
                    ]);

                    $this->result->setSuccess();
                    $this->result->set('Econtract', $econtract_id);
                } else {
                    $this->result->setError()
                        ->setCode('3001')
                        ->setMessage('Update error');
                }
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        }
        return $this->result->getResult();
    }
}
