<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 16 Jan 2018 07:25:26 GMT
 */
if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

$data_config = $module_config[$module_name] ?? [];

// File enum quản lý status và stage của e-contract
require NV_ROOTDIR . '/modules/' . $module_file . '/enum.php';

$sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_groups_leads WHERE active=1 ORDER BY weight ASC";
$array_groups_leads = $nv_Cache->db($sql, 'id', $module_name);

$sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_label WHERE active=1 ORDER BY weight ASC";
$array_label = $nv_Cache->db($sql, 'id', $module_name);

// Tất cả quản trị của site (không tính quản trị bị đình chỉ)
global $array_user_id_users;
$_sql = 'SELECT tb1.userid, tb1.first_name, tb1.last_name, tb1.username, tb1.email FROM ' . NV_USERS_GLOBALTABLE . ' tb1 INNER JOIN ' . $db_config['prefix'] . '_authors tb2 ON tb1.userid = tb2.admin_id WHERE tb1.userid IN (SELECT `admin_id` FROM ' . NV_AUTHORS_GLOBALTABLE . ' ORDER BY lev ASC) AND tb1.active = 1 AND tb2.is_suspend = 0';
$array_user_id_users = $nv_Cache->db($_sql, 'userid', 'users');

// Toàn bộ Quản trị site (tính luôn quản trị bị đình chỉ)
$_sql = 'SELECT tb1.userid, tb1.first_name, tb1.last_name, tb1.username, tb1.email FROM ' . NV_USERS_GLOBALTABLE . ' tb1 INNER JOIN ' . $db_config['prefix'] . '_authors tb2 ON tb1.userid = tb2.admin_id WHERE tb1.userid IN (SELECT `admin_id` FROM ' . NV_AUTHORS_GLOBALTABLE . ' ORDER BY lev ASC) AND tb1.active = 1';
$all_array_user_id_users = $nv_Cache->db($_sql, 'userid', 'users');

// Danh sách gói VIP
$global_arr_vip = [
    1 => $nv_Lang->getModule('vip1'),
    11 => $nv_Lang->getModule('vip11'),
    2 => $nv_Lang->getModule('vip2'),
    21 => $nv_Lang->getModule('vip21'),
    3 => $nv_Lang->getModule('vip3'),
    31 => $nv_Lang->getModule('vip31'),
    32 => $nv_Lang->getModule('vip32'),
    4 => $nv_Lang->getModule('vip4'),
    5 => $nv_Lang->getModule('vip5'),
    99 => $nv_Lang->getModule('vip99'),
    200 => $nv_Lang->getModule('non_vip99'),
    88 => $nv_Lang->getModule('vip88'),
    77 => $nv_Lang->getModule('vip77'),
    69 => $nv_Lang->getModule('vip69'),
    6 => $nv_Lang->getModule('vip6'),
    7 => $nv_Lang->getModule('vip7'),
    8 => $nv_Lang->getModule('vip8'),
    19 => $nv_Lang->getModule('vip19'),
    55 => $nv_Lang->getModule('vip55'),
    66 => $nv_Lang->getModule('vip66'),
    68 => $nv_Lang->getModule('vip68'),
    89 => $nv_Lang->getModule('vip89'),
    100 => $nv_Lang->getModule('vip100'),
    101 => $nv_Lang->getModule('vip101')
];

$global_arr_package = [
    '1' => $nv_Lang->getModule('vip1'),
    '2' => $nv_Lang->getModule('vip2'),
    '3' => $nv_Lang->getModule('vip3'),
    '31' => $nv_Lang->getModule('vip31'),
    '32' => $nv_Lang->getModule('vip32'),
    '331' => $nv_Lang->getModule('vip331'),
    '332' => $nv_Lang->getModule('vip332'),
    '11' => $nv_Lang->getModule('vip11'),
    '21' => $nv_Lang->getModule('vip21'),
    '5' => $nv_Lang->getModule('vip5'),
    '4' => $nv_Lang->getModule('vip4'),
    '99' => $nv_Lang->getModule('vip99'),
    '88' => $nv_Lang->getModule('vip88'),
    '77' => $nv_Lang->getModule('vip77'),
    '69' => $nv_Lang->getModule('vip69'),
    '6' => $nv_Lang->getModule('vip6'),
    '7' => $nv_Lang->getModule('vip7'),
    '8' => $nv_Lang->getModule('vip8'),
    '19' => $nv_Lang->getModule('vip19'),
    '33' => $nv_Lang->getModule('vip33'),
    '44' => $nv_Lang->getModule('vip44'),
    '66' => $nv_Lang->getModule('vip66'),
    '68' => $nv_Lang->getModule('vip68'),
    '89' => $nv_Lang->getModule('vip89'),
    '55' => $nv_Lang->getModule('vip55'),
    '100' => $nv_Lang->getModule('vip100'),
    'bvieweb' => $nv_Lang->getModule('bvieweb'),
    'bvip1' => $nv_Lang->getModule('bvip1'),
    'bvip2' => $nv_Lang->getModule('bvip2'),
    'bpro' => $nv_Lang->getModule('bpro'),
    'abasic' => $nv_Lang->getModule('abasic'),
    'avip1' => $nv_Lang->getModule('avip1'),
    'apro1' => $nv_Lang->getModule('apro1'),
    'apro2' => $nv_Lang->getModule('apro2'),
    'x3' => $nv_Lang->getModule('x3'),
    'authen' => $nv_Lang->getModule('authen_profile'),
    'toup_point' => $nv_Lang->getModule('wallet_point')
];

$array_status = array();
$array_status[0] = $nv_Lang->getModule('status0');
$array_status[1] = $nv_Lang->getModule('status1');
$array_status[2] = $nv_Lang->getModule('status2');
$array_status[3] = $nv_Lang->getModule('status3');
$array_status[4] = $nv_Lang->getModule('status4');

$array_status_opportunities = array();
$array_status_opportunities[0] = $nv_Lang->getModule('status0');
$array_status_opportunities[1] = $nv_Lang->getModule('status_opportunities1');
$array_status_opportunities[2] = $nv_Lang->getModule('status_opportunities2');
$array_status_opportunities[3] = $nv_Lang->getModule('status_opportunities3');
$array_status_opportunities[4] = $nv_Lang->getModule('status_opportunities4');

global $array_site;
$array_site = array();
$array_site[0] = 'CRM';
$array_site[1] = 'DauThau.info';
$array_site[2] = 'DauThau.Net';
$array_site[3] = 'Tracnghiem.dauthau.asia';

$array_lang = [
    0 => $nv_Lang->getModule('lang_vi'),
    1 => $nv_Lang->getModule('lang_en')
];

$_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups ORDER BY weight ASC';
$groupsList = $nv_Cache->db($_sql, 'group_id', $module_name);

$global_array_genders = [
    'N' => [
        'key' => 'N',
        'title' => $nv_Lang->getModule('na'),
        'selected' => ''
    ],
    'M' => [
        'key' => 'M',
        'title' => $nv_Lang->getModule('male'),
        'selected' => ''
    ],
    'F' => [
        'key' => 'F',
        'title' => $nv_Lang->getModule('female'),
        'selected' => ''
    ]
];

$arr_status_order = [
    0 => $nv_Lang->getModule('history_payment_no'),
    1 => $nv_Lang->getModule('history_payment_send'),
    2 => $nv_Lang->getModule('history_payment_check'),
    3 => $nv_Lang->getModule('history_payment_cancel'),
    4 => $nv_Lang->getModule('history_payment_yes'),
    5 => $nv_Lang->getModule('history_payment_cancel_by_user'),
    6 => $nv_Lang->getModule('history_payment_cancel_by_admin')
];

// Nguồn thanh toán
$arr_source_money = [
    0 => $nv_Lang->getModule('source_money0'),
    1 => $nv_Lang->getModule('source_money1'),
    2 => $nv_Lang->getModule('source_money2'),
    3 => $nv_Lang->getModule('source_money3'),
    4 => $nv_Lang->getModule('source_money4'),
    5 => $nv_Lang->getModule('source_money5'),
    6 => $nv_Lang->getModule('source_money6'),
    7 => $nv_Lang->getModule('source_money7'),
    8 => $nv_Lang->getModule('source_money8')
];

/**
 * Dungpt?: Một sale có thể thuộc nhiều nhóm,
 * chỗ này lấy key là userid thì chỉ lấy 1 nhóm cuối cùng trong danh sách?
 * Chưa hiểu vì sao?
 */
if (empty($array_groups_users)) {
    $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_crmbidding_groups_users ORDER BY group_id ASC, userid ASC';
    $result = $db->query($sql);
    $array_groups_users = $array_user_gr = [];
    while ($row = $result->fetch()) {
        $row['config'] = json_decode($row['config'], true);
        $row['config_percent'] = json_decode($row['config_percent'], true);
        $array_groups_users[$row['userid']] = $row;
        $array_user_gr[$row['userid']][$row['group_id']] = $row;
        $arr_group_user2[$row['group_id']][$row['userid']] = $row; // Danh sách user theo nhóm
    }
}

/**
 * Danh sách sale mà mình đang quản lý
 * Cấu hình các nhóm của mình
 */
$my_managed_users = $my_admin_config = [];
if (!empty($array_user_gr[$admin_info['userid']])) {
    foreach ($array_user_gr[$admin_info['userid']] as $_gr_id => $_gr_row) {
        $my_admin_config = array_merge($my_admin_config, $_gr_row['config'] ?? []);
        if ($_gr_row['is_leader'] == 1) {
            if (!empty($arr_group_user2[$_gr_id])) {
                foreach ($arr_group_user2[$_gr_id] as $_uid => $_gr_row2) {
                    $my_managed_users[$_uid] = $_gr_row2;
                }
            }
        }
    }
}
$my_managed_users[$admin_info['userid']] = $array_groups_users[$admin_info['userid']] ?? [];

function update_es()
{
    global $module_config, $db;

    if (!defined('CRM_ELASTIC_HOST')) {
        define('CRM_ELASTIC_HOST', $module_config['crmbidding']['elas_host']);
        define('CRM_ELASTIC_PORT', $module_config['crmbidding']['elas_port']);
        define('CRM_ELASTIC_USER', $module_config['crmbidding']['elas_user']);
        define('CRM_ELASTIC_PASS', $module_config['crmbidding']['elas_pass']);
    }

    try {
        $waitTimeoutInSeconds = 2;
        if ($fp = fsockopen(CRM_ELASTIC_HOST, CRM_ELASTIC_PORT, $errCode, $errStr, $waitTimeoutInSeconds)) {
            // It worked
            $elastic_online = 1;
        } else {
            $elastic_online = 0;
        }

        // Không kết nối được elastic thì ngưng.
        if ($fp == false)
            return;

        fclose($fp);
        if ($elastic_online) {

            $hosts = array(
                CRM_ELASTIC_HOST . ':' . CRM_ELASTIC_PORT
            );

            // Leads
            $params = [
                'body' => []
            ];

            $query_url = $db->query('SELECT * FROM nv4_vi_crmbidding_leads WHERE elasticsearch = 0 ORDER BY updatetime ASC LIMIT 50');
            $arr_leads_id = [];
            while ($row = $query_url->fetch()) {

                // các biến đưa lên es thì gán thêm dấu cách sau dấu ,
                if ($row['label'] != '') {
                    $row['label'] = explode(',', $row['label']);
                    $row['label'] = implode(', ', $row['label']);
                }
                if ($row['sub_phone'] != '') {
                    $row['sub_phone'] = explode(',', $row['sub_phone']);
                    $row['sub_phone'] = implode(', ', $row['sub_phone']);
                }
                if ($row['sub_phone_search'] != '') {
                    $row['sub_phone_search'] = explode(',', $row['sub_phone_search']);
                    $row['sub_phone_search'] = implode(', ', $row['sub_phone_search']);
                }
                if ($row['sub_email'] != '') {
                    $row['sub_email'] = explode(',', $row['sub_email']);
                    $row['sub_email'] = implode(', ', $row['sub_email']);
                }
                $params['body'][] = [
                    'index' => [
                        '_index' => 'crm_leads',
                        '_id' => $row['id']
                    ]
                ];
                $params['body'][] = $row;
                $arr_leads_id[] = $row['id'];
            }

            if (!empty($params['body'])) {
                $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication(CRM_ELASTIC_USER, CRM_ELASTIC_PASS)
                    ->setHosts($hosts)
                    ->setRetries(0)
                    ->build();

                $responses = $client->bulk($params)->asArray();
                if (empty($responses['errors'])) {
                    $db->query('UPDATE nv4_vi_crmbidding_leads SET elasticsearch = ' . NV_CURRENTTIME . '  WHERE id IN (' . implode(',', $arr_leads_id) . ')');
                    unset($responses['items']);
                }
            }

            // Nhà thầu
            $params = [
                'body' => []
            ];
            $query_url = $db->query('SELECT * FROM nv4_vi_crmbidding_opportunities WHERE elasticsearch = 0 ORDER BY updatetime ASC LIMIT 50');
            $array_id = [];
            while ($row = $query_url->fetch()) {
                if ($row['label'] != '') {
                    $row['label'] = explode(',', $row['label']);
                    $row['label'] = implode(', ', $row['label']);
                }
                if ($row['sub_phone'] != '') {
                    $row['sub_phone'] = explode(',', $row['sub_phone']);
                    $row['sub_phone'] = implode(', ', $row['sub_phone']);
                }
                if ($row['sub_phone_search'] != '') {
                    $row['sub_phone_search'] = explode(',', $row['sub_phone_search']);
                    $row['sub_phone_search'] = implode(', ', $row['sub_phone_search']);
                }
                if ($row['sub_email'] != '') {
                    $row['sub_email'] = explode(',', $row['sub_email']);
                    $row['sub_email'] = implode(', ', $row['sub_email']);
                }
                if ($row['orderid'] != '') {
                    $row['orderid'] = explode(',', $row['orderid']);
                    $row['orderid'] = implode(', ', $row['orderid']);
                }
                if (!empty($row['customs_id'])) {
                    $row['customs_id'] = explode(',', $row['customs_id']);
                    $row['customs_id'] = implode(', ', $row['customs_id']);
                }
                $params['body'][] = [
                    'index' => [
                        '_index' => 'crm_opportunities',
                        '_id' => $row['id']
                    ]
                ];
                $params['body'][] = $row;
                $array_id[] = $row['id'];
            }
            if (!empty($params['body'])) {
                $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication(CRM_ELASTIC_USER, CRM_ELASTIC_PASS)
                    ->setHosts($hosts)
                    ->setRetries(0)
                    ->build();

                $responses = $client->bulk($params)->asArray();
                if (empty($responses['errors'])) {
                    $db->query('UPDATE nv4_vi_crmbidding_opportunities SET elasticsearch = ' . NV_CURRENTTIME . '  WHERE id IN (' . implode(',', $array_id) . ')');
                    unset($responses['items']);
                }
            }
        }
    } catch (Exception $e) {
        trigger_error($e);
    }
}

/*
 * $array_where
 * viết dạng array where của API
 * $where = [];
 * $where['AND'][] = [
 * '=' => [
 * 'user_id' => 8223,
 * ],
 * ];
 */
function QueryMysqlAPI_to_ES($array_where)
{
    global $db;

    $search_elastic = [];
    $key_must = 0;
    $i_AND_OR = 0;
    foreach ($array_where as $k1 => $keys) {
        foreach ($keys as $key) {
            $operator = array_key_first($key);
            $field = array_key_first($key[$operator]);
            $value = $key[$operator][$field];
            $value = html_entity_decode($value, ENT_QUOTES | ENT_HTML401 | ENT_HTML5);
            // $value = trim($value, "'");

            if ($k1 == 'OR') {
                $_operator_es = 'should';
            } else if ($k1 == 'AND') {
                $_operator_es = 'must';
            } else if ($k1 == 'AND_OR') {
                $_operator_es = 'should';
                $i_AND_OR++;
            }

            if ($operator == 'NOT IN' or $operator == 'NOT' or $operator == '!=') {
                $_operator_es = 'must_not';
            } else if ($operator == 'IN') {
                $_operator_es = 'should';
            }
            if ($i_AND_OR > 0 and $k1 == 'AND_OR') {
                if (isset($search_elastic['must'])) {
                    foreach ($search_elastic['must'] as $_key_must => $value_must) {
                        if (isset($value_must['bool']['should'])) {
                            $key_must = $_key_must;
                        } else {
                            $key_must = $_key_must + 1;
                        }
                    }
                }
            }

            switch ($operator) {
                case '=':
                    if ($k1 == 'AND_OR') {
                        $search_elastic['must'][$key_must]['bool'][$_operator_es][] = [
                            "match" => [
                                $field => $value
                            ]
                        ];
                    } else {
                        if ($_operator_es == 'should') {
                            $search_elastic[$_operator_es][] = [
                                "match_phrase" => [
                                    $field => $value
                                ]
                            ];
                        } else {
                            if ($field == 'name' || $field == 'email' || $field == 'phone') {
                                $field = $field . '.keyword';
                            }
                            $search_elastic[$_operator_es][] = [
                                "match" => [
                                    $field => $value
                                ]
                            ];
                        }
                    }
                    break;
                case '!=':
                    if ($_operator_es == 'should') {
                        $search_elastic[$_operator_es][] = [
                            "match_phrase" => [
                                $field => $value
                            ]
                        ];
                    } else {
                        if ($field == 'name' || $field == 'email' || $field == 'phone') {
                            $field = $field . '.keyword';
                        }
                        $search_elastic[$_operator_es][] = [
                            "match" => [
                                $field => $value
                            ]
                        ];
                    }
                    break;

                case 'NOT IN':
                    $new_value = trim($value, '(');
                    $new_value = trim($new_value, ')');
                    $arr_key = explode(',', $new_value);
                    foreach ($arr_key as $k) {
                        $search_elastic[$_operator_es][] = [
                            "match" => [
                                $field => $k
                            ]
                        ];
                    }
                    break;

                case 'IN':
                    $new_value = trim($value, '(');
                    $new_value = trim($new_value, ')');
                    $arr_key = explode(',', $new_value);
                    if ($k1 == 'AND_OR') {
                        foreach ($arr_key as $k) {
                            $search_elastic['must'][$key_must]['bool'][$_operator_es][] = [
                                "match" => [
                                    $field => $k
                                ]
                            ];
                        }
                    } else {
                        $_search_elastic = [];
                        foreach ($arr_key as $k) {
                            $_search_elastic[$_operator_es][] = [
                                "match" => [
                                    $field => $k
                                ]
                            ];
                        }
                        $search_elastic['must'][]['bool'] = $_search_elastic;
                    }
                    break;
                case 'like':
                    $search_elastic[$_operator_es][] = [
                        "match_phrase_prefix" => [
                            $field => str_replace('%', '', $value)
                        ]
                    ];
                    break;
                case 'FIND_IN_SET':
                    $search_elastic[$_operator_es][] = [
                        "match_phrase" => [
                            $field => $value
                        ]
                    ];
                    break;
                case '>=':
                    $search_elastic[$_operator_es][]['range'][$field] = [
                        "gte" => $value
                    ];
                    break;
                case '>':
                    $search_elastic[$_operator_es][]['range'][$field] = [
                        "gt" => $value
                    ];
                    break;
                case '<=':
                    $search_elastic[$_operator_es][]['range'][$field] = [
                        "lte" => $value
                    ];
                    break;
                case '<':
                    $search_elastic[$_operator_es][]['range'][$field] = [
                        "lt" => $value
                    ];
                    break;
            }
        }
    }
    return $search_elastic;
}

/**
 * get_value_by_lang2()
 *
 * @param mixed $key
 * @param mixed $value
 * @return mixed
 */
function get_value_by_lang2($key, $value)
{
    if (is_array($value)) {
        if (!empty($value[NV_LANG_DATA])) {
            $return = $value[NV_LANG_DATA];
        } else {
            $return = '';
        }
    } else {
        $return = $value;
    }
    empty($return) && $return = $key;

    return $return;
}

//Danh sách mạng xã hội
enum SocialNetwork: string
{
    case Facebook = 'facebook';
    case Zalo = 'zalo';
    case LinkedIn = 'linkedin';
    case Instagram = 'instagram';
    case Twitter = 'twitter';

    public function getIcon()
    {
        return match ($this) {
            self::Facebook => 'icon_facebok',
            self::Zalo => 'icon_zalo',
            self::LinkedIn => 'icon_linkedin',
            self::Instagram => 'icon_instagram',
            self::Twitter => 'icon_twitter',
        };
    }

    public function getLabel()
    {
        global $nv_Lang;
        return match ($this) {
            self::Facebook => $nv_Lang->getModule('social_facebook'),
            self::Zalo => $nv_Lang->getModule('social_zalo'),
            self::LinkedIn => $nv_Lang->getModule('social_linkedin'),
            self::Instagram => $nv_Lang->getModule('social_instagram'),
            self::Twitter => $nv_Lang->getModule('social_twitter'),
        };
    }
}

/**
 * Hàm lấy lead/cơ hội kèm khóa update row hiện tại trên bảng lead/cơ hội. Hàm này chỉ được gọi bên trong khối transaction,
 * nó có nhiệm vụ khóa 1 hàng bất kỳ không cho phiên làm việc khác update cho đến khi phiên hiện tại commit hoặc rollback
 * @param mixed $row
 * $row bản ghi cần locking
 * @param int $type
 * $type 1 là lead, 2 là cơ hội
 */
function lead_with_locking($row, $type = 1)
{
    global $db, $module_data;

    $id_lead = ($type == 1) ? $row['id'] : $row['leadsid'];
    if ($id_lead > 0) {
        $lead_with_locking = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_leads WHERE id=' . $id_lead . ' FOR UPDATE')->fetch();
    }

    $id_opportunity = ($type == 1) ? $row['opportunities_id'] : $row['id'];
    if ($id_opportunity > 0) {
        $opportunity_with_locking = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities WHERE id=' . $id_opportunity . ' FOR UPDATE')->fetch();
    }
}

//Trạng thái hóa đơn điện tử
$array_econtract_status = [
    EContractStatus::Deleted->value,
    EContractStatus::Incomplete->value,
    EContractStatus::ContentIncomplete->value,
    EContractStatus::HSTDTSigned->value,
    EContractStatus::CustomerSigned->value,
    EContractStatus::TermsChanged->value,
    EContractStatus::Done->value
];

// Giải đoạn của hóa đơn điện tử
$array_econtract_stage = [
    EContractStage::Negotiating->value,
    EContractStage::SupplementingInfo->value,
    EContractStage::CustomerSignatureRequired->value,
    EContractStage::HSTDTSignatureRequired->value,
    EContractStage::CustomerContractReview->value
];

function create_log_econtract($data)
{
    global $db;

    try {
        $data['changed_data'] = !empty($data['changed_data']) ? $data['changed_data'] : '';
        $data['log_visible'] = !empty($data['log_visible']) ? $data['log_visible'] : 1;
        $data['created_at'] = !empty($data['created_at']) ? $data['created_at'] : NV_CURRENTTIME;

        // Insert log
        $query = 'INSERT INTO ' . NV_PREFIXLANG . '_crmbidding_econtract_logs
            (econtract_id, version_id, action, user_id, action_desc_vi, action_desc_en, changed_data, log_visible, created_at)
            VALUES (:econtract_id, :version_id, :action, :user_id, :action_desc_vi, :action_desc_en, :changed_data, :log_visible, :created_at)';

        $stmt = $db->prepare($query);

        $stmt->bindParam(':econtract_id', $data['econtract_id'], PDO::PARAM_INT);
        $stmt->bindParam(':version_id', $data['version_id'], PDO::PARAM_INT);
        $stmt->bindParam(':action', $data['action'], PDO::PARAM_INT);
        $stmt->bindParam(':user_id', $data['user_id'], PDO::PARAM_INT);
        $stmt->bindParam(':action_desc_vi', $data['action_desc_vi'], PDO::PARAM_STR);
        $stmt->bindParam(':action_desc_en', $data['action_desc_en'], PDO::PARAM_STR);
        $stmt->bindParam(':changed_data', $data['changed_data'], PDO::PARAM_STR);
        $stmt->bindParam(':log_visible', $data['log_visible'], PDO::PARAM_INT);
        $stmt->bindParam(':created_at', $data['created_at'], PDO::PARAM_INT);

        $exc = $stmt->execute();

        if (!$exc) {
            error_log('Ghi log thất bại: ' . json_encode($data));
        }

        return $exc;
    } catch (PDOException $e) {
        trigger_error('Lỗi SQL: ' . $e->getMessage());
        return false;
    }
}

function create_version_econtract($data)
{
    global $db, $module_data;

    try {
        // Insert version
        $query = 'INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions (econtract_id, user_id, version, pdf_path, contract_data, created_at)
        VALUES (:econtract_id, :user_id, :version, :pdf_path, :contract_data, :created_at)';
        $stmt = $db->prepare($query);
        $stmt->bindParam(':econtract_id', $data['econtract_id'], PDO::PARAM_INT);
        $stmt->bindParam(':user_id', $data['user_id'], PDO::PARAM_INT);
        $stmt->bindParam(':version', $data['version'], PDO::PARAM_INT);
        $stmt->bindParam(':pdf_path', $data['pdf_path'], PDO::PARAM_STR);
        $stmt->bindParam(':contract_data', $data['contract_data'], PDO::PARAM_STR);
        $stmt->bindValue(':created_at', NV_CURRENTTIME, PDO::PARAM_INT);
        $stmt->execute();

        // Cập nhật current_version của econtract thành phiên bản mới nhất
        $new_version_id = $db->lastInsertId();
        if (!empty($new_version_id)) {
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET current_version=' . $new_version_id . ' WHERE id=' . $data['econtract_id']);
        }
    } catch (PDOException $e) {
        trigger_error('Lỗi SQL: ' . $e->getMessage());
        return false;
    }
}

function numberToWords($amount, $end = true)
{
    if ($amount <= 0) {
        return $textnumber = "Tiền phải là số nguyên dương lớn hơn số 0";
    }
    if (NV_LANG_DATA == 'vi') {
        $Text = array(
            "không",
            "một",
            "hai",
            "ba",
            "bốn",
            "năm",
            "sáu",
            "bảy",
            "tám",
            "chín"
        );
        $TextLuythua = array(
            "",
            "nghìn",
            "triệu",
            "tỷ",
            "nghìn",
            "triệu",
            "tỷ"
        );
        // Phai de cac ham replace theo dung thu tu nhu the nay
        $thaythe = array(
            "không mươi" => "lẻ",
            "lẻ không" => "",
            "mươi không" => "mươi",
            "một mươi" => "mười",
            "mươi năm" => "mươi lăm",
            "mươi một" => "mươi mốt",
            "mười năm" => "mười lăm"
        );

        $textnumber = "";
        $length = strlen($amount);

        for ($i = 0; $i < $length; $i++)
            $unread[$i] = 0;

        for ($i = 0; $i < $length; $i++) {
            $so = substr($amount, $length - $i - 1, 1);

            if (($so == 0) && ($i % 3 == 0) && ($unread[$i] == 0)) {
                for ($j = $i + 1; $j < $length; $j++) {
                    $so1 = substr($amount, $length - $j - 1, 1);
                    if ($so1 != 0)
                        break;
                }

                if (intval(($j - $i) / 3) > 0) {
                    for ($k = $i; $k < intval(($j - $i) / 3) * 3 + $i; $k++)
                        $unread[$k] = 1;
                }
            }
        }

        for ($i = 0; $i < $length; $i++) {
            $so = substr($amount, $length - $i - 1, 1);
            if ($unread[$i] == 1)
                continue;

            if (($i % 3 == 0) && ($i > 0))
                $textnumber = $TextLuythua[$i / 3] . " " . $textnumber;

            if ($i % 3 == 2)
                $textnumber = 'trăm ' . $textnumber;

            if ($i % 3 == 1)
                $textnumber = 'mươi ' . $textnumber;

            $textnumber = $Text[$so] . " " . $textnumber;
        }

        foreach ($thaythe as $k => $v) {
            $textnumber = str_replace($k, $v, $textnumber);
        }

        return ucfirst(trim($textnumber) . ($end ? " đồng" : ''));
    } else {
        $amount = (int) $amount;
        $words = [];
        $list1 = [
            '',
            'one',
            'two',
            'three',
            'four',
            'five',
            'six',
            'seven',
            'eight',
            'nine',
            'ten',
            'eleven',
            'twelve',
            'thirteen',
            'fourteen',
            'fifteen',
            'sixteen',
            'seventeen',
            'eighteen',
            'nineteen'
        ];
        $list2 = [
            '',
            'ten',
            'twenty',
            'thirty',
            'forty',
            'fifty',
            'sixty',
            'seventy',
            'eighty',
            'ninety',
            'hundred'
        ];
        $list3 = [
            '',
            'thousand',
            'million',
            'billion',
            'trillion',
            'quadrillion',
            'quintillion',
            'sextillion',
            'septillion',
            'octillion',
            'nonillion',
            'decillion',
            'undecillion',
            'duodecillion',
            'tredecillion',
            'quattuordecillion',
            'quindecillion',
            'sexdecillion',
            'septendecillion',
            'octodecillion',
            'novemdecillion',
            'vigintillion'
        ];
        $amount_length = strlen($amount);
        $levels = (int) (($amount_length + 2) / 3);
        $max_length = $levels * 3;
        $amount = substr('00' . $amount, -$max_length);
        $amount_levels = str_split($amount, 3);
        for ($i = 0; $i < count($amount_levels); $i++) {
            $levels--;
            $hundreds = (int) ($amount_levels[$i] / 100);
            $hundreds = ($hundreds ? ' ' . $list1[$hundreds] . ' hundred' . ' ' : '');
            $tens = (int) ($amount_levels[$i] % 100);
            $singles = '';
            if ($tens < 20) {
                $tens = ($tens ? ' ' . $list1[$tens] . ' ' : '');
            } else {
                $tens = (int) ($tens / 10);
                $tens = ' ' . $list2[$tens] . ' ';
                $singles = (int) ($amount_levels[$i] % 10);
                $singles = ' ' . $list1[$singles] . ' ';
            }
            $words[] = $hundreds . $tens . $singles . (($levels && (int) ($amount_levels[$i])) ? ' ' . $list3[$levels] . ' ' : '');
        } // end for loop
        $commas = count($words);
        if ($commas > 1) {
            $commas = $commas - 1;
        }
        $textnumber = preg_replace('/\s+/', ' ', trim(implode(' ', $words)));
        return ucfirst($textnumber . ($end ? ' dong' : ''));
    }
}

// Gửi mail
if (!function_exists('nv_pending_mail')) {
    function nv_pending_mail($subject, $messages, $main_mail, $cc_mail = '')
    {
        global $db;

        try {
            $stmt = $db->prepare("INSERT INTO nv4_vi_bidding_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, source_email, `messageid`, `reject`, `bounce`, `complaint`, `click`, `open`, `failure`) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, :source_email, '', '', '', '', '', '', '')");
            $stmt->bindValue(':userid', 0, PDO::PARAM_INT);
            $stmt->bindParam(':main_mail', $main_mail, PDO::PARAM_STR);
            $stmt->bindValue(':cc_mail', $cc_mail, PDO::PARAM_STR);
            $stmt->bindValue(':number_phone', 0, PDO::PARAM_STR);
            $stmt->bindParam(':title', $subject, PDO::PARAM_STR);
            $stmt->bindParam(':content', $messages, PDO::PARAM_STR, strlen($messages));
            $stmt->bindValue(':type', 0, PDO::PARAM_INT);
            $stmt->bindValue(':vip', 0, PDO::PARAM_INT);
            $stmt->bindValue(':addtime', NV_CURRENTTIME, PDO::PARAM_INT);
            $stmt->bindValue(':send_time', 0, PDO::PARAM_INT);
            $stmt->bindValue(':status', 0, PDO::PARAM_INT);
            $stmt->bindValue(':source_email', 0, PDO::PARAM_INT);
            $stmt->execute();
        } catch (PDOException $e) {
            trigger_error($e);
        }
    }
}

function convert_data_vip($data_vip)
{

    // $data_vip = [
    //     ['VIP 1', 1],    // 1 năm
    //     ['VIP 2', 0.5],    // 1 năm
    //     ['VIP 3', 1],  // 6 tháng
    // ];

    /**
     * Dữ liệu trả về
     * Array (
     *    [vip] => VIP 1, VIP 2, VIP 3
     *    [year] => 1 năm đối với Gói VIP 1, 1 năm đối với Gói VIP 3 và 6 tháng với Gói VIP 2
     * )
     */

    $grouped_by_year = [
        'year' => [],
        'month' => []
    ];

    foreach ($data_vip as $item) {
        $vip_name = $item[0];
        $years = $item[1];

        /**
         * Nếu $years < 1 gói vip mua theo tháng
         */
        if ($years < 1) {
            $month = round($years * 12);
            $grouped_by_year['month'][$month][] = $vip_name;
        } else {
            $grouped_by_year['year'][$years][] = $vip_name;
        }
    }

    $data_vip_convert = [
        'vip' => implode(', ', array_unique(array_column($data_vip, 0))),
        'year' => ''
    ];

    $unique_years = array_keys($grouped_by_year['year']);

    if (count($unique_years) === 1 && empty($grouped_by_year['month'])) {
        $data_vip_convert['year'] .= $unique_years[0] . " năm";
    } else {
        $result = [];
        foreach ($unique_years as $years) {
            foreach ($grouped_by_year['year'][$years] as $vip_name) {
                $result[] = "$years năm đối với Gói $vip_name";
            }
        }

        $data_vip_convert['year'] .= implode(", ", $result);
    }

    if (!empty($grouped_by_year['month'])) {
        $month_values = array_keys($grouped_by_year['month']);

        // Nếu chỉ có 1 giá trị tháng, gộp chung lại
        if (count($month_values) === 1 && empty($grouped_by_year['year'])) {
            $month_result = "{$month_values[0]} tháng";
        } else {
            // Nếu có nhiều tháng khác nhau, liệt kê chi tiết
            $month_result = array_map(function ($months) use ($grouped_by_year) {
                return array_map(function ($vip_name) use ($months) {
                    return "$months tháng với Gói $vip_name";
                }, array_unique($grouped_by_year['month'][$months]));
            }, array_keys($grouped_by_year['month']));

            $month_result = implode(", ", array_merge(...$month_result));
        }

        if ($data_vip_convert['year']) {
            $data_vip_convert['year'] .= ", $month_result";
        } else {
            $data_vip_convert['year'] .= $month_result;
        }
    }

    if ($data_vip_convert['year']) {
        $data_vip_convert['year'] = preg_replace('/,([^,]*)$/', ' và\1', $data_vip_convert['year']);
    }

    return $data_vip_convert;
}

//Giới hạn dung lường file PDF tải lên
if (!defined('MAX_FILE_ECONTRACT_SIZE')) {
    define('MAX_FILE_ECONTRACT_SIZE', $data_config['max_file_econtract_file'] * 1024 * 1024); // Giới hạn file 10MB
}

// Hàm validate số hóa đơn
function formatInvoiceNumber($number)
{
    if (empty($number)) {
        return '';
    }

    if (preg_match('/^\d+$/', $number)) {
        $currentYear = date('y');
        return "1C{$currentYear}TYY - {$number}";
    }

    $formatted = preg_replace('/\s*-\s*/', ' - ', $number);

    if (!preg_match('/^1C(\d{2})TYY\s*-\s*\d+$/', $formatted, $matches)) {
        return '';
    }

    $year = intval($matches[1]);
    if ($year < 10 || $year > 99) {
        return '';
    }

    return $formatted;
}

/**
 * Hàm rest số hợp đồng
 * Khi qua năm mới thì số hợp đồng sẽ rest về lại thành 01
 * @return void
 */
function resetEContractNumber()
{
    global $db, $module_name, $nv_Cache, $module_data;

    $econtract_next_no = $db->query('SELECT config_value FROM ' . NV_CONFIG_GLOBALTABLE . ' WHERE lang=' . $db->quote(NV_LANG_DATA) . ' AND module=' . $db->quote($module_name) . ' AND config_name=' . $db->quote('econtract_next_no'))->fetchColumn() ?? 1;
    // Kiểm tra số hợp đồng điện tử cuối cùng trong năm
    $last_econtract = $db->query('SELECT created_at FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts ORDER BY created_at DESC LIMIT 1')->fetchColumn();
    $last_econtract_year = date('Y', $last_econtract);
    if ($last_econtract_year != date('Y') && $last_econtract > 2025) {
        $econtract_next_no = 1;
        $econtract_payment_proposal = 1;
        $sql_update_config = '
            UPDATE ' . NV_CONFIG_GLOBALTABLE . '
            SET
                config_value = CASE
                    WHEN config_name = ' . $db->quote('econtract_next_no') . ' THEN ' . $econtract_next_no . '
                    WHEN config_name = ' . $db->quote('econtract_payment_proposal') . ' THEN ' . $econtract_payment_proposal . '
                END
            WHERE lang=' . $db->quote(NV_LANG_DATA) . '
            AND module=' . $db->quote($module_name) . '
            AND config_name IN (' . $db->quote('econtract_next_no') . ', ' . $db->quote('econtract_payment_proposal') . ')';
        $db->query($sql_update_config);
        $nv_Cache->delMod($module_name);
    }
}

/**
 * chuyển dữ liệu trong mảng unicode tổ hợp -> unicode dững sắn
 *
 * @param array $array
 * @return array
 */
if (!function_exists('nv_compound_unicode_recursion')) {
    function nv_compound_unicode_recursion($array)
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = nv_compound_unicode_recursion($array[$key]);
            } elseif (is_string($value)) {
                $array[$key] = nv_compound_unicode($value);
            }
        }
        return $array;
    }
}

//Xử lý các đơn hàng mua theo combo
function group_combo_orders(array $items): array
{
    $result = [];
    $i = 0;
    $count = count($items);

    while ($i < $count) {
        $item = $items[$i];

        if ($item['is_combo'] == 1) {
            $rowspan = 1;

            // Đếm số dòng combo liên tiếp có cùng order_id và vip_price
            for ($j = $i + 1; $j < $count; $j++) {
                if (
                    $items[$j]['is_combo'] == 1 &&
                    $items[$j]['order_id'] == $item['order_id'] &&
                    $items[$j]['vip_price'] == $item['vip_price']
                ) {
                    $rowspan++;
                } else {
                    break;
                }
            }

            // Gán rowspan và giữ giá cho dòng đầu combo
            $item['rowspan'] = $rowspan;
            $result[] = $item;

            // Các dòng tiếp theo: bỏ giá, rowspan = 0
            for ($k = 1; $k < $rowspan; $k++) {
                $nextItem = $items[$i + $k];
                $nextItem['vip_price_format'] = '';
                $nextItem['rowspan'] = 0;
                $result[] = $nextItem;
            }

            $i += $rowspan;
        } else {
            $item['rowspan'] = 1;
            $result[] = $item;
            $i++;
        }
    }

    return $result;
}
