<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2023 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 27 Feb 2023 09:02:35 GMT
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Module\supportticket\Log;

global $nv_Request, $db, $nv_Lang, $nv_Cache, $array_ticket_cats, $array_ticket_labels, $array_admin_users;
// Get content info
if ($nv_Request->isset_request('getinfo', 'post')) {
    $id = $nv_Request->get_int('id', 'post', '0');
    $data = get_ticket_by_id($id);
    nv_jsonOutput($data);
}

//Get user info
if ($nv_Request->isset_request('view_users', 'post,get')) {
    $user_id = $nv_Request->get_int('user_id', 'post', '0');
    $array_user = [];
    $sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . " as a INNER JOIN " . NV_USERS_GLOBALTABLE . "_info as b ON a.userid= b.userid WHERE a.userid = " . $db->quote($user_id);
    $result = $db->query($sql);
    while ($_row = $result->fetch()) {
        $array_user = $_row;
    }
    $xtpl = new XTemplate('convert_user.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('MODULE_UPLOAD', $module_upload);
    $xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
    $xtpl->assign('OP', $op);
    if (!empty($array_user)) {
        $array_user['title'] = nv_show_name_user($array_user['first_name'], $array_user['last_name'], $array_user['username']);
        $array_user['link_view'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=users&amp;' . NV_OP_VARIABLE . '=edit&amp;userid=' . $array_user['userid'] . '&check_admin=' . md5(NV_CHECK_SESSION . $array_user['userid']);
        $array_user['gender'] = $global_array_genders[$array_user['gender']]['title'];
        $array_user['regdate'] = nv_date('d/m/Y', $array_user['regdate']);
        $xtpl->assign('USERS', $array_user);
        $xtpl->parse('main.user');
    }
    $xtpl->parse('main');
    $contents = $xtpl->text('main');
    include NV_ROOTDIR . '/includes/header.php';
    echo $contents;
    include NV_ROOTDIR . '/includes/footer.php';
}

// Edit detail
if ($nv_Request->isset_request('updatedetail', 'post')) {
    $ticket_id = $nv_Request->get_int('ticket_id', 'post', 0);
    $data = [
        'title' => nv_substr($nv_Request->get_title('title', 'post', ''), 0, 249),
        'content' => $nv_Request->get_editor('content', '', NV_ALLOWED_HTML_TAGS)
    ];
    $params_update = [
        'ticket_id' => $ticket_id,
        'admin_id' => $admin_info['userid'],
        'data' => $data,
    ];
    $update = nv_local_api('UpdateTicket', $params_update, $admin_info['username'], $module_name);
    $updateDetail = json_decode($update, true);

    nv_jsonOutput($updateDetail);
}

// Edit option
if ($nv_Request->isset_request('updateoption', 'post')) {
    $ticket_id = $nv_Request->get_int('ticket_id', 'post', 0);
    $vip = $nv_Request->get_int('vip', 'post', 0);
    $order = $nv_Request->get_int('order', 'post', 0);
    $label = $nv_Request->get_typed_array('label', 'post', 'int', []);
    $assignee = $nv_Request->get_typed_array('assignee', 'post', 'int', []);
    $status = $nv_Request->get_int('status', 'post', 0);
    $notify = $nv_Request->get_int('notify', 'post', 0);
    $data = [
        'vip_id' => $vip,
        'order_id' => $order,
        'label_ids' => implode(',', $label),
        'assignee_to' => implode(',', $assignee),
        'status' => $status,
        'notify' => $notify,
    ];
    //Ticket mới tạo mới được phép đổi bộ phận
    $cat = $nv_Request->get_int('cat', 'post', 0);
    $data['cat_id'] = $cat;
    $ticket = get_ticket_by_id($ticket_id);
    if (!empty($ticket) && $status == TicketStatus::Open->value) {
        if ($cat != $ticket['data']['cat_id']) {
            if (!empty($array_ticket_cats[$cat]) && $array_ticket_cats[$cat]['is_point'] == 1) {
                $data['is_paid'] = 1;
                $data['ask_expert'] = 1;
            } else {
                $data['is_paid'] = 0;
                $data['ask_expert'] = 0;
            }
        }
    }

    if ($status == TicketStatus::Close->value) {
        $data['status_client'] = TicketStatus::Close->value;
    }
    $params_update = [
        'ticket_id' => $ticket_id,
        'admin_id' => $admin_info['userid'],
        'data' => $data,
    ];
    $update = nv_local_api('UpdateTicket', $params_update, $admin_info['username'], $module_name);
    $updateDetail = json_decode($update, true);

    if ($data['status'] == TicketStatus::Close->value) {
        close_notification_to_user($ticket['data'], true);
    }

    nv_jsonOutput($updateDetail);
}

//Chi tiết ticket
$page_title = $nv_Lang->getModule('detail');
$row = [];
$row['id'] = $nv_Request->get_int('id', 'post,get', 0);
$per_page = 5;
$page = $nv_Request->get_int('page', 'post,get', 1);
$redirect = 0;
$array_related_status = $array_ticket_status;
if ($row['id'] > 0) {
    $ticket = get_ticket_by_id($row['id']);
    if (empty($ticket)) {
        $redirect = 1;
    } else {
        if ($ticket['canView']) {
            $row = $ticket['data'];
        } else {
            $redirect = 1;
        }
    }
} else {
    $redirect = 1;
}

if ($redirect == 1) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=tickets');
}

// Chuyển hướng khi là ticket trả phí
if ($row['is_paid'] == 1) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail_paid&amp;id=' . $row['id']);
}

$comment = $error = [];
//Kiểm tra trước khi edit comment
$commentid = $nv_Request->get_int('commentid', 'post,get', 0);
if ($commentid > 0) {
    $params = [
        'ticket_id' =>  $row['id'],
        'log_id'    =>  $commentid,
        'userid'    =>  $admin_info['userid']
    ];
    // GỌI API
    $comment_check = nv_local_api('GetComment', $params, $admin_info['username'], $module_name);
    $comment_check = json_decode($comment_check, true);
    $comment = $comment_check['data'];
    $comment['file_attach'] = decode_file($comment['file_attach']);
}

//Lấy danh sách comment
$params = [
    'ticket_id' =>  $row['id'],
    'page' => $page,
    'perpage' => $per_page
];
$comments = nv_local_api('ListAllComment', $params, $admin_info['username'], $module_name);
$comments = json_decode($comments, true);

if ($nv_Request->isset_request('commentSubmit', 'post') and $ticket['canReply']) {
    $comment['ticket_id'] = $row['id'];
    $comment['log_id'] = (isset($comment_check) && $comment_check['status'] == 'success' &&  $comment_check['canEdit']) ? $commentid : 0;
    $comment['reply_userid'] = $admin_info['userid'];
    $comment['display_userid'] = $nv_Request->get_int('comment_user', 'post', 0);
    $comment['display_userid'] = $comment['display_userid'] > 0 ? $comment['display_userid'] : $admin_info['userid'];
    $comment['area'] = 1;
    $comment['content'] = $nv_Request->get_editor('commentContent', '', NV_ALLOWED_HTML_TAGS);
    $comment['file_attach'] = $nv_Request->get_array('file_attach', 'post', []);
    $comment['comment_type'] = CommentType::Staff->value;

    $file_attach = [];
    foreach ($comment['file_attach'] as $file) {
        if (nv_is_file($file, NV_UPLOADS_DIR . '/' . $module_upload)) {
            $file = substr($file, strlen(NV_BASE_SITEURL . NV_UPLOADS_DIR . '/' . $module_upload . '/'));
        } else {
            $error = $nv_Lang->getModule('error_required_file');
            $file = '';
        }
        $file_attach[] = $file;
    }
    $comment['file_attach'] = $file_attach;

    if (empty($error)) {
        $create = nv_local_api('CreateComment', $comment, $admin_info['username'], $module_name);
        $createComment = json_decode($create, true);
        if ($createComment['status'] == 'success') {
            // Cập nhật trạng thái gốc của ticket
            $db->query('UPDATE ' . TB_TICKET_ROW . ' SET status= ' . TicketStatus::Done->value . ', status_client=' . TicketStatusClient::Done->value . ', last_comment_userid=' . $admin_info['userid'] . ', last_comment_time=' . NV_CURRENTTIME . ', activity_time=' . NV_CURRENTTIME . ' WHERE id=' . $row['id'])->fetch();

            $log_data = [
                Logkey::AdminReply->getLabel(),
                [
                    $nv_Lang->getModule('log_data'),
                    $comment['content'],
                ]
            ];
            add_ticket_logs($admin_info['userid'], LogKey::AdminReply->value, $log_data, $row['id'], 1);

            nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail&id=' . $row['id']);
        } else {
            $error = $createComment['message'];
        }
    }
}

/**
 * Xử lý cộng điểm cho khách hàng
 * * Khi báo lỗi thiếu gói thầu
 * * Khi có ý kiến đóng góp phát triển tính năng của phần mềm
 * ** cat_id = 10 => Loại yêu cầu Báo lỗi
 * ** cat_id = 11 => Loại yêu cầu đóng góp ý kiến
 */
if ($nv_Request->isset_request('plus_points_customer', 'post') and $nv_Request->isset_request('bonus_token', 'post', '') == NV_CHECK_SESSION) {
    $plus_point['cat_id'] = $nv_Request->get_int('cat_id', 'post', 0);
    $plus_point['bonus_point'] = $nv_Request->get_int('bonus_point', 'post', 0);
    $plus_point['action_type'] = $nv_Request->get_int('action_type', 'post', -1);
    $plus_point['expired'] = $nv_Request->get_int('expired', 'post', 0);

    if (!in_array($plus_point['action_type'], [PlusPointStatus::Deny->value, PlusPointStatus::Plus->value])) {
        nv_jsonOutput([
            'success' => false,
            'message' => $nv_Lang->getModule('error_incorrect_type_of_request'),
        ]);
    }

    if (!in_array($plus_point['cat_id'], [10, 11])) {
        nv_jsonOutput([
            'success' => false,
            'message' => $nv_Lang->getModule('error_incorrect_type_of_request'),
        ]);
    }

    if ($plus_point['action_type'] == PlusPointStatus::Deny->value) {
        $log_data = [
            LogKey::AdminHandleError->getLabel(),
            [
                $nv_Lang->getModule('status'),
                TicketStatus::RefuseToAddPoint->getLabel()
            ],
        ];
        add_ticket_logs($admin_info['userid'], LogKey::AdminHandleError->value, $log_data, $row['id'], 1);

        // Cập nhật status của ticket
        $db->query('UPDATE ' . TB_TICKET_ROW . ' SET status= ' . TicketStatus::RefuseToAddPoint->value . ' , last_comment_userid=' . $admin_info['userid'] . ', last_comment_time=' . NV_CURRENTTIME . ',
            activity_time=' . NV_CURRENTTIME . ' WHERE id=' . $row['id'])->fetch();
    } else {
        if ($plus_point['bonus_point'] < 0) {
            nv_jsonOutput([
                'success' => false,
                'message' => $nv_Lang->getModule('error_plus_point'),
            ]);
        }

        // Kiểm tra đã cộng điểm
        $check_bonus_point = $db->query('SELECT * FROM ' . TB_TICKET_ROW . ' WHERE status=' . TicketStatus::Done->value . ' AND bonus_point !=0 AND id=' . $row['id'])->fetch();
        if (!empty($check_bonus_point)) {
            nv_jsonOutput([
                'success' => false,
                'message' => $nv_Lang->getModule('error_point_have_been_added'),
            ]);
        }
        // Lấy thông tin điểm của khách hàng
        if (!class_exists('nukeviet_points')) {
            require_once NV_ROOTDIR . '/modules/points/points.class.php';
        }
        $nv_points = new nukeviet_points();
        $customs_points = $nv_points->my_point($row['customer_id']);

        // Cộng điểm cho khách
        $plus_point['expired'] = NV_CURRENTTIME + ($plus_point['expired'] * 86400);
        $plus_message = json_encode(get_both_lang($plus_point['cat_id'] == 10 ? 'log_plus_error_system' : 'log_plus_contributing_ideas'));
        $update_point = $nv_points->update(0, $plus_point['bonus_point'], $row['customer_id'], $plus_message, true, $admin_info['userid'], 1, $plus_point['expired']);
        if ($nv_points->isError()) {
            nv_jsonOutput([
                'success' => false,
                'message' => $update_point,
            ]);
        }
        // Ghi log cộng điểm
        add_point_logs($row['customer_id'], $row['id'], 0, $plus_point['bonus_point'], 1, $plus_message);
        // Ghi log thao tác
        $log_data = [
            LogKey::AdminHandleError->getLabel(),
            [
                $nv_Lang->getModule('status'),
                $nv_Lang->getModule('log_points_added'),
            ],
            [
                $nv_Lang->getModule('point_number'),
                $plus_point['bonus_point']
            ]
        ];
        add_ticket_logs($admin_info['userid'], LogKey::AdminHandleError->value, $log_data, $row['id'], 1);

        // Cập nhật status của ticket
        $db->query('UPDATE ' . TB_TICKET_ROW . ' SET status= ' . TicketStatus::Done->value . ' , last_comment_userid=' . $admin_info['userid'] . ', last_comment_time=' . NV_CURRENTTIME . ',
            activity_time=' . NV_CURRENTTIME . ', bonus_point=  ' . $plus_point['bonus_point'] . ' WHERE id=' . $row['id'])->fetch();

        // Gửi mail thông báo đã xử lý báo lỗi/góp ý cho khách hàng
        $row['bonus_point'] = $plus_point['bonus_point'];
    }
    notification_bonus_point_to_customer($row, $plus_point['action_type']);
    nv_jsonOutput([
        'success' => true,
        'message' => $nv_Lang->getModule('success_plus_point'),
    ]);
}

/**
 * Xử lý chat nội bộ
 * - Lấy thông tin chat
 * - Gửi tin nhắn chat
 * - Các file đính kèm khi trao đổi sẽ được lưu trong thư mục uploads/supportticket/chat/{ticket_id}
 * - Lấy thông tin admin
 */
if ($nv_Request->isset_request('get_admin_user', 'post')) {
    $optionData = [];
    $_sql_cat = 'SELECT userid FROM ' . TB_TICKET_CATADMIN . ' WHERE cat_id= ' . $row['cat_id'];
    $_result = $db->query($_sql_cat)->fetchAll(PDO::FETCH_COLUMN);
    $optionData = [];

    foreach ($_result as $admin_userid) {
        $cat_admin_info = get_user_info($admin_userid);
        if (!empty($cat_admin_info)) {
            $fullname = nv_show_name_user($cat_admin_info['first_name'], $cat_admin_info['last_name'], $cat_admin_info['username']);
            $optionData[] = [
                'username' => $cat_admin_info['username'],
                'fullname' => $fullname,
            ];
        }
    }

    nv_jsonOutput([
        'success' => true,
        'data' => $optionData
    ]);
}

if ($nv_Request->isset_request('get_chat_messages', 'post')) {
    $ticket_id = $nv_Request->get_int('ticket_id', 'post', 0);

    // Get chat messages
    $sql = "SELECT * FROM " . TB_TICKET_CHAT_PRIVATE . "
            WHERE ticket_id=" . $ticket_id . "
            ORDER BY created_time ASC";

    $messages = $db->query($sql)->fetchAll();
    $result = [];

    foreach ($messages as $msg) {
        $file_info = [];
        if (!empty($msg['file_path'])) {
            $file_path = NV_BASE_SITEURL . NV_UPLOADS_DIR . '/' . $module_upload . '/chat/' . $msg['file_path'];
            $file_ext = nv_getextension($msg['file_path']);
            $is_image = in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif', 'webp']);

            $file_info = [
                'path' => $file_path,
                'name' => basename($msg['file_path']),
                'is_image' => $is_image
            ];
        }

        $get_user_info = get_user_info($msg['admin_id']);
        $get_user_info['fullname'] = empty($get_user_info) ? '' : nv_show_name_user($get_user_info['first_name'], $get_user_info['last_name'], $get_user_info['username']);

        $result[] = [
            'id' => $msg['id'],
            'content' => $msg['content'],
            'admin_chat' => $msg['admin_id'] == $admin_info['userid'] ? 'my-message' : 'other-message',
            'file' => $file_info,
            'created_time' => nv_date('H:i d/m/Y', $msg['created_time']),
            'admin_name' => $get_user_info['fullname']
        ];
    }

    nv_jsonOutput([
        'success' => true,
        'messages' => $result
    ]);
}

if ($nv_Request->isset_request('send_chat', 'post')) {
    $chat = [];
    $chat['ticket_id'] = $nv_Request->get_int('ticket_id', 'post', 0);
    $chat['content'] = $nv_Request->get_textarea('content', '', NV_ALLOWED_HTML_TAGS);
    $chat['admin_id'] = $admin_info['userid'];
    $chat['created_time'] = NV_CURRENTTIME;
    $chat['file_path'] = '';

    if (empty($chat['ticket_id']) || empty($chat['content'])) {
        nv_jsonOutput([
            'success' => false,
            'message' => 'Không thấy dữ liệu'
        ]);
    }

    // Xử lý upload file
    $uploaded_file_path = null;
    if (isset($_FILES['file']) && !empty($_FILES['file']['name'])) {
        // Kiểm tra và tạo thư mục gốc
        $base = NV_UPLOADS_REAL_DIR . '/' . $module_name;
        if (!is_dir($base)) {
            nv_mkdir(NV_UPLOADS_REAL_DIR, $module_name);
            if (!is_writable($base)) {
                @chmod($base, 0777);
            }
        }

        // Tạo thư mục chat trong thư mục module
        $base_chat = $base . '/chat';
        if (!is_dir($base_chat)) {
            nv_mkdir($base, 'chat');
            if (!is_writable($base_chat)) {
                @chmod($base_chat, 0777);
            }
        }

        // Tạo thư mục theo ticket_id
        $fullPath = $base_chat . '/' . $chat['ticket_id'];
        if (!is_dir($fullPath)) {
            nv_mkdir($base_chat, $chat['ticket_id']);
            if (!is_writable($fullPath)) {
                @chmod($fullPath, 0777);
            }
        }

        // Định nghĩa các loại file được phép
        $allowed_extensions = ['images', 'documents', 'adobe'];
        $upload = new NukeViet\Files\Upload(
            $allowed_extensions,
            $global_config['forbid_extensions'],
            $global_config['forbid_mimes']
        );
        $upload->setLanguage($lang_global);
        $upload_info = $upload->save_file($_FILES['file'], $fullPath, false);

        if (empty($upload_info['error'])) {
            $chat['file_path'] = $chat['ticket_id'] . '/' . $upload_info['basename'];
            $uploaded_file_path = $fullPath . '/' . $upload_info['basename'];
        } else {
            nv_jsonOutput([
                'success' => false,
                'message' => $upload_info['error']
            ]);
        }
    }

    $stmt = $db->prepare('INSERT INTO ' . TB_TICKET_CHAT_PRIVATE . '
        (ticket_id, admin_id, content, file_path, created_time)
    VALUES
        (:ticket_id, :admin_id, :content, :file_path, :created_time)');

    $stmt->bindParam(':ticket_id', $chat['ticket_id'], PDO::PARAM_INT);
    $stmt->bindParam(':admin_id', $chat['admin_id'], PDO::PARAM_INT);
    $stmt->bindParam(':content', $chat['content'], PDO::PARAM_STR, strlen($chat['content']));
    $stmt->bindParam(':file_path', $chat['file_path'], PDO::PARAM_STR);
    $stmt->bindParam(':created_time', $chat['created_time'], PDO::PARAM_INT);

    $execute_result = $stmt->execute();

    if ($execute_result) {
        // Kiểm tra các tag @username trong nội dung
        preg_match_all('/@(\w+)/', $chat['content'], $matches);
        $tagged_usernames = $matches[1];

        // Gửi thông báo cho các user được tag
        if (!empty($tagged_usernames)) {
            foreach ($tagged_usernames as $username) {
                // Lấy user id từ username
                $sql = "SELECT userid FROM " . NV_USERS_GLOBALTABLE . " WHERE username=:username";
                $stmt = $db->prepare($sql);
                $stmt->bindParam(':username', $username, PDO::PARAM_STR);
                $stmt->execute();
                $tagged_user = $stmt->fetch();

                if ($tagged_user) {
                    $ticket_link = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=ticket_detail&amp;id=' . $chat['ticket_id'];

                    // Gửi thông báo
                    nv_insert_notification($module_name, 'tagged_in_notes', array(
                        'link' => $ticket_link,
                        'ticket_id' => $chat['ticket_id']
                    ), $chat['ticket_id'], $tagged_user['userid'], 0, 1, 0);
                }
            }
        }

        // Gửi thông báo tới người phụ trách ticket khi có trao đổi
        if (!empty($row['assignee_to'])) {
            $array_assignee = explode(',', $row['assignee_to']);
            foreach ($array_assignee as $key => $assignee_id) {
                $ticket_link = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=ticket_detail&amp;id=' . $chat['ticket_id'];

                // Gửi thông báo
                nv_insert_notification($module_name, 'assign_tagged_in_notes', array(
                    'link' => $ticket_link,
                    'ticket_id' => $chat['ticket_id']
                ), $chat['ticket_id'], $assignee_id, 0, 1, 0);
            }
        }

        nv_jsonOutput([
            'success' => true,
            'message' => 'Thành công'
        ]);
    } else {
        // Xóa file đã tải lên
        if ($uploaded_file_path !== null && file_exists($uploaded_file_path)) {
            @unlink($uploaded_file_path);
        }

        $error_info = $stmt->errorInfo();
        $error_message = 'Đã xảy ra lỗi khi lưu dữ liệu';
        if (!empty($error_info[2])) {
            $error_message .= ': ' . $error_info[2];
        }

        nv_jsonOutput([
            'success' => false,
            'message' => $error_message
        ]);
    }
}

$xtpl = new XTemplate('ticket_detail.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('UPLOADS_DIR_USER', NV_UPLOADS_DIR . '/' . $module_upload);
$xtpl->assign('TOKEN', NV_CHECK_SESSION);
$xtpl->assign('OP', $op);
$xtpl->assign('ATTACH_LIMITED', NV_ATTACH_LIMITED);
$xtpl->assign('ATTACH_LIMITED_MESSAGE', sprintf($nv_Lang->getModule('error_upload_limited'), NV_ATTACH_LIMITED));
$xtpl->assign('LIMITED_FILE_SIZE', sprintf($nv_Lang->getModule('limited_file_size'), NV_ATTACH_LIMITED, nv_convertfromBytes(NV_UPLOAD_MAX_FILESIZE), NV_MAX_WIDTH, NV_MAX_HEIGHT));
$xtpl->assign('FORM_ACTION', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $row['id']);
$xtpl->assign('EDIT_COMMENT', (isset($comment_check) && $comment_check['status'] == 'success' &&  $comment_check['canEdit']) ? $commentid : 0);

$row['add_time'] = $row['add_time'] > 0 ? nv_date('H:i d/m/Y', $row['add_time']) : '';
if ($row['edit_time'] > 0) {
    $row['edit_time'] = nv_date('H:i d/m/Y', $row['edit_time']);
    $xtpl->assign('EDIT_TIME', $row['edit_time']);
    $xtpl->parse('main.show_edit_time');
}
$row['pay_status'] = $row['payment_status'] == 2 ? $nv_Lang->getModule('payment_status2') : ($row['payment_status'] == 1 ? $nv_Lang->getModule('payment_status1') : $nv_Lang->getModule('payment_status3'));
$xtpl->assign('ROW', $row);

if (!defined('NV_EDITOR')) {
    define('NV_EDITOR', 'ckeditor5-classic');
}

if (defined('NV_EDITOR')) {
    require_once NV_ROOTDIR . '/' . NV_EDITORSDIR . '/' . NV_EDITOR . '/nv.php';
}
if (nv_function_exists('nv_aleditor')) {
    $comment_content = !empty($comment['content']) ? $comment['content'] : ((isset($comment_check) && $comment_check['status'] == 'success' &&  $comment_check['canEdit']) ? $comment_check['data']['content'] : '');
    $row['comment'] = nv_aleditor('commentContent', '100%', '250px', $comment_content);
    $xtpl->assign('COMMENT', $row['comment']);
}

$allow_editor = (defined('NV_EDITOR') and nv_function_exists('nv_aleditor')) ? true : false;

if (!defined('CKEDITOR') and $allow_editor) {
    define('CKEDITOR', true);
    $my_head .= '<script type="text/javascript" src="' . NV_STATIC_URL . NV_EDITORSDIR . '/' . NV_EDITOR . '/ckeditor.js?t=' . $global_config['timestamp'] . '"></script>';
}
$xtpl->assign('CONTENT', $allow_editor ? 'true' : 'false');

//Cho phép trả lời
if ($ticket['canReply']) {
    if (!empty($error)) {
        $xtpl->assign('ERROR', $error);
        $xtpl->parse('main.can_reply.error');
    }

    // Trả lời thay chuyên gia
    foreach ($array_admin_users as $admin_user) {
        $fullname = nv_show_name_user($admin_user['first_name'], $admin_user['last_name'], $admin_user['username']);
        $xtpl->assign('OPTION', [
            'key' => $admin_user['userid'],
            'title' => $admin_user['username'] . ' (' . $fullname . ')',
            'selected' => (!empty($comment['display_userid']) && $admin_user['userid'] == $comment['display_userid']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.can_reply.comment_user');
    }

    $total_file = !empty($comment['file_attach']) ? count($comment['file_attach']) + 1 : 0;
    $xtpl->assign('TOTAL_FILE', $total_file);
    if (!empty($comment['file_attach'])) {
        $file = [];
        foreach ($comment['file_attach'] as $key => $value) {
            $file['key'] = $key;
            $file['value'] = empty($value) ? '' : NV_BASE_SITEURL . NV_UPLOADS_DIR . '/' . $module_upload . '/' . $value;
            $xtpl->assign('FILE', $file);
            $xtpl->parse('main.can_reply.files');
        }
    }
    if ($commentid > 0) {
        $xtpl->parse('main.can_reply.cancel');
    }
    $xtpl->parse('main.can_reply');
    $xtpl->parse('main.can_reply_script');
} else {
    $my_head .= '<script type="text/javascript" src="' . NV_STATIC_URL . NV_EDITORSDIR . '/' . NV_EDITOR . '/ckeditor.js?t=' . $global_config['timestamp'] . '"></script>';
}

//Ticket info
$get_user_info = get_user_info($row['customer_id']);
$get_user_info['fullname'] = empty($get_user_info) ? '' : nv_show_name_user($get_user_info['first_name'], $get_user_info['last_name'], $get_user_info['username']);
$xtpl->assign('CUSTOMER', $get_user_info);

// Người tạo yêu cầu
$get_add_userid = get_user_info($row['add_userid']);
$get_add_userid['fullname'] = empty($get_add_userid) ? '' : nv_show_name_user($get_add_userid['first_name'], $get_add_userid['last_name'], $get_add_userid['username']);
$xtpl->assign('ADD_USER', $get_add_userid);

//Loại yc đã chọn
if (isset($array_ticket_cats[$row['cat_id']])) {
    $xtpl->assign('DETAIL_CAT', [
        'title' => $array_ticket_cats[$row['cat_id']]['title_' . NV_LANG_DATA],
    ]);
    $xtpl->parse('main.detail_cat');
}
//Thông tin gói vip
$user_vips = get_user_vips($row['customer_id']);
if ($row['vip_id'] > 0 && isset($user_vips[$row['vip_id']])) {
    $xtpl->assign('DETAIL_VIP', [
        'title' => $user_vips[$row['vip_id']]['title'],
        'link' => URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=cus_info&amp;id=' . $user_vips[$row['vip_id']]['user_id'] . '-' . $user_vips[$row['vip_id']]['id'],
    ]);
    $xtpl->parse('main.detail_vip');
}
//Thông tin đơn hàng
$user_orders = get_user_orders($row['customer_id']);
if ($row['order_id'] > 0 && isset($user_orders[$row['order_id']])) {
    $xtpl->assign('DETAIL_ORDER', [
        'title' => $user_orders[$row['order_id']]['title'],
        'link' => URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;vieworderid=' . $user_orders[$row['order_id']]['id'],
    ]);
    $xtpl->parse('main.detail_order');
}
//Ưu tiên
if (!empty($row['label_ids'])) {
    $labels = explode(',', $row['label_ids']);
    foreach ($labels as $label) {
        $xtpl->assign('DETAIL_LABEL', [
            'title' => NV_LANG_DATA == 'vi' ? $array_ticket_labels[$label]['title_vi'] : $array_ticket_labels[$label]['title_en'],
            'color' => $array_ticket_labels[$label]['color'],
        ]);
        $xtpl->parse('main.detail_label.loop');
    }
    $xtpl->parse('main.detail_label');
}
//Assignee
if (!empty($row['assignee_to'])) {
    $assignees = explode(',', $row['assignee_to']);
    $list_assignee = [];
    foreach ($assignees as $assignee) {
        $user = $array_admin_users[$assignee];
        $list_assignee[] = nv_show_name_user($user['first_name'], $user['last_name'], $user['username']);
    }
    $xtpl->assign('DETAIL_ASSIGNEE', implode(', ', $list_assignee));
    $xtpl->parse('main.detail_assignee');
}
//Status
if ($row['status'] > 0) {
    $detail_status = TicketStatus::tryFrom($row['status']);
    $xtpl->assign('DETAIL_STATUS', $detail_status?->getLabel());
    $xtpl->parse('main.detail_status');
}
//Hiển thị lead/cơ hội nóng hoặc mới nhất
$lead_info = get_lead_info($row['customer_id']);
if (!empty($lead_info)) {
    if ($lead_info['type'] == 'lead') {
        $xtpl->assign('DETAIL_LEAD', [
            'label' => $nv_Lang->getModule('newest_lead'),
            'name' => $lead_info['name'],
            'id' => $lead_info['id'],
            'type' => 1,
            'link' => NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=crmbidding&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $lead_info['id'],
        ]);
    } else {
        $xtpl->assign('DETAIL_LEAD', [
            'label' => $nv_Lang->getModule('newest_opportunity'),
            'name' => $lead_info['name'],
            'id' => $lead_info['id'],
            'type' => 2,
            'link' => NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=crmbidding&amp;' . NV_OP_VARIABLE . '=opportunities_info&amp;id=' . $lead_info['id'],
        ]);
    }
    $xtpl->parse('main.detail_lead');
    if ($ticket['canReply']) {
        $xtpl->parse('main.detail_lead_info');
        $xtpl->parse('main.detail_lead_script');
    }
}

//Update info
$root_ticket_status = $array_ticket_status; //Lưu lại danh sách status gốc
if ($ticket['canEdit']) {
    $xtpl->parse('main.can_edit_detail');

    if ($row['payment_status'] != 1) {
        foreach ($array_ticket_cats as $value) {
            $xtpl->assign('OPTION', [
                'key' => $value['cat_id'],
                'title' => $value['title_' . NV_LANG_DATA],
                'selected' => ($value['cat_id'] == $row['cat_id']) ? ' selected="selected"' : ''
            ]);
            $xtpl->parse('main.can_edit_info.select_cat_id.loop');
        }
        $xtpl->parse('main.can_edit_info.select_cat_id');
    } else {
        $xtpl->assign('CAT_NAME', $array_ticket_cats[$row['cat_id']]['title_' . NV_LANG_DATA]);
        $xtpl->parse('main.can_edit_info.selected_cat_id');
    }

    foreach ($user_vips as $value) {
        $xtpl->assign('OPTION', [
            'key' => $value['id'],
            'title' => $value['title'],
            'selected' => ($value['id'] == $row['vip_id']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.can_edit_info.select_vip');
    }

    foreach ($user_orders as $value) {
        $xtpl->assign('OPTION', [
            'key' => $value['id'],
            'title' => '#' . $value['id'] . ' ' . $value['title'],
            'selected' => ($value['id'] == $row['order_id']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.can_edit_info.select_order');
    }

    $row['label_ids'] = empty($row['label_ids']) ? [] : explode(',', $row['label_ids']);
    foreach ($array_ticket_labels as $value) {
        $xtpl->assign('OPTION', [
            'key' => $value['label_id'],
            'title' => NV_LANG_DATA == 'vi' ? $value['title_vi'] : $value['title_en'],
            'selected' => in_array($value['label_id'], $row['label_ids']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.can_edit_info.select_label_ids');
    }

    $row['assignee_to'] = empty($row['assignee_to']) ? [] : explode(',', $row['assignee_to']);
    foreach ($array_admin_users as $admin_user) {
        $fullname = nv_show_name_user($admin_user['first_name'], $admin_user['last_name'], $admin_user['username']);
        $xtpl->assign('OPTION', [
            'key' => $admin_user['userid'],
            'title' => $admin_user['username'] . ' (' . $fullname . ')',
            'selected' => in_array($admin_user['userid'], $row['assignee_to']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.can_edit_info.select_assignee');
    }

    // Trạng thái của ticket
    $array_ticket_status = [TicketStatus::Open->value, TicketStatus::Done->value, TicketStatus::Process->value, TicketStatus::Close->value];
    if (in_array($row['cat_id'], [10, 11])) {
        $array_ticket_status[] = TicketStatus::RefuseToAddPoint->value;
    }
    foreach ($array_ticket_status as $key) {
        $row['status'] = in_array($row['status'], $array_ticket_status) ? $row['status'] : TicketStatus::Process->value;
        $xtpl->assign('OPTION', [
            'key' => $key,
            'title' => TicketStatus::tryFrom($key)->getLabel(),
            'selected' => ($key == $row['status']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.can_edit_info.select_status');
    }
    $xtpl->parse('main.can_edit_info');
}

//Hiển thị ảnh đính kèm của ticket
$row['file_attach'] = decode_file($row['file_attach']);
if (sizeof($row['file_attach']) > 0) {
    foreach ($row['file_attach'] as $file) {
        $attached = [];
        $attached['src'] = get_image_src($file);
        $attached['name'] = $file;
        if (file_type(getextension($file)) == 'image') {
            $xtpl->assign('IMAGE', $attached);
            $xtpl->parse('main.detail_attach.loop_image');
        } else {
            $xtpl->assign('FILE', $attached);
            $xtpl->parse('main.detail_attach.loop_file');
        }
    }
    $xtpl->parse('main.detail_attach');
}

// Nếu là ticket báo lỗi thì sẽ hiển thị link báo lỗi
if ($row['cat_id'] == 10 and !empty($row['link_error'])) {
    $xtpl->assign('LINK_ERROR', nv_htmlspecialchars($row['link_error']));
    $xtpl->parse('main.link_error');
}

//Hiển thị danh sách comment
if ($comments['status'] == 'success' && $comments['code'] == '0000' && empty($commentid)) {
    $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $row['id'];
    $generate_page = nv_generate_page($base_url, $comments['total'], $comments['perpage'], $comments['page']);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.comments.generate_page');
    }
    $array_comment = array_reverse($comments['data']);
    foreach ($array_comment as $comment) {
        $reply_userid = $comment['display_userid'] > 0 ? $comment['display_userid'] : $comment['reply_userid'];
        $get_user_info = get_user_info($reply_userid);
        $comment['add_time'] = nv_date('d/m/Y H:i:s', $comment['add_time']);
        $comment['edited'] = $comment['edit_time'] > 0 ? $nv_Lang->getModule('comment_edited') : '';
        $comment['file_attach'] = decode_file($comment['file_attach']);

        if (sizeof($comment['file_attach']) > 0) {
            foreach ($comment['file_attach'] as $file) {
                $attached = [];
                $attached['src'] = get_image_src($file);
                $attached['name'] = $file;
                $attached['comment_id'] = $comment['log_id'];
                if (file_type(getextension($file)) == 'image') {
                    $xtpl->assign('IMAGE_CMT', $attached);
                    $xtpl->parse('main.comments.loop.detail_attach.loop_image');
                } else {
                    $xtpl->assign('FILE_CMT', $attached);
                    $xtpl->parse('main.comments.loop.detail_attach.loop_file');
                }
            }
            $xtpl->parse('main.comments.loop.detail_attach');
        }
        $comment['user_extend'] = get_user_extend($reply_userid);
        if (!empty($comment['user_extend']) && $comment['user_extend']['signature_state'] == 'on') {
            $xtpl->assign('SIGNATURE', $comment['user_extend']['signature_' . NV_LANG_DATA]);
            $xtpl->parse('main.comments.loop.signature');
        }
        $comment['display_position'] = display_position($comment);
        $comment['display_fullname'] = display_fullname($comment, $get_user_info);
        if ($comment['reply_userid'] != $comment['display_userid'] && $comment['display_userid'] > 0) {
            $xtpl->assign('REPLY_USER', get_user_info($comment['reply_userid'])['fullname']);
            $xtpl->parse('main.comments.loop.reply_user');
        }
        if ($comment['reply_userid'] == $admin_info['userid'] && $row['status'] != TicketStatus::Close->value) {
            $xtpl->assign('EDIT_COMMENT', $base_url . '&amp;commentid=' . $comment['log_id']);
            $xtpl->parse('main.comments.loop.edit_comment');
        }
        $xtpl->assign('CMT', $comment);
        $xtpl->parse('main.comments.loop');
    }

    $xtpl->parse('main.comments');
}

// Hiển thị phần xử lý cộng điểm
if (in_array($row['cat_id'], [10, 11]) && $row['status'] != TicketStatus::Close->value) {
    if ($row['bonus_point'] != 0 || $row['status'] == TicketStatus::RefuseToAddPoint->value) {
        $xtpl->assign('POINT_ADDED', $nv_Lang->getModule('point_added', $row['bonus_point']));
        $xtpl->parse('main.handle_bonus_point.points_added');
    } else {
        $row_cat = $array_active_cats[$row['cat_id']];
        $xtpl->assign('ROW_CAT', $row_cat);
        $xtpl->parse('main.handle_bonus_point.action_plus');
    }
    $xtpl->parse('main.handle_bonus_point');
}

//Hiển thị danh sách log
$where_api['AND'][] = [
    '=' => [
        'ticket_id' => $row['id']
    ]
];
$params = [
    'page' => 1,
    'perpage' => 20
];
$order['log_time'] = "DESC";
$params['where'] = $where_api;
$params['order'] = $order;
$data_log = nv_local_api('ListAllLog', $params, $admin_info['username'], $module_name);
$array_logs = json_decode($data_log, true);
if ($array_logs['status'] == 'success' and $array_logs['code'] == '0000') {
    foreach ($array_logs['data'] as $log) {
        $log['log_time'] = $log['log_time'] != 0 ? nv_date('H:i d/m/Y', $log['log_time']) : '';
        $get_user_info = get_user_info($log['userid']);
        $log['user'] = !empty($get_user_info) ? nv_show_name_user($get_user_info['first_name'], $get_user_info['last_name'], $get_user_info['username']) : ($log['userid'] == 0 ? $nv_Lang->getModule('system') : '');
        $xtpl->assign('LOG', $log);
        $log_data = json_decode($log['log_data'], true);
        if (!is_array($log_data)) {
            $log_data = [];
        }
        $log_data = array_values($log_data);
        $log_data_size = sizeof($log_data);
        if ($log_data_size > 0) {
            // Tiêu đề log
            $log_data_show = $log_data[0];
            unset($log_data[0]);
            $xtpl->assign('LOG_DATA_SHOW', $log_data_show);
            if (is_array($log_data_show)) {
                $xtpl->parse('main.alllog.data.sarray');
            } else {
                $xtpl->parse('main.alllog.data.sstring');
            }

            // Nội dung chi tiết khác
            if (!empty($log_data)) {
                foreach ($log_data as $dother) {
                    $xtpl->assign('LOG_DATA_OTHER', $dother);
                    if (isset($dother['type']) and $dother['type'] == 'link') {
                        // Dạng log có link
                        $xtpl->assign('LOG_DATA_OTHER', Log::getLogLink($dother));
                        $xtpl->parse('main.alllog.data.other.loop.sstring');
                    } elseif (isset($dother['type']) and $dother['type'] == 'directlink') {
                        // Dạng log link đến trang khác
                        $xtpl->assign('LOG_DATA_OTHER', Log::getLogDirectLink($dother));
                        $xtpl->parse('main.alllog.data.other.loop.sstring');
                    } elseif (is_array($dother)) {
                        $xtpl->parse('main.alllog.data.other.loop.sarray');
                    } else {
                        $xtpl->parse('main.alllog.data.other.loop.sstring');
                    }
                    $xtpl->parse('main.alllog.data.other.loop');
                }
                $xtpl->parse('main.alllog.data.other');
                $xtpl->parse('main.alllog.data.other1');
            }
            $xtpl->parse('main.alllog.data');
        }
        $xtpl->parse('main.alllog');
    }
}

//Hiển thị ticket liên quan
$params = [
    'page' => $page,
    'perpage' => $per_page
];

$where = [];
$where['AND'][] = [
    '=' => [
        'customer_id' => $row['customer_id']
    ]
];
$where['AND'][] = [
    '!=' => [
        'id' => $row['id']
    ]
];
$where['AND'][] = [
    '=' => [
        'is_paid' => 0 // Các ticket miễn phí
    ]
];

// Nếu có điều kiện where thì gán
if (!empty($where)) {
    $params['where'] = $where;
}

// GỌI API
$array_ticket_status = $root_ticket_status; //Trả lại danh sách status gốc
$list_related = nv_local_api('ListAllTicket', $params, $admin_info['username'], $module_name);
$ticket_related = json_decode($list_related, true);
if ($ticket_related['status'] == 'success' and $ticket_related['code'] == '0000') {
    $number = 1;
    foreach ($ticket_related['data'] as $related) {
        $related['number'] = $number;
        ++$number;
        $related['link_detail'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail&amp;id=' . $related['id'];
        $related['cat_title'] = empty($array_ticket_cats[$related['cat_id']]) ? '' : $array_ticket_cats[$related['cat_id']]['title_' . NV_LANG_DATA];
        $related['status'] = TicketStatus::tryFrom($related['status'])?->getLabel() ?? '';
        $related['add_time'] = nv_date('H:i:s d/m/Y', $related['add_time']);
        $xtpl->assign('RELATED', $related);
        $xtpl->parse('main.related.loop');
    }

    $xtpl->parse('main.related');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
