<!-- BEGIN: main -->
<div id="users">
    <!-- BEGIN: is_forum -->
    <div class="alert alert-warning">{LANG.modforum}</div>
    <!-- END: is_forum -->
    <div class="well">
        <form action="{FORM_ACTION}" method="get">
            <input name="{NV_NAME_VARIABLE}" type="hidden" value="{MODULE_NAME}" />
            <div class="row">
                <div class="col-xs-12 col-md-5">
                    <div class="form-group">
                        <input class="form-control" type="text" name="value" value="{SEARCH_VALUE}" id="f_value" placeholder="{LANG.search_key}" />
                    </div>
                </div>
                <div class="col-xs-12 col-md-5">
                    <div class="form-group">
                        <select class="form-control" name="method" id="f_method">
                            <option value="">---{LANG.search_method}---</option>
                            <!-- BEGIN: method -->
                            <option value="{METHODS.key}"{METHODS.selected}>{METHODS.value}</option>
                            <!-- END: method -->
                        </select>
                    </div>
                </div>
                <div class="col-xs-12 col-md-5">
                    <div class="form-group">
                        <select class="form-control" name="type" id="f_type">
                            <option value="">---{LANG.search_type}---</option>
                            <!-- BEGIN: type -->
                            <option value="{TYPE}"{TYPE_SELECTED}>{TYPE}</option>
                            <!-- END: type -->
                        </select>
                    </div>
                </div>
                
                <div class="col-xs-12 col-md-4">
                    <div class="form-group">
                        <input class="btn btn-primary" name="search" type="submit" value="{LANG.submit}" />
                    </div>
                </div>
            </div>
            <div class="form-inline">
                <div class="row">
                    <label class="control-label col-md-3" style="line-height: 38px;">{LANG.range_time_active}:</label>
                    <div class="form-group col-md-4">
                        <label class="control-label col-md-3" style="line-height: 38px;" for="from_time">{LANG.from}: </label>
                        <div class="input-group col-md-19">
                            <input class="form-control" type="text" id="active_time_from" name="active_time_from" value="{active_time_from}" pattern="^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{1,4}$" placeholder="{LANG.etime_type}" autocomplete="off"/>
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="button" id="from_time_btn">
                                    <em class="fa fa-calendar fa-fix">&nbsp;</em>
                                </button>
                            </span>
                        </div>
                    </div>
                    <div class="form-group col-md-4">
                        <label class="control-label col-md-4" style="line-height: 38px;" for="to_time">{LANG.to}:</label>
                        <div class="input-group col-md-19">
                            <input class="form-control" type="text" id="active_time_to" name="active_time_to" value="{active_time_to}" pattern="^[0-9]{1,2}\/[0-9]{1,2}\/[0-9]{1,4}$" placeholder="{LANG.etime_type}" autocomplete="off"/>
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="button" id="to_time_btn">
                                    <em class="fa fa-calendar fa-fix">&nbsp;</em>
                                </button>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <label><em>{LANG.search_note}</em></label>
        </form>
    </div>
    <form>
        <input type="hidden" class="testx" name="checkss" value="{CHECKSESS}" />
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover">
                <caption><em class="fa fa-file-text-o">&nbsp;</em>{TABLE_CAPTION}</caption>
                <thead>
                    <tr>
                        <th class="w50">{HEAD.userid.title}</th>
                        <th>{HEAD.username.title} / {HEAD.full_name.title}</th>
                        <th>{HEAD.email.title}</th>
                        <th>{HEAD.type.title}</th>
                        <th>{HEAD.adddate.title}</th>
                        <th>{HEAD.reason.title}</th>
                        <th class="text-center w100">{LANG.funcs}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: xusers -->
                    <tr>
                        <td class="align-middle"> {CONTENT_TD.userid} </td>
                        <td>
                            <a href="{CONTENT_TD.link}">{CONTENT_TD.username}</a>
                            <div class="mt-1">{CONTENT_TD.fullname}</div>
                        </td>
                        <td>
                            <a href="mailto:{CONTENT_TD.email}">{CONTENT_TD.email}</a>
                        </td>
                        <td>
                            <span class="text-info">{CONTENT_TD.type}</span>
                        </td>
                        <td>
                            <span class="text-info">{CONTENT_TD.time_add}</span>
                        </td>
                        <td>
                            <span class="text-info">{CONTENT_TD.reason}</span>
                        </td>
                        <td class="text-center align-middle">
                            <a data-toggle="tooltip" title="{LANG.delete}" href="javascript:void(0);" class="btn btn-xs btn-danger" onclick="nv_row_del({CONTENT_TD.id});"><em class="fa fa-trash-o"></em></a>
                        </td>
                    </tr>
                    <!-- END: xusers -->
                </tbody>
                <!-- BEGIN: footer -->
                <tfoot>
                    <tr>
                        <td colspan="7">
                            <!-- BEGIN: generate_page -->
                            {GENERATE_PAGE}
                            <!-- END: generate_page -->
                        </td>
                    </tr>
                </tfoot>
                <!-- END: footer -->
            </table>
        </div>
    </form>
</div>
<div id="pass-reset-modal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">{LANG.pass_reset_request}</h4>
            </div>
            <div class="modal-body">
                <input type="hidden" class="userid" value="0"/>
                <p>{LANG.username}: <span class="username"></span></p>
                <p>{LANG.currentpass_created_time}: <span class="currentpass-created-time"></span></p>
                <p>{LANG.currentpass_request_status}: <span class="currentpass-request-status"></span></p>
                <p><a class="btn btn-primary btn-xs" href="javascript:void(0);" onclick="passResetRequestSubmit(event, this, 1);">{LANG.pass_reset_request1_send}</a><span class="fa fa-spinner fa-spin m-left" style="display:none"></span></p>
                <p><a class="btn btn-primary btn-xs" href="javascript:void(0);" onclick="passResetRequestSubmit(event, this, 2);">{LANG.pass_reset_request2_send}</a><span class="fa fa-spinner fa-spin m-left" style="display:none"></span></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div><!-- /.modal -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript">
var export_note = '{LANG.export_note}';
var export_complete = '{LANG.export_complete}';
</script>
<script type="text/javascript">
	$(document).ready(function() {
		var dateFormat = "dd/mm/yy"
        $("#active_time_from,#active_time_to").datepicker({
            dateFormat : dateFormat,
            changeMonth : true,
            changeYear : true,
            showOtherMonths : true,
            yearRange: "-999:+1",
            showOn : 'focus'
        });
        $('#from_time_btn').click(function() {
            $("#active_time_from").datepicker('show');
        });

        $('#to_time_btn').click(function() {
            $("#active_time_to").datepicker('show');
        });

        $("#active_time_from").on('change', function() {
            if ($("#active_time_from").val() != '') {
                $("#active_time_to").attr('required', 'required');
            } else {
                $("#active_time_to").removeAttr('required');
                if ($("#active_time_to").val() != '') {
                    $("#active_time_from").attr('required', 'required');
                }
            }
            $("#active_time_to").datepicker( "option", "minDate", getDate( this ) );
        });
        $("#active_time_to").on('change', function() {
            if ($("#active_time_to").val() != '') {
                $("#active_time_from").attr('required', 'required');
            } else {
                $("#active_time_from").removeAttr('required');
                if ($("#active_time_from").val() != '') {
                    $("#active_time_to").attr('required', 'required');
                }
            }
            $("#active_time_from").datepicker( "option", "maxDate", getDate( this ) );
        });

        function getDate( element ) {
            var date;
            try {
            date = $.datepicker.parseDate( dateFormat, element.value );
            } catch( error ) {
            date = null;
            }
            return date;
        }
	});
</script>
<!-- END: main -->
