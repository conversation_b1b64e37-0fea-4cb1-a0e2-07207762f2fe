/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

function nv_chang_question(qid) {
    var nv_timer = nv_settimeout_disable('id_weight_' + qid, 5000);
    var new_vid = $('#id_weight_' + qid).val();
    $.post(script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=reason&nocache=' + new Date().getTime(), 'changeweight=1&qid=' + qid + '&new_vid=' + new_vid, function(res) {
        if (res != 'OK') {
            alert(nv_is_change_act_confirm[2]);
        }
        clearTimeout(nv_timer);
        nv_show_list_question();
    });
    return;
}

function nv_save_title(qid) {
    var new_title = document.getElementById('title_' + qid);
    var hidden_title = document.getElementById('hidden_' + qid);

    if (new_title.value == hidden_title.value) {
        return;
    }

    if (new_title.value == '') {
        alert(nv_content);
        new_title.focus();
        return false;
    }

    var nv_timer = nv_settimeout_disable('title_' + qid, 5000);
    $.post(script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=reason&nocache=' + new Date().getTime(), 'edit=1&qid=' + qid + '&title=' + new_title.value, function(res) {
        if (res != 'OK') {
            alert(nv_is_change_act_confirm[2]);
        }
        clearTimeout(nv_timer);
        nv_show_list_question();
    });
    return;
}

function nv_show_list_question() {
    if (document.getElementById('module_show_list')) {
        $.post(script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=reason&nocache=' + new Date().getTime(), 'qlist=1', function(res) {
            $("#module_show_list").html(res);
        });
    }
    return;
}

function nv_del_question(qid) {
    if (confirm(nv_is_del_confirm[0])) {
        $.post(script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=reason&nocache=' + new Date().getTime(), 'del=1&qid=' + qid, function(res) {
            if (res == 'OK') {
                nv_show_list_question();
            } else {
                alert(nv_is_del_confirm[2]);
            }
        });
    }
    return false;
}

function nv_add_question() {
    var new_title = document.getElementById('new_title');

    if (new_title.value == '') {
        alert(nv_content);
        new_title.focus();
        return false;
    }

    var nv_timer = nv_settimeout_disable('new_title', 5000);

    $.post(script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=reason&nocache=' + new Date().getTime(), 'add=1&title=' + new_title.value, function(res) {
        if (res == 'OK') {
            nv_show_list_question();
        } else {
            alert(nv_content);
        }
    });
    return;
}

function nv_check_form(OForm) {
    var f_method = $("#f_method").val();
    var f_value = $("#f_value").val();
    if (f_method != '' && f_value != '') {
        OForm.submit();
    }
    return false;
}

function nv_row_del(vid) {
    if (confirm(nv_is_del_confirm[0])) {
        var checkss = $("input[name='checkss']").val();
        $.post(script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=main&nocache=' + new Date().getTime(), '&del=1&id=' + vid + '&checkss=' + checkss, function(res) {
            if (res == 'OK') {
                window.location.href = window.location.href;
            } else {
                var r_split = res.split("_");
                if (r_split[0] == 'ERROR') {
                    alert(r_split[1]);
                } else {
                    alert(nv_is_del_confirm[2]);
                }
            }

        });
    }
    return false;
}