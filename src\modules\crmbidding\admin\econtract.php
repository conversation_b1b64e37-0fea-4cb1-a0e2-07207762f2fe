<?php

/**
 * T<PERSON><PERSON> năng quản lý hợp đồng điện tử
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2024 Hu<PERSON><PERSON> Đ<PERSON>. All rights reserved
 * @createdAt Mon, 15 Apr 2024 11:55:00
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

$api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);

// Lấy phân loại nhóm của user
$my_group_type = 'sale';
$sql = 'SELECT t1.* FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups as t1 LEFT JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users as t2 ON t1.group_id=t2.group_id WHERE t2.userid = ' . $admin_info['userid'];
$_my_groups = $db->query($sql)->fetchAll();
if ($_my_groups) {
    foreach ($_my_groups as $_my_group) {
        $_my_group['config'] = unserialize($_my_group['config']);
        if (
            $my_group_type == 'sale' &&
            isset($_my_group['config']['type']) &&
            !empty($_my_group['config']['type']) &&
            $_my_group['config']['type'] != 'sale'
        ) {
            $my_group_type = $_my_group['config']['type'];
        }
    }
}

$admin_config = [];
// Lấy danh sách user mà mình đang quản lý (chung team)
$filter_uploader_ids = [];
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE userid = ' . $admin_info['userid'];
$result = $db->query($sql);
while ($row_groups_users = $result->fetch()) {
    $_admin_config = json_decode($row_groups_users['config'], true) ?? [];
    $admin_config = array_merge($admin_config, $_admin_config);
    if ($row_groups_users['is_leader'] == 1) {
        $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $row_groups_users['group_id'] . ' AND userid != ' . $admin_info['userid'];
        $_result = $db->query($_sql);
        while ($_row_groups_users = $_result->fetch()) {
            $filter_uploader_ids[$_row_groups_users['userid']] = $_row_groups_users['userid'];
        }
    }
}
$filter_uploader_ids[$admin_info['userid']] = $admin_info['userid'];
$is_manage_econtract = isset($admin_config['manage_econtract']) && ($admin_config['manage_econtract'] == 1); // Phân quyền: Quản lý TẤT CẢ hợp đồng

// TODO: Bắt đầu tải form thông tin hợp đồng lên
if ($nv_Request->isset_request('submit', 'post,get')) {
    $error = '';
    $data_upload = [];
    $data_upload['contract_no'] = $nv_Request->get_title('contract_no', 'post', '');
    $data_upload['tax_code'] = $nv_Request->get_title('tax_code', 'post', '');
    $data_upload['c_name'] = $nv_Request->get_title('c_name', 'post', '');
    $data_upload['c_address'] = $nv_Request->get_title('c_address', 'post', '');
    $data_upload['order_ids'] = $nv_Request->get_typed_array('order_ids', 'post', 'int', []);

    // ? Validate dữ liệu
    if (empty($data_upload['tax_code'])) {
        $error = $nv_Lang->getModule('error_tax_code');
    } elseif (empty($data_upload['c_name'])) {
        $error = $nv_Lang->getModule('error_c_name');
    } elseif (empty($data_upload['c_address'])) {
        $error = $nv_Lang->getModule('error_c_address');
    } elseif (!count($data_upload['order_ids'])) {
        $error = $nv_Lang->getModule('error_order_ids');
    } elseif (empty($data_upload['contract_no'])) {
        $error = $nv_Lang->getModule('error_contract_no');
    } elseif ($db->query('SELECT COUNT(*) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE contract_no=' . $db->quote($data_upload['contract_no']))->fetchColumn()) {
        $error = $nv_Lang->getModule('error_existed_contract_no');
    } elseif (empty($_FILES['contract_path']['tmp_name'])) {
        $error = $nv_Lang->getModule('error_choose_file_contract');
    }

    if (empty($error)) {
        // Kiểm tra đơn hàng đã được thêm vào hợp đồng nào chưa
        $existed_orders = $db->query('SELECT t1.order_id, t2.contract_no FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders as t1 INNER JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts as t2 ON t1.econtract_id=t2.id WHERE t1.order_id IN (' . implode(',', $data_upload['order_ids']) . ')')->fetchAll();
        if ($existed_orders) {
            // Báo lỗi
            $error = '';
            foreach ($existed_orders as $existed_order) {
                $error .= sprintf($nv_Lang->getModule('existed_orders_by_contract'), $existed_order['order_id'], $existed_order['contract_no']) . "\n";
            }
        }
    }

    if (empty($error)) {
        // Lấy danh sách gói VIP
        $contract_vip_ids = [
            'vi' => [],
            'en' => [],
        ];
        $order_vip_ids = [];
        $order_user_ids = [];
        $order_prefix_langs = [];
        $where = [];
        $where['AND'] = [
            ['=' => ['tax' => $data_upload['tax_code']]],
            ['IN' => ['order_id' => '(' . implode(',', $data_upload['order_ids']) . ')']]
        ];

        $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
        $api_dtinfo->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingCustomsLog')
            ->setData([
                'array_select' => ['vip', 'order_id', 'user_id', 'prefix_lang'],
                'where' => $where
            ]);
        $result = $api_dtinfo->execute();
        $error = $api_dtinfo->getError();
        if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
            $bidding_customs_logs = $result['data'];
            if (is_array($bidding_customs_logs) && $result['total']) {
                foreach ($bidding_customs_logs as $bidding_customs_log) {
                    if ($bidding_customs_log['prefix_lang'] == 0) {
                        $contract_vip_ids['vi'][] = $bidding_customs_log['vip'];
                        $order_vip_ids[$bidding_customs_log['order_id']]['vi'][] = $bidding_customs_log['vip'];
                    } elseif ($bidding_customs_log['prefix_lang'] == 1) {
                        $contract_vip_ids['en'][] = $bidding_customs_log['vip'];
                        $order_vip_ids[$bidding_customs_log['order_id']]['en'][] = $bidding_customs_log['vip'];
                    }
                    $order_user_ids[$bidding_customs_log['order_id']] = intval($bidding_customs_log['user_id']);
                }
            }
        }

        if (empty($error)) {
            // Khởi tạo thư mục upload
            if (!is_dir(NV_UPLOADS_REAL_DIR . '/econtracts')) {
                nv_mkdir(NV_UPLOADS_REAL_DIR, 'econtracts');
            }
            if (!is_dir(NV_UPLOADS_REAL_DIR . '/econtracts/' . strtolower(change_alias($admin_info['username'])))) {
                nv_mkdir(NV_UPLOADS_REAL_DIR . '/econtracts', strtolower(change_alias($admin_info['username'])));
            }
            $path_to_upload_contract = NV_UPLOADS_REAL_DIR . '/econtracts/' . strtolower(change_alias($admin_info['username']));

            // Lưu tệp
            $upload = new NukeViet\Files\Upload();
            $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
            // Lưu hợp đồng
            $upload_contract_info = $upload->save_file($_FILES['contract_path'], $path_to_upload_contract, false, $global_config['nv_auto_resize']);
            if (!empty($upload_contract_info['error'])) {
                $error = $upload_contract_info['error'];
            } elseif (!in_array($upload_contract_info['ext'], ['pdf', 'doc', 'docx', 'zip'])) {
                $error = $nv_Lang->getModule('error_file_type');
            }

            if (empty($error)) {
                $data_upload['contract_path'] = NV_UPLOADS_DIR . '/econtracts/' . strtolower(change_alias($admin_info['username'])) . '/' . str_replace(['/', '\\'], '-', $data_upload['contract_no']) . '-' . $upload_contract_info['basename'];
                rename($upload_contract_info['name'], $path_to_upload_contract . '/' . str_replace(['/', '\\'], '-', $data_upload['contract_no']) . '-' . $upload_contract_info['basename']);

                // TODO: LƯU VÀO CSDL
                // ? Lưu bảng _econtracts
                $sql = 'INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts (
                    tax_code, c_name, c_address, contract_no, contract_path, uploader_id, vips_vi, vips_en, created_at, updated_at, customer_type, status, stage, stage_next
                    ) VALUES (
                        :tax_code, :c_name, :c_address, :contract_no, :contract_path,
                    ' . $admin_info['userid'] . ',
                    ' . $db->quote(implode(',', array_unique($contract_vip_ids['vi']))) . ',
                    ' . $db->quote(implode(',', array_unique($contract_vip_ids['en']))) . ',
                    ' . NV_CURRENTTIME . ',
                    ' . NV_CURRENTTIME . ', 1, 5, 5, 5)';

                $data_insert = [];
                $data_insert['contract_no'] = trim($data_upload['contract_no']);
                $data_insert['tax_code'] = trim($data_upload['tax_code']);
                $data_insert['c_name'] = trim($data_upload['c_name']);
                $data_insert['c_address'] = trim($data_upload['c_address']);
                $data_insert['contract_path'] = trim($data_upload['contract_path']);

                $econtract_id = $db->insert_id($sql, 'id', $data_insert);
                // ? Lưu bảng _econtract_orders
                if ($econtract_id) {
                    foreach ($data_upload['order_ids'] as $order_id) {
                        $_o_user_id = 0;
                        $_o_user_name = 'N/A';
                        if (isset($order_user_ids[$order_id]) && $order_user_ids[$order_id] > 0) {
                            $o_user = $db->query('SELECT username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $order_user_ids[$order_id])->fetch();
                            $_o_user_id = $order_user_ids[$order_id];
                            $_o_user_name = $o_user['username'] ?? '';
                        }
                        $sql = 'INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders (
                            econtract_id, order_id, site_id, user_id, username, vips_vi, vips_en
                        ) VALUES (
                            ' . $econtract_id . ',
                            ' . $order_id . ',
                            1,
                            ' . $_o_user_id . ',
                            ' . $db->quote($_o_user_name ?? '') . ',
                            ' . $db->quote(implode(',', $order_vip_ids[$order_id]['vi'] ?? [])) . ',
                            ' . $db->quote(implode(',', $order_vip_ids[$order_id]['en'] ?? [])) . '
                        )';

                        $db->insert_id($sql, 'id');

                        // Lưu log đơn hàng
                        $logApi = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                        $logApi = $logApi->setModule('bidding')
                            ->setLang('vi')
                            ->setAction('CreateBiddingAllLogs')
                            ->setData([
                                'userid' => $admin_info['admin_id'],
                                'log_area' => 1,
                                'log_key' => 'LOG_CHANGE_ORDER_CONTRACT',
                                'log_time' => NV_CURRENTTIME,
                                'log_data' => [
                                    [$nv_Lang->getModule('log_update_econtract_success'), $nv_Lang->getModule('new')],
                                    [$nv_Lang->getModule('contract_no') . ': ', $data_insert['contract_no']]
                                ],
                                'order_id' => $order_id
                            ])->execute();
                    }

                    nv_jsonOutput([
                        'status' => 'success',
                        'message' => $nv_Lang->getModule('econtract_save_success')
                    ]);
                } else {
                    $error = $nv_Lang->getModule('error_unknown');
                }
            }
        }
    }

    nv_jsonOutput([
        'status' => 'error',
        'message' => $error
    ]);
}

// TODO: Xóa Hợp đồng
if ($nv_Request->isset_request('delete_id', 'get') and $nv_Request->isset_request('delete_checkss', 'get')) {
    $id = $nv_Request->get_int('delete_id', 'get');
    $delete_checkss = $nv_Request->get_string('delete_checkss', 'get');
    if ($id > 0 and $delete_checkss == md5($id . NV_CACHE_PREFIX . $client_info['session_id'])) {
        $econtract_delete = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $id)->fetch();
        if ($econtract_delete) {
            $_order_ids = $db->query('SELECT order_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id = ' . $econtract_delete['id'])->fetchAll();
            // Xóa danh sách đơn hàng thuộc econtract này
            $db->query('DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id = ' . $econtract_delete['id']);
            // Xóa hợp đồng đính kèm
            nv_deletefile(NV_ROOTDIR . '/' . $econtract_delete['contract_path']);
            // Xóa row
            $db->query('DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id = ' . $econtract_delete['id']);
            // Lưu log đơn hàng
            foreach ($_order_ids as $_order) {
                $logApi = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                $logApi = $logApi->setModule('bidding')
                    ->setLang('vi')
                    ->setAction('CreateBiddingAllLogs')
                    ->setData([
                        'userid' => $admin_info['admin_id'],
                        'log_area' => 1,
                        'log_key' => 'LOG_CHANGE_ORDER_CONTRACT',
                        'log_time' => NV_CURRENTTIME,
                        'log_data' => [
                            [$nv_Lang->getModule('log_delete_econtract_success'), $econtract_delete['contract_no']]
                        ],
                        'order_id' => $_order['order_id']
                    ])->execute();
            }
        }
    }
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
}

// TODO: Call API: Lấy thông tin từ MST
if ($nv_Request->get_int('getcominfo', 'post,get', 0)) {
    $customer_info = [
        'c_name' => '',
        'c_address' => '',
        'orders' => []
    ];
    $tax_code = $nv_Request->get_title('tax_code', 'post,get');

    $where = [];
    $error = '';
    $is_found_com = false;
    if (!empty($tax_code)) {
        /**
         * TODO: LẤY THÔNG TIN CÔNG TY / KHÁCH HÀNG
         * 1. Lấy từ CSDL nhà thầu (businesslistings trên dauthau.asia)
         * 2. (nếu không có, hoặc chưa đủ thông tin) => Lấy từ API doanh nghiệp (dauthau.net)
         * 3. Lấy thông tin từ cơ hội kinh doanh
         */
        // ? 1. Lấy từ CSDL nhà thầu (businesslistings trên dauthau.asia)
        $where = [];
        $error_dtasia = '';
        $where['AND'] = [
            ['=' => ['code' => $tax_code]]
        ];
        $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
        $api_dtinfo->setModule('businesslistings')
            ->setLang('vi')
            ->setAction('ListAllBusinessListings')
            ->setData([
                'where' => $where
            ]);
        $result_dtasia = $api_dtinfo->execute();
        $error_dtasia = $api_dtinfo->getError();
        if (empty($error_dtasia) && $result_dtasia['status'] == 'success' && !empty($result_dtasia['data'])) {
            $is_found_com = true;
            $data_dtinfo = array_values($result_dtasia['data']);
            $customer_info['c_name'] = (isset($data_dtinfo[0]['companyname']) && !empty($data_dtinfo[0]['companyname']) && empty($customer_info['c_name'])) ? $data_dtinfo[0]['companyname'] : $customer_info['c_name'];
            $customer_info['c_address'] = (isset($data_dtinfo[0]['issue_invoice_addr']) && !empty($data_dtinfo[0]['issue_invoice_addr']) && empty($customer_info['c_address'])) ? $data_dtinfo[0]['issue_invoice_addr'] : $customer_info['c_address'];
        } elseif ($result_dtasia['status'] == 'error') {
            $error_dtasia = $result_dtasia['message'];
        }

        // ? 2. (nếu không có, hoặc chưa đủ thông tin) => Lấy từ API doanh nghiệp (dauthau.net)
        if (empty($customer_info['c_name']) || empty($customer_info['c_address'])) {
            $error_dtnet = '';
            $api_dtnet = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
            $api_dtnet->setModule('')
                ->setLang('vi')
                ->setAction('GetBidsProfile')
                ->setData([
                    'prof_code' => $tax_code
                ]);
            $result_dtnet = $api_dtnet->execute();
            $error_dtnet = $api_dtnet->getError();
            if (empty($error_dtnet) && $result_dtnet['status'] == 'success' && !empty($result_dtnet['profile_info'])) {
                $is_found_com = true;
                $customer_info['c_name'] = (isset($result_dtnet['profile_info']['profile']['prof_name']) && !empty($result_dtnet['profile_info']['profile']['prof_name']) && empty($customer_info['c_name'])) ? $result_dtnet['profile_info']['profile']['prof_name'] : $customer_info['c_name'];
                $customer_info['c_address'] = (isset($result_dtnet['profile_info']['profile']['represent_address']) && !empty($result_dtnet['profile_info']['profile']['represent_address']) && empty($customer_info['c_address'])) ? $result_dtnet['profile_info']['profile']['represent_address'] : $customer_info['c_address'];
            } elseif ($result_dtnet['status'] == 'error' && $result_dtnet['code'] != '0000') {
                $error_dtnet = $result_dtnet['message'];
            }
        }

        // ? 3. Lấy thông tin từ cơ hội kinh doanh
        if (empty($customer_info['c_name']) || empty($customer_info['c_address'])) {
            // Code here
            $opportunities_info = $db->query('SELECT company_name, address_company FROM ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities WHERE tax=' . $db->quote($tax_code))->fetch();
            if ($opportunities_info) {
                $is_found_com = true;
                $customer_info['c_name'] = (!empty($opportunities_info['company_name']) && empty($customer_info['c_name'])) ? $opportunities_info['company_name'] : $customer_info['c_name'];
                $customer_info['c_address'] = (!empty($opportunities_info['address_company']) && empty($customer_info['c_address'])) ? $opportunities_info['address_company'] : $customer_info['c_address'];
            }
        }

        /**
         * TODO: Lấy thông tin đơn hàng
         * 1. Viết API mới, lấy danh sách đơn hàng theo MST => (api.dauthau.asia)
         * 2. Gọi API từ đây để lấy
         */
        $where = [];
        $where['AND'] = [
            ['=' => ['tax' => $tax_code]]
        ];
        $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
        $api_dtinfo->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingOrdersByLog')
            ->setData([
                'where' => $where
            ]);
        $result_order = $api_dtinfo->execute();
        $error_order = $api_dtinfo->getError();
        if (empty($error_order) && $result_order['status'] == 'success' && !empty($result_order['data'])) {
            // nv_jsonOutput($result_order['data']);
            foreach ($result_order['data'] as $_order) {
                if (($_order['caregiver_id'] == 0 && (defined('NV_IS_SPADMIN') || $is_manage_econtract)) || isset($filter_uploader_ids[strval($_order['caregiver_id'])]) || defined('NV_IS_SPADMIN') || $is_manage_econtract) {
                    // ? Lấy thông tin tên KH, địa chỉ từ đơn hàng (nếu vẫn chưa đủ thông tin)
                    $customer_info['c_name'] = (isset($_order['c_name']) && !empty($_order['c_name']) && empty($customer_info['c_name'])) ? $_order['c_name'] : $customer_info['c_name'];
                    $customer_info['c_address'] = (isset($_order['c_address']) && !empty($_order['c_address']) && empty($customer_info['c_address'])) ? $_order['c_address'] : $customer_info['c_address'];
                    $is_found_com = true;

                    $customer_order = [];
                    $customer_order['id'] = $_order['id'];
                    $customer_order['money'] = number_format($_order['money']) . ' đ';
                    $customer_order['discount'] = number_format($_order['discount']) . ' đ';
                    $customer_order['total'] = number_format($_order['total']) . ' đ';
                    $customer_order['price_reduce'] = number_format($_order['price_reduce']) . ' đ';
                    $customer_order['total_end'] = number_format($_order['total_end']) . ' đ';
                    $customer_order['vip_titles'] = '';
                    $_vip_titles = [];
                    foreach ($_order['vip'] as $_vip) {
                        $_vip_titles[] = $global_arr_vip[$_vip];
                    }
                    $customer_order['vip_titles'] = implode(', ', $_vip_titles);
                    // Lấy username của đơn hàng
                    $customer_order['username'] = $db->query('SELECT username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $_order['userid'])->fetchColumn() ?: '';
                    $customer_order['edit_time'] = !empty($_order['edit_time']) ? nv_date('H:i:s, d/m/Y', $_order['edit_time']) : '-';
                    $customer_order['order_code'] = '<a href="' . URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $_order['id'] . '" target="_blank"><strong class="label label-success">' . sprintf('BDH%010s', $_order['id']) . '</strong><br><small>(' . $customer_order['username'] . ')</small></a>';
                    // Check đơn hàng có hợp đồng chưa?
                    $_existed_econtract = $db->query('SELECT t2.contract_no FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders t1 RIGHT JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts t2 ON t1.econtract_id=t2.id WHERE order_id=' . $_order['id'])->fetchColumn();
                    $customer_order['econtract_no'] = $_existed_econtract ?: '';
                    $customer_info['orders'][] = $customer_order;
                }
            }
        } elseif ($result_order['status'] == 'error') {
            $error_order = $result_order['message'];
        }

        if (empty($error)) {
            $error = !empty($error_dtasia) ? $error_dtasia : (!empty($error_dtnet) ? $error_dtnet : (!empty($error_order) ? $error_order : ''));
        }

        if (!$is_found_com && !count($customer_info['orders'])) {
            $error = $nv_Lang->getModule('no_result');
        } elseif (!count($customer_info['orders'])) {
            $error = sprintf($nv_Lang->getModule('no_result_orders'), $tax_code);
        } else {
            nv_jsonOutput([
                'status' => 'success',
                'data' => $customer_info
            ]);
        }
    } else {
        $error = $nv_Lang->getModule('error_no_taxcode');
    }

    if (!empty($error)) {
        nv_jsonOutput([
            'status' => 'error',
            'message' => $error
        ]);
    } else {
        nv_jsonOutput([
            'status' => 'error',
            'message' => $nv_Lang->getModule('error_getcominfo')
        ]);
    }
}

// TODO: Lấy HTML xem chi tiết Hợp đồng điện tử
if ($nv_Request->isset_request('view_detail', 'get')) {
    $view_id = $nv_Request->get_int('econtract_id', 'get,post', 0);
    $error_view = '';
    $view_econtract = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $view_id)->fetch();
    if ($view_econtract) {
        // Kiểm tra có được quyền Xem không
        if (defined('NV_IS_SPADMIN') || $is_manage_econtract || $my_group_type == 'marketing' || isset($filter_uploader_ids[$view_econtract['uploader_id']])) {
            $xtpl = new XTemplate('econtract.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
            $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

            $view_econtract['econtract_link'] = NV_MAIN_DOMAIN . '/' . $view_econtract['contract_path'];
            $view_econtract['uploader'] = 'N/A';
            if (isset($all_array_user_id_users[$view_econtract['uploader_id']])) {
                $uploader = $all_array_user_id_users[$view_econtract['uploader_id']];
                $view_econtract['uploader'] = nv_show_name_user($uploader['first_name'], $uploader['last_name'], $uploader['userid']) . ' (' . $uploader['username'] . ')';
            }
            $view_econtract['uploader_link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&uploader_id=' . $view_econtract['uploader_id'];
            $view_econtract['updated_at'] = nv_date('d/m/Y H:i', $view_econtract['updated_at']);

            $xtpl->assign('VIEW_ECONTRACT', $view_econtract);

            $order_rows = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id=' . $view_econtract['id'] . ' ORDER BY order_id ASC')->fetchAll();
            if ($order_rows) {
                foreach ($order_rows as $order_row) {
                    $order_row['link'] = URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $order_row['order_id'];
                    $order_vips = [];
                    $text_order_vips = [];
                    if (!empty($order_row['vips_vi'])) {
                        $order_vips = array_merge($order_vips, explode(',', $order_row['vips_vi']));
                    }
                    if (!empty($order_row['vips_en'])) {
                        $order_vips = array_merge($order_vips, explode(',', $order_row['vips_en']));
                    }
                    $order_vips = array_unique($order_vips);
                    if (count($order_vips)) {
                        foreach ($order_vips as $order_vip) {
                            $text_order_vips[] = $global_arr_vip[$order_vip];
                        }
                    }
                    $order_row['vips'] = implode('; ', $text_order_vips);
                    $order_row['order_code'] = '<a href="' . URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $order_row['order_id'] . '" target="_blank"><strong class="label label-success">' . sprintf('BDH%010s', $order_row['order_id']) . '</strong></a>';
                    $xtpl->assign('ORDER_ROW', $order_row);
                    $xtpl->parse('view_detail.loop_order');
                }
            }

            if (defined('NV_IS_SPADMIN') || $is_manage_econtract || ($my_group_type == 'sale' && isset($filter_uploader_ids[$view_econtract['uploader_id']]))) {
                $xtpl->parse('view_detail.show_btn_edit');
            }

            $xtpl->parse('view_detail');
            $res_html = $xtpl->text('view_detail');

            if (empty($error_view)) {
                nv_jsonOutput([
                    'status' => 'success',
                    'html' => $res_html
                ]);
            }
        } else {
            $error_view = $nv_Lang->getModule('no_permission');
        }
    } else {
        $error_view = $nv_Lang->getModule('no_result');
    }

    nv_jsonOutput([
        'status' => 'error',
        'message' => $error_view
    ]);
}

// TODO: Lấy HTML Sửa Hợp đồng điện tử
if ($nv_Request->isset_request('edit_form', 'get')) {
    $view_id = $nv_Request->get_int('econtract_id', 'get,post', 0);
    $error_view = '';
    $edit_econtract = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $view_id)->fetch();
    if ($edit_econtract) {
        // Kiểm tra có được quyền Xem không
        if (defined('NV_IS_SPADMIN') || $is_manage_econtract || isset($filter_uploader_ids[$edit_econtract['uploader_id']])) {
            // Lấy DS đơn hàng đã chọn
            $order_rows = $db->query('SELECT order_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id=' . $edit_econtract['id'])->fetchAll();
            if ($order_rows) {
                foreach ($order_rows as $_order_row) {
                    $order_rows[$_order_row['order_id']] = $_order_row['order_id'];
                }
            } else {
                $order_rows = [];
            }

            $xtpl = new XTemplate('econtract.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
            $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
            $xtpl->assign('OP_BASE_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
            $xtpl->assign('AJAX_SUBMIT_EDIT', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&submit_edit=1');

            $arr_filename_contract = explode('/', $edit_econtract['contract_path']);
            $edit_econtract['contract_attachment'] = end($arr_filename_contract);
            $edit_econtract['url_download_contract'] = NV_MAIN_DOMAIN . '/' . $edit_econtract['contract_path'];

            $xtpl->assign('EDIT_ECONTRACT', $edit_econtract);

            // Hiện DS đơn hàng: Lấy DS theo MST để show
            $where = [];
            $where['AND'] = [
                ['=' => ['tax' => $edit_econtract['tax_code']]]
            ];
            $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
            $api_dtinfo->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListBiddingOrdersByLog')
                ->setData([
                    'where' => $where
                ]);
            $result_order = $api_dtinfo->execute();
            $error_order = $api_dtinfo->getError();
            if (empty($error_order) && $result_order['status'] == 'success' && !empty($result_order['data'])) {
                foreach ($result_order['data'] as $_order) {
                    if (($_order['caregiver_id'] == 0 && (defined('NV_IS_SPADMIN') || $is_manage_econtract)) || isset($filter_uploader_ids[strval($_order['caregiver_id'])]) || defined('NV_IS_SPADMIN') || $is_manage_econtract) {
                        // Thông tin đơn hàng
                        $customer_order = [];
                        $customer_order['id'] = $_order['id'];
                        $customer_order['money'] = number_format($_order['money']) . ' đ';
                        $customer_order['discount'] = number_format($_order['discount']) . ' đ';
                        $customer_order['total'] = number_format($_order['total']) . ' đ';
                        $customer_order['price_reduce'] = number_format($_order['price_reduce']) . ' đ';
                        $customer_order['total_end'] = number_format($_order['total_end']) . ' đ';
                        $customer_order['vip_titles'] = '';
                        $_vip_titles = [];
                        foreach ($_order['vip'] as $_vip) {
                            $_vip_titles[] = $global_arr_vip[$_vip];
                        }
                        $customer_order['vip_titles'] = implode(', ', $_vip_titles);
                        // Lấy username của đơn hàng
                        $customer_order['username'] = $db->query('SELECT username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $_order['userid'])->fetchColumn() ?: '';
                        $customer_order['edit_time'] = !empty($_order['edit_time']) ? nv_date('H:i:s, d/m/Y', $_order['edit_time']) : '-';
                        $customer_order['order_code'] = '<a href="' . URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $_order['id'] . '" target="_blank"><strong class="label label-success">' . sprintf('BDH%010s', $_order['id']) . '</strong><br><small>(' . $customer_order['username'] . ')</small></a>';
                        // Check đơn hàng có hợp đồng chưa?
                        $_existed_econtract = $db->query('SELECT t2.contract_no FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders t1 RIGHT JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts t2 ON t1.econtract_id=t2.id WHERE order_id=' . $_order['id'])->fetchColumn();
                        $customer_order['econtract_no'] = $_existed_econtract ?: '';
                        $customer_order['check'] = isset($order_rows[$_order['id']]) ? 'checked="checked"' : (!empty($customer_order['econtract_no']) ? 'disabled="disabled"' : '');
                        $xtpl->assign('CUSTOMER_ORDER', $customer_order);
                        $xtpl->parse('edit_detail.order_loop');
                    }
                }
            }

            $xtpl->parse('edit_detail');
            $res_html = $xtpl->text('edit_detail');

            if (empty($error_view)) {
                nv_jsonOutput([
                    'status' => 'success',
                    'html' => $res_html
                ]);
            }
        } else {
            $error_view = $nv_Lang->getModule('no_permission');
        }
    } else {
        $error_view = $nv_Lang->getModule('no_result');
    }

    nv_jsonOutput([
        'status' => 'error',
        'message' => $error_view
    ]);
}

// TODO: Bắt đầu xử lý Submit Sửa Hợp đồng điện tử
if ($nv_Request->isset_request('submit_edit', 'post,get')) {
    $error_edit = '';
    $data_upload = [];
    $data_upload['contract_id'] = $nv_Request->get_int('contract_id', 'post', 0);
    $edit_econtract = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $data_upload['contract_id'])->fetch();
    if ($edit_econtract) {
        $data_upload['contract_no'] = $nv_Request->get_title('contract_no', 'post', '');
        $data_upload['tax_code'] = $nv_Request->get_title('tax_code', 'post', '');
        $data_upload['c_name'] = $nv_Request->get_title('c_name', 'post', '');
        $data_upload['c_address'] = $nv_Request->get_title('c_address', 'post', '');
        $data_upload['order_ids'] = $nv_Request->get_typed_array('order_ids', 'post', 'int', []);

        // ? Validate dữ liệu
        if (empty($data_upload['tax_code'])) {
            $error_edit = $nv_Lang->getModule('error_tax_code');
        } elseif (empty($data_upload['c_name'])) {
            $error_edit = $nv_Lang->getModule('error_c_name');
        } elseif (empty($data_upload['c_address'])) {
            $error_edit = $nv_Lang->getModule('error_c_address');
        } elseif (!count($data_upload['order_ids'])) {
            $error_edit = $nv_Lang->getModule('error_order_ids');
        } elseif (empty($data_upload['contract_no'])) {
            $error_edit = $nv_Lang->getModule('error_contract_no');
        } elseif ($db->query('SELECT COUNT(*) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE contract_no=' . $db->quote($data_upload['contract_no']) . ' AND id<>' . $edit_econtract['id'])->fetchColumn()) {
            $error_edit = $nv_Lang->getModule('error_existed_contract_no');
        }

        if (empty($error_edit)) {
            // Lấy danh sách gói VIP
            $contract_vip_ids = [
                'vi' => [],
                'en' => [],
            ];
            $order_vip_ids = [];
            $order_user_ids = [];
            $order_prefix_langs = [];
            $where = [];
            $where['AND'] = [
                ['=' => ['tax' => $data_upload['tax_code']]],
                ['IN' => ['order_id' => '(' . implode(',', $data_upload['order_ids']) . ')']]
            ];
            $api_dtinfo->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListBiddingCustomsLog')
                ->setData([
                    'array_select' => ['vip', 'order_id', 'user_id', 'prefix_lang'],
                    'where' => $where
                ]);
            $result = $api_dtinfo->execute();
            $error_edit = $api_dtinfo->getError();
            if (empty($error_edit) and $result['status'] == 'success' and !empty($result['data'])) {
                $bidding_customs_logs = $result['data'];
                if (is_array($bidding_customs_logs) && $result['total']) {
                    foreach ($bidding_customs_logs as $bidding_customs_log) {
                        if ($bidding_customs_log['prefix_lang'] == 0) {
                            $contract_vip_ids['vi'][] = $bidding_customs_log['vip'];
                            $order_vip_ids[$bidding_customs_log['order_id']]['vi'][] = $bidding_customs_log['vip'];
                        } elseif ($bidding_customs_log['prefix_lang'] == 1) {
                            $contract_vip_ids['en'][] = $bidding_customs_log['vip'];
                            $order_vip_ids[$bidding_customs_log['order_id']]['en'][] = $bidding_customs_log['vip'];
                        }
                        $order_user_ids[$bidding_customs_log['order_id']] = intval($bidding_customs_log['user_id']);
                    }
                }
            }

            if (empty($error_edit)) {
                // Lưu tệp
                $upload = new NukeViet\Files\Upload();
                $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
                if (!empty($_FILES['contract_path']['tmp_name'])) {
                    // Lưu hợp đồng (nếu có)
                    if (!is_dir(NV_UPLOADS_REAL_DIR . '/econtracts')) {
                        nv_mkdir(NV_UPLOADS_REAL_DIR, 'econtracts');
                    }
                    if (!is_dir(NV_UPLOADS_REAL_DIR . '/econtracts/' . strtolower(change_alias($admin_info['username'])))) {
                        nv_mkdir(NV_UPLOADS_REAL_DIR . '/econtracts', strtolower(change_alias($admin_info['username'])));
                    }
                    $path_to_upload_contract = NV_UPLOADS_REAL_DIR . '/econtracts/' . strtolower(change_alias($admin_info['username']));
                    $upload_contract_info = $upload->save_file($_FILES['contract_path'], $path_to_upload_contract, false, $global_config['nv_auto_resize']);
                    if (!empty($upload_contract_info['error'])) {
                        $error_edit = $upload_contract_info['error'];
                    } elseif (!in_array($upload_contract_info['ext'], ['pdf', 'doc', 'docx', 'zip'])) {
                        $error_edit = $nv_Lang->getModule('error_file_type');
                    }
                    if (empty($error_edit)) {
                        // Xóa file cũ đi
                        nv_deletefile(NV_ROOTDIR . '/' . $edit_econtract['contract_path']);
                    }
                }

                if (empty($error_edit)) {
                    if (isset($upload_contract_info) && !empty($upload_contract_info['name'])) {
                        $data_upload['contract_path'] = NV_UPLOADS_DIR . '/econtracts/' . strtolower(change_alias($admin_info['username'])) . '/' . str_replace(['/', '\\'], '-', $data_upload['contract_no']) . '-' . $upload_contract_info['basename'];
                        rename($upload_contract_info['name'], $path_to_upload_contract . '/' . str_replace(['/', '\\'], '-', $data_upload['contract_no']) . '-' . $upload_contract_info['basename']);
                    } else {
                        $data_upload['contract_path'] = $edit_econtract['contract_path'];
                    }

                    // TODO: LƯU VÀO CSDL
                    // ? Lưu bảng _econtracts
                    $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET
                            tax_code=:tax_code,
                            c_name=:c_name,
                            c_address=:c_address,
                            contract_no=:contract_no,
                            contract_path=:contract_path,
                            uploader_id=' . $admin_info['userid'] . ',
                            vips_vi=' . $db->quote(implode(',', array_unique($contract_vip_ids['vi']))) . ',
                            vips_en=' . $db->quote(implode(',', array_unique($contract_vip_ids['en']))) . ',
                            updated_at=' . NV_CURRENTTIME . '
                        WHERE id=' . $edit_econtract['id'];

                    $sth = $db->prepare($sql);
                    $sth->bindParam(':tax_code', $data_upload['tax_code'], PDO::PARAM_STR);
                    $sth->bindParam(':c_name', $data_upload['c_name'], PDO::PARAM_STR);
                    $sth->bindParam(':c_address', $data_upload['c_address'], PDO::PARAM_STR);
                    $sth->bindParam(':contract_no', $data_upload['contract_no'], PDO::PARAM_STR);
                    $sth->bindParam(':contract_path', $data_upload['contract_path'], PDO::PARAM_STR);
                    $sth->execute();

                    // Lấy DS đơn hàng đã chọn trước đó
                    $old_order_rows = [];
                    $_old_order_rows = $db->query('SELECT order_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id=' . $edit_econtract['id'])->fetchAll();
                    if ($_old_order_rows) {
                        foreach ($_old_order_rows as $_order_row) {
                            $old_order_rows[$_order_row['order_id']] = $_order_row['order_id'];
                        }
                    }

                    // ? Lưu bảng _econtract_orders
                    foreach ($data_upload['order_ids'] as $order_id) {
                        // Nếu chưa có thì thêm mới
                        if (!isset($old_order_rows[$order_id])) {
                            $_o_user_id = 0;
                            $_o_user_name = 'N/A';
                            if (isset($order_user_ids[$order_id]) && $order_user_ids[$order_id] > 0) {
                                $o_user = $db->query('SELECT username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $order_user_ids[$order_id])->fetch();
                                $_o_user_id = $order_user_ids[$order_id];
                                $_o_user_name = $o_user['username'];
                            }
                            $sql = 'INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders (
                                    econtract_id, order_id, site_id, user_id, username, vips_vi, vips_en
                                ) VALUES (
                                    ' . $edit_econtract['id'] . ',
                                    ' . $order_id . ',
                                    1,
                                    ' . $_o_user_id . ',
                                    ' . $db->quote($_o_user_name) . ',
                                    ' . $db->quote(implode(',', $order_vip_ids[$order_id]['vi'] ?? [])) . ',
                                    ' . $db->quote(implode(',', $order_vip_ids[$order_id]['en'] ?? [])) . '
                                )';
                            $db->insert_id($sql, 'id');
                            // Lưu log đơn hàng
                            $logApi = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                            $logApi = $logApi->setModule('bidding')
                                ->setLang('vi')
                                ->setAction('CreateBiddingAllLogs')
                                ->setData([
                                    'userid' => $admin_info['admin_id'],
                                    'log_area' => 1,
                                    'log_key' => 'LOG_CHANGE_ORDER_CONTRACT',
                                    'log_time' => NV_CURRENTTIME,
                                    'log_data' => [
                                        [$nv_Lang->getModule('log_update_econtract_success'), $nv_Lang->getModule('new')],
                                        [$nv_Lang->getModule('contract_no') . ': ', $data_upload['contract_no']]
                                    ],
                                    'order_id' => $order_id
                                ])->execute();
                        } else {
                            unset($old_order_rows[$order_id]);
                        }
                    }

                    // Xóa các đơn hàng cũ (nếu không chọn)
                    $order2dels = array_values($old_order_rows);
                    if ($order2dels) {
                        foreach ($order2dels as $order2del) {
                            $db->query('DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE order_id=' . $order2del);
                            $logApi = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                            $logApi->setModule('bidding')
                                ->setLang('vi')
                                ->setAction('CreateBiddingAllLogs')
                                ->setData([
                                    'userid' => $admin_info['admin_id'],
                                    'log_area' => 1,
                                    'log_key' => 'LOG_CHANGE_ORDER_CONTRACT',
                                    'log_time' => NV_CURRENTTIME,
                                    'log_data' => [
                                        [$nv_Lang->getModule('log_delete_econtract_success'), $edit_econtract['contract_no']]
                                    ],
                                    'order_id' => $order2del
                                ])->execute();
                        }
                    }

                    nv_jsonOutput([
                        'status' => 'success',
                        'message' => $nv_Lang->getModule('econtract_update_success')
                    ]);
                }
            }
        }
    } else {
        $error_edit = $nv_Lang->getModule('no_result');
    }

    nv_jsonOutput([
        'status' => 'error',
        'message' => $error_edit
    ]);
}

// Xử lý trình lãnh đạo ký
require_once NV_ROOTDIR . '/modules/' . $module_file . '/admin/econtract_sign.php';

// Xử lý rest econtract_next_no
resetEContractNumber();

// TODO: HIỂN THỊ GIAO DIỆN TRANG DANH SÁCH HỢP ĐỒNG ĐIỆN TỬ
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op;
$per_page = 20;
$page = $nv_Request->get_int('page', 'post,get', 1);
$is_draft = $nv_Request->get_int('draft', 'post,get', 0);

$array_search = array();
$array_search['q'] = $nv_Request->get_title('q', 'post,get', '');
$array_search['vip_id'] = $nv_Request->get_typed_array('vip_id', 'post,get', 'int', []);
$array_search['uploader_id'] = $nv_Request->get_typed_array('uploader_id', 'post,get', 'int', []);
$array_search['status'] = $nv_Request->get_typed_array('status', 'post,get', 'int', []);
$array_search['stage'] = $nv_Request->get_typed_array('stage', 'post,get', 'int', []);
$array_search['term_changed'] = $nv_Request->get_int('term_changed', 'post,get', 0);
$array_search['hard_copy'] = $nv_Request->get_int('hard_copy', 'post,get', 0);

// Lấy danh sách hợp đồng điện tử để hiển thị
$econtract_rows = [];
$where = [];

if (!empty($array_search['q'])) {
    $where_q = [];
    // Tìm theo username
    $username_econtract_ids = [];
    $_econtract_with_usernames = $db->query('SELECT econtract_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE username LIKE ' . $db->quote('%' . $array_search['q'] . '%'))->fetchAll();
    if ($_econtract_with_usernames && count($_econtract_with_usernames)) {
        foreach ($_econtract_with_usernames as $_econtract_with_username) {
            $username_econtract_ids[] = $_econtract_with_username['econtract_id'];
        }
    }
    $username_econtract_ids = array_unique($username_econtract_ids);
    if (count($username_econtract_ids)) {
        $where_q[] = 'id IN (' . implode(',', $username_econtract_ids) . ')';
    }
    // Tìm theo MST
    $where_q[] = 'tax_code LIKE ' . $db->quote($array_search['q'] . '%');
    // Tìm theo Số hợp đồng
    $where_q[] = 'contract_no LIKE ' . $db->quote('%' . $array_search['q'] . '%');
    // Tìm theo Tên công ty
    $where_q[] = 'c_name LIKE ' . $db->quote('%' . $array_search['q'] . '%');

    $where[] = '(' . implode(' OR ', $where_q) . ')';
    $base_url .= '&amp;q=' . $array_search['q'];
}

if (!empty($array_search['vip_id'])) {
    $where_vip = [];
    foreach ($array_search['vip_id'] as $svid) {
        $where_vip[] = 'FIND_IN_SET(' . $svid . ', vips_vi)';
        $where_vip[] = 'FIND_IN_SET(' . $svid . ', vips_en)';
        $base_url .= '&amp;vip_id[]=' . $svid;
    }
    $where[] = '(' . implode(' OR ', $where_vip) . ')';
}

if (!empty($array_search['uploader_id'])) {
    foreach ($array_search['uploader_id'] as $suid) {
        $base_url .= '&amp;uploader_id[]=' . $suid;
    }
    if (!(defined('NV_IS_SPADMIN') || $my_group_type == 'marketing' || $is_manage_econtract)) {
        foreach ($array_search['uploader_id'] as $_isuid => $_suid) {
            if (!in_array($_suid, array_values($filter_uploader_ids))) {
                unset($array_search['uploader_id'][$_isuid]);
            }
        }
        if (!empty($array_search['uploader_id'])) {
            $where[] = 'uploader_id IN (' . implode(',', $array_search['uploader_id']) . ')';
        } else {
            $where[] = 'uploader_id IN (' . implode(',', array_values($filter_uploader_ids)) . ')';
        }
    } else {
        $where[] = 'uploader_id IN (' . implode(',', $array_search['uploader_id']) . ')';
    }
} elseif (!(defined('NV_IS_SPADMIN') || $my_group_type == 'marketing' || $is_manage_econtract)) {
    $where[] = 'uploader_id IN (' . implode(',', array_values($filter_uploader_ids)) . ')';
}

if (!empty($array_search['status'])) {
    foreach ($array_search['status'] as $status) {
        $base_url .= '&amp;status[]=' . $status;
    }
    $where[] = 'status IN (' . implode(', ', $array_search['status']) . ')';
}

if (!empty($array_search['stage'])) {
    foreach ($array_search['stage'] as $stage) {
        $base_url .= '&amp;stage[]=' . $stage;
    }
    $where[] = 'stage IN (' . implode(', ', $array_search['stage']) . ')';
}

if ($array_search['term_changed'] == 1) {
    $where[] = 'term_changed=' . $array_search['term_changed'];
    $base_url .= '&amp;term_changed=' . $array_search['term_changed'];
}

if (!empty($array_search['hard_copy'])) {
    $where[] = "(receiver <> '' AND receiver_phone <> '' AND receiver_address <> '')";
    $base_url .= '&amp;hard_copy=' . $array_search['hard_copy'];
}

$page_title = $nv_Lang->getModule('econtract');

/**
 * https://vinades.org/dauthau/dauthau.info/-/issues/3272
 * Hiện tại các hợp đồng nháp sau khi có 1 bên ký, cập nhật trạng thái tương ứng vẫn đang là hợp đồng nháp.
 * => Cần điều chỉnh nó trở thành hợp đồng chính thức (vì trên thực tế khi 1 bên ký thì đó đã là bản chính thức rồi).
 */
$status_done = [
    EcontractStatus::Done->value,
    EcontractStatus::HSTDTSigned->value,
    EContractStatus::CustomerSigned->value
];
$where[] = 'status IN (' . implode(',', $status_done) . ')';

// Lấy tổng số lượng rows
$db->sqlreset()
    ->select('COUNT(*)')
    ->from(NV_PREFIXLANG . '_' . $module_data . '_econtracts');

if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}
$sth = $db->prepare($db->sql());
$sth->execute();
$num_items = $sth->fetchColumn();

$db->select('*')
    ->from(NV_PREFIXLANG . '_' . $module_data . '_econtracts')
    ->order('created_at DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);

$sth = $db->prepare($db->sql());
$sth->execute();

$xtpl = new XTemplate('econtract.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('Q', $array_search['q']);
$xtpl->assign('OP_BASE_URL', $base_url);
$xtpl->assign('AJAX_GETCOMINFO', $base_url . '&getcominfo=1&tax_code=');
$xtpl->assign('AJAX_SUBMIT_UPLOAD', $base_url . '&submit=1');
$xtpl->assign('NUM_ITEMS', $num_items);
$xtpl->assign('DONE_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
$xtpl->assign('DRAFT_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_draft');
$xtpl->assign('CREATE_CONTRACT_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_content');
$xtpl->assign('TOKEN', NV_CHECK_SESSION);

// Nếu Marketing thì ẩn nút Tải lên hợp đồng
if ($my_group_type == 'sale' || $is_manage_econtract || defined('NV_IS_SPADMIN')) {
    $xtpl->parse('main.allow_add');
    $xtpl->parse('main.allow_add_modal');
}
// Hiển thị danh sách chọn gói VIP tìm kiếm
foreach ($global_arr_vip as $vip_id => $vip_title) {
    $xtpl->assign('VIP', [
        'id' => $vip_id,
        'title' => $vip_title,
        'selected' => in_array($vip_id, $array_search['vip_id']) ? 'selected="selected"' : ''
    ]);
    $xtpl->parse('main.search_vip');
}

// Hiển thị danh sách người tải lên
if (defined('NV_IS_SPADMIN') || $my_group_type == 'marketing' || $is_manage_econtract) {
    foreach ($all_array_user_id_users as $value) {
        $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
        $value['selected'] = in_array($value['userid'], $array_search['uploader_id']) ? 'selected="selected"' : '';
        $value['title'] = $value['username'] . ' (' . $fullname . ')';
        $xtpl->assign('UPLOADER_ID', $value);
        $xtpl->parse('main.search_uploader_id');
    }
} else {
    foreach ($all_array_user_id_users as $value) {
        $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
        $value['selected'] = in_array($value['userid'], $array_search['uploader_id']) ? 'selected="selected"' : '';
        $value['title'] = $value['username'] . ' (' . $fullname . ')';
        $xtpl->assign('UPLOADER_ID', $value);
        if (isset($filter_uploader_ids[$value['userid']])) {
            $xtpl->parse('main.search_uploader_id');
        }
    }
}

// Tìm kiếm theo trang thái
foreach ($status_done as $status_key) {
    $xtpl->assign('STATUS', [
        'id' => $status_key,
        'title' => EContractStatus::tryFrom($status_key)?->getLabel() ?? '',
        'selected' => in_array($status_key, $array_search['status']) ? 'selected="selected"' : ''
    ]);
    $xtpl->parse('main.search_status');
}

// Tìm kiếm theo giai đoạn
foreach ($array_econtract_stage as $stage_key) {
    $xtpl->assign('STAGE', [
        'id' => $stage_key,
        'title' => EContractStage::tryFrom($stage_key)?->getLabel() ?? '',
        'selected' => in_array($stage_key, $array_search['stage']) ? 'selected="selected"' : ''
    ]);
    $xtpl->parse('main.search_stage');
}

// Tìm theo điều khoản thay đổi
foreach ([0, 1] as $term_changed) {
    $xtpl->assign('TERM_CHANGED', [
        'id' => $term_changed,
        'title' => $term_changed ? $nv_Lang->getModule('term_changed_yes') : $nv_Lang->getModule('term_changed_no'),
        'selected' => $term_changed == $array_search['term_changed'] ? 'selected="selected"' : ''
    ]);
    $xtpl->parse('main.search_term_changed');
}

// Lọc theo hợp đồng có in bản cứng
foreach ([0, 1] as $hard_copy) {
    $xtpl->assign('HARD_COPY', [
        'id' => $hard_copy,
        'title' => $hard_copy ? $nv_Lang->getModule('print_hardcopy_yes') : $nv_Lang->getModule('print_hardcopy_no'),
        'selected' => $hard_copy == $array_search['hard_copy'] ? 'selected="selected"' : ''
    ]);
    $xtpl->parse('main.search_hard_copy');
}

$stt = ($page - 1) * $per_page;
while ($row = $sth->fetch()) {
    $row['orders'] = '';
    $row['vips'] = '';
    $row['status_label'] = EContractStatus::tryFrom($row['status'])->getLabel();
    $row['stage_label'] = EContractStage::tryFrom($row['stage'])->getLabel();
    $arr_filename_contract = explode('/', $row['contract_path']);
    $row['contract_attachment'] = end($arr_filename_contract);
    $row['url_download_contract'] = !empty($row['contract_path']) ? NV_MAIN_DOMAIN . '/' . $row['contract_path'] : NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=download&id=' . $row['id'];
    $row['url_detail'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $row['id'];
    $row['url_edit'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_content&id=' . $row['id'];
    $row['url_preview'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=preview&id=' . $row['id'];
    $row['url_upload'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=upload&id=' . $row['id'];
    $row['url_download'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=download&id=' . $row['id'];

    $order_rows = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id=' . $row['id'] . ' ORDER BY order_id ASC')->fetchAll();
    if ($order_rows) {
        foreach ($order_rows as $order_row) {
            $row['orders'] .= '<a href="' . URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $order_row['order_id'] . '" target="_blank"><strong class="label label-success">' . sprintf('BDH%010s', $order_row['order_id']) . '</strong> <small>(' . $order_row['username'] . ')</small></a><br>';
        }
    }
    $order_vips = [];
    if (!empty($row['vips_vi'])) {
        $_order_vips = explode(',', $row['vips_vi']);
        if (!empty($_order_vips)) {
            foreach ($_order_vips as $_order_vip) {
                $order_vips[] = $global_arr_vip[$_order_vip] . ' (Tiếng Việt)';
            }
        }
    }
    if (!empty($row['vips_en'])) {
        $_order_vips = explode(',', $row['vips_en']);
        if (!empty($_order_vips)) {
            foreach ($_order_vips as $_order_vip) {
                $order_vips[] = $global_arr_vip[$_order_vip] . ' (Tiếng Anh)';
            }
        }
    }
    $row['vips'] = implode('; ', $order_vips);

    $row['del_url'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;delete_id=' . $row['id'] . '&amp;delete_checkss=' . md5($row['id'] . NV_CACHE_PREFIX . $client_info['session_id']);

    $row['stt'] = ++$stt;
    // Lấy tên người tải lên (thường là sale)
    $row['uploader'] = 'N/A';
    if (isset($all_array_user_id_users[$row['uploader_id']])) {
        $uploader = $all_array_user_id_users[$row['uploader_id']];
        $row['uploader'] = nv_show_name_user($uploader['first_name'], $uploader['last_name'], $uploader['userid']);
    }
    $row['updated_at'] = nv_date('d/m/Y H:i', $row['updated_at']);

    $xtpl->assign('ROW', $row);
    if (defined('NV_IS_SPADMIN') || $is_manage_econtract || $my_group_type == 'sale') {
        $xtpl->parse('main.loop_row.allow_del_econtract');
    }
    if (defined('NV_IS_SPADMIN') || $is_manage_econtract || $my_group_type == 'sale') {
        $xtpl->parse('main.loop_row.allow_edit_econtract');
    }
    if ($row['customer_type']) {
        $xtpl->parse('main.loop_row.show_mst');
    } else {
        $xtpl->parse('main.loop_row.show_cccd');
    }
    $xtpl->parse('main.loop_row');
}

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
if (!empty($generate_page)) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
