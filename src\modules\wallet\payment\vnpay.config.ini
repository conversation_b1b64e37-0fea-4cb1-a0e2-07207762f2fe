<?xml version="1.0" encoding="utf-8"?>
<payment>
    <name>VNPAYQR</name>
    <domain>https://vnpay.vn/</domain>
    <images_button>[NV_BASE_SITEURL]themes/default/images/wallet/pay-vnpay.jpg</images_button>
    <config>
        <vnp_TmnCode lang_vi="Mã website tại VNPAY" lang_en="Website code at VNPAY">NUKEVIET 1</vnp_TmnCode>
        <vnp_HashSecret lang_vi="Chuỗi bí mật" lang_en="Secret">CZRKYIENCMLGSUZAOLKLXTPSUSENEKEH 1</vnp_HashSecret>
        <vnp_Url lang_vi="URL thanh toán" lang_en="Payment URL">http://sandbox.vnpayment.vn/paymentv2/vpcpay.html 1</vnp_Url>
        <vnp_Version lang_vi="Phiên bản" lang_en="Version">2.0.0</vnp_Version>
        <vnp_Command lang_vi="Mã API sử dụng" lang_en="API code to use">pay</vnp_Command>
        <vnp_CurrCode lang_vi="Đơn vị tiền tệ sử dụng thanh toán" lang_en="Currency used for payment">VND</vnp_CurrCode>
        <vnp_IPIPN lang_vi="IP bên VNPay" lang_en="IP of VNPay" note_vi="Dùng để kiểm tra IPN, bỏ trống thì không kiểm tra" note_en="Used to check IPN, leave it blank and do not check">*************</vnp_IPIPN>
        <IPNAlert lang_vi="Cảnh báo IPN qua email" lang_en="Email IPN Alert" note_vi="Nếu IPN sai IP sẽ thông báo vào email. Nhập 1 để bật, nhập 0 để tắt" note_en="If the IPN is wrong, the IP will be notified to the email. Enter 1 to turn on, enter 0 to turn off">1</IPNAlert>
        <IPNAlertEmail lang_vi="Email nhận cảnh báo" lang_en="Email to receive alert" note_vi="Danh sách email nhận cảnh báo IPN, cách nhau dấu phảy" note_en="IPN alert email list, separated by commas"></IPNAlertEmail>
        <IPNAlertNoti lang_vi="Cảnh báo IPN qua thông báo" lang_en="IPN alert via notification" note_vi="Nếu IPN sai IP sẽ cảnh báo qua thông báo hệ thống. Nhập 1 để bật, nhập 0 để tắt" note_en="If the IPN is wrong, the IP will alert you through the system message. Enter 1 to turn on, enter 0 to turn off">1</IPNAlertNoti>
    </config>
    <currency>VND</currency>
    <optional>1</optional>
</payment>
