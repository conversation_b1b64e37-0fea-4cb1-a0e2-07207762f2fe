<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Fri, 30 Oct 2020 10:49:34 GMT
 */
if (!defined('NV_IS_MOD_UNSUBSCRIBE')) {
    exit('Stop!!!');
}
$page_title = $nv_Lang->getModule('unsub_email');
// dùng để unsubscribe các email cho từng user
if ($nv_Request->isset_request('type_viewhits', 'post')) {
    $checkss = $nv_Request->get_title('c', 'post', '');
    $reason = $nv_Request->get_title('reason', 'post', '');
    $email = $nv_Request->get_title('email', 'post', '');
    if ($reason == $nv_Lang->getModule('other')) {
        $reason = $nv_Request->get_title('moreinfo', 'post', $nv_Lang->getModule('other'));
    }
    if (empty($email)) {
        $contents = nv_theme_alert('', $nv_Lang->getModule('unknown_error'), 'danger');
    }
    $type = 'viewhits';
    if ($checkss != md5('viewhits-' . NV_CHECK_SESSION . '-' . $email)) {
        $contents = nv_theme_alert('', $nv_Lang->getModule('unknown_error'), 'danger');
    } else {
        $stmt = $db->prepare('SELECT userid, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE email = :email');
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->execute();
        $user = $stmt->fetch();
        empty($user) && $user['userid'] = 0;
        $stmt = $db->prepare('SELECT email FROM ' . $db_config['prefix'] . '_' . $module_name . ' WHERE email = :email AND type="viewhits"');
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->execute();
        $check = $stmt->fetch();
        if (!empty($check)) {
            $contents = nv_theme_alert('', sprintf($nv_Lang->getModule('duplicate_error'), $email), 'danger');
        } else {
            $stmt = $db->prepare('INSERT INTO ' . $db_config['prefix'] . '_' . $module_name . '(userid, email, reason, type, time_add) VALUE (:userid, :email, :reason, :type, ' . NV_CURRENTTIME . ')');
            $stmt->bindParam(':userid', $user['userid'], PDO::PARAM_STR);
            $stmt->bindParam(':email', $email, PDO::PARAM_STR);
            $stmt->bindParam(':reason', $reason, PDO::PARAM_STR);
            $stmt->bindParam(':type', $type, PDO::PARAM_STR);
            if ($stmt->execute()) {
                $contents = nv_theme_alert('', $nv_Lang->getModule('unsub_success'), 'success');
            } else {
                $contents = nv_theme_alert('', $nv_Lang->getModule('unknown_error'), 'danger');
            }
        }
    }
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

// Unsubcribe One-Click
if ($nv_Request->isset_request('d', 'get') && $nv_Request->isset_request('c', 'get')) {
    $d = $nv_Request->get_title('d', 'get', '');
    $c = $nv_Request->get_title('c', 'get', '');
    $email = nv_base64_decode(urldecode($d));

    $email_encoded = urlencode(nv_base64_encode($email));
    $check_key = md5($email_encoded . UNSUBCRIBE_KEY);

    if ($check_key != $c) {
        $contents = nv_theme_alert('', $nv_Lang->getModule('unknown_error'), 'danger');
        include NV_ROOTDIR . '/includes/header.php';
        echo nv_site_theme($contents);
        include NV_ROOTDIR . '/includes/footer.php';
    }

    $type = 'oneclick';
    $reason = 'Unsubcribe One-Click';
    $stmt = $db->prepare('SELECT userid, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE email = :email');
    $stmt->bindParam(':email', $email, PDO::PARAM_STR);
    $stmt->execute();
    $user = $stmt->fetch();
    empty($user) && $user['userid'] = 0;
    $stmt = $db->prepare('SELECT email FROM ' . $db_config['prefix'] . '_' . $module_name . ' WHERE email = :email AND type="oneclick"');
    $stmt->bindParam(':email', $email, PDO::PARAM_STR);
    $stmt->execute();
    $check = $stmt->fetch();

    if (nv_check_valid_email($email) != '') {
        $contents = nv_theme_alert('', $nv_Lang->getModule('unknown_error'), 'danger');
    } elseif (!empty($check)) {
        $contents = nv_theme_alert('', sprintf($nv_Lang->getModule('duplicate_error'), $email), 'danger');
    } else {
        $stmt = $db->prepare('INSERT INTO ' . $db_config['prefix'] . '_' . $module_name . '(userid, email, reason, type, time_add) VALUE (:userid, :email, :reason, :type, ' . NV_CURRENTTIME . ')');
        $stmt->bindParam(':userid', $user['userid'], PDO::PARAM_STR);
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->bindParam(':reason', $reason, PDO::PARAM_STR);
        $stmt->bindParam(':type', $type, PDO::PARAM_STR);
        if ($stmt->execute()) {
            $parameters = [
                'emails' => json_encode([[
                    'email' => $email,
                    'message' => $reason
                ]])
            ];
            doMarketingAPI('SetDeadEmail', $parameters);

            $contents = nv_theme_alert('', $nv_Lang->getModule('unsub_success'), 'success');
        } else {
            $contents = nv_theme_alert('', $nv_Lang->getModule('unknown_error'), 'danger');
        }
    }

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

if (empty($array_op)) {
    $contents = nv_theme_alert('', $nv_Lang->getModule('unknown_error'), 'danger');
} else {
    $email = $nv_Request->get_title('email', 'get', '');
    if (empty($email)) {
        $contents = nv_theme_alert('', $nv_Lang->getModule('unknown_error'), 'danger');
    } else {
        $stmt = $db->prepare('SELECT userid, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE email = :email');
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->execute();
        $user = $stmt->fetch();
        if ($array_op[0] == md5('viewhits-notuser-' . $email) || (!empty($user) && $array_op[0] == md5('viewhits-' . $user['username'] . '-' . $user['userid'] . '-' . $email))) {
            $type = 'viewhits';
        }

        if (empty($type)) {
            $contents = nv_theme_alert('', $nv_Lang->getModule('unknown_error'), 'danger');
        } else {
            $checkss = md5('viewhits-' . NV_CHECK_SESSION . '-' . $email);
            $reasons = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_reason ORDER BY weight ASC')->fetchAll();
            $contents = nv_unsubscride_main_theme($type, $email, $reasons, $checkss);
        }
    }
}

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
