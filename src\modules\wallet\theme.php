<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */

if (!defined('NV_IS_MOD_WALLET'))
    die('Stop!!!');

/**
 * redict_link()
 *
 * @param mixed $lang_view
 * @param mixed $lang_back
 * @param mixed $nv_redirect
 * @return
 */
function redict_link($lang_view, $lang_back, $nv_redirect)
{
    $nv_redirect = nv_url_rewrite($nv_redirect, true);
    $contents = "<div class=\"text-center alert alert-info\">";
    $contents .= $lang_view . "<br /><br />\n";
    $contents .= "<img border=\"0\" src=\"" . NV_BASE_SITEURL . NV_ASSETS_DIR . "/images/load_bar.gif\"><br /><br />\n";
    $contents .= "<a href=\"" . $nv_redirect . "\">" . $lang_back . "</a>";
    $contents .= "</div>";
    $contents .= "<meta http-equiv=\"refresh\" content=\"6;url=" . $nv_redirect . "\" />";
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

/**
 * @param array $transaction
 * @param array $api_banks
 * @param array $array_bank
 * @return string
 */
function nv_theme_wallet_waitatm($transaction, $api_banks, $array_bank)
{
    global $module_info, $nv_Lang;

    $xtpl = new XTemplate('waitatm.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    $xtpl->assign('TRANSACTION', $transaction);
    $xtpl->assign('COL_WIDTH', $transaction['qr_supported'] ? '12' : '24');

    // Hiển thị mã QR
    if ($transaction['qr_supported']) {
        if (empty($transaction['qr_image'])) {
            $xtpl->parse('main.qr.noimg');
        } else {
            $xtpl->parse('main.qr.img');
        }

        $xtpl->parse('main.qr');
    }

    if (isset($transaction['acq_id']) and isset($api_banks[$transaction['acq_id']])) {
        $xtpl->assign('LOGO', $api_banks[$transaction['acq_id']]['logo']);
        $xtpl->assign('ALT_LOGO', $api_banks[$transaction['acq_id']]['name']);
        $xtpl->parse('main.logo_bank');
    }
    $xtpl->assign('BANK', $array_bank);
    $xtpl->assign('MESSAGE_NOTE', sprintf($nv_Lang->getModule('atm_mess_note'), $transaction['transaction_code']));

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * @param mixed $url_checkout
 * @param mixed $payport_content
 * @param array $bank_info_list
 * @return
 */
function nv_theme_wallet_main($url_checkout, $payport_content, $bank_info_list)
{
    global $global_config, $module_name, $module_config, $module_info, $op, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('module_file', $module_info['module_theme']);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('TEMPLATE', $module_info['template']);

    if (!empty($payport_content)) {
        $xtpl->assign('PAYPORT_CONTENT', $payport_content);
        $xtpl->parse('main.payport_content');
    }

    $flag = false;
    if ($module_config[$module_name]['allow_smsNap'] == 1) {
        $xtpl->assign('URLNAP', NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=pay/sms");
        $xtpl->parse('main.payment.smsNap');
        $flag = true;
    }

    if (!empty($url_checkout)) {
        $loop_i = 0;
        foreach ($url_checkout as $value) {
            $loop_i++;
            $xtpl->assign('DATA_PAYMENT', $value);
            if ($loop_i % 2 == 0) {
                $xtpl->parse('main.payment.paymentloop.clear_sm');
            }
            if ($loop_i % 3 == 0) {
                $xtpl->parse('main.payment.paymentloop.clear_md');
            }
            $xtpl->parse('main.payment.paymentloop');

            if ($value['payment'] == 'ATM') {
                if (!empty($bank_info_list)) {
                    foreach ($bank_info_list as $key => $bank_info) {
                        $xtpl->assign('bank_info', $bank_info);
                        if (!empty($bank_info['description_content'])) {
                            $xtpl->parse('main.payment.paymentguideloop.bank_info.description_content');
                        }
                        if (!empty($bank_info['note'])) {
                            $xtpl->parse('main.payment.paymentguideloop.bank_info.note');
                        }
                        $xtpl->parse('main.payment.paymentguideloop.bank_info');
                    }
                }
            }
            if (!empty($value['guide'])) {
                $xtpl->parse('main.payment.paymentguideloop.guide');
            }
            $xtpl->parse('main.payment.paymentguideloop');
        }

        $flag = true;
    }
    if ($flag) {
        $xtpl->parse('main.payment');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * @param mixed $row_payment
 * @param mixed $post
 * @param mixed $array_money_unit
 * @param array $bank_info
 * @return
 */
function nv_theme_wallet_recharge($row_payment, $post, $array_money_unit, $bank_info)
{
    global $global_config, $module_name, $module_config, $module_info, $module_captcha, $op, $payment_config, $nv_Cache, $array_banks, $nv_Lang, $province_list;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('ROW_PAYMENT', $row_payment);
    $xtpl->assign('TOKEND', sha1($row_payment['payment'] . NV_CHECK_SESSION));
    $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "/" . $row_payment['payment']);

    foreach ($province_list as $key => $value) {
        $xtpl->assign('PROVINCE', array(
            'key' => $key,
            'title' => $value['title'],
            'selected' => $post['province'] == $key ? ' selected="selected"' : ''
        ));
        $xtpl->parse('main.province');
    }

    // Nếu không hỗ trợ nạp tiền VND thì hiển thị quy đổi
    if (!isset($array_money_unit[$post['money_unit']])) {
        $unit = array_key_first($array_money_unit);
        $amount = display_money($array_money_unit[$unit] * floatval(str_replace([' ', '.', ','], '', $post['money_amount'] ?: $post['money_other'])));

        $xtpl->assign('CONVERT_AMOUNT', $amount);
        $xtpl->assign('CONVERT_UNIT', $unit);
        $xtpl->assign('CONVERT_RATE', $array_money_unit[$unit]);

        $xtpl->parse('main.currency_convert');
    }

    // Xuất các mức tiền nạp của loại tiền tệ đang chọn
    $array_amount = $module_config[$module_name]['minimum_amount'][$post['money_unit']];
    $array_amount = array_filter(explode(',', $array_amount));
    if (!empty($array_amount)) {
        $xtpl->assign('DISPLAY_MINIMUM_AMOUNT', '');
        $xtpl->assign('MINIMUM_AMOUNT', get_display_money($array_amount[0]));
        foreach ($array_amount as $amount) {
            $select_amount = array(
                'key' => $amount,
                'title' => get_display_money($amount),
                'selected' => $post['money_amount'] == $amount ? ' selected="selected"' : ''
            );
            $xtpl->assign('SELECT_AMOUNT', $select_amount);
            $xtpl->parse('main.select_amount.loop');
        }
        if (!empty($row_payment['allowedoptionalmoney'])) {
            if (empty($post['money_amount'])) {
                $xtpl->assign('SELECT_AMOUNT_OTHER', ' selected="selected"');
            } else {
                $xtpl->assign('SELECT_AMOUNT_OTHER', '');
            }
            $xtpl->parse('main.select_amount.other');
        }
        $xtpl->parse('main.select_amount');
    } else {
        $xtpl->assign('DISPLAY_MINIMUM_AMOUNT', ' style="display: none;"');
        $xtpl->assign('MINIMUM_AMOUNT', '');
        // Nếu không cấu hình mức tiền tệ thì xuất input nhập tiền
        $xtpl->parse('main.input_amount');
    }

    // Hiển thị hoặc ẩn ô nhập số tiền khác
    if (!empty($row_payment['allowedoptionalmoney']) and empty($post['money_amount'])) {
        $xtpl->assign('SHOWCUSTOMMONEYAMOUNT', '');
    } else {
        $xtpl->assign('SHOWCUSTOMMONEYAMOUNT', ' style="display: none;"');
    }

    $post['check_term'] = empty($post['check_term']) ? '' : ' checked="checked"';
    if (!empty($post['money_other'])) {
        $post['money_other'] = number_format($post['money_other'], 0, ',', '.');
    }

    $xtpl->assign('DATA', $post);

    // Điều khoản thanh toán
    if (!empty($row_payment['term'])) {
        $xtpl->parse('main.term');
    }

    if ($row_payment['payment'] == 'ATM') {
        // Hiển thị thông tin thanh toán qua ATM
        if (!empty($bank_info)) {
            foreach ($bank_info as $k => $bank) {
                if ($bank['account_number'] == $post['atm_toacc']) {
                    $bank['selected'] = 'selected';
                }
                $xtpl->assign('bank_number', $bank);
                $xtpl->parse('main.atm.bank_number');
                $xtpl->assign('bank_name', $bank);
                $xtpl->parse('main.atm.bank_name');
            }
        }

        $xtpl->parse('main.atm');
    }

    if ($module_captcha == 'recaptcha' and $global_config['recaptcha_ver'] == 3) {
        // Nếu dùng reCaptcha v3
        $xtpl->parse('main.recaptcha3');
    } elseif ($module_captcha == 'recaptcha' and $global_config['recaptcha_ver'] == 2) {
        // Nếu dùng reCaptcha v2
        $xtpl->parse('main.recaptcha');
    } elseif ($module_captcha == 'captcha') {
        $xtpl->parse('main.captcha');
    }

    if (!empty($post['error'])) {
        $xtpl->parse('main.error');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_wallet_acountuser()
 *
 * @param mixed $arr_money_user
 * @return
 */
function nv_wallet_acountuser($arr_money_user)
{
    global $global_config, $module_name, $module_config, $module_info, $op, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);

    for ($i = 0; $i < count($arr_money_user); $i++) {
        $arr_money_user_i = $arr_money_user[$i]['detail'];
        $xtpl->assign('ROW', $arr_money_user_i);
        $xtpl->parse('main.loop');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_wallet_money_sys()
 *
 * @param mixed $arr_money_sys
 * @return
 */
function nv_wallet_money_sys($arr_money_sys)
{
    global $global_config, $module_name, $module_config, $module_info, $op, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('URL_EXCHANGE_BACK', nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=money", true));

    foreach ($arr_money_sys as $arr_money_sys_i) {
        $xtpl->assign('money1', $arr_money_sys_i['code']);
        $xtpl->parse('main.loopmoney1');
        $xtpl->assign('money2', $arr_money_sys_i['code']);
        $xtpl->parse('main.loopmoney2');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_wallet_history_exchange()
 *
 * @param mixed $array
 * @param mixed $generate_page
 * @param mixed $page
 * @return
 */
function nv_wallet_history_exchange($array, $generate_page, $page, $per_page)
{
    global $module_name, $module_info, $op, $global_array_transaction_status, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);

    $i = ($page - 1) * $per_page;
    foreach ($array as $row) {
        $i++;
        $xtpl->assign('STT', $i);

        $row['created_time'] = date("d/m/Y H:i", $row['created_time']);
        $row['money_total'] = get_display_money($row['money_total']);
        $row['money_net'] = get_display_money($row['money_net']);
        $row['status'] = empty($row['order_id']) ? ($row['status'] == 1 ? '+' : '-') : '';
        $row['transaction_status'] = isset($global_array_transaction_status[$row['transaction_status']]) ? $global_array_transaction_status[$row['transaction_status']] : 'N/A';

        $xtpl->assign('ROW', $row);
        $xtpl->parse('main.loop');

    }

    if (!empty($generate_page)) {
        $xtpl->assign('GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_theme_wallet_pay()
 *
 * @param mixed $row_payment
 * @return
 */
function nv_theme_wallet_pay_gamebank($row_payment, $post, $error)
{
    global $global_config, $module_name, $module_captcha, $module_config, $module_info, $op, $nv_Lang;

    if (empty($row_payment['bodytext'])) {
        $nv_Lang->setModule('note_pay', sprintf($nv_Lang->getModule('note_pay'), $row_payment['domain']));
    } else {
        $nv_Lang->setModule('note_pay', $row_payment['bodytext']);
    }
    $xtpl = new XTemplate("gamebank.tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('DATA', $post);
    if (!empty($error)) {
        $xtpl->assign('ERROR', $error);
        $xtpl->parse('main.error');
    }

    // Nếu dùng reCaptcha v3
    if ($module_captcha == 'recaptcha' and $global_config['recaptcha_ver'] == 3) {
        $xtpl->parse('main.recaptcha3');
    }
    // Nếu dùng reCaptcha v2
    elseif ($module_captcha == 'recaptcha' and $global_config['recaptcha_ver'] == 2) {
        $xtpl->parse('main.recaptcha');
    } elseif ($module_captcha == 'captcha') {
        $xtpl->parse('main.captcha');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_theme_sms()
 *
 * @param mixed $smsConfig_keyword
 * @param mixed $smsConfig_port
 * @param mixed $smsConfig_prefix
 * @return
 */
function nv_theme_sms($smsConfig_keyword, $smsConfig_port, $smsConfig_prefix)
{
    global $global_config, $module_name, $user_info, $module_config, $module_info, $op, $nv_Lang;

    $xtpl = new XTemplate("sms.tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('sms', sprintf($nv_Lang->getModule('sms'), $smsConfig_keyword . " " . $smsConfig_prefix, $user_info['email'], $smsConfig_keyword . " " . $smsConfig_prefix, $user_info['email'], $smsConfig_port));

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * nv_theme_wallet_pay()
 *
 * @param mixed $url_checkout
 * @param mixed $payport_content
 * @return
 */
function nv_theme_wallet_pay($url_checkout, $payport_content, $order_info, $money_info)
{
    global $global_config, $module_name, $module_config, $module_info, $op, $nv_Lang;

    $xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('module_file', $module_info['module_theme']);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('TEMPLATE', $module_info['template']);

    if (!empty($payport_content)) {
        $xtpl->assign('PAYPORT_CONTENT', $payport_content);
        $xtpl->parse('main.payport_content');
    }

    $order_info['money_amountdisplay'] = get_display_money($order_info['money_amount']);
    $xtpl->assign('ORDER', $order_info);
    $xtpl->assign('ORDER_OBJ', $order_info['title']);

    if (!empty($url_checkout)) {
        $loop_i = 0;
        foreach ($url_checkout as $value) {
            $loop_i++;
            $xtpl->assign('DATA_PAYMENT', $value);
            if ($loop_i % 2 == 0) {
                $xtpl->parse('main.payment.paymentloop.clear_sm');
            }
            if ($loop_i % 3 == 0) {
                $xtpl->parse('main.payment.paymentloop.clear_md');
            }

            $xtpl->parse('main.payment.paymentloop');

            if (!empty($value['data']['bodytext'])) {
                $xtpl->parse('main.payment.paymentguideloop.guide');
            }
            if ($value['payment_type'] != 'direct') {
                // Thanh toán quy đổi bằng ngoại tệ khác
                $xtpl->assign('EXPAY_MSG', sprintf($nv_Lang->getModule('paygate_exchange_pay_msg'), $order_info['money_unit'], get_display_money($value['exchange_info']['total']) . ' ' . $value['exchange_info']['currency']));
                $xtpl->parse('main.payment.paymentguideloop.exchange');
            }
            $xtpl->parse('main.payment.paymentguideloop');
        }

        $xtpl->parse('main.payment');
    }

    // Xuất thông tin ví tiền
    $xtpl->assign('WALLET', $money_info);
    $xtpl->assign('WPAYMSG', sprintf($nv_Lang->getModule('paygate_wpay_msg'), $order_info['money_amountdisplay'] . ' ' . $order_info['money_unit']));

    if ($money_info['moneytotalnotformat'] < $order_info['money_amount']) {
        $xtpl->assign('LINK_RECHARGE', NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . '&amp;amount=' . ($order_info['money_amount'] - $money_info['moneytotalnotformat']) . '-' . $order_info['money_unit']);
        $xtpl->parse('main.wpay_cant');
    } else {
        $xtpl->parse('main.wpay_detail');
        $xtpl->parse('main.wpay_submit');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * @param array $order_info
 * @param array $row_payment
 * @param array $post
 * @param string $error
 * @return string
 */
function nv_theme_wallet_atm_pay($order_info, $row_payment, $post, $error, $bank_info)
{
    global $global_config, $module_info, $module_captcha, $payment_config, $array_banks, $money_net, $nv_Lang;

    $xtpl = new XTemplate('atm_pay.tpl', NV_ROOTDIR . "/themes/" . $module_info['template'] . "/modules/" . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $xtpl->assign('ROW_PAYMENT', $row_payment);
    $xtpl->assign('FORM_ACTION', $order_info['payurl'] . '&amp;payment=' . $row_payment['payment']);
    $xtpl->assign('AJAX_ACTION', str_replace('&amp;', '&', $order_info['payurl'] . '&amp;payment=' . $row_payment['payment']));
    $xtpl->assign('TOKEND', NV_CHECK_SESSION);

    $xtpl->assign('MONEY_NET', $money_net);
    $xtpl->assign('DATA', $post);

    $order_info['code'] = sprintf('DH%010s', $order_info['id']);
    $order_info['money_amount'] = get_display_money($order_info['money_amount']);
    $xtpl->assign('ORDER', $order_info);

    // Hiển thị thông tin thanh toán qua ATM
    if (!empty($bank_info) and $row_payment['payment'] == 'ATM') {
        foreach ($bank_info as $k => $bank) {
            if ($bank['account_number'] == $post['atm_toacc']) {
                $bank['selected'] = 'selected';
            }
            $xtpl->assign('bank_number', $bank);
            $xtpl->parse('main.bank_number');
            $xtpl->assign('bank_name', $bank);
            $xtpl->parse('main.bank_name');
        }
    }

    if ($module_captcha == 'recaptcha' and $global_config['recaptcha_ver'] == 3) {
        // Nếu dùng reCaptcha v3
        $xtpl->parse('main.recaptcha3');
    } elseif ($module_captcha == 'recaptcha' and $global_config['recaptcha_ver'] == 2) {
        // Nếu dùng reCaptcha v2
        $xtpl->parse('main.recaptcha');
    } elseif ($module_captcha == 'captcha') {
        $xtpl->parse('main.captcha');
    }

    if (!empty($error)) {
        $xtpl->assign('ERROR', $error);
        $xtpl->parse('main.error');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}
