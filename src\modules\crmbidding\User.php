<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\crmbidding;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

/**
 * <AUTHOR>
 *
 */
class User
{
    /**
     * @param array|int $id
     * @return array|string[]
     * @desc Lấy danh sách thành viên theo 1 id hoặc nhiều id nào đó
     */
    public static function getUsers($id)
    {
        global $db;

        if (!is_array($id)) {
            $id = [$id];
        }
        if (empty($id)) {
            return [];
        }

        $sql = "SELECT userid, username, first_name, last_name, email
        FROM " . NV_USERS_GLOBALTABLE . " WHERE userid IN(" . implode(',', $id) . ")";
        $result = $db->query($sql);

        $array = [];
        while ($row = $result->fetch()) {
            $row['full_name'] = nv_show_name_user($row['first_name'], $row['last_name']);
            $row['show_name'] = $row['username'];
            if (!empty($row['full_name'])) {
                $row['show_name'] .= ' (' . $row['full_name'] . ')';
            }
            $array[$row['userid']] = $row;
        }

        return $array;
    }

    /**
     * @return array
     * @desc Lấy cấu hình tùy biến dữ liệu module users
     */
    public static function getFieldsConfig()
    {
        global $db;

        $array_field_config = [];
        $result_field = $db->query('SELECT * FROM ' . NV_USERS_GLOBALTABLE . '_field ORDER BY weight ASC');
        while ($row_field = $result_field->fetch()) {
            $language = unserialize($row_field['language']);
            $row_field['title'] = (isset($language[NV_LANG_DATA])) ? $language[NV_LANG_DATA][0] : $row_field['field'];
            $row_field['description'] = (isset($language[NV_LANG_DATA])) ? nv_htmlspecialchars($language[NV_LANG_DATA][1]) : '';
            if (!empty($row_field['field_choices'])) {
                $row_field['field_choices'] = unserialize($row_field['field_choices']);
            } elseif (!empty($row_field['sql_choices'])) {
                $row_field['sql_choices'] = explode('|', $row_field['sql_choices']);
                $row_field['field_choices'] = [];
                $query = 'SELECT ' . $row_field['sql_choices'][2] . ', ' . $row_field['sql_choices'][3] . ' FROM ' . $row_field['sql_choices'][1];
                if (!empty($row_field['sql_choices'][4]) and !empty($row_field['sql_choices'][5])) {
                    $query .= ' ORDER BY ' . $row_field['sql_choices'][4] . ' ' . $row_field['sql_choices'][5];
                }
                $result = $db->query($query);
                $weight = 0;
                while (list ($key, $val) = $result->fetch(3)) {
                    $row_field['field_choices'][$key] = $val;
                }
            }
            $row_field['system'] = $row_field['is_system'];
            $array_field_config[$row_field['field']] = $row_field;
        }

        return $array_field_config;
    }
}
