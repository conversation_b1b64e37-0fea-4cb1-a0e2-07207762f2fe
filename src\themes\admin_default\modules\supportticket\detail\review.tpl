<!-- BEGIN: main -->
<div class="rating-wrap">
    <div class="row-rating">
        <div class="rating-12">
            <form class="rating" data-comment-id="{COMMENT.log_id}" style="display:{RATING.display_rating}">
                <div class="rating-row">
                    <span class="rating-title">{LANG.member_review}</span>
                    <span class="rating-star">
                        <!-- BEGIN: star -->
                        <input type="radio" name="star" class="star" value="{STAR.value}" {STAR.checked} disabled/>
                        <!-- END: star -->
                    </span>
                </div>
            </form>
        </div>
        <!-- BEGIN: mark_invalid -->
        <div class="rating-12">
            <form class="rating" data-comment-id="{COMMENT.log_id}" style="display:{RATING.display_rating}">
                <label class="rating-row">
                    <input type="checkbox" name="ivalid" id="change_invalid_{COMMENT.log_id}" value="{COMMENT.log_id}" {CHECK} onclick="nv_change_invalid({COMMENT.log_id}, {COMMENT.ticket_id});" />
                    {LANG.mark_invalid}
                </label>
            </form>
        </div>
        <!-- END: mark_invalid -->
    </div>
    <!-- BEGIN: review -->
    <div class="row">
        <div class="col-md-24 review">
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-24">
                        <div class="text-left"><strong>{COMMENT.customer_review}</strong> <span class="time">{COMMENT.rating_time}</span></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-24">
                        <div class="comment-content bodytext">
                            <div class="content">{COMMENT.rating_content}</div>
                        </div>
                        <!-- BEGIN: refund -->
                        <button type="button" class="btn btn-primary add-refund" data-comment-id="{COMMENT.log_id}">
                            <i class="fa fa-paper-plane" aria-hidden="true"></i> {LANG.process_action_refund}
                        </button>
                        <!-- END: refund -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- BEGIN: admin_reply -->
    <div class="row admin_reply">
        <div class="col-md-24">
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-24">
                        <div class="text-left"><strong>{LANG.reply_by_admin}</strong> <span class="time">{COMMENT.reply_time}</span> <span class="text-{COMMENT.class_refund}">{COMMENT.message_refund}</span></div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-24">
                        <div class="comment-content bodytext">
                            <div class="content">{COMMENT.rating_reply}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    <!-- END: admin_reply -->
    <!-- END: review -->
</div>
<script>
    $(document).ready(function() {
        //Sửa đánh giá
        $('.add-refund').on('click', function (event) {
            event.stopImmediatePropagation(); // Ngăn chặn việc click 1 lần nhưng call nhiều lần
            var commentId = $(this).data('comment-id');
            var refundFormToken = $('#detail_token').val();
            var actionUrl = $('#detail_paid_form_action').attr('action');
            var parentDiv = $(this).closest('.rating-wrap');
            $.ajax({
                url: actionUrl,
                method: 'POST',
                data: { comment_id: commentId, refund_form_token: refundFormToken },
                success: function(response) {
                    if (response.success) {
                        parentDiv.find('.review').html(response.data.html);
                        handleEditor('rating_reply_' + response.data.comment_id);
                    } else {
                        alert(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Form submission failed:', error);
                }
            });
        });
    });
</script>
<!-- END: main -->
