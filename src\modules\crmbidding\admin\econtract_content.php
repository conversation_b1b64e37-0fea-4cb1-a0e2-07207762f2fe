<?php

/**
 * T<PERSON>h năng quản lý hợp đồng điện tử: T<PERSON><PERSON> đơn hàng
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2024 Hu<PERSON>nh Quốc Đạt. All rights reserved
 * @createdAt Mon, 15 Apr 2024 11:55:00
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

// Xử lý trình lãnh đạo ký
require_once NV_ROOTDIR . '/modules/' . $module_file . '/admin/econtract_sign.php';

// Load thông tin đơn hàng đã chọn
if ($nv_Request->isset_request('load_order_data', 'get')) {
    $_error = '';
    $data_return = [
        'sale_name' => 'N/A',
        'orders' => [],
        'vips' => [],
        'price_info' => [
            'total' => 0,
            'discount' => 0,
            'total_payment' => 0,
        ]
    ];
    $ids = $nv_Request->get_typed_array('ids', 'get', 'int');
    $ec_id = $nv_Request->get_int('id', 'get', 0);

    if (count($ids) == 1) {
        // Nếu chỉ có đơn hàng => kiểm tra đơn hàng đó đã có hợp đồng chưa để hiện cảnh báo
        $exited_contract = $db->query('SELECT t2.id, t2.contract_no FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders t1 RIGHT JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts t2 ON t1.econtract_id=t2.id WHERE order_id=' . end($ids))->fetch();
        if (!empty($exited_contract['contract_no']) && $exited_contract['id'] != $ec_id) {
            $_error = 'Đơn hàng bạn chọn đã có hợp đồng: ' . $exited_contract['contract_no'] . ', vui lòng kiểm tra lại. Nếu muốn gộp chung vào danh sách đơn hàng, hãy chọn từ 2 đơn hàng trở lên.';
        }
    }
    if (empty($_error)) {
        $where = [];
        $where['AND'] = [
            ['IN' => ['id' => '(' . implode(',', $ids) . ')']]
        ];
        $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
        $api_dtinfo->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingOrder')
            ->setData([
                'where' => $where,
                'show_customs_log' => 1
            ]);
        $result = $api_dtinfo->execute();
        $error = $api_dtinfo->getError();

        if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
            foreach ($result['data'] as $_order) {
                // Sale name
                if (isset($all_array_user_id_users[$_order['caregiver_id']])) {
                    $saler = $all_array_user_id_users[$_order['caregiver_id']];
                    $data_return['sale_name'] = nv_show_name_user($saler['first_name'], $saler['last_name'], $saler['userid']);
                }
                // Danh sách thông tin đơn hàng (hiện sidebar phải)
                $data_return['orders'][] = [
                    'id' => $_order['id'],
                    'code' => sprintf('BDH%010s', $_order['id']),
                    'link' => URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $_order['id']
                ];
                // Thông tin giá, chi phí đơn hàng
                $data_return['price_info']['total'] += $_order['money'];
                $data_return['price_info']['discount'] += $_order['discount'];
                $data_return['price_info']['total_payment'] += $_order['total'];

                if (!empty($_order['customs_log'])) {
                    foreach ($_order['customs_log'] as $custom_vip) {
                        // Danh sách các sản phẩm, dịch vụ
                        $data_return['vips'][] = [
                            'title' => $global_arr_vip[$custom_vip['vip']] . ($custom_vip['prefix_lang'] == 0 ? ' (Tiếng Việt)' : ' (Tiếng Anh)'),
                            'price_format' => nv_currency_format($custom_vip['vip_price'] * $custom_vip['numbers_year'])
                        ];
                    }
                }
            }

            $data_return['price_info']['total'] = nv_currency_format($data_return['price_info']['total']);
            $data_return['price_info']['discount'] = nv_currency_format($data_return['price_info']['discount']);
            $data_return['price_info']['total_payment'] = nv_currency_format($data_return['price_info']['total_payment']);

            nv_jsonOutput([
                'status' => true,
                'data' => $data_return
            ]);
        }
    }

    nv_jsonOutput([
        'status' => false,
        'message' => $_error
    ]);
}

// Load thông tin khách hàng và danh sách đơn hàng của khách
if ($nv_Request->isset_request('load_customer_data', 'get,post')) {
    $_error = '';
    $data_return = [
        'username' => '',
        'fullname' => '',
        'orders' => []
    ];
    $userid = $nv_Request->get_int('userid', 'get,post');
    if ($userid) {
        $customer = $db->query('SELECT userid, username, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $userid)->fetch() ?: null;
        if ($customer) {
            $data_return['username'] = $customer['username'];
            $data_return['fullname'] = nv_show_name_user($customer['first_name'], $customer['last_name'], $customer['username']);
        }
    }
    if (empty($data_return['username']) || empty($data_return['fullname'])) {
        $_error = 'Không lấy được thông tin khách hàng';
    }

    if (empty($_error)) {
        // Gọi API: Lấy danh sách đơn hàng của userid
        // Lấy thông tin đơn hàng
        $where = [];
        $where['AND'] = [
            ['=' => ['userid' => $userid]]
        ];
        $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
        $api_dtinfo->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingOrder')
            ->setData([
                'where' => $where,
                'show_customs_log' => 1
            ]);
        $result = $api_dtinfo->execute();
        $_error = $api_dtinfo->getError();
        if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
            // Xử lý HTML show vào modal chọn đơn hàng
            $xtpl = new XTemplate('econtract_content.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
            $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
            foreach ($result['data'] as $_order) {
                $_order['code'] = sprintf('BDH%010s', $_order['id']);

                // Gói VIP
                $_vips = [];
                if (!empty($_order['customs_log'])) {
                    foreach ($_order['customs_log'] as $custom_vip) {
                        $_vips[] = $global_arr_vip[$custom_vip['vip']] . ($custom_vip['prefix_lang'] == 0 ? ' (Tiếng Việt)' : ' (Tiếng Anh)');
                    }
                }
                $_order['vips_title'] = !empty($_vips) ? implode(',<br>', $_vips) : '';

                // Thông tin chi phí
                $_order['money'] = number_format($_order['money']) . ' đ';
                $_order['discount'] = number_format($_order['discount']) . ' đ';
                $_order['total'] = number_format($_order['total']) . ' đ';
                $_order['price_reduce'] = number_format($_order['price_reduce']) . ' đ';
                $_order['total_end'] = number_format($_order['total_end'] ?: 0) . ' đ';

                // Kiểm tra đơn hàng có HĐ chưa, lấy mã HĐ
                $_order['contract_no'] = $db->query('SELECT t2.contract_no FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders t1 RIGHT JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts t2 ON t1.econtract_id=t2.id WHERE order_id=' . $_order['id'])->fetchColumn() ?: '';

                $xtpl->assign('ORDER', $_order);
                $xtpl->parse('table_orders.loop_item');
            }
            $xtpl->parse('table_orders');
            $data_return['modal_html'] = $xtpl->text('table_orders');
            nv_jsonOutput([
                'status' => true,
                'data' => $data_return
            ]);
        } else {
            $_error = 'Không lấy được danh sách đơn hàng của khách hàng: ' . $data_return['fullname'] . ' (' . $data_return['username'] . ')';
        }
    }

    nv_jsonOutput([
        'status' => false,
        'message' => $_error
    ]);
}

$error = '';
// Khởi tạo $econtract mặc định rỗng (tạo mới)
$econtract = [
    'current_version' => 0,
    'tax_code' => '',
    'c_name' => '',
    'representative' => '',
    'jobtitle' => '',
    'c_address' => '',
    'phone' => '',
    'email' => '',
    'authorization_letter' => '',
    'receiver' => '',
    'receiver_phone' => '',
    'receiver_address' => '',
    'cccd' => '',
    'customer_type' => 1,
    'contract_no' => '',
    'contract_path' => '',
    'status' => 0,
    'term_changed' => 0,
    'term_changed_notes' => '',
    'stage' => 0,
    'stage_next' => 1,
    'uploader_id' => 0,
    'customer_id' => 0,
    'created_at' => 0,
    'updated_at' => 0,
    'type_econtract' => 0, // Mặc định hợp đồng là bản dài
];

$versions = $logs = $orders = $order_data = [];
$version_id = 0;

// Nếu sửa thì phải có id hợp đồng
$id = $nv_Request->get_int('id', 'get,post', 0);
$order_ids = $old_order_ids = $new_order_ids = $data_orders = $order2dels = [];

// Bổ sung thông tin hợp đồng
if (!empty($id)) {
    // Kiểm tra hợp đồng: Nếu trạng thái đã hoàn thành hoặc hủy thì không cho update
    $status_check = implode(',', [EContractStatus::Cancel->value]);
    $econtract = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $id . ' AND status NOT IN (' . $status_check . ')')->fetch();
    if (!$econtract) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract');
    }

    $is_manage_econtract = isset($my_admin_config['manage_econtract']) && ($my_admin_config['manage_econtract'] == 1); // Phân quyền: Quản lý TẤT CẢ hợp đồng

    if (!isset($my_managed_users[$econtract['uploader_id']]) && !defined('NV_IS_SPADMIN') && !$is_manage_econtract) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_draft');
    }

    $old_econtract = $econtract;
    $page_title = 'Bổ sung thông tin hợp đồng: ' . $econtract['contract_no'];

    // Lấy danh sách phiên bản hợp đồng
    $versions = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions WHERE econtract_id=' . $id . ' ORDER BY id DESC')->fetchAll();

    // Phiên bản hiện tại của hợp đồng
    $version_id = $nv_Request->get_int('version', 'get', $econtract['current_version']);
    if ($version_id) {
        $econtract['version_data'] = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions WHERE id=' . $version_id)->fetch();
        if ($econtract['version_data']) {
            $econtract['version_data']['code'] = str_pad($econtract['version_data']['version'] ?? 0, 2, '0', STR_PAD_LEFT);
        }
    }

    // Lấy danh sách nhật ký hoạt động của hợp đồng
    $logs = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_logs WHERE econtract_id=' . $id . ' ORDER BY created_at DESC')->fetchAll();

    // Lấy danh sách đơn hàng đang gắn vào hợp đồng
    $orders = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id=' . $id)->fetchAll();

    if (!empty($orders)) {
        foreach ($orders as $order) {
            if (empty($econtract['customer_id'])) {
                $econtract['customer_id'] = $order['user_id'];
            }
            $order_ids[] = $order['order_id'];
            $old_order_ids[$order['order_id']] = $order['order_id'];
        }
        $order_ids = array_unique($order_ids);
    }

    if (!empty($order_ids)) {
        $order_data = [];
        // Lấy thông tin đơn hàng
        $where = [];
        $where['AND'] = [
            ['IN' => ['id' => '(' . implode(',', $order_ids) . ')']]
        ];
        $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
        $api_dtinfo->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingOrder')
            ->setData([
                'where' => $where,
                'show_customs_log' => 1
            ]);
        $result = $api_dtinfo->execute();
        $error = $api_dtinfo->getError();
        if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
            $order_data = end($result['data']);
        }
        if (empty($order_data)) {
            nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract');
        }
        $order_data['code'] = sprintf('BDH%010s', $order_data['id']);
        $order_data['url_detail'] = URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $order_data['id'];
        $econtract['customer_id'] = $order_data['userid'];
        $econtract['uploader_id'] = $order_data['caregiver_id']; // Lưu id của người tạo hợp đồng chính là người chăm sóc đơn hàng

        // Hiển thị các thông tin sidebar bên phải
        $econtract['sale_name'] = 'N/A';
        if (isset($all_array_user_id_users[$order_data['caregiver_id']])) {
            $uploader = $all_array_user_id_users[$order_data['caregiver_id']];
            $econtract['sale_name'] = nv_show_name_user($uploader['first_name'], $uploader['last_name'], $uploader['userid']);
        }

        $econtract['username'] = $econtract['fullname'] = 'N/A';
        if ($econtract['customer_id']) {
            $customer = $db->query('SELECT userid, username, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $econtract['customer_id'])->fetch() ?: null;
            if ($customer) {
                $econtract['username'] = $customer['username'];
                $econtract['fullname'] = nv_show_name_user($customer['first_name'], $customer['last_name'], $customer['username']);
            }
        }
    }
} else {
    $page_title = 'Tạo hợp đồng mới';
    // Tạo mới từ đơn hàng
    $from_order_id = $nv_Request->get_int('order_id', 'get', 0);
    if (!empty($from_order_id)) {
        $order_ids[] = $from_order_id;
        // Kiểm tra nếu đơn hàng này đã có hợp đồng rồi thì chuyển hướng đến chi tiết của hợp đồng
        $check_econtract_id = $db->query('SELECT econtract_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE order_id=' . $from_order_id)->fetchColumn();
        if ($check_econtract_id) {
            nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $check_econtract_id);
        }

        // Lấy thông tin đơn hàng
        $where = [];
        $where['AND'] = [
            ['=' => ['id' => $from_order_id]]
        ];
        $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
        $api_dtinfo->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingOrder')
            ->setData([
                'where' => $where,
                'show_customs_log' => 1
            ]);
        $result = $api_dtinfo->execute();
        $error = $api_dtinfo->getError();
        if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
            if (isset($result['data'][$from_order_id])) {
                $econtract['customer_id'] = $result['data'][$from_order_id]['userid'];
            }
        }
    }
}

// Cache file
$cache_file = 'econtract_' . $id . '_' . NV_LANG_DATA . '.cache';

/**
 * Nếu đã có file cache rồi thì load dữ liệu econtract từ file cache
 */
$cache_data = $nv_Cache->getItem($module_name, $cache_file);
if ($cache_data != false) {
    $row_new = json_decode($cache_data, true);
    // Cập nhật dữ liệu econtract theo file cache
    $econtract['tax_code'] = $row_new['tax_code'];
    $econtract['c_name'] = $row_new['c_name'];
    $econtract['representative'] = $row_new['representative'];
    $econtract['jobtitle'] = $row_new['jobtitle'];
    $econtract['c_address'] = $row_new['c_address'];
    $econtract['phone'] = $row_new['phone'];
    $econtract['email'] = $row_new['email'];
    $econtract['authorization_letter'] = $row_new['authorization_letter'];
    $econtract['receiver'] = $row_new['receiver'];
    $econtract['receiver_phone'] = $row_new['receiver_phone'];
    $econtract['receiver_address'] = $row_new['receiver_address'];
    $econtract['cccd'] = $row_new['cccd'];
    $econtract['customer_type'] = $row_new['customer_type'];
    $econtract['contract_no'] = $row_new['contract_no'];
    $econtract['contract_path'] = $row_new['contract_path'];
    $econtract['status'] = $row_new['status'];
    $econtract['term_changed'] = $row_new['term_changed'];
    $econtract['term_changed_notes'] = $row_new['term_changed_notes'];
    $econtract['stage'] = $row_new['stage'];
    $econtract['stage_next'] = $row_new['stage_next'];
    $econtract['uploader_id'] = $row_new['uploader_id'];
    $econtract['customer_id'] = $row_new['customer_id'];
    $econtract['created_at'] = $row_new['created_at'];
    $econtract['updated_at'] = $row_new['updated_at'];
    $econtract['contract_data'] = $row_new['contract_data'];
    $econtract['current_version'] = $row_new['current_version'];
    $econtract['bank_account'] = $row_new['current_version'] ?? '';
}

// Bắt đầu xử lý lưu hợp đồng (xử lý cho cả tạo mới và bổ sung thông tin)
if ($nv_Request->isset_request('save', 'post')) {
    // 1. Xử lý dữ liệu
    $order_ids = $nv_Request->get_typed_array('order_ids', 'post', 'int');
    $econtract['customer_type'] = $nv_Request->isset_request('customer_type', 'post') ? 0 : 1;
    $econtract['authorization_letter'] = $nv_Request->get_title('authorization_letter', 'post', '');
    $econtract['c_name'] = $nv_Request->get_title('c_name', 'post', '');
    $econtract['tax_code'] = $nv_Request->get_title('tax_code', 'post', '');
    $econtract['cccd'] = $nv_Request->get_title('cccd', 'post', '');
    $econtract['representative'] = $nv_Request->get_title('representative', 'post', '');
    $econtract['jobtitle'] = $nv_Request->get_title('jobtitle', 'post', '');
    $econtract['phone'] = $nv_Request->get_title('phone', 'post', '');
    $econtract['email'] = $nv_Request->get_title('email', 'post', '');
    $econtract['receiver'] = $nv_Request->get_title('receiver', 'post', '');
    $econtract['receiver_phone'] = $nv_Request->get_title('receiver_phone', 'post', '');
    $econtract['receiver_address'] = $nv_Request->get_title('receiver_address', 'post', '');
    $econtract['c_address'] = $nv_Request->get_title('c_address', 'post', '');
    $econtract['customer_id'] = $nv_Request->get_int('customer_id', 'post', 0);
    $econtract['term_changed'] = $nv_Request->isset_request('term_changed', 'post') ? 1 : 0;
    $econtract['term_changed_notes'] = $nv_Request->get_textarea('term_changed_notes', '', '');
    $econtract['bank_account'] = $nv_Request->get_title('bank_account', 'post', '');

    if (empty($id)) {
        $econtract['created_at'] = NV_CURRENTTIME;
    }
    $econtract['updated_at'] = NV_CURRENTTIME;
    $_vips_vi = $_vips_en = $data_vip = [];
    $_total = $_discount = $_total_end = 0;

    if ($econtract['customer_type'] == 1 && !empty($econtract['tax_code']) && !taxcodecheck2($econtract['tax_code'])) {
        $error = 'Lỗi: <strong>MST</strong> không hợp lệ';
    } elseif ($econtract['customer_type'] == 0 && !empty($econtract['cccd']) && !cccdCheck($econtract['cccd'])) {
        $error = 'Lỗi: <strong>CCCD</strong> không hợp lệ';
    } elseif (!empty($econtract['phone']) && !phonecheck($econtract['phone'])) {
        $error = 'Lỗi: <strong>Số điện thoại</strong> không hợp lệ';
    } elseif (!empty($econtract['receiver_phone']) && !phonecheck($econtract['receiver_phone'])) {
        $error = 'Lỗi: <strong>Số điện thoại người nhận</strong> không hợp lệ';
    } elseif (!empty($econtract['email']) && !filter_var($econtract['email'], FILTER_VALIDATE_EMAIL)) {
        $error = 'Lỗi: <strong>Email</strong> không hợp lệ';
    }

    // chuyển dữ liệu trong mảng unicode tổ hợp -> unicode dững sắn
    $econtract = nv_compound_unicode_recursion($econtract);

    if (!empty($order_ids)) {
        // Lấy thông tin từ đơn hàng
        $where = [];
        $where['AND'] = [
            ['IN' => ['id' => '(' . implode(',', $order_ids) . ')']]
        ];
        $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
        $api_dtinfo->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingOrder')
            ->setData([
                'where' => $where,
                'show_customs_log' => 1
            ]);
        $result = $api_dtinfo->execute();
        if (!empty($api_dtinfo->getError())) {
            $error = $api_dtinfo->getError();
        }
        if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
            foreach ($result['data'] as $_order) {
                if (isset($all_array_user_id_users[$_order['caregiver_id']])) {
                    $econtract['uploader_id'] = $_order['caregiver_id'];
                }
                $_total += $_order['money'];
                $_discount += $_order['discount'];
                $_total_end += $_order['total'];

                $_order['vips_vi'] = $_order['vips_en'] = [];
                if (!empty($_order['customs_log'])) {
                    foreach ($_order['customs_log'] as $custom_vip) {
                        $data_vip[] = [$custom_vip['vip_name'], $custom_vip['numbers_year']];
                        if ($custom_vip['prefix_lang'] == 0) {
                            $_vips_vi[] = $custom_vip['vip'];
                            $_order['vips_vi'][] = $custom_vip['vip'];
                        } else {
                            $_vips_en[] = $custom_vip['vip'];
                            $_order['vips_en'][] = $custom_vip['vip'];
                        }
                    }
                }
                $_order['username'] = $db->query('SELECT username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $_order['userid'])->fetchColumn() ?: '';

                // Xử lý và lưu các thông tin liên quan đến đơn hàng cần sử dụng bên dưới
                $data_orders[$_order['id']] = [
                    'order_id' => $_order['id'],
                    'user_id' => $_order['userid'],
                    'username' => $_order['username'],
                    'vips_vi' => $_order['vips_vi'],
                    'vips_en' => $_order['vips_en']
                ];
            }
        }
    }

    $econtract['uploader_id'] = $econtract['uploader_id'] ?: $admin_info['userid'];
    $econtract['vips_vi'] = implode(',', $_vips_vi);
    $econtract['vips_en'] = implode(',', $_vips_en);
    $econtract['contract_data'] = json_encode([
        'data_vip' => convert_data_vip($data_vip),
        'content' => '',
        'total_service' => $_total,
        'promotion' => $_discount,
        'total_payment' => $_total_end,
    ]);

    if ($econtract['customer_id']) {
        $customer = $db->query('SELECT userid, username, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $econtract['customer_id'])->fetch() ?: null;
        if ($customer) {
            $econtract['username'] = $customer['username'];
            $econtract['fullname'] = nv_show_name_user($customer['first_name'], $customer['last_name'], $customer['username']);
        }
    }

    //Log cập nhật
    $info_admin_action_log = $nv_Lang->getModule('$info_admin_action_log');
    $admin_action_log = $db->query('SELECT userid, username, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $admin_info['userid'])->fetch() ?: null;
    if ($admin_action_log) {
        $info_admin_action_log = nv_show_name_user($admin_action_log['first_name'], $admin_action_log['last_name'], $admin_action_log['username']);
    }
    $log_action_vi = '<strong>' . $info_admin_action_log . '</strong> Cập nhật thông tin hợp đồng <strong>' . $econtract['contract_no'] . '</strong>';
    $log_action_en = '<strong>' . $info_admin_action_log . '</strong> Update econtract information <strong>' . $econtract['contract_no'] . '</strong>';

    /**
     * Trường hợp đã ký rồi nhưng có thay đổi thông tin thì rest lại
     * - Cập nhật contract_path về default
     * - Cập nhật status, stage và stage_next
     */
    $econtract['contract_path'] = '';
    if (!in_array($econtract['status'], [EContractStatus::Deleted->value, EContractStatus::Done->value, EContractStatus::Cancel->value])) {
        $econtract['status'] = EContractStatus::Incomplete->value;
        $econtract['stage'] = EContractStage::SupplementingInfo->value;
        $econtract['stage_next'] = EContractStage::Negotiating->value;
    }
    if ($econtract['term_changed']) {
        if (empty($econtract['term_changed_notes'])) {
            $error = 'Lỗi: Chưa nhập ghi chú thay đổi điều khoản hợp đồng';
        } elseif (empty($_FILES['contract_path']['tmp_name']) && empty($econtract['contract_path'])) {
            $error = 'Vui lòng chọn file hợp đồng đã thay đổi điều khoản để cập nhật';
        } elseif (!empty($_FILES['contract_path']['tmp_name'])) {
            // Đổi tên file để lưu
            $_FILES['contract_path']['name'] = $econtract['id'] . '-' . NV_CURRENTTIME . '-' . $_FILES['contract_path']['name'];
            // Khởi tạo thư mục upload
            if (!is_dir(NV_UPLOADS_REAL_DIR . '/econtracts/' . strtolower(change_alias($econtract['username'])))) {
                nv_mkdir(NV_UPLOADS_REAL_DIR . '/econtracts/', strtolower(change_alias($econtract['username'])));
            }
            $path_to_upload_contract = NV_UPLOADS_REAL_DIR . '/econtracts/' . strtolower(change_alias($econtract['username']));

            // Lưu tệp
            $upload = new NukeViet\Files\Upload();
            $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
            // Lưu file hợp đồng
            $upload_contract_info = $upload->save_file($_FILES['contract_path'], $path_to_upload_contract, false, $global_config['nv_auto_resize']);

            if (!empty($upload_contract_info['error'])) {
                $error = $upload_contract_info['error'];
            } elseif (!in_array($upload_contract_info['ext'], ['pdf', 'zip'])) {
                $error = $nv_Lang->getModule('error_file_type');
            }

            if (empty($error)) {
                $econtract['contract_path'] = NV_UPLOADS_DIR . '/econtracts/' . strtolower(change_alias($econtract['username'])) . '/' . $upload_contract_info['basename'];
                $log_action_vi = '<strong>' . $info_admin_action_log . '</strong> đã tải file lên thay thế hợp đồng <strong>' . $econtract['contract_no'] . '</strong>';
                $log_action_en = '<strong>' . $info_admin_action_log . '</strong> has uploaded a file to replace the contract <strong>' . $econtract['contract_no'] . '</strong>';
            }
        }
    }

    if (empty($id)) {
        // Mã hợp đồng: contract_no
        $econtract_next_no = str_pad($data_config['econtract_next_no'] ?? 1, 2, '0', STR_PAD_LEFT);
        $econtract['contract_no'] = $econtract_next_no . '/HĐ' . date('Y') . '/DAUTHAU.INFO';
        // Kiểm tra xem hợp đồng đã tồn tại hay chưa
        $check_contract_no = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE contract_no=' . $db->quote($econtract['contract_no']))->fetchColumn();
        if ($check_contract_no) {
            $econtract_next_no++;
            $econtract['contract_no'] = str_pad($econtract_next_no, 2, '0', STR_PAD_LEFT) . '/HĐ' . date('Y') . '/DAUTHAU.INFO';
        }

        // Mã đề nghị thanh toán: payment_proposal_no
        $payment_proposal_number = str_pad($data_config['econtract_payment_proposal'] ?? 1, 2, '0', STR_PAD_LEFT);
        $econtract['payment_proposal_no'] = $payment_proposal_number . '/DNTT' . date('Y') . '/DAUTHAU.INFO';
        // Kiểm tra xem hợp đồng đã tồn tại hay chưa
        $payment_proposal_no = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE payment_proposal_no=' . $db->quote($econtract['payment_proposal_no']))->fetchColumn();
        if ($payment_proposal_no) {
            $payment_proposal_number++;
            $econtract['payment_proposal_no'] = str_pad($payment_proposal_number, 2, '0', STR_PAD_LEFT) . '/DNTT' . date('Y') . '/DAUTHAU.INFO';
        }
    }

    // Kiểm tra các thông tin có dấu * để cập nhật trạng thái, giai đoạn cho đúng. Chỉ kiểm tra khi chưa bên nào ký HĐ (status < 2)
    if ($econtract['status'] < EContractStatus::HSTDTSigned->value) {
        $flag_done_required = false;
        if (
            ($econtract['customer_type'] == 1 && !empty($econtract['tax_code']) && !empty($econtract['c_name']) && !empty($econtract['representative']) && !empty($econtract['jobtitle']) && !empty($econtract['phone']) && !empty($econtract['email']) && !empty($econtract['c_address'])) ||
            ($econtract['customer_type'] == 0 && !empty($econtract['c_name']) && !empty($econtract['cccd']) && !empty($econtract['phone']) && !empty($econtract['email']) && !empty($econtract['c_address']))
        ) {
            $flag_done_required = true;
        }

        if ($flag_done_required) {
            $econtract['status'] = EContractStatus::Incomplete->value;
            $econtract['stage'] = EContractStage::Negotiating->value;
            $econtract['stage_next'] = EContractStage::HSTDTSignatureRequired->value;
        } else {
            $econtract['status'] = EContractStatus::Incomplete->value;
            $econtract['stage'] = EContractStage::SupplementingInfo->value;
            $econtract['stage_next'] = EContractStage::Negotiating->value;
        }
    }

    // 2. Lưu hợp đồng: econtracts
    if (empty($error)) {
        try {
            $changed_econtract_orders = []; // Lưu danh sách id đơn hàng nếu trước đó nằm trong 1 hợp đồng khác, được gộp vào hợp đồng này. Mục đích: ghi log đơn hàng cũ biết được gộp hợp đồng. Issue: https://vinades.org/dauthau/dauthau.info/-/work_items/3045

            $db->beginTransaction();
            if (!empty($id)) {
                // Cập nhật hợp đồng
                $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET tax_code=:tax_code, c_name=:c_name, representative=:representative, jobtitle=:jobtitle, c_address=:c_address, phone=:phone, email=:email, authorization_letter=:authorization_letter, receiver=:receiver, receiver_phone=:receiver_phone, receiver_address=:receiver_address, cccd=:cccd, customer_type=:customer_type, contract_path=:contract_path, status=:status, stage=:stage, stage_next=:stage_next, uploader_id=:uploader_id, customer_id=:customer_id, term_changed=:term_changed, term_changed_notes=:term_changed_notes, updated_at=:updated_at, vips_vi=:vips_vi, vips_en=:vips_en, contract_data=:contract_data, bank_account=:bank_account WHERE id=' . $id);
            } else {
                // Tạo mới hợp đồng
                $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts (
                    tax_code, c_name, representative, jobtitle, c_address, phone, email, authorization_letter, receiver, receiver_phone, receiver_address, cccd, customer_type, contract_no, contract_path, status, stage, stage_next, uploader_id, customer_id, term_changed, term_changed_notes, created_at, updated_at, vips_vi, vips_en, contract_data, payment_proposal_no, bank_account)
                VALUES (:tax_code, :c_name, :representative, :jobtitle, :c_address, :phone, :email, :authorization_letter, :receiver, :receiver_phone, :receiver_address, :cccd, :customer_type, :contract_no, :contract_path, :status, :stage, :stage_next, :uploader_id, :customer_id, :term_changed, :term_changed_notes, :created_at, :updated_at, :vips_vi, :vips_en, :contract_data, :payment_proposal_no, :bank_account)');
                $stmt->bindParam(':contract_no', $econtract['contract_no'], PDO::PARAM_STR);
                $stmt->bindParam(':payment_proposal_no', $econtract['payment_proposal_no'], PDO::PARAM_STR);
                $stmt->bindParam(':created_at', $econtract['created_at'], PDO::PARAM_INT);
            }
            // Bind data
            $stmt->bindParam(':tax_code', $econtract['tax_code'], PDO::PARAM_STR);
            $stmt->bindParam(':c_name', $econtract['c_name'], PDO::PARAM_STR);
            $stmt->bindParam(':representative', $econtract['representative'], PDO::PARAM_STR);
            $stmt->bindParam(':jobtitle', $econtract['jobtitle'], PDO::PARAM_STR);
            $stmt->bindParam(':c_address', $econtract['c_address'], PDO::PARAM_STR);
            $stmt->bindParam(':phone', $econtract['phone'], PDO::PARAM_STR);
            $stmt->bindParam(':email', $econtract['email'], PDO::PARAM_STR);
            $stmt->bindParam(':authorization_letter', $econtract['authorization_letter'], PDO::PARAM_STR);
            $stmt->bindParam(':receiver', $econtract['receiver'], PDO::PARAM_STR);
            $stmt->bindParam(':receiver_phone', $econtract['receiver_phone'], PDO::PARAM_STR);
            $stmt->bindParam(':receiver_address', $econtract['receiver_address'], PDO::PARAM_STR);
            $stmt->bindParam(':cccd', $econtract['cccd'], PDO::PARAM_STR);
            $stmt->bindParam(':customer_type', $econtract['customer_type'], PDO::PARAM_INT);
            $stmt->bindParam(':contract_path', $econtract['contract_path'], PDO::PARAM_STR);
            $stmt->bindParam(':status', $econtract['status'], PDO::PARAM_INT);
            $stmt->bindParam(':stage', $econtract['stage'], PDO::PARAM_INT);
            $stmt->bindParam(':stage_next', $econtract['stage_next'], PDO::PARAM_INT);
            $stmt->bindParam(':uploader_id', $econtract['uploader_id'], PDO::PARAM_INT);
            $stmt->bindParam(':customer_id', $econtract['customer_id'], PDO::PARAM_INT);
            $stmt->bindParam(':term_changed', $econtract['term_changed'], PDO::PARAM_INT);
            $stmt->bindParam(':term_changed_notes', $econtract['term_changed_notes'], PDO::PARAM_STR);
            $stmt->bindParam(':updated_at', $econtract['updated_at'], PDO::PARAM_INT);
            $stmt->bindParam(':vips_vi', $econtract['vips_vi'], PDO::PARAM_STR);
            $stmt->bindParam(':vips_en', $econtract['vips_en'], PDO::PARAM_STR);
            $stmt->bindParam(':contract_data', $econtract['contract_data'], PDO::PARAM_STR);
            $stmt->bindParam(':bank_account', $econtract['bank_account'], PDO::PARAM_STR);
            $stmt->execute();

            if (empty($id)) {
                $econtract['id'] = $db->lastInsertId();
                // Cập nhật lại config: $module_config[$module_name]['econtract_next_no'] => xóa cache
                $econtract_next_no++;
                $payment_proposal_number++;
                $sql = '
                    UPDATE ' . NV_CONFIG_GLOBALTABLE . '
                    SET
                        config_value = CASE
                            WHEN config_name = ' . $db->quote('econtract_next_no') . ' THEN ' . $econtract_next_no . '
                            WHEN config_name = ' . $db->quote('econtract_payment_proposal') . ' THEN ' . $payment_proposal_number . '
                        END
                    WHERE lang=' . $db->quote(NV_LANG_DATA) . '
                    AND module=' . $db->quote($module_name) . '
                    AND config_name IN (' . $db->quote('econtract_next_no') . ', ' . $db->quote('econtract_payment_proposal') . ')';
                $db->query($sql);
                $nv_Cache->delMod($module_name);
            }

            // 3. Lưu đơn hàng: econtract_orders
            if (!empty($order_ids)) {
                foreach ($order_ids as $_order_id) {
                    if (!isset($old_order_ids[$_order_id])) {
                        $old_econtract_id = $db->query('SELECT econtract_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE order_id=' . $_order_id)->fetchColumn();
                        if ($old_econtract_id) {
                            $changed_econtract_orders[$_order_id] = $old_econtract_id;
                            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders SET econtract_id=' . $econtract['id'] . ' WHERE order_id=' . $_order_id);
                        } else {
                            $db->query('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders (
                                econtract_id, order_id, site_id, user_id, username, vips_vi, vips_en
                            ) VALUES (
                                ' . $econtract['id'] . ',
                                ' . $_order_id . ',
                                1,
                                ' . ($data_orders[$_order_id]['user_id'] ?? 0) . ',
                                ' . $db->quote($data_orders[$_order_id]['username'] ?? '') . ',
                                ' . $db->quote(implode(',', $data_orders[$_order_id]['vips_vi'] ?? [])) . ',
                                ' . $db->quote(implode(',', $data_orders[$_order_id]['vips_en'] ?? [])) . '
                            )');
                        }
                        $new_order_ids[] = $_order_id;
                    } else {
                        unset($old_order_ids[$_order_id]);
                    }
                }

                $order2dels = array_values($old_order_ids);
                if (!empty($order2dels)) {
                    $db->query('DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE order_id IN (' . implode(',', $order2dels) . ')');
                }
            }

            // 4. Tạo phiên bản hợp đồng: econtract_versions => update current_version trong econtracts
            $changed_data = [];
            if (!empty($id)) {
                // Kiểm tra nếu các trường sau bị thay đổi thì mới tạo phiên bản mới: tax_code, c_name, representative, jobtitle, c_address, phone, email, authorization_letter, cccd, customer_type, contract_path, term_changed, term_changed_notes
                $changed_fields = ['tax_code', 'c_name', 'representative', 'jobtitle', 'c_address', 'phone', 'email', 'authorization_letter', 'cccd', 'customer_type', 'contract_path', 'term_changed', 'term_changed_notes', 'bank_account'];
                foreach ($changed_fields as $field) {
                    if ($econtract[$field] != $old_econtract[$field]) {
                        $changed_data[$field] = [
                            'old' => $old_econtract[$field],
                            'new' => $econtract[$field]
                        ];
                    }
                }
                if (!empty($changed_data)) {
                    $max_code_ver = $db->query('SELECT MAX(version) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions WHERE econtract_id=' . $id)->fetchColumn() ?: 0;
                    $version_id = $db->insert_id('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions (
                    econtract_id, user_id, version, pdf_path, contract_data, created_at
                ) VALUES (
                    ' . $id . ',
                    ' . $admin_info['userid'] . ',
                    ' . ($max_code_ver + 1) . ',
                    ' . $db->quote($econtract['contract_path']) . ',
                    ' . $db->quote(json_encode($econtract)) . ',
                    ' . NV_CURRENTTIME . '
                )', 'id');
                    $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET current_version=' . $version_id . ' WHERE id=' . $id);
                } else {
                    // Nếu không thì cập nhật contract_data vào version hiện tại
                    $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions SET pdf_path=' . $db->quote($econtract['contract_path']) . ', contract_data=' . $db->quote(json_encode($econtract)) . ' WHERE id=' . $econtract['current_version']);
                }

                // Tạo log hợp đồng: econtract_logs
                $db->query('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_logs (econtract_id, version_id, action, user_id, action_desc_vi, action_desc_en, changed_data, log_visible, created_at) VALUES (' . $id . ', ' . $version_id . ', 1, ' . $admin_info['userid'] . ', ' . $db->quote($log_action_vi) . ', ' . $db->quote($log_action_en) . ',' . $db->quote(json_encode($changed_data)) . ', 1, ' . NV_CURRENTTIME . ')');
            } else {
                $version_id = $db->insert_id('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions (
                    econtract_id, user_id, version, pdf_path, contract_data, created_at
                ) VALUES (
                    ' . $econtract['id'] . ',
                    ' . $admin_info['userid'] . ',
                    0,
                    ' . $db->quote('') . ',
                    ' . $db->quote(json_encode($econtract)) . ',
                    ' . NV_CURRENTTIME . '
                )', 'id');
                $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET current_version=' . $version_id . ' WHERE id=' . $econtract['id']);

                // Tạo log hợp đồng: econtract_logs
                $db->query('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_logs (econtract_id, version_id, action, user_id, action_desc_vi, action_desc_en, log_visible, created_at) VALUES (' . $econtract['id'] . ', ' . $version_id . ', 0, ' . $admin_info['userid'] . ', ' . $db->quote('Tạo hợp đồng mới') . ', ' . $db->quote('Create new econtract') . ', 1, ' . NV_CURRENTTIME . ')');
            }
            $db->commit();
        } catch (\Throwable $th) {
            $error = 'Đã xảy ra lỗi: ' . $th->getMessage();
        }
        if (empty($error)) {
            if (empty($id)) {
                // Tạo log cho đơn hàng về thông tin hợp đồng mới tạo
                if (!empty($order_ids)) {
                    foreach ($order_ids as $_order_id) {
                        $logApi = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                        $logApi = $logApi->setModule('bidding')
                            ->setLang('vi')
                            ->setAction('CreateBiddingAllLogs')
                            ->setData([
                                'userid' => $admin_info['admin_id'],
                                'log_area' => 1,
                                'log_key' => 'LOG_CHANGE_ORDER_CONTRACT',
                                'log_time' => NV_CURRENTTIME,
                                'log_data' => [
                                    [$nv_Lang->getModule('log_update_econtract_success'), $nv_Lang->getModule('new')],
                                    [$nv_Lang->getModule('contract_no') . ': ', $econtract['contract_no']]
                                ],
                                'order_id' => $_order_id
                            ])->execute();

                        // Kiểm tra hợp đồng cũ của đơn hàng $_order_id để đánh dấu gộp hợp đồng
                        if (isset($changed_econtract_orders[$_order_id]) && !$db->query('SELECT COUNT(id) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id=' . $changed_econtract_orders[$_order_id])->fetchColumn()) {
                            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET merged_econtract_id=' . $econtract['id'] . ' WHERE id=' . $changed_econtract_orders[$_order_id]);

                            $db->query('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_logs (econtract_id, version_id, action, user_id, action_desc_vi, action_desc_en, log_visible, created_at) VALUES (' . $changed_econtract_orders[$_order_id] . ', 0, 0, ' . $admin_info['userid'] . ', ' . $db->quote('Gộp đơn hàng ' . sprintf('BDH%010s', $_order_id) . ' vào hợp đồng ' . $econtract['contract_no']) . ', ' . $db->quote('Merged order ' . sprintf('BDH%010s', $_order_id) . ' to econtract ' . $econtract['contract_no']) . ', 1, ' . NV_CURRENTTIME . ')');
                        }
                    }
                }
            } else {
                // Tạo log xóa hợp đồng cho các đơn hàng bỏ chọn: $order2dels
                if (!empty($order2dels)) {
                    foreach ($order2dels as $order2del) {
                        $logApi = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                        $logApi->setModule('bidding')
                            ->setLang('vi')
                            ->setAction('CreateBiddingAllLogs')
                            ->setData([
                                'userid' => $admin_info['admin_id'],
                                'log_area' => 1,
                                'log_key' => 'LOG_CHANGE_ORDER_CONTRACT',
                                'log_time' => NV_CURRENTTIME,
                                'log_data' => [
                                    [$nv_Lang->getModule('log_delete_econtract_success'), $econtract['contract_no']]
                                ],
                                'order_id' => $order2del
                            ])->execute();
                    }
                }
                // Tạo log hợp đồng mới cho các đơn hàng vừa chọn thêm: $new_order_ids
                if (!empty($new_order_ids)) {
                    foreach ($new_order_ids as $new_order_id) {
                        $logApi = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                        $logApi = $logApi->setModule('bidding')
                            ->setLang('vi')
                            ->setAction('CreateBiddingAllLogs')
                            ->setData([
                                'userid' => $admin_info['admin_id'],
                                'log_area' => 1,
                                'log_key' => 'LOG_CHANGE_ORDER_CONTRACT',
                                'log_time' => NV_CURRENTTIME,
                                'log_data' => [
                                    [$nv_Lang->getModule('log_update_econtract_success'), $nv_Lang->getModule('new')],
                                    [$nv_Lang->getModule('contract_no') . ': ', $econtract['contract_no']]
                                ],
                                'order_id' => $new_order_id
                            ])->execute();

                        // Kiểm tra hợp đồng cũ của đơn hàng $new_order_id để đánh dấu gộp hợp đồng
                        if (isset($changed_econtract_orders[$new_order_id]) && !$db->query('SELECT COUNT(id) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id=' . $changed_econtract_orders[$new_order_id])->fetchColumn()) {
                            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET merged_econtract_id=' . $econtract['id'] . ' WHERE id=' . $changed_econtract_orders[$new_order_id]);

                            $db->query('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_logs (econtract_id, version_id, action, user_id, action_desc_vi, action_desc_en, log_visible, created_at) VALUES (' . $changed_econtract_orders[$new_order_id] . ', 0, 0, ' . $admin_info['userid'] . ', ' . $db->quote('Gộp đơn hàng ' . sprintf('BDH%010s', $new_order_id) . ' vào hợp đồng ' . $econtract['contract_no']) . ', ' . $db->quote('Merged order ' . sprintf('BDH%010s', $new_order_id) . ' to econtract ' . $econtract['contract_no']) . ', 1, ' . NV_CURRENTTIME . ')');
                        }
                    }
                }
            }

            // Trường hợp nếu như đã ký hết rồi mà cập nhật (cập nhật thông tin, thay đổi điều khoản)
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET hstdt_signed=0, customer_signed=0 WHERE id=' . $econtract['id']);

            // Sau khi lưu thông tin thì sẽ xóa file cache-preview
            $nv_Cache->delItem($module_name, $cache_file);
            // Chuyển hướng về trang chi tiết hợp đồng
            nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $econtract['id']);
        }
    }
}

// Xử lý xem trước hợp đồng
if ($nv_Request->isset_request('previewEcontract', 'post')) {
    // 1. Xử lý dữ liệu
    $order_ids = $nv_Request->get_typed_array('order_ids', 'post', 'int');
    $econtract['customer_type'] = $nv_Request->isset_request('customer_type', 'post') ? 0 : 1;
    $econtract['authorization_letter'] = $nv_Request->get_title('authorization_letter', 'post', '');
    $econtract['c_name'] = $nv_Request->get_title('c_name', 'post', '');
    $econtract['tax_code'] = $nv_Request->get_title('tax_code', 'post', '');
    $econtract['cccd'] = $nv_Request->get_title('cccd', 'post', '');
    $econtract['representative'] = $nv_Request->get_title('representative', 'post', '');
    $econtract['jobtitle'] = $nv_Request->get_title('jobtitle', 'post', '');
    $econtract['phone'] = $nv_Request->get_title('phone', 'post', '');
    $econtract['email'] = $nv_Request->get_title('email', 'post', '');
    $econtract['receiver'] = $nv_Request->get_title('receiver', 'post', '');
    $econtract['receiver_phone'] = $nv_Request->get_title('receiver_phone', 'post', '');
    $econtract['receiver_address'] = $nv_Request->get_title('receiver_address', 'post', '');
    $econtract['c_address'] = $nv_Request->get_title('c_address', 'post', '');
    $econtract['customer_id'] = $nv_Request->get_int('customer_id', 'post', 0);
    $econtract['term_changed'] = $nv_Request->isset_request('term_changed', 'post') ? 1 : 0;
    $econtract['term_changed_notes'] = $nv_Request->get_textarea('term_changed_notes', '', '');
    $econtract['bank_account'] = $nv_Request->get_title('bank_account', 'post', '');

    if (empty($id)) {
        $econtract['created_at'] = NV_CURRENTTIME;
    }
    $econtract['updated_at'] = NV_CURRENTTIME;
    $_vips_vi = $_vips_en = $data_vip = [];
    $_total = $_discount = $_total_end = 0;

    if ($econtract['customer_type'] == 1 && !empty($econtract['tax_code']) && !taxcodecheck2($econtract['tax_code'])) {
        $error = 'Lỗi: <strong>MST</strong> không hợp lệ';
    } elseif ($econtract['customer_type'] == 0 && !empty($econtract['cccd']) && !cccdCheck($econtract['cccd'])) {
        $error = 'Lỗi: <strong>CCCD</strong> không hợp lệ';
    } elseif (!empty($econtract['phone']) && !phonecheck($econtract['phone'])) {
        $error = 'Lỗi: <strong>Số điện thoại</strong> không hợp lệ';
    } elseif (!empty($econtract['receiver_phone']) && !phonecheck($econtract['receiver_phone'])) {
        $error = 'Lỗi: <strong>Số điện thoại người nhận</strong> không hợp lệ';
    } elseif (!empty($econtract['email']) && !filter_var($econtract['email'], FILTER_VALIDATE_EMAIL)) {
        $error = 'Lỗi: <strong>Email</strong> không hợp lệ';
    }

    // chuyển dữ liệu trong mảng unicode tỏ hợp -> unicode dững sắn
    $econtract = nv_compound_unicode_recursion($econtract);


    if (!empty($order_ids)) {
        // Lấy thông tin từ đơn hàng
        $where = [];
        $where['AND'] = [
            ['IN' => ['id' => '(' . implode(',', $order_ids) . ')']]
        ];
        $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
        $api_dtinfo->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingOrder')
            ->setData([
                'where' => $where,
                'show_customs_log' => 1
            ]);
        $result = $api_dtinfo->execute();
        if (!empty($api_dtinfo->getError())) {
            $error = $api_dtinfo->getError();
        }
        if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
            foreach ($result['data'] as $_order) {
                if (isset($all_array_user_id_users[$_order['caregiver_id']])) {
                    $econtract['uploader_id'] = $_order['caregiver_id'];
                }
                $_total += $_order['money'];
                $_discount += $_order['discount'];
                $_total_end += $_order['total'];

                $_order['vips_vi'] = $_order['vips_en'] = [];
                if (!empty($_order['customs_log'])) {
                    foreach ($_order['customs_log'] as $custom_vip) {
                        $data_vip[] = [$custom_vip['vip_name'], $custom_vip['numbers_year']];
                        if ($custom_vip['prefix_lang'] == 0) {
                            $_vips_vi[] = $custom_vip['vip'];
                            $_order['vips_vi'][] = $custom_vip['vip'];
                        } else {
                            $_vips_en[] = $custom_vip['vip'];
                            $_order['vips_en'][] = $custom_vip['vip'];
                        }
                    }
                }
                $_order['username'] = $db->query('SELECT username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $_order['userid'])->fetchColumn() ?: '';

                // Xử lý và lưu các thông tin liên quan đến đơn hàng cần sử dụng bên dưới
                $data_orders[$_order['id']] = [
                    'order_id' => $_order['id'],
                    'user_id' => $_order['userid'],
                    'username' => $_order['username'],
                    'vips_vi' => $_order['vips_vi'],
                    'vips_en' => $_order['vips_en']
                ];
            }
        }
    }

    $econtract['uploader_id'] = $econtract['uploader_id'] ?: $admin_info['userid'];
    $econtract['vips_vi'] = implode(',', $_vips_vi);
    $econtract['vips_en'] = implode(',', $_vips_en);
    $econtract['contract_data'] = json_encode([
        'data_vip' => convert_data_vip($data_vip),
        'content' => '',
        'total_service' => $_total,
        'promotion' => $_discount,
        'total_payment' => $_total_end,
    ]);

    if ($econtract['customer_id']) {
        $customer = $db->query('SELECT userid, username, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $econtract['customer_id'])->fetch() ?: null;
        if ($customer) {
            $econtract['username'] = $customer['username'];
            $econtract['fullname'] = nv_show_name_user($customer['first_name'], $customer['last_name'], $customer['username']);
        }
    }

    if ($econtract['term_changed']) {
        if (empty($econtract['term_changed_notes'])) {
            $error = 'Lỗi: Chưa nhập ghi chú thay đổi điều khoản hợp đồng';
        } elseif (empty($_FILES['contract_path']['tmp_name']) && empty($econtract['contract_path'])) {
            $error = 'Vui lòng chọn file hợp đồng đã thay đổi điều khoản để cập nhật';
        } elseif (!empty($_FILES['contract_path']['tmp_name'])) {
            // Đổi tên file để lưu
            $_FILES['contract_path']['name'] = $econtract['id'] . '-' . NV_CURRENTTIME . '-' . $_FILES['contract_path']['name'];
            // Khởi tạo thư mục upload
            if (!is_dir(NV_UPLOADS_REAL_DIR . '/econtracts/' . strtolower(change_alias($econtract['username'])))) {
                nv_mkdir(NV_UPLOADS_REAL_DIR . '/econtracts/', strtolower(change_alias($econtract['username'])));
            }
            $path_to_upload_contract = NV_UPLOADS_REAL_DIR . '/econtracts/' . strtolower(change_alias($econtract['username']));

            // Lưu tệp
            $upload = new NukeViet\Files\Upload();
            $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
            // Lưu file hợp đồng
            $upload_contract_info = $upload->save_file($_FILES['contract_path'], $path_to_upload_contract, false, $global_config['nv_auto_resize']);

            if (!empty($upload_contract_info['error'])) {
                $error = $upload_contract_info['error'];
            } elseif (!in_array($upload_contract_info['ext'], ['pdf', 'zip'])) {
                $error = $nv_Lang->getModule('error_file_type');
            }

            if (empty($error)) {
                $econtract['contract_path'] = NV_UPLOADS_DIR . '/econtracts/' . strtolower(change_alias($econtract['username'])) . '/' . $upload_contract_info['basename'];
            }
        }
    }

    if (!empty($error)) {
        nv_jsonOutput([
            'status' => false,
            'message' => $error
        ]);
    }

    if (empty($id)) {
        // Mã hợp đồng: contract_no
        $econtract_next_no = $data_config['econtract_next_no'] ?? 1;
        $econtract['contract_no'] = $econtract_next_no . '/HĐ' . date('Y') . '/DAUTHAU.INFO';
        // Kiểm tra xem hợp đồng đã tồn tại hay chưa
        $check_contract_no = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE contract_no=' . $db->quote($econtract['contract_no']))->fetchColumn();
        if ($check_contract_no) {
            $econtract_next_no++;
            $econtract['contract_no'] = $econtract_next_no . '/HĐ' . date('Y') . '/DAUTHAU.INFO';
        }
    }

    // Kiểm tra các thông tin có dấu * để cập nhật trạng thái, giai đoạn cho đúng. Chỉ kiểm tra khi chưa bên nào ký HĐ (status < 2)
    if ($econtract['status'] < 2) {
        $flag_done_required = false;
        if (
            ($econtract['customer_type'] == 1 && !empty($econtract['tax_code']) && !empty($econtract['c_name']) && !empty($econtract['representative']) && !empty($econtract['jobtitle']) && !empty($econtract['phone']) && !empty($econtract['email']) && !empty($econtract['c_address'])) ||
            ($econtract['customer_type'] == 0 && !empty($econtract['c_name']) && !empty($econtract['cccd']) && !empty($econtract['phone']) && !empty($econtract['email']) && !empty($econtract['c_address']))
        ) {
            $flag_done_required = true;
        }

        if ($flag_done_required) {
            $econtract['status'] = 0;
            $econtract['stage'] = 0;
            $econtract['stage_next'] = 3;
        } else {
            $econtract['status'] = 1;
            $econtract['stage'] = 1;
            $econtract['stage_next'] = 0;
        }
    }
    $nv_Cache->setItem($module_name, $cache_file, json_encode($econtract, JSON_UNESCAPED_UNICODE), 3600);
    nv_jsonOutput([
        'status' => true,
        'redirect' => NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=preview&id=' . $id . '&version=' . $econtract['current_version'] . '&type_view=view_draft'
    ]);
}
$_last_back = $econtract['status'] != 5 ? '_draft' : '';

$xtpl = new XTemplate('econtract_content.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('TEMPLATE', $global_config['module_theme']);
$xtpl->assign('OP', $op);
$xtpl->assign('SUBMIT_TEXT', !empty($from_order_id) ? 'Tạo hợp đồng' : 'Lưu lại các thay đổi');
$xtpl->assign('DETAIL_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $id . '&version=' . $version_id);
$xtpl->assign('PREVIEW_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=preview&id=' . $id . '&version=' . $version_id);
$xtpl->assign('EDIT_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_content&id=' . $id . '&version=' . $version_id);
$xtpl->assign('DOWNLOAD_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=download&id=' . $id . '&version=' . $version_id);
$xtpl->assign('UPLOAD_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=upload&id=' . $id . '&version=' . $version_id);
$xtpl->assign('URL_LOAD_DATA_CUSTOMER', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_content&load_customer_data=1');
$xtpl->assign('BACK_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract' . $_last_back);
$xtpl->assign('TOKEN', NV_CHECK_SESSION);
$xtpl->assign('ARRAY_ORDER_IDS', isset($order_ids) && !empty($order_ids) && is_array($order_ids) ? implode(',', $order_ids) : '');
$xtpl->assign('MAX_FILE_ECONTRACT_SIZE', MAX_FILE_ECONTRACT_SIZE);

$xtpl->parse('main.order_inlist');
$xtpl->parse('main.no_order_inlist');

$econtract['updated_at'] = nv_datetime_format($econtract['updated_at']);
$econtract['sale_name'] = '';
if (isset($all_array_user_id_users[$econtract['uploader_id']])) {
    $uploader = $all_array_user_id_users[$econtract['uploader_id']];
    $econtract['sale_name'] = nv_show_name_user($uploader['first_name'], $uploader['last_name'], $uploader['userid']);
}
$econtract['status_label'] = EContractStatus::tryFrom($econtract['status'])?->getLabel() ?? '';
$econtract['stage_label'] = EContractStage::tryFrom($econtract['stage'])?->getLabel() ?? '';
if (!$econtract['stage'] && !$econtract['stage_next']) {
    $econtract['stage_next'] = 1;
}
$econtract['stage_next_label'] = EContractStage::tryFrom($econtract['stage_next'])?->getLabel() ?? '';
$econtract['customer_type_checked'] = $econtract['customer_type'] ? '' : 'checked="checked"';
$econtract['authorization_letter_checked'] = empty($econtract['authorization_letter']) ? '' : 'checked="checked"';
$econtract['term_changed_checked'] = $econtract['term_changed'] ? 'checked="checked"' : '';
$econtract['type_econtract'] = $nv_Lang->getModule('type_econtract_' . $econtract['type_econtract'] ?? 0);
$xtpl->assign('ECONTRACT', $econtract);

if (!empty($from_order_id)) {
    $xtpl->assign('ACTION_FORM_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_content&order_id=' . $from_order_id);
} elseif (!empty($id)) {
    $xtpl->assign('ACTION_FORM_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_content&id=' . $id . '&version=' . $version_id);

    $xtpl->parse('main.show_action_buttons');

    // Show các thông tin của hợp đồng ở sidebar phải
    if (!$version_id || $version_id == $econtract['current_version']) {
        $xtpl->parse('main.show_econtract_code.show_label_current_version');
    } else {
        $xtpl->parse('main.show_econtract_code.show_action_choose_version');
    }
    $xtpl->parse('main.show_econtract_code');

    // Hiển thị danh sách phiên bản
    if (!empty($versions)) {
        foreach ($versions as $version) {
            $version['is_current_class'] = $version['id'] == $econtract['current_version'] ? 'current-version' : '';
            $version['is_current_label'] = $version['id'] == $econtract['current_version'] ? '<span class="label label-success">Chính thức</span>' : '';
            $version['is_viewing_class'] = $version['id'] == $version_id ? 'viewing' : '';
            $version['is_viewing_label'] = $version['id'] == $version_id ? '<span class="label label-info">Đang xem</span>' : '';
            $version['created_at'] = nv_datetime_format($version['created_at']);
            $version['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $id . '&version=' . $version['id'];
            $version['code'] = $econtract['contract_no'] . '-' . str_pad($version['version'] ?? 0, 2, '0', STR_PAD_LEFT);
            $version['contract_data'] = json_decode($version['contract_data'], true);
            $version['status_label'] = '<strong>' . (EContractStatus::tryFrom($version['id'] == $econtract['current_version'] ? $econtract['status'] : $version['contract_data']['status'])?->getLabel() ?? '') . '</strong>';
            if ($version['contract_data']['term_changed']) {
                $version['status_label'] .= ' (Điều khoản hợp đồng đã thay đổi)';
            }
            // Link tệp HĐ đính kèm
            if (!empty($version['pdf_path']) && file_exists(NV_ROOTDIR . '/' . $version['pdf_path'])) {
                $version['link_file'] = NV_MY_DOMAIN . '/' . $version['pdf_path'];
            } else {
                $version['link_file'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=download&id=' . $id . '&version=' . $version['id'];
            }
            $xtpl->assign('VERSION', $version);
            $xtpl->parse('main.sidebar.show_version.loop_version');
        }
        $xtpl->parse('main.sidebar.show_version');
    }

    // Hiển thị danh sách nhật ký hoạt động
    if (!empty($logs)) {
        foreach ($logs as $log) {
            $log['fullname'] = 'Hệ thống';
            if ($log['user_id']) {
                $logger = $db->query('SELECT userid, username, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $log['user_id'])->fetch() ?: null;
                if ($logger) {
                    $log['fullname'] = nv_show_name_user($logger['first_name'], $logger['last_name'], $logger['username']);
                }
            }
            $log['action_desc'] = $log['action_desc_' . NV_LANG_INTERFACE] ?? '...';
            $log['created_at'] = nv_datetime_format($log['created_at']);
            $xtpl->assign('LOG', $log);
            if (!empty($log['changed_data'])) {
                $log['changed_data'] = str_replace('\"', '"', $log['changed_data']);
                $log['changed_data'] = trim($log['changed_data'], "'");
                $changed_datas = json_decode($log['changed_data'], true);
                foreach ($changed_datas as $field => $value_change) {
                    $xtpl->assign('CHANGED_KEY', '- ' . $nv_Lang->getModule($field));
                    if ($field == 'contract_path') {
                        $value_change['old'] = empty($value_change['old']) ? '' : basename($value_change['old']);
                        $value_change['new'] = empty($value_change['new']) ? '' : basename($value_change['new']);
                    }
                    $xtpl->assign('CHANGED_DATA', $value_change);
                    $xtpl->parse('main.sidebar.show_logs.loop_log.detail_log.loop');
                }
                $xtpl->parse('main.sidebar.show_logs.loop_log.detail_log');
                $xtpl->parse('main.sidebar.show_logs.loop_log.detail_log_label');
            }
            $xtpl->parse('main.sidebar.show_logs.loop_log');
        }
        $xtpl->parse('main.sidebar.show_logs');
    }
    if ($econtract['term_changed']) {
        $xtpl->parse('main.sidebar.show_label_term_changed');
    }
    $xtpl->parse('main.sidebar');
    $xtpl->parse('main.show_box_term_changed');
    $xtpl->parse('main.show_script_term_changed');
}
if (!empty($error)) {
    $xtpl->assign('ERROR', $error);
    $xtpl->parse('main.error');
}
$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
