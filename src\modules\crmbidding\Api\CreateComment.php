<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\crmbidding\Api;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

class CreateComment implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'comment';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $error = '';
        $error_code = '0000';

        $row['post_id'] = $nv_Request->get_absint('post_id', 'post', 0);
        $row['source'] = $nv_Request->get_absint('source', 'post', 0);
        $row['sourceid'] = $nv_Request->get_absint('sourceid', 'post', 0);
        $row['timecreate'] = $nv_Request->get_absint('timecreate', 'post', NV_CURRENTTIME);
        $row['update_time'] = $nv_Request->get_absint('update_time', 'post', 0);
        $row['note'] = $nv_Request->get_textarea('note', '', NV_ALLOWED_HTML_TAGS);
        $row['schedule'] = $nv_Request->get_absint('schedule', 'post', 0);

        // Kiểm tra tồn tại
        if ($row['source'] == 1) {
            // Lead
            $sql = "SELECT id FROM " . NV_PREFIXLANG . '_' . $module_data . "_leads WHERE id=" . $row['sourceid'];
        } else {
            // Cơ hội
            $sql = "SELECT id FROM " . NV_PREFIXLANG . '_' . $module_data . "_opportunities WHERE id=" . $row['sourceid'];
        }
        $is_exists = $db->query($sql)->fetchColumn();

        if ($row['post_id'] > 0 and !isset($array_user_id_users[$row['post_id']])) {
            $error_code = '2001';
            $error = $nv_Lang->getModule('sale_id_not_defind');
        } elseif ($row['source'] != 1 and $row['source'] != 2) {
            $error_code = '2002';
            $error = $nv_Lang->getModule('api_cmt_error_source');
        } elseif (!$is_exists) {
            $error_code = '2003';
            $error = $nv_Lang->getModule('api_cmt_error_source1');
        } elseif ($row['update_time'] > 0 and $row['update_time'] < $row['timecreate']) {
            $error_code = '2004';
            $error = $nv_Lang->getModule('api_cmt_error_uptime');
        } elseif (empty($row['note'])) {
            $error_code = '2005';
            $error = $nv_Lang->getModule('api_cmt_error_note');
        } elseif ($row['schedule'] > 0 and $row['schedule'] <= NV_CURRENTTIME) {
            $error_code = '2006';
            $error = $nv_Lang->getModule('api_cmt_error_schedule');
        }

        if (empty($error)) {
            try {
                $stmt = $db->prepare("INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_comment (
                    source, sourceid, post_id, timecreate, update_time, note, schedule
                ) VALUES (
                    :source, :sourceid, :post_id, :timecreate, :update_time, :note, :schedule
                )");
                $stmt->bindParam(':source', $row['source'], PDO::PARAM_INT);
                $stmt->bindParam(':sourceid', $row['sourceid'], PDO::PARAM_INT);
                $stmt->bindParam(':post_id', $row['post_id'], PDO::PARAM_STR);
                $stmt->bindParam(':timecreate', $row['timecreate'], PDO::PARAM_INT);
                $stmt->bindParam(':update_time', $row['update_time'], PDO::PARAM_INT);
                $stmt->bindParam(':note', $row['note'], PDO::PARAM_STR, strlen($row['note']));
                $stmt->bindParam(':schedule', $row['schedule'], PDO::PARAM_INT);

                $exc = $stmt->execute();
                $id = $db->lastInsertId();
                if ($exc and $id > 0) {
                    $this->result->setSuccess();
                    $this->result->set('commentid', $id);
                } else {
                    $this->result->setError()
                        ->setCode('3000')
                        ->setMessage('Can not creat comment');
                }
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode($error_code)
                ->setMessage($error)
                ->getResult();
        }
        return $this->result->getResult();
    }
}
