<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

if (!defined('SITE_ID_DOMAIN')) {
    define('SITE_ID_DOMAIN', 'https://id.dauthau.net');
}

use NukeViet\Api\DoApi;

if ($nv_Request->isset_request('sign_contract', 'post') == NV_CHECK_SESSION) {
    $econtract_id = $nv_Request->get_int('contract_id', 'post', 0);

    $econtract_leader_emails = $data_config['econtract_leader_emails'];
    if (empty($econtract_leader_emails)) {
        nv_jsonOutput([
            'status' => false,
            'message' => 'Chưa cấu hình mail lãnh đạo ký hợp đồng',
        ]);
    }

    // Kiểm tra hợp đồng
    $econtract = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $econtract_id)->fetch();
    if (empty($econtract)) {
        nv_jsonOutput([
            'status' => false,
            'message' => 'Hợp đồng không tồn tại',
        ]);
    }

    if ($econtract['status'] == EContractStatus::Done->value && $econtract['stage'] == EContractStage::Done->value) {
        nv_jsonOutput([
            'status' => false,
            'message' => 'Hợp đồng đã hoàn thành',
        ]);
    }

    if (empty($econtract['c_name'])) {
        nv_jsonOutput([
            'status' => false,
            'message' => 'Thiếu tên Công ty/Khách hàng',
        ]);
    }
    if ($econtract['customer_type'] == 1) {
        if (empty($econtract['tax_code'])) {
            nv_jsonOutput([
                'status' => false,
                'message' => 'Thiếu thông tin mã số thuế',
            ]);
        }
        if (empty($econtract['representative'])) {
            nv_jsonOutput([
                'status' => false,
                'message' => 'Thiếu thông tin người đại diện',
            ]);
        }
        if (empty($econtract['jobtitle'])) {
            nv_jsonOutput([
                'status' => false,
                'message' => 'Thiếu thông tin chức vụ',
            ]);
        }
    }
    if ($econtract['customer_type'] == 0 && empty($econtract['cccd'])) {
        nv_jsonOutput([
            'status' => false,
            'message' => 'Thiếu thông tin CCCD/CMND',
        ]);
    }
    if (empty($econtract['phone'])) {
        nv_jsonOutput([
            'status' => false,
            'message' => 'Thiếu thông tin Số điện thoại',
        ]);
    }
    if (empty($econtract['email'])) {
        nv_jsonOutput([
            'status' => false,
            'message' => 'Thiếu thông tin Email',
        ]);
    }
    if (empty($econtract['c_address'])) {
        nv_jsonOutput([
            'status' => false,
            'message' => 'Thiếu thông tin địa chỉ',
        ]);
    }
    // Kiểm tra đơn hàng
    $orders = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id=' . $econtract_id)->fetchAll();
    $order_ids = $code_orders = [];
    $_total = $_discount = $_total_end = $caregiver_id = 0;
    if (!empty($orders)) {
        foreach ($orders as $order) {
            $order_ids[] = $order['order_id'];
        }
        $order_ids = array_unique($order_ids);
    } else {
        nv_jsonOutput([
            'status' => false,
            'message' => 'Hợp đồng chưa bổ sung đơn hàng',
        ]);
    }
    $where = [];
    $where['AND'] = [
        ['IN' => ['id' => '(' . implode(',', $order_ids) . ')']]
    ];
    $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
    $api_dtinfo->setModule('bidding')
        ->setLang('vi')
        ->setAction('ListBiddingOrder')
        ->setData([
            'where' => $where,
            'show_customs_log' => 1
        ]);
    $result = $api_dtinfo->execute();
    $error = $api_dtinfo->getError();
    if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
        foreach ($result['data'] as $_order) {
            $code_orders[] = sprintf('BDH%010s', $_order['id']);
            $_total += $_order['money'];
            $_discount += $_order['discount'];
            $_total_end += $_order['total'];
            if (!$caregiver_id && !empty($_order['caregiver_id'])) {
                $caregiver_id = $_order['caregiver_id'];
            }
        }
    } else {
        nv_jsonOutput([
            'status' => false,
            'message' => 'Đã xảy ra lỗi khi lấy danh sách đơn hàng: ' . $error,
        ]);
    }

    // Lấy thông tin email của sale
    $info_caregiver = $db->query('SELECT email, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' where userid= ' . $caregiver_id)->fetch();
    if (empty($info_caregiver)) {
        nv_jsonOutput([
            'status' => false,
            'message' => 'Không tìm thấy thông tin sale chăm sóc',
        ]);
    }
    $sale_name = nv_show_name_user($info_caregiver['first_name'], $info_caregiver['last_name'], $info_caregiver['username']) ?? '';

    // Gửi mail và thông báo tới lãnh đạo
    $econtract_leader_emails = explode(',', $econtract_leader_emails);
    foreach ($econtract_leader_emails as $leader) {
        $email_leader = trim($leader);
        // Xử lý số hợp đồng có dạng link
        if (!empty($econtract['receiver']) && !empty($econtract['receiver_phone']) && !empty($econtract['receiver_address'])) {
            $subject = '[HỢP ĐỒNG ĐIỆN TỬ] Bạn có một yêu cầu ký tươi của sale ' . $sale_name . ' cần ký xác nhận!';
        } else {
            $subject = '[HỢP ĐỒNG ĐIỆN TỬ] Bạn có một hợp đồng của sale ' . $sale_name . ' cần ký xác nhận!';
        }
        $link = SITE_ID_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $econtract_id;
        // Nội dung mail
        $subject = html_entity_decode(nv_htmlspecialchars($subject));
        $messages = 'Kính gửi Ban Lãnh Đạo<br/>';
        $messages .= 'Hệ thống xin thông báo, Sale <strong>' . $sale_name . '</strong> đã hoàn tất hợp đồng <strong>' . nv_autoLinkDisable($econtract['contract_no']) . '</strong> với khách hàng <strong>' . $econtract['c_name'] . '</strong><br/>';
        $messages .= 'Để đảm bảo tiến độ dự án, mong bạn dành chút thời gian kiểm tra và thực hiện ký số. Bản hợp đồng điện tử đã được đính kèm trong email này.<br/>';
        $messages .= '<strong>Thông tin của hợp đồng như sau này như sau:</strong><br/>';
        if ($econtract['customer_type'] == 0) {
            $messages .= '- Khách hàng: <strong>' . $econtract['c_name'] . '</strong><br/>';
            $messages .= '- CCCD/CMND: <strong>' . $econtract['cccd'] . '</strong><br/>';
        } else {
            $messages .= '- Tên công ty: <strong>' . $econtract['c_name'] . '</strong><br/>';
            $messages .= '- Người đại diện: <strong>' . $econtract['representative'] . '</strong><br/>';
            $messages .= '- Chức vụ: <strong>' . $econtract['jobtitle'] . '</strong><br/>';
            $messages .= '- Mã số thuế: <strong>' . $econtract['tax_code'] . '</strong><br/>';
        }
        $messages .= '- Email: <strong>' . $econtract['email'] . '</strong><br/>';
        $messages .= '- Số điện thoại: <strong>' . $econtract['phone'] . '</strong><br/>';
        $messages .= '- Địa chỉ: <strong>' . $econtract['c_address'] . '</strong><br/>';
        if (isset($code_orders) && !empty($code_orders)) {
            $messages .= '<strong>Thông tin đơn hàng:</strong><br/>';
            $messages .= '- Đơn hàng: <strong>' . implode(',', $code_orders) . '</strong><br/>';
            $messages .= '- Tổng dịch vụ: <strong>' . nv_currency_format($_total) . '</strong><br/>';
            $messages .= '- Được giảm: <strong>' . nv_currency_format($_discount) . '</strong><br/>';
            $messages .= '- Tổng thanh toán: <strong>' . nv_currency_format($_total_end) . '</strong><br/>';
        }
        if ($econtract['term_changed'] != 0) {
            $messages .= '<strong>Nội dung hợp đồng đã được thay đổi điều khoản </strong><br/>';
        }
        $messages .= 'Để xem thông tin chi tiết, mời nhấp vào đây: <a href="' . $link . '">' . $link . '</a>';
        // Gửi mail thông báo
        nv_pending_mail($subject, $messages, $email_leader, $info_caregiver['email'] ?? '');

        //Gửi thông báo
        $userid_leader = $db->query('SELECT userid FROM ' . NV_USERS_GLOBALTABLE . ' where email= ' . $db->quote($email_leader))->fetchColumn();
        if ($userid_leader) {
            nv_insert_notification($module_name, '', array(
                'type' => 3,
                'content' => $nv_Lang->getModule('notification_econtract', $econtract['contract_no'], $econtract['c_name'], $sale_name),
            ), $econtract_id, $userid_leader, 0, 1, 0);
        }
    }
    // Cập nhật trạng thái của hợp đồng
    $sql_update = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET status=:status, stage=:stage, stage_next=:stage_next, updated_at=' . NV_CURRENTTIME . ' WHERE id=' . $econtract_id;

    $status = $econtract['customer_signed'] ? EContractStatus::CustomerSigned->value : EContractStatus::Incomplete->value;
    $stage = EContractStage::HSTDTSignatureRequired->value;
    $stage_next = $econtract['customer_signed'] ? EContractStage::Done->value : EContractStage::CustomerSignatureRequired->value;

    $sth = $db->prepare($sql_update);
    $sth->bindParam(':status', $status, PDO::PARAM_INT);
    $sth->bindParam(':stage', $stage, PDO::PARAM_INT);
    $sth->bindParam(':stage_next', $stage_next, PDO::PARAM_INT);
    $exc = $sth->execute();
    if ($exc) {
        $data_log = [
            'econtract_id' => $econtract_id,
            'version_id' => $econtract['current_version'],
            'action' => 1,
            'user_id' => $admin_info['admin_id'],
            'action_desc_vi' => 'Trình lãnh đạo ký hợp đồng',
            'action_desc_en' => 'Present the contract to the leader',
            'changed_data' => '',
            'log_visible' => 1,
            'created_at' => NV_CURRENTTIME
        ];
        create_log_econtract($data_log);
    }

    // Gửi mail tới văn phòng khi khách có nhu cầu in bản cứng
    if (!empty($data_config['email_hardcopy_contract']) && !empty($econtract['receiver']) && !empty($econtract['receiver_phone']) && !empty($econtract['receiver_address'])) {
        $link = SITE_ID_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=preview&id=' . $econtract['id'] . '&version=' . $econtract['current_version'];
        // Nội dung mail
        $subject_hardcopy = 'Yêu cầu gửi bản cứng hợp đồng cho Khách hàng ' . $econtract['c_name'];
        $subject_hardcopy = html_entity_decode(nv_htmlspecialchars($subject_hardcopy));

        $messages_hardcopy = 'Kính gửi bộ phận Văn Phòng!<br/>';
        $messages_hardcopy .= 'Hiện tại, hợp đồng của khách hàng <strong>' . $econtract['c_name'] . '</strong> (Mã hợp đồng: <strong>' . nv_autoLinkDisable($econtract['contract_no']) . '</strong>) do Sale <strong>' . $sale_name . '</strong> chăm sóc đã ký hợp đồng điện tử, nhưng khách có nhu cầu nhận thêm bản cứng của hợp đồng<br/>';
        $messages_hardcopy .= '<strong>Thông tin gửi hợp đồng bản cứng như sau:</strong><br/>';
        $messages_hardcopy .= '- Tên khách hàng: <strong>' . $econtract['receiver'] . '</strong><br/>';
        $messages_hardcopy .= '- Địa chỉ nhận: <strong>' . $econtract['receiver_address'] . '</strong><br/>';
        $messages_hardcopy .= '- Số điện thoại liên hệ: <strong>' . $econtract['receiver_phone'] . '</strong><br/>';
        $messages_hardcopy .= 'Kính mong bộ phận Văn Phòng hỗ trợ in ấn và gửi hợp đồng đến khách hàng theo thông tin trên trong thời gian sớm nhất.<br/>';
        $messages_hardcopy .= 'Nếu có vấn đề cần làm rõ, vui lòng liên hệ lại với Sale <strong>' . $sale_name . '</strong> để trao đổi thêm!<br/>';
        $messages_hardcopy .= 'Để xem thông tin chi tiết, mời nhấp vào đây: <a href="' . $link . '">' . $link . '</a>';
        // Gửi mail
        nv_pending_mail($subject_hardcopy, $messages_hardcopy, $data_config['email_hardcopy_contract'], $info_caregiver['email'] ?? '');
    }

    nv_jsonOutput([
        'status' => true,
        'message' => 'Đã gửi mail cho lãnh đạo',
    ]);
}
