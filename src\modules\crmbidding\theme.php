<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */

if (!defined('NV_IS_MOD_CRMBIDDING')) {
    die('Stop!!!');
}

use NukeViet\Module\crmbidding\Log;

/**
 * nv_theme_crmbidding_main()
 *
 * @param mixed $array_data
 * @return
 */
function nv_theme_crmbidding_main($array_data)
{
    global $module_info, $op, $nv_Lang;

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    //------------------
    // Viết code vào đây
    //------------------

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_list_econtract($array_data, $generate_page, $check, $array_number)
{
    global $module_info, $op, $module_name, $nv_Lang;

    $xtpl = new XTemplate('list-econtract.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('OP', $op);
    // Phân loại
    $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;

    if (!empty($array_number['process'])) {
        $xtpl->assign('PROCESS_LINK', $nv_Lang->getModule('pending_contracts', $array_number['process'], $base_url . '/process'));
        $xtpl->parse('main.link_process');
    }

    if (!empty($array_number['done'])) {
        $xtpl->assign('DONE_LINK', $nv_Lang->getModule('completed_contracts', $base_url . '/done'));
        $xtpl->parse('main.link_done');
    }

    if (!empty($array_data)) {
        $list_to_display = $check == '' ? $array_data[0] : ($check == 'done' ? $array_data[1] : $array_data[2]);
        $xtpl->assign('NUM_ITEMS', $check == '' ? $array_number['all'] : ($check == 'done' ? $array_number['done'] : $array_number['process']));
        foreach ($list_to_display as $data) {
            // Nếu đơn hàng đã hủy rồi thì không cho thao tác nữa
            if ($data['status'] != EContractStatus::Cancel->value) {
                if ($data['status'] != EContractStatus::Done->value) {
                    $xtpl->assign('LINK_UPDATE', $data['url_update']);
                    $xtpl->parse('main.data.loop.update_contract');
                }
                if ($data['status'] != EContractStatus::Done->value) {
                    $xtpl->assign('LINK_UPLOAD', $data['url_upload']);
                    $xtpl->parse('main.data.loop.upload_contract');
                }
                if (!empty($data['contract_path']) && file_exists(NV_ROOTDIR . '/' . $data['contract_path'])) {
                    $xtpl->assign('CONTRACT_ATTACMENT', $data['contract_attachment']);
                    $xtpl->assign('LINK_DOWNLOAD', $data['url_download_contract']);
                    $xtpl->parse('main.data.loop.download_contract');
                } else {
                    $xtpl->assign('LINK_DOWNLOAD_PDF', $data['url_download_contract']);
                    $xtpl->parse("main.data.loop.download_pdf_contract");
                }
            }

            $xtpl->assign('ROW', $data);
            $xtpl->parse('main.data.loop');
        }
        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.data.generate_page');
        }
        $xtpl->parse('main.data');
    } else {
        $xtpl->parse('main.no_data');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_doc_econtract($array_data, $array_data_sell, $array_data_link, $array_contact, $type)
{
    global $module_info, $module_name, $array_op;

    if ($array_data['type_econtract'] == 0) {
        $xtpl = new XTemplate('doc-econtract.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    } else {
        $xtpl = new XTemplate('doc-econtract-short.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    }
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('ECONTRACT_DATA', $array_data);
    $xtpl->assign('ECONTRACT_DATA_LINK', $array_data_link);
    $xtpl->assign('ECONTRACT_DATA_SELL', $array_data_sell);
    $xtpl->assign('TEMPLATE', $module_info['template']);

    $link_update = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=';
    if (isset($array_op[2]) && is_numeric($array_op[2])) {
        $xtpl->assign('BACK_URL', $link_update . $module_info['alias']['version-econtract'] . '/' . $array_op[1]);
    } else {
        $xtpl->assign('BACK_URL', $link_update . $module_info['alias']['list-econtract']);
    }
    if (!in_array($array_data['status'], [EContractStatus::Cancel->value, EContractStatus::Done->value])) {
        $xtpl->assign('EDIT_URL', $link_update . $module_info['alias']['update-econtract'] . '/' . $array_data['id']);
        $xtpl->parse('main.update_econtract');
        $xtpl->assign('UPLOAD_URL', $link_update . $module_info['alias']['upload-econtract'] . '/' . $array_data['id']);
        $xtpl->parse('main.upload_econtract');
    }
    $xtpl->assign('DOWNLOAD_URL', $link_update . $module_info['alias']['doc-econtract'] . '/' . $array_data['id'] . '&amp;pdf=1');

    // Xuất thông tin hợp đồng
    $contract_path = NV_ROOTDIR . '/' . $array_data['contract_path'];
    $is_valid_file = !empty($array_data['contract_path'])
        && file_exists($contract_path)
        && strtolower(pathinfo($contract_path, PATHINFO_EXTENSION)) === 'pdf';

    if ($type == 'preview') {
        $xtpl->assign('SAVE_URL', $link_update . $module_info['alias']['doc-econtract'] . '/' . $array_data['id'] . '/prevew&amp;save=1');
        $xtpl->parse('main.preview');
    }

    if ($is_valid_file && $type != 'preview') {
        $file_path = '<iframe style="height: calc(120vh - 200px);" scrolling="yes" src="/' . $array_data['contract_path'] . '#toolbar=0&navpanes=0" width="100%"></iframe>';
        $xtpl->assign('PDF_PATH', $file_path);
        $xtpl->parse('main.preview_pdf');
    } else {
        if (!empty($array_contact)) {
            $links = [];
            foreach ($array_contact as $contact) {
                $links[] = '<a href="mailto:' . $contact . '">' . $contact . '</a>';
            }
            if (count($links) == 1) {
                $array_contact = $links[0];
            } elseif (count($links) == 2) {
                $array_contact = implode(' và ', $links);
            } else {
                $array_contact = implode(', ', array_slice($links, 0, -1)) . ' và ' . end($links);
            }
            $xtpl->assign('LIST_CONTACT', $array_contact);
            $xtpl->parse('main.prevew_html.list_contact');
            $xtpl->parse('main.prevew_html.list_contact_e');
            $xtpl->parse('main.prevew_html.list_contact_buy');
        }

        if ($array_data['customer_type'] == 1 && !empty($array_data['representative'])) {
            $xtpl->assign('REPRESENTATIVE', $array_data['representative']);
            $xtpl->parse('main.prevew_html.customer_representative');
        }

        if ($array_data['customer_type'] == 1 && !empty($array_data['jobtitle'])) {
            $xtpl->assign('JOBTITLE', $array_data['jobtitle']);
            $xtpl->parse('main.prevew_html.customer_jobtitle');
        }

        if (!empty($array_data['authorization_letter'])) {
            $xtpl->assign('AUTHORIZATION_LETTER', $array_data['authorization_letter']);
            $xtpl->parse('main.prevew_html.customer_authorization_letter');
        }

        if ($array_data['customer_type'] == 1 && !empty($array_data['tax_code'])) {
            $xtpl->assign('TAX_CODE', $array_data['tax_code']);
            $xtpl->parse('main.prevew_html.customer_type_1');
        }

        if ($array_data['customer_type'] == 0 && !empty($array_data['cccd'])) {
            $xtpl->assign('CCCD', $array_data['cccd']);
            $xtpl->parse('main.prevew_html.customer_type_0');
        }

        if (!empty($array_data['bank_account'])) {
            $xtpl->parse('main.prevew_html.bank_account');
        }
        $xtpl->parse('main.prevew_html');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_version_econtract($array_data, $generate_page, $check_change_econtract)
{
    global $module_info, $op, $module_name;

    $xtpl = new XTemplate('version-econtract.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('OP', $op);
    $xtpl->assign('HISTORY_CHANGE_VERSION', $check_change_econtract);
    $xtpl->assign('BACK_URL', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['list-econtract']);

    if (!empty($array_data)) {
        foreach ($array_data as $data) {
            $xtpl->assign('ROW', $data);
            $xtpl->parse('main.data.loop');
        }
        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.data.generate_page');
        }
        $xtpl->parse('main.data');
    } else {
        $xtpl->parse('main.no_data');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_update_econtract($array_data, $order_user_ids, $data_total, $error, $orders, $success_message)
{
    global $module_info, $module_name;

    $xtpl = new XTemplate('update-econtract.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('DATA', $array_data);
    $xtpl->assign('CHECKED_DATA', $array_data['customer_type'] == 0 ? 'checked="checked"' : '');
    $xtpl->assign('CHECKED_AUTHOR_LETTER', !empty($array_data['authorization_letter']) ? 'checked="checked"' : '');
    $xtpl->assign('BACK_URL', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['list-econtract']);

    $link_action = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=';
    $xtpl->assign('ACTION', $link_action . $module_info['alias']['update-econtract'] . '/' . $array_data['id']);
    // Tổng giá
    $xtpl->assign('TOTAL_SERVICE', nv_number_format($data_total['total_service']) . ' VND');
    $xtpl->assign('PROMOTION', nv_number_format($data_total['promotion']) . ' VND');
    $xtpl->assign('TOTAL_PAYMENT', nv_number_format($data_total['total_payment']) . ' VND');
    if (!empty($order_user_ids)) {
        $custom_vips_processed = group_combo_orders($order_user_ids);
        foreach ($custom_vips_processed as $order) {
            if ($order['vip'] == 88) {
                $order['title_type'] = $order['type_export'] == 1 ? '- Quá khứ' : '- Hiện tại gia hạn theo năm';
            } else {
                $order['title_type'] = '';
            }
            $xtpl->assign('DATA_ORDER', $order);
            if ($order['rowspan'] > 0) {
                $xtpl->parse('main.loop.price');
            }
            $xtpl->parse('main.loop');
        }
    }

    if (!empty($orders)) {
        $orders = implode(' ', $orders);
        $xtpl->assign('URL_ORDER', $orders);
        $xtpl->parse('main.url_order');
    }

    if (!empty($error)) {
        $xtpl->assign('ERROR', $error);
        $xtpl->parse('main.error');
    }

    if (!empty($success_message)) {
        $xtpl->assign('SUCCESS_MESSAGE', $success_message);
        $xtpl->parse('main.success_message');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_upload_econtract($array_data, $orders, $error)
{
    global $module_info, $op, $module_name, $array_econtract_status;

    $xtpl = new XTemplate('upload-econtract.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('OP', $op);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('DATA', $array_data);
    $xtpl->assign('MAX_FILE_ECONTRACT_SIZE', MAX_FILE_ECONTRACT_SIZE);
    $xtpl->assign('BACK_URL', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['list-econtract']);

    $action_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['upload-econtract'] . '/' . $array_data['id'];
    $xtpl->assign('ACTION', $action_url);

    $xtpl->assign('URL_ORDER', implode(' ', $orders));

    foreach ($array_econtract_status as $key) {
        $xtpl->assign('OPTION', [
            'key' => $key,
            'title' => EContractStatus::tryFrom($key)->getLabel(),
            'selected' => $key == $array_data['status'] ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.select_status');
    }

    if (!empty($error)) {
        $xtpl->assign('ERROR', $error);
        $xtpl->parse('main.error');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}
