<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 24/4/2020, 20:6
 */

namespace NukeViet\InForm;

use PDO;

class InForm
{
    public static function creat(array $postdata)
    {
        global $db;

        $postdata['sender_group'] = $postdata['sender_group'] ?? 0;
        $postdata['sender_admin'] = $postdata['sender_admin'] ?? 0;
        $postdata['add_time'] = $postdata['add_time'] ?? NV_CURRENTTIME;
        $postdata['exp_time'] = $postdata['exp_time'] ?? 0;
        $postdata['receiver_grs'] = $postdata['receiver_grs'] ?? '';
        $postdata['receiver_ids'] = $postdata['receiver_ids'] ?? '';
        $postdata['sender_role'] = $postdata['sender_role'] ?? 'system';
        $postdata['isdef'] = $postdata['isdef'] ?? NV_LANG_DATA;

        if (empty($postdata['message']) or !is_array($postdata['message'])) {
            return false;
        }
        $postdata['message'] = json_encode([
            'isdef' => $postdata['isdef'],
            'contents' => $postdata['message']
        ], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        if (empty($postdata['link']) or !is_array($postdata['link'])) {
            $postdata['link'] = [];
        }
        $postdata['link'] = json_encode([
            'isdef' => $postdata['isdef'],
            'contents' => $postdata['link']
        ], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        try {
            $sth = $db->prepare('INSERT INTO ' . NV_INFORM_GLOBALTABLE . ' (
                receiver_grs, receiver_ids, sender_role, sender_group,
                sender_admin, message, link, add_time, exp_time
            ) VALUES (
                :receiver_grs, :receiver_ids, :sender_role, ' . $postdata['sender_group'] . ',
                ' . $postdata['sender_admin'] . ', :message, :link,
                ' . $postdata['add_time'] . ', ' . $postdata['exp_time'] . '
            )');

            $sth->bindValue(':receiver_grs', $postdata['receiver_grs'], PDO::PARAM_STR);
            $sth->bindValue(':receiver_ids', $postdata['receiver_ids'], PDO::PARAM_STR);
            $sth->bindValue(':sender_role', $postdata['sender_role'], PDO::PARAM_STR);
            $sth->bindValue(':message', $postdata['message'], PDO::PARAM_STR);
            $sth->bindValue(':link', $postdata['link'], PDO::PARAM_STR);
            $sth->execute();
            return 1;
        } catch (\Exception $e) {
            trigger_error(print_r($e, true));
            return 0;
        }
    }

    public static function insertInfrom($userid, $arrMess, $alias) {
        global $admin_info;
        // Thực hiện thông báo cho khách hàng
        $params['operation'] = 'add';
        $params['receiver_type'] = 'ids';
        $params['receiver_ids'] = $userid;
        if (!empty($admin_info)) {
            $params['sender_admin'] = $admin_info['admin_id'];
        }
        $params['isdef'] = 'vi';
        $params['message'] = $arrMess;
        
        $params['link'] = [
            'vi' => NV_MAIN_DOMAIN . '/vi/' . $alias,
            'en' => NV_MAIN_DOMAIN . '/en/' . $alias,
        ];

        // $params['add_time'] = date('d/m/Y');
        // $params['add_hour'] = date('H');
        // $params['add_min'] = date('i');

        $params['add_time'] = NV_CURRENTTIME;

        $result = self::creat($params);
        if ($result) {
            return 1;
        } else {
            return 0;
        }
    }
}
