<?php

/**
 * <PERSON><PERSON> t<PERSON>i ký hợp đồng
 */

if (!defined('NV_IS_MOD_CRMBIDDING')) {
    die('Stop!!!');
}

if (!defined('NV_IS_USER')) {
    $redirect = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name, true);
    Header("Location: " . NV_BASE_SITEURL . "index.php?" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($redirect));
    die();
}

if (!isset($array_op[1]) || !is_numeric($array_op[1])) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}
// Kiểm tra hợp đồng
$status_check = implode(',', [EContractStatus::Done->value, EContractStatus::Cancel->value]);
$row = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $array_op[1] . ' AND customer_id=' . $user_info['userid'] . ' AND status NOT IN (' . $status_check . ')')->fetch();
if (empty($row)) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}

// Lấy thông tin chi tiết của hợp đồng
$row_econtract_order = $db->query('SELECT order_id, username FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id=' . $row['id'])->fetchAll();
if (empty($row_econtract_order)) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}

// Chỉ hiện tên file
$row['contract_path'] = empty($row['contract_path']) ? '' : basename($row['contract_path']);

// Lấy danh sách các đơn hàng
$user_order = array_column($row_econtract_order, 'username'); // Người tạo đơn hàng
foreach ($row_econtract_order as $order_id) {
    $code = sprintf('BDH%010s', $order_id['order_id']);
    $link = URL_DTINFO . 'orders/pay/?worderid=' . $order_id['order_id'];
    $url_order = '<a href="' . $link . '" target="_blank"><span class="label label-info"> ' . $code . ' <em class="fa fa-external-link"></em></span></a>';
    $orders[] = $url_order;
}

$page_title = $nv_Lang->getModule('upload_contract');
$key_words = $module_info['keywords'];
$array_mod_title[] = array(
    'title' => $nv_Lang->getModule('upload_contract'),
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op, true)
);
$error = '';

// Lấy thông tin đơn hàng
$econtract_order_id = array_column($row_econtract_order, 'order_id');
$where = [];
$where['AND'][] = [
    'IN' => ['id' => '(' . implode(',', $econtract_order_id) . ')']
];
$api_dtinfo = new NukeViet\Api\DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
$api_dtinfo->setModule('bidding')
    ->setLang('vi')
    ->setAction('ListBiddingOrder')
    ->setData([
        'array_select' => ['id', 'caregiver_id'],
        'where' => $where,
    ]);
$check_order = $api_dtinfo->execute();
$error = $api_dtinfo->getError();
if (!empty($error)) {
    $error = $nv_Lang->getModule('error_request');
}

if ($check_order['status'] == 'error') {
    $error = $nv_Lang->getModule('order_not_found');
}
$_caregiver_ids = [];
if ($check_order['status'] == 'success' and !empty($check_order['data'])) {
    foreach ($check_order['data'] as $key => $row_oder) {
        $_caregiver_ids[] = $row_oder['caregiver_id'];
    }
    $_caregiver_ids = array_unique($_caregiver_ids);
}
// TODO: Xử lý upload hợp đồng đã ký và cập nhật trạng thái
if ($nv_Request->isset_request('uploadEcontract', 'post') && empty($error)) {
    $econtract_id = $nv_Request->get_int('econtract_id', 'post', 0);
    $status = $nv_Request->get_int('status', 'post', 0);

    $check_contract = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE customer_id=' . $user_info['userid'] . ' AND id=' . $econtract_id)->fetch();
    if (empty($check_contract)) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
    }

    if ($status == 0) {
        $error = $nv_Lang->getModule('status_not_select');
    }

    if (empty($_FILES['contract_path'])) {
        $error = $nv_Lang->getModule('teleimport_error_file');
    } else {
        // Khởi tạo thư mục upload
        if (!is_dir(NV_UPLOADS_REAL_DIR . '/econtracts')) {
            nv_mkdir(NV_UPLOADS_REAL_DIR, 'econtracts');
        }
        if (!is_dir(NV_UPLOADS_REAL_DIR . '/econtracts/' . strtolower(change_alias($user_order[0])))) {
            nv_mkdir(NV_UPLOADS_REAL_DIR . '/econtracts', strtolower(change_alias($user_order[0])));
        }
        $path_to_upload_contract = NV_UPLOADS_REAL_DIR . '/econtracts/' . strtolower(change_alias($user_order[0]));

        // Lưu tệp
        $upload = new NukeViet\Files\Upload();
        $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
        // Lưu hợp đồng
        if ($_FILES['contract_path']['size'] > MAX_FILE_ECONTRACT_SIZE) {
            $error = $nv_Lang->getModule('error_file_too_large');
        } else {
            $upload_contract_info = $upload->save_file($_FILES['contract_path'], $path_to_upload_contract, false, $global_config['nv_auto_resize']);
            if (!empty($upload_contract_info['error'])) {
                $error = $upload_contract_info['error'];
            } elseif (!in_array($upload_contract_info['ext'], ['pdf', 'zip'])) {
                $error = $nv_Lang->getModule('error_file_type');
            } else {
                $contract_path = NV_UPLOADS_DIR . '/econtracts/' . strtolower(change_alias($user_order[0])) . '/' . str_replace(['/', '\\'], '-', $econtract_id . '-' . NV_CURRENTTIME . '-' . $upload_contract_info['basename']);
                rename($upload_contract_info['name'], $path_to_upload_contract . '/' . str_replace(['/', '\\'], '-', $econtract_id . '-' . NV_CURRENTTIME . '-' . $upload_contract_info['basename']));
            }
        }
    }

    if (empty($error)) {
        // TODO: LƯU VÀO CSDL
        $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET
            contract_path=:contract_path,
            status=:status,
            stage=:stage,
            stage_next=:stage_next,
            customer_signed=:customer_signed,
            updated_at=' . NV_CURRENTTIME . ',
            signing_time=' . NV_CURRENTTIME . '
        WHERE id=' . $econtract_id;

        // Cập nhật stage và stage_next theo status
        switch ($status) {
            case 1: // Khách hàng đã ký
                $status = EContractStatus::CustomerSigned->value;
                $stage = EContractStage::CustomerContractReview->value;
                $stage_next = $row['hstdt_signed'] ? EContractStage::Done->value : EContractStage::HSTDTSignatureRequired->value;
                $customer_signed = 1; // Khách đã ký
                break;
            default:
                $status = EContractStatus::Incomplete->value;
                $stage = EContractStage::Negotiating->value;
                $stage_next = EContractStage::SupplementingInfo->value;
                $customer_signed = 0; // Khách đã ký
                break;
        }

        $sth = $db->prepare($sql);
        $sth->bindParam(':contract_path', $contract_path, PDO::PARAM_STR);
        $sth->bindParam(':status', $status, PDO::PARAM_INT);
        $sth->bindParam(':stage', $stage, PDO::PARAM_INT);
        $sth->bindParam(':stage_next', $stage_next, PDO::PARAM_INT);
        $sth->bindParam(':customer_signed', $customer_signed, PDO::PARAM_INT);
        $exc = $sth->execute();
        if ($exc) {
            // Lưu thông tin contract_path vào bảng econtract_versions
            $sql_update_version = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions SET pdf_path= ' . $db->quote($contract_path) . ' WHERE econtract_id=' . $econtract_id . ' AND version=' . $check_contract['current_version'];
            $db->query($sql_update_version);

            // Tạo log hợp đồng: econtract_logs
            $link_to_contract = NV_BASE_SITEURL . $contract_path;
            $log_desc_vi = '<strong>Khách hàng</strong> tải lên hợp đồng đã ký: <a href="' . $link_to_contract . '" target="_blank">Hợp đồng đính kèm</a>';
            $log_desc_en = '<strong>Customer</strong> uploads signed contract: <a href="' . $link_to_contract . '" target="_blank">Attachment</a>';
            $changed_data['contract_path'] = [
                'old' => $check_contract['contract_path'],
                'new' => $contract_path
            ];
            create_log_econtract([
                'econtract_id' => $check_contract['id'],
                'version_id' => $check_contract['current_version'],
                'action' => 1,
                'user_id' => $user_info['userid'],
                'action_desc_vi' => $log_desc_vi,
                'action_desc_en' => $log_desc_en,
                'changed_data' => json_encode($changed_data, JSON_UNESCAPED_UNICODE),
                'log_visible' => 1,
                'created_at' => NV_CURRENTTIME
            ]);

            // Gửi mail thông báo tới sale
            if (!empty($_caregiver_ids)) {
                $link = NV_MAIN_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=preview&id=' . $econtract_id . '&version=' . $check_contract['current_version'];
                foreach ($_caregiver_ids as $key => $caregiver_id) {
                    $email_caregiver = $db->query('SELECT userid, first_name, last_name, email, username FROM ' . NV_USERS_GLOBALTABLE . ' where userid= ' . $caregiver_id)->fetch();
                    if (!empty($email_caregiver)) {
                        $fullname = nv_show_name_user($email_caregiver['first_name'], $email_caregiver['last_name'], $email_caregiver['username']);
                        // Nội dung mail
                        $subject = 'Thông báo kiểm tra hợp đồng ' . $row['contract_no'] . ' đã được khách hàng tải lên';
                        $subject = html_entity_decode(nv_htmlspecialchars($subject));
                        $messages = 'Hệ thống gửi thông báo tới sale: ' . $fullname . ' (' . $email_caregiver['username']  . ')' . '<br/>';
                        $messages .= 'Kiểm tra lại file hợp đồng khách hàng đã tải lên. Tránh nhầm lẫn<br/>';
                        $messages .= 'Để xem thông tin chi tiết, mời nhấp vào đây: <a href="' . $link . '">' . $link . '</a>';
                        // Gửi mail
                        nv_pending_mail($subject, $messages, $email_caregiver['email']);

                        // Gửi thông báo tới sale khi khách hàng tải hợp đồng lên
                        nv_insert_notification($module_name, '', array(
                            'type' => 3,
                            'content' => 'Hợp dồng <strong>' . $row['contract_no'] . '</strong> đã được khách hàng <strong>' . $row['c_name'] . '</strong> tải lên hệ thống. Vui lòng kiểm tra lại thông tin hợp đồng!',
                        ), $row['id'], $email_caregiver['userid'], 0, 1, 0);
                    }
                }
            }

            nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
        }
    }
}

$contents = nv_theme_upload_econtract($row, $orders, $error);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
