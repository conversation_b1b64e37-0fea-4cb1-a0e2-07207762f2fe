<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\crmbidding;

use Exception;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

/**
 * <AUTHOR>
 *
 */
class Log
{
    /**
     * @param array $log
     * @return string
     */
    public static function getLogLink($log)
    {
        global $user_info, $admin_info;

        // <PERSON><PERSON><PERSON> đ<PERSON>nh quyền show cái link
        $userid = -1;
        if (defined('NV_IS_USER')) {
            $userid = $user_info['userid'];
        } elseif (defined('NV_IS_ADMIN')) {
            $userid = $admin_info['userid'];
        }
        if (!nv_user_in_groups($log['group_view']) and !empty($log['id_view']) and $log['id_view'] != $userid) {
            return $log['text'];
        }

        // Xác định link
        $link = (empty($log['admin']) ? NV_BASE_SITEURL : NV_BASE_ADMINURL) . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA;
        $link .= '&amp;' . NV_NAME_VARIABLE . '=' . $log['module'];
        if (!empty($log['op'])) {
            $link .= '&amp;' . NV_OP_VARIABLE . '=' . $log['op'];
        }
        if (!empty($log['query_str'])) {
            $link .= '&amp;' . str_replace('&', '&amp;', $log['query_str']);
        }

        return '<a href="' . $link . '">' . $log['text'] . '</a>';
    }

    /**
     * @param array $log
     * @return string
     */
    public static function getLogDirectLink($log)
    {
        return '<a target="_blank" href="' . $log['link'] . '">' . $log['text'] . '</a>';
    }

    private $array = [];

    /**
     * @param string|number $primary_message
     * @throws Exception
     * @return \NukeViet\Module\crmbidding\Log
     */
    public function __construct($primary_message)
    {
        // Không cho thêm item trống vào
        if (empty($primary_message)) {
            throw new Exception('Log message can not empty!');
        }
        if (!is_string($primary_message) and !is_numeric($primary_message)) {
            throw new Exception('Log message must is string or number!');
        }

        $this->array[] = $primary_message;
        return $this;
    }

    /**
     * @param LogRow $logItem
     * @throws Exception
     * @return \NukeViet\Module\crmbidding\Log
     */
    public function add($logItem)
    {
        $item = $logItem->getData();

        // Không cho thêm item trống vào
        if (empty($item)) {
            throw new Exception('Log item can not empty!');
        }

        $this->array[] = $item;
        $logItem->reset();
        return $this;
    }

    /**
     * @return string
     */
    public function toString()
    {
        return json_encode($this->array);
    }
}
