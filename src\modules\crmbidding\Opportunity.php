<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\crmbidding;

use Exception;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

/**
 * <AUTHOR>
 *
 */
class Opportunity
{
    public static function getOpportunities($id)
    {
        global $db;

        if (!is_array($id)) {
            $id = [$id];
        }
        if (empty($id)) {
            return [];
        }

        $sql = "SELECT userid, username, first_name, last_name, email
        FROM " . NV_USERS_GLOBALTABLE . " WHERE userid IN(" . implode(',', $id) . ")";
        $result = $db->query($sql);

        $array = [];
        while ($row = $result->fetch()) {
            $row['full_name'] = nv_show_name_user($row['first_name'], $row['last_name']);
            $row['show_name'] = $row['username'];
            if (!empty($row['full_name'])) {
                $row['show_name'] .= ' (' . $row['full_name'] . ')';
            }
            $array[$row['userid']] = $row;
        }

        return $array;
    }

    private $array;

    /**
     * @param array $array
     * @return \NukeViet\Module\crmbidding\Opportunity
     */
    public function __construct($array)
    {
        $this->array = [];
        if (!empty($array)) {
            $this->array = $array;
        }
        return $this;
    }

    /**
     * @param string $name tên cơ hội
     * @return \NukeViet\Module\crmbidding\Opportunity
     */
    public function setName($name)
    {
        $this->array['name'] = $name;
        return $this;
    }

    public function setEmail($email)
    {
        $this->array['email'] = $email;
        return $this;
    }

    /**
     * @param array $array
     * @return \NukeViet\Module\crmbidding\Opportunity
     */
    public function setArray($array)
    {
        $this->array = array_merge($this->array, $array);
        return $this;
    }

    /**
     * @param string $key
     * @return string
     */
    public function buildParam($key)
    {
        return (':' . $key);
    }

    /**
     * @throws Exception
     * @return number
     */
    public function save()
    {
        global $db, $module_data;

        if (empty($this->array['name'])) {
            throw new Exception('Missing opportunity name');
        }
        if (empty($this->array['email'])) {
            throw new Exception('Missing opportunity email');
        }

        $sql = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_opportunities (" . implode(', ', array_keys($this->array)) . ")
        VALUES (" . implode(', ', array_map([$this, 'buildParam'], array_keys($this->array))) . ")";
        $opportunity_id = $db->insert_id($sql, 'id', $this->array);

        if (empty($opportunity_id)) {
            throw new Exception('Add opportunity error');
        }

        return $opportunity_id;
    }
}
