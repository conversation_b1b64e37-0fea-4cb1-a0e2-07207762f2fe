.zalo-chat-widget {
    bottom: 100px!important;
    right: 18px!important;
    z-index: $zindex-social-btn !important;
}

#modalCallWidget {
    padding-top: 15%!important;
    .meta1 {
        font-size: 18px;
        font-weight: 500;
    }

    .meta2 {
        font-size: 18px;
    }

    .meta3 {
        font-size: 30px;
    }
}

#dauthauToggleCallWidget {
    position: fixed;
    right: 30px;
    bottom: 100px;
}

.fb_dialog.fb_dialog_advanced {
    z-index: $zindex-social-btn !important;

    > .fb_dialog_content {
        > iframe:first-child {
            bottom: 18px!important;
        }

        > iframe:last-child {
            bottom: 68px!important;
        }
    }
}

.fb_dialog.fb_dialog_mobile {
    z-index: $zindex-social-btn !important;

    > .fb_dialog_content {
        > iframe:first-child {
            bottom: 18px!important;
        }

        > iframe:last-child {
            bottom: 68px!important;
        }
    }
}

.fb-customerchat {
    > span {
        > iframe {
            z-index: ($zindex-social-btn + 1) !important;
            bottom: 160px!important;
        }
    }
}

.fb_new_ui_mobile_overlay_active {
    .fb-customerchat {
        z-index: $zindex-social-btn !important;
    }
}

.backdrop-check-user-data {
    padding-top: 15%!important;
    z-index: ($zindex-social-btn + 5) !important;
}
