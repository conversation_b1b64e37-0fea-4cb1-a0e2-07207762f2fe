<?php

/*
 * <PERSON><PERSON><PERSON> tra những email sử dụng Google apps/ Gsuite (Gmail theo tên miền...
 * (https://vinades.org/dauthau/dauthau.net/-/issues/986)
 *
 */
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));

require NV_CONSOLE_DIR . '/server.php';
require NV_ROOTDIR . '/includes/mainfile.php';

$console_starttime = microtime(true);
define('NV_USER_TABLE', 'nv4_users');

$no_check_mx = [
    'gmail.com',
    'yahoo.com',
    'yahoo.com.vn'
];
try {
    $array_mx_check = [];
    $domain_user_file = NV_CONSOLE_DIR . '/check_gsuite_userid.txt';
    $userid = 0;
    if (file_exists($domain_user_file)) {
        $userid_domain = file_get_contents($domain_user_file);
        $userid = isset($userid_domain) ? intval($userid_domain) : 0;
    }

    // lấy các tên miền
    $sth = $db->query('SELECT userid, email FROM nv4_users WHERE userid > ' . $userid . ' AND `is_gsuite`=0 ORDER BY userid ASC LIMIT 1000');
    while ($row = $sth->fetch()) {
        $userid = $row['userid'];
        $result = explode('@', $row['email']);
        $_domain = strtolower($result[1]);

        $is_gsuite = 0; // Chưa check
        if (isset($array_mx_check[$_domain])) {
            $is_gsuite = $array_mx_check[$_domain];
        } else {
            if (in_array($_domain, $no_check_mx)) {
                $is_gsuite = 3; // Không cần check MX
            } else {
                $mxhosts = [];
                getmxrr($_domain, $mxhosts);
                $_is_md_gooogle = false;
                $_mxvalue = '';
                foreach ($mxhosts as $mxValue) {
                    $_mxvalue = strtolower($mxValue);
                    if (strpos($_mxvalue, '.l.google.com')) {
                        $_is_md_gooogle = true;
                        break;
                    } elseif (strpos($_mxvalue, '.googlemail.com')) {
                        $_is_md_gooogle = true;
                        break;
                    }
                }
                if ($_is_md_gooogle) {
                    $is_gsuite = 1;
                } elseif ($_mxvalue != '') {
                    $is_gsuite = 2; // Có MX không phải là gmail
                } else {
                    $is_gsuite = 4; // Lỗi chưa check được MX
                }
                $array_mx_check[$_domain] = $is_gsuite;
            }
        }

        $db->query("UPDATE `nv4_users` SET `is_gsuite` = " . $is_gsuite . " WHERE `userid` = " . $userid);
        echo $row['email'] . "\t" . $is_gsuite . "\n";
    }
    if ($userid > 0) {
        file_put_contents($domain_user_file, $userid);
    }
} catch (PDOException $e) {
    print_r($e);
}

// Thông báo kết thúc
$console_endtime = microtime(true);
$execution_time = getConsoleExecuteTime($console_starttime, $console_endtime);
echo ("\nExecution time: " . $execution_time . "\n");
