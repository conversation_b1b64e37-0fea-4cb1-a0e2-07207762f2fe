<!-- BEGIN: main -->
<!-- BEGIN: search -->
<div class="well">
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" /> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" /> <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" /> <input type="hidden" name="sdt" value="{SDT}" />
        <div class="row">
            <div class="form-group">
                <select class="form-control" name="loai_cuoc_goi">
                    <option value="-1">{LANG.loai_cuoc_goi}</option>
                    <!-- BEGIN: array_loai_cuoc_goi -->
                    <option value="{LOAI.id}"{LOAI.selected}>{LOAI.title}</option>
                    <!-- END: array_loai_cuoc_goi -->
                </select>
            </div>
            <div class="form-group">
                <select class="form-control" name="trang_thai_cuoc_goi">
                    <option value="-1">{LANG.status}</option>
                    <!-- BEGIN: array_trang_thai_cuoc_goi -->
                    <option value="{STATUS.id}"{STATUS.selected}>{STATUS.title}</option>
                    <!-- END: array_trang_thai_cuoc_goi -->
                </select>
            </div>
            <div class="form-group">
                <input class="btn btn-primary" type="submit" value="{LANG.filter}" />
            </div>
        </div>
    </form>
</div>
<!-- END: search -->
<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th class="w50">{LANG.number}</th>
                    <th class="w100">
                        <div class="inlineblock">{LANG.file_ghi_am}</div>
                    </th>
                    <th class="w150">
                        <div class="inlineblock">{LANG.so_dien_thoai}</div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">{LANG.loai_cuoc_goi}</div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">{LANG.status}</div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">{LANG.nhan_vien}</div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">{LANG.thoi_gian}</div>
                    </th>
                </tr>
            </thead>
            <!-- BEGIN: generate_page -->
            <tfoot>
                <tr>
                    <td class="text-center" colspan="10">{NV_GENERATE_PAGE}</td>
                </tr>
            </tfoot>
            <!-- END: generate_page -->
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="text-center">{VIEW.number}</td>
                    <td>
                        <!-- BEGIN: recording --> <audio controls>
                            <source src="{VIEW.link}" type="audio/mpeg">
                            {LANG.browser_not_support}
                        </audio> <!-- END: recording -->
                    </td>
                    <td class="text-center">{VIEW.sdt}<!-- BEGIN: download --> <a href="{VIEW.link}" title="{LANG.download_audio}"><i class="fa fa-download" aria-hidden="true"></i></a> <!-- END: download --></td>
                    <td class="text-center">{VIEW.loai}</td>
                    <td class="text-center">{VIEW.trang_thai_cuoc_goi}</td>
                    <td>{VIEW.chuyen_vien}</td>
                    <td class="text-center">{VIEW.thoi_gian}</td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
</form>
<!-- END: main -->
