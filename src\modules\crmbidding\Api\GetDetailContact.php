<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetDetailContact implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'contact';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        $contactid = $nv_Request->get_int('contactid', 'post', '0');
        if ($contactid > 0) {
            try {
                require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';
                $row = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_contact WHERE id=' . $contactid)->fetch();
                if (empty($row)) {
                    $this->result->setSuccess()
                        ->setCode('4000')
                        ->setMessage($nv_Lang->getModule('api_error_400'));
                } else {
                    // xử lý dữ liệu thô
                    $row['createtime_display'] = $row['createtime'] > 0 ? nv_date('h:i d/m/Y', $row['createtime']) : '';
                    $row['updatetime_display'] = $row['updatetime'] > 0 ? nv_date('h:i d/m/Y', $row['updatetime']) : '';

                    $this->result->setSuccess();
                    $this->result->set('data', $row);
                }
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Contactid is integer, more than 0')
                ->getResult();
        }
        return $this->result->getResult();
    }
}
