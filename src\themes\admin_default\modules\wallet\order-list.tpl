<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />

<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>

<form method="get" action="{NV_BASE_ADMINURL}index.php">
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}"/>
    <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}"/>
    <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}"/>
    <div class="row">
        <div class="col-sm-6">
            <div class="form-group">
                <label><strong>{LANG.order_by_site}:</strong></label>
                <select class="form-control" name="site">
                    <option value="0">{LANG.order_by_site_all}</option>
                    <!-- BEGIN: site -->
                    <option value="{SITE.id}"{SITE.selected}>{SITE.title}</option>
                    <!-- END: site -->
                </select>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label><strong>{LANG.order_manager_bymod}:</strong></label>
                <select class="form-control" name="mod">
                    <option value="">{LANG.order_manager_bymod_all}</option>
                    <!-- BEGIN: mod -->
                    <option value="{MOD.key}"{MOD.selected}>{MOD.title}</option>
                    <!-- END: mod -->
                </select>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label><strong>{LANG.transaction_status}:</strong></label>
                <select class="form-control" name="st">
                    <option value="-1">{LANG.transaction_status_al}</option>
                    <!-- BEGIN: transtatus -->
                    <option value="{TRANSTATUS.key}"{TRANSTATUS.selected}>{TRANSTATUS.title}</option>
                    <!-- END: transtatus -->
                </select>
            </div>
        </div>
        <div class="col-sm-6">
            <div class="form-group">
                <label class="visible-sm-block visible-md-block visible-lg-block">&nbsp;</label>
                <button class="btn btn-primary" type="submit"><i class="fa fa-filter" aria-hidden="true"></i> {LANG.filterdata}</button>
            </div>
        </div>
    </div>
</form>

<div class="alert alert-info">
    {VIEW_TRANSCTION_NOTE}
</div>

<form class="form-inline" action="{NV_BASE_ADMINURL}index.php" method="post">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th class="w100">{LANG.order_manager_code}</th>
                    <th class="w150">{LANG.order_manager_module}</th>
                    <th>{LANG.order_manager_obj}</th>
                    <th class="w150 text-right">{LANG.sms_money}</th>
                    <th class="w150">{LANG.datecreate_admin}</th>
                    <th class="w150">{LANG.adddate}</th>
                    <th class="w150">{LANG.transaction_status}</th>
                    <th class="w150 text-center">{LANG.function}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td>{ROW.code}</td>
                    <td>{ROW.sitename}:<a href="{ROW.module_link}">{ROW.module_title}</a></td>
                    <td>
                        <!-- BEGIN: obj_text -->
                        {ROW.order_object} {ROW.order_name}
                        <!-- END: obj_text -->
                        <!-- BEGIN: obj_link -->
                        <a href="{LINK_OBJ}" target="_blank">{ROW.order_object} {ROW.order_name}</a>
                        <!-- END: obj_link -->
                    </td>
                    <td class="text-right"><strong class="text-danger">{ROW.money_amount}&nbsp;{ROW.money_unit}</strong></td>
                    <td>{ROW.add_time}</td>
                    <td>{ROW.update_time}</td>
                    <td>
                        <strong>{ROW.paid_status}</strong>
                    </td>
                    <td class="text-center">
                        <!-- BEGIN: delete -->
                        <a href="#" data-toggle="delorder" data-id="{ROW.id}" data-mgs="{LANG.order_del_note}" class="btn btn-danger btn-xs"><i class="fa fa-fw fa-trash"></i>{GLANG.delete}</a>
                        <!-- END: delete -->
                    </td>
                </tr>
                <!-- END: loop -->
            <tbody>
            <!-- BEGIN: generate_page -->
            <tfoot>
                <tr>
                    <td class="text-center" colspan="8">{NV_GENERATE_PAGE}</td>
                </tr>
            </tfoot>
            <!-- END: generate_page -->
        </table>
    </div>
</form>
<script type="text/javascript">
//<![CDATA[
$("#crf,#crt,#trf,#trt").datepicker({
    showOn : "both",
    dateFormat : "dd.mm.yy",
    changeMonth : true,
    changeYear : true,
    showOtherMonths : true,
    buttonText : null,
    buttonImage : null,
    buttonImageOnly : true,
    yearRange : "-99:+0",
    beforeShow : function() {
        setTimeout(function() {
            $('.ui-datepicker').css('z-index', 999999999);
        }, 0);
    }
});
//]]>
</script>
<!-- END: main -->
