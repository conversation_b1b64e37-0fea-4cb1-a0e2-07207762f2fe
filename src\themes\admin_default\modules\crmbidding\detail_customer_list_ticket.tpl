<!-- BEGIN: main -->
<div class="panel panel-default panel__success_left panel__custom">
    <div class="panel-heading">
        <h3 class="panel-title" id="support_ticket"><i class="fa fa-bookmark" aria-hidden="true"></i>  {LANG.list_ticket_panel_title} <i class="fa fa-angle-double-right" aria-hidden="true"></i></h3>
    </div>

    <div class="panel-body">
        <div class="info_product info_ticket">

                <h2 class="title_list_ticket listTicket">{LANG.list_ticket_title} <i class="fa fa-angle-double-right" aria-hidden="true"></i></h2>
                <div id="listTicket">
                    <table class="table table-bordered" id="table1">
                        <thead>
                            <tr>
                                <th class="text-center" width="3%">{LANG.stt}</th>
                                <th width="30%">{LANG.ticket_title}</th>
                                <th>{LANG.ticket_cat}</th>
                                <th>{LANG.ticket_status}</th>
                                <th class="text-center">{LANG.ticket_label}</th>
                                <th class="text-center">{LANG.ticket_assignee}</th>
                                <th class="text-center">{LANG.ticket_add_time} /<br>{LANG.ticket_edit_time}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- BEGIN: loop -->
                            <tr>
                                <td class="text-center">{TICKET.stt}</td>
                                <td><a href="{TICKET.link_detail}" title="{TICKET.title}" target="_blank"><strong>{TICKET.title}</strong></a></td>
                                <td class="text-center">{TICKET.cat_title}</td>
                                <td>{TICKET.status_display}</td>
                                <td>{TICKET.label}</td>
                                <td>{TICKET.assignee}</td>
                                <td>{TICKET.add_time}<br>{TICKET.edit_time}</td>
                            </tr>
                            <!-- END: loop -->
                        </tbody>
                        <!-- BEGIN: generate_page -->
                        <tfoot>
                            <tr>
                                <td colspan="7"><div class="text-center pagination">{NV_GENERATE_PAGE}</div></td>
                            </tr>
                        </tfoot>
                        <!-- END: generate_page -->
                    </table>
                </div>
        </div>
    </div>
</div>

<!-- BEGIN: generate_page_js -->
<script type="text/javascript">
	$(document).ready(function($) {
        var listTicket_page_selector = '.info_ticket .pagination';
        //phân trang API ListTicket
        $(listTicket_page_selector + ' .page-item').not('.active, .disabled').on('click', function (e) {
            e.preventDefault();
            var $this = $(this);
		    ajax_API_pagination($this, callAPI, 'ListTicket', '#show_api_list_ticket', '{LANG.err_list_ticket}')
        })
	});
    function ajax_API_pagination(selector, callBackAPI, apiName, resultLocation, errorMess) {
        //phân trang API
        var page_href = selector.children(".page-link").attr('href');
        if (page_href.includes('page=')) {
            var start = page_href.lastIndexOf('page=');
            var page_string = page_href.substring(start);
            var page = page_string.split('=');
            callBackAPI(apiName, resultLocation, errorMess, page[1]);
        } else {
            callBackAPI(apiName, resultLocation, errorMess, 1);
        }
    }
</script>
<!-- END: generate_page_js -->
<script>
$(document).ready(function($) {
    $(".title_list_ticket").click(function(event) {
        const id = $(this).attr('class').split(' ')[1];
        $('#' + id).slideToggle(500);
        // if (id == 'listOrder') {
        //     $(this).find('i').toggleClass('iconUp');
        // } else {
            $(this).find('i').toggleClass('iconDown');
        // }
    });
    $("#support_ticket").click(function(event) {
        $(this).find('.fa-angle-double-right').toggleClass('iconDown');
        console.log( $(this).parent().parent().find('.panel-body').slideToggle(400));
    });
})
</script>
<!-- END: main -->
