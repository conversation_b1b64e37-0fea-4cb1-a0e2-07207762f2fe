<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE') or !defined('NV_IS_MODADMIN')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;
use NukeViet\Module\crmbidding\User;

define('NV_IS_FILE_ADMIN', true);
define('CURRENCY_UNIT', ' VND');
require NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';
require NV_ROOTDIR . '/modules/' . $module_file . '/functions/hotlead.functions.php';

// telepro job
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_jobs';
$_array_telepro_jobs = $nv_Cache->db($sql, 'id', $module_name);

/**
 *
 * @since 11/09/2021
 * @link https://vinades.org/dauthau/dauthau.info/-/issues/536
 *       https://vinades.org/dauthau/dauthau.info/-/issues/607
 * <AUTHOR>
 *         nút đồng bộ thông tin tài khoản với bên id.dauthau.net
 */
if ($nv_Request->get_title('sync_user', 'post', '') === NV_CHECK_SESSION and defined('ID_API_USER_KEY')) {
    $respon = [
        'title' => $nv_Lang->getModule('error'),
        'text' => '',
        'classcolor' => 'danger',
        'success' => false
    ];

    $userid = $nv_Request->get_absint('userid', 'post', 0);
    $custom_fields = User::getFieldsConfig();

    $api = new DoApi(ID_API_URL, ID_API_USER_KEY, ID_API_USER_SECRET);
    $api->setModule('users')
        ->setLang('vi')
        ->setAction('GetUser')
        ->setData([
        'userid' => $userid
    ]);
    $result = $api->execute();
    $error = $api->getError();
    if (!empty($error)) {
        $respon['message'] = $error;
    } elseif ($result['status'] != 'success') {
        $respon['message'] = $result['message'];
    } else {
        $db->beginTransaction();
        try {
            // Cập nhật tài khoản
            $sql = "UPDATE " . NV_USERS_GLOBALTABLE . " SET last_update=" . intval($result['userinfo']['last_update']) . " WHERE userid=" . $userid;
            $db->query($sql);

            // Cập nhật tùy biến dữ liệu
            $data = [];
            foreach ($custom_fields as $field) {
                if (empty($field['is_system']) and isset($result['userinfo'][$field['field']])) {
                    $data[] = $field['field'] . "=" . $db->quote($result['userinfo'][$field['field']]);
                }
            }
            if (!empty($data)) {
                $sql = "UPDATE " . NV_USERS_GLOBALTABLE . "_info SET " . implode(', ', $data) . " WHERE userid=" . $userid;
                $db->query($sql);
            }

            $respon['success'] = true;
            $respon['message'] = $nv_Lang->getModule('success');
            $db->commit();
        } catch (Exception $e) {
            $db->rollBack();
            trigger_error($e->getMessage());
            $respon['message'] = $e->getMessage();
        }
    }

    nv_jsonOutput($respon);
}

function get_user($userid)
{
    global $array_user_id_users, $db;

    if (isset($array_user_id_users[$userid])) {
        return $array_user_id_users[$userid];
    } else {
        $_sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $userid;
        $user = $db->query($_sql)->fetch();
        return $user;
    }
}

function get_user_by_username($username)
{
    global $db;

    $username = trim($db->quote($username));
    $_sql = "SELECT userid, first_name, last_name, username FROM " . NV_USERS_GLOBALTABLE . " WHERE  username = " . $username;
    $user = $db->query($_sql)->fetch();

    return $user;
}

function get_last_day($month, $year)
{
    if ($month == 1 or $month == 3 or $month == 5 or $month == 7 or $month == 8 or $month == 10 or $month == 12) {
        return 31;
    } else if ($month == 2) {
        if ($year % 4 == 0 && ($year % 100 != 0 || $year % 400 == 0)) {
            return 29;
        } else {
            return 28;
        }
    } else {
        return 30;
    }
}

function get_time_ago($time)
{
    $time_difference = time() - $time;

    if ($time_difference < 1) {
        return '1 giây';
    }
    $condition = array(
        12 * 30 * 24 * 60 * 60 => ' năm',
        30 * 24 * 60 * 60 => 'tháng',
        24 * 60 * 60 => 'ngày',
        60 * 60 => 'giờ',
        60 => 'phút',
        1 => 'giây'
    );

    foreach ($condition as $secs => $str) {
        $d = $time_difference / $secs;

        if ($d >= 1) {
            $t = round($d);
            return '' . ($t > 1 ? '' : '') . ' ' . $t . ' ' . $str . '';
        }
    }
}

/**
 * Hàm dồn dữ liệu vào $array_timline
 *
 * @param int $time
 * @param array $data
 * @param array $array_timline
 */
function insertTimeline($time, $data, &$array_timline)
{
    $time = intval($time);
    if (!isset($array_timline[$time])) {
        $array_timline[$time] = [];
    }
    $array_timline[$time][] = $data;
}

/**
 * Lấy tất cả đơn hàng theo điều kiện từ trang đầu đến hết
 *
 * @param array $where
 */
function fetchAllOrders($where)
{
    $perpage = 50;
    $page = 1;
    $array = [];

    while (1) {
        $api = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
        $api->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingOrder')
            ->setData([
            'where' => $where,
            'page' => $page,
            'perpage' => $perpage
        ]);
        $result = $api->execute();
        if ($result['status'] != 'success') {
            trigger_error('Fetch Order via API error!!!');
        }

        $result['data'] = isset($result['data']) && !empty($result['status']) ? $result['data'] : [];
        $result['total'] = isset($result['total']) && !empty($result['total']) ? $result['total'] : 0;
        $array = array_merge_recursive($array, $result['data']);
        $max_page = ceil($result['total'] / $perpage);
        $page++;
        if ($page > $max_page) {
            break;
        }
    }

    return $array;
}

/**
 * Lấy tất cả đơn hàng theo điều kiện từ trang đầu đến hết
 *
 * @param array $where
 */
function fetchAllOrdersFromDauThauNet($where)
{
    $perpage = 50;
    $page = 1;
    $array = [];

    while (1) {
        $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
        $api->setModule('')
            ->setLang('vi')
            ->setAction('GetListPayment')
            ->setData([
            'where' => $where,
            'page' => $page,
            'perpage' => $perpage
        ]);

        $result = $api->execute();
        if (!isset($result['status']) || $result['status'] != 'success') {
            trigger_error('Fetch Order via API error!!!');
        }

        $result['data'] = isset($result['data']) && !empty($result['status']) ? $result['data'] : [];
        $result['total'] = isset($result['total']) && !empty($result['total']) ? $result['total'] : 0;
        $array = array_merge_recursive($array, $result['data']);
        $max_page = ceil($result['total'] / $perpage);
        $page++;
        if ($page > $max_page) {
            break;
        }
    }
    return $array;
}

/**
 * Trả về danh sách đã chia đơn và sale sẽ được chia tiếp theo
 */
function processSaleOrder($sale_order_to, &$array_orders, &$array_user_set_order)
{
    global $array_user_id_users;

    $sale_orders = [];
    foreach ($sale_order_to as $users) {
        if (!empty($array_user_id_users[$users])) {
            $sale_orders[] = $array_user_id_users[$users]['username'];
        }
    }
    $sale_orders = implode(', ', $sale_orders);
    if (count($array_orders) <= count($sale_order_to) && end($array_orders) === end($sale_order_to)) {
        $sale_order_to = [];
    }
    foreach ($sale_order_to as $sale) {
        unset($array_orders[$sale]);
    }
    $order_vip_id = array_key_first($array_orders);
    if ($order_vip_id === null) {
        $last_sale_id = end($sale_order_to);
        $find = false;
        $order_vip_id = 0;
        foreach ($array_user_set_order as $value) {
            if ($find) {
                $order_vip_id = $value;
                break;
            }
            if ($last_sale_id === $value) {
                $find = true;
            }
        }
        if ($order_vip_id === 0) {
            $order_vip_id = array_key_first($array_user_set_order);
        }
    }
    return [
        'sale_orders' => $sale_orders,
        'next_user' => $array_user_id_users[$order_vip_id]['username'] ?? ''
    ];
}

function get_bidding_customs($customs_id)
{
    $api = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
    $api->setModule('bidding')
        ->setLang('vi')
        ->setAction('GetBiddingCustoms')
        ->setData([
        'id' => $customs_id
    ]);
    $result = $api->execute();
    $error = $api->getError();
    if (empty($error) and $result['status'] == 'success' && !empty($result['data'])) {
        return $result['data'];
    }
    return [];
}

/**
 * Kiểm tra email chết tại cổng marketing
 *
 * @param string $email
 * @return array
 */
function get_dead_email_from_marketing($array_email)
{
    global $global_config, $db;
    if (!defined('MARKETING_API_CRM_KEY') || empty($array_email)) {
        return [];
    }

    $NV_Http_sub = new NukeViet\Http\Http($global_config, NV_TEMP_DIR);
    $NV_Http_sub->reset();
    $args_sub = [
        'headers' => [
            'Referer' => NV_MY_DOMAIN
        ],
        'body' => '',
        'timeout' => 10,
        'sslverify' => false,
        'decompress' => false
    ];
    $request_sub = [
        'apikey' => MARKETING_API_CRM_KEY,
        'apisecret' => MARKETING_API_CRM_SECRET,
        'action' => 'GetDeadEmail',
        'module' => 'marketing',
        'emails' => implode(', ', $array_email),
        'language' => 'vi'
    ];
    $args_sub['body'] = $request_sub;
    $responsive_sub = $NV_Http_sub->post(MARKETING_API_URL, $args_sub);
    if (!is_array($responsive_sub) || !empty(NukeViet\Http\Http::$error)) {
        return [];
    }
    $data = [];
    $email_marketing_sub = !empty($responsive_sub['body']) ? json_decode($responsive_sub['body'], true) : [];
    if (!empty($email_marketing_sub) && ($email_marketing_sub['status'] == 'success') && !empty($email_marketing_sub['data'])) {
        $data = $email_marketing_sub['data'];
    }
    return $data;
}
function get_config_vinades()
{
    $api = new DoApi(API_VINADES_URL, API_VINADES_KEY, API_VINADES_SECRET);
    $api->setModule('timekeeping')
        ->setLang('vi')
        ->setAction('GetConfig');
    /*
     * param
     * 'language' => 'vi',
     * 'action' => 'GetConfig',
     * 'module' => 'timekeeping',
     * 'month' => 5, // Không truyền lấy tháng hiện tại
     * 'year' => 2025, // Không truyền lấy năm hiện tại
     * 'users' => '5,nguyennam', // Truyền nếu muốn lấy thêm cấu hình riêng của từng người. Userid hoặc username phân cách nhau bằng dấu phẩy
     */
    /**
     * Ví dụ trả về
     * Array
     * (
     * [status] => success
     * [code] => 0000
     * [message] =>
     * [year] => 2025
     * [month] => 5
     * [config] => Array
     * (
     * [system] => Array
     * (
     * [dayAllowOfWeek] =>
     * [holidays] => 30/04,01/05,02/09,01/09
     * [timeLateAllow] => 6
     * [work_week] => 1,3,5
     * )
     *
     * [users] => Array
     * (
     * [5] => Array
     * (
     * [dayAllowOfWeek] => 1,2,3,4,5,6
     * [holidays] =>
     * [timeLateAllow] =>
     * [work_week] => 1,2,3,4,5
     * )
     *
     * [595] => Array
     * (
     * [dayAllowOfWeek] =>
     * [holidays] =>
     * [timeLateAllow] =>
     * [work_week] => 2,4
     * )
     *
     * )
     *
     * )
     * )
     * dayAllowOfWeek: Ngày làm việc trong tuần 0 là chủ nhật,....
     * holidays: Các ngày nghỉ lễ trong năm
     * timeLateAllow số phút cho phép đi muộn
     * work_week tuần làm việc t7 trong tháng, tính theo tuần có thứ 7 đầu tiên.
     * Ví dụ tháng này có ngày 1 rơi vào chủ nhật thì tuần 1 theo cấu hình đó là tuần bắt đầu từ ngày 2 của tháng
     */

    $result = $api->execute();
    $error = $api->getError();
    $config_work_vinades = [];
    if (empty($error) and $result['status'] == 'success') {
        $config_work_vinades = $result['config']['system'];
    }
    return $config_work_vinades;
}

// check ngày nghỉ trên site vinades
function isWorkingDay($timestamp, $config_work_vinades)
{
    $config_work_vinades['work_week'] = explode(',', $config_work_vinades['work_week']);
    $config_work_vinades['holidays'] = explode(',', $config_work_vinades['holidays']);
    $arr = [];
    $arr['saturday'] = 0;
    $arr['to_day'] = 0;

    $date = (new DateTime())->setTimestamp($timestamp);
    $currentDayOfWeek = $date->format('w'); // Thứ trong tuần (1 = Thứ 2, 0 = Chủ Nhật)
    $currentDate = $date->format('d/m');
    $year = $date->format('Y');
    $month = $date->format('m');

    // Tìm ngày đầu tiên của tháng
    $firstDayOfMonth = new DateTime("$year-$month-01");

    // Tìm ngày thứ 7 đầu tiên của tháng
    if ($firstDayOfMonth->format('N') <= 6) {
        $firstSaturday = clone $firstDayOfMonth;
        $firstSaturday->modify('next saturday');
    } else {
        // Nếu ngày đầu tháng là thứ 7
        $firstSaturday = clone $firstDayOfMonth;
    }
    // Tính số tuần từ thứ 7 đầu tiên đến hôm nay
    $diffDays = $date->diff($firstSaturday)->days;
    // Tuần thứ mấy tính từ thứ 7 đầu tiên
    $weekNumber = intval($diffDays / 7) + 1;
    // Kiểm tra xem tuần đó có nằm trong danh sách tuần đi làm không
    $isWorkingSaturday = in_array($weekNumber, $config_work_vinades['work_week']);
    if ($isWorkingSaturday) {
        $arr['saturday'] = 1;
    }

    // ngày lễ
    if (in_array($currentDate, $config_work_vinades['holidays'])) {
        $arr['to_day'] = 1;
    } else if ($currentDayOfWeek == 0) { // chủ nhật
        $arr['to_day'] = 1;
    } else if ($currentDayOfWeek == 6 and !$isWorkingSaturday) { // t7
        $arr['to_day'] = 1;
    }
    return $arr;
}

// tìm ngày làm việc gần nhất tính từ ngày nghỉ
function get_Working_day_next($_next_timestamp, $config_work_vinades)
{
    $check = isWorkingDay($_next_timestamp, $config_work_vinades);
    if ($check['to_day'] == 1) {
        return get_Working_day_next($_next_timestamp + 86400, $config_work_vinades);
    } else {
        // đệ quy để tìm ra ngày lv gần nhất
        return $_next_timestamp;
    }
}
