<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Http\Http;
use NukeViet\Api\DoApi;

nvUpdateTransactionExpired();

// Thay đổi sale tính thống kê
if ($nv_Request->isset_request('change_id_sale_static', 'post')) {
    $transactionid = $nv_Request->get_int('transactionid', 'post', 0);
    $new_caregiver_id = $nv_Request->get_int('new_vid', 'post', 0);
    $content = 'NO_' . $transactionid;

    // Kiểm tra quyền
    if ($IS_FULL_ADMIN or !empty($PERMISSION_ADMIN['is_mtransaction'])) {
        try {
            $db->beginTransaction();
            $sql = 'SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_transaction WHERE id=' . $transactionid;
            $row = $db->query($sql)->fetch();
            if (!empty($row)) {
                // Cập nhật trạng thái giao dịch
                $sql = 'UPDATE ' . $db_config['prefix'] . '_' . $module_data . '_transaction SET
                    id_sale_static=' . $new_caregiver_id . ' WHERE id=' . $transactionid;
                $exe = $db->query($sql);
                if ($exe) {
                    // các giao dịch trong ngày thì k cần xử lý do có tool tự động vào cuối ngày, chỉ xử lý các giao dịch cũ
                    $date = mktime(0, 0, 0, date('m', $row['transaction_time']), date('d', $row['transaction_time']), date('Y', $row['transaction_time']));
                    if ($date < mktime(0, 0, 0, date('m', NV_CURRENTTIME), date('d', NV_CURRENTTIME), date('Y', NV_CURRENTTIME))) {

                        // nếu đã dc tính thống kê cho sale thì trừ doanh số của sale đó đi
                        if ($row['id_sale_static'] > 0) {
                            $sql_wallet = 'SELECT * FROM nv4_vi_crmbidding_sale_static WHERE date = ' . $date . ' AND userid=' . $row['id_sale_static'];
                            $result_wallet = $db->query($sql_wallet);
                            if ($_row = $result_wallet->fetch()) {
                                $money_point_num = $_row['money_point_num'] - 1;
                                $money_point = $_row['money_point'] - $row['money_total'];
                                $money_point_bonus = $_row['money_point_bonus'] - (($row['money_total'] * 10) / 100);
                                $db->query('UPDATE nv4_vi_crmbidding_sale_static SET money_point_num = ' . $money_point_num . ', money_point = ' . $money_point . ', money_point_bonus = ' . $money_point_bonus . ' WHERE date = ' . $date . ' AND userid=' . $row['id_sale_static']);
                            }
                        }

                        // cộng doanh số cho sale mới
                        if ($new_caregiver_id > 0) {
                            $sql_wallet = 'SELECT * FROM nv4_vi_crmbidding_sale_static WHERE date = ' . $date . ' AND userid=' . $new_caregiver_id;
                            $result_wallet = $db->query($sql_wallet);
                            if ($_row = $result_wallet->fetch()) {
                                $money_point_num = $_row['money_point_num'] + 1;
                                $money_point = $_row['money_point'] + $row['money_total'];
                                $money_point_bonus = $_row['money_point_bonus'] + (($row['money_total'] * 10) / 100);
                                $db->query('UPDATE nv4_vi_crmbidding_sale_static SET money_point_num = ' . $money_point_num . ', money_point = ' . $money_point . ', money_point_bonus = ' . $money_point_bonus . ' WHERE date = ' . $date . ' AND userid=' . $new_caregiver_id);
                            }
                        }
                    }
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'change_id_sale_static', 'change_id_sale_static from ' . $row['id_sale_static'] . ' to ' . $new_caregiver_id, $admin_info['userid']);
                    $content = 'OK_' . $transactionid;
                }
            }
            $db->commit();
        } catch (PDOException $e) {
            $db->rollBack();
            trigger_error($e, 256);
        }
    }
    die($content);
}

// Thay đổi người chăm sóc
if ($nv_Request->isset_request('change_caregiver_id', 'post')) {
    $transactionid = $nv_Request->get_int('transactionid', 'post', 0);
    $new_caregiver_id = $nv_Request->get_int('new_vid', 'post', 0);
    $content = 'NO_' . $transactionid;

    // Kiểm tra quyền
    if ($IS_FULL_ADMIN or !empty($PERMISSION_ADMIN['is_mtransaction'])) {
        $sql = 'SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_transaction WHERE id=' . $transactionid;
        $row = $db->query($sql)->fetch();
        if (!empty($row)) {
            // Cập nhật trạng thái giao dịch
            $sql = 'UPDATE ' . $db_config['prefix'] . '_' . $module_data . '_transaction SET
                    caregiver_id=' . $new_caregiver_id . ' WHERE id=' . $transactionid;
            $exe = $db->query($sql);
            if ($exe) {
                nv_insert_logs(NV_LANG_DATA, $module_name, 'change_caregiver_id', 'change_caregiver_id from ' . $row['caregiver_id'] . ' to ' . $new_caregiver_id, $admin_info['userid']);
                $content = 'OK_' . $transactionid;
            }
        }
        $nv_Cache->delMod($module_name);
    }
    die($content);
}

// Thay đổi nguồn tiền
if ($nv_Request->isset_request('ajax_action_source_money', 'post')) {
    $transactionid = $nv_Request->get_int('transactionid', 'post', 0);
    $new_source_money = $nv_Request->get_int('new_source_money', 'post', 0);
    $content = 'NO_' . $transactionid;

    // Kiểm tra quyền
    if ($IS_FULL_ADMIN or !empty($PERMISSION_ADMIN['is_mtransaction'])) {
        $sql = 'SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_transaction WHERE id=' . $transactionid;
        $row = $db->query($sql)->fetch();
        if (!empty($row)) {
            // Cập nhật trạng thái giao dịch
            $sql = 'UPDATE ' . $db_config['prefix'] . '_' . $module_data . '_transaction SET
                    source_money=' . $new_source_money . ' WHERE id=' . $transactionid;
            $exe = $db->query($sql);
            if ($exe) {
                $content = 'OK_' . $transactionid;
            }
        }
        $nv_Cache->delMod($module_name);
    }
    die($content);
}

// Thay đổi trạng thái giao dịch
if ($nv_Request->isset_request('ajax_action', 'post')) {
    $transactionid = $nv_Request->get_int('transactionid', 'post', 0);
    $new_vid = $nv_Request->get_int('new_vid', 'post', 0);
    $content = 'NO_' . $transactionid;

    // Kiểm tra quyền
    if ($IS_FULL_ADMIN or !empty($PERMISSION_ADMIN['is_mtransaction'])) {
        $sql = 'SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_transaction WHERE id=' . $transactionid;
        $row = $db->query($sql)->fetch();
        if (isset($row['transaction_status']) and $row['transaction_status'] != $new_vid and $row['transaction_status'] != 4 and $new_vid != 0 and empty($row['is_expired'])) {
            // Lấy thông tin đơn hàng nếu giao dịch thanh toán cho đơn hàng
            $order_info = [];
            if (!empty($row['order_id'])) {
                $order_info = $db->query("SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_orders WHERE id=" . $row['order_id'])->fetch();
            }

            $db->beginTransaction();

            try {
                // Cập nhật trạng thái giao dịch
                $sql = 'UPDATE ' . $db_config['prefix'] . '_' . $module_data . '_transaction SET
                    transaction_status=' . $new_vid . ',
                    transaction_time=' . NV_CURRENTTIME . '
                WHERE id=' . $transactionid;
                $db->query($sql);

                if (!empty($row['order_id'])) {
                    // Cập nhật trạng thái giao dịch nếu thanh toán hóa đơn của các module khác
                    $db->query('UPDATE ' . $db_config['prefix'] . '_' . $module_data . '_orders SET
                        paid_status=' . $new_vid . ',
                        paid_time=' . NV_CURRENTTIME . '
                    WHERE id=' . $row['order_id']);

                    if (!empty($order_info) and isset($global_array_sites[$order_info['site_id']])) {
                        // Gọi API để cập nhật đơn hàng trên các site con
                        Http::$error = [];
                        $api_url = NV_SERVER_PROTOCOL . '://' . $global_array_sites[$order_info['site_id']]['sitedomain'] . '/api.php';
                        $api = new DoApi($api_url, $global_array_sites[$order_info['site_id']]['api_key'], $global_array_sites[$order_info['site_id']]['api_secret']);
                        $api->setModule($order_info['order_mod'])
                            ->setLang(NV_LANG_DATA)
                            ->setAction('UpdateOrderPaid')
                            ->setData([
                            'order_id' => $order_info['order_id'],
                            'paid_status' => $new_vid,
                            'paid_time' => NV_CURRENTTIME,
                            'transaction_id' => $transactionid,
                            'admin_id' => $admin_info['admin_id']
                        ]);
                        $result_api = $api->execute();
                        $error = $api->getError();
                        if (!empty($error)) {
                            throw new Exception($error);
                        }
                        if ($result_api['status'] != 'success') {
                            throw new Exception($result_api['message'] ?: 'API call error!!!');
                        }
                    } elseif (!empty($order_info) and isset($sys_mods[$order_info['order_mod']])) {
                        // Gọi về module để cập nhật đơn hàng của module trên site này
                        $order_info['paid_status'] = $new_vid;
                        $order_info['paid_time'] = NV_CURRENTTIME;

                        // Backup lại các biến của module wallet
                        $_module_name = $module_name;
                        $_module_info = $module_info;
                        $_module_file = $module_file;
                        $_module_data = $module_data;
                        $_module_upload = $module_upload;

                        $module_name = $order_info['order_mod'];
                        $module_info = $sys_mods[$order_info['order_mod']];
                        $module_file = $module_info['module_file'];
                        $module_data = $module_info['module_data'];
                        $module_upload = $module_info['module_upload'];

                        // Gọi ra file cập nhật giao dịch
                        try {
                            if (file_exists(NV_ROOTDIR . '/modules/' . $module_file . '/wallet.admin.php')) {
                                define('NV_IS_WALLET_ADMIN', true);
                                require NV_ROOTDIR . '/modules/' . $module_file . '/wallet.admin.php';
                            }
                        } catch (Exception $ex) {
                            trigger_error($ex->getMessage());
                        }

                        // Trả lại các biến backup
                        $module_name = $_module_name;
                        $module_info = $_module_info;
                        $module_file = $_module_file;
                        $module_data = $_module_data;
                        $module_upload = $_module_upload;
                    }
                } else {
                    // Cập nhật số tiền nếu giao dịch nạp tiền
                    update_money($row['userid'], $row['money_total'], $row['money_unit'], $new_vid, $row['transaction_status'], $row['status']);
                }

                $db->commit();
            } catch (Exception $e) {
                $db->rollBack();
                nv_htmlOutput('ERR_' . $e->getMessage());
            }

            $content = 'OK_' . $transactionid;
        }
        $nv_Cache->delMod($module_name);
    }

    include NV_ROOTDIR . '/includes/header.php';
    echo $content;
    include NV_ROOTDIR . '/includes/footer.php';
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);

// Các trường thông tin của tài khoản bị tác động
$array_fields_search = [
    'customer_name' => [
        'title' => $nv_Lang->getModule('customer_name'),
        'sql_prefix' => 'tb1'
    ],
    'customer_email' => [
        'title' => $nv_Lang->getModule('customer_email'),
        'sql_prefix' => 'tb1'
    ],
    'customer_phone' => [
        'title' => $nv_Lang->getModule('customer_phone'),
        'sql_prefix' => 'tb1'
    ],
    'customer_address' => [
        'title' => $nv_Lang->getModule('customer_address'),
        'sql_prefix' => 'tb1'
    ],
    'customer_info' => [
        'title' => $nv_Lang->getModule('customer_address'),
        'sql_prefix' => 'tb1'
    ],
    'username' => [
        'title' => $nv_Lang->getModule('account'),
        'sql_prefix' => 'tb3'
    ],
    'email' => [
        'title' => $nv_Lang->getModule('customer_email'),
        'sql_prefix' => 'tb3'
    ]
];

// Các trường thông tin của tài khoản thực hiện giao dịch
$arr_fields_trans = [
    'email' => [
        'title' => $nv_Lang->getModule('customer_email'),
        'sql_prefix' => [
            'tb2',
            'tb4'
        ]
    ],
    'username' => [
        'title' => $nv_Lang->getModule('account'),
        'sql_prefix' => [
            'tb2',
            'tb4'
        ]
    ]
];

$isSearchSubmit = false;
$array_search = [];
$array_search['q'] = $nv_Request->get_title('q', 'get', '');
$array_search['type'] = $nv_Request->get_int('type', 'get', ''); // Kiểu tìm kiếm, mặc định tìm theo tài khoản bị tác động
$array_search['are'] = $nv_Request->get_title('are', 'get', ''); // Các field tìm theo khóa
$array_search['transacter_fields'] = $nv_Request->get_title('transacter_fields', 'get', ''); // Các field tìm theo khóa
$array_search['crf'] = $nv_Request->get_title('crf', 'get', ''); // Tạo từ
$array_search['crt'] = $nv_Request->get_title('crt', 'get', ''); // Tạo đến
$array_search['st'] = $nv_Request->get_int('st', 'get', 0); // Cộng tiền hay trừ tiền
$array_search['mo'] = $nv_Request->get_title('mo', 'get', ''); // Loại tiền tệ
$array_search['aou'] = $nv_Request->get_int('aou', 'get', 0); // Admin giao dịch hay thành viên giao dịch
$array_search['tty'] = $nv_Request->get_int('tty', 'get', -1); // Kiểu giao dịch
$array_search['trf'] = $nv_Request->get_title('trf', 'get', ''); // Giao dịch từ ngày
$array_search['trt'] = $nv_Request->get_title('trt', 'get', ''); // Giao dịch đến ngày
$array_search['tst'] = $nv_Request->get_int('tst', 'get', -1); // Trạng thái giao dịch
$array_search['tpa'] = $nv_Request->get_title('tpa', 'get', ''); // Cổng thanh toán
$array_search['per_page'] = $nv_Request->get_int('per_page', 'get', 0); // Số bản ghi
$array_search['ad_id_static'] = $nv_Request->get_int('ad_id_static', 'get', -1); // thống kê sale, tính cả customer_id và adminid
$array_search['source_money'] = $nv_Request->get_int('source_money', 'get', -1); // nguồn tiền
$array_search['show_static_crm'] = $nv_Request->get_int('show_static_crm', 'get', 0); // xem thống kê từ crm
$array_search['admin_id'] = $nv_Request->get_int('admin_id', 'get', -1);

// Xem theo thành viên
$view_userid = $nv_Request->get_int('userid', 'get', 0);
$view_user_info = [];
if ($view_userid) {
    $sql = "SELECT userid, username FROM " . NV_USERS_GLOBALTABLE . " WHERE userid=" . $view_userid;
    $view_user_info = $db->query($sql)->fetch();
    if (empty($view_user_info)) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
    }
}

// Xem theo đơn hàng
$view_orderid = $nv_Request->get_int('orderid', 'get', 0);
$view_order_info = [];
if ($view_orderid) {
    $sql = "SELECT * FROM " . $db_config['prefix'] . '_' . $module_data . "_orders WHERE id=" . $view_orderid;
    $view_order_info = $db->query($sql)->fetch();
    if (empty($view_order_info)) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
    }
}

// Xử lý dữ liệu vào
if (!empty($array_search['are']) and !isset($array_fields_search[$array_search['are']])) {
    $array_search['are'] = '';
}
if (!empty($array_search['transacter_fields']) and !isset($arr_fields_trans[$array_search['transacter_fields']])) {
    $array_search['transacter_fields'] = '';
}
$array_ele_date = [
    'crf',
    'crt',
    'trf',
    'trt'
];
foreach ($array_ele_date as $f) {
    $fval = $array_search[$f];
    $array_search[$f] = 0;
    if (preg_match('/^([0-9]{2})\.([0-9]{2})\.([0-9]{4})$/', $fval, $m)) {
        $array_search[$f] = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
    }
}
if ($array_search['st'] != 1 and $array_search['st'] != -1) {
    $array_search['st'] = 0;
}
if (!empty($array_search['mo']) and !isset($global_array_money_sys[$array_search['mo']])) {
    $array_search['mo'] = '';
}
if ($array_search['aou'] > 2 and $array_search['aou'] < 0) {
    $array_search['aou'] = 0;
}
if ($array_search['tty'] != -1 and !isset($global_array_transaction_type[$array_search['tty']])) {
    $array_search['tty'] = -1;
}
if ($array_search['tst'] != -1 and !isset($global_array_transaction_status[$array_search['tst']])) {
    $array_search['tst'] = -1;
}
if (!empty($array_search['tpa']) and !isset($global_array_payments[$array_search['tpa']])) {
    $array_search['tpa'] = '';
}
if ($array_search['per_page'] > 1000 or $array_search['per_page'] < 1) {
    $array_search['per_page'] = 30;
}

$page = $nv_Request->get_int('page', 'get', 1);
$per_page_old = $nv_Request->get_int('per_page', 'cookie', 50);
$per_page = !empty($array_search['per_page']) ? $array_search['per_page'] : $per_page_old;
if ($per_page_old != $per_page) {
    $nv_Request->set_Cookie('per_page', $per_page, NV_LIVE_COOKIE_TIME);
}

$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;

$where = [];
if (!empty($view_user_info)) {
    $base_url .= '&amp;userid=' . $view_user_info['userid'];
    $where[] = 'tb1.userid=' . $view_user_info['userid'];
}
if (!empty($view_order_info)) {
    $base_url .= '&amp;orderid=' . $view_order_info['id'];
    $where[] = 'tb1.order_id=' . $view_order_info['id'];
}
if (!empty($array_search['q'])) {
    $isSearchSubmit = true;
    $base_url .= '&amp;q=' . urlencode($array_search['q']);
    if (preg_match('/^(GD|WP)([0-9]+)$/i', $array_search['q'], $m)) {
        // Tìm theo số hóa đơn
        $where[] = 'tb1.id=' . intval($m[2]);
    } else {
        // Tìm từ khóa thông thường
        $dblike = $db->dblikeescape($array_search['q']);

        // Mặc định sẽ cho tìm theo tài khoản bị tác động
        if ($array_search['type'] !== 2) {
            if (empty($array_search['are'])) {
                $whereOr = [];
                foreach ($array_fields_search as $key => $val) {
                    $whereOr[] = $val['sql_prefix'] . '.' . $key . " LIKE '%" . $dblike . "%'";
                }
                $where[] = '(' . implode(' OR ', $whereOr) . ')';
            } else {
                $where[] = $array_fields_search[$array_search['are']]['sql_prefix'] . '.' . $array_search['are'] . " LIKE '%" . $dblike . "%'";
            }
        } else {
            if (empty($array_search['transacter_fields'])) {
                $whereOr = [];
                foreach ($arr_fields_trans as $key => $val) {
                    $whereOr[] = $val['sql_prefix'][0] . '.' . $key . " LIKE '%" . $dblike . "%'";
                    $whereOr[] = $val['sql_prefix'][1] . '.' . $key . " LIKE '%" . $dblike . "%'";
                }
                $where[] = '(' . implode(' OR ', $whereOr) . ')';
            } else {
                $whereOr = [];
                $whereOr[] = $arr_fields_trans[$array_search['transacter_fields']]['sql_prefix'][0] . '.' . $array_search['transacter_fields'] . " LIKE '%" . $dblike . "%'";
                $whereOr[] = $arr_fields_trans[$array_search['transacter_fields']]['sql_prefix'][1] . '.' . $array_search['transacter_fields'] . " LIKE '%" . $dblike . "%'";
                $where[] = '(' . implode(' OR ', $whereOr) . ')';
            }
        }
    }
}
if (!empty($array_search['are'])) {
    $base_url .= '&amp;are=' . $array_search['are'];
}
if (!empty($array_search['transacter_fields'])) {
    $base_url .= '&amp;transacter_fields=' . $array_search['transacter_fields'];
}
if (!empty($array_search['type'])) {
    $base_url .= '&amp;type=' . $array_search['type'];
}
if ($array_search['crf']) {
    $isSearchSubmit = true;
    $base_url .= '&amp;crf=' . nv_date('d.m.Y', $array_search['crf']);
    $where[] = 'tb1.created_time>=' . $array_search['crf'];
}
if ($array_search['crt']) {
    $isSearchSubmit = true;
    $base_url .= '&amp;crt=' . nv_date('d.m.Y', $array_search['crt']);
    $where[] = 'tb1.created_time<' . ($array_search['crt'] + 86400);
}
if ($array_search['st'] != 0) {
    $isSearchSubmit = true;
    $base_url .= '&amp;st=' . $array_search['st'];
    $where[] = 'tb1.status=' . $array_search['st'];
}
if (!empty($array_search['mo'])) {
    $isSearchSubmit = true;
    $base_url .= '&amp;mo=' . $array_search['mo'];
    $where[] = 'tb1.money_unit=' . $db->quote($array_search['mo']);
}
if ($array_search['aou']) {
    $isSearchSubmit = true;
    $base_url .= '&amp;aou=' . $array_search['aou'];
    if ($array_search['aou'] == 1) {
        $where[] = 'tb1.adminid!=0';
    } else {
        $where[] = 'tb1.customer_id!=0';
    }
}
if ($array_search['tty'] != -1) {
    $isSearchSubmit = true;
    $base_url .= '&amp;tty=' . $array_search['tty'];
    $where[] = 'tb1.transaction_type=' . $array_search['tty'];
}
if ($array_search['trf']) {
    $isSearchSubmit = true;
    $base_url .= '&amp;trf=' . nv_date('d.m.Y', $array_search['trf']);
    $where[] = 'tb1.transaction_time>=' . $array_search['trf'];
}
if ($array_search['trt']) {
    $isSearchSubmit = true;
    $base_url .= '&amp;trt=' . nv_date('d.m.Y', $array_search['trt']);
    $where[] = 'tb1.transaction_time<' . ($array_search['trt'] + 86400);
}
if ($array_search['tst'] != -1) {
    $isSearchSubmit = true;
    $base_url .= '&amp;tst=' . $array_search['tst'];
    $where[] = 'tb1.transaction_status=' . $array_search['tst'];
}
if (!empty($array_search['tpa'])) {
    $isSearchSubmit = true;
    $base_url .= '&amp;tpa=' . $array_search['tpa'];
    $where[] = 'tb1.payment=' . $db->quote($array_search['tpa']);
}

if ($array_search['source_money'] != -1) {
    $isSearchSubmit = true;
    $base_url .= '&amp;source_money=' . $array_search['source_money'];
    $where[] = 'tb1.source_money=' . $array_search['source_money'];
}

if ($array_search['show_static_crm'] == 1) {
    $isSearchSubmit = true;
    $base_url .= '&amp;show_static_crm=' . $array_search['show_static_crm'];
    $where[] = 'tb1.userid NOT IN (' . implode(',', array_keys($array_user_id_users)) . ')';

    $base_url .= '&amp;ad_id_static=' . $array_search['ad_id_static'];
    if ($array_search['st'] == 1) {
        if ($array_search['ad_id_static'] == 0) {
            $where[] = 'tb1.adminid =0 AND tb1.customer_id > 0';
        } else if ($array_search['ad_id_static'] > 0) {
            $where[] = '(tb1.adminid = ' . $array_search['ad_id_static'] . ' OR customer_id=' . $array_search['ad_id_static'] . ')';
        } else if ($array_search['ad_id_static'] < 0 and $array_search['source_money'] < 0) {
            $where[] = 'tb2.username IS NULL AND tb4.username IS NULL';
        }
    } else {
        if ($array_search['ad_id_static'] == 0) {
            $where[] = 'tb1.id_sale_static =0 AND tb1.customer_id > 0';
        } else if ($array_search['ad_id_static'] > 0) {
            $where[] = '(tb1.id_sale_static = ' . $array_search['ad_id_static'] . ' OR customer_id=' . $array_search['ad_id_static'] . ')';
        } else if ($array_search['ad_id_static'] < 0 and $array_search['source_money'] < 0) {
            $where[] = 'tb2.username IS NULL AND tb4.username IS NULL';
        }
    }

    $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_crmbidding_groups_users';
    $result = $db->query($sql);
    $array_groups_users = array();
    while ($row = $result->fetch()) {
        $row['config'] = json_decode($row['config'], true);
        $array_groups_users[$row['userid']] = $row;
    }

    $arr_admin_view = $arr_admin_view_tmp = [];
    if ($module_config['crmbidding']['view_static'] == 2 and !defined('NV_IS_SPADMIN')) {
        if (isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 0) {
            if ($array_groups_users[$admin_info['userid']]['is_leader'] == 1) {
                $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_crmbidding_groups_users WHERE group_id = ' . $array_groups_users[$admin_info['userid']]['group_id'] . ' AND userid != ' . $admin_info['userid'];
                $_result = $db->query($_sql);
                while ($_row_groups_users = $_result->fetch()) {
                    $arr_admin_view[$_row_groups_users['userid']] = $_row_groups_users['userid'];
                }
                $arr_admin_view[$admin_info['userid']] = $admin_info['userid'];
                $arr_admin_view_tmp = $arr_admin_view;
                if ($array_search['admin_id'] != -1) {
                    $arr_admin_view = [];
                }
            } else {
                $array_search['admin_id'] = $admin_info['userid'];
            }
        }
    }

    if (!empty($arr_admin_view)) { // sale leader
        $where[] = '  (tb1.adminid IN (' . implode(',', $arr_admin_view) . ') OR tb1.customer_id IN (' . implode(',', $arr_admin_view) . '))';
    } else if ($array_search['admin_id'] > 0) { // sale thường
        $where[] = '  (tb1.adminid = ' . $array_search['admin_id'] . ' OR tb1.customer_id = ' . $array_search['admin_id'] . ')';
    } else if ($array_search['admin_id'] == 0) { // admin_id tính cho mkt
        $where[] = '  (tb1.adminid = 0 AND tb1.customer_id > 0)';
    }
} else {
    /*
     * phân quyền xem giao dịch
     * - điều hành chung xem tất cả
     * - sale xem các giao dịch của mình
     * - Trưởng nhóm xem của nhóm mình
     * - kế toán xem toàn bộ
     */

    if (!defined('NV_IS_SPADMIN')) {
        // các user mà sale làm trưởng nhóm
        $caregiver_id_leads = array();
        $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_crmbidding_groups_users WHERE userid = ' . $admin_info['userid'];
        $result = $db->query($sql);
        $admin_config = [];
        while ($row_groups_users = $result->fetch()) {
            $admin_config = json_decode($row_groups_users['config'], true);
            if ($row_groups_users['is_leader'] == 1) {
                $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_crmbidding_groups_users WHERE group_id = ' . $row_groups_users['group_id'] . ' AND userid != ' . $admin_info['userid'];
                $_result = $db->query($_sql);
                while ($_row_groups_users = $_result->fetch()) {
                    $caregiver_id_leads[$_row_groups_users['userid']] = $_row_groups_users['userid'];
                }
            }
        }
        $caregiver_id_leads[$admin_info['userid']] = $admin_info['userid'];
        if (!empty($caregiver_id_leads) and (!isset($admin_config['show_chart']) or $admin_config['show_chart'] == 0)) { // sale leader
            $where[] = '  (tb1.adminid IN (' . implode(',', $caregiver_id_leads) . ') OR tb1.customer_id IN (' . implode(',', $caregiver_id_leads) . ') OR tb1.caregiver_id IN (' . implode(',', $caregiver_id_leads) . ') OR tb1.id_sale_static IN (' . implode(',', $caregiver_id_leads) . '))';
        }
    }
}

$db->sqlreset();
$db->select('COUNT(*) as num, SUM(money_total) as sum');
$db->from($db_config['prefix'] . '_' . $module_data . '_transaction tb1');
$db->join('LEFT JOIN ' . NV_USERS_GLOBALTABLE . ' tb2 ON tb1.adminid=tb2.userid LEFT JOIN ' . NV_USERS_GLOBALTABLE . ' tb3 ON tb1.userid=tb3.userid LEFT JOIN ' . NV_USERS_GLOBALTABLE . ' tb4 ON tb1.customer_id=tb4.userid');

if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}

$transaction = $db->query($db->sql())
    ->fetch();
$all_page = $transaction['num'];
$sum_trans = $transaction['sum'];

$db->order('is_expired ASC, IF(transaction_time=0,created_time,transaction_time) DESC');
$db->limit($per_page);
$db->offset(($page - 1) * $per_page);
$db->select('tb1.*, tb2.username admin_transaction, tb3.username accounttran, tb4.username customer_transaction');
$result = $db->query($db->sql());
$xuatra = $congvao = 0;
$arr_list_transaction = [];
while ($row = $result->fetch()) {
    if ($row['status'] == -1) {
        $xuatra = $row['money_total'] + $xuatra;
    } else {
        $congvao = $row['money_total'] + $congvao;
    }
    $arr_list_transaction[$row['id']] = [
        'id' => $row['id'], //
        'code' => empty($row['order_id']) ? sprintf('GD%010s', $row['id']) : sprintf('WP%010s', $row['id']),
        'created_time' => date('d/m/Y H:i', $row['created_time']), //
        'status' => ($row['status'] == 1) ? '+' : '-', //
        'money_unit' => $row['money_unit'], //
        'money_total' => get_display_money($row['money_total']), //
        'money_net' => get_display_money($row['money_net']), //
        'accounttran' => empty($row['accounttran']) ? 'N/A' : $row['accounttran'], //
        'userid' => $row['userid'],
        'caregiver_id' => $row['caregiver_id'],
        'caregiver' => isset($array_user_id_users[$row['caregiver_id']]) ? nv_show_name_user($array_user_id_users[$row['caregiver_id']]['first_name'], $array_user_id_users[$row['caregiver_id']]['last_name'], $array_user_id_users[$row['caregiver_id']]['username']) : '',
        'id_sale_static' => $row['id_sale_static'],
        'sale_static' => isset($array_user_id_users[$row['id_sale_static']]) ? nv_show_name_user($array_user_id_users[$row['id_sale_static']]['first_name'], $array_user_id_users[$row['id_sale_static']]['last_name'], $array_user_id_users[$row['id_sale_static']]['username']) : '',
        'tran_uid' => $row['adminid'] ? $row['adminid'] : $row['customer_id'], // Tài khoản thực hiện giao dịch
        'tran_uname' => ($row['admin_transaction'] ? '<strong>' . $row['admin_transaction'] . '</strong>' : ($row['customer_transaction'] ? $row['customer_transaction'] : 'N/A')), // Tên người thực hiện giao dịch
        'customer_name' => $row['customer_name'], //
        'customer_email' => $row['customer_email'], //
        'customer_phone' => $row['customer_phone'], //
        'customer_address' => $row['customer_address'], //
        'customer_info' => $row['customer_info'], //
        'transaction_id' => $row['transaction_id'], //
        'transaction_status' => $row['transaction_status'], //
        'transaction_time' => $row['transaction_time'], //
        'transaction_data' => $row['transaction_data'], //
        'payment' => $row['payment'], //
        'source_money' => $row['source_money'], //
        'is_expired' => $row['is_expired'], //
        'view_user' => NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "&amp;userid=" . $row['userid'], //
        'view_transaction' => NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=viewtransaction&amp;id=" . $row['id'] //
    ];
}

$sum = -$xuatra + $congvao;
$sum = get_display_money($sum);

$i = ($page - 1) * $per_page + 1;
foreach ($arr_list_transaction as $element) {
    $xtpl->assign('stt', $i);
    $xtpl->assign('CONTENT', $element);
    if (!empty($element['is_expired'])) {
        $xtpl->assign('TRANSACTION_STATUS', $nv_Lang->getModule('transaction_expired'));
        $xtpl->parse('main.loop.transaction_status1');
    } elseif ($element['transaction_status'] != 4 and ($IS_FULL_ADMIN or !empty($PERMISSION_ADMIN['is_mtransaction']))) {
        foreach ($global_array_transaction_status as $key => $value) {
            $xtpl->assign('OPTION', [
                'key' => $key,
                'title' => $value,
                'selected' => ($key == $element['transaction_status']) ? ' selected="selected"' : '',
                'disabled' => ($key == 0) ? ' disabled="disabled"' : ''
            ]);
            $xtpl->parse('main.loop.transaction_status.loops');
        }
        $xtpl->parse('main.loop.transaction_status');
    } else {
        $xtpl->assign('TRANSACTION_STATUS', isset($global_array_transaction_status[$element['transaction_status']]) ? $global_array_transaction_status[$element['transaction_status']] : 'N/A');
        $xtpl->parse('main.loop.transaction_status1');
    }

    if ($element['status'] == '+') {
        foreach ($arr_source_money as $key => $value) {
            $xtpl->assign('OPTION', [
                'key' => $key,
                'title' => $value,
                'selected' => ($key == $element['source_money']) ? ' selected="selected"' : ''
            ]);
            $xtpl->parse('main.loop.source_money.loops');
        }
        $xtpl->parse('main.loop.source_money');

        // check quyền sửa người chăm sóc
        if (defined('NV_IS_SPADMIN')) {
            foreach ($array_user_id_users as $key => $value) {
                $xtpl->assign('OPTION', [
                    'key' => $key,
                    'title' => nv_show_name_user($value['first_name'], $value['last_name'], $value['username']),
                    'selected' => ($key == $element['caregiver_id']) ? ' selected="selected"' : ''
                ]);
                $xtpl->parse('main.loop.caregiver_id.loops');
            }
            $xtpl->parse('main.loop.caregiver_id');
        } else {
            $xtpl->parse('main.loop.caregiver_id1');
        }
    } else {
        // check quyền sửa người chăm sóc
        if (defined('NV_IS_SPADMIN')) {

            foreach ($array_user_id_users as $key => $value) {
                $xtpl->assign('OPTION', [
                    'key' => $key,
                    'title' => nv_show_name_user($value['first_name'], $value['last_name'], $value['username']),
                    'selected' => ($key == $element['id_sale_static']) ? ' selected="selected"' : ''
                ]);
                $xtpl->parse('main.loop.id_sale_static.loops');
            }
            if ($element['id_sale_static'] == 0) {
                $xtpl->assign('id_sale_static0', ' selected="selected"');
            }

            $xtpl->parse('main.loop.id_sale_static');
        } else {
            $xtpl->parse('main.loop.id_sale_static1');
        }
    }

    $xtpl->parse('main.loop');
    $i++;
}

$generate_page = nv_generate_page($base_url, $all_page, $per_page, $page);
if ($generate_page) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}

$xtpl->assign('sum', $all_page);
$xtpl->assign('sum_page', $sum);
$xtpl->assign('sum_money', number_format($sum_trans));

$array_search['crf'] = empty($array_search['crf']) ? '' : nv_date('d.m.Y', $array_search['crf']);
$array_search['crt'] = empty($array_search['crt']) ? '' : nv_date('d.m.Y', $array_search['crt']);
$array_search['trf'] = empty($array_search['trf']) ? '' : nv_date('d.m.Y', $array_search['trf']);
$array_search['trt'] = empty($array_search['trt']) ? '' : nv_date('d.m.Y', $array_search['trt']);
$xtpl->assign('DATA_SEARCH', $array_search);

if (!empty($view_user_info)) {
    $xtpl->assign('VIEW_USER_NAME', $view_user_info['username']);
    $xtpl->assign('VIEW_USER_CANCEL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);
    $xtpl->parse('main.view_user_info');
}

if (!empty($view_order_info)) {
    $xtpl->assign('VIEW_ORDER_NAME', sprintf('DH%010s', $view_order_info['id']));
    $xtpl->assign('VIEW_ORDER_CANCEL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);
    $xtpl->parse('main.view_order_info');
}

if ($isSearchSubmit) {
    $xtpl->assign('COLLAPSE1', 'true');
    $xtpl->assign('COLLAPSE2', ' in');
} else {
    $xtpl->assign('COLLAPSE1', 'false');
    $xtpl->assign('COLLAPSE2', '');
}

// Xuất phạm vi tìm
foreach ($array_fields_search as $k => $v) {
    $fields_search = [
        'key' => $k,
        'title' => $v['title'],
        'selected' => $k == $array_search['are'] ? ' selected="selected"' : ''
    ];
    $xtpl->assign('FIELDS_SEARCH', $fields_search);
    $xtpl->parse('main.fields_search');
}

foreach ($arr_fields_trans as $k => $v) {
    $fields_search = [
        'key' => $k,
        'title' => $v['title'],
        'selected' => $k == $array_search['transacter_fields'] ? ' selected="selected"' : ''
    ];
    $xtpl->assign('FIELDS_TRANSACTER_SEARCH', $fields_search);
    $xtpl->parse('main.fields_transacter_search');
}

// Xuất kiểu tìm kiếm
foreach ([
    1 => $nv_Lang->getModule('search_type_affected'),
    2 => $nv_Lang->getModule('search_type_transacter')
] as $k => $v) {
    $types_search = [
        'key' => $k,
        'title' => $v,
        'selected' => $k == $array_search['type'] ? ' selected="selected"' : ''
    ];
    $xtpl->assign('TYPES_SEARCH', $types_search);
    $xtpl->parse('main.types_search');
}

$xtpl->assign('CURRENT_SEARCH_TYPE', 'search-type-' . ($array_search['type'] == 2 ? 'transacter' : 'affected'));

foreach ([
    1,
    -1
] as $val) {
    $st = [
        'key' => $val,
        'title' => $val == 1 ? $nv_Lang->getModule('transaction1') : $nv_Lang->getModule('transaction2'),
        'selected' => $val == $array_search['st'] ? ' selected="selected"' : ''
    ];
    $xtpl->assign('ST', $st);
    $xtpl->parse('main.st');
}
foreach ($global_array_money_sys as $row) {
    $money_sys = [
        'key' => $row['code'],
        'title' => $row['code'],
        'selected' => $row['code'] == $array_search['mo'] ? ' selected="selected"' : ''
    ];
    $xtpl->assign('MONEY_SYS', $money_sys);
    $xtpl->parse('main.money_sys');
}
foreach ([
    1,
    2
] as $row) {
    $aou = [
        'key' => $row,
        'title' => $nv_Lang->getModule('search_aou' . $row),
        'selected' => $row == $array_search['aou'] ? ' selected="selected"' : ''
    ];
    $xtpl->assign('AOU', $aou);
    $xtpl->parse('main.aou');
}
foreach ($global_array_transaction_type as $k => $v) {
    $tty = [
        'key' => $k,
        'title' => $v,
        'selected' => $k == $array_search['tty'] ? ' selected="selected"' : ''
    ];
    $xtpl->assign('TTY', $tty);
    $xtpl->parse('main.tty');
}
foreach ($global_array_transaction_status as $k => $v) {
    $tst = [
        'key' => $k,
        'title' => $v,
        'selected' => $k == $array_search['tst'] ? ' selected="selected"' : ''
    ];
    $xtpl->assign('TST', $tst);
    $xtpl->parse('main.tst');
}
foreach ($global_array_payments as $row) {
    $tpa = [
        'key' => $row['payment'],
        'title' => $row['paymentname'],
        'selected' => $row['payment'] == $array_search['tpa'] ? ' selected="selected"' : ''
    ];
    $xtpl->assign('TPA', $tpa);
    $xtpl->parse('main.tpa');
}
for ($i = 1; $i <= 100; $i++) {
    $val = $i * 5;
    $per_page = [
        'key' => $val,
        'title' => $val,
        'selected' => $val == $array_search['per_page'] ? ' selected="selected"' : ''
    ];
    $xtpl->assign('PER_PAGE', $per_page);
    $xtpl->parse('main.per_page');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('transaction');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
