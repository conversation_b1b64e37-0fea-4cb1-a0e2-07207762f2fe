<!-- BEGIN: main -->
<script type="text/javascript">
	li = $(".timeline > li");
	for (i = 0; i < li.length; i++) {
		liClass = li.eq(i).attr('class');
		li.eq(i).attr("old-class", liClass.trim(" "));
	}

	$(document).ready(function() {
       var headerHeight = $('.navbar-inverse').outerHeight(true) + $('#header').outerHeight(true); // true value, adds margins to the total height
		var footerHeight = $('.panel__custom').innerHeight();
		$('#menu__customer').affix({
		    offset: {
		        top: headerHeight,
		        bottom: footerHeight
		    }
		})

		scrllMenu = $(".scroll__menu");
		height = 0;
		for (var i = 0; i < scrllMenu.length; i++) {
			id = scrllMenu.eq(i).attr('data-id');
			if ($(id).length < 1) {
				height += 40;
				scrllMenu.eq(i).hide();
			}
		}
		new_height = 360 - height;

		$(".scroll__menu").click(function(event) {
			id = $(this).attr('data-id');
			$('html, body').animate({scrollTop: $(id).offset().top - 20}, 500);
		});

		const navigation = document.querySelector('.navigation_menu');
		const menu__customer = document.querySelector('#menu__customer');
		document.querySelector('.toggle_menu').onclick = function() {
			this.classList.toggle('active');
			navigation.classList.toggle('active');
			menu__customer.classList.toggle('active');
			if ($(".navigation_menu.active").length > 0) {
				$(".navigation_menu.active").css({
					'height' : new_height
				})
			} else {
				$(".navigation_menu").css({
					'height' : '40px'
				})
			}
		}

		if (window.matchMedia("(max-width: 768px)").matches) {
			$(".timeline").addClass('timeline_mobile');
			li = $(".timeline > li");
			for (i = 0; i < li.length; i++) {
				liClass = li.eq(i).attr('class');
				if (li.eq(i).attr('old-class') != "li__time_custom") {
					if (liClass.search("timeline-inverted") === -1) {
						li.eq(i).addClass('timeline-inverted');
					}
				}
			}
		} else {
 			$(".timeline").removeClass('timeline_mobile');
			li = $(".timeline > li");
 			for (i = 0; i < li.length; i++) {
 				liClass = li.eq(i).attr('class');
				li.eq(i).removeClass(liClass);
				li.eq(i).addClass(li.eq(i).attr("old-class"))
			}
		}

		$(window).resize(function() {
            var width = $(window).width();
 			if (width < 768){
 				$(".timeline").addClass('timeline_mobile');
	 			li = $(".timeline > li");
				for (i = 0; i < li.length; i++) {
					liClass = li.eq(i).attr('class');
					if (li.eq(i).attr('old-class') != "li__time_custom") {
						if (liClass.search("timeline-inverted") === -1) {
							li.eq(i).addClass('timeline-inverted');
						}
					}
				}
 			} else {
 				$(".timeline").removeClass('timeline_mobile');
 				li = $(".timeline > li");
	 			for (i = 0; i < li.length; i++) {
	 				liClass = li.eq(i).attr('class');
					li.eq(i).removeClass(liClass);
					li.eq(i).addClass(li.eq(i).attr("old-class"))
				}
 			}
 		});
	});
</script>
<!-- BEGIN: error-->
<div class="alert alert-warning">
	{ERROR}
</div>
<!-- END: error-->
<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<div id="menu__customer" class="affix-top">
	<div class="navigation_menu">
		<div class="toggle_menu"></div>
		<ul>
			<li class="scroll__menu" data-id="#dsdv">
				<a href="#">
					<span class="icon"><i class="fa-solid fa-house"></i></span>
					<span class="title">{LANG.list_vip}</span>
				</a>
			</li>

			<li class="scroll__menu" data-id="#ttcbkh">
				<a href="#">
					<span class="icon"><i class="fa-solid fa fa-newspaper-o" aria-hidden="true"></i></span>
					<span class="title">{LANG.name_info_customer}</span>
				</a>
			</li>

			<li class="scroll__menu" data-id="#dsdh">
				<a href="#">
					<span class="icon"><i class="fa-solid fa fa-list-alt" aria-hidden="true"></i></span>
					<span class="title">{LANG.list_order}</span>
				</a>
			</li>

			<li class="scroll__menu" data-id="#sttv">
				<a href="#">
					<span class="icon"><i class="fa fa-solid fa-life-ring" aria-hidden="true"></i></span>
					<span class="title">{LANG.sotientrongvi}</span>
				</a>
			</li>

			<li class="scroll__menu" data-id="#sdkh">
				<a href="#">
					<span class="icon"><i class="fa fa-solid fa-asterisk" aria-hidden="true"></i></span>
					<span class="title">{LANG.point_customer}</span>
				</a>
			</li>

			<li class="scroll__menu" data-id="#htkh">
				<a href="#">
					<span class="icon"><i class="fa fa-solid fa-binoculars" aria-hidden="true"></i></span>
					<span class="title">{LANG.support_customer}</span>
				</a>
			</li>
			<li class="scroll__menu" data-id="#support_ticket">
				<a href="#">
					<span class="icon"><i class="fa fa-solid fa-binoculars" aria-hidden="true"></i></span>
					<span class="title">{LANG.menu_support_ticket}</span>
				</a>
			</li>

			<li class="scroll__menu" data-id="#lskh">
				<a href="#">
					<span class="icon"><i class="fa-solid fa-address-card"></i></span>
					<span class="title">Lịch sử hỗ trợ khách hàng</span>
				</a>
			</li>

			<li class="scroll__menu" data-id="#tthtkh">
				<a href="#">
					<span class="icon"><i class="fa-solid fa-screwdriver-wrench"></i></span>
					<span class="title">Hành trình khách hàng</span>
				</a>
			</li>
		</ul>
	</div>
</div>
<div id="detail_customer" class="responsivetb">
	<div class="row">
		<div class="col-md-24 hidden-lg hidden-md panel__custom">
			<div class="panel panel-info">
				<div class="panel-heading">
					<h3 class="panel-title btn__downleft ttKHTop"><i class="fa fa-bookmark" aria-hidden="true"></i>  {LANG.name_info_customer} <i class="fa fa-angle-double-right icon" aria-hidden="true"></i></h3>
				</div>
				<div class="panel-body" id="ttKHTop">
					<p><span><b>{LANG.fullname}:</b></span> <span><a href="{LINK.editUser}" data-toggle="tooltip" title="Thông tin tài khoản">{USER.fullname} <i class="fa fa-user" aria-hidden="true"></i></a></span></p>
					<p><span><b>{LANG.taikhoan}:</b></span> <span class="text-primary">{USER.username} </span></p>
					<p><span><b>{LANG.birthday}:</b></span> <span>{USER.birthday}</span></p>
					<p><span><b>{LANG.email}:</b></span> <span class="span__data"><a href ="mailto:{USER.email}">{USER.email}</a></span></p>
					<p><span><b>{LANG.phone}:</b></span> <span class="span__data"><a href ="tel:{USER.phone}">{USER.phone}</a></span></p>
					<p><span><b>{LANG.tax}:</b></span> <span class="span__data text-warning">{USER.mst}</span></p>
				</div>
			</div>
		</div>

		<div class="col-md-18">
			<div class="panel panel-default panel__success_left panel__custom">
				<div class="panel-heading">
					<h3 class="panel-title slideDiv" id="ttdhkh"><i class="fa fa-bookmark" aria-hidden="true"></i> {LANG.info_order_customer} <i class="fa fa-angle-double-right" aria-hidden="true"></i></h3>
				</div>

				<div class="panel-body">
					<!-- BEGIN: show_email_die-->
					<div class="alert alert-warning">
						<h4><i class="fa fa-exclamation-triangle" aria-hidden="true"></i> {LANG.title_email_die}</h4>
						<!-- BEGIN: loop-->
							<p><i class="fa fa-envelope-o" aria-hidden="true"></i> {VALUE.email} -- <span>{LANG.nguyennhan}:</span> <span>{VALUE.comments}</span></p>
						<!-- END: loop-->
					</div>
					<!-- END: show_email_die-->

					<!-- BEGIN: show_waring_filter-->
					<div class="alert alert-warning">
						<h4><i class="fa fa-exclamation-triangle" aria-hidden="true"></i> {WARING_FILTER}</h4>
					</div>
					<!-- END: show_waring_filter-->

					<!-- BEGIN: show_waring_filter_email-->
					<div class="alert alert-warning">
						<h4><i class="fa fa-exclamation-triangle" aria-hidden="true"></i> {WARING_FILTER_EMAIL}</h4>
					</div>
					<!-- END: show_waring_filter_email-->

					<div class="info_product ttdhkh">
						<!-- BEGIN: view_ordervip -->
							<h2 class="title_list listVIP show_api_list_vip" id="dsdv">{LANG.list_vip} <i class="fa fa-angle-double-right" aria-hidden="true"></i></h2>
							<div id="listVIP">
								<table class="table table-bordered" id="table1">
									<caption>{LANG.service_dtinfo}</caption>
									<thead>
										<tr>
											<th class="text-center" width="5%">{LANG.stt}</th>
											<th width="13%">{LANG.box_vip}</th>
											<th width="18%">{LANG.name_time_from}</th>
											<th width="18%">{LANG.time_noteactive}</th>
											<th class="text-center" width="12%">{LANG.status}</th>
											<th class="text-center"> <a class="show_detail">{LANG.showall} <i class="fa fa-angle-double-right" aria-hidden="true"></i></a></th>
										</tr>
									</thead>
									<tbody>
										<!-- BEGIN: loop -->
										<tr>
											<td scope="row" data-label="{LANG.stt}" class="text-center">{ORDER.stt}</td>
											<td data-label="{LANG.box_vip}">
												<a href="{ORDER.link_vip}" data-toggle="tooltip" title="Thông tin gói {ORDER.vip}"><span class="text--weight info_order__vip span__data">{ORDER.vip}</span></a>
											</td>
											<td data-label="{LANG.from_time}"> <span>{ORDER.from_time}</span></td>
											<td data-label="{LANG.time_noteactive}"> <span>{ORDER.end_time}</span></td>
											<td class="text-center" data-label="{LANG.status}">
												<span class="{ORDER.class_status} label_responsive">{ORDER.status}</span>
											</td>
											<td class="text-center">
												<p>
													<a href="#" class="viewmore">Xem thêm <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
												</p>
												<div class="view_ordervip text-left">
												<p>
				                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
				                                    <span>{LANG.name_deal}:</span> <span class=" text--weight span__data">{ORDER.deal_price}</span>
				                                </p>
				                                <p>
				                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
				                                    <span>{LANG.name_sum_viptime}:</span> <span class="text--weight span__data">{ORDER.sum_viptime}</span>
				                                </p>
				                                <p>
				                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
				                                    <span>{LANG.tax}:</span> <span class="info_order__tax text--weight span__data">{ORDER.tax}</span>
				                                </p>
				                                <p>
				                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
				                                    <span>{LANG.contact_to}: </span> <span class="info_order__contact-to text--weight span__data">{ORDER.contact_to}</span>
				                                </p>


				                                <p>
				                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
				                                    <span>{LANG.deal_time}:</span> <span class="text--weight span__data">{ORDER.deal_time}</span>
				                                </p>

				                                <p>
				                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
				                                    <span>{LANG.phone_tin}:</span> <span class="info_order__phone span__data"><a href="tel:{ORDER.phone} text--weight">{ORDER.phone}</a></span>
				                                </p>
				                                <p>
				                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
				                                    <span>{LANG.contact_phone}:</span> <span class="info_order__contact_phone text--weight span__data"><a href="tel:{ORDER.contact_phone}">{ORDER.contact_phone}</a></span>
				                                </p>
				                                <p>
				                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
				                                    <span>{LANG.email_tin}: </span> <span class="info_order__email text--weight span__data"> <b>{ORDER.email}</b></span>
				                                </p>
				                                <p>
				                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
				                                    <span class="info_order__sub_email">{LANG.sub_email}: </span> <span class="text--weight span__data">{ORDER.sub_email}</span>
				                                </p>

				                                <p>
				                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
				                                    <span>{LANG.address_org}: </span> <span class="info_order__address-org span__data">{ORDER.address_org}</span>
				                                </p>

					                                <a href="{ORDER.link_vip}" class="btn btn-primary btn--social btn-xs"><i class="fa fa-eye" aria-hidden="true"></i> {LANG.detail_vip}</a>
												</div>
											</td>
										</tr>
										<!-- END: loop -->

									</tbody>

								</table>
							</div>
						<!-- END: view_ordervip -->
							<div id="show_api_list_vip"></div>
						<!-- BEGIN: view_order -->
							<hr>
							<h2 class="title_list listOrder show_api_list_payment" id="dsdh">{LANG.list_order} <i class="fa fa-angle-double-right" aria-hidden="true"></i></h2>
							<div id="listOrder">
								<table class="table table-bordered">
									<caption>{LANG.order_dtinfo}</caption>
									<thead>
										<tr>
											<th class="text-center">{LANG.stt}</th>
											<th width="25%">{LANG.log_data}</th>
											<th class="text-center">{LANG.from_time}</th>
											<th class="text-center">{LANG.status}</th>
											<th >{LANG.name_caregiver_id}</th>
											<th class="text-center" width="200px"><a class="show_detail_order">{LANG.showall} <i class="fa fa-angle-double-right" aria-hidden="true"></i></a></th>
										</tr>
									</thead>
									<tbody>
										<!-- BEGIN: loop -->
										<tr>
											<td data-label="{LANG.stt}" class="text-center">{ORDER.stt}</td>
											<td data-label="{LANG.log_data}">
												<span class="ttnddh">{ORDER.title}:
													<strong class="text-danger">{ORDER.money}</strong>
													<span class="olist-promo text-muted">{ORDER.promo_code}</span>
												</span>
						                    </td>
											<td class="text-center" data-label="{LANG.from_time}">
												<span class="text--weight span__data">{ORDER.add_time}</span>
											</td>
											<td class="text-center" data-label="{LANG.status}">
												<span class="text--weight span__data {ORDER.class_expired}">{ORDER.name_status}</span>
												<!-- <span class="text--weight span__data {ORDER.class_expired}">{ORDER.is_expired}</span> -->
											</td>
											<td data-label="{LANG.name_caregiver_id}">
												<span>{ORDER.name_caregiver_id}</span>
											</td>
											<td class="text-center">
												<p>
													<a href="#" class="viewmore">Xem thêm <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
												</p>
												<div class="view_ordervip text-left">
					                                <p>
					                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
					                                    <span>{LANG.name_tthd}:</span> <span class="text--weight">{ORDER.money}</span>
					                                </p>

					                                <p>
					                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
					                                    <span>{LANG.source_money}:</span> <span class="text--weight label label-success">{ORDER.name_source_money}</span>
					                                </p>

					                                <p>
					                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
					                                    <span>{LANG.promo_code}:</span> <span class="text--weight span__data">{ORDER.promo_code}</span>
					                                </p>
					                                <p>
					                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
					                                    <span>{LANG.promo_type}:</span> <span class="text--weight span__data">{ORDER.promo_type}</span>
					                                </p>

					                                <p>
					                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
					                                    <span>{LANG.discount}:</span> <span class="text--weight span__data">{ORDER.discount}</span>
					                                </p>
					                                <p>
					                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
					                                    <span>{LANG.price_reduce}:</span> <span class="text--weight span__data">{ORDER.price_reduce}</span>
					                                </p>

					                                <p>
					                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
					                                    <span>{LANG.total_end}:</span> <span class="text--weight span__data">{ORDER.total_end}</span>
					                                </p>
					                                <p>
					                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
					                                    <span>{LANG.name_admin_id}:</span> <span class="text--weight span__data">{ORDER.name_admin_id}</span>
					                                </p>
					                                <p>
					                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
					                                    <span>{LANG.name_caregiver_id}:</span> <span class="text--weight span__data">{ORDER.name_caregiver_id}</span>
					                                </p>
					                                <p>
					                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
					                                    <span>{LANG.name_affiliate_userid}:</span> <span class="text--weight span__data">{ORDER.name_affiliate_userid}</span>
					                                </p>
					                                <p>
					                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
					                                    <span>{LANG.name_promo_userid}:</span> <span class="text--weight span__data">{ORDER.name_promo_userid}</span>
					                                </p>

					                                <p>
					                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
					                                    <span>{LANG.edit_time_bill}:</span> <span class="text--weight span__data">{ORDER.edit_time}</span>
					                                </p>
					                                <p>
					                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
					                                    <span>{LANG.total}:</span> <span class="text--weight span__data">{ORDER.total}</span>
					                                </p>
					                                <p>
					                                    <i class="fa fa-caret-right" aria-hidden="true"></i>
					                                    <span>{LANG.status}:</span> <span class="text--weight label label-warning {ORDER.status_bg}">{ORDER.name_status}</span>
					                                </p>

					                                <a href="{ORDER.link_order}" class="btn btn-warning btn--social btn-xs" ><i class="fa fa-eye" aria-hidden="true"></i> {LANG.detail_order}</a>
												</div>
											</td>
										</tr>
										<!-- END: loop -->
									</tbody>
								</table>
							</div>
						<!-- END: view_order -->
						<div id="show_api_list_payment"></div>
					</div>
				</div>
			</div>

			<div id="show_api_list_profile"></div>

			<div id="show_api_list_ticket"></div>

			<!-- BEGIN: show_support -->
			<div class="main_support hidden-xs hidden-sm" id="lskh">
				{HISTORY_SUPPORT}
			</div>
			<!-- END: show_support -->

			<div class="panel panel-danger panel__htkh panel__custom">
				<div class="panel-heading">
					<h3 class="panel-title slideDiv" id="tthtkh"><i class="fa fa-bookmark" aria-hidden="true"></i> Thông tin hành trình của khác hàng <i class="fa fa-angle-double-right" aria-hidden="true"></i></h3>
				</div>
				<div class="panel-body">
					<div class="box_timeline tthtkh" id="info">
						<div class="header">
			                <h2 class="showleadsduplicate col-md-10 col-xs-10">
			                    <a href="#"> <span><i class="fa fa-caret-right" aria-hidden="true"></i> Hành trình khách hàng <i class="fa fa-angle-double-down"></i> <i class="fa fa-angle-double-up hidden"></i></span></a>
			                </h2>
			                <h2 class="col-md-14 text-right show_log col-xs-14">
			                    <a href="#" class="show_all_log"> <span><i class="fa fa-eye" aria-hidden="true"></i> Hiển thị điểm chạm không quan trọng</span></a> <a href="#" class="not_show_all_log hidden"> <span><i class="fa fa-eye-slash" aria-hidden="true"></i> Ẩn điểm chạm không quan trọng</span></a>
			                </h2>
					    </div>
					    <ul class="timeline">
					    	<!-- BEGIN: timline -->
						    	<!-- BEGIN: show_line -->
									<li class="li__time_custom">
										<div class="{CLASS}" id="{ID}"><i class="fa fa-ellipsis-h" aria-hidden="true"></i></div>
									</li>
						    	<!-- END: show_line -->
								<li class="{TIMELINE.class_li} not_display{TIMELINE.not_display}" class-hide='{TIMELINE.class_hide}'>
									<div class="timeline-badge {TIMELINE.class}">{TIMELINE.icon}</div>
			                        <div class="timeline-panel">
			                            <div class="timeline-heading">
			                                <h3 class="timeline-title">
			                                    {TIMELINE.title} <a data-toggle="collapse" href="#history{TIMELINE.id}" aria-expanded="false" aria-controls="history{TIMELINE.id}"><i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
			                                </h3>
			                            </div>
			                            <div class="timeline-body collapse" id="history{TIMELINE.id}">
                                            <div class="info-body">
                                                {TIMELINE.body}
                                            </div>
			                            </div>
			                        </div>
								</li>
					    	<!-- END: timline -->
					    </ul>
					</div>
				</div>
			</div>
		</div>

		<div class="col-md-6">
			<div class="panel panel-info hidden-xs hidden-sm panel__custom">
				<div class="panel-heading">
					<h3 class="panel-title btn__downleft ttKH" id="ttcbkh"><i class="fa fa-bookmark" aria-hidden="true"></i>  {LANG.name_info_customer} <i class="fa fa-angle-double-right icon" aria-hidden="true"></i></h3>
				</div>
				<div class="panel-body" id="ttKH">
					<p><span><b>{LANG.fullname}:</b></span> <span><a href="{LINK.editUser}" data-toggle="tooltip" title="Thông tin tài khoản">{USER.fullname} <i class="fa fa-user" aria-hidden="true"></i></a></span></p>
					<p><span><b>{LANG.taikhoan}:</b></span> <span class="text-primary">{USER.username} </span></p>
					<p><span><b>{LANG.birthday}:</b></span> <span>{USER.birthday}</span></p>
					<p><span><b>{LANG.email}:</b></span> <span class="span__data"><a href ="mailto:{USER.email}">{USER.email}</a></span></p>
					<p><span><b>{LANG.phone}:</b></span> <span class="span__data"><a href ="tel:{USER.phone}">{USER.phone}</a></span></p>
					<p><span><b>{LANG.tax}:</b></span> <span class="span__data text-warning">{USER.mst}</span></p>
				</div>
			</div>

			<div class="panel panel-info panel__custom">
				<div class="panel-heading">
					<h3 class="panel-title btn__downleft socialList"><i class="fa fa-bookmark" aria-hidden="true"></i>  {LANG.social_network} <i class="fa fa-angle-double-right icon" aria-hidden="true"></i></h3>
				</div>
				<div class="panel-body" id="socialList">
					<div class="social-network">
						<table class="table table-bordered" id="table_profile" style="display:{DISPLAY_PROFILE};">
							<thead>
								<tr>
									<th width="40%">{LANG.social_profile_name}</th>
									<th class="text-center" width="30%">{LANG.social_platform}</th>
									<th width="30%"></th>
								</tr>
							</thead>
							<tbody id="social-network-list">
								<!-- BEGIN: social_profile -->
								<tr data-id="{PROFILE.id}">
									<td data-label="{LANG.social_profile_name}"><a href="{PROFILE.profile_url}" data-platform="{PROFILE.platform}">{PROFILE.profile_name}</a></td>
									<td data-label="{LANG.social_platform}" class="text-center platform">{PROFILE.platform_label}</td>
									<td class="text-center">
										<button class="edit btn btn-warning btn-xs btn--social" data-id="{PROFILE.id}">{LANG.edit}</button>
										<button class="delete btn btn-danger btn-xs btn--social" data-id="{PROFILE.id}">{LANG.delete}</button>
									</td>
								</tr>
								<!-- END: social_profile -->
							</tbody>
						</table>
						<button id="add_social_network" class="btn btn-primary btn-sm btn--social"><i class="fa fa-plus"></i> {LANG.social_add}</button>
						<form id="social_network_form" class="form-horizontal" style="display: none; margin-top: 20px;">
							<div class="form-group">
								<div class="col-sm-14">
									<label for="profile_name" class="control-label">{LANG.social_profile_name}</label>
									<input type="text" class="form-control" id="profile_name" placeholder="{LANG.social_profile_name_placeholder}" required>
								</div>
								<div class="col-sm-10">
									<label for="platform" class="control-label">{LANG.social_platform}</label>
									<select id="platform" class="form-control">
										<!-- BEGIN: social_platform -->
										<option value="{SOCIAL.key}">{SOCIAL.value}</option>
										<!-- END: social_platform -->
									</select>
								</div>
							</div>
							<div class="form-group">
								<div class="col-sm-24">
									<label for="profile_url" class="control-label">{LANG.social_profile_url}</label>
									<input type="text" class="form-control" id="profile_url" placeholder="{LANG.social_profile_url_placeholder}" required>
								</div>
							</div>
							<input type="hidden" id="edit_id" value="">
							<div class="form-group">
								<div class="col-sm-24">
									<button type="submit" id="save_profile" class="btn btn-primary btn-sm btn--social">{LANG.save}</button>
									<button type="button" id="cancel_profile" class="btn btn-default btn-sm btn--social">{LANG.cancel}</button>
									<div class="error" id="error_message" style="display: none;"></div>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>

			<!-- Tổng giá trị đơn hàng -->
			<div class="panel panel-success panel__custom">
				<div class="panel-heading">
					<h3 class="panel-title btn__downleft ttMoney1" id="gtdh"><i class="fa fa-bookmark" aria-hidden="true"></i>  {LANG.sotiendonhang} <i class="fa fa-angle-double-right icon" aria-hidden="true"></i></h3>
				</div>
				<div class="panel-body" id="ttMoney1">
					<div class="panel__money">
						<p>
							<i class="fa fa-caret-right" aria-hidden="true"></i>
							<span>{LANG.ttgiadichvu}:</span> <span class="text--weight money--total">{SUM_TOTAL.money}</span>
						</p>

						<p>
							<i class="fa fa-caret-right" aria-hidden="true"></i>
							<span data-toggle="tooltip" title="{LANG.label_name_money_pay}">{LANG.name_money_pay}:</span> <span class="text--weight money--total">{SUM_TOTAL.money_pay}</span>
						</p>

						<p>
							<i class="fa fa-caret-right" aria-hidden="true"></i>
							<span>{LANG.name_sum_total}:</span> <span class="text--weight money--total">{SUM_TOTAL.amount_paid}</span>
						</p>


						<p>
							<i class="fa fa-caret-right" aria-hidden="true"></i>
							<span>{LANG.ttchietkhau}:</span> <span class="text--weight money--total">{SUM_TOTAL.price_reduce}</span>
						</p>

						<p>
							<i class="fa fa-caret-right" aria-hidden="true"></i>
							<span>{LANG.ttthuegiaodich}:</span> <span class="text--weight money--total">{SUM_TOTAL.taxes_fees}</span>
						</p>


						<p>
							<i class="fa fa-caret-right" aria-hidden="true"></i>
							<span>{LANG.ttgiamgia}:</span> <span class="text--weight money--total">{SUM_TOTAL.discount}</span>
						</p>

						<p>
							<i class="fa fa-caret-right" aria-hidden="true"></i>
							<span>{LANG.ttthucnhan}:</span> <span class="text--weight money--total">{SUM_TOTAL.total_end}</span>
						</p>

						<a class="text-center" target="_blank" href="{LINK.order}"><i class="fa fa-list-ul" aria-hidden="true"></i> {LANG.list_order}</a>
					</div>
				</div>
			</div>

			<!-- Số tiền của khách -->
			<div class="panel panel-warning panel__custom">
				<div class="panel-heading">
					<h3 class="panel-title btn__downleft ttMoney" id="sttv"><i class="fa fa-bookmark" aria-hidden="true"></i> {LANG.sotientrongvi} <i class="fa fa-angle-double-right icon" aria-hidden="true"></i></h3>
				</div>
				<div class="panel-body" id="ttMoney">
					<div class="panel__money">
						<p>
							<i class="fa fa-caret-right" aria-hidden="true"></i>
							<span>{LANG.total_amount_deposited}:</span> <span class="text--weight money--total">{SUM_TOTAL.loaded}</span>
						</p>

						<p>
							<i class="fa fa-caret-right" aria-hidden="true"></i>
							<span>{LANG.sotiendatieu}:</span> <span class="text--weight money--total">{MONEY.money_out}</span>
						</p>

						<p>
							<i class="fa fa-caret-right" aria-hidden="true"></i>
							<span>{LANG.ttconlai}:</span> <span class="text--weight money--total">{MONEY.money_total}</span>
						</p>

						<p>
							<a href="{LINK.link_money}" target="_blank">{LANG.thongtinvitien}</a>
						</p>
					</div>
				</div>
			</div>

			<!-- Số điểm của khách -->
			<div class="panel panel-default panel--info panel__custom">
				<div class="panel-heading">
					<h3 class="panel-title btn__downleft ttPoint" id="sdkh"><i class="fa fa-bookmark" aria-hidden="true"></i>  {LANG.point_customer} <i class="fa fa-angle-double-right icon" aria-hidden="true"></i></h3>
				</div>
				<div class="panel-body" id="ttPoint">
					<div class="panel__point">
						<p>
							<i class="fa fa-caret-right" aria-hidden="true"></i>
							<span>{LANG.point_in}:</span> <span class="text--weight point--total">{POINT.point_in}</span>
						</p>

						<p>
							<i class="fa fa-caret-right" aria-hidden="true"></i>
							<span>{LANG.point_out}:</span> <span class="text--weight point--total">{POINT.point_out}</span>
						</p>

						<p>
							<i class="fa fa-caret-right" aria-hidden="true"></i>
							<span>{LANG.point_total}:</span> <span class="text--weight point--total"> {POINT.point_total}</span>
						</p>

						<p>
							<i class="fa fa-caret-right" aria-hidden="true"></i>
							<a href="{LINK.link_transaction_point}" target="_blank">{LANG.taogiaodich}</a></span>
						</p>

						<p>
							<a href="{LINK.link_point}" target="_blank"><i class="fa fa-history" aria-hidden="true"></i> {LANG.manage_point}</a>
						</p>
					</div>
				</div>
			</div>

			<!-- Hỗ trợ khách -->
			<div class="panel panel-default panel--info panel__custom" id="support_customer">
				<div class="panel-heading">
					<h3 class="panel-title" id="htkh"><i class="fa fa-bookmark" aria-hidden="true"></i>  {LANG.support_customer}</h3>
				</div>
				<div class="panel-body">
					<form method="post">
						<div class="support_customer">
							<p class="error"></p>
							<div class="form-group">
								<label><b>{LANG.content_note} (<span class="color--red">{LANG.neuco}</span>):</b></label>
								<textarea class="form-control" rows="10" id="content"></textarea>
								<textarea class="form-control" id="note" rows="10"></textarea>
								<input type="hidden" id="source">
							</div>
							<div class="form-group" id="selectVip">
								<label><b>{LANG.mount_vip} (<span class="color--red">{LANG.neuco}</span>) :</b></label>
								<!-- BEGIN: view_vip -->
								<select name="vip" id="vip" class="form-control">
									<option value="0_0">---{LANG.select_vip}---</option>
									<!-- BEGIN: loop -->
										<option value="{OPTION.key}">{OPTION.title}</option>
									<!-- END: loop -->
								</select>
								<!-- END: view_vip -->
							</div>

							<div class="form-group text-center">
								<button type="button" class="btn btn-default btn--social" id="huy">{LANG.cancel}</button>
								<button type="button" name="button" id="comment" class="btn btn-success btn--social" data-status='add' value="{USER.userid}" ><i class="fa fa-floppy-o" aria-hidden="true"></i> &nbsp;{LANG.note}</button>
							</div>
						</div>
					</form>
				</div>
			</div>

		</div>

		<div class="col-md-24 hidden-lg hidden-md">
			<!-- BEGIN: show_support1 -->
			<div class="main_support">
				{HISTORY_SUPPORT}
			</div>
			<!-- END: show_support1 -->
		</div>
	</div>
</div>

<div id="toast"></div>
<input type="hidden" value="{LANG.button_up_cm}" id="bt_up">
<input type="hidden" value="{LANG.note}" id="bt_note">
<script type="text/javascript" src="{LINK_FILE}js/datatables.min.js"></script>
<script type="text/javascript">
    function review_timeline() {
        // Xóa tất cả class = timeline-inverted có tác dụng làm so le
        $("ul.timeline li").removeClass("timeline-inverted");
        // Lấy tất cả li có class = not_display và class = showtimeline là những li sẽ hiện thị.
        let i = 0;
        let list_li = $("ul.timeline > li.not_display, ul.timeline > li.not_display0, ul.timeline > li.showtimeline");
        list_li.each(function(index, el) {
            i++;
            if (i % 2 == 0) {
                $(el).addClass("timeline-inverted");
            }
        })
    }
	$(document).ready(function() {
		history1 = $(".timeline-body .info-body")
		for(var i = 0; i < history1.length; i++) {
			if (history1.eq(i).text().trim(" ") == "") {
				id = history1.eq(i).parent().attr('id');
				$('a[href="#' + id + '"]').hide();
			}
		}

		li_notdisplay = $('.timeline > li');
		y = 0;
		dem = 0;
		for(var i = 0; i < li_notdisplay.length; i++) {
			if (li_notdisplay.eq(i).attr('class-hide') == "") {
				if (i > y) {
					x = li_notdisplay.eq(y).attr('class');
					z = li_notdisplay.eq(i).attr('class');
					if(x == z) {
						li_notdisplay.eq(i).css({
							'margin-top': '10px'
						});

						li_notdisplay.eq(y).css({
							// 'margin-top': '18px',
							// 'margin-bottom': '31px',
						})
					}
					y = i;
				}
			}
		}

		$(".hidentime").click(function(event) {
			id = $(this).attr("id");
			$("li[class-hide='" + id + "']").toggleClass('showtimeline');
			$(this).find('i').removeClass('fa fa-ellipsis-h');
			$(this).find('i').toggleClass('fa fa-eye-slash');
			if ($(this).find('i').attr('class') == 'fa') {
				$(this).find('i').addClass('fa fa-ellipsis-h');
			}
            review_timeline();
		});

		$('.show_log').click(function() {
	        if ($('.show_log .not_show_all_log').hasClass('hidden')) {
	        	$(".hidentime[id*='hide_not_important']").find('i').removeClass('fa fa-ellipsis-h');
	        	$(".hidentime[id*='hide_not_important']").find('i').addClass('fa fa-eye-slash');
	            $('.not_display1').addClass('showtimeline');
	            $('.not_show_all_log').removeClass('hidden');
	            $('.show_all_log').addClass('hidden');
	        } else {
	        	$(".hidentime[id*='hide_not_important']").find('i').removeClass('fa fa-eye-slash');
	        	$(".hidentime[id*='hide_not_important']").find('i').addClass('fa fa-ellipsis-h');
	            $('.not_display1').removeClass('showtimeline');
	            $('.not_show_all_log').addClass('hidden');
	            $('.show_all_log').removeClass('hidden');
	        }
            review_timeline();
	        return;
	    });
	    $('.showleadsduplicate').click(function() {
	        if ($('#info .collapse').hasClass('in')) {
	            $('#info .collapse').collapse('hide');
	            $('.fa-angle-double-down').removeClass('hidden');
	            $('.fa-angle-double-up').addClass('hidden');
	        } else {
	            $('#info .collapse').collapse('show');
	            $('.fa-angle-double-down').addClass('hidden');
	            $('.fa-angle-double-up').removeClass('hidden');
	        }
	        return;
	    });

		// $('#table1').DataTable({
  //           ordering: false,
  //           paging: false,
  //           "pageLength": 100,
  //           "info": false,
		// 	"lengthChange": false,
		// 	"searching": false
  //       })

  //       $('#table2').DataTable({
  //           ordering: false,
  //           paging: false,
  //           "pageLength": 100,
  //           "info": false,
		// 	"lengthChange": false,
		// 	"searching": false
  //       })
		//Gọi lần lượt API lấy danh sách profile

		setTimeout(function() {
			callAPI('ListProfile', '#show_api_list_profile', '{LANG.err_list_profile_dtnet}', 1);
			setTimeout(function() {
				//Gọi API lấy danh sách ticket
				callAPI('ListTicket', '#show_api_list_ticket', '{LANG.err_list_ticket}', 1);
				setTimeout(function() {
					//Gọi API lấy danh sách các gói VIP
					callAPI('ListVip', '#show_api_list_vip', '{LANG.err_list_vip_dtnet}', 1);
					//Gọi API lấy danh sách các đơn hàng
					setTimeout(callAPI, 3000, 'ListPayment', '#show_api_list_payment', '{LANG.err_list_payment_dtnet}', 1);
				}, 3000);
			}, 3000);
		}, 3000);


		// //Gọi API lấy danh sách ticket
		// callAPI('ListTicket', '#show_api_list_ticket', '{LANG.err_list_ticket}', 1);
		// //Gọi API lấy danh sách các gói VIP
		// callAPI('ListVip', '#show_api_list_vip', '{LANG.err_list_vip_dtnet}', 1);
		// //Gọi API lấy danh sách các đơn hàng
		// callAPI('ListPayment', '#show_api_list_payment', '{LANG.err_list_payment_dtnet}', 1);
	});

	function callAPI(apiName, resultLocation, errorMess, page) {
		$(resultLocation).html('<i class="fa fa-circle-o-notch fa-spin" aria-hidden="true"></i>');
		$.ajax({
			type: "POST",
			url: window.location.href,
			cache: !1,
			data: {apiName: apiName, page: page},
			dataType: "json",
			success : function(result) {
				//In danh sách hồ sơ
				if (result.status == 'ok') {
					console.log(result);

					if (result.number_profile > 0) {
						$(resultLocation).html(result.data);
					} else {
						$(resultLocation).html('');
					}
				} else if (result.status == 'fail') {
					console.log(result.error);
					console.log(result);
					$(resultLocation).html('<div class="alert alert-warning">' + errorMess + '</div>');
				}
			},
			error: function(jqXHR, exception) {
				console.log(jqXHR);
				console.log(exception);
				$(resultLocation).html('<div class="alert alert-warning">' + errorMess + '</div>');
			}
		});
	}
</script>
<script>
	$(document).ready(function () {
		// Hiển thị form và ẩn nút Thêm Mạng Xã Hội
		$('#add_social_network').click(function () {
			$(this).hide();
			$('#social_network_form').show();
			$('#edit_id').val('');
			$('#profile_name').val('');
			$('#profile_url').val('');
			$('#platform').val('facebook');
			$('#error_message').hide();
		});

		// Xử lý khi bấm nút Hủy
		$('#cancel_profile').click(function () {
			$('#social_network_form').hide();
			$('#add_social_network').show();
			$('#error_message').hide();
		});

		// Xử lý form submit
		$('#social_network_form').submit(function (e) {
			e.preventDefault();
			$('#save_profile').addClass('processing');
        	$('#save_profile').prop('disabled', true);
			$('#cancel_profile').prop('disabled', true);
			var profileName = $('#profile_name').val();
			var profileUrl = $('#profile_url').val();
			var platform = $('#platform').val();
			var editId = $('#edit_id').val();

			$.ajax({
				type: "POST",
				url: window.location.href,
				cache: !1,
				data: {profileName: profileName, profileUrl: profileUrl, platform: platform, editId: editId},
				dataType: "json",
				success : function(result) {
					if (result.status == 'success') {
						if (editId > 0) {
							$('tr[data-id="' + editId + '"] a').text(profileName);
							$('tr[data-id="' + editId + '"] a').attr('href', profileUrl);
							$('tr[data-id="' + editId + '"] .platform').text(result.data.platform);
						} else {
							var newData = result.data;
							var newItem =
								'<tr data-id="' + newData.id + '">' +
									'<td><a href="' + profileUrl + '" data-platform="' + platform + '">' + profileName + '</a></td>' +
									'<td class="text-center platform">' + newData.platform + '</td>' +
									'<td class="text-center">' +
										'<button class="edit btn btn-warning btn-xs btn--social" data-id="' + newData.id + '">{LANG.edit}</button>' +
										'<button class="delete btn btn-danger btn-xs btn--social" data-id="' + newData.id + '">{LANG.delete}</button>' +
									'</td>' +
								'</tr>';
							$('#social-network-list').append(newItem);
						}
						$('#social_network_form').hide();
						$('#add_social_network').show();
						$('#error_message').hide();
						$('#table_profile').show();
					} else if (result.status == 'error') {
						showError(result.mess);
					}
					$('#save_profile').removeClass('processing');
					$('#save_profile').prop('disabled', false);
					$('#cancel_profile').prop('disabled', false);
				},
				error: function(jqXHR, exception) {
					console.log(jqXHR);
					console.log(exception);
				}
			});
		});

		// Xử lý sự kiện click vào nút Sửa
		$(document).on('click', '.edit', function () {
			var id = $(this).data('id');
			var tr = $('tr[data-id="' + id + '"]');
			var profileName = tr.find('a').text();
			var profileUrl = tr.find('a').attr('href');
			var platform = tr.find('a').data('platform');

			// Điền dữ liệu vào form để sửa
			$('#profile_name').val(profileName);
			$('#profile_url').val(profileUrl);
			$('#platform').val(platform);
			$('#edit_id').val(id);
			$('#social_network_form').show();
			$('#add_social_network').hide();
			$('#error_message').hide();
		});

		// Xử lý sự kiện click vào nút Xóa
		$(document).on('click', '.delete', function () {
			var id = $(this).data('id');
			var tr = $('tr[data-id="' + id + '"]');
			var profileName = tr.find('a').text();
			var platform = tr.find('.platform').text();
			if (confirm('{LANG.social_delete_confirm} ' + profileName + ' (' + platform +  ')?')) {
				tr.find('.delete').addClass('processing');
				$('.edit').prop('disabled', true);
				$('.delete').prop('disabled', true);
				$.ajax({
					type: "POST",
					url: window.location.href,
					cache: !1,
					data: {profileIdSelected: id},
					dataType: "json",
					success : function(result) {
						if (result.status == 'success') {
							$('tr[data-id="' + id + '"]').remove();
							$('.edit').prop('disabled', false);
							$('.delete').prop('disabled', false);
						}
					},
					error: function(jqXHR, exception) {
						console.log(jqXHR);
						console.log(exception);
					}
				});
			}
		});

		// Hàm hiển thị thông báo lỗi
		function showError(message) {
			$('#error_message').text(message).show();
		}
	});
</script>
<!-- END: main -->
