<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\elink\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdatePromotionCodeUse implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'promotion';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        $array = [];

        // Lấy các biến để xác định được thông tin sử dụng
        $array['site_id'] = $nv_Request->get_absint('site_id', 'post', 0);
        $array['order_id'] = $nv_Request->get_absint('order_id', 'post', 0);
        $array['promo_id'] = $nv_Request->get_absint('promo_id', 'post', 0);

        $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code_use
        WHERE site_id=" . $array['site_id'] . " AND order_id=" . $array['order_id'];
        if (!empty($array['promo_id'])) {
            $sql .= " AND promo_id=" . $array['promo_id'];
        }
        $use = $db->query($sql)->fetch();
        if (empty($use)) {
            return $this->result->setCode('1001')->setMessage($nv_Lang->getModule('apierror_promouse_exists'))->getResult();
        }

        $fields = [
            'status' => 'yesno',
            'use_userid' => 'absint',
        ];

        // Dữ liệu cập nhật
        $data = [];
        foreach ($fields as $field => $field_type) {
            if ($nv_Request->isset_request($field, 'post')) {
                if ($field_type == 'yesno') {
                    $data[$field] = (int) $nv_Request->get_bool($field, 'post', false);
                } elseif ($field_type == 'absint') {
                    $data[$field] = $nv_Request->get_absint($field, 'post', 0);
                }
            }
        }
        if (empty($data)) {
            return $this->result->setCode('1002')->setMessage($nv_Lang->getModule('apierror_update_datar'))->getResult();
        }

        try {
            $update = [];
            foreach ($data as $field => $value) {
                if (in_array($fields[$field], ['yesno', 'absint'])) {
                    $update[] = $field . '=' . $value;
                } else {
                    $update[] = $field . '=' . $db->quote($value);
                }
            }

            $sql = "UPDATE " . $db_config['prefix'] . "_" . $module_data . "_promotion_code_use SET
            " . implode(', ', $update) . " WHERE id=" . $use['id'];
            $db->query($sql);

            // Tăng/giảm số lượt sử dụng mã giảm giá nếu trạng thái sử dụng thay đổi
            try {
                if (isset($data['status']) and $data['status'] != $use['status']) {
                    $db->query("UPDATE " . $db_config['prefix'] . "_" . $module_data . "_promotion_code SET
                        use_count=use_count" . ($data['status'] == 1 ? '+' : '-') . "1
                    WHERE promo_id=" . $use['promo_id']);
                }
            } catch (\Exception $e) {
                // No thing
            }

            $this->result->setSuccess();
        } catch (\Exception $e) {
            trigger_error(print_r($e, true));
            return $this->result->setCode('1003')->setMessage($e->getMessage())->getResult();
        }

        return $this->result->getResult();
    }
}
