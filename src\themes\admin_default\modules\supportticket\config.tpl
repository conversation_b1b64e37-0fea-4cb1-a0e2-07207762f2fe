<!-- BEGIN: main -->
<!-- BEGIN: error -->
<div class="alert alert-warning">{ERROR}</div>
<!-- END: error -->
<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post" class="form-inline">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <caption>{LANG.module_config}</caption>
            <tbody>
                <tr>
                    <th>{LANG.email_support}</th>
                    <td><input type="text" value="{DATA.email_support}" name="email_support" class="form-control" /></td>
                </tr>
                <tr>
                    <th>{LANG.logoname}</th>
                    <td><input type="text" value="{DATA.logoname}" name="logoname" class="form-control" /></td>
                </tr>
                <tr>
                    <th>{LANG.sitename}</th>
                    <td><input type="text" value="{DATA.sitename}" name="sitename" class="form-control" /></td>
                </tr>
                <tr>
                    <th>{LANG.close_unpaid}</th>
                    <td><input type="text" value="{DATA.close_unpaid}" name="close_unpaid" class="form-control" /></td>
                </tr>
                <tr>
                    <th>{LANG.close_unreply}</th>
                    <td><input type="text" value="{DATA.close_unreply}" name="close_unreply" class="form-control" /></td>
                </tr>
                <tr>
                    <th>{LANG.open_reply}</th>
                    <td><input type="text" value="{DATA.open_reply}" name="open_reply" class="form-control" /></td>
                </tr>
                <tr>
                    <th>{LANG.return_point}</th>
                    <td><input type="text" value="{DATA.return_point}" name="return_point" class="form-control" /></td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="text-center">
        <input class="btn btn-primary" name="submit" type="submit" value="{LANG.save}" />
    </div>
</form>
<!-- END: main -->
