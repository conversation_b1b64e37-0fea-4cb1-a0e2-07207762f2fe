<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */
if (!defined('NV_IS_FILE_ADMIN'))
    die('Stop!!!');

$per_page = 30;
$per_page_detail = 30;
$base_url = NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=statistical_give_points";
$userid = $nv_Request->get_int('userid', 'get', 0);
$admin_id = $nv_Request->get_int('admin_id', 'get', 0);

$_from = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
$from = nv_date('d/m/Y', $_from); // Mặc định ngày 01 của tháng
$to = nv_date('d/m/Y', NV_CURRENTTIME);
$sfrom = nv_substr($nv_Request->get_title('sfrom', 'get', $from), 0, 10);
$sto = nv_substr($nv_Request->get_title('sto', 'get', $to), 0, 10);
$tk_tang = $nv_Request->get_title('tk_tang', 'get', '');
$tk_nhan = $nv_Request->get_title('tk_nhan', 'get', '');
// Tách ngày tháng thành mảng để xử lý
$_arr_sfrom = explode('/', $sfrom);
$_arr_sto = explode('/', $sto);

$start = mktime(0, 0, 0, $_arr_sfrom[1], $_arr_sfrom[0], $_arr_sfrom[2]);
$end = mktime(23, 59, 59, $_arr_sto[1], $_arr_sto[0], $_arr_sto[2]);

$where = " (tb1.created_time >= " . $start . " and tb1.created_time <=" . $end . ")";
$base_url .= '&amp;sfrom=' . $sfrom . '&amp;sto=' . $sto . '&amp;tk_tang=' . $tk_tang . '&amp;tk_nhan=' . $tk_nhan;

if (!empty($tk_tang)) {
    $user = $db->query('SELECT distinct userid FROM ' . NV_USERS_GLOBALTABLE . ' WHERE username LIKE "%' . $tk_tang . '%"')->fetchAll();
    $arrUser = array_column($user, 'userid');
    if (!empty($arrUser)) {
        $where .= " AND (admin_id IN (" . implode(",", $arrUser) . "))";
    }
}

if (!empty($tk_nhan)) {
    $user = $db->query('SELECT distinct userid FROM ' . NV_USERS_GLOBALTABLE . ' WHERE username LIKE "%' . $tk_nhan . '%"')->fetchAll();
    $arrUser = array_column($user, 'userid');
    if (!empty($arrUser)) {
        $where .= " AND (userid IN (" . implode(",", $arrUser) . "))";
    }
}
// loại trừ các gói của admin
$arr_admin = [];
$result_admin = $db->query('SELECT admin_id FROM nv4_authors');
while ($_admin_info = $result_admin->fetch()) {
    $arr_admin[$_admin_info['admin_id']] = $_admin_info['admin_id'];
}

// các tài khoản test mà k muốn thống kê đưa vào đây, lấy theo nhóm test https://id.dauthau.net/qtdtvnds/index.php?language=vi&nv=users&op=groups&userlist=19
$result = $db->query('SELECT userid FROM ' . NV_GROUPS_GLOBALTABLE . '_users WHERE group_id = 19');
while ($_gr_user_info = $result->fetch()) {
    $arr_admin[$_gr_user_info['userid']] = $_gr_user_info['userid'];
}

if (!empty($arr_admin)) {
    $where .= " AND (userid NOT IN (" . implode(",", $arr_admin) . "))";
    $where .= " AND (admin_id NOT IN (" . implode(",", $arr_admin) . "))";
}

$page = $nv_Request->get_int('page', 'get', 1);
$db->sqlreset()
    ->select('COUNT(*)')
    ->from($db_config['prefix'] . "_" . $module_data . "_log_give_points tb1")
    ->where($where);
$result = $db->query($db->sql());
$all_page = $result->fetchColumn();
$db->select('tb1.*')
    ->order('tb1.created_time DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);

$result = $db->query($db->sql());
$generate_page_list = nv_generate_page($base_url, $all_page, $per_page, $page);

$list = [];

while ($row = $result->fetch()) {
    $list[] = $row;
    $arrUserid[$row['userid']] = $row['userid'];
    $arrUserid[$row['admin_id']] = $row['admin_id'];
}

// Lấy danh sách chi tiết
if ($userid > 0 and $admin_id > 0) {
    $page_detail = $nv_Request->get_int('page', 'get', 1);
    $where = " (tb1.admin_id = " . $admin_id . " and tb1.userid = " . $userid . ")";
    if ($nv_Request->isset_request('searchLog', 'GET')) {
        $where .= " AND (tb1.created_time >= " . $start . " and tb1.created_time <=" . $end . ")";
    }

    $db->sqlreset()
        ->select('COUNT(*)')
        ->from($db_config['prefix'] . "_" . $module_data . "_log_give_points tb1")
        ->where($where);

    $result = $db->query($db->sql());
    $all_page = $result->fetchColumn();

    $db->sqlreset()
        ->select('tb1.*')
        ->from($db_config['prefix'] . "_" . $module_data . "_log_give_points tb1")
        ->where($where)
        ->order('tb1.created_time DESC')
        ->order('tb1.created_time DESC')
        ->limit($per_page_detail)
        ->offset(($page_detail - 1) * $per_page_detail);
    $result = $db->query($db->sql());
    $base_url .= '&amp;userid=' . $userid . '&amp;admin_id=' . $admin_id;
    $generate_page_detail = nv_generate_page($base_url, $all_page, $per_page_detail, $page_detail);
    $list_detail = [];
    while ($row = $result->fetch()) {
        $arrUserid[$row['admin_id']] = $row['admin_id'];
        $arrUserid[$row['userid']] = $row['userid'];
        $list_detail[] = $row;
    }

    if ($page_detail > 1 and empty($list_detail)) {
        nv_redirect_location($base_url);
    }
} else {
    if ($page > 1 and empty($list)) {
        nv_redirect_location($base_url);
    }
}

// Kiểm tra có mảng user mới thực hiện truy vấn CSDL
if (!empty($arrUserid)) {
    $user = $db->query('SELECT userid, username, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arrUserid) . ')')->fetchAll();
    foreach ($user as $key => $value) {
        $arrUser[$value['userid']] = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']) . ' (' . $value['username'] . ')';
    }
    unset($user);
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('URL', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=statistical_give_points");
$xtpl->assign('OP', $op);
$xtpl->assign('NODATA', $nv_Lang->getModule('nodata'));
$xtpl->assign('FROM', $sfrom);
$xtpl->assign('FROM_DEFAULT', $from);
$xtpl->assign('TO', $sto);
$xtpl->assign('TO_DEFAULT', $to);
$xtpl->assign('TK_TANG', $tk_tang);
$xtpl->assign('TK_NHAN', $tk_nhan);
$min_date = $db->query('SELECT created_time FROM ' . $db_config['prefix'] . '_' . $module_data . '_log_give_points ORDER BY created_time ASC LIMIT 1')->fetchColumn();
$xtpl->assign('MINDATE', nv_date('d/m/Y', $min_date));

if ($userid > 0 and $admin_id > 0) {
    $min_date = $db->query('SELECT created_time FROM ' . $db_config['prefix'] . '_' . $module_data . '_log_give_points WHERE userid = ' . $userid . '  and admin_id = ' . $admin_id . ' ORDER BY created_time ASC LIMIT 1')->fetchColumn();
    $xtpl->assign('MINDATE', nv_date('d/m/Y', $min_date));
    if (!empty($list_detail)) {
        $tongDiemTang = 0;
        $tongDiemNhan = 0;
        if ($page_detail > 1) {
            $stt = $page_detail * $per_page_detail - $per_page_detail + 1;
        } else {
            $stt = $page_detail;
        }
        foreach ($list_detail as $k => $row) {
            $row['stt'] = $stt++;
            $row['full_name'] = $arrUser[$row['userid']];
            $row['full_name_admin'] = $arrUser[$row['admin_id']];
            $row['addtime'] = nv_date('H:i:s d/m/Y', $row['created_time']);
            $tongDiemTang += $row['point_total'];
            $tongDiemNhan += $row['point_receive'];
            $xtpl->assign('ACOUNT', $row);
            $xtpl->parse('main.detail.loop_detail');
        }
        $xtpl->assign('TONG_TANG', number_format($tongDiemTang, 0, ',', '.'));
        $xtpl->assign('TONG_NHAN', number_format($tongDiemNhan, 0, ',', '.'));
    } else {
        $xtpl->parse('main.detail.nodata1');
    }
    if ($generate_page_detail) {
        $xtpl->assign('GENERATE_PAGE', $generate_page_detail);
        $xtpl->parse('main.detail.generate_page');
    }
    $xtpl->assign('USERID', $userid);
    $xtpl->assign('ADMIN_ID', $admin_id);
    $xtpl->parse('main.detail');
} else {
    if (!empty($list)) {
        if ($page > 1) {
            $stt = $page * $per_page - $per_page + 1;
        } else {
            $stt = $page;
        }
        foreach ($list as $k => $row) {
            $row['stt'] = $stt++;
            $row['view_url'] = NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=statistical_give_points&amp;userid=" . $row['userid'] . '&amp;admin_id=' . $row['admin_id'];
            $row['full_name'] = $arrUser[$row['userid']];
            $row['full_name_admin'] = $arrUser[$row['admin_id']];
            $row['created_time'] = nv_date('H:i:s d/m/Y', $row['created_time']);
            $xtpl->assign('ACOUNT', $row);
            $xtpl->parse('main.showlist.loop_listacount');
        }

        if ($generate_page_list) {
            $xtpl->assign('GENERATE_PAGE', $generate_page_list);
            $xtpl->parse('main.showlist.generate_page');
        }
    } else {
        $xtpl->parse('main.showlist.nodata3');
    }
    $xtpl->parse('main.showlist');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');
$page_title = $nv_Lang->getModule('title_staticcal_give_point');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
