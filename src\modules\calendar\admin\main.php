<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    exit('Stop!!!');
}
$page_title = $nv_Lang->getModule('main');

if ($nv_Request->get_title('changestatus', 'post', '') === NV_CHECK_SESSION) {
    $id = $nv_Request->get_absint('id', 'post', 0);

    // Ki<PERSON>m tra tồn tại
    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_event WHERE id=" . $id;
    $array = $db->query($sql)->fetch();
    if (empty($array)) {
        nv_htmlOutput('NO_' . $id);
    }

    $status = empty($array['status']) ? 1 : 0;

    $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_event SET status = " . $status . " WHERE id = " . $id;
    $db->query($sql);

    nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_CHANGE_STATUS_CALENDAR', json_encode($array), $admin_info['admin_id']);
    $nv_Cache->delMod($module_name);

    nv_htmlOutput("OK");
}

if ($nv_Request->get_title('delete', 'post', '') === NV_CHECK_SESSION) {
    $id = $nv_Request->get_absint('id', 'post', 0);

    // Kiểm tra tồn tại
    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_event WHERE id=" . $id;
    $array = $db->query($sql)->fetch();
    if (empty($array)) {
        nv_htmlOutput('NO_' . $id);
    }

    // Xóa
    $sql = "DELETE FROM " . NV_PREFIXLANG . "_" . $module_data . "_event WHERE id=" . $id;
    $db->query($sql);

    $db->query("DELETE FROM " . NV_PREFIXLANG . "_" . $module_data . "_repeats WHERE event_id=" . $id);

    nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_DELETE_CALENDAR', json_encode($array), $admin_info['admin_id']);
    $nv_Cache->delMod($module_name);

    nv_htmlOutput("OK");
}

if ($nv_Request->get_title('delete_all', 'post,get','') === NV_CHECK_SESSION) {
    $id = $nv_Request->get_typed_array('idcheck', 'post,get', 'int', []);

    if ($id) {
        $sql = "DELETE FROM " . NV_PREFIXLANG . "_" . $module_data . "_event WHERE id IN (" . implode(',', $id) . ")";
        $db->query($sql);
        $db->query("DELETE FROM " . NV_PREFIXLANG . "_" . $module_data . "_repeats WHERE event_id IN (" . implode(',', $id) . ")");

        nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_DELETE_CALENDAR', json_encode($id), $admin_info['admin_id']);
        $nv_Cache->delMod($module_name);

        $res = [
            'res' => 'success',
            'mess' => $nv_Lang->getModule('delete_success')
        ];
    } else {
        $res = [
            'res' => 'error',
            'mess' => $nv_Lang->getModule('error_required_id')
        ];
    }
    nv_jsonOutput($res);
}

$db->sqlreset()->select('id, name_calendar')->from(NV_PREFIXLANG . '_' . $module_data);
$result = $db->query($db->sql());
while ($row = $result->fetch()) {
    $arr_calendar[$row['id']] = $row;
}

$perpage = 30;
$page = $nv_Request->get_int('page', 'get', 1);
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name;

$array_search = [];
$array_search['q'] = $nv_Request->get_title('q', 'get', '');
$array_search['c'] = $nv_Request->get_int('c', 'get', 0);
$array_search['from'] = $nv_Request->get_title('f', 'get', '');
$array_search['to'] = $nv_Request->get_title('t', 'get', '');
$array_search['type'] = $nv_Request->get_int('type', 'get', -1);

if (preg_match('/^([0-9]{1,2})\-([0-9]{1,2})\-([0-9]{4})$/', $array_search['from'], $m)) {
    $array_search['from'] = mktime(0, 0, 0, intval($m[2]), intval($m[1]), intval($m[3]));
} else {
    $array_search['from'] = 0;
}

if (preg_match('/^([0-9]{1,2})\-([0-9]{1,2})\-([0-9]{4})$/', $array_search['to'], $m)) {
    $array_search['to'] = mktime(23, 59, 59, intval($m[2]), intval($m[1]), intval($m[3]));
} else {
    $array_search['to'] = 0;
}

$db->sqlreset()->select('COUNT(*)')->from(NV_PREFIXLANG . '_' . $module_data . '_event');
$error = [];
$where = [];
if (!empty($array_search['q'])) {
    $base_url .= '&amp;q=' . $array_search['q'];
    $where[] = "(title LIKE '%" . $db->dblikeescape($array_search['q']) . "%' OR description LIKE '%" . $db->dblikeescape($array_search['q']) . "%')";
}

if (!empty($array_search['c'])) {
    $base_url .= '&amp;c=' . $array_search['c'];
    $where[] = "calendar_id=" . $array_search['c'];
}

if ($array_search['type'] !== -1) {
    $base_url .= '&amp;type=' . $array_search['type'];
    $where[] = "is_lunar=" . $array_search['type'];
}

if (!empty($array_search['from'])) {
    $base_url .= '&amp;f=' . nv_date('d-m-Y', $array_search['from']);
    $where[] = "date_event>=" . $array_search['from'];
}
if (!empty($array_search['to'])) {
    $base_url .= '&amp;t=' . nv_date('d-m-Y', $array_search['to']);
    $where[] = "date_event<=" . $array_search['to'];
}

if ($array_search['from'] > $array_search['to']) {
    $error[] = $nv_Lang->getModule('date_event_error');
}

// Phần sắp xếp
$array_order = [];
$array_order['field'] = $nv_Request->get_title('of', 'get', '');
$array_order['value'] = $nv_Request->get_title('ov', 'get', '');
$base_url_order = $base_url;
if ($page > 1) {
    $base_url_order .= '&amp;page=' . $page;
}

// Định nghĩa các field và các value được phép sắp xếp
$order_fields = ['title', 'date_event', 'add_time', 'edit_time'];
$order_values = ['asc', 'desc'];

if (!in_array($array_order['field'], $order_fields)) {
    $array_order['field'] = '';
}
if (!in_array($array_order['value'], $order_values)) {
    $array_order['value'] = '';
}

if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}

$total = $db->query($db->sql())->fetchColumn();

if (!empty($array_order['field']) and !empty($array_order['value'])) {
    $order = $array_order['field'] . ' ' . $array_order['value'];
} else {
    $order = 'id DESC';
}

$db->select('*')->from(NV_PREFIXLANG . '_' . $module_data . '_event')->order($order)->limit($perpage)->offset(($page - 1) * $perpage);
$result = $db->query($db->sql());
$array_calendar = [];
while ($row = $result->fetch()) {
    $array_calendar[] = $row;
    $ids_calendar[$row['calendar_id']] = $row['calendar_id'];
}   

if (!empty($ids_calendar)) {
    $sql = "SELECT id, name_calendar FROM " . NV_PREFIXLANG . "_" . $module_data . " WHERE id IN (" . implode(',', $ids_calendar) . ")";
    $result = $db->query($sql);
    $name_calendar = [];
    while ($row = $result->fetch()) {
        $name_calendar[$row['id']] = $row;
    }
}

$xtpl = new XTemplate('main.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('TOTAL', $total);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_FILE', $module_file);
$xtpl->assign('OP', $op);
$xtpl->assign('URL_IMPORT_ICS', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=import-ical');

if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br>', $error));
    $xtpl->parse('main.error');
}

if (!empty($arr_calendar)) {
    foreach ($arr_calendar as $row) {
        $row['selected'] = $row['id'] == $array_search['c'] ? ' selected="selected"' : '';
        $xtpl->assign('CALENDAR', $row);
        $xtpl->parse('main.calendar');
    }
}

$array_search['from'] = empty($array_search['from']) ? '' : nv_date('d-m-Y', $array_search['from']);
$array_search['to'] = empty($array_search['to']) ? '' : nv_date('d-m-Y', $array_search['to']);

$xtpl->assign('SEARCH', [
    'q' => $array_search['q'],
    'c' => $array_search['c'],
    'from' => $array_search['from'],
    'to' => $array_search['to'],
    'type_1' => $array_search['type'] == 1 ? ' selected="selected"' : '',
    'type_0' => $array_search['type'] == 0 ? ' selected="selected"' : ''
]);

if (!empty($array_calendar)) {
    foreach ($array_calendar as $row) { 
        $row['name_calendar'] = isset($name_calendar[$row['calendar_id']]) ? $name_calendar[$row['calendar_id']]['name_calendar'] : '';
        $row['date_event'] = isset($row['date_event']) ? nv_date('d/m/Y', $row['date_event']) : '';
        $row['add_time'] = isset($row['add_time']) ? nv_date('d/m/Y', $row['add_time']) : '';
        $row['edit_time'] = isset($row['edit_time']) ? nv_date('d/m/Y', $row['edit_time']) : '';
        $row['status_render'] = empty($row['status']) ? '' : ' checked="checked"';
        $row['url_edit'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=event&id=' . $row['id'];
        $xtpl->assign('ROW', $row);
        $xtpl->parse('main.loop');
    }
}

// Xuất phân trang
$generate_page = nv_generate_page($base_url, $total, $perpage, $page);
if (!empty($generate_page)) {
    $xtpl->assign('GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}
// Xuất các phần sắp xếp
foreach ($order_fields as $field) {
    $url = $base_url_order;
    if ($array_order['field'] == $field) {
        if (empty($array_order['value'])) {
            $url .= '&amp;of=' . $field . '&amp;ov=asc';
            $icon = '<i class="fa fa-sort" aria-hidden="true"></i>';
        } elseif ($array_order['value'] == 'asc') {
            $url .= '&amp;of=' . $field . '&amp;ov=desc';
            $icon = '<i class="fa fa-sort-asc" aria-hidden="true"></i>';
        } else {
            $icon = '<i class="fa fa-sort-desc" aria-hidden="true"></i>';
        }
    } else {
        $url .= '&amp;of=' . $field . '&amp;ov=asc';
        $icon = '<i class="fa fa-sort" aria-hidden="true"></i>';
    }
    $xtpl->assign(strtoupper('URL_ORDER_' . $field), $url);
    $xtpl->assign(strtoupper('ICON_ORDER_' . $field), $icon);
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
