<!-- BEGIN: main -->
<link rel="stylesheet" href="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/select2/select2.min.css">
<script type="text/javascript" src="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
<!-- BEGIN: error -->
<div class="alert alert-danger">{ERROR}</div>
<!-- END: error -->
<div class="panel panel-default">
    <div class="panel-body">
        <form method="post" class="form-horizontal" enctype="multipart/form-data">
            <div class="form-group">
                <input type="file" name="import_file" accept=".ics" required />
            </div>
            <div class="form-group">
                <label class="control-label">{LANG.name_calendar}</label>
                <div class="form-inline mt-2">
                    <select class="form-control" name="calendar_id">
                        <option value="0">----</option>
                        <!-- BEGIN: calendar -->
                        <option value="{CALENDAR.id}"{CALENDAR.selected}>{CALENDAR.name_calendar}</option>
                        <!-- END: calendar -->
                    </select>
                </div>
            </div>
            <div class="form-group">
                <div class="checkbox">
                    <label><input type="checkbox" name="truncate_data" value="1"{DATA.truncate_data}> <strong class="text-danger">{LANG.truncate}</strong></label>
                </div>
            </div>
            <div class="form-group">
                <input type="hidden" name="save" value="{NV_CHECK_SESSION}">
                <button type="submit" class="btn btn-primary"><i class="fa fa-cloud-upload"></i> {GLANG.submit}</button>
            </div>
        </form>
    </div>
</div>
<script>
    $(document).ready(function() {
        $('select').select2();
    });
</script>
<!-- END: main -->
