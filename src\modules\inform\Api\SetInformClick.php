<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2022 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\inform\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    exit('Stop!!!');
}

/**
 * NukeViet\Module\inform\Api\InformSetStatus
 * API dùng để cập nhật trạng thái một thông báo
 *
 * @package NukeViet\Module\inform\Api
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2022 VINADES.,JSC. All rights reserved
 * @version 4.6.00
 * @access public
 */
class SetInformClick implements IApi
{
    private $result;

    /**
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     * @return string
     */
    public static function getCat()
    {
        return 'Action';
    }

    /**
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        $admin_id = Api::getAdminId();
        $admin_lev = Api::getAdminLev();

        $postdata = [];
        $postdata['id'] = $nv_Request->get_absint('id', 'post', 0);

        $this->result->setError()->setCode('0001');

        if (empty($postdata['id'])) {
            return $this->result->setMessage('No ID!!!')->getResult();
        }

        $sql = "SELECT * FROM " . NV_INFORM_GLOBALTABLE . " WHERE id=" . $postdata['id'];
        $inform = $db->query($sql)->fetch();
        if (empty($inform)) {
            return $this->result->setMessage('No InForm!!!')->getResult();
        }
        if (empty($inform['meta_id'])) {
            return $this->result->setMessage('No Meta ID!!!')->getResult();
        }

        $db->query("INSERT INTO " . NV_INFORM_GLOBALTABLE . "_events (meta_id, event_name, event_time) VALUES (
            " . $db->quote($inform['meta_id']) . ", 'click', " . NV_CURRENTTIME . "
        )");

        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
