<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */
if (!defined('NV_MAINFILE')) {
    exit('Stop!!!');
}

if (!function_exists('taxcodecheck')) {
    function taxcodecheck($strTaxCode)
    {
        if (strlen($strTaxCode) < 10) {
            return false;
        }
        $strTaxCode = substr($strTaxCode, 0, 10); // công thức trên chỉ kiểm tra 10 số đầu theo một quy luật thôi.
        $array_iCheck = array(
            31,
            29,
            23,
            19,
            17,
            13,
            7,
            5,
            3
        );
        $ChkNumber = 0;
        for ($i = 0; $i < strlen($strTaxCode) - 1; $i++) {
            $ChkNumber += intval(substr($strTaxCode, $i, 1)) * $array_iCheck[$i];
        }
        return (substr($strTaxCode, 9, 1) == (10 - $ChkNumber % 11)) ? true : false;
    }
}

if (!function_exists('taxcodecheck2')) {
    /*
    * Hàm kiểm tra mã số thuế
    * Quy định về MST theo: https://luatminhkhue.vn/quy-dinh-ve-ma-so-thue-va-y-nghia-cac-con-so-theo-quy-dinh-cua-luat.aspx
    * Cấu trúc: N1N2 N3N4N5N6N7N8N9 N10 - N11N12N13
    * Trong đó:
    * - Hai chữ số đầu N1N2 là số phân Khoảng tỉnh cấp mã số thuế được quy định theo danh Mục mã phân Khoảng tỉnh (đối với mã số thuế cấp cho người nộp thuế là doanh nghiệp, tổ chức, hộ gia đình, nhóm cá nhân và cá nhân kinh doanh) hoặc số không phân Khoảng tỉnh cấp mã số thuế (đối với mã số thuế cấp cho các cá nhân khác).
    * - Bảy chữ số N3N4N5N6N7N8N9 được quy định theo một cấu trúc xác định, tăng dần trong Khoảng từ 0000001 đến 9999999. Chữ số N10 là chữ số kiểm tra.
    * - Ba chữ số N11N12N13 là các số thứ tự từ 001 đến 999.
    * - Dấu gạch ngang là ký tự để phân tách nhóm 10 số đầu và nhóm 3 số cuối.
    */
    function taxcodecheck2($strTaxCode)
    {
        if (strlen($strTaxCode) < 10) {
            return false;
        }

        if (strlen($strTaxCode) == 10) {
            if (preg_match('/^[0-9]{10}$/', $strTaxCode)) {
                return taxcodecheck($strTaxCode);
            } else {
                return false;
            }
        } else {
            // trường hợp 13 số
            if (preg_match('/^([0-9]{10})\-([0-9]{3})$/', $strTaxCode, $m)) {
                return taxcodecheck($m[1]);
            } else {
                return false;
            }
        }
    }
}

if (!function_exists('trim_space')) {
    function trim_space($text)
    {
        $text = preg_replace('/^[\pZ\pC]+|[\pZ\pC]+$/u', '', $text);
        return $text;
    }
}

if (!function_exists('phonecheck')) {
    function phonecheck($strphone, $lang_nation = 0)
    {
        global $nv_Request;

        // trước khi check phone thì check với dữ liệu tùy biến của user để lấy quốc gia nhập vào
        if ($nv_Request->isset_request('custom_fields', 'post')) {
            $custom_fields_user = $nv_Request->get_array('custom_fields', 'post');
            if (isset($custom_fields_user['nation'])) {
                // việt nam mã 244 trong nv4_users_nation
                if ($custom_fields_user['nation'] != 244) {
                    $lang_nation = 1;
                }
            }
        }

        if ($lang_nation == 0) {
            // check mã việt nam
            if (!preg_match('/^(09|\+849|\+00|\+|03|\+843|08|\+848|05|\+845|07|\+847)+([0-9]{8})$/', $strphone) and !preg_match('/^(01|\+841|02|\+842)+([0-9]{9})$/', $strphone)) {
                return false;
            }
        } else {
            // check mã quốc tế https://vi.wikipedia.org/wiki/M%C3%A3_s%E1%BB%91_%C4%91i%E1%BB%87n_tho%E1%BA%A1i_qu%E1%BB%91c_t%E1%BA%BF
            // check theo cấu trúc
            /*
             * + hoặc 00
             * mã quốc gia: từ 2-4 số
             * số điện thoại: từ 6-15 số
             * => 9-15 số sau + hoặc 00
             */

            if (!preg_match('/^(\+|\+00|00)+([0-9]{9,15})$/', $strphone)) {
                return false;
            }
        }
        return true;
    }
}

if (!function_exists('cccdCheck')) {
    /*
    * Hàm kiểm tra căn cước công dân
    * Quy định: https://mytour.vn/vi/blog/bai-viet/tam-quan-trong-cua-12-so-tren-the-can-cuoc-cong-dan.html
    * Cấu trúc: dãy 12 số
    * Trong đó:
    * - 3 số đầu là mã của tỉnh hoặc thành phố trực thuộc trung ương - hoặc quốc gia đăng kí khai sinh (có quy định cụ thể tại đường dẫn trên)
    * - 3 số tiếp theo là mã số xác định mã thế kỷ sinh, mã giới tính, và năm sinh (trải dài từ 000 - 999)
    * - 6 số cuối là 6 số ngẫu nhiên trên từng thẻ căn cước.
    */
    function cccdCheck($strCccd)
    {
        if (strlen($strCccd) != 12) {
            return false;
        }

        if (preg_match('/^[0-9]{3}[0123][0-9]{8}$/', $strCccd)) {
            //các mã này được quy định tại Phụ lục I, Phụ lục II của Thông tư 59/2021/TT-BCA
            $str_prefix12 = substr($strCccd, 0, 3);
            //mã theo tỉnh thành đăng kí khai sinh tại Việt Nam
            $array_prefixProvince12 = array(
                '001', '002', '004', '006', '008', '010', '011', '012', '014', '015', '017', '019', '020', '022', '024', '025', '026', '027', '030', '031', '033', '034', '035', '036', '037', '038', '040', '042', '044', '045', '046', '048', '049', '051', '052', '054', '056', '058', '060', '062', '064', '066', '067', '068', '070', '072', '074', '075', '077', '079', '080', '082', '083', '084', '086', '087', '089', '091', '092', '093', '094', '095', '096'
            );
            //mã quốc gia đăng kí khai sinh
            $array_prefixNation12 = array(
                '101', '102', '103', '104', '105', '106', '107', '108', '109', '110', '111', '112', '113', '114', '115', '116', '117', '118', '119', '120', '121', '122', '123', '124', '125', '126', '127', '128', '129', '130', '131', '132', '133', '134', '135', '136', '137', '138', '139', '140', '141', '142', '143', '144', '145', '146', '147', '148', '149', '150', '151', '152', '153', '154', '155', '156', '157', '158', '159', '160', '161', '162', '163', '164', '165', '166', '167', '168', '169', '170', '171', '172', '173', '174', '175', '176', '177', '178', '179', '180', '181', '182', '183', '184', '185', '186', '187', '188', '189', '190', '191', '192', '193', '194', '195', '196', '197', '198', '199', '200', '201', '202', '203', '204', '205', '206', '207', '208', '209', '210', '211', '212', '213', '214', '215', '216', '217', '218', '219', '220', '221', '222', '223', '224', '225', '226', '227', '228', '229', '230', '231', '232', '233', '234', '235', '236', '237', '238', '239', '240', '241', '242', '243', '244', '245', '246', '247', '248', '249', '250', '251', '252', '253', '254', '255', '256', '257', '258', '259', '260', '261', '262', '263', '264',
                '265', '266', '267', '268', '269', '270', '271', '272', '273', '274', '275', '276', '277', '278', '279', '280', '281', '282', '283', '284', '285', '286', '287', '288', '289', '290', '291', '292', '293', '294', '295'
            );
            if (in_array($str_prefix12, $array_prefixProvince12) or in_array($str_prefix12, $array_prefixNation12)) {
                return true;
            }
        }
        return false;
    }
}

if (!function_exists('doMarketingAPI')) {
    /**
     * @param string $action
     * @param array $parameters
     * @param string $module
     * @return array|mixed
     */
    function doMarketingAPI($action = '', $parameters = [], $module = 'marketing')
    {
        global $global_config;

        if (!defined('MARKETING_API_EMAILDIE_KEY')) {
            return [];
        }

        $res = [];
        $request = [
            // Tham số bắt buộc
            'apikey' => MARKETING_API_EMAILDIE_KEY,
            'apisecret' => MARKETING_API_EMAILDIE_SECRET,
            'action' => $action,
            'module' => $module
        ];
        $request = array_merge($request, $parameters);

        $NV_Http = new NukeViet\Http\Http($global_config, NV_TEMP_DIR);
        $NV_Http->reset();
        $args = [
            'headers' => [
                'Referer' => NV_MY_DOMAIN
            ],
            'body' => $request,
            'timeout' => 20,
            'sslverify' => false,
            'decompress' => false
        ];
        $responsive = $NV_Http->post(MARKETING_API_URL, $args);

        if (is_array($responsive) and empty(NukeViet\Http\Http::$error)) {
            $res = !empty($responsive['body']) ? json_decode($responsive['body'], true) : [];
        }

        return $res;
    }
}

if (!function_exists('connect_dbcr')) {
    function connect_dbcr()
    {
        global $cr_config;

        $_time_zone_db = preg_replace('/^([\+|\-]{1}\d{2})(\d{2})$/', '$1:$2', date('O'));
        $driver_options = array(
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::ATTR_PERSISTENT => $cr_config['persistent'],
            PDO::ATTR_CASE => PDO::CASE_LOWER,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        );

        $dsn = $cr_config['dbtype'] . ':dbname=' . $cr_config['dbname'] . ';host=' . $cr_config['dbhost'] . ';charset=' . $cr_config['charset'];
        if (!empty($cr_config['dbport'])) {
            $dsn .= ';port=' . $cr_config['dbport'];
        }
        $driver_options[PDO::ATTR_ERRMODE] = PDO::ERRMODE_EXCEPTION;
        try {
            $dbcr = new PDO($dsn, $cr_config['dbuname'], $cr_config['dbpass'], $driver_options);
            $dbcr->exec("SET SESSION time_zone='" . $_time_zone_db . "'");
        } catch (PDOException $e) {
            print_r($e->getMessage());
            echo 'PDOException';
            die();
        }
        return $dbcr;
    }
}

nv_add_hook($module_name, 'sector1', $priority, function () {
    global $close_time_dauthau, $open_maunal_time_dauthau, $global_config, $cr_config;

    $close_time_dauthau = mktime(0, 0, 0, 4, 23, 2021); // t2, 26/4
    $open_maunal_time_dauthau = mktime(23, 59, 59, 4, 24, 2021); // t5 29/4
});
