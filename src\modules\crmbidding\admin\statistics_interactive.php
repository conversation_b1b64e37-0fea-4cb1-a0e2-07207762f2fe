<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$page_title = $nv_Lang->getModule('statistics_interactive');
if (!defined('NV_IS_SPADMIN')) {
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_admin_theme('<h2 class="text-center text-warning">' . $nv_Lang->getModule('info_notallowed') . '</h2>');
    include NV_ROOTDIR . '/includes/footer.php';
    exit();
}
$base_url = $base_url_origin = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
$perpage = 50;
$page = $nv_Request->get_int('page', 'get', 1);

$array_search = array();
$array_search['email'] = $nv_Request->get_title('email', 'post,get', '');
$array_search['stat_from'] = $nv_Request->get_title('stat_from', 'post,get', '');
$array_search['stat_to'] = $nv_Request->get_title('stat_to', 'post,get', '');

if (!empty($array_search['email'])) {
    $base_url .= '&amp;email=' . $array_search['email'];
}

if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['stat_from'], $m)) {
    $stat_from = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
    $base_url .= '&amp;stat_from=' . $array_search['stat_from'];
} else {
    $stat_from = 0;
}

if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['stat_to'], $m)) {
    $stat_to = mktime(23, 59, 59, $m[2], $m[1], $m[3]) + 86400;
    $base_url .= '&amp;stat_to=' . $array_search['stat_to'];
} else {
    $stat_to = NV_CURRENTTIME;
}

$nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_host'], $module_config[$module_name]['elas_port'], 'dauthau_mail', $module_config[$module_name]['elas_user'], $module_config[$module_name]['elas_pass']);

$search_elastic = [];
$search_elastic['must'] = [];
$array_query_elastic = [];

if (!empty($array_search['email'])) {
    $search_elastic['must']['term'] = ['main_mail.keyword' => $array_search['email']];
}

$search_elastic['filter']['range']['addtime'] = [
    'gte' => $stat_from,
    'lte' => $stat_to
];

$array_query_elastic['query']['bool'] = $search_elastic;

$array_query_elastic['size'] = $perpage;
$array_query_elastic['sort'] = [
    [
        "addtime" => [
            "order" => "desc"
        ]
    ]
];
$array_query_elastic['aggs'] = [
    "sum_num" => [
        "sum" => [
            "field" => "click_count"
        ]
    ],
];

$array_query_elastic['from'] = ($page - 1) * $perpage;
$array_query_elastic['_source'] = array(
    'userid',
    'main_mail',
    'title',
    'number_phone',
    'addtime',
    'click_count',
);

$response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_mail', $array_query_elastic);

$total = $response['hits']['total']['value'];
$sum_click_count = $response['aggregations']['sum_num']['value'];
$data = [];
$arr_userid = [];
foreach ($response['hits']['hits'] as $value) {
    $_source = $value['_source'];
    $_source['addtime_fm'] = nv_date('d/m/Y', $_source['addtime']);
    $arr_userid[] = $_source['userid'];
    $data[] = $_source;
}

$arr_userid = array_unique($arr_userid);
$array_full_name = [];
// Lấy tên tài khoản
if (!empty($arr_userid)) {
    $result = $db->query('SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arr_userid) . ')');
    while (list($userid, $first_name, $last_name, $username) = $result->fetch(3)) {
        $full_name = nv_show_name_user($first_name, $last_name, $username);
        $array_full_name[$userid] = $full_name;
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('ARRAY_SEARCH', $array_search);
$xtpl->assign('sum_click_count', $sum_click_count);

$tt = ($page - 1) * $perpage;
foreach ($data as $view) {
    $tt ++;
    $view['number'] = $tt;
    $view['full_name'] = $array_full_name[$view['userid']];
    $xtpl->assign('VIEW', $view);
    $xtpl->parse('main.loop');
}
$generate_page = nv_generate_page($base_url, $total, $perpage, $page);
if (!empty($generate_page)) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}
$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
