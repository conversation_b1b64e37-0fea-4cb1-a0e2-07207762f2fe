<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Apr 20, 2010 10:47:41 AM
 */

if (!defined('NV_IS_MOD_ELINK')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

$page_title = $nv_Lang->getModule('promotion_all_orders');
$description = $keywords = 'no';

if (!defined('NV_IS_USER')) {
    $link_redirect = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    nv_redirect_location($link_redirect);
}

/*
 * <PERSON><PERSON><PERSON> tra quyền truy cập vào đây
 * - Cho phép affiliate
 * - Cho phép promotion
 */
$affiliate_enabled = nv_user_in_groups($global_array_config['affiliate_code_groups']);
$promotion_enabled = nv_user_in_groups($global_array_config['groups_allowed_string']);
if (!$affiliate_enabled and !$promotion_enabled) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}

$array_mod_title[] = [
    'catid' => 0,
    'title' => $nv_Lang->getModule('promotion_all_orders1'),
    'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op
];
$page = $nv_Request->get_int('page', 'get', 1);
$per_page = 20;
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
if (isset($array_op[1])) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
}
if ($page > 99999999 or $page < 1) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
}

$array_search = [];
$array_search['type'] = $nv_Request->get_title('t', 'get', '');
$array_search['site_id'] = $nv_Request->get_absint('s', 'get', 1);
if (!empty($array_search['type']) and $array_search['type'] != 'a' and $array_search['type'] != 'p') {
    $array_search['type'] = '';
}
if (!isset($global_array_sites[$array_search['site_id']])) {
    $array_search['site_id'] = 1;
}
$base_url .= '&amp;s=' . $array_search['site_id'];

$site = $global_array_sites[$array_search['site_id']];
$api = new DoApi(NV_SERVER_PROTOCOL . '://' . $site['sitedomain'] . '/api.php', $site['api_key'], $site['api_secret']);
$api->setModule($array_search['site_id'] == 1 ? 'bidding' : 'dn')
->setLang('vi')
->setAction('ListElinkOrders')
->setData([
    'userid' => $user_info['userid'],
    'page' => $page,
    'type' => $array_search['type']
]);
$result_api = $api->execute();
$error = $api->getError();

// Lỗi truy vấn
$error_message = '';
$array_orders = $array_vips = [];
$num_items = 0;

if (!empty($error)) {
    $error_message = $error;
}
if ($result_api['status'] != 'success') {
    $error_message = $result_api['message'] ?: 'Api error with no respon';
}
if (empty($error_message)) {
    $array_orders = $result_api['orders'];
    $array_vips = $result_api['vips'];
    $num_items = $result_api['num_items'];
}

if ($array_search['type'] == 'a') {
    // Affiliate
    $base_url .= '&amp;t=a';
} elseif ($array_search['type'] == 'p') {
    // Promotion
    $base_url .= '&amp;t=p';
}

if ($page > 1 and empty($array_orders)) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
}
if ($page > 1) {
    $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . number_format($page, 0, ',', '.');
}

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
$contents = nv_elink_theme_statordersall($array_orders, $array_vips, $generate_page, $array_search, $error_message);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
