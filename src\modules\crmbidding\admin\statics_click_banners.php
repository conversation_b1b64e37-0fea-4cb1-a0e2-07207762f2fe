<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    exit('Stop!!!');
}

use NukeViet\Api\DoApi;

$page_title = $table_caption = $nv_Lang->getModule('statics_click_banners');
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op;

$page = $nv_Request->get_int('page', 'get', 1);
$keyword = $nv_Request->get_title('q', 'post,get', '');
$banner_id = $nv_Request->get_int('banner_id', 'post,get', -1);
$from_click = $nv_Request->get_title('fromtime', 'post,get', nv_date('d-m-Y', NV_CURRENTTIME - 86400 * 30));
$to_click = $nv_Request->get_title('totime', 'post,get', nv_date('d-m-Y', NV_CURRENTTIME));

if (preg_match("/^([0-9]{1,2})([-\/])([0-9]{1,2})([-\/])([0-9]{4})$/", $from_click, $m)) {
    $frclick = mktime(0, 0, 0, $m[3], $m[1], $m[5]);
} else {
    $frclick = NV_CURRENTTIME - 86400 * 30;
}

if (preg_match("/^([0-9]{1,2})([-\/])([0-9]{1,2})([-\/])([0-9]{4})$/", $to_click, $m)) {
    $toclick = mktime(23, 59, 59, $m[3], $m[1], $m[5]);
} else {
    $toclick = NV_CURRENTTIME;
}

$from_check = $toclick - (86400 * 6 * 30);
if ($frclick + 86400 < $from_check) {
    $frclick = $from_check;
    $from_click = nv_date('d-m-Y', $frclick);
    $error = $nv_Lang->getModule('error_max_time');
}

$per_page = 10;
$page_url = $base_url;
$is_search = false;
$arr_where['AND'][] = [
    '!=' => [
        'c.userid' => 0
    ]
];
if (!empty($keyword)) {
    $is_search = true;
    $base_url .= '&q=' . $keyword;
    $arr_where['OR'][] = [
        'like' => [
            'u.username' => '%' . $keyword . '%'
        ]
    ];
    $arr_where['OR'][] = [
        'like' => [
            'i.mst' => '%' . $keyword . '%'
        ]
    ];
    $arr_where['OR'][] = [
        'like' => [
            'u.first_name' => '%' . $keyword . '%'
        ]
    ];
    $arr_where['OR'][] = [
        'like' => [
            'u.last_name' => '%' . $keyword . '%'
        ]
    ];
    $arr_where['OR'][] = [
        'like' => [
            'u.email' => '%' . $keyword . '%'
        ]
    ];
}

if ($banner_id >= 0) {
    $is_search = true;
    $base_url .= '&banner_id=' . $banner_id;
    $arr_where['AND'][] = [
        '=' => [
            'c.bid' => $banner_id
        ]
    ];
}
if ($frclick > 0) {
    $is_search = true;
    $arr_where['AND'][] = [
        '>=' => [
            'c.click_time' => $frclick
        ]
    ];
    $base_url .= '&fromtime=' . $from_click;
}

if ($toclick > 0) {
    $is_search = true;
    $arr_where['AND'][] = [
        '<=' => [
            'c.click_time' => $toclick
        ]
    ];
    $base_url .= '&totime=' . $to_click;
}
$params = [
    'page' => $page,
    'perpage' => $per_page,
    'where' => $arr_where,
    'banner_id' => $banner_id,
    'keyword' => $keyword
];
$_arr_data = $list_userid = $array_banner = [];
$_total = 0;
$api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
$api->setModule('bannersdt')
    ->setLang('vi')
    ->setAction('ListUsersClickBanners')
    ->setData($params);
$result = $api->execute();

if ($result['status'] == 'success') {    
    $array_banner = $result['arr_banner'];
    if ($is_search) {
        $_total = $result['total'];
        $_arr_data = $result['data'];
        $list_userid = $result['list_userid'];
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('SEARCH_VALUE', $keyword);
$xtpl->assign('CHECKSESS', md5(NV_CHECK_SESSION . '_' . $module_name . '_' . $op));
$xtpl->assign('FORM_ACTION', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);
$xtpl->assign('FROMTIME', $from_click);
$xtpl->assign('TOTIME', $to_click);

if ($is_search) {
    if (!empty($list_userid)) {
        $where_lead['AND'][] = [
            'IN' => [
                'user_id' => '(' . implode(',', $list_userid) . ')'
            ]
        ];
        $params_leads = [
            'userid' => $admin_info['userid'],
            'where' => $where_lead
        ];
        $data_leads = nv_local_api('ListAllLeads', $params_leads, $admin_info['username'], $module_name);
        $check_leads = json_decode($data_leads, true);
        if (isset($check_leads['data'])) {
            $_temp_lead = '';
            $list_leads = [];
            foreach ($check_leads['data'] as $key => $value) {
                $_link_view = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $value['id'] . '&showheader=1';
                $_temp_lead = '<a href="' . $_link_view . '">' . $value['name'] . '</a>';
                $list_leads[$value['user_id']][] = $_temp_lead;
                $_arr_data[$value['user_id']]['list_leads'] = implode('<br/>', $list_leads[$value['user_id']]);
            }
        }
    }    
    if (!empty($_arr_data)) {
        foreach ($_arr_data as $key => $values) {
            $values['total_bn'] = count($values['list_banners']);
            $xtpl->assign('DATA', $values);
            $first_row = true;
            foreach ($values['list_banners'] as $key => $_bn) {
                $xtpl->assign('BANNERS', $_bn);
                if ($first_row) {
                    $xtpl->parse('main.loop.user_row');
                    $first_row = false;
                }
                $xtpl->parse('main.loop.banners');
            }
            $xtpl->parse('main.loop');
        }
    } else {
        $xtpl->parse('main.empty_data');
    }

    $generate_page = nv_generate_page($base_url, $_total, $per_page, $page);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.footer.generate_page');
        $xtpl->parse('main.footer');
    }
}

foreach ($array_banner as $_bnr) {
    $_bnr['selected'] = $_bnr['id'] == $banner_id ? 'selected="selected"' : '';
    $xtpl->assign('BANNER_SELECT', $_bnr);
    $xtpl->parse('main.banner_sl');
}

if (!empty($error)) {
    $xtpl->assign('ERROR', $error);
    $xtpl->parse('main.error');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');
include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
