<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 10/03/2010 10:51
 */

if (!defined('NV_SYSTEM')) {
    die('Stop!!!');
}

define('NV_IS_MOD_ELINK', true);
define('NV_BIDDING_TABLE', NV_PREFIXLANG . '_bidding');

// Các site thanh toán
$sql = "SELECT * FROM " . $db_config['prefix'] . "_wallet_sites ORDER BY weight ASC";
$global_array_sites = $nv_Cache->db($sql, 'id', 'wallet');

// Các thiết lập
$sql = 'SELECT config_name, config_value FROM ' . $db_config['prefix'] . '_' . $module_data . '_config';
$list = $nv_Cache->db($sql, '', $module_name);
$global_array_config = [];
foreach ($list as $values) {
    $global_array_config[$values['config_name']] = $values['config_value'];
}

$sql = 'SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_config_group';
$global_array_config['groups'] = $nv_Cache->db($sql, 'group_id', $module_name);

$groups_allowed = [];
foreach ($global_array_config['groups'] as $row) {
    if (!empty($row['promo_enable'])) {
        $groups_allowed[] = $row['group_id'];
    }
}
$global_array_config['groups_allowed'] = $groups_allowed;
$global_array_config['groups_allowed_string'] = implode(',', $groups_allowed);
unset($groups_allowed);

$array_mod_title = [];

// Cộng tác viên chính thức hay không
$is_official_coll = false;
if (defined('NV_IS_USER')) {
    $is_official_coll = nv_user_in_groups($global_array_config['group_id_collaborator']);
}

/**
 * Lấy số tiền CTV được nhận của một đơn hàng
 *
 * @param array $order
 * @return number
 */
function EL_GetAmountReceivedFromOrder($order)
{
    if (empty($order['promo_userid'])) {
        return 0;
    }

    global $global_array_config;

    $max_promo_value = !empty($order['official_collaborator']) ? $global_array_config['max_official_promo_value'] : $global_array_config['max_freelance_promo_value'];
    $discount_max = !empty($order['official_collaborator']) ? $global_array_config['discount_official'] : $global_array_config['discount_freelance'];

    // Giảm giá số tiền cố định
    if ($order['promo_type'] == 1) {
        // Vượt quá giới hạn giảm giá thì không còn chiết khấu nữa
        if ($order['promo_value']  >= $max_promo_value) {
            return 0;
        }

        // CTV chỉ dc nhận hoa hồng theo khoản chênh với giảm giá đã giảm cho khách
        $affilicate_value = ($max_promo_value - $order['discount']) < 0 ? 0 : $max_promo_value - $order['discount'];
        $affilicate_value = intval($affilicate_value);

        return $affilicate_value;
    }

    /*
     * Giảm giá theo %
     * Vượt quá giới hạn thì không còn chiết khấu
     */
    if ($order['promo_value'] >= $discount_max) {
        return 0;
    }

    $max_affilicate_value = $order['total'] * $discount_max / 100;
    // CTV chỉ dc nhận hoa hồng theo khoản chênh với giảm giá đã giảm cho khách
    $affilicate_value = ($max_affilicate_value - $order['discount']) < 0 ? 0 : $max_affilicate_value - $order['discount'];
    $affilicate_value = intval($affilicate_value);

    return $affilicate_value;
}
