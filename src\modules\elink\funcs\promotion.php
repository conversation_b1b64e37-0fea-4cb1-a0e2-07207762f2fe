<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Apr 20, 2010 10:47:41 AM
 */

if (!defined('NV_IS_MOD_ELINK')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

$page_title = $nv_Lang->getModule('promotion_manager');
$description = $keywords = 'no';

if (!defined('NV_IS_USER')) {
    $link_redirect = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    nv_redirect_location($link_redirect);
}

// <PERSON><PERSON><PERSON> g<PERSON>i VIP
$array_vip_plans = [
    1, // VIP 1
    2, // VIP 2
    3, // VIP 3
    31, // VIP 3 plus
    32, // PLP Report
    11, // VIP 1 Qu<PERSON><PERSON> tế
    21, // VIP 2 Quốc tế
    5, // VIP 5
    4, // VIP 4
    99, // VIEWEB
    88, // X1,
    89, // X2,
    77, // T0
    19, // PRO 1
    7, // VIP 7
    //69, // Siêu vip
    6, // VIp6
    55,
    66, // API VIP
    68, // API PRO
    100, // T100
    8, // VIP 8
    101 //X4
];
$array_vip_en = [1, 2];

$array_vip2_plans = [
    'abasic', 'avip1', 'apro1', 'apro2',
    'bvieweb', 'bvip1', 'bvip2', 'bpro',
    'x3'
];

$error = '';
$array = [];
$array['promotion_enabled'] = nv_user_in_groups($global_array_config['groups_allowed_string']);
$array['promotion_numallowed'] = 1;
$array['promotion_typeallowed'] = [];
$array['promotion_couponmax'] = 10;
$array['promotion_vouchermax'] = 299000;

// Xác định số Promotion Code tối đa thành viên được tạo. Bằng số lớn nhất trong các nhóm
$in_groups = $user_info['in_groups'];
if (!empty($user_info['group_id'])) {
    $in_groups[] = $user_info['group_id'];
    $in_groups = array_filter(array_unique($in_groups));
}
if (empty($in_groups)) {
    $in_groups = [4];
}
foreach ($in_groups as $group_id) {
    if (isset($global_array_config['groups'][$group_id]) and !empty($global_array_config['groups'][$group_id]['promo_enable'])) {
        // Số mã được tạo
        if ($global_array_config['groups'][$group_id]['promo_number'] > $array['promotion_numallowed']) {
            $array['promotion_numallowed'] = $global_array_config['groups'][$group_id]['promo_number'];
        }
        // Loại khuyến mãi được áp dụng
        if (!in_array(0, $array['promotion_typeallowed']) and !empty($global_array_config['groups'][$group_id]['promo_coupon'])) {
            $array['promotion_typeallowed'][] = 0;
        }
        if (!in_array(1, $array['promotion_typeallowed']) and !empty($global_array_config['groups'][$group_id]['promo_voucher'])) {
            $array['promotion_typeallowed'][] = 1;
        }

        // Giá trị lớn nhất
        if ($array['promotion_couponmax'] < $global_array_config['groups'][$group_id]['promo_couponmax']) {
            $array['promotion_couponmax'] = $global_array_config['groups'][$group_id]['promo_couponmax'];
        }
        if ($array['promotion_vouchermax'] < $global_array_config['groups'][$group_id]['promo_vouchermax']) {
            $array['promotion_vouchermax'] = $global_array_config['groups'][$group_id]['promo_vouchermax'];
        }
    }
}

if (empty($array['promotion_typeallowed'])) {
    $array['promotion_enabled'] = false;
}

if (!$array['promotion_enabled']) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}

$array_mod_title[] = array(
    'catid' => 0,
    'title' => $page_title,
    'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op
);

// Lấy thống kê nhanh 5 đơn hàng gần đây
if (isset($_POST['gettoporders']) and $nv_Request->get_title('gettoporders', 'post', '') == NV_CHECK_SESSION) {
    $respon = [
        'message' => '',
        'updated_time' => '',
        'content' => ''
    ];
    $reload = $nv_Request->get_absint('reload', 'post', 0);
    $site_id = $nv_Request->get_absint('site_id', 'post', 1);
    if (!isset($global_array_sites[$site_id])) {
        $site_id = 1;
    }

    $cacheFile = 'promotoporders' . $site_id . '_' . $user_info['userid'] . '_' . NV_CACHE_PREFIX . '.cache';
    $cacheTTL = 3600; // 1 tiếng cache
    if (!$reload and ($cache = $nv_Cache->getItem($module_name, $cacheFile, $cacheTTL)) != false) {
        $respon['content'] = $cache;
        $respon['updated_time'] = nv_date('H:i d/m/Y', filemtime(NV_ROOTDIR . '/' . NV_CACHEDIR . '/' . $module_name . '/' . $cacheFile));
    } else {
        $site = $global_array_sites[$site_id];
        $api = new DoApi(NV_SERVER_PROTOCOL . '://' . $site['sitedomain'] . '/api.php', $site['api_key'], $site['api_secret']);
        $api->setModule($site_id == 1 ? 'bidding' : 'dn')
        ->setLang('vi')
        ->setAction('ListElinkPromotionOrders')
        ->setData([
            'userid' => $user_info['userid'],
            'page' => 1,
            'per_page' => 5,
        ]);
        $result_api = $api->execute();
        $error = $api->getError();

        if (!empty($error)) {
            $respon['message'] = $error;
            nv_jsonOutput($respon);
        }
        if ($result_api['status'] != 'success') {
            $respon['message'] = $result_api['message'] ?: 'Api error with no respon';
            nv_jsonOutput($respon);
        }

        $respon['content'] = nv_url_rewrite(nv_elink_theme_promotion_top_orders($result_api['orders'], $result_api['vips']), true);
        $nv_Cache->setItem($module_name, $cacheFile, $respon['content'], $cacheTTL);

        $respon['updated_time'] = nv_date('H:i d/m/Y', NV_CURRENTTIME);
    }

    nv_jsonOutput($respon);
}

// Lấy mã Promotion không trùng
if ($nv_Request->isset_request('getuniquecode', 'post')) {
    if (!defined('NV_IS_AJAX')) {
        die('Wrong URL!!!');
    }

    $respon = [
        'status' => 'ERROR',
        'message' => 'ERROR'
    ];

    $promo_code = '';
    while (empty($promo_code) or $db->query("SELECT COUNT(promo_code) FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE promo_code=" . $db->quote($promo_code))->fetchColumn()) {
        $promo_code = nv_strtoupper(nv_genpass($global_array_config['promotion_code_length']));
    }

    $respon['status'] = 'SUCCESS';
    $respon['message'] = $promo_code;

    nv_jsonOutput($respon);
}

// Lấy hết Promotion Code đã tạo
$array['data'] = [];
$array_promocode = [];
$sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE userid=" . $user_info['userid'] . " ORDER BY add_time DESC";
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $row['link_edit'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/edit/' . $row['promo_code'];
    $row['link_view_users'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/u-' . $row['promo_code'];
    $row['link_view_orders'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/o-' . $row['promo_code'];
    $row['link_view_links'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=affiliate-links&amp;p=' . $row['promo_code'];
    $array['data'][$row['promo_code']] = $row;
    $array_promocode[$row['promo_code']] = $row['promo_code'];
}

// Tạo link chứa
if ($nv_Request->isset_request('creatpromolink', 'post')) {
    if (!defined('NV_IS_AJAX')) {
        die('Wrong URL!!!');
    }

    $respon = [
        'status' => 'ERROR',
        'message' => 'ERROR',
        'link' => 'ERROR'
    ];

    $code = $nv_Request->get_title('code', 'post', '');
    if (!isset($array_promocode[$code])) {
        nv_jsonOutput($respon);
    }
    $link = str_replace('&amp;', '&', $nv_Request->get_title('link', 'post', ''));
    $url_data = parse_url($link);

    // Các domain được phép tạo link
    $allowed_server = [];
    foreach ($global_array_sites as $site) {
        $allowed_server[] = $site['sitedomain'];
    }

    if (empty($link) or $url_data === false or empty($url_data['path'])) {
        $respon['message'] = $nv_Lang->getModule('aff_error_link1');
    } elseif (isset($url_data['host']) and !in_array($url_data['host'], $allowed_server)) {
        $respon['message'] = $nv_Lang->getModule('aff_error_link2') . ' ' . implode(', ', $allowed_server);
    } else {
        $query_string = [];
        if (!empty($url_data['query'])) {
            parse_str(urldecode($url_data['query']), $query_string);
        }
        unset($query_string['km']);
        $query_string['km'] = $code;
        $respon['status'] = 'SUCCESS';
        $respon['link'] = '';
        if (isset($url_data['host'])) {
            if (isset($url_data['scheme'])) {
                $respon['link'] .= $url_data['scheme'] . '://';
            }
            $respon['link'] .= $url_data['host'];
        } else {
            $respon['link'] = NV_MY_DOMAIN;
        }
        $respon['link'] .= $url_data['path'] . '?' . http_build_query($query_string);
    }

    nv_jsonOutput($respon);
}

// Xoá mã
if ($nv_Request->isset_request('delete', 'post')) {
    if (!defined('NV_IS_AJAX')) {
        die('Wrong URL!!!');
    }

    $respon = [
        'status' => 'ERROR',
        'message' => 'ERROR'
    ];

    $promo_code = $nv_Request->get_title('code', 'post', '');
    if (isset($array['data'][$promo_code])) {
        // Xóa code
        $db->query("DELETE FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE promo_code=" . $db->quote($promo_code));

        // Xóa thống kê
        $db->query("DELETE FROM " . $db_config['prefix'] . "_" . $module_data . "_statistics WHERE stype=1 AND sval=" . $db->quote($promo_code));

        $db->query("UPDATE " . NV_USERS_GLOBALTABLE . " SET num_promot_code = num_promot_code - 1 WHERE userid = " . $array['data'][$promo_code]['userid']);

        $nv_Cache->delMod($module_name);

        $respon['status'] = 'SUCCESS';
    }

    nv_jsonOutput($respon);
}

// Tạo mới
if (isset($array_op[1]) and ($array_op[1] == 'creat' or ($array_op[1] == 'edit' and !empty($array_op[2])))) {
    if ($array_op[1] == 'creat') {
        // Không cho tạo quá số lượng
        if (sizeof($array['data']) >= $array['promotion_numallowed']) {
            nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
        }

        $page_title = $nv_Lang->getModule('promotion_creat');
        $array['form_action'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1] . (!empty($array_op[2]) ? ('/' . $array_op[2]) : '');
        $data = [
            'promo_code' => '',
            'promo_type' => current($array['promotion_typeallowed']),
            'promo_value' => 0,
            'use_number' => 0,
            'use_user' => 0,
            'use_peruser' => 0,
            'use_type' => [],
            'use_limityear' => [],
            'start_time' => 0,
            'end_time' => 0,
            'vip_apply' => [],
            'vip_include' => [],
            'is_affiliate' => 0,
            'apply_lang' => NV_LANG_DATA,
            'othernote' => ''
        ];
        $isEdit = false;
    } else {
        if (!isset($array['data'][$array_op[2]])) {
            nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
        }

        $page_title = $nv_Lang->getModule('promotion_edit');
        $array['form_action'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1] . (!empty($array_op[2]) ? ('/' . $array_op[2]) : '');
        $row = $array['data'][$array_op[2]];
        $data = [
            'promo_code' => $row['promo_code'],
            'promo_type' => $row['promo_type'],
            'promo_value' => $row['promo_value'],
            'use_number' => $row['use_number'],
            'use_user' => $row['use_user'],
            'use_peruser' => $row['use_peruser'],
            'use_type' => $row['use_type'] === '' ? [] : explode(',', $row['use_type']),
            'use_limityear' => empty($row['use_limityear']) ? [] : explode(',', $row['use_limityear']),
            'start_time' => $row['start_time'],
            'end_time' => $row['end_time'],
            'vip_apply' => empty($row['vip_apply']) ? [] : explode(',', $row['vip_apply']),
            'vip_include' => empty($row['vip_include']) ? [] : explode(',', $row['vip_include']),
            'is_affiliate' => $row['is_affiliate'],
            'apply_lang' => $row['apply_lang'],
            'othernote' => nv_br2nl($row['othernote'])
        ];
        $isEdit = true;
        $oldProCodeDB = nv_strtoupper($row['promo_code']);
    }
    $array_mod_title[] = array(
        'catid' => 0,
        'title' => $page_title,
        'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1] . (!empty($array_op[2]) ? ('/' . $array_op[2]) : '')
    );

    if ($nv_Request->isset_request('submit', 'post')) {
        $data['promo_code'] = $nv_Request->get_title('promo_code', 'post', '');
        $data['promo_type'] = $nv_Request->get_int('promo_type', 'post', 0);
        $data['promo_value'] = $nv_Request->get_float('promo_value', 'post', 0);
        $data['use_number'] = $nv_Request->get_int('use_number', 'post', 0);
        $data['use_user'] = $nv_Request->get_int('use_user', 'post', 0);
        $data['use_peruser'] = $nv_Request->get_int('use_peruser', 'post', 0);
        $data['use_limityear'] = $nv_Request->get_typed_array('use_limityear', 'post', 'int', []);
        $data['use_type'] = $nv_Request->get_typed_array('use_type', 'post', 'int', []);
        $data['start_time'] = $nv_Request->get_string('start_time', 'post', '');
        $data['end_time'] = $nv_Request->get_string('end_time', 'post', '');
        $data['vip_apply'] = $nv_Request->get_typed_array('vip_apply', 'post', 'title', []);
        $data['vip_include'] = $nv_Request->get_typed_array('vip_include', 'post', 'title', []);
        $data['is_affiliate'] = intval($nv_Request->get_bool('is_affiliate', 'post', false));
        $data['apply_lang'] = $nv_Request->get_title('apply_lang', 'post', '');

        $data['othernote'] = $nv_Request->get_string('othernote', 'post', '');
        $data['othernote'] = nv_nl2br(nv_htmlspecialchars(strip_tags($data['othernote'])));

        $proCodeDB = nv_strtoupper($data['promo_code']);

        if (preg_match('/^([0-9]{2})\/([0-9]{2})\/([0-9]{4})$/', $data['start_time'], $m)) {
            $data['start_time'] = mktime(0, 0, 0, intval($m[2]), intval($m[1]), intval($m[3]));
        } else {
            $data['start_time'] = 0;
        }
        if (preg_match('/^([0-9]{2})\/([0-9]{2})\/([0-9]{4})$/', $data['end_time'], $m)) {
            $data['end_time'] = mktime(23, 59, 59, intval($m[2]), intval($m[1]), intval($m[3]));
        } else {
            $data['end_time'] = 0;
        }
        if ($data['promo_type'] == 1) {
            $data['promo_value'] = intval($data['promo_value']);
        }
        if ($data['use_number'] < 0 or $data['use_number'] > 9999999999) {
            $data['use_number'] = 0;
        }
        if ($data['use_user'] < 0 or $data['use_user'] > 9999999999) {
            $data['use_user'] = 0;
        }
        if ($data['use_peruser'] < 0 or $data['use_peruser'] > 9999999999) {
            $data['use_peruser'] = 0;
        }
        if (!in_array($data['apply_lang'], $global_config['allow_sitelangs'])) {
            $data['apply_lang'] = '';
        }
        $allowed1_vips = $data['apply_lang'] == 'en' ? $array_vip_en : $array_vip_plans;
        $data['use_limityear'] = array_intersect($data['use_limityear'], explode(',', $global_array_config['promotion_use_limityear']));
        $data['vip_apply'] = array_intersect($data['vip_apply'], array_merge_recursive($allowed1_vips, $array_vip2_plans));
        $data['vip_include'] = array_intersect($data['vip_include'], array_merge_recursive($allowed1_vips, $array_vip2_plans));

        // Kiểm tra mã bị trùng
        $isExists = 0;
        if (!empty($data['promo_code'])) {
            $isExists = $db->query("SELECT COUNT(promo_code) FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE promo_code=" . $db->quote($proCodeDB) . ($isEdit ? ' AND promo_code!=' . $db->quote($oldProCodeDB) : ''))->fetchColumn();
        }

        if (empty($data['promo_code'])) {
            $error = $nv_Lang->getModule('promotion_error_nocode');
        } elseif (!preg_match('/^([a-zA-Z0-9]{' . $global_array_config['promotion_code_length'] . ',50})$/i', $data['promo_code'])) {
            $error = sprintf($nv_Lang->getModule('promotion_error_rule'), $global_array_config['promotion_code_length']);
        } elseif ($isExists) {
            $error = $nv_Lang->getModule('promotion_error_exists');
        } elseif (!in_array($data['promo_type'], $array['promotion_typeallowed'])) {
            $error = $nv_Lang->getModule('promotion_error_typeallowed');
        } elseif ($data['promo_value'] <= 0) {
            $error = $nv_Lang->getModule('promotion_error_value_min');
        } elseif (($data['promo_type'] == 1 and $data['promo_value'] > $array['promotion_vouchermax']) or ($data['promo_type'] == 0 and $data['promo_value'] > $array['promotion_couponmax'])) {
            $error = $nv_Lang->getModule('promotion_error_value_max');
        } elseif ($data['use_number'] > 0 and $data['use_peruser'] > $data['use_number']) {
            $error = $nv_Lang->getModule('promotion_error_use_value');
        } elseif (!empty($data['start_time']) and !empty($data['end_time']) and $data['end_time'] < $data['start_time']) {
            $error = $nv_Lang->getModule('promotion_error_time');
        } else {
            if ($isEdit) {
                $sql = "UPDATE " . $db_config['prefix'] . "_" . $module_data . "_promotion_code SET
                    promo_code=" . $db->quote($proCodeDB) . ",
                    promo_type=" . $data['promo_type'] . ",
                    promo_value=" . $data['promo_value'] . ",
                    use_number=" . $data['use_number'] . ",
                    use_user=" . $data['use_user'] . ",
                    use_peruser=" . $data['use_peruser'] . ",
                    use_limityear=" . $db->quote(implode(',', $data['use_limityear'])) . ",
                    use_type=" . $db->quote(implode(',', $data['use_type'])) . ",
                    start_time=" . $data['start_time'] . ",
                    end_time=" . $data['end_time'] . ",
                    edit_time=" . NV_CURRENTTIME . ",
                    vip_apply=" . $db->quote(implode(',', $data['vip_apply'])) . ",
                    vip_include=" . $db->quote(implode(',', $data['vip_include'])) . ",
                    apply_lang=" . $db->quote($data['apply_lang']) . ",
                    is_affiliate=" . $data['is_affiliate'] . ",
                    othernote=" . $db->quote($data['othernote']) . "
                WHERE promo_code=" . $db->quote($oldProCodeDB);
            } else {
                $sql = "INSERT INTO " . $db_config['prefix'] . "_" . $module_data . "_promotion_code (
                    userid, promo_code, promo_type, promo_value, use_number, use_user, use_peruser, use_limityear, use_type,
                    start_time, end_time, add_time, vip_apply, vip_include, apply_lang, is_affiliate, othernote
                ) VALUES (
                    " . $user_info['userid'] . ", " . $db->quote($proCodeDB) . ", " . $data['promo_type'] . ",
                    " . $data['promo_value'] . ", " . $data['use_number'] . ", " . $data['use_user'] . ", " . $data['use_peruser'] . ",
                    " . $db->quote(implode(',', $data['use_limityear'])) . ", " . $db->quote(implode(',', $data['use_type'])) . ",
                    " . $data['start_time'] . ", " . $data['end_time'] . ", " . NV_CURRENTTIME . ", " . $db->quote(implode(',', $data['vip_apply'])) . ",
                    " . $db->quote(implode(',', $data['vip_include'])) . ", " . $db->quote($data['apply_lang']) . ", " . $data['is_affiliate'] . ", " . $db->quote($data['othernote']) . "
                )";
            }

            if ($db->exec($sql)) {
                if ($isEdit) {
                    // Cập nhật các mã khuyến mãi không mặc định
                    if ($data['is_affiliate']) {
                        $db->query("UPDATE " . $db_config['prefix'] . "_" . $module_data . "_promotion_code SET is_affiliate=0 WHERE promo_code!=" . $db->quote($oldProCodeDB) . " AND userid=" . $user_info['userid']);
                    }

                    // Cập nhật thống kê khi thay đổi mã khuyến mãi
                    if ($proCodeDB != $oldProCodeDB) {
                        $db->query("UPDATE " . $db_config['prefix'] . "_" . $module_data . "_statistics SET sval=" . $db->quote($proCodeDB) . " WHERE sval=" . $db->quote($oldProCodeDB) . ' AND stype=1');
                    }
                } else {
                    $db->query("UPDATE " . NV_USERS_GLOBALTABLE . " SET num_promot_code = num_promot_code + 1 WHERE userid = " . $user_info['userid']);
                }

                $nv_Cache->delMod($module_name);
                nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
            } else {
                $data['othernote'] = nv_br2nl($data['othernote']);
                $error = "Unknow Error!";
            }
        }
    }

    $contents = nv_elink_theme_promotion_content($array, $data, $error, $isEdit);

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

// Xem thống kê người dùng
if (isset($array_op[1]) and preg_match('/^u\-([a-zA-Z0-9]+)$/', $array_op[1], $op_code) and isset($array['data'][$op_code[1]])) {
    $page_title = $nv_Lang->getModule('promotion_view_user1') . ' ' . $op_code[1];
    $array_mod_title[] = array(
        'catid' => 0,
        'title' => $page_title,
        'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1]
    );
    $promo = $array['data'][$op_code[1]];

    $page = 1;
    $per_page = 20;
    $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1];
    if (isset($array_op[2])) {
        if (preg_match('/^page\-([0-9]+)$/i', $array_op[2], $m)) {
            $page = intval($m[1]);
        } else {
            nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1]);
        }
        if ($page > 99999999 or $page < 1) {
            nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1]);
        }
    }
    if (isset($array_op[3])) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1]);
    }

    // Xác định tổng số thành viên đã sử dụng
    $sql = "SELECT COUNT(DISTINCT use_userid) FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code_use WHERE promo_id=" . $promo['promo_id'] . " AND status=1";
    $promo['use_user_count'] = $db->query($sql)->fetchColumn();

    $db->sqlreset()->from($db_config['prefix'] . "_" . $module_data . "_promotion_code_use tb1, " . NV_USERS_GLOBALTABLE . " tb2");
    $db->where("tb1.use_userid=tb2.userid AND tb1.promo_id=" . $promo['promo_id'] . " AND tb1.status=1");
    $db->select('COUNT(tb2.userid)');

    $num_items = $db->query($db->sql())->fetchColumn();

    $db->order('tb1.use_time DESC')->limit($per_page)->offset(($page - 1) * $per_page);
    $db->select('tb1.use_time, tb2.userid, tb2.email, tb2.first_name, tb2.last_name, tb2.regdate');

    $result = $db->query($db->sql());

    $array_userids = [];
    $array_users = [];
    while ($row = $result->fetch()) {
        $array_users[$row['userid']] = $row;
        $array_userids[$row['userid']] = $row['userid'];
    }

    // Lấy số điện thoại các thành viên trên
    $array_user_phone = [];
    $array_user_vip = [];
    if (!empty($array_userids)) {
        $sql = "SELECT userid, phone FROM " . NV_USERS_GLOBALTABLE . "_info WHERE userid IN(" . implode(',', $array_userids) . ")";
        $result = $db->query($sql);
        while ($row = $result->fetch()) {
            $array_user_phone[$row['userid']] = $row['phone'];
        }

        // Lấy các gói VIP
        $arr_where = [];
        foreach ($array_userids as $uid) {
            $arr_where['OR'][] = [
                '=' => [
                    'user_id' => $uid
                ]
            ];
        }
        $arr_where['AND_OR'][] = [
            '=' => [
                'status' => 1
            ]
        ];
        $arr_where['AND_OR'][] = [
            '=' => [
                'status' => 2
            ]
        ];
        $params_customs = [
            'where' => $arr_where,
            'page' => 1,
            'per_page' => sizeof($array_userids)
        ];

        $api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
        $api->setModule('bidding')
        ->setLang('vi')
        ->setAction('ListBiddingCustoms')
        ->setData($params_customs);
        $result_customs = $api->execute();

        if (is_array($result_customs) and !empty($result_customs['data'])) {
            foreach ($result_customs['data'] as $row) {
                if (!isset($array_user_vip[$row['user_id']][$row['vip']]) or $array_user_vip[$row['user_id']][$row['vip']]['status'] > $row['status']) {
                    $array_user_vip[$row['user_id']][$row['vip']] = $row;
                }
            }
        }
    }

    if ($page > 1 and empty($array['data'])) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1]);
    }
    if ($page > 1) {
        $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . number_format($page, 0, ',', '.');
    }

    $generate_page = nv_alias_page($page_title, $base_url, $num_items, $per_page, $page);
    $contents = nv_elink_theme_promotion_statusers($array, $promo, $array_users, $array_user_phone, $array_user_vip, $generate_page);

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

// Xem thống kê các lần sử dụng
if (isset($array_op[1]) and preg_match('/^o\-([a-zA-Z0-9]+)$/', $array_op[1], $op_code) and isset($array['data'][$op_code[1]])) {
    $page_title = $nv_Lang->getModule('promotion_view_orders') . ' ' . $op_code[1];
    $array_mod_title[] = array(
        'catid' => 0,
        'title' => $page_title,
        'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1]
    );
    $promo = $array['data'][$op_code[1]];

    $page = $nv_Request->get_absint('page', 'get', 1);
    $per_page = 20;
    $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1];
    if (isset($array_op[2])) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1]);
    }
    if ($page > 99999999 or $page < 1) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1]);
    }

    $site_id = $nv_Request->get_absint('s', 'get', 1);
    if (!isset($global_array_sites[$site_id])) {
        $site_id = 1;
    }
    if ($site_id > 1) {
        $base_url .= '&amp;s=' . $site_id;
    }

    $site = $global_array_sites[$site_id];
    $api = new DoApi(NV_SERVER_PROTOCOL . '://' . $site['sitedomain'] . '/api.php', $site['api_key'], $site['api_secret']);
    $api->setModule($site_id == 1 ? 'bidding' : 'dn')
    ->setLang('vi')
    ->setAction('ListElinkPromotionOrders')
    ->setData([
        'promo_id' => $promo['promo_id'],
        'page' => 1,
        'per_page' => $per_page
    ]);
    $result_api = $api->execute();
    $error = $api->getError();

    $error_message = '';
    $array_orders = $array_vips = [];
    $num_items = 0;

    if (!empty($error)) {
        $error_message = $error;
    }
    if ($result_api['status'] != 'success') {
        $error_message = $result_api['message'] ?: 'Api error with no respon';
    }
    if (empty($error_message)) {
        $array_orders = $result_api['orders'];
        $array_vips = $result_api['vips'];
        $num_items = $result_api['num_items'];
    }

    if ($page > 1 and empty($array['data'])) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1]);
    }
    if ($page > 1) {
        $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . number_format($page, 0, ',', '.');
    }

    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
    $contents = nv_elink_theme_promotion_statorders($array, $promo, $array_orders, $array_vips, $generate_page, $error_message);

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

// Tất cả các khách sử dụng
if (isset($array_op[1]) and $array_op[1] == 'all-users') {
    $page_title = $nv_Lang->getModule('promotion_all_users');
    $array_mod_title[] = array(
        'catid' => 0,
        'title' => $nv_Lang->getModule('promotion_all_users1'),
        'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1]
    );

    $page = 1;
    $per_page = 20;
    $base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1];
    if (isset($array_op[2])) {
        if (preg_match('/^page\-([0-9]+)$/i', $array_op[2], $m)) {
            $page = intval($m[1]);
        } else {
            nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1]);
        }
        if ($page > 99999999 or $page < 1) {
            nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1]);
        }
    }
    if (isset($array_op[3])) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1]);
    }

    $sql = "FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code_use tb1, " . NV_USERS_GLOBALTABLE . " tb2,
    " . $db_config['prefix'] . "_" . $module_data . "_promotion_code tb3
    WHERE tb1.use_userid=tb2.userid AND tb1.promo_id=tb3.promo_id AND tb3.userid=" . $user_info['userid'] . " AND tb1.status=1";

    $num_items = $db->query("SELECT COUNT(tb1.id)" . $sql)->fetchColumn();

    $sql = "SELECT tb1.use_time, tb1.promo_code, tb2.userid, tb2.email, tb2.first_name, tb2.last_name, tb2.regdate " . $sql;
    $sql .= " ORDER BY tb1.use_time DESC LIMIT " . (($page - 1) * $per_page) . "," . $per_page;

    $array_users = [];
    $array_userids = [];
    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        $row['link_view'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/u-' . $row['promo_code'];
        $array_users[] = $row;
        $array_userids[$row['userid']] = $row['userid'];
    }

    $array_user_phone = [];
    $array_user_vip = [];
    if (!empty($array_userids)) {
        // Lấy số điện thoại các thành viên trên
        $sql = "SELECT userid, phone FROM " . NV_USERS_GLOBALTABLE . "_info WHERE userid IN(" . implode(',', $array_userids) . ")";
        $result = $db->query($sql);
        while ($row = $result->fetch()) {
            $array_user_phone[$row['userid']] = $row['phone'];
        }

        /*
         * Lấy các gói VIP
         * Chỉ hiển thị gói do mình giới thiệu
         */
        // Lấy các gói VIP
        $arr_where = [];
        foreach ($array_userids as $uid) {
            $arr_where['OR'][] = [
                '=' => [
                    'user_id' => $uid
                ]
            ];
        }
        $arr_where['AND_OR'][] = [
            '=' => [
                'status' => 1
            ]
        ];
        $arr_where['AND_OR'][] = [
            '=' => [
                'status' => 2
            ]
        ];
        $params_customs = [
            'where' => $arr_where,
            'page' => 1,
            'per_page' => sizeof($array_userids)
        ];

        $api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
        $api->setModule('bidding')
        ->setLang('vi')
        ->setAction('ListBiddingCustoms')
        ->setData($params_customs);
        $result_customs = $api->execute();

        if (is_array($result_customs) and !empty($result_customs['data'])) {
            foreach ($result_customs['data'] as $row) {
                if (!isset($array_user_vip[$row['user_id']][$row['vip']]) or $array_user_vip[$row['user_id']][$row['vip']]['status'] > $row['status']) {
                    $array_user_vip[$row['user_id']][$row['vip']] = $row;
                }
            }
        }
    }

    if ($page > 1 and empty($array_users)) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '/' . $array_op[1]);
    }
    if ($page > 1) {
        $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . number_format($page, 0, ',', '.');
    }

    $generate_page = nv_alias_page($page_title, $base_url, $num_items, $per_page, $page);
    $contents = nv_elink_theme_promotion_statusersall($array_users, $array_user_phone, $array_user_vip, $generate_page);

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

$array['link_creat'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/creat';
$array['link_users'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/all-users';
$array['link_orders'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=all-orders&amp;t=p';

// Thống kê truy cập
$array_statistics = [];
if (!empty($array_promocode)) {
    $sql = "SELECT sval, scount FROM " . $db_config['prefix'] . "_" . $module_data . "_statistics WHERE stype=1 AND islink='' AND sval IN('" . implode("','", $array_promocode) . "')";
    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        $array_statistics[$row['sval']] = $row['scount'];
    }
}

// 5 khách hàng vừa dùng promotion code
$sql = "SELECT tb1.id, tb1.use_time, tb1.promo_code, tb2.userid, tb2.email, tb2.first_name, tb2.last_name, tb2.regdate FROM
" . $db_config['prefix'] . "_" . $module_data . "_promotion_code_use tb1, " . NV_USERS_GLOBALTABLE . " tb2, " . $db_config['prefix'] . "_" . $module_data . "_promotion_code tb3
WHERE tb1.use_userid=tb2.userid AND tb1.promo_id=tb3.promo_id AND tb3.userid=" . $user_info['userid'] . " AND tb1.status=1
ORDER BY tb1.use_time DESC LIMIT 0,5";

$array_lastest_users = [];
$array_userids = [];

$result = $db->query($sql);
while ($row = $result->fetch()) {
    $row['link_view'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/u-' . $row['promo_code'];
    $array_lastest_users[] = $row;
    $array_userids[$row['userid']] = $row['userid'];
}

$array_user_phone = [];
$array_user_vip = [];
if (!empty($array_userids)) {
    // Lấy số điện thoại các thành viên trên
    $sql = "SELECT userid, phone FROM " . NV_USERS_GLOBALTABLE . "_info WHERE userid IN(" . implode(',', $array_userids) . ")";
    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        $array_user_phone[$row['userid']] = $row['phone'];
    }

    // Lấy các gói VIP
    $arr_where = [];
    foreach ($array_userids as $uid) {
        $arr_where['OR'][] = [
            '=' => [
                'user_id' => $uid
            ]
        ];
    }
    $arr_where['AND_OR'][] = [
        '=' => [
            'status' => 1
        ]
    ];
    $arr_where['AND_OR'][] = [
        '=' => [
            'status' => 2
        ]
    ];
    $params_customs = [
        'where' => $arr_where,
        'page' => 1,
        'per_page' => sizeof($array_userids)
    ];

    $api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
    $api->setModule('bidding')
    ->setLang('vi')
    ->setAction('ListBiddingCustoms')
    ->setData($params_customs);
    $result_customs = $api->execute();

    if (is_array($result_customs) and !empty($result_customs['data'])) {
        foreach ($result_customs['data'] as $row) {
            if (!isset($array_user_vip[$row['user_id']][$row['vip']]) or $array_user_vip[$row['user_id']][$row['vip']]['status'] > $row['status']) {
                $array_user_vip[$row['user_id']][$row['vip']] = $row;
            }
        }
    }
}

$contents = nv_elink_theme_promotion($array, $array_statistics, $array_lastest_users, $array_user_phone, $array_user_vip);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
