<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 * API lấy danh sách:
 * - <PERSON><PERSON>ểm sẽ hết hạn trong vòng 45 ngày tới
 * - Có dùng điểm trong vòng 30 qua
 * - Có điểm hết hạn không dùng trong vòng 30 ngày qua
 * https://vinades.org/dauthau/dauthau.info/-/issues/2358
 */

 namespace NukeViet\Module\points\Api;

 use NukeViet\Api\Api;
 use NukeViet\Api\ApiResult;
 use NukeViet\Api\IApi;
 use PDOException;
 if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
     die('Stop!!!');
 }

class ListPointsExpired implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'point';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $nv_Request, $db, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/points/points.class.php';
        $point_object = new \nukeviet_points();
        $current_time = NV_CURRENTTIME;

        try {
            $limit = $nv_Request->get_int('limit', 'post', 20);
            $last_id = $nv_Request->get_int('last_id', 'post', 0);
            $maxid = $db->query('SELECT max(userid) as max FROM ' . $db_config['prefix'] . '_' . $module_data . '_customs WHERE nexttime_sendmail <= ' . $current_time . ' AND point_total > 0')->fetchColumn();
            $maxid = !empty($maxid) ? : 0;
            $sql = 'SELECT userid, point_total, nexttime_sendmail FROM ' . $db_config['prefix'] . '_' . $module_data . '_customs WHERE userid > ' . $last_id . '  AND point_total > 0 AND nexttime_sendmail <= ' . $current_time . ' ORDER BY userid ASC LIMIT ' . $limit;
            $sth = $db->query($sql);
            $arr_userid = [];
            while ($row = $sth->fetch()) {
                $arr_userid[] = $row['userid'];
            }
            $max = !empty($arr_userid) ? max($arr_userid) : 0;
            $expired_time = $current_time + 45 * 24 * 60 * 60;
            $_30_days_ago = $current_time - 30 * 24 * 60 * 60;

            /**
             * - Điểm sẽ hết hạn trong vòng 45 ngày tới
             * - Có dùng điểm trong vòng 30 ngày qua
             * - Có điểm hết hạn không dùng trong vòng 30 ngày qua
             */
            $_data = [];

            if (!empty($arr_userid)) {
                $where = ' AND ((expired > ' . $current_time . ' AND expired <= ' . $expired_time . ')
                        OR (created_time >= ' . $_30_days_ago . ' AND created_time <= ' . $current_time . ' AND status = -1)
                        OR (expired >= ' . $_30_days_ago . ' AND expired <= ' . $current_time . ' AND check_expired = 1))';

                $_sql = 'SELECT DISTINCT(userid) FROM ' . $db_config['prefix'] . '_' . $module_data . '_log WHERE userid IN (' . implode(',', $arr_userid). ')' . $where;
                $sth = $db->query($_sql);
                $pre_month_time = strtotime("-1 month", $current_time);
                $month = date('m', $pre_month_time);
                $year = date('Y', $pre_month_time);
                $startOfMonth = mktime(0, 0, 0, $month, 1, $year);
                $endOfMonth = mktime(23, 59, 59, $month, date('t', $startOfMonth), $year);
                while ($_row = $sth->fetch()) {

                    // Lấy thông tin tên, username, email của user
                    $_user = $db->query('SELECT userid, first_name, last_name, username, email FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $_row['userid'])->fetch();
                    if (!empty($_user)) {

                        // Tổng số điểm trong tài khoản
                        $_point = $point_object->my_point($_user['userid']);
                        $_row['total_point'] = $_point['point_total'];

                        // Số điểm đã sử dụng tháng trước
                        $_row['total_point_used'] = $db->query('SELECT CASE WHEN COUNT(*) > 0 THEN SUM(point_total) ELSE 0 END AS total FROM ' . $db_config['prefix'] . '_' . $module_data . '_log WHERE userid = ' . $_row['userid'] . ' AND created_time >= ' . $startOfMonth . ' AND created_time <= ' . $endOfMonth . ' AND status = -1 AND type_transaction > 0 AND type_transaction < 1000')->fetchColumn();

                        // Tổng số điểm sẽ hết hạn trong vòng 45 ngày tới
                        $_row['total_point_expired'] = $db->query('SELECT CASE WHEN COUNT(*) > 0 THEN SUM(point_total) ELSE 0 END AS total FROM ' . $db_config['prefix'] . '_' . $module_data . '_log WHERE userid = ' . $_row['userid'] . ' AND expired > ' . $current_time . ' AND expired <= ' . $expired_time)->fetchColumn();

                        // Số điểm tháng trước chưa sử dụng bị quá hạn
                        $_row['total_point_expired_no_used'] = $db->query('SELECT CASE WHEN COUNT(*) > 0 THEN SUM(point_total) ELSE 0 END AS total FROM ' . $db_config['prefix'] . '_' . $module_data . '_log WHERE userid = ' . $_row['userid'] . ' AND expired >= ' . $startOfMonth . ' AND expired <= ' . $endOfMonth . ' AND check_expired = 1')->fetchColumn();

                        $_row['first_name'] = $_user['first_name'];
                        $_row['last_name'] = $_user['last_name'];
                        $_row['username'] = $_user['username'];
                        $_row['email'] = $_user['email'];
                        $_row['userid'] = $_user['userid'];
                        $_data[$_row['userid']] = $_row;
                    }
                }
            }

            $this->result->setSuccess();
            $this->result->set('data', $_data);
            $this->result->set('max_userid', $max);
            $this->result->set('maxid', $maxid);
            return $this->result->getResult();

        } catch (PDOException $e) {
            $this->result->setError();
            $this->result->setCode('3002');
            $this->result->setMessage(print_r($e, true));
            return $this->result->getResult();
        }
    }
}
