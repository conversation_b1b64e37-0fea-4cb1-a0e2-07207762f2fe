<?php

/**
 * @Project SSO
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @Createdate 18/7/2020, 9:40
 */

if (!defined('NV_IS_MOD_USER')) {
    die('Stop!!!');
}

/**
 * Require tệp này để buộc SSO server ghi lại thông tin đăng nhập
 * của thành viên sau khi đã thông qua một số bước xác thực mà đảm bảo an toàn trước đó
 * Ví dụ như đăng nhập qua OpenID, Oauth, Google Identity (GSI), Passkey
 */

$broker = new Jasny\SSO\Broker(SSO_SERVER, SSO_BROKER_ID, SSO_BROKER_SECRET);
$broker->attach(true);

$error = 'Unspecified error, please contact support for guidance';
$result_json = (!empty($server) and $server == 'google-identity' and ($op ?? '') == 'oauth');
/** @disregard P1011 */
$signin_result = (defined('NV_SET_LOGIN_MODE') and NV_SET_LOGIN_MODE == 'PASSKEY');
$set_only = (defined('NV_SET_LOGIN_MODE') and NV_SET_LOGIN_MODE == 'NORMALLY');

try {
    if (!$signin_result and !$set_only) {
        $query = 'SELECT * FROM ' . NV_MOD_TABLE . ' WHERE userid=' . $user_id;
        $row = $db->query($query)->fetch();
    }

    // Xác định xem có phải đăng nhập thông qua cổng của bên thứ 3 không?
    $login_provider = $login_uid = '';
    if (!empty($reg_attribs) and is_array($reg_attribs)) {
        // Trường hợp đăng kí qua OpenID, Oauth, GSI
        $login_provider = $reg_attribs['server'] ?? '';
        $login_uid = $reg_attribs['opid'] ?? $reg_attribs['email'] ?? '';
    } elseif (!empty($attribs) and is_array($attribs) and !empty($opid)) {
        // Trường hợp đăng nhập qua OpenID, Oauth
        $login_provider = $attribs['server'] ?? '';
        $login_uid = $opid;
    } elseif (($server ?? '') == 'google-identity' and !empty($credential) and is_array($credential) and !empty($opid)) {
        // Trường hợp đăng nhập qua Google Identity (GSI)
        $login_provider = 'google-identity';
        $login_uid = $opid;
    }

    /** @disregard P1011 */
    $validate_hash = (defined('NV_SET_LOGIN_MODE') ? ('{' . NV_SET_LOGIN_MODE . '}') : '{SSO_SET}') . md5($row['password'] . '_' . SSO_BROKER_SECRET);
    if ($broker->login(
        $row['username'],
        $validate_hash,
        passkey: ($row['passkey_name'] ?? ''),
        login_provider: $login_provider,
        login_uid: $login_uid
    )) {
        $user_info = $broker->getUserInfo();
        if (isset($user_info['userid'])) {
            global $nv_Request;
            $nv_Request->set_Session('ssochecknum', $user_info['checknum']);

            if (!$set_only) {
                // Trả về khi đăng nhập thông thường từ passkey
                if ($signin_result) {
                    signin_result([
                        'status' => 'ok',
                        'mess' => $nv_Lang->getModule('login_ok')
                    ]);
                }

                // Trả về khi đăng nhập Google Identity (GSI) đã liên kết trước đó
                if ($result_json) {
                    $redirect = nv_redirect_decrypt($nv_redirect ?? '');
                    if (defined('SSO_REGISTER_SECRET')) {
                        $sso_client = $nv_Request->get_title('sso_client_' . $module_data, 'session', '');
                        $sso_redirect = $nv_Request->get_title('sso_redirect_' . $module_data, 'session', '');
                        $sso_redirect = NukeViet\Client\Sso::decrypt($sso_redirect);

                        if (!empty($sso_redirect) and !empty($sso_client) and str_starts_with($sso_redirect, $sso_client)) {
                            $redirect = $sso_redirect;
                        }

                        $nv_Request->unset_request('sso_client_' . $module_data, 'session');
                        $nv_Request->unset_request('sso_redirect_' . $module_data, 'session');
                    }

                    nv_jsonOutput([
                        'redirect' => $redirect,
                        'status' => 'success',
                        'mess' => $nv_Lang->getModule('login_ok')
                    ]);
                }

                // Trả về khi đăng nhập OpenID, Oauth, GSI chưa liên kết trước đó
                opidr_login([
                    'status' => 'success',
                    'mess' => $nv_Lang->getModule('login_ok')
                ]);
            } else {
                $error = '';
            }
        }
    } else {
        $error = $nv_Lang->getGlobal('loginincorrect');
    }
} catch (Jasny\SSO\Exception $e) {
    $error = $e->getMessage();
}

if (!$set_only) {
    // Lỗi khi đăng nhập thông thường từ passkey
    if ($signin_result) {
        signin_result([
            'status' => 'error',
            'mess' => $error
        ]);
    }

    // Lỗi đăng nhập khác như Google Identity (GSI)
    if ($result_json) {
        nv_jsonOutput([
            'status' => 'error',
            'mess' => $error
        ]);
    }

    // Lỗi khi đăng nhập openID, Oauth
    opidr_login([
        'status' => 'error',
        'mess' => $error
    ]);
}
