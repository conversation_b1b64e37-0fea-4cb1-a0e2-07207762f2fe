<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */
if (!defined('NV_IS_FILE_ADMIN'))
    die('Stop!!!');
define('MONEY_UNIT', 'VND');
$page_title = $nv_Lang->getModule('add_transaction');
use NukeViet\InForm\InForm;
use NukeViet\Dauthau\LangMulti;

$row = array();
$error = array();
require NV_ROOTDIR . '/modules/points/points.class.php';
// Gọi và khởi tạo wallet Class
require_once NV_ROOTDIR . '/modules/wallet/wallet.class.php';
$wallet = new nukeviet_wallet();
$nv_points = new nukeviet_points();
// lấy cấu hình config
$get_config = $config = $db->query('SELECT * FROM ' . $db_config['prefix'] . "_" . $module_data . '_config')->fetchAll();
$arrConfig = [];
foreach ($get_config as $key => $value) {
    $arrConfig[$value['exchange_point']] = $value['exchange_money'];
}

if ($nv_Request->isset_request('action', 'post') == 'getMoneyUser') {
    $userID = $nv_Request->get_int('userID', 'POST', '');
}

// $get_moneyUser = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_wallet_transaction WHERE ' . );

if ($nv_Request->isset_request('submit', 'post')) {
    // Tài khoản tác động tới
    $row['account'] = $nv_Request->get_title('account', 'post', '');
    $row['money_transaction'] = $nv_Request->get_title('money_transaction', 'post', '');
    $row['money_transaction'] = intval(str_replace(',', '', $row['money_transaction']));
    $row['transaction_info'] = $nv_Request->get_title('transaction_info', 'post', '');
    $row['typeadd'] = $nv_Request->get_title('typeadd', 'post', '+');
    $row['expired'] = $nv_Request->get_int('expired', 'post', '0');
    $row['donate'] = $nv_Request->get_int('note_point', 'post', -1);
    $row['moneyConfig'] = 0;
    // Nếu chọn Giao dịch hộ khách hàng
    if ($row['donate'] == -1) {
        $row['moneyConfig'] = $nv_Request->get_int('moneyConfig', 'post', 0);
    }

    // Nếu Điều chỉnh số liệu lệch và money_transaction > 0 phải gán cái moneyConfig =0 để reset
    if ($row['money_transaction'] > 0 and $row['donate'] == 1) {
        $row['moneyConfig'] = 0;
    }

    // Xác định tài khoản tác động
    $sql = "SELECT userid, username, first_name, last_name, email FROM " . NV_USERS_GLOBALTABLE . " WHERE username=:username";
    $sth = $db->prepare($sql);
    $sth->bindParam(':username', $row['account'], PDO::PARAM_STR);
    $sth->execute();
    $account_info = $sth->fetch();

    if (empty($row['account'])) {
        $error[] = $nv_Lang->getModule('error_required_customer');
    } elseif (empty($account_info)) {
        $error[] = sprintf($nv_Lang->getModule('error_exists_customer'), $row['account']);
    } elseif (($row['money_transaction'] <= 0 && $row['donate'] == 1) || ($row['moneyConfig'] <= 0 && $row['donate'] == -1)) {
        $error[] = $nv_Lang->getModule('error_required_money_transaction');
    } elseif (empty($row['transaction_info'])) {
        $error[] = $nv_Lang->getModule('error_required_transaction_info');
    }

    // Kiểm tra nếu Giao dịch hộ khách hàng thì gán
    if ($row['money_transaction'] <= 0 && $row['donate'] == -1) {
        $row['money_transaction'] = $row['moneyConfig'];
    }

    $sub_status = $row['donate'] == 1 ? 1 : 0;

    //  Kiểm tra xem quản trị nạp tiền cho khách chọn option trừ tiền k?
    $check = false;
    if (isset($arrConfig[$row['moneyConfig']]) && $row['donate'] == -1) {
        $check = true;
    } else if (empty($arrConfig[$row['moneyConfig']]) && $row['donate'] == -1){
        $error[] = $nv_Lang->getModule('error_not_point');
    }

    if (!empty($account_info)) {
        $user_money = $wallet->my_money($account_info['userid']);
        if ($user_money['money_current'] < $arrConfig[$row['moneyConfig']]) {
            $error[] = sprintf($nv_Lang->getModule('error_not_money'), $account_info['username'], number_format($user_money['money_current'], 0, '', '.') . ' VNĐ');
        }
    }

    if ($row['money_transaction'] > ********) {
        $error[] = $nv_Lang->getModule('error_max_int');
    }
    if (empty($error)) {

            // nếu là giao dịch hộ kh thì trừ tiền trước, cộng điểm sau
            if ($check) {
                if ($row['typeadd'] == '+') {
                    // Cộng tiền
                    $tacdong = false;
                }

                if ($row['typeadd'] == '-') {
                    // Trừ tiền
                    $tacdong = true;
                }

                $message = get_both_lang('mess_transaction');
                $message['vi'] .=  ('-' . $admin_info['username']);
                $message['en'] .=  ('-' . $admin_info['username']);

                $tran_id = $wallet->update($arrConfig[$row['moneyConfig']], MONEY_UNIT, 0, $account_info['userid'], json_encode($message), $tacdong, $admin_info['userid']);
                if ($wallet->isError() == true) {
                    $error[] = $nv_Lang->getModule('payclass_error_save_transaction');
                }
            }

            if ($row['typeadd'] == '-') {
                $id_transaction_point = $nv_points->update(0, $row['money_transaction'], $account_info['userid'], $row['transaction_info'], false, $admin_info['userid'], 0, 0, $sub_status);
            } else {
                $row['expired'] = NV_CURRENTTIME + ($row['expired'] * 86400);
                $id_transaction_point = $nv_points->update(0, $row['money_transaction'], $account_info['userid'], $row['transaction_info'], true, $admin_info['userid'], 0, $row['expired'], $sub_status);
            }

            if ($id_transaction_point > 0 and $nv_points->isError() == false) {
                $arrMess = [
                    'vi' => sprintf(LangMulti::get('vi', 'point_interaction'), $row['typeadd'],number_format($row['money_transaction'], 0, ',', '.')),
                    'en' => sprintf(LangMulti::get('en', 'point_interaction'), $row['typeadd'],number_format($row['money_transaction'], 0)),
                ];
                $res = InForm::insertInfrom($account_info['userid'], $arrMess, 'points/#log/');
                if (!$res) {
                    $error[] = $nv_Lang->getModule('error_notification_point');
                } else {
                    if ($check and !empty($tran_id)) {
                        // update lại trans wallet để thống kê doanh số cho sale
                        $wallet->update_transaction($tran_id, $id_transaction_point, 1, $admin_info['userid']);
                    }
                    nv_redirect_location(NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=transaction&amp;userid=" . $account_info['userid']);
                }
            }
    }
}

// LẤY IDUSSER
$username = $nv_Request->get_title('username', 'get', '');

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('CONFIG_POINT', json_encode($arrConfig));
$xtpl->assign('MONEY_UNIT', MONEY_UNIT);
$xtpl->assign('ROW', $row);
$xtpl->assign('CONFIG', $module_config[$module_name]);

if (!empty($username)) {
    $get_id_user = $db->query('SELECT username, userid FROM ' . NV_USERS_GLOBALTABLE . ' WHERE username = ' . $db->quote($username))->fetch();
    if (!empty($get_id_user)) {
        $xtpl->assign('USERNAME', trim($username));
        $xtpl->parse('main.show_username');
    }
}

if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

foreach ($arrConfig as $key => $value) {
    $row['key'] = $key;
    $row['value'] = number_format($value, 0, ',', '.');
    $row['keyFomat'] = number_format($key, 0, ',', '.');
    $row['valueDefault'] = $value;
    $xtpl->assign('ROW', $row);
    $xtpl->parse('main.loop');
    $xtpl->parse('main.config_point');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
