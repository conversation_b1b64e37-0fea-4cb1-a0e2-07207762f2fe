<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<link rel="stylesheet" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/clipboard/clipboard.min.js"></script>
<link type="text/css" href="{NV_STATIC_URL}themes/default/images/{MODULE_FILE}/bootstrap-datepicker/css/bootstrap-datepicker.min.css" rel="stylesheet" />
<link type="text/css" href="{NV_STATIC_URL}themes/default/images/{MODULE_FILE}/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" />
<script type="text/javascript" src="{NV_STATIC_URL}themes/default/images/{MODULE_FILE}/bootstrap-datepicker/js/bootstrap-datepicker.min.js"></script>
<script type="text/javascript" src="{NV_STATIC_URL}themes/default/images/{MODULE_FILE}/bootstrap-datepicker/locales/bootstrap-datepicker.{NV_LANG_INTERFACE}.min.js"></script>
<!-- BEGIN: error -->
<div class="alert alert-danger">{ERROR}</div>
<!-- END: error -->
<form method="get" action="{NV_BASE_ADMINURL}index.php">
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}">
    <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
    <div class="row">
        <div class="col-md-4">
            <div class="form-group">
                <label for="element_q">{LANG.search_keywords}:</label>
                <input type="text" class="form-control" id="element_q" name="q" value="{SEARCH.q}" placeholder="{LANG.enter_search_key}">
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label>{LANG.name_calendar}:</label>
                <select class="form-control" name="c">
                    <option value="0">{LANG.all}</option>
                    <!-- BEGIN: calendar -->
                    <option value="{CALENDAR.id}"{CALENDAR.selected}>{CALENDAR.name_calendar}</option>
                    <!-- END: calendar -->
                </select>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label>{LANG.type_calendar}:</label>
                <select class="form-control" name="type">
                    <option value="-1">{LANG.all}</option>
                    <option value="1"{SEARCH.type_1}>{LANG.event_lunar}</option>
                    <option value="0"{SEARCH.type_0}>{LANG.event_solar}</option>
                </select>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label for="element_from">{LANG.main_search_from}:</label>
                <input type="text" class="form-control datepicker" id="element_from" name="f" value="{SEARCH.from}" placeholder="dd/mm/yyyy" autocomplete="off">
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label for="element_to">{LANG.main_search_to}:</label>
                <input type="text" class="form-control datepicker" id="element_to" name="t" value="{SEARCH.to}" placeholder="dd/mm/yyyy" autocomplete="off">
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                <label class="visible-sm-block visible-md-block visible-lg-block">&nbsp;</label>
                <button class="btn btn-primary" type="submit"><i class="fa fa-search" aria-hidden="true"></i> {GLANG.search}</button>
                <a href="{URL_IMPORT_ICS}" class="btn btn-default"><i class="fa fa-upload"></i> {LANG.import_ical}</a>
            </div>
        </div>
    </div>
</form>
<script type="text/javascript">
    $(document).ready(function() {
        $('.datepicker').datepicker({
            language: '{NV_LANG_INTERFACE}',
            format: 'dd-mm-yyyy',
            weekStart: 1,
            todayBtn: 'linked',
            autoclose: true,
            todayHighlight: true,
            zIndexOffset: 1000
        });
    });
</script>
<form id="form_event">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <caption><em class="fa fa-file-text-o"></em> {LANG.all}: <span>{TOTAL}</span></caption>
            <thead>
            <tr>
                <th style="width: 1%" class="text-center">
                    <input name="check_all[]" type="checkbox" value="yes" onclick="nv_checkAll(this.form, 'idcheck[]', 'check_all[]',this.checked);">
                </th>
                <th style="width: 20%" class="text-nowrap">
                    <a href="{URL_ORDER_TITLE}">{ICON_ORDER_TITLE} {LANG.name_event}</a>
                </th>
                <th style="width: 20%" class="text-nowrap">
                    {LANG.name_calendar}
                </th>
                <th class="text-nowrap" style="width: 15%;">
                    <a href="{URL_ORDER_DATE_EVENT}">{ICON_ORDER_DATE} {LANG.date_event}</a>      
                </th>
                <th style="width: 15%" class="text-nowrap">
                    <a href="{URL_ORDER_ADD_TIME}">{ICON_ORDER_ADD_TIME} {LANG.addtime}</a>
                </th>
                <th style="width: 15%" class="text-nowrap">
                    <a href="{URL_ORDER_EDIT_TIME}">{ICON_ORDER_EDIT_TIME} {LANG.edittime}</a>
                </th>
                <th style="width: 15%" class="text-nowrap text-center">{LANG.status}</th>
                <th style="width: 14%" class="text-nowrap text-center">{LANG.function}</th>
            </tr>
            </thead>
            <tbody>
            <!-- BEGIN: loop -->
            <tr>
                <td class="text-center">
                    <input type="checkbox" onclick="nv_UncheckAll(this.form, 'idcheck[]', 'check_all[]', this.checked);" value="{ROW.id}" name="idcheck[]">
                </td>
                <td>
                    <div class="mb-1">
                        <span class="label label-primary">{ROW.event_day}</span>
                        <span class="label label-info">{ROW.cat_name}</span>
                    </div>
                    <a href="#md-content-{ROW.id}" data-toggle="modal">{ROW.title}</a>
                </td>
                <td class="text-nowrap">{ROW.name_calendar}</td>
                <td class="text-nowrap">{ROW.date_event}</td>
                <td class="text-nowrap">{ROW.add_time}</td>
                <td class="text-nowrap">{ROW.edit_time}</td>
                <td class="text-center">
                    <input name="status" id="change_status{ROW.id}" value="1" type="checkbox"{ROW.status_render} onclick="nv_change_event_status('{ROW.id}', '{NV_CHECK_SESSION}');">
                </td>
                <td class="text-center text-nowrap">
                    <a href="{ROW.url_edit}" class="btn btn-xs btn-default"><i class="fa fa-edit"></i> {GLANG.edit}</a>
                    <a href="javascript:void(0);" onclick="nv_delele_event('{ROW.id}', '{NV_CHECK_SESSION}');" class="btn btn-xs btn-danger"><i class="fa fa-trash"></i> {GLANG.delete}</a>
                </td>
            </tr>
            <!-- END: loop -->
            </tbody>
            <!-- BEGIN: generate_page -->
            <tfoot>
            <tr>
                <td colspan="6">
                    {GENERATE_PAGE}
                </td>
            </tr>
            </tfoot>
            <!-- END: generate_page -->
        </table>
    </div>
    <div class="form-group form-inline">
        <div class="form-group">
            <select class="form-control" id="action-of-content" name="delete_all">
                <option value="{NV_CHECK_SESSION}">{GLANG.delete}</option>
            </select>
        </div>
        <button type="button" class="btn btn-primary" id="btn_delete_all" onclick="nv_delete_all_event('{ROW.id}', '{NV_CHECK_SESSION}')">{GLANG.submit}</button>
    </div>
</form>
<script>
    $(document).ready(function() {
        $('select').select2();
    });
</script>
<!-- END: main -->
