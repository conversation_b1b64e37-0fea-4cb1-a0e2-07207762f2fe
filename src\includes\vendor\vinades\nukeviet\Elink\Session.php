<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Elink;

/**
 * NukeViet\Elink\Session
 *
 * @package NukeViet\Elink
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @version 4.5.00
 * @since 4.3.08
 * @access public
 */
class Session
{
    public static function checkSessionReferralTarget($session_static_rt)
    {
        if (!is_array($session_static_rt)) {
            return [];
        }

        $array_isset_keys = ['userid', 'referer', 'selfurl', 'stat_id'];
        $array_empty_keys = ['accesstime', 'ip', 'user_agent', 'checksum'];

        foreach ($array_isset_keys as $key) {
            if (!isset($session_static_rt[$key])) {
                return [];
            }
        }
        foreach ($array_empty_keys as $key) {
            if (empty($session_static_rt[$key])) {
                return [];
            }
        }

        // Tính và so sánh checksum
        $checksum = [];
        if (!empty($session_static_rt['stat_id'])) {
            // Có ID row đã lưu vào CSDL thì tính checksum cho cả nó
            $checksum[] = $session_static_rt['stat_id'];
        }
        $checksum[] = $session_static_rt['referer'];
        $checksum[] = $session_static_rt['selfurl'];
        $checksum[] = $session_static_rt['accesstime'];
        $checksum[] = $session_static_rt['ip'];
        $checksum = md5(implode('|', $checksum));

        if ($checksum != $session_static_rt['checksum']) {
            return [];
        }

        return $session_static_rt;
    }

    /*@return array[
     $session_static_rt['userid'] = $stat_userid;
     $session_static_rt['referer'] = $client_info['referer'];
     $session_static_rt['selfurl'] = $client_info['selfurl'];
     $session_static_rt['accesstime'] = NV_CURRENTTIME;
     $session_static_rt['ip'] = NV_CLIENT_IP;
     $session_static_rt['user_agent'] = NV_USER_AGENT;
     ]*/
    public static function getReferralTarget()
    {
        global $nv_Request, $db_config, $db, $client_info, $user_info;

        $session_static_rt = $nv_Request->get_string('statistic_all_rt', 'session', '');
        $session_static_rt = empty($session_static_rt) ? [] : (json_decode($session_static_rt, true) ?: []);

        // Kiểm tra session hợp lệ
        if (!self::checkSessionReferralTarget($session_static_rt)) {
            /*
            $static_rt = [];
            $static_rt['userid'] = defined('NV_IS_USER') ? $user_info['userid'] : 0;
            $static_rt['referer'] = $client_info['referer'] ?? '';
            $static_rt['selfurl'] = $client_info['selfurl'] ?? '';
            $static_rt['accesstime'] = NV_CURRENTTIME;
            $static_rt['ip'] = NV_CLIENT_IP;
            $static_rt['user_agent'] = NV_USER_AGENT;
            return $static_rt;
            */
            return [];
        }

        $session_static_rt['site_id'] = $nv_Request->get_absint('site_id', 'session', 0);

        return $session_static_rt;
    }
}
