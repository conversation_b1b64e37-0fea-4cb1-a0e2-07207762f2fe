<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;
$page_title = $nv_Lang->getModule('main');
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=';

if (isset($array_groups_users[$admin_info['userid']])) {
    $admin_config = $array_groups_users[$admin_info['userid']]['config'];
}

$_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_crmbidding_groups_users ORDER BY weight ASC';
$_query = $db->query($_sql);
$array_user_set_order = $array_user_share_messenger_zalo = $array_sale_view_zalo = [];
while ($_row = $_query->fetch()) {
    $_row['config'] = json_decode($_row['config'], true);
    if (!empty($array_user_id_users[$_row['userid']])) {
        $_row['username'] = $array_user_id_users[$_row['userid']]['username'];
    }

    if (isset($_row['config']['set_order']) and $_row['config']['set_order'] == 1) {
        $array_user_set_order[$_row['userid']] = $_row;
    }
    // danh sách dc chia lead zalo, mess
    if (isset($_row['config']['share_messenger_zalo']) and $_row['config']['share_messenger_zalo'] == 1) {
        $array_user_share_messenger_zalo[$_row['userid']] = $_row;
    }
}

// danh sách đc trực kênh zalo
foreach ($arr_group_user2 as $gr_user) {
    foreach ($gr_user as $_user) {
        if (isset($_user['config']['sale_view_zalo']) and $_user['config']['sale_view_zalo'] == 1) {
            $array_sale_view_zalo[$_user['userid']] = $_user['userid'];
        }
    }
}
$array_sale_view_zalo_tmp = $array_sale_view_zalo;

$sto = NV_CURRENTTIME;
$sfrom = NV_CURRENTTIME - (86400 * 6 * 30);

if ($nv_Request->isset_request('viewhistory', 'post,get')) {
    $typeviewhistory = $nv_Request->get_int('typeviewhistory', 'post,get', 0);
    $arr_log = $arr_adminid = $arr_leads_id = $arr_oppotunities_id = $params = $order = $where_api = [];
    if ($typeviewhistory == 1) {
        $where_api['AND'][] = [
            '>' => [
                'leads_id' => 0
            ]
        ];
        $where_api['AND'][] = [
            '=' => [
                'userid' => $admin_info['userid']
            ]
        ];
        $where_api['AND'][] = [
            '=' => [
                'log_key' => 'LOG_ADMIN_UPDATE_LEADS_INFO'
            ]
        ];
        $where_api['AND'][] = [
            '>=' => [
                'log_time' => $sfrom
            ]
        ];
        $where_api['AND'][] = [
            '<=' => [
                'log_time' => $sto
            ]
        ];
        $params = [
            'page' => 1,
            'perpage' => 20,
            'get_total' => false
        ];
        $order['log_time'] = "DESC";
        $params['where'] = $where_api;
        $params['order'] = $order;
        $data_log = nv_local_api('ListAllLog', $params, $admin_info['username'], 'crmbidding');
        $data_log = json_decode($data_log, true);
        // END API
        if (!empty($data_log['data'])) {
            foreach ($data_log['data'] as $row) {
                $row['log_data'] = json_decode($row['log_data'], true);
                foreach ($row['log_data'] as $key => $value) {
                    if (is_array($value)) {
                        $row['log_data'][$key] = implode(' ', $value);
                    }
                }
                $row['log_data'] = implode(', ', $row['log_data']);
                if ($row['leads_id'] > 0) {
                    $row['type'] = 1;
                    $arr_leads_id[$row['leads_id']] = $row['leads_id'];
                }
                $arr_log[$row['log_time']] = $row;
                $arr_adminid[$row['userid']] = $row['userid'];
            }
        }
    } else if ($typeviewhistory == 2) {
        $where_api['AND'][] = [
            '>' => [
                'oppotunities_id' => 0
            ]
        ];
        $where_api['AND'][] = [
            '=' => [
                'userid' => $admin_info['userid']
            ]
        ];
        $where_api['AND'][] = [
            '=' => [
                'log_key' => 'LOG_ADMIN_UPDATE_LEADS_INFO'
            ]
        ];
        $where_api['AND'][] = [
            '>=' => [
                'log_time' => $sfrom
            ]
        ];
        $where_api['AND'][] = [
            '<=' => [
                'log_time' => $sto
            ]
        ];
        $params = [
            'page' => 1,
            'perpage' => 10,
            'get_total' => false
        ];
        $order['log_time'] = "DESC";
        $params['where'] = $where_api;
        $params['order'] = $order;
        $data_log = nv_local_api('ListAllLog', $params, $admin_info['username'], 'crmbidding');
        $data_log = json_decode($data_log, true);

        if (!empty($data_log['data'])) {
            foreach ($data_log['data'] as $row) {
                $row['log_data'] = json_decode($row['log_data'], true);
                foreach ($row['log_data'] as $key => $value) {
                    if (is_array($value)) {
                        $row['log_data'][$key] = implode(' ', $value);
                    }
                }
                $row['log_data'] = implode(', ', $row['log_data']);
                if ($row['oppotunities_id'] > 0) {
                    $row['type'] = 2;
                    $arr_oppotunities_id[$row['oppotunities_id']] = $row['oppotunities_id'];
                }
                $arr_log[$row['log_time']] = $row;
                $arr_adminid[$row['userid']] = $row['userid'];
            }
        }
    } else if ($typeviewhistory == 3) {
        $where_api['AND'][] = [
            '>' => [
                'order_id' => 0
            ]
        ];
        $where_api['AND'][] = [
            '=' => [
                'userid' => $admin_info['userid']
            ]
        ];
        $params = [
            'page' => 1,
            'perpage' => 10,
            'get_total' => false
        ];
        $order['log_time'] = "DESC";
        $params['where'] = $where_api;
        $params['order'] = $order;
        $data_log = nv_local_api('ListAllLog', $params, $admin_info['username'], 'bidding');
        $data_log = json_decode($data_log, true);

        $arr_orderid = $params = $order = $where_api = [];
        if (!empty($data_log['data'])) {
            foreach ($data_log['data'] as $row) {
                $row['log_data'] = json_decode($row['log_data'], true);
                foreach ($row['log_data'] as $key => $value) {
                    if (is_array($value)) {
                        $row['log_data'][$key] = implode(' ', $value);
                    }
                }
                $row['log_data'] = implode(', ', $row['log_data']);
                if ($row['order_id'] > 0) {
                    $row['type'] = 3;
                    $arr_orderid[$row['order_id']] = $row['order_id'];
                }

                $arr_log[$row['log_time']] = $row;
                $arr_adminid[$row['userid']] = $row['userid'];
            }
        }
    } else if ($typeviewhistory == 4) {
        $where_api['AND'][] = [
            '>' => [
                'uvip_id' => 0
            ]
        ];
        $where_api['AND'][] = [
            '=' => [
                'userid' => $admin_info['userid']
            ]
        ];
        $params = [
            'page' => 1,
            'perpage' => 10,
            'get_total' => false
        ];
        $order['log_time'] = "DESC";
        $params['where'] = $where_api;
        $params['order'] = $order;
        $data_log = nv_local_api('ListAllLog', $params, $admin_info['username'], 'bidding');
        $data_log = json_decode($data_log, true);

        $arr_orderid = $params = $order = $where_api = [];
        if (!empty($data_log['data'])) {
            foreach ($data_log['data'] as $row) {
                $row['log_data'] = json_decode($row['log_data'], true);
                foreach ($row['log_data'] as $key => $value) {
                    if (is_array($value)) {
                        $row['log_data'][$key] = implode(' ', $value);
                    }
                }
                $row['log_data'] = implode(', ', $row['log_data']);
                if ($row['uvip_id'] > 0 and $row['vip_id'] > 0) {
                    $row['type'] = 4;
                    // $result_customs = $db->query("SELECT * FROM " . NV_PREFIXLANG . "_bidding_customs WHERE user_id = " . $row['uvip_id'] . " AND vip = " . $row['vip_id'])->fetch();
                    $arr_where = $infoAPI = [];
                    $arr_where['AND'][] = [
                        '=' => [
                            'user_id' => $row['uvip_id']
                        ]
                    ];

                    $arr_where['AND'][] = [
                        '=' => [
                            'vip' => $row['vip_id']
                        ]
                    ];

                    $params_customs = [
                        'where' => $arr_where,
                        'page' => 1,
                        'per_page' => 1
                    ];
                    $api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
                    $api->setModule('bidding')
                        ->setLang('vi')
                        ->setAction('ListBiddingCustoms')
                        ->setData($params_customs);
                    $result_customs = $api->execute();
                    $error = $api->getError();
                    if (!empty($result_customs['data'])) {
                        $row['customs'] = array_values($result_customs['data'])[0];
                    }
                }

                $arr_log[$row['log_time']] = $row;
                $arr_adminid[$row['userid']] = $row['userid'];
            }
        }
    } else {
        $where_api['AND'][] = [
            '=' => [
                'log_key' => 'LOG_ADMIN_UPDATE_LEADS_INFO'
            ]
        ];
        $where_api['AND'][] = [
            '=' => [
                'userid' => $admin_info['userid']
            ]
        ];
        $where_api['AND'][] = [
            '>=' => [
                'log_time' => $sfrom
            ]
        ];
        $where_api['AND'][] = [
            '<=' => [
                'log_time' => $sto
            ]
        ];
        $params = [
            'page' => 1,
            'perpage' => 10,
            'get_total' => false
        ];
        $order['log_time'] = "DESC";
        $params['where'] = $where_api;
        $params['order'] = $order;
        $data_log = nv_local_api('ListAllLog', $params, $admin_info['username'], 'crmbidding');
        $data_log = json_decode($data_log, true);

        if (!empty($data_log['data'])) {
            foreach ($data_log['data'] as $row) {
                $row['log_data'] = json_decode($row['log_data'], true);
                foreach ($row['log_data'] as $key => $value) {
                    if (is_array($value)) {
                        $row['log_data'][$key] = implode(' ', $value);
                    }
                }
                $row['log_data'] = implode(', ', $row['log_data']);
                if ($row['leads_id'] > 0) {
                    $row['type'] = 1;
                    $arr_leads_id[$row['leads_id']] = $row['leads_id'];
                } else if ($row['oppotunities_id'] > 0) {
                    $row['type'] = 2;
                    $arr_oppotunities_id[$row['oppotunities_id']] = $row['oppotunities_id'];
                }
                $arr_log[$row['log_time']] = $row;
                $arr_adminid[$row['userid']] = $row['userid'];
            }
        }

        $params = [
            'userid' => $admin_info['userid'],
            'page' => 1,
            'perpage' => 10,
            'get_total' => false
        ];
        $order['log_time'] = "DESC";
        $params['order'] = $order;
        $data_log = nv_local_api('ListAllLog', $params, $admin_info['username'], 'bidding');
        $data_log = json_decode($data_log, true);
        $arr_orderid = $params = $order = $where_api = [];
        if (!empty($data_log['data'])) {
            foreach ($data_log['data'] as $row) {
                $row['log_data'] = json_decode($row['log_data'], true);
                foreach ($row['log_data'] as $key => $value) {
                    if (is_array($value)) {
                        $row['log_data'][$key] = implode(' ', $value);
                    }
                }
                $row['log_data'] = implode(', ', $row['log_data']);
                if ($row['order_id'] > 0) {
                    $row['type'] = 3;
                    $arr_orderid[$row['order_id']] = $row['order_id'];
                } else if ($row['uvip_id'] > 0 and $row['vip_id'] > 0) {
                    $row['type'] = 4;
                    // $result_customs = $db->query("SELECT * FROM " . NV_PREFIXLANG . "_bidding_customs WHERE user_id = " . $row['uvip_id'] . " AND vip = " . $row['vip_id'])->fetch();
                    // if (!empty($result_customs)) {
                    // $row['customs'] = $result_customs;
                    // }
                    $arr_where = $infoAPI = [];
                    $arr_where['AND'][] = [
                        '=' => [
                            'user_id' => $row['uvip_id']
                        ]
                    ];

                    $arr_where['AND'][] = [
                        '=' => [
                            'vip' => $row['vip_id']
                        ]
                    ];

                    $params_customs = [
                        'where' => $arr_where,
                        'page' => 1,
                        'per_page' => 1
                    ];
                    $api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
                    $api->setModule('bidding')
                        ->setLang('vi')
                        ->setAction('ListBiddingCustoms')
                        ->setData($params_customs);
                    $result_customs = $api->execute();
                    $error = $api->getError();
                    if (!empty($result_customs['data'])) {
                        $row['customs'] = array_values($result_customs['data'])[0];
                    }
                }

                $arr_log[$row['log_time']] = $row;
                $arr_adminid[$row['userid']] = $row['userid'];
            }
        }
    }

    if (!empty($arr_leads_id)) {
        $where_api['AND'][] = [
            'IN' => [
                'id' => '(' . implode(',', $arr_leads_id) . ')'
            ]
        ];
        $where_api['AND'][] = [
            '>=' => [
                'log_time' => $sfrom
            ]
        ];
        $where_api['AND'][] = [
            '<=' => [
                'log_time' => $sto
            ]
        ];
        $params = [
            'userid' => $admin_info['userid'],
            'page' => 1,
            'perpage' => 50
        ];
        $params['where'] = $where_api;
        $data_leads = nv_local_api('ListAllLeads', $params, $admin_info['username'], 'crmbidding');
        $data_leads = json_decode($data_leads, true);
        $arr_leads = $where_api = $params = [];
        if (!empty($data_leads['data'])) {
            foreach ($data_leads['data'] as $key => $value) {
                $arr_leads[$value['id']] = $value;
            }
        }
    }

    if (!empty($arr_oppotunities_id)) {
        $where_api['AND'][] = [
            'IN' => [
                'id' => '(' . implode(',', $arr_oppotunities_id) . ')'
            ]
        ];
        $where_api['AND'][] = [
            '>=' => [
                'log_time' => $sfrom
            ]
        ];
        $where_api['AND'][] = [
            '<=' => [
                'log_time' => $sto
            ]
        ];
        $params = [
            'userid' => $admin_info['userid'],
            'page' => 1,
            'perpage' => 50
        ];
        $params['where'] = $where_api;
        $data_opportunities = nv_local_api('ListAllOpportunities', $params, $admin_info['username'], 'crmbidding');
        $data_opportunities = json_decode($data_opportunities, true);
        $arr_oppotunities = $where_api = $params = [];
        if (!empty($data_opportunities['data'])) {
            foreach ($data_opportunities['data'] as $key => $value) {
                $arr_oppotunities[$value['id']] = $value;
            }
        }
    }
    if (!empty($arr_adminid)) {
        $sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . " WHERE userid IN (" . implode(',', $arr_adminid) . ")";
        $result = $db->query($sql);
        $arr_admin = [];
        while ($row = $result->fetch()) {
            $row['full_name'] = nv_show_name_user($row['first_name'], $row['last_name'], $row['username']);
            $arr_admin[$row['userid']] = $row;
        }
    }
    if (!empty($arr_orderid)) {
        $arr_order = [];
        $params_customs = [
            'arr_orderid' => $arr_orderid
        ];
        $api = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
        $api->setModule('bidding')
            ->setLang('vi')
            ->setAction('GetBiddingOrderCustomsLog')
            ->setData($params_customs);
        $result = $api->execute();
        $error = $api->getError();
        if (!empty($result['data'])) {
            $arr_order = $result['data'];
        }
    }

    $xtpl = new XTemplate('main.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
    $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
    $base_url_bidding = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=';

    if (!empty($arr_log)) {
        krsort($arr_log);
        foreach ($arr_log as $log) {
            if ($log['type'] == 1) {
                $log['icon'] = '<i class="fa fa-cube">';
                $log['content'] = sprintf($nv_Lang->getModule('content_log'), $base_url . 'leads_info&amp;id=' . $log['leads_id'], (!empty($arr_admin[$log['userid']]) ? $arr_admin[$log['userid']]['full_name'] : 'Hệ thống'), 'lead ' . $arr_leads[$log['leads_id']]['name'], defined('NV_IS_SPADMIN') ? $log['log_data'] : '');
            } else if ($log['type'] == 2) {
                $log['icon'] = '<i class="fa fa-cubes">';
                $log['content'] = sprintf($nv_Lang->getModule('content_log'), $base_url . 'opportunities_info&amp;id=' . $log['oppotunities_id'], (!empty($arr_admin[$log['userid']]) ? $arr_admin[$log['userid']]['full_name'] : 'Hệ thống'), 'cơ hội ' . $arr_oppotunities[$log['oppotunities_id']]['name'], defined('NV_IS_SPADMIN') ? $log['log_data'] : '');
            } else if ($log['type'] == 3) {
                $log['icon'] = '<i class="fa fa-shopping-cart"></i>';
                $log['content'] = sprintf($nv_Lang->getModule('content_log'), $base_url_bidding . 'payment&amp;vieworderid=' . $log['order_id'], (!empty($arr_admin[$log['userid']]) ? $arr_admin[$log['userid']]['full_name'] : 'Hệ thống'), 'đơn hàng ' . $arr_order[$log['order_id']]['name'], defined('NV_IS_SPADMIN') ? $log['log_data'] : '');
            } else if ($log['type'] == 4) {
                $log['icon'] = '<i class="fa fa-user"></i>';
                $log['content'] = sprintf($nv_Lang->getModule('content_log'), $base_url_bidding . 'cus_info&amp;id=' . $log['uvip_id'] . '-' . $log['vip_id'], (!empty($arr_admin[$log['userid']]) ? $arr_admin[$log['userid']]['full_name'] : 'Hệ thống'), 'khách vip ' . $log['customs']['name'], defined('NV_IS_SPADMIN') ? $log['log_data'] : '');
            }

            $log['log_time'] = get_time_ago($log['log_time']);
            $xtpl->assign('LOG', $log);
            $xtpl->parse('viewhistory.loop');
        }
    }

    $xtpl->parse('viewhistory');
    $contents = $xtpl->text('viewhistory');

    include NV_ROOTDIR . '/includes/header.php';
    echo $contents;
    include NV_ROOTDIR . '/includes/footer.php';
}

$where_api['AND'][] = [
    '=' => [
        'active' => 1
    ]
];
$where_api['AND'][] = [
    '=' => [
        'caregiver_id' => $admin_info['userid']
    ]
];
$params = [
    'page' => 1,
    'perpage' => 10
];
$order['last_comment'] = 'DESC';
$params['where'] = $where_api;
$params['order'] = $order;
$data_leads_no_care = nv_local_api('ListAllLeads', $params, $admin_info['username'], 'crmbidding');
$data_leads_no_care = json_decode($data_leads_no_care, true);

$data_opportunities = nv_local_api('ListAllOpportunities', $params, $admin_info['username'], 'crmbidding');
$data_opportunities = json_decode($data_opportunities, true);
$arr_opportunities = [];
if (!empty($data_opportunities['data'])) {
    foreach ($data_opportunities['data'] as $key => $value) {
        $arr_opportunities[$value['id']] = $value;
    }
}

$where_api = $params = $order = [];
$params = [
    'page' => 1,
    'perpage' => 10,
    'use_elastic' => 1
];
$params['group'] = 'status';
$where_api['AND'][] = [
    '=' => [
        'caregiver_id' => $admin_info['userid']
    ]
];
$params['where'] = $where_api;

$data_leads = nv_local_api('ListAllLeads', $params, $admin_info['username'], 'crmbidding');
$data_leads = json_decode($data_leads, true);
$where_api = $params = [];

$arr_label = $array_status;
unset($arr_label[0]);
$arr_value = $_arr_value = [];
if ($module_config[$module_name]['elas_use']) {
    if (!empty($data_leads['data'])) {
        foreach ($data_leads['data'] as $key => $_row) {
            if ($_row['status'] != 0) {
                $_arr_value[$_row['status']][$_row['id']] = $_row['id'];
            }
        }
    }
    if (!empty($_arr_value)) {
        foreach ($_arr_value as $key => $value) {
            $arr_value[$key] = sizeof($value);
        }
    }
} else {
    if (!empty($data_leads['data'])) {
        foreach ($data_leads['data'] as $key => $_row) {
            if ($_row['status'] != 0) {
                $arr_value[$_row['status']] = $_row['num'];
            }
        }
    }
}

$params = [
    'page' => 1,
    'perpage' => 10,
    'use_elastic' => 0
];
$params['group'] = 'status';

$where_api['AND'][] = [
    '=' => [
        'caregiver_id' => $admin_info['userid']
    ]
];
$params['where'] = $where_api;

$data_opportunities = nv_local_api('ListAllOpportunities', $params, $admin_info['username'], 'crmbidding');
$data_opportunities = json_decode($data_opportunities, true);
$where_api = $params = [];
$arr_label_opportunities = $array_status_opportunities;
$arr_value_opportunities = $_arr_value_opportunities = [];
if ($module_config[$module_name]['elas_use']) {
    if (!empty($data_opportunities['data'])) {
        foreach ($data_opportunities['data'] as $key => $_row) {
            if ($_row['status'] != 0) {
                $_arr_value_opportunities[$_row['status']][$_row['id']] = $_row['id'];
            }
        }
    }
    if (!empty($_arr_value_opportunities)) {
        foreach ($_arr_value_opportunities as $key => $value) {
            $arr_value_opportunities[$key] = sizeof($value);
        }
    }
} else {
    if (!empty($data_opportunities['data'])) {
        foreach ($data_opportunities['data'] as $key => $_row) {
            if ($_row['status'] != 0) {
                $arr_value_opportunities[$_row['status']] = $_row['num'];
            }
        }
    }
}

// thống kê tổng:
$arr_static = [];
// tổng doanh thu
$sql = 'SELECT SUM(money) as total_money, SUM(discount) as discount, SUM(total) as total, SUM(price_reduce) as price_reduce, SUM(total_end) as total_end FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE admin_id = ' . $admin_info['userid'];
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $static_from = mktime(0, 0, 0, 01, 01, 2018);
    $static_to = mktime(23, 59, 59, 12, 31, nv_date('Y', NV_CURRENTTIME));
    $arr_static['all']['link'] = $base_url . 'static&time_from=' . nv_date('d/m/Y', $static_from) . '&time_to=' . nv_date('d/m/Y', $static_to);
    $arr_static['all']['total_money'] = number_format(!empty($row['total_money']) ? $row['total_money'] : 0);
    $arr_static['all']['total_end'] = number_format(!empty($row['total_end']) ? $row['total_end'] : 0);
}

// năm
$static_from = mktime(0, 0, 0, 01, 01, nv_date('Y', NV_CURRENTTIME));
$static_to = mktime(23, 59, 59, 12, 31, nv_date('Y', NV_CURRENTTIME));
$sql = 'SELECT SUM(money) as total_money, SUM(discount) as discount, SUM(total) as total, SUM(price_reduce) as price_reduce, SUM(total_end) as total_end FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE static_time >= ' . $static_from . ' AND static_time <=' . $static_to . ' AND admin_id = ' . $admin_info['userid'];
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_static['year']['link'] = $base_url . 'static&time_from=' . nv_date('d/m/Y', $static_from) . '&time_to=' . nv_date('d/m/Y', $static_to);
    $arr_static['year']['total_money'] = number_format(!empty($row['total_money']) ? $row['total_money'] : 0);
    $arr_static['year']['total_end'] = number_format(!empty($row['total_end']) ? $row['total_end'] : 0);
}

// quý
$month = intval(nv_date('n', NV_CURRENTTIME));

if ($month == 1 or $month == 2 or $month == 3) {
    $static_from = mktime(0, 0, 0, 01, 01, nv_date('Y', NV_CURRENTTIME));
    $static_to = mktime(23, 59, 59, 03, 31, nv_date('Y', NV_CURRENTTIME));
} else if ($month == 4 or $month == 5 or $month == 6) {
    $static_from = mktime(0, 0, 0, 04, 01, nv_date('Y', NV_CURRENTTIME));
    $static_to = mktime(23, 59, 59, 06, 30, nv_date('Y', NV_CURRENTTIME));
} else if ($month == 7 or $month == 8 or $month == 9) {
    $static_from = mktime(0, 0, 0, 7, 01, nv_date('Y', NV_CURRENTTIME));
    $static_to = mktime(23, 59, 59, 9, 30, nv_date('Y', NV_CURRENTTIME));
} else {
    $static_from = mktime(0, 0, 0, 10, 01, nv_date('Y', NV_CURRENTTIME));
    $static_to = mktime(23, 59, 59, 12, 31, nv_date('Y', NV_CURRENTTIME));
}

$sql = 'SELECT SUM(money) as total_money, SUM(discount) as discount, SUM(total) as total, SUM(price_reduce) as price_reduce, SUM(total_end) as total_end FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE static_time >= ' . $static_from . ' AND static_time <=' . $static_to . ' AND admin_id = ' . $admin_info['userid'];

$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_static['quy']['link'] = $base_url . 'static&time_from=' . nv_date('d/m/Y', $static_from) . '&time_to=' . nv_date('d/m/Y', $static_to);
    $arr_static['quy']['total_money'] = number_format(!empty($row['total_money']) ? $row['total_money'] : 0);
    $arr_static['quy']['total_end'] = number_format(!empty($row['total_end']) ? $row['total_end'] : 0);
}

// tháng hiện tại
$last_day = get_last_day($month, nv_date('Y', NV_CURRENTTIME));
$static_from = mktime(0, 0, 0, $month, 01, nv_date('Y', NV_CURRENTTIME));
$static_to = mktime(23, 59, 59, $month, $last_day, nv_date('Y', NV_CURRENTTIME));
$sql = 'SELECT SUM(money) as total_money, SUM(discount) as discount, SUM(total) as total, SUM(price_reduce) as price_reduce, SUM(total_end) as total_end FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE static_time >= ' . $static_from . ' AND static_time <=' . $static_to . ' AND admin_id = ' . $admin_info['userid'];
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_static['month']['link'] = $base_url . 'static&time_from=' . nv_date('d/m/Y', $static_from) . '&time_to=' . nv_date('d/m/Y', $static_to);
    $arr_static['month']['total_money'] = number_format(!empty($row['total_money']) ? $row['total_money'] : 0);
    $arr_static['month']['total_end'] = number_format(!empty($row['total_end']) ? $row['total_end'] : 0);
}

// tháng trước
if ($month == 1) {
    $month = 12;
    $year = nv_date('Y', NV_CURRENTTIME) - 1;
} else {
    $month = $month - 1;
    $year = nv_date('Y', NV_CURRENTTIME);
}
$last_day = get_last_day($month, $year);
$static_from = mktime(0, 0, 0, $month, 01, $year);
$static_to = mktime(23, 59, 59, $month, $last_day, $year);

$sql = 'SELECT SUM(money) as total_money, SUM(discount) as discount, SUM(total) as total, SUM(price_reduce) as price_reduce, SUM(total_end) as total_end FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE static_time >= ' . $static_from . ' AND static_time <=' . $static_to . ' AND admin_id = ' . $admin_info['userid'];
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $arr_static['lastmonth']['link'] = $base_url . 'static&time_from=' . nv_date('d/m/Y', $static_from) . '&time_to=' . nv_date('d/m/Y', $static_to);
    $arr_static['lastmonth']['total_money'] = number_format(!empty($row['total_money']) ? $row['total_money'] : 0);
    $arr_static['lastmonth']['total_end'] = number_format(!empty($row['total_end']) ? $row['total_end'] : 0);
}

$xtpl = new XTemplate('main.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);

if (!empty($array_user_set_order)) {
    foreach ($array_user_set_order as $users) {
        $xtpl->assign('USERS', $users);
        $xtpl->parse('main.loop_users');
    }
}

if (!empty($array_user_share_messenger_zalo)) {
    foreach ($array_user_share_messenger_zalo as $users) {
        $xtpl->assign('USERS_MESS_ZALO', $users);
        $xtpl->parse('main.loop_users_mess_zalo');
    }
}
if (!empty($array_sale_view_zalo_tmp)) {
    foreach ($array_sale_view_zalo_tmp as $users) {
        $xtpl->assign('LIST_SALE_VIEW_ZALO', $array_user_id_users[$users]['username']);
        $xtpl->parse('main.list_sale_view_zalo');
    }
}
// sale đã chia trong vòng
if (!empty($module_config[$module_name]['sale_order_to'])) {
    $module_config[$module_name]['sale_order_to'] = explode(',', $module_config[$module_name]['sale_order_to']);
    foreach ($module_config[$module_name]['sale_order_to'] as $users) {
        if (!empty($array_user_id_users[$users])) {
            $users = $array_user_id_users[$users]['username'];
        }
        $xtpl->assign('SALE_ORDER_TO', $users);
        $xtpl->parse('main.sale_order_to');
    }
}
if (!empty($module_config[$module_name]['sale_order_to_vieweb'])) {
    $module_config[$module_name]['sale_order_to_vieweb'] = explode(',', $module_config[$module_name]['sale_order_to_vieweb']);
    foreach ($module_config[$module_name]['sale_order_to_vieweb'] as $users) {
        if (!empty($array_user_id_users[$users])) {
            $users = $array_user_id_users[$users]['username'];
        }
        $xtpl->assign('SALE_ORDER_TO_VIEWEB', $users);
        $xtpl->parse('main.sale_order_to_vieweb');
    }
}
if (!empty($module_config[$module_name]['leads_messenger_to_sale'])) {
    $module_config[$module_name]['leads_messenger_to_sale'] = explode(',', $module_config[$module_name]['leads_messenger_to_sale']);
    foreach ($module_config[$module_name]['leads_messenger_to_sale'] as $users) {
        if (!empty($array_user_id_users[$users])) {
            $users = $array_user_id_users[$users]['username'];
        }
        $xtpl->assign('LEADS_MESSENGER_TO_SALE', $users);
        $xtpl->parse('main.leads_messenger_to_sale');
    }
}
if (!empty($module_config[$module_name]['leads_zalo_to_sale'])) {
    $module_config[$module_name]['leads_zalo_to_sale'] = explode(',', $module_config[$module_name]['leads_zalo_to_sale']);
    foreach ($module_config[$module_name]['leads_zalo_to_sale'] as $users) {
        if (!empty($array_user_id_users[$users])) {
            $users = $array_user_id_users[$users]['username'];
        }
        $xtpl->assign('LEADS_ZALO_TO_SALE', $users);
        $xtpl->parse('main.leads_zalo_to_sale');
    }
}

if (!empty($module_config[$module_name]['api_pro_to_sale'])) {
    $module_config[$module_name]['api_pro_to_sale'] = explode(',', $module_config[$module_name]['api_pro_to_sale']);
    foreach ($module_config[$module_name]['api_pro_to_sale'] as $users) {
        if (!empty($array_user_id_users[$users])) {
            $users = $array_user_id_users[$users]['username'];
        }
        $xtpl->assign('API_PRO_TO_SALE', $users);
        $xtpl->parse('main.api_pro_to_sale');
    }
}
if (!empty($module_config[$module_name]['sale_order_dtnet_to'])) {
    $module_config[$module_name]['sale_order_dtnet_to'] = explode(',', $module_config[$module_name]['sale_order_dtnet_to']);
    foreach ($module_config[$module_name]['sale_order_dtnet_to'] as $users) {
        if (!empty($array_user_id_users[$users])) {
            $users = $array_user_id_users[$users]['username'];
        }
        $xtpl->assign('SALE_ORDER_DTNET_TO', $users);
        $xtpl->parse('main.sale_order_dtnet_to');
    }
}
if (!empty($module_config[$module_name]['transation_wallet_to_sale'])) {
    $module_config[$module_name]['transation_wallet_to_sale'] = explode(',', $module_config[$module_name]['transation_wallet_to_sale']);
    foreach ($module_config[$module_name]['transation_wallet_to_sale'] as $users) {
        if (!empty($array_user_id_users[$users])) {
            $users = $array_user_id_users[$users]['username'];
        }
        $xtpl->assign('SHARE_TRANSACTION_WALLET_TO', $users);
        $xtpl->parse('main.share_transaction_wallet_to');
    }
}

$xtpl->assign('ARR_STATIC', $arr_static);

if (isset($admin_config['view_leads_new']) and $admin_config['view_leads_new'] == 1 or defined('NV_IS_SPADMIN')) {
    if (!empty($data_leads_no_care['data'])) {
        foreach ($data_leads_no_care['data'] as $key => $view) {
            $view['source_leads'] = isset($array_groups_leads[$view['source_leads']]['title']) ? $array_groups_leads[$view['source_leads']]['title'] : '';
            $view['link_view'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $view['id'];
            $view['last_comment'] = nv_date('H:i:s d/m/Y', $view['last_comment']);

            $xtpl->assign('VIEW', $view);
            $xtpl->parse('main.leads.loop');
        }
    }
    $xtpl->assign('link_view_all', $base_url . 'leads&status=0');
    $xtpl->parse('main.leads');
} else {
    $xtpl->parse('main.empty_leads');
}
if (!empty($arr_opportunities) and (isset($admin_config['view_leads_new']) and $admin_config['view_leads_new'] == 1 or defined('NV_IS_SPADMIN'))) {
    foreach ($arr_opportunities as $opportunities) {
        $opportunities['source_leads'] = isset($array_groups_leads[$opportunities['source_leads']]['title']) ? $array_groups_leads[$opportunities['source_leads']]['title'] : '';
        $opportunities['last_comment'] = nv_date('H:i:s d/m/Y', $opportunities['last_comment']);
        $opportunities['link_view'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=opportunities_info&amp;id=' . $opportunities['id'];
        $xtpl->assign('VIEW_OP', $opportunities);

        $xtpl->parse('main.opportunities.loop');
    }
    $xtpl->assign('link_view_all', $base_url . 'opportunities&status=4');
    $xtpl->parse('main.opportunities');
} else {
    $xtpl->parse('main.empty_opportunities');
}

$xtpl->assign('DATA_LABEL', "'" . implode("', '", $arr_label) . "'");
$xtpl->assign('DATA_VALUE', implode(",", $arr_value));

$xtpl->assign('DATA_LABEL_OPPOTUNITIES', "'" . implode("', '", $arr_label_opportunities) . "'");
$xtpl->assign('DATA_VALUE_OPPOTUNITIES', implode(",", $arr_value_opportunities));

if ((isset($admin_config['show_chart']) and $admin_config['show_chart'] == 1) or defined('NV_IS_SPADMIN')) {
    $xtpl->parse('main.static_number');
}

$link_lead_log = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_log&amp;log_type=';
$xtpl->assign('LINK_LEADS_LOG_SHARE_MESSAGE', $link_lead_log . '2');
$xtpl->assign('LINK_LEADS_LOG_SHARE_ZALO', $link_lead_log . '8');
$xtpl->assign('LINK_LEADS_LOG_VIP', $link_lead_log . '3');
$xtpl->assign('LINK_LEADS_LOG_APIPRO', $link_lead_log . '4');
$xtpl->assign('LINK_LEADS_LOG_VIEWEB', $link_lead_log . '5');
$xtpl->assign('LINK_LEADS_LOG_TRANS_WALLET', $link_lead_log . '6');
$xtpl->assign('LINK_LEADS_LOG_ORDER_DTNET', $link_lead_log . '7');

$e_dead = doMarketingAPI('GetDeadEmail', [
    'get_edie' => 1,
    'per_page' => 10
]);
if ($e_dead) {
    foreach ($e_dead['data'] as $email) {
        $email['date_added'] = date('d/m/Y H:i', $email['date_added']);
        $email['comments'] = nv_htmlspecialchars($email['comments']);
        $xtpl->assign('DATA', $email);
        $xtpl->parse('main.email_dead.loop_email_dead');
    }
    $xtpl->assign('URL_BIDDING', URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=customs&amp;e_status=4');
    $xtpl->parse('main.email_dead');
}

// xác định giờ
$hour = (int) date('G', NV_CURRENTTIME);
if ($hour <= 12) {
    // ca sáng
    $current = 'Sáng - Ngày ' . date("d/m/Y", NV_CURRENTTIME);
    $next = 'Chiều - Ngày ' . date("d/m/Y", NV_CURRENTTIME);
    $current_hour_zalo = 1;
} else {
    // ca chiều
    $current = 'Chiều - Ngày ' . date("d/m/Y", NV_CURRENTTIME);
    $next = 'Sáng - Ngày ' . date("d/m/Y", NV_CURRENTTIME + 86400);
    $current_hour_zalo = 2;
}

$config_work_vinades = get_config_vinades();
/*
 * $config_work_vinades = [
 * 'dayAllowOfWeek' => '1,2,3,4,5,6',
 * 'holidays' => '30/04,01/05,02/09,01/09',
 * 'timeLateAllow' => '6',
 * 'work_week' => '1,3,5'
 * ];
 */
// check ngày nghỉ trên site vinades
$check = isWorkingDay(NV_CURRENTTIME, $config_work_vinades);
if ($check['to_day'] == 1) {
    // hôm nay nghỉ, cần check các ngày tiếp theo xem có làm hay k, tìm ngày làm gần nhất để hiển thị
    $next_woking = get_Working_day_next(NV_CURRENTTIME + 86400, $config_work_vinades);
    $next = 'Sáng - Ngày ' . date("d/m/Y", $next_woking);
} else {
    // hôm nay làm
    $currentDayOfWeek = date('w', NV_CURRENTTIME);
    if ($check['saturday'] == 1) {
        // t7 tuần này làm
        if ($currentDayOfWeek == 6 and $hour > 12) {
            // nay là t7 thì hiển thị ca tiếp theo vào sáng t2 tuần tới
            $next = 'Sáng - Ngày ' . date("d/m/Y", NV_CURRENTTIME + (86400 * 2));
        }
    } else {
        if ($currentDayOfWeek == 5 and $hour > 12) {
            // nay là t6 thì hiển thị ca tiếp theo vào sáng t2 tuần tới
            $next = 'Sáng - Ngày ' . date("d/m/Y", NV_CURRENTTIME + (86400 * 3));
        }
    }
}

$xtpl->assign('CURRENT_LANG', sprintf($nv_Lang->getModule('title_sale_view_zalo_ca1'), $current));
$xtpl->assign('NEXT_LANG', sprintf($nv_Lang->getModule('title_sale_view_zalo_ca2'), $next));

// sale đã chia trong vòng
$sale_view_zalo_to = [];
if (!empty($module_config[$module_name]['sale_view_zalo_to'])) {
    $sale_view_zalo_to = explode(',', $module_config[$module_name]['sale_view_zalo_to']);
    foreach ($sale_view_zalo_to as $users) {
        if (isset($array_sale_view_zalo[$users])) {
            unset($array_sale_view_zalo[$users]);
        }
    }
}

// nếu đã chạy đến cuối thì reset
if (empty($array_sale_view_zalo)) {
    $array_sale_view_zalo = $array_sale_view_zalo_tmp;
    $sale_view_zalo_to = [];
} else if (count($array_sale_view_zalo) == 3) { // danh sách chỉ còn 3 thì nối tiếp từ đầu danh sách 1 người
    $_first = array_key_first($array_sale_view_zalo_tmp);
    $array_sale_view_zalo[$_first] = $_first;
} else if (count($array_sale_view_zalo) == 1) { // danh sách chỉ còn 1 thì đưa 1 lên đầu danh sách gốc
    $_last = array_key_last($array_sale_view_zalo);

    // check lại do sau khi đưa sale cuối lên đầu thì vòng tiếp theo bị xóa ngay ở trên
    $index = array_search($_last, array_keys($array_sale_view_zalo_tmp));
    // nếu sau last đó vẫn còn sale
    if ($index !== false && $index < count($array_sale_view_zalo_tmp) - 1) {
        // gán sale tiếp theo vào ds
        $nextKey = array_keys($array_sale_view_zalo_tmp)[$index + 1];
        $nextValue = $array_sale_view_zalo_tmp[$nextKey];
        $array_sale_view_zalo[$nextKey] = $nextValue;
    } else {
        $tmp = $array_sale_view_zalo_tmp;
        $_tmp[$_last] = $_last;
        $array_sale_view_zalo = $_tmp + $tmp;
        $sale_view_zalo_to = [];
    }
}

if ($module_config[$module_name]['current_hour_zalo'] != $current_hour_zalo and $check['to_day'] == 0) {
    $currentShiftSales = array_slice($array_sale_view_zalo, 0, 2);
    foreach ($currentShiftSales as $users) {
        $sale_view_zalo_to[] = $users;
        // unset($array_sale_view_zalo[$users]);
    }
    $db->exec("UPDATE " . NV_CONFIG_GLOBALTABLE . " SET config_value = " . $db->quote(implode(',', $sale_view_zalo_to)) . " WHERE lang = '" . NV_LANG_DATA . "' AND module = " . $db->quote('crmbidding') . " AND config_name = " . $db->quote('sale_view_zalo_to'));
    $db->exec("UPDATE " . NV_CONFIG_GLOBALTABLE . " SET config_value = " . $current_hour_zalo . " WHERE lang = '" . NV_LANG_DATA . "' AND module = " . $db->quote('crmbidding') . " AND config_name = " . $db->quote('current_hour_zalo'));

    $nv_Cache->delMod('settings');
    $nv_Cache->delMod($module_name);
}
$currentShiftSales = array_slice($array_sale_view_zalo, 0, 2);
if (count($array_sale_view_zalo) == 2) {
    $nextShiftSales = array_slice($array_sale_view_zalo_tmp, 0, 2);
} else {
    $nextShiftSales = array_slice($array_sale_view_zalo, 2, 2);
}

if ($check['to_day'] == 0) {
    foreach ($currentShiftSales as $users) {
        $sale_view_zalo_to[$users] = $users;
        if (!empty($array_user_id_users[$users])) {
            $users = $array_user_id_users[$users]['username'];
        }
        $xtpl->assign('CURRENTSHIFTSALES', $users);
        $xtpl->parse('main.sale_view_zalo_ca1');
    }
} else {
    $xtpl->assign('CURRENTSHIFTSALES', 'Nghỉ');
    $xtpl->parse('main.sale_view_zalo_ca1');
    $nextShiftSales = array_slice($array_sale_view_zalo, 0, 2);
}
foreach ($nextShiftSales as $users) {
    if (!empty($array_user_id_users[$users])) {
        $users = $array_user_id_users[$users]['username'];
    }
    $xtpl->assign('NEXTSHIFTSALES', $users);
    $xtpl->parse('main.sale_view_zalo_ca2');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
