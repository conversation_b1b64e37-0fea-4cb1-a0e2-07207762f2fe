$(document).ready(function() {
    // Xóa người giới thiệu của tài khoản
    $('[data-toggle="delaffuser"]').on('click', function(e) {
        e.preventDefault();
        var pri_uid = $(this).data('pri');
        var pre_uid = $(this).data('pre');
        if (confirm(nv_is_del_confirm[0])) {
            $.post(
                script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=affiliate&nocache=' + new Date().getTime(),
                'delete=1&pri_uid=' + pri_uid + '&pre_uid=' + pre_uid, function(res) {
                if (res == 'OK') {
                    location.reload();
                    return;
                }
                alert(res);
            });
        }
    });
});
