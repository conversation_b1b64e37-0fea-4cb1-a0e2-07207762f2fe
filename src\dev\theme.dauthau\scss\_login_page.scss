body.op-login.mf-users {
  background-color: #f0f4f9;
}

.hstdt-login {
  display: flex;
  min-height: 100%;
  align-items: center;
  justify-content: center;
}

.login-page {
  .page-box {
    background-color: #fff;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);

    .parent-logo {
      position: relative;
      margin-bottom: 8px;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 15%;
        right: 15%;
        height: 1px;
        background-color: #ddd;
      }

      img {
        max-width: 100%;
        height: auto;
      }
    }

    .broker-logo {
      img {
        width: 300px;
        max-width: 85%;
        height: auto;
      }
    }

    .hstdt-welcome {
      margin-bottom: 20px;
    }
  }
}

.register-page {
  .broker-logo {
    img {
      max-width: 85%;
      height: auto;
      width: 300px;
    }
  }
}

.login-page-footer {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  padding: 8px;

  .language {
    .form-control {
      background-color: transparent;
      border: 0;
      box-shadow: none;
      padding: 0;
      height: 24px;
    }
  }
}
