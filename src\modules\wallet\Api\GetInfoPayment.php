<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\wallet\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetInfoPayment implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'order';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        require_once NV_ROOTDIR . '/modules/' . $module_file . '/init.php';
        require_once NV_ROOTDIR . '/modules/wallet/wallet.class.php';
        $wallet = new \nukeviet_wallet();

        // Đưa các giá trị array vào post key khác để security get
        $_POST['url_back_op'] = $_POST['url_back']['op'] ?? '';
        $_POST['url_back_querystr'] = $_POST['url_back']['querystr'] ?? '';
        $_POST['url_admin_op'] = $_POST['url_admin']['op'] ?? '';
        $_POST['url_admin_querystr'] = $_POST['url_admin']['querystr'] ?? '';

        $data = [
            'site_id' => $nv_Request->get_absint('site_id', 'post', 0),
            'modname' => nv_substr($nv_Request->get_title('modname', 'post', ''), 0, 100), // Module thanh toán
            'id' => nv_substr($nv_Request->get_title('id', 'post', ''), 0, 100), // ID đơn hàng
            'order_object' => nv_substr($nv_Request->get_title('order_object', 'post', ''), 0, 250), // Loại đối tượng được mua ví dụ: Ứng dụng, sản phẩm, giỏ hàng...
            'order_name' => nv_substr($nv_Request->get_title('order_name', 'post', ''), 0, 250), // Tên đối tượng mua ví dụ: Iphone X 256GB, module shops...
            'money_amount' => doubleval($nv_Request->get_float('money_amount', 'post', 0)), // Giá tiền cần thanh toán
            'money_unit' => nv_substr($nv_Request->get_title('money_unit', 'post', ''), 0, 3), // Đơn vị tiền tệ sử dụng ví dụ VND, USD...
            'url_back' => [
                'op' => $nv_Request->get_title('url_back_op', 'post', ''),
                'querystr' => $nv_Request->get_title('url_back_querystr', 'post', '')
            ],
            'url_admin' => [
                'op' => $nv_Request->get_title('url_admin_op', 'post', ''),
                'querystr' => $nv_Request->get_title('url_admin_querystr', 'post', '')
            ]
        ];

        $payment_info = $wallet->getInfoPayment($data);
        if ($payment_info['status'] !== 'SUCCESS') {
            return $this->result->setCode('1001')->setMessage($payment_info['message'])->getResult();
        }

        $this->result->setSuccess();
        $this->result->set('url', $payment_info['url']);

        return $this->result->getResult();
    }
}
