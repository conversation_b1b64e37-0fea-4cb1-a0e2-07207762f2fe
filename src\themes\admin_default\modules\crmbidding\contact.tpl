<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<script type="text/javascript">
    $(document).ready(function() {
        $("#filter_organizations, #filter_organizations_add").select2({
            language: "vi",
            width: '100%',
            allowClear: true,
            placeholder: '{LANG.filter_organizations}',
            ajax: {
            url: script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=contact&get_org=1',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q_select: params.term, // search term
                    };
                },
                processResults: function (data, params) {
                    return {
                        results: data,
                        pagination: {
                            more: (params.page * 30) < data.total_count
                        }
                    };
                },
                cache: true
            },
            escapeMarkup: function (markup) { return markup; },
            minimumInputLength: 0,
            templateResult: formatRepo, // omitted for brevity, see the source of this page
            templateSelection: formatRepoSelection // omitted for brevity, see the source of this page
        });


        function formatRepo (repo) {
            if (repo.loading) {
                return "Loading....";
            }
            return repo.title;
        }

        function formatRepoSelection (repo) {
            return repo.title || "{LANG.filter_organizations}";
        }
    });

</script>
<!-- BEGIN: selected_search -->
<script type="text/javascript">
    $(document).ready(function() {
        $("#filter_organizations").select2("trigger", "select", {
            data: { id: "{ORG.id}", title: "{ORG.organizationname}"}
        });
    });
</script>
<!-- END: selected_search -->

<!-- BEGIN: selected_edit -->
<script type="text/javascript">
    $(document).ready(function() {
        $("#filter_organizations_add").select2("trigger", "select", {
            data: { id: "{ORG_EDIT.id}", title: "{ORG_EDIT.organizationname}"}
        });
    });
</script>
<!-- END: selected_edit -->
<!-- BEGIN: view -->
<div class="well">
<form action="{NV_BASE_ADMINURL}index.php" method="get">
    <input type="hidden" name="{NV_LANG_VARIABLE}"  value="{NV_LANG_DATA}" />
    <input type="hidden" name="{NV_NAME_VARIABLE}"  value="{MODULE_NAME}" />
    <input type="hidden" name="{NV_OP_VARIABLE}"  value="{OP}" />
    <div class="row">
        <div class="col-xs-24 col-md-6">
            <div class="form-group">
                <input class="form-control" type="text" value="{Q}" name="q" maxlength="255" placeholder="{LANG.search_title}" />
            </div>
        </div>
        <div class="col-xs-24 col-md-6">
            <div class="form-group">
                <select class="form-control" id="filter_organizations" name="filter_organizations">
                    
                </select>
            </div>
        </div>
        <div class="col-xs-12 col-md-3">
            <div class="form-group">
                <input class="btn btn-primary btn-block" type="submit" value="{LANG.search_submit}" />
            </div>
        </div>
        <div class="col-xs-12 col-md-3">
            <div class="form-group">
                <a class="btn btn-success btn-block" href="#add_contact">{LANG.add_contact}</a>
            </div>
        </div>
        <div class="col-xs-12 col-md-6">
        </div>
    </div>
</form>
</div>
<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th class="w100 text-center">{LANG.number}</th>
                    <th class="text-center">
                        <div class="inlineblock">
                            <a href="{ORDER_BY_CONTACT}">{LANG.contactname}</a>
                        </div> <!-- BEGIN: contactname --> <!-- BEGIN: asc --> <a href="{ORDER_BY_CONTACT_NAME_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_CONTACT_NAME_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: contactname -->
                    </th>
                    <th class="w150 text-center">
                       <div class="inlineblock">
                            <a href="{ORDER_BY_PRIMARY_PHONE}">{LANG.primaryphone_title}</a>
                        </div> <!-- BEGIN: primaryphone --> <!-- BEGIN: asc --> <a href="{ORDER_BY_PRIMARY_PHONE_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_PRIMARY_PHONE_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: primaryphone -->
                    </th>
                    <th class="text-center">    
                       <div class="inlineblock">
                            <a href="{ORDER_BY_PRIMARY_EMAIL}">{LANG.primaryemail}</a>
                        </div> <!-- BEGIN: primaryemail --> <!-- BEGIN: asc --> <a href="{ORDER_BY_PRIMARY_EMAIL_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_PRIMARY_EMAIL_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: primaryemail -->
                    <th class="text-center"> 
                       <div class="inlineblock">
                            <a href="{ORDER_BY_ORGANIZATION}">{LANG.organizationsid}</a>
                        </div> <!-- BEGIN: organizationsid --> <!-- BEGIN: asc --> <a href="{ORDER_BY_ORGANIZATION_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_ORGANIZATION_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: organizationsid -->
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">
                            <a href="{ORDER_BY_ADDRESS}">{LANG.address}</a>
                        </div> <!-- BEGIN: address --> <!-- BEGIN: asc --> <a href="{ORDER_BY_ADDRESS_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_ADDRESS_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: address -->
                    </th>
                    <th class="w100 text-center">
                        <div class="inlineblock">
                            <a href="{ORDER_BY_ACTICE}">{LANG.active}</a>
                        </div> <!-- BEGIN: active --> <!-- BEGIN: asc --> <a href="{ORDER_BY_ACTICE_ASC}"><i class="fa fa-sort-asc icon-lead" aria-hidden="true"></i></a> <!-- END: asc --> <!-- BEGIN: desc --> <a href="{ORDER_BY_ACTICE_DESC}"><i class="fa fa-sort-desc icon-lead" aria-hidden="true"></i></a> <!-- END: desc --> <!-- END: active -->
                    </th>
                    <th class="w150 text-center">{LANG.action}</th>
                </tr>
            </thead>
            <!-- BEGIN: generate_page -->
            <tfoot>
                <tr>
                    <td class="text-center" colspan="8">{NV_GENERATE_PAGE}</td>
                </tr>
            </tfoot>
            <!-- END: generate_page -->
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="text-center"> {VIEW.number} </td>
                    <td class="w400 text-break"><a href="{VIEW.link_detail}" alt="{VIEW.contactname}"><b> {VIEW.contactname} </b></a></td>
                    <td class="w150 text-center text-break"> {VIEW.primaryphone} </td>
                    <td class="w250 text-break"> {VIEW.primaryemail} </td>
                    <td class="w400 text-break"><a href="{VIEW.link_detail_org}" alt="{VIEW.organizationsid}"><b>  {VIEW.organizationsid} </b></a></td>
                    <td class="w300 text-break"> {VIEW.address} </td>
                    <td class="text-center"><input type="checkbox" name="active" id="change_status_{VIEW.id}" value="{VIEW.id}" {CHECK} onclick="nv_change_status({VIEW.id});" /></td>
                    <td class="text-center">
                        <a data-toggle="tooltip" title="{LANG.edit}" href="{VIEW.link_edit}#edit"><i class="fa fa-edit fa-lg">&nbsp;</i></a>
                        <a data-toggle="tooltip" title="{LANG.delete}" href="{VIEW.link_delete}" onclick="return confirm(nv_is_del_confirm[0]);"><em class="fa fa-trash-o fa-lg">&nbsp;</em></a>
                    </td>
                </tr>
                <!-- END: loop -->
                <!-- BEGIN: empty -->
                <tr>
                    <td colspan="8"><p class="text-center"><i>{LANG.no_result}</i></p></td>
                </tr>
                <!-- END: empty -->
            </tbody>
        </table>
    </div>
</form>
<!-- END: view -->
<!-- BEGIN: success -->
    <div class="alert alert-success">{SUCCESS}</div>
<!-- END: success -->
<!-- BEGIN: error -->
    <div class="alert alert-warning">{ERROR}</div>
<!-- END: error -->
<div class="panel panel-default">
    <div class="panel-body">
    <form class="form-horizontal" id="add_contact" action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
        <input type="hidden" name="id" value="{ROW.id}" />
        <input type="hidden" name="convert_leads" value="{ROW.convert_leads}" />
        <div class="form-group">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.contactname}</strong> <span class="red">(*)</span></label>
            <div class="col-sm-19 col-md-20">
                <input class="form-control" type="text" name="contactname" value="{ROW.contactname}" required="required" oninvalid="setCustomValidity(nv_required)" oninput="setCustomValidity('')" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.shortname}</strong></label>
            <div class="col-sm-19 col-md-20">
                <input class="form-control" type="text" name="shortname" value="{ROW.shortname}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.primaryphone}</strong> <span class="red">(*)</span> <a class="order-edit-ection" href="#" data-toggle="tooltip" title="" data-original-title="Giá trị Số điện thoại chính hoặc Email chính có thể nhập 1 trong 2"><i class="fa fa-info-circle" aria-hidden="true"></i></a></label>
            <div class="col-sm-19 col-md-20">
                <input class="form-control" type="text" name="primaryphone" value="{ROW.primaryphone}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.secondaryphone}</strong></label>
            <div class="col-sm-19 col-md-20">
                <input class="form-control" type="text" name="secondaryphone" value="{ROW.secondaryphone}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.primaryemail}</strong> <span class="red">(*)</span> <a class="order-edit-ection" href="#" data-toggle="tooltip" title="" data-original-title="Giá trị Số điện thoại chính hoặc Email chính có thể nhập 1 trong 2"><i class="fa fa-info-circle" aria-hidden="true"></i></a></label>
            <div class="col-sm-19 col-md-20">
                <input class="form-control" type="text" name="primaryemail" value="{ROW.primaryemail}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.secondaryemail}</strong></label>
            <div class="col-sm-19 col-md-20">
                <input class="form-control" type="text" name="secondaryemail" value="{ROW.secondaryemail}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.address}</strong></label>
            <div class="col-sm-19 col-md-20">
                <input class="form-control" type="text" name="address" value="{ROW.address}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.description}</strong></label>
            <div class="col-sm-19 col-md-20">
                <input class="form-control" type="text" name="description" value="{ROW.description}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.organizationsid}</strong> <span class="red">(*)</span></label>
            <div class="col-sm-19 col-md-20">
                <select class="form-control" name="organizationsid" id="filter_organizations_add">
                </select>
            </div>
        </div>
        <p class="text-center"><i>{LANG.note_email_phone}</i></p>
        <div class="form-group" style="text-align: center"><input class="btn btn-primary" name="submit" type="submit" value="{LANG.save}" /></div>
    </form>
    </div>
</div>
<script type="text/javascript">
//<![CDATA[
    function nv_change_status(id) {
        var new_status = $('#change_status_' + id).is(':checked') ? true : false;
        if (confirm(nv_is_change_act_confirm[0])) {
            var nv_timer = nv_settimeout_disable('change_status_' + id, 5000);
            $.post(script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=contact&nocache=' + new Date().getTime(), 'change_status=1&id='+id, function(res) {
                var r_split = res.split('_');
                if (r_split[0] != 'OK') {
                    alert(nv_is_change_act_confirm[2]);
                }
            });
        }
        else{
            $('#change_status_' + id).prop('checked', new_status ? false : true);
        }
        return;
    }

//]]>
</script>
<!-- END: main -->
<!-- BEGIN: view_detail -->
<div class="panel panel-default">
    <div class="panel-body">
        <div class="form-group row">
            <a class="inlineblock mr-2" href="{BACK_URL}"><h3><i class="fa fa-arrow-left"></i> {LANG.return}</h3></a>
            <!-- BEGIN: link_convert_leads -->
            <a class="btn btn-primary" href="{LINK_CONVERT_LEADS}"><i class="fa fa-cube"></i> {LANG.chuyen_leads}</a>
            <!-- END: link_convert_leads -->
            <!-- BEGIN: link_view_leads -->
            <a class="btn btn-primary" href="{LINK_VIEW_LEADS}"><i class="fa fa-cube"></i> {LANG.view_leads}</a>
            <!-- END: link_view_leads -->
        </div>
        <div class="form-group row">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.contactname}</strong></label>
            <div class="col-sm-19 col-md-20">
                <p class="text-primary textinfo"><b>{ROW.contactname}</b></p>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.shortname}</strong></label>
            <div class="col-sm-19 col-md-20">
                <p class="text-primary textinfo"><b>{ROW.shortname}</b></p>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.primaryphone}</strong></label>
            <div class="col-sm-19 col-md-20">
                <p class="text-primary textinfo"><b>{ROW.primaryphone}</b></p>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.secondaryphone}</strong></label>
            <div class="col-sm-19 col-md-20">
                <p class="text-primary textinfo"><b>{ROW.secondaryphone}</b></p>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.primaryemail}</strong></label>
            <div class="col-sm-19 col-md-20">
                <p class="text-primary textinfo"><b>{ROW.primaryemail}</b></p>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.secondaryemail}</strong></label>
            <div class="col-sm-19 col-md-20">
                <p class="text-primary textinfo"><b>{ROW.secondaryemail}</b></p>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.address}</strong></label>
            <div class="col-sm-19 col-md-20">
                <p class="text-primary textinfo"><b>{ROW.address}</b></p>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.description}</strong></label>
            <div class="col-sm-19 col-md-20">
                <p class="text-primary textinfo"><b>{ROW.description}</b></p>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.organizationsid}</strong></label>
            <div class="col-sm-19 col-md-20">
                <p class="text-primary textinfo"><b><u><a href="{ORG_URL}" alt="{NAME_ORG}">{NAME_ORG}</a></u></b></p>
            </div>
        </div>
    </div>
</div>
<!-- END: view_detail -->