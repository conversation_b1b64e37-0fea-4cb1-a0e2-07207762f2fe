<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\wallet\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdateMoney implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'money';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        require_once NV_ROOTDIR . '/modules/' . $module_file . '/init.php';
        require_once NV_ROOTDIR . '/modules/wallet/wallet.class.php';
        $wallet = new \nukeviet_wallet();

        $site_id = $nv_Request->get_absint('site_id', 'post', 0);
        $userid = $nv_Request->get_absint('userid', 'post', 0);
        $money = $nv_Request->get_float('money', 'post', 0);
        $message = $nv_Request->get_title('message', 'post', '');
        $money_unit = $nv_Request->get_title('money_unit', 'post', 'VND');
        $type = $nv_Request->get_absint('type', 'post', 1);

        $trans_id = $wallet->update($money, $money_unit, $site_id, $userid, $message, ($type == 1 ? false : true));

        // Lỗi
        if ($wallet->isError()) {
            return $this->result->setCode('1001')->setMessage($trans_id)->getResult();
        }

        $this->result->setSuccess();
        $this->result->set('data', $trans_id);

        return $this->result->getResult();
    }
}
