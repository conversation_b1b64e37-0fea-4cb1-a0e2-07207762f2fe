<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_MOD_CALENDAR')) {
    exit('Stop!!!');
}

/**
 * @return string
 */
function nv_theme_main($group_event, $is_call_ajax = false, $on_calendars = [])
{
    global $module_info, $module_name, $module_file, $op_file, $nv_Lang;

    $xtpl = new XTemplate('main.tpl', get_module_tpl_dir('main.tpl'));
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('MODULE_FILE', $module_file);
    $xtpl->assign('OP', $op_file);

    $xtpl->assign('EVENTS', json_encode($group_event, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE));
    $xtpl->assign('IS_CALL_AJAX', $is_call_ajax ? 1 : 0);
    $xtpl->assign('ON_CALENDARS', json_encode($on_calendars));

    $xtpl->parse('main');
    return $xtpl->text('main');
}
