<!-- BEGIN: main -->
<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <caption>{LANG.order_config}</caption>
            <tbody>
                <tr>
                    <th>{LANG.orderid_to}</th>
                    <td><input type="text" value="{DATA.orderid_to}" class="form-control" name="orderid_to" /></td>
                </tr>
                <tr>
                    <th>{LANG.orderid_dtnet_to}</th>
                    <td><input type="text" value="{DATA.orderid_dtnet_to}" class="form-control" name="orderid_dtnet_to" /></td>
                </tr>
                <tr>
                    <th>{LANG.sale_order_to}</th>
                    <td><input type="text" value="{DATA.sale_order_to}" class="form-control" name="sale_order_to" /></td>
                </tr>
                <tr>
                    <th>{LANG.sale_order_to_vieweb}</th>
                    <td><input type="text" value="{DATA.sale_order_to_vieweb}" class="form-control" name="sale_order_to_vieweb" /></td>
                </tr>
                <tr>
                    <th>{LANG.sale_order_dtnet_to}</th>
                    <td><input type="text" value="{DATA.sale_order_dtnet_to}" class="form-control" name="sale_order_dtnet_to" /></td>
                </tr>

                <tr>
                    <th>{LANG.sale_telepro_callback_to}</th>
                    <td><input type="text" value="{DATA.sale_telepro_callback_to}" class="form-control" name="sale_telepro_callback_to" /></td>
                </tr>

                <tr>
                    <th>{LANG.leads_messenger_id}</th>
                    <td><input type="text" value="{DATA.leads_messenger_id}" class="form-control" name="leads_messenger_id" /></td>
                </tr>
                <tr>
                    <th>{LANG.leads_messenger_to_sale}</th>
                    <td><input type="text" value="{DATA.leads_messenger_to_sale}" class="form-control" name="leads_messenger_to_sale" /></td>
                </tr>
                 <tr>
                    <th>{LANG.leads_zalo_id}</th>
                    <td><input type="text" value="{DATA.leads_zalo_id}" class="form-control" name="leads_zalo_id" /></td>
                </tr>
                <tr>
                    <th>{LANG.leads_zalo_to_sale}</th>
                    <td><input type="text" value="{DATA.leads_zalo_to_sale}" class="form-control" name="leads_zalo_to_sale" /></td>
                </tr>
                <tr>
                    <th>{LANG.list_sale_view_zalo_to}</th>
                    <td><input type="text" value="{DATA.sale_view_zalo_to}" class="form-control" name="sale_view_zalo_to" /></td>
                </tr>
                
                <tr>
                    <th>{LANG.api_pro_to_sale}</th>
                    <td><input type="text" value="{DATA.api_pro_to_sale}" class="form-control" name="api_pro_to_sale" /></td>
                </tr>
                <tr>
                    <th>{LANG.last_active_profile_dtnet}</th>
                    <td><input type="text" value="{DATA.last_active_profile_dtnet}" class="form-control" name="last_active_profile_dtnet" /></td>
                </tr>
                <tr>
                    <th>{LANG.sale_active_profile_dtnet_to}</th>
                    <td><input type="text" value="{DATA.sale_active_profile_dtnet_to}" class="form-control" name="sale_active_profile_dtnet_to" /></td>
                </tr>
                <tr>
                    <th>{LANG.transation_wallet_id}</th>
                    <td><input type="text" value="{DATA.transation_wallet_id}" class="form-control" name="transation_wallet_id" /></td>
                </tr>
                <tr>
                    <th>{LANG.transation_wallet_to_sale}</th>
                    <td><input type="text" value="{DATA.transation_wallet_to_sale}" class="form-control" name="transation_wallet_to_sale" /></td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover form-inline">
            <caption>{LANG.config_sale}</caption>
            <tbody>
                <tr>
                    <th class="text-center w100">{LANG.weight}</th>
                    <th class="text-center">{LANG.userid}</th>
                    <th class="text-center">{LANG.account}</th>
                </tr>
                <!-- BEGIN: user -->
                <tr>
                    <td class="text-center"><select class="form-control" id="id_weight_{USERS.userid}" onchange="nv_change_weight('{USERS.userid}');">
                            <!-- BEGIN: weight_loop -->
                            <option value="{WEIGHT.key}"{WEIGHT.selected}>{WEIGHT.title}</option>
                            <!-- END: weight_loop -->
                    </select></td>
                    <td>{USERS.userid}</td>
                    <td>{USERS.username}</td>
                </tr>
                <!-- END: user -->
            </tbody>
        </table>
    </div>
    <div class="text-center">
        <!-- BEGIN: submit -->
        <input class="btn btn-primary" name="submit" type="submit" value="{LANG.save}" />
        <!-- END: submit -->
    </div>
</form>

<script type="text/javascript">
    function nv_change_weight(id) {
        var nv_timer = nv_settimeout_disable('id_weight_' + id, 5000);
        var new_vid = $('#id_weight_' + id).val();
        $.post(script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=config_sale&nocache=' + new Date().getTime(), 'ajax_action=1&id=' + id + '&new_vid=' + new_vid, function(res) {
            var r_split = res.split('_');
            if (r_split[0] != 'OK') {
                alert(nv_is_change_act_confirm[2]);
            }
            window.location.href = script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=config_sale';
            return;
        });
        return;
    }
</script>
<!-- END: main -->
