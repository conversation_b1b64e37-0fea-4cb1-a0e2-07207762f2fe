<?php
use NukeViet\Api\DoApi;

/**
 *
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('static_renewal');

$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users';
$result = $db->query($sql);
$array_groups_users = array();
while ($row = $result->fetch()) {
    $row['config'] = json_decode($row['config'], true);
    $array_groups_users[$row['userid']] = $row;
}

$array_search = array();
$array_search['admin_id'] = $nv_Request->get_int('admin_id', 'post,get', 0);
$array_search['static_vieweb'] = $nv_Request->get_int('static_vieweb', 'post,get', 0);

$_from = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
$from = nv_date('d/m/Y', $_from); // Mặc định ngày 01 của tháng
$to = nv_date('d/m/Y', NV_CURRENTTIME);
$sfrom = nv_substr($nv_Request->get_title('sfrom', 'get', $from), 0, 10);
$sto = nv_substr($nv_Request->get_title('sto', 'get', $to), 0, 10);

/**
 * điều hành chung xem tất
 * Sales nào dc cấu hình xem tất thì dc xem còn lại chỉ xem của mình
 * trường hợp trưởng nhóm thì xem thêm các thành viên của nhóm, $arr_admin_view
 */
$arr_admin_view = $arr_admin_view_tmp = [];
if ($data_config['view_static'] == 2 and !defined('NV_IS_SPADMIN')) {
    if (isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 0) {
        if ($array_groups_users[$admin_info['userid']]['is_leader'] == 1) {
            $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $array_groups_users[$admin_info['userid']]['group_id'] . ' AND userid != ' . $admin_info['userid'];
            $_result = $db->query($_sql);
            while ($_row_groups_users = $_result->fetch()) {
                $arr_admin_view[$_row_groups_users['userid']] = $_row_groups_users['userid'];
            }
            $arr_admin_view[$admin_info['userid']] = $admin_info['userid'];
            $arr_admin_view_tmp = $arr_admin_view;
            if ($array_search['admin_id'] != 0) {
                $arr_admin_view = [];
            }
        } else {
            $array_search['admin_id'] = $admin_info['userid'];
        }
    }
}

$api = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);

$api->setModule('bidding')
    ->setLang('vi')
    ->setAction('GetStaticRenewal')
    ->setData([
    'admin_id' => $array_search['admin_id'],
    'static_vieweb' => $array_search['static_vieweb'],
    'sfrom' => $sfrom,
    'sto' => $sto,
    'arr_admin_view' => $arr_admin_view
]);
$result = $api->execute();

$array_vip = $array_vip_renewal = $array_vip_next = [];
if ($result['status'] == 'success') {
    $array_vip = $result['array_vip'];

    $time_search_from = $result['time_search_from'];
    $time_search_to = $result['time_search_to'];
    $time_search_next_from = $result['time_search_next_from'];
    $time_search_next_to = $result['time_search_next_to'];

// tuyenhv comment lại do thấy thừa
   /*  // Các gói cần gia hạn trong tháng
    $array_vip_renewal_month = $result['array_vip_month'];
    $array_vip_renewal_before = [];
    if (!empty($array_vip_renewal_month)) {
        foreach ($array_vip_renewal_month as $k => $v) {
            foreach ($v as $k1 => $v1) {
                $check_order = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE order_id=' . $v1['order_id'] . ' AND vip = ' . $v1['vip'] . ' AND type_export= ' . $v1['type_export'] . ' AND status = 4')->fetch();
                $array_vip_renewal_before[$v1['vip']][$v1['id']] = $v1;
            }
        }
    }

    // Lấy những đã gia hạn trước đó
    $_order = $db->query('SELECT total_end, static_time, userid, affiliate_userid, admin_id,caregiver_id  FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE static_time <= ' . $result['time_search_to'] . ' AND (affiliate_userid = ' . $array_search['admin_id'] . ' OR caregiver_id = ' . $array_search['admin_id'] . ' OR admin_id = ' . $array_search['admin_id'] . ') and is_renewal = 1')->fetchAll();
    foreach ($_order as $k => $v) {
        $_order[$k]['time'] = date('d/m/Y H:i', $v['static_time']);
    } */

    foreach ($array_vip as $k_vip => $row) { // lấy từng nhóm vip
        foreach ($row as $_row) { // lấy từng customs
            if (!empty($_row['isrenewal_order_id'])) {
                // xác định tổng giá gia hạn và việc gia hạn trc hay không qua bảng order
                $sql_order = 'SELECT * FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE order_id=' . $_row['isrenewal_order_id'] . ' AND vip = ' . $_row['vip'] . ' AND status = 4';
                if ($_row['vip'] == 88) {
                    if ($_row['type_export'] == 1) {
                        $sql_order .= ' AND type_export= ' . $_row['type_export'] . ' ';
                    } else {
                        $sql_order .= ' AND (type_export= 2 OR type_export= 3)';
                    }
                }
                $_order = $db->query($sql_order)->fetch();
                if (!empty($_order)) {
                    // các đơn hàng đã gia hạn trước đó
                    if ($_order['static_time'] < $time_search_from) {
                        $array_vip_renewal[$_row['vip']]['data_before'][$_row['id']] = $_row;
                    }
                    if (!isset($array_vip_renewal[$_row['vip']]['total_end'])) {
                        $array_vip_renewal[$_row['vip']]['total_end'] = 0;
                    }
                    $array_vip_renewal[$_row['vip']]['total_end'] += $_order['total_end'];
                    $array_vip_renewal[$_row['vip']]['data'][$_row['id']] = $_row;
                }
            }
        }
    }

    $array_vip_next = $result['array_vip_next'];
    foreach ($array_vip_next as $row) {
        foreach ($row as $_row) {
            $_order = $db->query('SELECT total_end FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE order_id=' . $_row['order_id'] . ' AND vip = ' . $_row['vip'] . ' AND type_export= ' . $_row['type_export'])->fetch();
            $_row['total'] = 0;
            if (!empty($_order)) {
                $_row['total'] = $_order['total_end'];
            }
            $array_vip_next[$_row['vip']][$_row['id']] = $_row;
        }
    }
}

// tháng tiếp theo
$next_month = nv_date('m', NV_CURRENTTIME) + 1;
$next_year = nv_date('Y', NV_CURRENTTIME);
if (nv_date('m', NV_CURRENTTIME) == 12) {
    $next_month = 1;
    $next_year = $next_year + 1;
}

$params = [
    'average_years' => 1,
];

$api = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
$api->setModule('bidding')
    ->setLang('vi')
    ->setAction('StaticVip')
    ->setData($params);
$ApiStaticVip = $api->execute();
if ($ApiStaticVip['status'] == 'success') {
    $staticVip = $ApiStaticVip['data'];
    $stt = 1;
    foreach ($staticVip['ave_vip_year'] as $k1 => $v1) {
        $staticVip['ave_vip_year'][$k1]['ave'] = round($v1['ave'], 2);
        $staticVip['ave_vip_year'][$k1]['stt'] = $stt++;
        $staticVip['ave_vip_year'][$k1]['title_vip'] = $nv_Lang->getModule('vip' . $v1['vip']);
    }
}


$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('SEARCH', $array_search);
$xtpl->assign('NEXT_MONTH', $next_month);
$xtpl->assign('NEXT_YEAR', $next_year);

$xtpl->assign('STATIC_VIEWEB', $array_search['static_vieweb'] == 1 ? 'checked="checked"' : '');

$xtpl->assign('FROM', $sfrom);
$xtpl->assign('FROM_DEFAULT', $from);
$xtpl->assign('TO', $sto);
$xtpl->assign('TO_DEFAULT', $to);

$xtpl->assign('lang_static_renewal', sprintf($nv_Lang->getModule('lang_static_renewal'), $sfrom, $sto));

// $min_date = $db->query('SELECT from_time FROM ' . NV_PREFIXLANG . '_bidding_customs ORDER BY from_time ASC LIMIT 1')->fetchColumn();
$min_date = 0;
$param_customs = $api_custom  = $order = [];
$order['from_time'] = "ASC";
$params_customs = [
    'page' => 1,
    'per_page' => 1,
    'order' => $order
];
$api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
$api->setModule('bidding')
    ->setLang('vi')
    ->setAction('ListBiddingCustoms')
    ->setData($params_customs);
$result_min_date = $api->execute();
$error = $api->getError();
if (!empty($result_min_date['data'])) {
    $min_date = array_values($result_min_date['data'])[0]['from_time'];
}

$xtpl->assign('MINDATE', nv_date('d/m/Y', $min_date));

$base_url = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=customs&amp;showheader=0&amp;static_renewal=1&amp;time_end_from=' . nv_date('d/m/Y', $time_search_from) . '&amp;time_end_to=' . nv_date('d/m/Y', $time_search_to);
$base_url_next = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=customs&amp;showheader=0&amp;static_renewal=1&amp;time_end_from=' . nv_date('d/m/Y', $time_search_next_from) . '&amp;time_end_to=' . nv_date('d/m/Y', $time_search_next_to);
if ($array_search['admin_id'] > 0) {
    $base_url .= '&amp;s_admin=' . $array_search['admin_id'];
    $base_url_next .= '&amp;s_admin=' . $array_search['admin_id'];
}

$a = 1;
$static_total = [];
$static_total['number_vip'] = 0;
$static_total['isrenewal'] = 0;
$static_total['data_before'] = 0;
$static_total['total_end'] = 0;

foreach ($array_vip as $key => $vips) {
    $data = [];
    $data['stt'] = $a;
    $data['vip'] = $nv_Lang->getModule('vip' . $key);
    $data['number_vip'] = sizeof($vips);

    if (!empty($array_vip_renewal[$key])) {
        $data['isrenewal'] = sizeof($array_vip_renewal[$key]['data']);
        $data['total_end'] = number_format($array_vip_renewal[$key]['total_end']);
        $data['data_before'] = isset($array_vip_renewal[$key]['data_before']) ? sizeof($array_vip_renewal[$key]['data_before']) : 0;
        $static_total['total_end'] += $array_vip_renewal[$key]['total_end'];
    } else {
        $data['isrenewal'] = 0;
        $data['data_before'] = 0;
        $data['total_end'] = 0;
    }

   /*  if (!empty($array_vip_renewal_before[$key])) {
        $data['data_before'] = isset($array_vip_renewal_before[$key]) ? sizeof($array_vip_renewal_before[$key]) : 0;
    } */

    $data['ti_le'] = $data['isrenewal'] / $data['number_vip'] * 100;
    $data['ti_le'] = number_format($data['ti_le'], 2);

    $data['link_vip'] = $base_url . '&amp;s_vip=' . $key;
    $data['link_vip_renewal'] = $base_url . '&amp;s_vip=' . $key . '&amp;renewal=1';
    $data['link_vip_before'] = $base_url . '&amp;s_vip=' . $key . '&amp;renewal=1&amp;renewal_before=1';

    $data['link_order'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment_static&amp;showheader=0&amp;renewal=1&amp;viewall=1&amp;static_time_from=' . nv_date('d/m/Y', $time_search_from) . '&amp;static_time_to=' . nv_date('d/m/Y', $time_search_to) . '&amp;vip=' . $key;
    if ($array_search['admin_id'] > 0) {
        $data['link_order'] .= '&amp;admin_id=' . $array_search['admin_id'];
    }

    $static_total['number_vip'] += $data['number_vip'];
    $static_total['isrenewal'] += $data['isrenewal'];
    $static_total['data_before'] += $data['data_before'];

    $xtpl->assign('DATA', $data);
    $xtpl->parse('main.loop');
    $a++;
}

$static_total['ti_le'] = $static_total['number_vip'] == 0 ? 0 : ($static_total['isrenewal'] / $static_total['number_vip'] * 100);
$static_total['ti_le'] = number_format($static_total['ti_le'], 2);
$static_total['total_end'] = number_format($static_total['total_end']);
$xtpl->assign('STATIC_TOTAL', $static_total);

$a = 1;
$static_total_next = 0;
$static_total_money_next = 0;
foreach ($array_vip_next as $key => $vips) {
    $data_next = [];
    $data_next['stt'] = $a;
    $data_next['vip'] = $nv_Lang->getModule('vip' . $key);
    $data_next['number_vip'] = sizeof($vips);
    $data_next['link_vip'] = $base_url_next . '&amp;s_vip=' . $key;
    $total = 0;
    foreach ($vips as $v) {
        $total += $v['total'];
    }
    $data_next['total'] = number_format($total);
    $static_total_money_next += $total;
    $static_total_next += $data_next['number_vip'];

    $xtpl->assign('DATA_NEXT', $data_next);
    $xtpl->parse('main.loop_next_month');
    $a++;
}
$xtpl->assign('STATIC_TOTAL_NEXT', $static_total_next);
$xtpl->assign('STATIC_TOTAL_MONEY_NEXT', number_format($static_total_money_next));

// Xuất danh sách admin ra
$sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (SELECT admin_id FROM ' . NV_AUTHORS_GLOBALTABLE . ') AND active = 1';
if ($data_config['view_static'] == 2 and !defined('NV_IS_SPADMIN')) {
    if (isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 0) {
        if (!empty($arr_admin_view_tmp)) {
            $sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arr_admin_view_tmp) . ') AND active = 1';
        } else {
            $sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $admin_info['userid'] . ' AND active = 1';
        }
    }
}
$result = $db->query($sql);
while ($_user_info = $result->fetch()) {
    $xtpl->assign('OPTION', array(
        'key' => $_user_info['userid'],
        'title' => nv_show_name_user($_user_info['first_name'], $_user_info['last_name'], $_user_info['username']),
        'selected' => $array_search['admin_id'] == $_user_info['userid'] ? ' selected="selected"' : ''
    ));
    $xtpl->parse('main.loop_admin');
}

// Thống kê trung bình tuổi thọ khách hàng và gói vip
if (!empty($staticVip)) {
    foreach ($staticVip['ave_vip_year'] as $k => $v) {
        $xtpl->assign('AVE_VIP', $v);
        $xtpl->parse('main.loop_avevip');
    }

    $xtpl->assign('average_years', sprintf($nv_Lang->getModule('title__ave_cus'), round($staticVip['average_years'], 2)));
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
