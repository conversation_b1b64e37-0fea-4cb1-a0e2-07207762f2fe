<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 * API cập nhật ngày gửi mail thông báo điểm sắp hết hạn, mỗi mail cách nhau 30 ngày
 * https://vinades.org/dauthau/dauthau.info/-/issues/2358
 */

namespace NukeViet\Module\points\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

use Exception;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdateSendMailPointExpired implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'point';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        try {
            $data_json = html_entity_decode($nv_Request->get_title('data', 'post', ''));
            $data = json_decode($data_json . PHP_EOL, true);
            if (!empty($data)) {
                foreach ($data as $item) {
                    $sqls[] = 'UPDATE ' . $db_config['prefix'] . '_' . $module_data . '_customs SET nexttime_sendmail = ' . $item['nexttime_sendmail'] . ' WHERE userid = ' . $item['userid'];
                }
                if (!empty($sqls)) {
                    $ext = $db->exec(implode('; ', $sqls));
                }
                if ($ext) {
                    $this->result->setMessage('Cập nhật trạng thái thành công!');
                    $this->result->setSuccess();
                    return $this->result->getResult();
                } else {
                    $this->result->setMessage('Đã có lỗi trong quá trình xử lý');
                    $this->result->setCode('0001');
                    $this->result->setError();
                }
            }
        } catch (Exception $e) {
            trigger_error($e);
            $this->result->setMessage(print_r($e, true));
            $this->result->setError();
        }
        return $this->result->getResult();
    }
}
