<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css">
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">

<div class="row">
    <div class="col-md-16">
        <div class="panel panel-default">
            <div class="panel-body">
                <form class="detail form-horizontal" action="{FORM_ACTION}" method="post" id="detail_form_action">
                    <input type="hidden" name="ticket-id" id="ticket_id" value="{ROW.id}">
                    <div class="row">
                        <h2 class="text-info col-md-16">
                            <strong class="text-primary ticket_title">{ROW.title}</strong>
                            <input id="ticket_title" class="form-control hidden" type="text" name="ticket-title" value="{ROW.title}" />
                        </h2>
                        <div class="col-md-8 text-right">
                            <!-- BEGIN: can_edit_detail -->
                            <button type="button" class="btn editbtn btn-primary" data-toggle="tooltip" title="{GLANG.edit}">
                                <i class="fa fa-pencil" aria-hidden="true"></i>
                            </button>
                            <button type="submit" name="submit" class="btn savebtn btn-primary hidden" value="1">
                                <i class="fa fa-floppy-o" aria-hidden="true"></i>
                            </button>
                            <button type="button" class="btn deletebtn btn-danger hidden">
                                <i class="fa fa-times" aria-hidden="true"></i>
                            </button>
                            <!-- END: can_edit_detail -->
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-md-24">
                            <div class="ticket_content">{ROW.content}</div>
                            <textarea name="ticket-content" id="ticket_content" class="form-control hidden txt" data-editor="{CONTENT}" rows="7"></textarea>
                            <!-- BEGIN: detail_attach -->
                            </br>
                            <em>{LANG.ticket_attach}</em>
                            <div class="container ticket-images">
                                <div class="row">
                                    <!-- BEGIN: loop_image -->
                                    <div class="col-xs-12 col-sm-8 col-md-6">
                                        <a href="{IMAGE.src}" class="fancybox image" data-fancybox="content">
                                            <div class="image-container">
                                                <img src="{IMAGE.src}">
                                            </div>
                                        </a>
                                    </div>
                                    <!-- END: loop_image -->
                                </div>
                                <div class="row">
                                    <ul class="file-container">
                                        <!-- BEGIN: loop_file -->
                                            <li class="file-item"><a href="{FILE.src}" target="_blank">{FILE.name}</a></li>
                                        <!-- END: loop_file -->
                                    </ul>
                                </div>
                            </div>
                            <!-- END: detail_attach -->
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <!-- BEGIN: link_error -->
        <div class="panel panel-default">
            <div class="panel-body">
                <h3 class="text-info">{LANG.error_link}</h3>
                <hr>
                <a href="{LINK_ERROR}" target="_blank">{LINK_ERROR}</a>
            </div>
        </div>
        <!-- END: link_error -->
        <!-- BEGIN: comments -->
            <!-- BEGIN: loop -->
            <div class="panel panel-default">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-10">
                            <h2 class="text-info mb-0">{CMT.display_fullname}</h2>
                            <!-- BEGIN: reply_user -->
                            <small>{LANG.comment_reply_user}: {REPLY_USER}</small>
                            <!-- END: reply_user -->
                        </div>
                        <div class="col-md-14">
                            <div class="text-right"><strong>{CMT.display_position}</strong> - <em class="add-time">{CMT.add_time}</em></div>
                        </div>
                    </div>
                    <hr>
                    <div class="col-md-24">
                        <div class="comment-content">
                            <div class="content comment-content">{CMT.content} {CMT.edited}</div>
                            <!-- BEGIN: edit_comment -->
                            <div class="text-right">
                                <i class="fa fa-edit fa-lg">&nbsp;</i> <a href="{EDIT_COMMENT}">{LANG.edit}</a>
                            </div>
                            <!-- END: edit_comment -->
                            <!-- BEGIN: detail_attach -->
                            </br>
                            <em>{LANG.ticket_attach}</em>
                            <div class="ticket-images">
                                <div class="row">
                                    <!-- BEGIN: loop_image -->
                                    <div class="col-xs-12 col-sm-8 col-md-6">
                                        <a href="{IMAGE_CMT.src}" class="fancybox image" data-fancybox="comment-{IMAGE_CMT.comment_id}">
                                            <div class="image-container">
                                                <img src="{IMAGE_CMT.src}">
                                            </div>
                                        </a>
                                    </div>
                                    <!-- END: loop_image -->
                                </div>
                                <div class="row">
                                    <ul class="file-container">
                                        <!-- BEGIN: loop_file -->
                                            <li class="file-item"><a href="{FILE_CMT.src}" target="_blank">{FILE_CMT.name}</a></li>
                                        <!-- END: loop_file -->
                                    </ul>
                                </div>
                            </div>
                            <!-- END: detail_attach -->
                        </div>
                    </div>
                </div>
                <!-- BEGIN: signature -->
                <div class="panel-separator"></div>
                <div class="panel-footer">
                    {SIGNATURE}
                </div>
                <!-- END: signature -->
            </div>
            <!-- END: loop -->
            <!-- BEGIN: generate_page -->
            <div class="text-center">{NV_GENERATE_PAGE}</div>
            <!-- END: generate_page -->
        <!-- END: comments -->
        <!-- BEGIN: can_reply -->
        <div class="panel panel-default">
            <div class="panel-body">
                <h2 class="text-info">{LANG.comment}</h2>
                <hr>
                <div class="form-group col-md-24">
                    <div class="panel-body">
                        <!-- BEGIN: error -->
                        <div class="alert alert-danger">{ERROR}</div>
                        <!-- END: error -->
                        <form id="commentForm" class="form-horizontal" action="{FORM_ACTION}" method="post" enctype="multipart/form-data">
                            <input type="hidden" name="commentid" value="{EDIT_COMMENT}">
                            <div class="form-group">
                                <label class="col-md-24"><strong>{LANG.comment_user}</strong></label>
                                <div class="col-md-24">
                                    <select class="form-control" name="comment_user">
                                        <option value="0">{LANG.comment_user_select}</option>
                                        <!-- BEGIN: comment_user -->
                                            <option value="{OPTION.key}" {OPTION.selected}>{OPTION.title}</option>
                                        <!-- END: comment_user -->
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-24"><strong>{LANG.comment_content} <span class="text-danger">(*)</span></strong></label>
                                <div class="col-md-24">
                                    {COMMENT}
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-md-24"><strong>{LANG.ticket_attach}</strong></label>
                                <div class="col-md-24">
                                    <button type="button" class="btn-sm btn btn-success" data-toggle="rowElementAdd">
                                        <i class="fa fa-plus-circle" aria-hidden="true"></i> {LANG.ticket_attach_add}
                                    </button>
                                    <span class="help-block">{LANG.allowed_file_extensions} {LIMITED_FILE_SIZE}</span>
                                    <table class="table table-striped table-bordered table-hover">
                                        <tbody id="element-rows-container">
                                            <!-- BEGIN: files -->
                                            <tr data-toggle="rowElement" data-offset="{FILE.key}">
                                                <td>
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" id="tickect_file_{FILE.key}" name="file_attach[]" value="{FILE.value}"/>
                                                        <span class="input-group-btn">
                                                            <button class="btn btn-default" type="button" onclick="nv_select_image('tickect_file_{FILE.key}', '{UPLOADS_DIR_USER}');"><i class="fa fa-file-image-o"></i></button>
                                                        </span>
                                                    </div>
                                                </td>
                                                <th style="width: 30px">
                                                    <div class="input-group-btn">
                                                        <button class="btn btn-danger" data-toggle="rowElementDel" data-offset="{FILE.key}"><i class="fa fa-trash"></i></button>
                                                    </div>
                                                </th>
                                            </tr>
                                            <!-- END: files -->
                                        <tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="form-group" style="text-align: center">
                                <input class="btn btn-primary" name="commentSubmit" type="submit" value="{LANG.send}" />
                                <!-- BEGIN: cancel -->
                                <a class="btn btn-danger" href="{FORM_ACTION}">{LANG.comment_cancel}</a>
                                <!-- END: cancel -->
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- END: can_reply -->
    </div>
    <div class="col-md-8">
        <!-- BEGIN: handle_bonus_point -->
        <div class="panel panel-default">
            <div class="panel-body" id="info">
                <div class="col-md-24">
                    <h2 class="text-info">{LANG.handle_add_point}</h2>
                    <hr>
                    <div class="table-responsive">
                        <!-- BEGIN: points_added -->
                        <p>{POINT_ADDED}</p>
                        <!-- END: points_added -->
                        <!-- BEGIN: action_plus -->
                        <form class="form-horizontal" action="{FORM_ACTION}" method="post" id="formBonusPointToCustomer">
                            <input hidden name="cat_id" value="{ROW.cat_id}">
                            <input hidden name="bonus_token" value="{TOKEN}">
                            <input hidden name="plus_points_customer" value="1">
                            <input type="hidden" id="action_type" name="action_type" value="">
                            <div class="form-group">
                                <div class="notify">
                                    <p>{LANG.points_added} <span class="number_expert">
                                        <input type="number" name="bonus_point" min="0" value="{ROW_CAT.bonus_point}" id="bonus_point"></span> {LANG.point}
                                    </p>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="notify">
                                    <p>{LANG.expired_day} <span class="number_expert">
                                        <input type="number" name="expired" min="0" value="365"></span>
                                    </p>
                                </div>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary" id="plusPointsCustomer">
                                    <i class="fa fa-paper-plane" aria-hidden="true"></i> {LANG.handle}
                                </button>
                                <button type="submit" class="btn btn-danger" id="denyPointsCustomer">
                                    <i class="fa fa-paper-plane" aria-hidden="true"></i> {LANG.deny}
                                </button>
                            </div>
                        </form>
                        <!-- END: action_plus -->
                    </div>
                </div>
            </div>
        </div>
        <!-- END: handle_bonus_point -->
        <div class="panel panel-default">
            <div class="panel-body" id="info">
                <div class="col-md-24">
                    <div class="row mb-0">
                        <h2 class="text-info col-md-8">{LANG.ticket_info}</h2>
                        <div class="col-md-16 text-right">
                            <button type="button" class="btn edit-info-btn btn-primary" data-toggle="tooltip" title="{LANG.update}">
                                <i class="fa fa-pencil" aria-hidden="true"></i>
                            </button>
                            <button type="button" class="btn cancel-info-btn btn-danger hidden" data-toggle="tooltip" title="{LANG.update_cancel}">
                                <i class="fa fa-times" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                    <hr>
                    <div class="table-responsive">
                        <form class="form-horizontal option" action="{FORM_ACTION}" method="post">
                            <input type="hidden" name="ticket_id" value="{ROW.id}" />
                            <div class="form-group">
                                <label class="col-md-24"><strong>{LANG.customer_id}</strong></label>
                                <div class="col-md-24"><a class="view_users" href="javascript:;"><i class="fa fa-user" aria-hidden="true"></i> {CUSTOMER.fullname}</a></div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-24"><strong>{LANG.add_userid}</strong></label>
                                <div class="col-md-24"><a class="view_add_user" href="javascript:;"><i class="fa fa-user" aria-hidden="true"></i> {ADD_USER.fullname}</a></div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-24"><strong>{LANG.add_time}</strong></label>
                                <div class="col-md-24"><i class="fa fa-clock-o" aria-hidden="true"></i> {ROW.add_time}</div>
                            </div>
                            <!-- BEGIN: show_edit_time -->
                            <div class="form-group">
                                <label class="col-md-24"><strong>{LANG.edit_time}</strong></label>
                                <div class="col-md-24"><i class="fa fa-clock-o" aria-hidden="true"></i> {EDIT_TIME}</div>
                            </div>
                            <!-- END: show_edit_time -->

                            <div class="detail-info">
                                <!-- BEGIN: detail_cat -->
                                <div class="form-group">
                                    <label class="col-md-24"><strong>{LANG.cat_id}</strong></label>
                                    <div class="col-md-24">{DETAIL_CAT.title}</div>
                                </div>
                                <!-- END: detail_cat -->
                                <!-- BEGIN: detail_vip -->
                                <div class="form-group">
                                    <label class="col-md-24"><strong>{LANG.vip_id}</strong></label>
                                    <div class="col-md-24"><a target="_blank" href="{DETAIL_VIP.link}">{DETAIL_VIP.title}</a></div>
                                </div>
                                <!-- END: detail_vip -->
                                <!-- BEGIN: detail_order -->
                                <div class="form-group">
                                    <label class="col-md-24"><strong>{LANG.order_id}</strong></label>
                                    <div class="col-md-24"><a target="_blank" href="{DETAIL_ORDER.link}">{DETAIL_ORDER.title}</a></div>
                                </div>
                                <!-- END: detail_order -->
                                <!-- BEGIN: detail_lead -->
                                <div class="form-group">
                                    <label class="col-md-24"><strong>{DETAIL_LEAD.label}</strong></label>
                                    <div class="col-md-24"><a target="_blank" href="{DETAIL_LEAD.link}">{DETAIL_LEAD.name}</a></div>
                                </div>
                                <!-- END: detail_lead -->
                                <!-- BEGIN: detail_label -->
                                <div class="form-group">
                                    <label class="col-md-24"><strong>{LANG.label_ids} <span class="text-danger"></span></strong></label>
                                    <div class="col-md-24">
                                        <!-- BEGIN: loop -->
                                            <span style="background-color: {DETAIL_LABEL.color}" class="btn">{DETAIL_LABEL.title}</span>
                                        <!-- END: loop -->
                                    </div>
                                </div>
                                <!-- END: detail_label -->
                                <!-- BEGIN: detail_assignee -->
                                <div class="form-group">
                                    <label class="col-md-24"><strong>{LANG.assignee_to} <span class="text-danger"></span></strong></label>
                                    <div class="col-md-24">{DETAIL_ASSIGNEE}</div>
                                </div>
                                <!-- END: detail_assignee -->
                                <!-- BEGIN: detail_status -->
                                <div class="form-group">
                                    <label class="col-md-24"><strong>{LANG.status}</strong></label>
                                    <div class="col-md-24">{DETAIL_STATUS}</div>
                                </div>
                                <!-- END: detail_status -->
                            </div>

                            <!-- BEGIN: can_edit_info -->
                            <div class="edit-info hidden">
                                <!-- BEGIN: select_cat_id -->
                                <div class="form-group">
                                    <label class="col-md-24"><strong>{LANG.cat_id} <span class="text-danger">(*)</span></strong></label>
                                    <div class="col-md-24">
                                        <select class="form-control" name="cat_id">
                                            <option value=""> --- </option>
                                            <!-- BEGIN: loop -->
                                            <option value="{OPTION.key}" {OPTION.selected}>{OPTION.title}</option>
                                            <!-- END: loop -->
                                        </select>
                                    </div>
                                </div>
                                <!-- END: select_cat_id -->
                                <!-- BEGIN: selected_cat_id -->
                                <div class="form-group">
                                    <label class="col-md-24"><strong>{LANG.cat_id}</strong></label>
                                    <div class="col-md-24">{CAT_NAME}</div>
                                </div>
                                <!-- END: selected_cat_id -->

                                <div class="form-group">
                                    <label class="col-md-24"><strong>{LANG.vip_id}</strong></label>
                                    <div class="col-md-24">
                                        <select class="form-control" name="vip_id">
                                            <!-- BEGIN: select_vip -->
                                            <option value="{OPTION.key}" {OPTION.selected}>{OPTION.title}</option>
                                            <!-- END: select_vip -->
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-24"><strong>{LANG.order_id}</strong></label>
                                    <div class="col-md-24">
                                        <select class="form-control" name="order_id">
                                            <!-- BEGIN: select_order -->
                                            <option value="{OPTION.key}" {OPTION.selected}>{OPTION.title}</option>
                                            <!-- END: select_order -->
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-24"><strong>{LANG.label_ids} <span class="text-danger"></span></strong></label>
                                    <div class="col-md-24">
                                        <select class="form-control" name="label_ids[]" multiple="multiple">
                                            <option value=""> --- </option>
                                            <!-- BEGIN: select_label_ids -->
                                            <option value="{OPTION.key}" {OPTION.selected}>{OPTION.title}</option>
                                            <!-- END: select_label_ids -->
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-24"><strong>{LANG.assignee_to} <span class="text-danger"></span></strong></label>
                                    <div class="col-md-24">
                                        <select class="form-control" name="assignee_to[]" multiple="multiple">
                                            <!-- BEGIN: select_assignee -->
                                                <option value="{OPTION.key}" {OPTION.selected}>{OPTION.title}</option>
                                            <!-- END: select_assignee -->
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-24"><strong>{LANG.status}</strong></label>
                                    <div class="col-md-24">
                                        <select class="form-control" name="status">
                                            <!-- BEGIN: select_status -->
                                            <option value="{OPTION.key}" {OPTION.selected}>{OPTION.title}</option>
                                            <!-- END: select_status -->
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group" style="text-align: center">
                                    <input class="btn btn-primary" name="submit" type="submit" value="{LANG.save}" />
                                </div>
                            </div>
                            <!-- END: can_edit_info -->
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- Xử lý chat nội bộ -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">{LANG.chat_internal}</h3>
            </div>
            <div class="panel-body">
                <div id="chat-messages" class="chat-messages">
                </div>

                <form id="chat-form" class="chat-form" enctype="multipart/form-data">
                    <input type="hidden" name="ticket_id" value="{ROW.id}" />
                    <div class="form-group">
                        <textarea name="content" class="form-control" required></textarea>
                    </div>
                    <div class="form-group">
                        <input type="file" name="file" class="form-control" />
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-paper-plane"></i> {LANG.send}
                    </button>
                </form>
            </div>
        </div>

        <!-- BEGIN: detail_lead_info -->
        <div class="panel panel-info panel__custom">
            <div class="panel-heading">
                <h3 class="panel-title btn__downleft showleadsduplicate">
                    {LANG.customer_data} <i class="fa fa-angle-double-right icon" aria-hidden="true"></i>
                </h3>
            </div>
            <div class="panel-body" id="showleadsduplicate" style="display:none">
                <div class="form-group text-center loading hidden">
                    <i class="fa fa-spinner fa-spin" aria-hidden="true"></i>
                </div>
                <div class="form-group content_duplicate"></div>
            </div>
        </div>
        <!-- END: detail_lead_info -->
        <div class="panel panel-default">
            <div class="panel-body" id="logs">
                <div class="col-md-24 col-sm-24">
                    <h2 class="text-info">{LANG.update_history}</h2>
                    <hr>
                    <div class="table-responsive log_history">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th class="text-nowrap w100">{LANG.log_time}</th>
                                    <th class="text-nowrap">{LANG.log_user}</th>
                                    <th class="text-nowrap">{LANG.log_data}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- BEGIN: alllog -->
                                <tr>
                                    <td>{LOG.log_time}</td>
                                    <td>{LOG.user}</td>
                                    <td>
                                        <!-- BEGIN: data -->
                                        <div>
                                            <!-- BEGIN: sarray --> {LOG_DATA_SHOW.0} <strong>{LOG_DATA_SHOW.1}</strong> <!-- END: sarray -->
                                            <!-- BEGIN: sstring --> {LOG_DATA_SHOW} <!-- END: sstring -->
                                            <!-- BEGIN: other1 --> &nbsp; <a data-toggle="collapse" href="#logOther{LOG.id}" aria-expanded="false" aria-controls="logOther{LOG.id}"><i class="fa fa-ellipsis-h" aria-hidden="true"></i></a> <!-- END: other1 -->
                                        </div>
                                        <!-- BEGIN: other -->
                                        <div class="collapse" id="logOther{LOG.id}">
                                            <ul class="logotherlists">
                                                <!-- BEGIN: loop -->
                                                <li>
                                                    <!-- BEGIN: sarray --> {LOG_DATA_OTHER.0} <strong>{LOG_DATA_OTHER.1}</strong> <!-- END: sarray -->
                                                    <!-- BEGIN: sstring --> {LOG_DATA_OTHER} <!-- END: sstring -->
                                                </li>
                                                <!-- END: loop -->
                                            </ul>
                                        </div>
                                        <!-- END: other -->
                                        <!-- END: data -->
                                    </td>
                                </tr>
                                <!-- END: alllog -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- BEGIN: related -->
        <div class="panel panel-default">
            <div class="panel-body" id="related">
                <div class="col-md-24">
                    <h2 class="text-info">{LANG.ticket_related}</h2>
                    <hr>
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th class="w50">{LANG.number}</th>
                                    <th>{LANG.ticket_title}</th>
                                    <th>{LANG.status}</th>
                                    <th>{LANG.add_time}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- BEGIN: loop -->
                                <tr>
                                    <td> {RELATED.number} </td>
                                    <td> <a href="{RELATED.link_detail}">{RELATED.title}</a> </td>
                                    <td> {RELATED.status} </td>
                                    <td> {RELATED.add_time} </td>
                                </tr>
                                <!-- END: loop -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- END: related -->
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function() {
        var row_count = {TOTAL_FILE};
        $(document).delegate('[data-toggle="rowElementDel"]', 'click', function(e) {
            e.preventDefault();
            var offset = $(this).data('offset');
            $('[data-toggle="rowElement"][data-offset="' + offset + '"]').remove();
        });
        $('[data-toggle="rowElementAdd"]').on('click', function(e) {
            e.preventDefault();
            var item_count = $('[data-toggle="rowElement"]').length;
            if (item_count >= {ATTACH_LIMITED}) {
                alert('{ATTACH_LIMITED_MESSAGE}');
            } else {
                var html = '\
                <tr data-toggle="rowElement" data-offset="' + row_count + '">\
                    <td>\
                        <div class="input-group">\
                            <input type="text" class="form-control" id="tickect_file_' + row_count + '" name="file_attach[]"/>\
                            <span class="input-group-btn">\
                                <button class="btn btn-default" type="button" onclick="nv_select_image(\'tickect_file_' + row_count + '\', \'{UPLOADS_DIR_USER}\');"><i class="fa fa-file-image-o"></i></button>\
                            </span>\
                        </div>\
                    </td>\
                    <th style="width: 30px">\
                        <div class="input-group-btn">\
                            <button class="btn btn-danger" data-toggle="rowElementDel" data-offset="' + row_count + '"><i class="fa fa-trash"></i></button>\
                        </div>\
                    </th>\
                </tr>';
                $('#element-rows-container').append(html);
                row_count++;
            }
        });
    });
    // issue 2301 disable button xác nhận ở update point
    var isFormSubmitted = false;
    $('#formUpdatePoint').on('submit', function(e) {
        if (isFormSubmitted) {
            e.preventDefault();
        } else {
            isFormSubmitted = true;
            var btn = document.getElementById("btnUpdatePoint");
            btn.disabled = true;
            setTimeout(function() {
                btn.disabled = false;
                isFormSubmitted = false;
            }, 5000);
        }
    });
</script>
<script type="text/javascript">
    $(document).ready(function() {
        function viewUser(userId) {
        $.post(script_name + '?' + nv_name_variable + '=supportticket&'
            + nv_fc_variable + '=ticket_detail&nocache='
            + new Date().getTime(), 'user_id=' + userId + '&view_users=1',
            function(res) {
                $('.modal-body').empty();
                //modalShow('{LANG.ticket_info_user}', res);
                modalShow('{LANG.ticket_info_user}', res, () => {}, () => {
                    location.reload();
                });
            });
        }
        $('.view_users').click(function() {
            viewUser('{ROW.customer_id}');
            return;
        });
        $('.view_add_user').click(function() {
            viewUser('{ROW.add_userid}');
            return;
        });
        $("[data-fancybox]").fancybox({});

        // Xử lý cộng điểm khi khách báo lỗi/Đóng góp ý tưởng
        $('#plusPointsCustomer').on('click', function() {
            $('#action_type').val(1);
        });
        $('#denyPointsCustomer').on('click', function() {
            $('#action_type').val(0);
        });

        $('#formBonusPointToCustomer').on('submit', function(e) {
            e.preventDefault();

            var submitPlusPoint = $('#plusPointsCustomer');
            var submitDenyPoint = $('#denyPointsCustomer');
            var bonusPoint = $('#bonus_point').val();
            var actionType = $('#action_type').val();

            if (actionType === 1 && bonusPoint < 0) {
                alert('{LANG.error_point_quote}');
                return;
            }

            submitPlusPoint.prop('disabled', true);
            submitDenyPoint.prop('disabled', true);

            var formData = $(this).serialize();
            var actionUrl = $('#detail_form_action').attr('action');
            $.ajax({
                url: actionUrl,
                method: 'POST',
                data: formData,
                success: function(response) {
                    alert(response.message);
                    if (response.success) {
                        location.reload();
                    } else {
                        submitPlusPoint.prop('disabled', false);
                        submitDenyPoint.prop('disabled', false);
                    }
                },
                error: function(xhr, status, error) {
                    alert(response.message);
                    submitPlusPoint.prop('disabled', false);
                    submitDenyPoint.prop('disabled', false);
                }
            });
        });

        // Xử lý phần chat nội bộ
        const chatMessages = $('#chat-messages');
        const chatForm = $('#chat-form');
        const ticketId = '{ROW.id}';
        const currentAdminId = $('#current_admin_id').val();
        const linkTicket = window.location.href;

        // Khởi tạo chat
        initChat({
            messages: chatMessages,
            form: chatForm,
            ticketId: ticketId,
            currentAdminId: currentAdminId,
            linkTicket: linkTicket
        });
    });

    $("select[name='comment_user']").select2({
        placeholder: "{LANG.comment_user_select}"
    });
    $("select[name='assignee_to[]']").select2({
        placeholder: "{LANG.assignee_to}"
    });
    $("select[name='label_ids[]']").select2({
        placeholder: "{LANG.label_ids}"
    });

</script>
<!-- BEGIN: detail_lead_script -->
<script>
    var is_show_duplicate = 1;
    $('.showleadsduplicate').click(function() {
        if ($('#info .content_duplicate').hasClass('in')) {
            $('#info .content_duplicate').removeClass('in');
            $('.fa-angle-double-down').removeClass('hidden');
            $('.fa-angle-double-up').addClass('hidden');
            if (is_show_duplicate == 1) {
                $('.content_duplicate').html('');
            } else {
                $('.content_duplicate').addClass('hidden');
            }
        } else {
            $('#info .content_duplicate').addClass('in');
            $('.fa-angle-double-down').addClass('hidden');
            $('.fa-angle-double-up').removeClass('hidden');
            if (is_show_duplicate == 1) {
                $('.loading').removeClass('hidden');
                checkduplicate(1);
                is_show_duplicate  = 0;
            } else {
                $('.content_duplicate').removeClass('hidden');
            }
        }
        return;
    });

    function checkduplicate(next) {
        $.post(script_name + '?' + nv_name_variable + '=crmbidding&' + nv_fc_variable + '=check_duplicate&nocache=' + new Date().getTime(), 'id={DETAIL_LEAD.id}&type={DETAIL_LEAD.type}&next=' + next, function(res) {
            $('.content_duplicate').append(res.data);
            if (res.next == 5) {
                $('.loading').addClass('hidden');
            } else {
                checkduplicate(res.next);
            }
        });
    }
</script>
<!-- END: detail_lead_script -->
<!-- END: main -->
