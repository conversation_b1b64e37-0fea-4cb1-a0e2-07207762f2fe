<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<div class="panel">
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}"> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}"> <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}">
        <div class="form-group">
            <label>{LANG.time_from}:</label> <input class="form-control w100 uidatepicker" type="text" value="{ARRAY_SEARCH.time_from}" name="time_from" maxlength="10" autocomplete="off">
        </div>
        <div class="form-group">
            <label>{LANG.to}:</label> <input class="form-control w100 uidatepicker" type="text" value="{ARRAY_SEARCH.time_to}" name="time_to" maxlength="10" autocomplete="off">
        </div>
        <div class="form-group">
            <select class="form-control" name="admin_id">
                <option value="-1">{LANG.admin_id_all}</option>
                <!-- <option value="0"{marketing_selected}>{LANG.marketing}</option> -->
                <!-- BEGIN: loop_admin -->
                <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                <!-- END: loop_admin -->
            </select>
        </div>
        <div class="form-group">
            <label>{LANG.static_type}:</label> <select class="form-control" name="type">
                <option value="1"{TYPE1}>{LANG.static_type1}</option>
                <option value="2"{TYPE2}>{LANG.static_type2}</option>
            </select>
        </div>
        <div class="form-group">
            <label>{LANG.static_method}:</label> <select class="form-control" name="method">
                <option value="1"{METHOD1}>{LANG.static_method1}</option>
                <option value="2"{METHOD2}>{LANG.static_method2}</option>
            </select>
        </div>
        <div class="form-group">
            <input class="btn btn-primary" type="submit" value="{LANG.search_submit}">
        </div>
    </form>
</div>
<script type="text/javascript">
    $('.view_leads').click(function() {
        var href = $(this).attr('data-href');
        nv_open_browse(href, 'NVImg', 1200, 600, 'resizable=no,scrollbars=no,toolbar=no,location=no,status=no');
        return;
    });
    $(document).ready(function() {
        $("select[name='admin_id']").select2();
        $('.uidatepicker').datepicker({ showOn : "both", dateFormat : "dd/mm/yy", changeMonth : true, changeYear : true, showOtherMonths : true, buttonImage : nv_base_siteurl + "assets/images/calendar.gif", buttonImageOnly : true });
    });
</script>

<div class="panel panel-default">
    <div class="table-responsive">
        <table class="table table-bordered table-hover text-center">
            <thead>
                <tr>
                    <th class="w50 text-center">{LANG.stt}</th>
                    <th class="w100 text-center">{LANG.kenh}</th>
                    <th class="w100 text-center">{LANG.leads_title}</th>
                    <th class="w100 text-center">{LANG.oppotunities}</th>
                    <th class="w100 text-center">{LANG.payment}</th>
                    <th class="w100 text-center">{LANG.payment_success}</th>
                    <th class="text-center">{LANG.ti_le_chuyen_doi_co_hoi} (%)</th>
                    <th class="text-center">{LANG.ti_le_chuyen_doi_don_hang} (%)</th>
                    <th class="text-center">{LANG.ti_le_chuyen_doi_don_vip} (%)</th>
                    <th class="w100 text-center">{LANG.doanh_so}</th>
                    <th class="text-center">{LANG.discount}</th>
                    <th class="text-center">{LANG.doanh_so_sau_giam}</th>
                    <th class="text-center">{LANG.chiet_khau}</th>
                    <th class="text-center">{LANG.doanh_so_thuc}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop_groupsleads -->
                <tr>
                    <td>{GROUPS_LEADS.stt}</td>
                    <td>{GROUPS_LEADS.title}</td>
                    <td><a href="#" class="view_leads" data-href="{GROUPS_LEADS.link_leads}"> {GROUPS_LEADS.num_leads}</a> <br /> {GROUPS_LEADS.leads_prev}</td>
                    <td><a href="#" class="view_leads" data-href="{GROUPS_LEADS.link_opportunities}"> {GROUPS_LEADS.num_opportunities}</a><br /> {GROUPS_LEADS.oppo_prev}</td>
                    <td><a href="#" class="view_leads" data-href="{GROUPS_LEADS.link_order}"> {GROUPS_LEADS.num_order}</a><br /> {GROUPS_LEADS.order_prev}</td>
                    <td><a href="#" class="view_leads" data-href="{GROUPS_LEADS.link_vip}"> {GROUPS_LEADS.num_vip}</a></td>
                    <td>{GROUPS_LEADS.rate_leads}</td>
                    <td>{GROUPS_LEADS.rate_order}</td>
                    <td>{GROUPS_LEADS.rate_vip}</td>
                    <td>{GROUPS_LEADS.money}</td>
                    <td>{GROUPS_LEADS.discount}</td>
                    <td>{GROUPS_LEADS.total}</td>
                    <td>{GROUPS_LEADS.price_reduce}</td>
                    <td>{GROUPS_LEADS.total_end}</td>
                </tr>
                <!-- END: loop_groupsleads -->
            </tbody>
            <tfoot>
                <tr class="red">
                    <th colspan=2 class="text-center">{LANG.total}:</th>
                    <th class="text-center">{STATIC_GROUPS.leads}</th>
                    <th class="text-center">{STATIC_GROUPS.opportunities}</th>
                    <th class="text-center">{STATIC_GROUPS.order}</th>
                    <th class="text-center">{STATIC_GROUPS.vip}</th>
                    <th class="text-center">{STATIC_GROUPS.rate_leads}</th>
                    <th class="text-center">{STATIC_GROUPS.rate_order}</th>
                    <th class="text-center">{STATIC_GROUPS.rate_vip}</th>
                    <th class="text-center">{STATIC_GROUPS.money}</th>
                    <th class="text-center">{STATIC_GROUPS.discount}</th>
                    <th class="text-center">{STATIC_GROUPS.total}</th>
                    <th class="text-center">{STATIC_GROUPS.price_reduce}</th>
                    <th class="text-center">{STATIC_GROUPS.total_end}</th>
                </tr>
            </tfoot>
        </table>
    </div>
</div>
<div class="col-md-24 col-sm-24">
    <p class="text-danger">
        <b>Ghi chú:</b>
    </p>
    <ul>
        <li><b>Xuôi</b>: Tính theo các giá trị dc tạo trong thời gian xem.</li>
        <li><b>Ngược</b>: Bao gồm: các giá trị trong thời gian xem + các giá trị trước đó tạo ra</li>
        <li><b>Chu kì</b>: Tính từ giai đoạn đầu tiên là lead -> đơn thanh toán. Đối với nguồn khách tự đăng ký thì từ oppotunities</li>
        <li><b>Phân đoạn</b>: Tính theo từng giai đoạn chuyển đổi.</li>
    </ul>
</div>
<!-- END: main -->
