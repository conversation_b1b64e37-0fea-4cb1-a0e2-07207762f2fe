<!-- BEGIN: main -->
<table class="table table-bordered" id="table2">
    <caption>{LANG.service_dtnet}</caption>
    <thead>
        <tr>
            <th class="text-center" width="5%">{LANG.stt}</th>
            <th width="13%">{LANG.box_vip}</th>
            <th width="18%">{LANG.name_time_from}</th>
            <th width="18%">{LANG.time_noteactive}</th>
            <th class="text-center" width="12%">{LANG.status}</th>
            <th>{LANG.name_caregiver_id}</th>
            <th class="text-center"> <a class="show_detail_vip">{LANG.showall} <i class="fa fa-angle-double-right" aria-hidden="true"></i></a></th>
        </tr>
    </thead>
    <tbody>
        <!-- BEGIN: loop -->
        <tr class="dtnet_vip">
            <td scope="row" data-label="{LANG.stt}" class="text-center">{VIP.stt}</td>
            <td data-label="{LANG.box_vip}">
                <a href="{VIP.link_vip}" data-toggle="tooltip" title="Thông tin gói {VIP.vip_title}"><span class="text--weight info_order__vip span__data">{VIP.vip_title}</span></a>
            </td>
            <td data-label="{LANG.from_time}"> <span>{VIP.from_time}</span></td>
            <td data-label="{LANG.time_noteactive}"> <span>{VIP.end_time}</span></td>
            <td class="text-center" data-label="{LANG.status}"> 
                <span class="{VIP.class_status} label_responsive">{VIP.status}</span>
            </td>
            <td>{VIP.name_admin_id}</td>
            <td class="text-center">
                <p>
                    <a href="#" class="viewmore_vip">{LANG.viewmore} <i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
                </p>
                <div class="view_ordervip text-left" style="display: none;">
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.name_deal}:</span> <span class=" text--weight span__data">{VIP.deal_price}</span>
                    </p>
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.name_sum_viptime}:</span> <span class="text--weight span__data">{VIP.sum_viptime}</span>
                    </p>
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.tax}:</span> <span class="info_order__tax text--weight span__data">{VIP.tax}</span>
                    </p>
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.contact_to}: </span> <span class="info_order__contact-to text--weight span__data">{VIP.contact_to}</span>
                    </p>
                    

                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.deal_time}:</span> <span class="text--weight span__data">{VIP.deal_time}</span>
                    </p>
                    
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.phone_tin}:</span> <span class="info_order__phone span__data"><a href="tel:{VIP.phone} text--weight">{VIP.phone}</a></span>
                    </p>
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.contact_phone}:</span> <span class="info_order__contact_phone text--weight span__data"><a href="tel:{VIP.contact_phone}">{VIP.contact_phone}</a></span>
                    </p>
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.email_tin}: </span> <span class="info_order__email text--weight span__data"> <b>{VIP.email}</b></span>
                    </p>
                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span class="info_order__sub_email">{LANG.sub_email}: </span> <span class="text--weight span__data">{VIP.sub_email}</span>
                    </p>

                    <p>
                        <i class="fa fa-caret-right" aria-hidden="true"></i>
                        <span>{LANG.address_org}: </span> <span class="info_order__address-org span__data">{VIP.address_org}</span>
                    </p>

                        <a href="{VIP.link_vip}" class="btn btn-primary btn--social btn-xs" target="_blank"><i class="fa fa-eye" aria-hidden="true"></i> {LANG.detail_vip}</a>
                </div>
            </td>
        </tr>
        <!-- END: loop -->
    </tbody>
</table>                  
<!-- BEGIN: generate_page -->
<div class="text-center pagination" id="vip_pagination">{NV_GENERATE_PAGE}</div>
<!-- END: generate_page -->

<!-- BEGIN: generate_page_js -->
<script type="text/javascript">
	$(document).ready(function($) {
        var listVip_page_selector = '#vip_pagination';
        //phân trang API ListVip 
        $(listVip_page_selector + ' .page-item').not('.active, .disabled').on('click', function (e) {
            e.preventDefault();
            var $this = $(this);
		    ajax_API_pagination($this, callAPI, 'ListVip', '#show_api_list_vip', '{LANG.err_list_vip_dtnet}')
        });
        
	});
    
    function ajax_API_pagination(selector, callBackAPI, apiName, resultLocation, errorMess) {
        //phân trang API 
        var page_href = selector.children(".page-link").attr('href');
        if (page_href.includes('page=')) {
            var start = page_href.lastIndexOf('page=');
            var page_string = page_href.substring(start);
            var page = page_string.split('=');
            callBackAPI(apiName, resultLocation, errorMess, page[1]);
        } else {
            callBackAPI(apiName, resultLocation, errorMess, 1);
        }
    }
</script>
<!-- END: generate_page_js -->
<script>
span__data = $(".span__data");
for (var i = 0; i < span__data.length; i++) {
    if (span__data.eq(i).text() === '' || span__data.eq(i).text() == 0) {
        span__data.eq(i).parent().hide();
    }
}

$(".show_detail_vip").click(function(event) {
    $(this).parent().find('i').toggleClass('iconUp');
    if ($(this).parent().find('i').attr('class').split(' ')[2]) {
        $(this).parent().parent().parent().parent().find('.view_ordervip').hide();
        $(this).parent().parent().parent().parent().find('.view_ordervip').slideToggle(500);
    } else {
        $(this).parent().parent().parent().parent().find('.view_ordervip').show();
        $(this).parent().parent().parent().parent().find('.view_ordervip').slideToggle(500);
    }
});

$(".viewmore_vip").click(function(event) {
    $(this).closest('tr').find(".view_ordervip").slideToggle(500);
});
$(".title_list").click(function(event) {
    const id = $(this).attr('class').split(' ')[2];
    $('#' + id).slideToggle(500);
});
</script>
<!-- END: main -->
