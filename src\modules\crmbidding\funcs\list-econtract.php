<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2017 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Thu, 02 Feb 2017 08:48:59 GMT
 */

if (!defined('NV_IS_MOD_CRMBIDDING')) {
    die('Stop!!!');
}

if (!defined('NV_IS_USER')) {
    $redirect = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name, true);
    Header("Location: " . NV_BASE_SITEURL . "index.php?" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($redirect));
    die();
}

$page_title = $nv_Lang->getModule('list_econtract');
$key_words = $module_info['keywords'];
$array_mod_title[] = array(
    'title' => $nv_Lang->getModule('list_econtract'),
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op, true)
);

$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;

$array_data = $where = $list_done = $list_process = $all_data = [];
if (isset($array_op[1])) {
    $base_url .= '/' . $array_op[1];
}
$per_page = 10;
$page = $nv_Request->get_int('page', 'post,get', 1);
// lấy page_url
$page_url = $base_url;
if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
}

$where_common = [];
$where_common[] = 'customer_id=' . $user_info['userid'];
$where_common[] = 'status !=' . EcontractStatus::Cancel->value;
$where_common_sql = implode(' AND ', $where_common);

// Truy vấn duy nhất để đếm tất cả hợp đồng, hợp đồng hoàn thành và hợp đồng chưa hoàn thành
$db->sqlreset()
    ->select('COUNT(*) AS total,
              SUM(CASE WHEN status = ' . EContractStatus::Done->value . ' THEN 1 ELSE 0 END) AS done,
              SUM(CASE WHEN status <> ' . EContractStatus::Done->value . ' THEN 1 ELSE 0 END) AS in_process')
    ->from(NV_PREFIXLANG . '_' . $module_data . '_econtracts')
    ->where($where_common_sql);

$sth = $db->prepare($db->sql());
$sth->execute();
$result = $sth->fetch(PDO::FETCH_ASSOC);

$num_items_all = $result['total'];
$num_items_done = $result['done'];
$num_items_process = $result['in_process'];

$array_number = [
    'all' => $num_items_all,
    'done' => $num_items_done,
    'process' => $num_items_process
];

// Lấy ra danh sách
$where = [];
$where[] = 'customer_id=' . $user_info['userid'];
$where[] = 'status !=' . EContractStatus::Cancel->value;

if (isset($array_op[1]) && $array_op[1] == 'done') {
    $where[] = 'status =' . EContractStatus::Done->value;
    $num_items = $num_items_done;
} else if (isset($array_op[1]) && $array_op[1] == 'process') {
    $where[] = 'status <>' . EContractStatus::Done->value;
    $num_items = $num_items_process;
} else {
    $num_items = $num_items_all;
}

// Kiểm tra đánh số trang
betweenURLs($page, ceil($num_items / $per_page), $base_url, '&amp;page=', $prevPage, $nextPage);

$where_sql = implode(' AND ', $where);

$db->sqlreset()
    ->select('*')
    ->from(NV_PREFIXLANG . '_' . $module_data . '_econtracts')
    ->where($where_sql)
    ->order('created_at DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);

$sth = $db->prepare($db->sql());
$sth->execute();

$stt = ($page - 1) * $per_page;
while ($row = $sth->fetch()) {
    $row['stt'] = ++$stt;
    $link_detail = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['doc-econtract'] . '/' . $row['id'];
    $row['link_contract_path'] = '<a href="' . $link_detail . '&amp;pdf=1" data-toggle="tooltip" title="' . $nv_Lang->getModule('click_download_attachment') . '">
            <em class="fa fa-paperclip"></em> <span>' . $nv_Lang->getModule('attachment') . '</span>
        </a>';
    $row['url_download_contract'] = $link_detail . '&amp;pdf=1';;
    if (!empty($row['contract_path'])) {
        $arr_filename_contract = explode('/', $row['contract_path']);
        $row['contract_attachment'] = end($arr_filename_contract);
        $row['url_download_contract'] = NV_MAIN_DOMAIN . '/' . $row['contract_path'];
        $file_path = NV_ROOTDIR . '/' . $row['contract_path'];
        if (file_exists($file_path)) {
            $row['link_contract_path'] = '<a href="' . $row['url_download_contract'] . '" data-toggle="tooltip" title="' . $nv_Lang->getModule('click_download_attachment') . '" download="' . $row['contract_attachment'] . '">
                <em class="fa fa-paperclip"></em> <span>' . $nv_Lang->getModule('attachment') . '</span>
            </a>';
        }
    }
    $row['status_label'] = EContractStatus::tryFrom($row['status'])->getLabel();
    $row['stage_next'] = ($row['status'] == EContractStatus::Cancel->value || $row['stage'] == EContractStage::Done->value) ? '' : EContractStage::tryFrom($row['stage'])->getLabel();
    $row['updated_at'] = nv_date('d/m/Y H:i', $row['updated_at']);
    // Xử lý link
    $row['url_detail'] = $link_detail;
    // Kiểm tra thông tin version
    $num_version = $db->query('SELECT count(id) as num_version FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions WHERE econtract_id=' . $row['id'])->fetchColumn() ?? 0;
    $url_version = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['version-econtract'] . '/' . $row['id'];
    $row['url_version'] = [
        'check' => $num_version > 1 ? 'link' : 'text',
        'link' => $num_version > 1 ? nv_url_rewrite($url_version, true) : $nv_Lang->getModule('version_unique_message')
    ];
    $row['url_update'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['update-econtract'] . '/' . $row['id'];
    $row['url_upload'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['upload-econtract'] . '/' . $row['id'];

    $row['type_econtract'] = $nv_Lang->getModule('type_econtract_' . $row['type_econtract']);
    // Phân loại theo trạng thái
    if ($row['status'] != EContractStatus::Cancel->value) {
        if ($row['status'] == EContractStatus::Done->value) {
            $list_done[] = $row;
        } else {
            $list_process[] = $row;
        }
    }
    $all_data[] = $row;
}
$array_data = [$all_data, $list_done, $list_process];
$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
$canonicalUrl = getCanonicalUrl($page_url);

$contents = nv_theme_list_econtract($array_data, $generate_page, $array_op[1] ?? '', $array_number);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
