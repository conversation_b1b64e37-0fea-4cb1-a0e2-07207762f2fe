<?php

/**
 * Tool này không đưa sang crmprivate vì:
 * - Load code vào module point (point class) nếu đ<PERSON><PERSON> sang crmprivate khi sửa code phải sửa 2 chỗ
 * - <PERSON>h<PERSON><PERSON> bóc tin ra bên ngoài chỉ xử lý nội bộ nên không ảnh hưởng đến site
 * - Khi có dữ liệu cần phải xóa cache
 */
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __FILE__), PATHINFO_DIRNAME))));

$console_starttime = microtime(true);

require NV_CONSOLE_DIR . '/server.php';
require NV_ROOTDIR . '/includes/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

// Kiểm tra không chạy đè tiến trình
$check_runfile = NV_CONSOLE_DIR . '/point-handling.log';
$checktime = file_exists($check_runfile) ? file_get_contents($check_runfile) : 0;
$checktime = intval($checktime);
/*
 * if (!empty($checktime) and (NV_CURRENTTIME - $checktime) < (10 * 60)) {
 * exit('There is another process running' . PHP_EOL);
 * }
 */
file_put_contents($check_runfile, NV_CURRENTTIME, LOCK_EX);

$points_config = $module_config['points'];

require NV_ROOTDIR . '/modules/points/points.class.php';
$nv_points = new nukeviet_points();

include NV_ROOTDIR . '/modules/points/language/vi.php';
$lang_module_vi = $lang_module;

include NV_ROOTDIR . '/modules/points/language/en.php';
$lang_module_en = $lang_module;

$delete_cache = false;

try {
    /**
     * Đọc điểm tặng đang chờ xử lý và xử lý tặng điểm.
     * Mỗi lần xử lý tối đa 300 thành viên, mỗi thành viên 10 bản ghi
     */

    $sql = "SELECT userid FROM " . $db_config['prefix'] . "_points_users_tmp WHERE status=0 GROUP BY userid LIMIT 300";
    $result = $db->query($sql);
    $arr_userid = [];
    while ($rows = $result->fetch()) {
        $arr_userid[$rows['userid']] = $rows['userid'];
    }

    if (!empty($arr_userid)) {
        $sql = "SELECT * FROM " . $db_config['prefix'] . "_points_users_tmp WHERE userid IN (" . implode(',', $arr_userid) . ") AND status=0";
        $result = $db->query($sql);
        $arr_row = [];
        while ($rows = $result->fetch()) {
            $arr_row[$rows['userid']][$rows['id']] = $rows;
        }

        if (!empty($arr_row)) {
            $row_offset = 0;
            foreach ($arr_row as $userid => $_arr_row) {
                echo "#userid: " . $userid . " ";
                $row_offset++;
                foreach ($_arr_row as $rkey => $row) {
                    $row_offset++;
                    echo "## id:" . $rkey . " ";

                    if ($row['type'] > 100) {
                        // Hết hạn tùy biến
                        $expired_time = $row['giveexpired'];
                    } elseif ($row['type'] < 5) {
                        // Hết hạn theo tài khoản của khách
                        $expired_time = $row['addtime'] + ($points_config['expired_time'] * 86400);
                    } else {
                        // Hết hạn theo cấu hình người giới thiệu
                        $expired_time = $row['addtime'] + ($points_config['expired_aff'] * 86400);
                    }

                    $message_reward_point = $message_log = [];
                    $online10 = [];

                    // $message lưu dạng biến lang,
                    if ($row['type'] == 0) {
                        $message_log['vi'] = sprintf($lang_module_vi['point_givelog0'], nv_date('d/m/Y', $expired_time));
                        $message_log['en'] = sprintf($lang_module_en['point_givelog0'], nv_date('d/m/Y', $expired_time));

                        $message_reward_point['vi'] = sprintf($lang_module_vi['point_givemessage0'], $points_config['new_user']);
                        $message_reward_point['en'] = sprintf($lang_module_en['point_givemessage0'], $points_config['new_user']);
                        $message_log = json_encode($message_log);
                        $give_message = $nv_points->update($row['site_id'], $points_config['new_user'], $row['userid'], $message_log, true, 0, 1, $expired_time);
                    }
                    if ($row['type'] == 1) {
                        $message_log['vi'] = sprintf($lang_module_vi['point_givelog1'], nv_date('d/m/Y', $expired_time));
                        $message_log['en'] = sprintf($lang_module_en['point_givelog1'], nv_date('d/m/Y', $expired_time));

                        $message_reward_point['vi'] = sprintf($lang_module_vi['point_givemessage1'], $points_config['new_phone']);
                        $message_reward_point['en'] = sprintf($lang_module_en['point_givemessage1'], $points_config['new_phone']);
                        $message_log = json_encode($message_log);
                        $give_message = $nv_points->update($row['site_id'], $points_config['new_phone'], $row['userid'], $message_log, true, 0, 1, $expired_time);
                    }
                    if ($row['type'] == 2) {
                        $message_log['vi'] = sprintf($lang_module_vi['point_givelog2'], nv_date('d/m/Y', $expired_time));
                        $message_log['en'] = sprintf($lang_module_en['point_givelog2'], nv_date('d/m/Y', $expired_time));

                        $message_reward_point['vi'] = sprintf($lang_module_vi['point_givemessage2'], $points_config['new_tax']);
                        $message_reward_point['en'] = sprintf($lang_module_en['point_givemessage2'], $points_config['new_tax']);
                        $message_log = json_encode($message_log);
                        $give_message = $nv_points->update($row['site_id'], $points_config['new_tax'], $row['userid'], $message_log, true, 0, 1, $expired_time);
                    }
                    if ($row['type'] == 3) {
                        $message_log['vi'] = sprintf($lang_module_vi['point_givelog3'], nv_date('d/m/Y', $expired_time));
                        $message_log['en'] = sprintf($lang_module_en['point_givelog3'], nv_date('d/m/Y', $expired_time));

                        $message_reward_point['vi'] = sprintf($lang_module_vi['point_givemessage3'], $points_config['new_login']);
                        $message_reward_point['en'] = sprintf($lang_module_en['point_givemessage3'], $points_config['new_login']);
                        $message_log = json_encode($message_log);

                        $give_message = $nv_points->update($row['site_id'], $points_config['new_login'], $row['userid'], $message_log, true, 0, 1, $expired_time);
                        $online10['vi'] = sprintf($lang_module_vi['online10'], $points_config['login_10m']);
                        $online10['en'] = sprintf($lang_module_en['online10'], $points_config['login_10m']);
                    }
                    if ($row['type'] == 4) {
                        $message_log['vi'] = sprintf($lang_module_vi['point_givelog4'], nv_date('d/m/Y', $expired_time));
                        $message_log['en'] = sprintf($lang_module_en['point_givelog4'], nv_date('d/m/Y', $expired_time));

                        $message_reward_point['vi'] = sprintf($lang_module_vi['point_givemessage4'], $points_config['login_10m']);
                        $message_reward_point['en'] = sprintf($lang_module_en['point_givemessage4'], $points_config['login_10m']);
                        $message_log = json_encode($message_log);
                        $give_message = $nv_points->update($row['site_id'], $points_config['login_10m'], $row['userid'], $message_log, true, 0, 1, $expired_time);
                    }

                    if ($row['type'] == 5) {
                        $pre_name = '';
                        if ($row['pre_uid'] > 0) {
                            $pre_uid_info = $db->query("SELECT * FROM " . NV_USERS_GLOBALTABLE . " WHERE userid =" . $row['pre_uid'])->fetch();
                            if (!empty($pre_uid_info)) {
                                $pre_name = nv_show_name_user($pre_uid_info['first_name'], $pre_uid_info['last_name'], $pre_uid_info['username']);
                            }
                        }

                        $message_log['vi'] = sprintf($lang_module_vi['point_givelog5'], $pre_name, nv_date('d/m/Y', $expired_time));
                        $message_log['en'] = sprintf($lang_module_en['point_givelog5'], $pre_name, nv_date('d/m/Y', $expired_time));

                        $message_reward_point['vi'] = sprintf($lang_module_vi['point_givemessage5'], $points_config['new_user_aff'], $pre_name);
                        $message_reward_point['en'] = sprintf($lang_module_en['point_givemessage5'], $points_config['new_user_aff'], $pre_name);
                        $message_log = json_encode($message_log);
                        $give_message = $nv_points->update($row['site_id'], $points_config['new_user_aff'], $row['userid'], $message_log, true, 0, 1, $expired_time);
                    }
                    if ($row['type'] == 6) {
                        $message_log['vi'] = sprintf($lang_module_vi['point_givelog6'], nv_date('d/m/Y', $expired_time));
                        $message_log['en'] = sprintf($lang_module_en['point_givelog6'], nv_date('d/m/Y', $expired_time));

                        $message_reward_point['vi'] = sprintf($lang_module_vi['point_givemessage6'], $points_config['new_phone_aff']);
                        $message_reward_point['en'] = sprintf($lang_module_en['point_givemessage6'], $points_config['new_phone_aff']);
                        $message_log = json_encode($message_log);
                        $give_message = $nv_points->update($row['site_id'], $points_config['new_phone_aff'], $row['userid'], $message_log, true, 0, 1, $expired_time);
                    }
                    if ($row['type'] == 7) {
                        $message_log['vi'] = sprintf($lang_module_vi['point_givelog7'], nv_date('d/m/Y', $expired_time));
                        $message_log['en'] = sprintf($lang_module_en['point_givelog7'], nv_date('d/m/Y', $expired_time));

                        $message_reward_point['vi'] = sprintf($lang_module_vi['point_givemessage7'], $points_config['new_tax_aff']);
                        $message_reward_point['en'] = sprintf($lang_module_en['point_givemessage7'], $points_config['new_tax_aff']);
                        $message_log = json_encode($message_log);
                        $give_message = $nv_points->update($row['site_id'], $points_config['new_tax_aff'], $row['userid'], $message_log, true, 0, 1, $expired_time);
                    }

                    // Tặng điểm cho khách hàng khi bóc lại dữ liệu
                    if ($row['type'] == 8) {
                        $lang = $db->query("SELECT * FROM  " . NV_CONFIG_GLOBALTABLE . " WHERE config_name = 'site_lang'")->fetch()['config_value'];
                        $point_crawl_mintus = $db->query("SELECT config_value FROM " . NV_CONFIG_GLOBALTABLE . " WHERE config_name = 'point_crawl_mintus'")->fetchcolumn();

                        $expired_crawl = $db->query("SELECT config_value FROM " . NV_CONFIG_GLOBALTABLE . " WHERE config_name = 'expired_crawl'")->fetchcolumn();

                        // Nếu tồn tại link chi tiết
                        if (!empty($row['link_detail'])) {
                            $message_log['en'] = sprintf($lang_module_en['message_point_add_link'], $row['givepoints'], $row['key_crawl'], $point_crawl_mintus, $row['link_detail']);
                            $message_log['vi'] = sprintf($lang_module_vi['message_point_add_link'], $row['givepoints'], $row['key_crawl'], $point_crawl_mintus, $row['link_detail']);
                        } else {
                            $message_log['en'] = sprintf($lang_module_en['message_point_add'], $row['givepoints'], $row['key_crawl'], $point_crawl_mintus);
                            $message_log['vi'] = sprintf($lang_module_vi['message_point_add'], $row['givepoints'], $row['key_crawl'], $point_crawl_mintus);
                        }

                        if ($lang == 'vi') {
                            $message = $message_log['vi'];
                        } else {
                            $message = $message_log['en'];
                        }

                        $row['givepoints'] = $row['givepoints'] + $point_crawl_mintus;

                        $message_reward_point = $message_log;
                        $expired_time = $time_expired = $row['addtime'] + ($expired_crawl * 86400);
                        // Kiểm tra xem tặng điểm hay trừ điểm
                        $give_message = $nv_points->update($row['site_id'], $row['givepoints'], $row['userid'], $message, true, 0, 0, $time_expired);
                    }

                    if ($row['type'] > 100) {
                        $message = $row['givelog'] ?: $row['givemessage'];
                        if ($row['type'] == 104) {
                            $message_reward_point['vi'] = $lang_module_vi['msg_104'];
                            $message_reward_point['en'] = $lang_module_en['msg_104'];
                        } elseif ($row['type'] > 104 && $row['type'] < 109) {
                            if ($row['site_id'] == 1) {
                                $link = 'https://dauthau.asia/news/blog/chuong-trinh-tang-diem-tu-dauthau-info-nhan-ngay-doanh-nhan-viet-nam-13-10-1091.html';
                            } else {
                                $link = 'https://dauthau.net/vi/news/blog/chuong-trinh-tang-diem-tu-dauthau-net-nhan-ngay-doanh-nhan-viet-nam-13-10-233.html';
                            }
                            if ($row['type'] == 108) {
                                $key_message = 'msg_108';
                            } else {
                                $key_message = 'msg_105';
                            }
                            $message_reward_point['vi'] = sprintf($lang_module_vi[$key_message], $row['givepoints'], $link);
                            $message_reward_point['en'] = sprintf($lang_module_en[$key_message], $row['givepoints'], $link);
                        } elseif ($row['type'] == 111 or $row['type'] == 112) {
                            $link_vi = 'https://dauthau.asia/news/tin-tuc/dauthau-info-trien-khai-chuoi-su-kien-chao-mung-ngay-doanh-nhan-viet-nam-1428.html';
                            $link_en = 'https://dauthau.asia/en/news/general-information/dauthau-info-launches-event-series-to-celebrate-vietnam-entrepreneurs-day-on-october-13-294.html';
                            $message_reward_point['vi'] = sprintf($lang_module_vi['msg_105'], $row['givepoints'], $link_vi);
                            $message_reward_point['en'] = sprintf($lang_module_en['msg_105'], $row['givepoints'], $link_en);
                        } elseif ($row['type'] == 109) {
                            $link_vi = 'https://dauthau.asia/news/blog/dauthau-info-li-xi-len-den-300-diem-nhan-ngay-dau-xuan-giap-thin-2024-1193.html';
                            $link_en = 'https://dauthau.asia/en/news/blog/dauthau-info-lucky-money-up-to-300-points-on-the-first-day-of-giap-thin-spring-2024-265.html';
                            $message_reward_point['vi'] = sprintf($lang_module_vi['msg_109'], $row['givepoints'], $link_vi);
                            $message_reward_point['en'] = sprintf($lang_module_en['msg_109'], $row['givepoints'], $link_en);
                            $message = json_encode($message_reward_point);
                        } elseif ($row['type'] == 113) {
                            $link_vi = 'https://dauthau.asia/news/blog/li-xi-len-den-300-diem-nhan-ngay-dau-xuan-at-ty-2025-1531.html';
                            $link_en = 'https://dauthau.asia/en/news/blog/dauthau-info-lucky-money-up-to-300-points-on-the-first-day-of-spring-at-ty-2025-296.html';
                            $message_reward_point['vi'] = sprintf($lang_module_vi['msg_113'], $row['givepoints'], $link_vi);
                            $message_reward_point['en'] = sprintf($lang_module_en['msg_113'], $row['givepoints'], $link_en);
                        } else {
                            $message_reward_point = $row['givemessage'];
                        }
                        $give_message = $nv_points->update($row['site_id'], $row['givepoints'], $row['userid'], $message, true, 0, 1, $expired_time);
                    }

                    if (!$nv_points->isError()) {
                        $db->exec("UPDATE " . $db_config['prefix'] . "_points_users_tmp SET status=1, updatetime=" . NV_CURRENTTIME . " WHERE id = " . $row['id']);
                        echo "success\n";

                        // Lưu vào bảng message
                        if (!empty($message_reward_point)) {
                            if (is_array($message_reward_point)) {
                                $message_reward_point = json_encode($message_reward_point);
                            }

                            $online10 = !empty($online10) ? json_encode($online10) : '';
                            $sql = "INSERT INTO " . $db_config['prefix'] . "_points_reward_messages (
                                userid, site_id, expired_time, give_type, message_reward, online10, add_time, status
                            ) VALUES (
                                :userid, :site_id, :expired_time, :give_type, :message_reward, :online10, :add_time, 0
                            )";

                            $insert_data = [
                                'userid' => $row['userid'],
                                'site_id' => $row['site_id'],
                                'expired_time' => $expired_time,
                                'give_type' => $row['type'],
                                'message_reward' => $message_reward_point,
                                'online10' => $online10,
                                'add_time' => NV_CURRENTTIME
                            ];
                            $db->insert_id($sql, 'id', $insert_data);

                            // Đánh dấu point có message
                            $sql = "UPDATE " . $db_config['prefix'] . "_points_customs SET have_msg=1, updatetime=" . NV_CURRENTTIME . " WHERE userid=" . $row['userid'];
                            $db->query($sql);

                            $delete_cache = true;
                        }
                    } else {
                        echo "error: " . $give_message . "\n";
                    }

                    if ($row_offset >= 10) {
                        break;
                    }
                }

                // Tính toán thời gian tiếp theo sẽ xử lý hết hạn điểm tặng
                $nv_points->nextExpired($userid);
            }
        }
    }

    // Xử lý hết hạn điểm tặng
    $nv_points->setNoCheckBalance();

    echo "Expried proccess...\n";
    $sql = "SELECT userid FROM " . $db_config['prefix'] . "_points_customs
    WHERE next_expired>0 AND next_expired<=" . NV_CURRENTTIME . " LIMIT 3000";
    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        echo "U #" . $row['userid'] . "\n";
        $nv_points->expired($row['userid']);
    }
} catch (Exception $e) {
    print_r($e);
}

if ($delete_cache) {
    $nv_Cache->delMod('points');
}

$console_endtime = microtime(true);
$execution_time = getConsoleExecuteTime($console_starttime, $console_endtime);

echo ('Execution time: ' . $execution_time . PHP_EOL);
echo ('Console end!' . PHP_EOL);
unlink($check_runfile);
