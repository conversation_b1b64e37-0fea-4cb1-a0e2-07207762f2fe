<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CreateContact implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'contact';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $array_user_id_users, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $error = '';
        $error_code = '0000';
        $admin_id = $nv_Request->get_int('admin_id', 'post', '0');
        $row['contactname'] = $nv_Request->get_title('contactname', 'post', '');
        $row['primaryphone'] = $nv_Request->get_title('primaryphone', 'post', '');
        $row['primaryemail'] = $nv_Request->get_title('primaryemail', 'post', '');
        $row['organizationsid'] = $nv_Request->get_int('organizationsid', 'post', 0);

        if ($row['contactname'] == '') {
            $error_code = '2003';
            $error = $nv_Lang->getModule('error_required_name');
        } else if ($row['primaryphone'] == '' and $row['primaryemail'] == '') {
            $error_code = '2004';
            $error = $nv_Lang->getModule('error_required_phone');
        } elseif ($row['primaryphone'] != '' && !phonecheck($row['primaryphone'])) {
            $error_code = '2005';
            $error = $nv_Lang->getModule('error_phone_number');
        } elseif ($row['primaryemail'] != '' && nv_check_valid_email($row['primaryemail']) != '') {
            $error_code = '2006';
            $error = $nv_Lang->getModule('error_email');
        } else if ($admin_id > 0 and !isset($array_user_id_users[$admin_id])) {
            $error_code = '2011';
            $error = $nv_Lang->getModule('admin_id_not_defind');
        } else if ($row['organizationsid'] == 0) {
            $error_code = '2010';
            $error = $nv_Lang->getModule('organizationsid_not_defined');
        }

        $otherdata = $nv_Request->get_array('otherdata', 'post');
        if (!is_array($otherdata)) {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Param otherdata invalid to array')
                ->getResult();
        }

        $keys_check = [
            'shortname',
            'secondaryphone',
            'secondaryemail',
            'address',
            'description',
            'convert_leads'
        ];
        $array_sql = [];
        if (!empty($otherdata)) {
            // check $field
            foreach ($otherdata as $key => $value) {
                if (!in_array($key, $keys_check)) {
                    return $this->result->setError()
                        ->setCode('2009')
                        ->setMessage('Missing field ' . $key . ' in otherdata')
                        ->getResult();
                }
                $array_sql[$key] = $key;
            }
        }

        $row = array_merge($row, $otherdata);

        if (isset($row['secondaryemail']) and $row['secondaryemail'] != '') {
            $row['secondaryemail'] = str_replace(';', ',', $row['secondaryemail']);
            $row['secondaryemail'] = str_replace("\n", ',', $row['secondaryemail']);
            $_arr_email = array();
            $list_mail = explode(',', $row['secondaryemail']);
            foreach ($list_mail as $_mail) {
                $_mail = trim($_mail);
                if (($check_valid_email = nv_check_valid_email($_mail)) != '') {
                    $error_code = '2008';
                    $error = sprintf($nv_Lang->getModule('error_subemail'), $_mail);
                } elseif (!in_array($_mail, $_arr_email)) {
                    $_arr_email[] = $_mail;
                }
            }
            $row['secondaryemail'] = implode(',', $_arr_email);
        }

        if (isset($row['secondaryphone']) and $row['secondaryphone'] != '') {
            $row['secondaryphone'] = str_replace(';', ',', $row['secondaryphone']);
            $row['secondaryphone'] = str_replace("\n", ',', $row['secondaryphone']);
            $_arr_phone = array();
            $list_phone = explode(',', $row['secondaryphone']);
            foreach ($list_phone as $_phone) {
                $_phone = trim($_phone);
                $_arr_phone[] = $_phone;
                if (!phonecheck($_phone)) {
                    $error_code = '2007';
                    $error = sprintf($nv_Lang->getModule('error_sub_phone'), $_phone);
                }
            }
            $row['secondaryphone'] = implode(',', $_arr_phone);
        }
        $_row = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_contact WHERE active=1 AND contactname=' . $db->quote($row['contactname']) . ' AND primaryphone = ' . $db->quote($row['primaryphone']) . ' AND primaryemail =' . $db->quote($row['primaryemail']) . ' AND organizationsid =' . $db->quote($row['organizationsid']))
            ->fetch();
        if (!empty($_row)) {
            $error_code = '4002';
            $error = $nv_Lang->getModule('error_duplicate');
        }

        if (empty($error)) {
            try {
                $row['createtime'] = $row['updatetime'] = NV_CURRENTTIME;
                if (!empty($array_sql)) {
                    $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_contact (contactname, primaryphone, primaryemail, organizationsid, createtime, updatetime, active, ' . implode(',', $array_sql) . ') VALUES (:contactname, :primaryphone, :primaryemail, :organizationsid, :createtime, :updatetime, :active, :' . implode(', :', $array_sql) . ')');
                } else {
                    $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_contact (contactname, primaryphone, primaryemail, organizationsid, createtime, updatetime, active) VALUES (:contactname, :primaryphone, :primaryemail, :organizationsid, :createtime, :updatetime, :active)');
                }

                $stmt->bindParam(':contactname', $row['contactname'], PDO::PARAM_STR);
                $stmt->bindParam(':primaryphone', $row['primaryphone'], PDO::PARAM_STR);
                $stmt->bindParam(':primaryemail', $row['primaryemail'], PDO::PARAM_STR);
                $stmt->bindValue(':active', 1, PDO::PARAM_INT);
                $stmt->bindParam(':createtime', $row['createtime'], PDO::PARAM_INT);
                $stmt->bindParam(':updatetime', $row['updatetime'], PDO::PARAM_INT);
                $stmt->bindParam(':organizationsid', $row['organizationsid'], PDO::PARAM_INT);

                if (!empty($otherdata)) {
                    foreach ($otherdata as $key => $value) {
                        $stmt->bindValue(':' . $key, $value, PDO::PARAM_STR);
                    }
                }

                $exc = $stmt->execute();
                $id = $db->lastInsertId();
                if ($exc and $id > 0) {
                    $log_data = [
                        $nv_Lang->getModule('contact_add_log')
                    ];

                    $sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (userid, log_area, log_key, log_time, log_data, contactid) VALUES (" . $admin_id . ", 1, 'LOG_ADMIN_ADD_CONTACT', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $id . ")";
                    $db->query($sql);

                    if ($row['organizationsid']) {
                        $sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (userid, log_area, log_key, log_time, log_data, organizationsid) VALUES (" . $admin_id . ", 1, 'LOG_ADMIN_UPDATE_ORGANIZATIONS', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $row['organizationsid'] . ")";
                        $db->query($sql);
                    }

                    $nv_Cache->delMod($module_name);
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Add Contact', $id, $admin_id);
                    $this->result->setSuccess();
                    $this->result->set('ContactID', $id);
                } else {
                    $this->result->setError()
                        ->setCode('4001')
                        ->setMessage('ContactID not response');
                }
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode($error_code)
                ->setMessage($error)
                ->getResult();
        }
        return $this->result->getResult();
    }
}
