<?php

/**
 * @Project SSO
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @Createdate 18/7/2020, 9:40
 */
if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

if (strpos(NV_USER_AGENT, 'VNPAY') !== false) {
    $client_info['is_bot'] = true;
} elseif (isset($_SERVER['HTTP_X_KNOWN_BOTS']) and $_SERVER['HTTP_X_KNOWN_BOTS'] == 'true') {
    $client_info['is_bot'] = true;
    /*
     * Cần config cloudflare của domain theo hướng dần tại
     * Log in to the Cloudflare dashboard, and select your account and website.
     * Go to Rules > Transform Rules.
     * Go to the Modify Request Header tab.
     * Select Create rule
     * Rule name: Add x-known-bots
     * Expression Preview: (http.request.method eq "GET")
     * Modify request header: Set dynamic, x-known-bots, cf.client.bot
     * Chi tiết: https://community.cloudflare.com/t/how-to-create-a-custom-cache-key-for-bot-traffic/362466/3
     */
} elseif ($client_info['client_os']['key'] == 'unknown') {
    $client_info['is_bot'] = true;
} else {
    // Các từ khóa phổ biến để xác định bot
    $botKeywords = [
        'bot',
        'craw',
        'spider',
        'scraper',
        'robot',
        'archive',
        'validator',
        'WhatsApp'
    ];

    foreach ($botKeywords as $keyword) {
        if (stripos(NV_USER_AGENT, $keyword) !== false) {
            $client_info['is_bot'] = true;
            break;
        }
    }
}

if (empty($client_info['is_bot'])) {
    $broker = new Jasny\SSO\Broker(SSO_SERVER, SSO_BROKER_ID, SSO_BROKER_SECRET, 36000);
    $isBrokerAttached = $broker->isAttached();
    $broker->attach(true);

    try {
        $user_info = $broker->getUserInfo();

        // Xử lý 1 tài khoản chỉ được đăng nhập được trên 1 trình duyệt
        if (isset($user_info['checknum'])) {
            if ($nv_Request->isset_request('ssochecknum', 'session')) {
                if ($nv_Request->get_string('ssochecknum', 'session') != $user_info['checknum']) {
                    $nv_Request->unset_request('ssochecknum', 'session');
                    $broker->logout();
                    $user_info = [];
                }
            } else {
                $nv_Request->set_Session('ssochecknum', $user_info['checknum']);
            }
        }

        /**
         * Xử lý trong trường hợp không tồn tại các giá trị
         */
        if (!isset($user_info['marketing_type'])) {
            $user_info['marketing_type'] = 0;
        }
        if (!isset($user_info['register_url'])) {
            $user_info['register_url'] = null;
        }

        if (isset($user_info['userid'])) {
            $custom_fields = $user_info['custom_fields'];
            try {
                $sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . " WHERE userid =" . intval($user_info['userid']);
                $row = $db->query($sql)->fetch();

                if (empty($row)) {
                    // Xử lý khi đăng nhập lần đầu
                    $_sql = "INSERT INTO " . NV_USERS_GLOBALTABLE . " (
                        userid, group_id, username, md5username, password, email, first_name, last_name, gender, photo, birthday, regdate,
                        question, answer, passlostkey, view_mail, remember, in_groups,
                        active, active2step, secretkey, checknum, last_login, last_ip, last_agent, last_openid, last_update, idsite, email_verification_time, active_obj,
                        marketing_type, register_url
                    ) VALUES (
                        :userid,
                        4,
                        :username,
                        :md5username,
                        :password,
                        :email,
                        :first_name,
                        :last_name,
                        :gender,
                        :photo,
                        0,
                        " . NV_CURRENTTIME . ",
                        '', '', '', 0, 0, '', 1, " . $user_info['active2step'] . ",
                        " . $db->quote($user_info['secretkey']) . ", '', 0, '', '', '',
                        :last_update,
                        " . intval($global_config['idsite']) . ",
                        -1, " . $db->quote('OAUTH:SSO') . ",
                        " . intval($user_info['marketing_type']) . ",
                        " . (is_null($user_info['register_url']) ? 'null' : ':register_url') . "
                    )";

                    $stmt = $db->prepare($_sql);
                    $stmt->bindParam(':userid', $user_info['userid'], PDO::PARAM_INT);
                    $stmt->bindParam(':username', $user_info['username'], PDO::PARAM_STR);
                    $stmt->bindParam(':md5username', $user_info['md5username'], PDO::PARAM_STR);
                    $stmt->bindParam(':password', $user_info['password'], PDO::PARAM_STR);
                    $stmt->bindParam(':email', $user_info['email'], PDO::PARAM_STR);
                    $stmt->bindParam(':first_name', $user_info['first_name'], PDO::PARAM_STR);
                    $stmt->bindParam(':last_name', $user_info['last_name'], PDO::PARAM_STR);
                    $stmt->bindParam(':gender', $user_info['gender'], PDO::PARAM_STR);
                    $stmt->bindParam(':photo', $user_info['photo'], PDO::PARAM_STR);
                    $stmt->bindParam(':last_update', $user_info['last_update'], PDO::PARAM_INT);
                    if (!is_null($user_info['register_url'])) {
                        $stmt->bindParam(':register_url', $user_info['register_url'], PDO::PARAM_STR, strlen($user_info['register_url']));
                    }
                    $stmt->execute();

                    // Cap nhat so thanh vien
                    $db->query('UPDATE ' . NV_USERS_GLOBALTABLE . '_groups SET numbers = numbers+1 WHERE group_id=4');
                    $row['in_groups'] = 4;

                    // Luu vao bang thong tin tuy chinh
                    $query_field = [];
                    $query_field['userid'] = $user_info['userid'];
                    $result_field = $db->query('SELECT * FROM ' . NV_USERS_GLOBALTABLE . '_field ORDER BY fid ASC');
                    while ($row_f = $result_field->fetch()) {
                        if ($row_f['is_system'] == 1) {
                            continue;
                        }
                        $query_field[$row_f['field']] = (isset($custom_fields[$row_f['field']])) ? $db->quote($custom_fields[$row_f['field']]) : $db->quote($row_f['default_value']);
                    }
                    $db->query('INSERT INTO ' . NV_USERS_GLOBALTABLE . '_info (' . implode(', ', array_keys($query_field)) . ') VALUES (' . implode(', ', array_values($query_field)) . ')');

                    /**
                     *
                     * <AUTHOR>
                     *         đoạn xử lý lưu lại nếu là thành viên mới để tặng điểm, chỉ áp dụng với dauthau.info
                     */
                    if (NV_MY_DOMAIN == 'https://dauthau.asia' or NV_MY_DOMAIN == 'http://dauthau.my') {
                        // đăng ký
                        $db->query('INSERT INTO ' . $db_config['prefix'] . '_points_users_tmp (userid, type, status, addtime) VALUES (' . $user_info['userid'] . ', 0, 0, ' . NV_CURRENTTIME . ')');
                        // nếu có khai báo đủ số điện thoại lần đầu
                        if ($query_field['phone'] != '') {
                            $db->query('INSERT INTO ' . $db_config['prefix'] . '_points_users_tmp (userid, type, status, addtime) VALUES (' . $user_info['userid'] . ', 1, 0, ' . NV_CURRENTTIME . ')');
                        }
                        // nếu có khai báo đủ số mã số thuế lần đầu
                        if ($query_field['mst'] != '') {
                            $db->query('INSERT INTO ' . $db_config['prefix'] . '_points_users_tmp (userid, type, status, addtime) VALUES (' . $user_info['userid'] . ', 2, 0, ' . NV_CURRENTTIME . ')');
                        }
                    }

                    /**
                     *
                     * <AUTHOR>
                     * @since 07/09/2021
                     *
                     *        Lấy người giới thiệu của thành viên này
                     */
                    if (defined('ID_API_USER_KEY')) {
                        $api = new DoApi(ID_API_URL, ID_API_USER_KEY, ID_API_USER_SECRET);
                        $api->setModule('users')
                            ->setLang('vi')
                            ->setAction('GetUserFirstLogin')
                            ->setData([
                            'userid' => $user_info['userid']
                        ]);
                        $result = $api->execute();
                        $error = $api->getError();
                        if (!empty($error)) {
                            trigger_error(print_r($error, true));
                        } elseif ($result['status'] != 'success') {
                            trigger_error($result['message']);
                        } elseif (!empty($result['pri_uid'])) {
                            $sql = "INSERT IGNORE INTO " . $db_config['prefix'] . "_elink_affiliate_set (
                                pri_uid, pre_uid
                            ) VALUES (
                                " . intval($result['pri_uid']) . ", " . $user_info['userid'] . "
                            )";
                            $insert_aff = $db->query($sql);
                            if ($insert_aff->rowCount() > 0) {
                                $db->query("UPDATE " . NV_USERS_GLOBALTABLE . " SET num_aff_user = num_aff_user + 1 WHERE userid = " . $result['pri_uid']);
                            }
                        }

                        unset($api, $result, $error);
                    }
                } else {
                    // Cập nhật thông tin nếu có thay đổi
                    if ($user_info['last_update'] > $row['last_update']) {
                        $db->query("UPDATE " . NV_USERS_GLOBALTABLE . " SET
                            username=" . $db->quote($user_info['username']) . ",
                            md5username=" . $db->quote($user_info['md5username']) . ",
                            password=" . $db->quote($user_info['password']) . ",
                            email=" . $db->quote($user_info['email']) . ",
                            first_name=" . $db->quote($user_info['first_name']) . ",
                            last_name=" . $db->quote($user_info['last_name']) . ",
                            gender=" . $db->quote($user_info['gender']) . ",
                            photo=" . $db->quote($user_info['photo']) . ",
                            birthday=" . intval($user_info['birthday']) . ",
                            sig=" . $db->quote($user_info['sig']) . ",
                            question=" . $db->quote($user_info['question']) . ",
                            answer=" . $db->quote($user_info['answer']) . ",
                            view_mail=" . $user_info['view_mail'] . ",
                            active2step=" . $user_info['active2step'] . ",
                            secretkey=" . $db->quote($user_info['secretkey']) . ",
                            last_update=" . $user_info['last_update'] . ",
                            marketing_type=" . intval($user_info['marketing_type']) . ",
                            register_url=" . (is_null($user_info['register_url']) ? 'null' : $db->quote($user_info['register_url'])) . "
                        WHERE userid=" . $user_info['userid']);

                        $query_field = [];
                        $result_field = $db->query('SELECT * FROM ' . NV_USERS_GLOBALTABLE . '_field ORDER BY fid ASC');
                        while ($row_f = $result_field->fetch()) {
                            if ($row_f['is_system'] == 1) {
                                continue;
                            }
                            if (isset($custom_fields[$row_f['field']])) {
                                $query_field[$row_f['field']] = $row_f['field'] . '=' . $db->quote($custom_fields[$row_f['field']]);
                            }
                        }

                        if (NV_MY_DOMAIN == 'https://dauthau.asia' or NV_MY_DOMAIN == 'http://dauthau.my') {
                            $row_field_old = $db->query("SELECT * FROM " . NV_USERS_GLOBALTABLE . "_info WHERE userid =" . intval($user_info['userid']))->fetch();
                            // nếu có khai báo đủ số điện thoại lần đầu
                            if ($query_field['phone'] != '' and $row_field_old['phone'] == '') {
                                $_query = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_points_users_tmp WHERE userid =' . $user_info['userid'] . ' AND type=1')->fetch();
                                if (empty($_query)) {
                                    $db->query('INSERT INTO ' . $db_config['prefix'] . '_points_users_tmp (userid, type, status, addtime) VALUES (' . $user_info['userid'] . ', 1, 0, ' . NV_CURRENTTIME . ')');
                                }
                            }
                            // nếu có khai báo đủ số mã số thuế lần đầu
                            if ($query_field['mst'] != '' and $row_field_old['mst'] == '') {
                                $_query = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_points_users_tmp WHERE userid =' . $user_info['userid'] . ' AND type=2')->fetch();
                                if (empty($_query)) {
                                    $db->query('INSERT INTO ' . $db_config['prefix'] . '_points_users_tmp (userid, type, status, addtime) VALUES (' . $user_info['userid'] . ', 2, 0, ' . NV_CURRENTTIME . ')');
                                }
                            }
                        }

                        if (!empty($query_field)) {
                            $db->query('UPDATE ' . NV_USERS_GLOBALTABLE . '_info SET ' . implode(', ', $query_field) . ' WHERE userid=' . $user_info['userid']);
                        }
                    }

                    // Cập nhật các thông tin theo từng lượt đăng nhập
                    if ($user_info['last_login'] != $row['last_login']) {
                        $db->query("UPDATE " . NV_USERS_GLOBALTABLE . " SET
                            last_login=" . $user_info['last_login'] . ",
                            last_ip=" . $db->quote($user_info['last_ip']) . ",
                            last_agent=" . $db->quote($user_info['last_agent']) . ",
                            last_openid=" . $db->quote($user_info['last_openid']) . ",
                            last_passkey=" . $db->quote($user_info['last_passkey']) . "
                        WHERE userid=" . $user_info['userid']);
                    }
                }

                define('NV_IS_USER', true);

                $user_info['avata'] = !empty($user_info['photo']) ? SSO_REGISTER_DOMAIN . NV_BASE_SITEURL . $user_info['photo'] : '';
                $user_info['full_name'] = nv_show_name_user($user_info['first_name'], $user_info['last_name'], $user_info['username']);
                $user_info['in_groups'] = nv_user_groups($row['in_groups']);
                $user_info['st_login'] = !empty($user_info['password']) ? true : false;
                !isset($user_info['current_mode']) && $user_info['current_mode'] = 0;
                !isset($user_info['current_passkey']) && $user_info['current_passkey'] = '';
                $user_info['valid_question'] = true;
                $user_info['current_login'] = $user_info['last_login'];

                if (NV_MY_DOMAIN == 'https://dauthau.asia' or NV_MY_DOMAIN == 'http://dauthau.my') {
                    // chỉ áp dụng cho các thành viên thường (thành viên không sử dụng gói nào, với các thành viên đang sử dụng gói VIEWEB hoặc gói VIP thì không áp dụng, thành viên đã đăng ký VIEWEB/VIP mà hết hạn thì lại được áp dụng
                    $_query = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_vi_bidding_customs WHERE user_id =' . $user_info['userid'] . ' AND (end_time >= ' . NV_CURRENTTIME . ' AND status =1)')->fetch();
                    if (empty($_query)) {
                        $from_time = mktime(0, 0, 0, date('m', NV_CURRENTTIME), date('d', NV_CURRENTTIME), date('Y', NV_CURRENTTIME));
                        $to_time = mktime(23, 59, 59, date('m', NV_CURRENTTIME), date('d', NV_CURRENTTIME), date('Y', NV_CURRENTTIME));

                        // lần đầu tiên login trong ngày
                        $_query = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_points_users_tmp WHERE userid =' . $user_info['userid'] . ' AND type=3 AND addtime > ' . $from_time . ' AND addtime < ' . $to_time . '')->fetch();
                        if (empty($_query)) {
                            $db->query('INSERT INTO ' . $db_config['prefix'] . '_points_users_tmp (userid, type, status, addtime) VALUES (' . $user_info['userid'] . ', 3, 0, ' . NV_CURRENTTIME . ')');
                        }

                        // login đủ 10 phút
                        if ($user_info['last_login'] < (NV_CURRENTTIME - 600)) {
                            $_query = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_points_users_tmp WHERE userid =' . $user_info['userid'] . ' AND type=4 AND addtime > ' . $from_time . ' AND addtime < ' . $to_time . '')->fetch();
                            if (empty($_query)) {
                                $db->query('INSERT INTO ' . $db_config['prefix'] . '_points_users_tmp (userid, type, status, addtime) VALUES (' . $user_info['userid'] . ', 4, 0, ' . NV_CURRENTTIME . ')');
                            }
                        }
                    }
                }

                unset($user_info['password']);
            } catch (PDOException $e) {
                trigger_error($e->getMessage());
            }
        } elseif ($isBrokerAttached and $nv_Request->get_int('sso_sid', 'session', 0) == 0) {
            /**
             * Trong một phiên nếu đã attacted mà không có userinfo thì ép attact vào lần truy cập sau
             *
             * @since 16/01/2023
             * @link https://vinades.org/dauthau/dauthau.info/-/issues/1362
             */
            $broker->clearToken();
            $nv_Request->set_Session('sso_sid', NV_CURRENTTIME);
            $protocol = !empty($_SERVER['HTTPS']) ? 'https://' : 'http://';
            $port = ($_SERVER['SERVER_PORT'] == '80' or $_SERVER['SERVER_PORT'] == '443') ? '' : (':' . $_SERVER['SERVER_PORT']);
            $returnUrl = $protocol . $_SERVER['HTTP_HOST'] . $port . $_SERVER['REQUEST_URI'];
            nv_redirect_location($returnUrl);
        }
    } catch (Jasny\SSO\Exception $e) {
        $user_info = [];
    } catch (Exception $e) {
        $user_info = [];
    }
} else {
    $user_info = [];
}
