<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
	<div class="search">
		<div class="form-group">
			<form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline">
				<input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}">
				<input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
				<input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}">
				<div class="form-group">
				<div class="form-group">
					<input class="form-control" type="text" value="{Q}" name="q" maxlength="255" placeholder="{LANG.search_title}">
				</div>
				<div class="form-group">
                    <select class="form-control" name="s_vip" id="vip">
                        <option value="0">{LANG.vip_all}</option>
                        <!-- BEGIN: loop_vip -->
                        <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                        <!-- END: loop_vip -->
                    </select>
                </div>
				<div class="form-group">
                    <select class="form-control" name="s_admin" id="sale">
                        <option value="0">{LANG.sale_manager}</option>
                        <!-- BEGIN: loop_admin -->
                        <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                        <!-- END: loop_admin -->
                    </select>
                </div>
				<div class="form-group">
                    <select class="form-control" name="s_point" id="point">
                        <option value="0">--Điểm--</option>
                        <option value="1" {S_POINT.1}>{LANG.used_point}</option>
						<option value="2" {S_POINT.2}>{LANG.not_used_point}</option>
                    </select>
                </div>
				<div class="form-group">
                    <select class="form-control" name="s_wallet" id="wallet">
                        <option value="0">--Ví tiền--</option>
                        <option value="1" {S_WALLET.1}>{LANG.charge_wallet}</option>
						<option value="2" {S_WALLET.2}>{LANG.charge_wallet_not}</option>
                    </select>
                </div>
				<div class="form-group">
                    <select class="form-control" name="s_money_wallet" id="money_wallet">
                        <option value="0">--Số dư--</option>
                        <option value="1" {S_MONEY_WALLET.1}>0</option>
						<option value="2" {S_MONEY_WALLET.2}>>0</option>
                    </select>
                </div>
				<div class="form-group">
					<input class="btn btn-primary" type="submit" value="{LANG.search_submit}">
				</div>
			</form>
		</div>
	</div>

	<div class="list_customer responsivetb">
		<table class="table table-bordered table-hover table-responsive">
			<thead>
				<tr>
					<th class="text-center" rowspan="2">{LANG.stt}</th>
					<th class="text-center" rowspan="2">{LANG.customer_info}</th>
					<th class="text-center" rowspan="2">{LANG.tax}</th>
					<th class="text-center" rowspan="2">{LANG.regdate}</th>
					<th class="text-center" rowspan="2">{LANG.last_login}</th>
					<th class="text-center" colspan="2">{LANG.vip}</th>
					<th class="text-center" colspan="2">{LANG.point}</th>
					<th class="text-center" colspan="2">{LANG.wallet}</th>
					<th class="text-center" width="10%" rowspan="2">{LANG.action}</th>
				</tr>
				<tr>
					<th class="text-center">{LANG.num_vip}</th>
					<th class="text-center">{LANG.revenue}</th>
					<th class="text-center">{LANG.point_in}</th>
					<th class="text-center">{LANG.point_customer}</th>
					<th class="text-center">{LANG.money_total_nap}</th>
					<th class="text-center">{LANG.money_total}</th>
				</tr>
			</thead>
			<tbody id="tbody">
				<!-- BEGIN: loop -->
					<tr>
						<td data-label="{LANG.stt}" class="text-center">{VIEW.number}</td>
						<td data-label="{LANG.fullname}">
							<a href="{VIEW.link}">{VIEW.fullname}</a> <br>
							<span class="word-wrap">{VIEW.phone}</span>
							<span class="word-wrap">{VIEW.email}</span>
						</td>
						<td data-label="{LANG.mst}" class="text-center"><b>{VIEW.mst}</b></td>
						<td data-label="{LANG.regdate}" class="text-center">{VIEW.regdate}</td>
						<td data-label="{LANG.last_login}" class="text-center">{VIEW.last_login}</td>
						<td data-label="{LANG.num_vip}" class="text-center">{VIEW.num_vip}</td>
						<td data-label="{LANG.revenue}" class="text-center">{VIEW.revenue}</td>
						<td data-label="{LANG.point_in}" class="text-center">{VIEW.point_in}</td>
						<td data-label="{LANG.point_customer}" class="text-center">{VIEW.point_customer}</td>
						<td data-label="{LANG.money_total_nap}" class="text-center">{VIEW.money_total_nap}</td>
						<td data-label="{LANG.money_total}" class="text-center">{VIEW.money_total}</td>

						<td data-label="{LANG.action}" class="text-center">
							<a href="{VIEW.linkorder}" class="btn btn-primary btn-xs link_manager"><i class="fa fa-check" aria-hidden="true"></i> {LANG.name_bill}</a>
							<a href="{VIEW.linkvip}" class="btn btn-success btn-xs link_manager"><i class="fa fa-check" aria-hidden="true"></i> {LANG.customs}</a>
							<a href="{VIEW.editUser}" class="btn btn-info btn-xs link_manager"><i class="fa fa-user" aria-hidden="true"></i> {LANG.taikhoan}</a>
						</td>
					</tr>
				<!-- END: loop -->
				<!-- BEGIN: nodata -->
				<tr>
					<td colspan="9">
						<p class="alert alert-warning">{LANG.nodata}</p>
					</td>
				</tr>
				<!-- END: nodata -->
			</tbody>
		</table>
		<!-- BEGIN: generate_page -->
		<div class="text-center">
		    {NV_GENERATE_PAGE}
		</div>
		<!-- END: generate_page -->
	</div>
<script type="text/javascript">
	$(document).ready(function($) {
		$("#sale").select2();
	});
</script>
<!-- END: main -->
