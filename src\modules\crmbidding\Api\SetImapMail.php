<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\crmbidding\Api;

use Exception;
use PDO;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use NukeViet\Module\crmbidding\Log;
use NukeViet\Module\crmbidding\LogRow;
use NukeViet\Module\crmbidding\ImapMail;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class SetImapMail implements IApi
{
    private $result;
    private $admin_id = 0;
    private $error = '';

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_SP;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'email';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $admin_id = Api::getAdminId();
        $this->admin_id = $admin_id;

        $data = isset($_POST['data']) ? json_decode($_POST['data'], true) : [];
        if (!is_array($data)) {
            return $this->result->setCode('1001')->setMessage('Data invalid to json')->getResult();
        }

        $keys_check = [
            'imap_account',
            'has_attachments',
            'id',
            'imap_path',
            'mailbox_folder',
            'is_draft',
            'mail_date',
            'organization',
            'subject',
            'sort_content',
            'from_host',
            'from_name',
            'from_address',
            'sender_host',
            'sender_name',
            'sender_address',
            'send_to',
            'send_cc',
            'send_bcc',
            'reply_to',
            'message_id',
            'text_plain',
            'text_html'
        ];

        $insert_data = [];
        foreach ($keys_check as $key) {
            if (!isset($data[$key])) {
                return $this->result->setCode('1002')->setMessage('Missing element ' . $key . ' in data')->getResult();
            }
            if ($key != 'text_plain' and $key != 'text_html') {
                $insert_data[$key] = $data[$key];
            }
        }

        $insert_data['subject'] = empty($insert_data['subject']) ? 'No subject' : $insert_data['subject'];

        try {
            // Đổi sang thao tác với utf8mb4
            $sql_array = [
                "SET character_set_client='utf8mb4'",
                "SET character_set_results='utf8mb4'",
                "SET character_set_connection='utf8mb4'"
            ];
            foreach ($sql_array as $sql) {
                $db->query($sql);
            }

            // Lấy hết email của sale
            $sql = "SELECT userid, email, first_name, last_name, username FROM " . NV_USERS_GLOBALTABLE . " WHERE active=1 AND userid IN(
                SELECT userid FROM " . NV_PREFIXLANG . "_" . $module_data . "_groups_users
            )";
            $result = $db->query($sql);
            $array_sale_emails = [];
            $array_sale_ids = [];
            while ($row = $result->fetch()) {
                $array_sale_emails[$row['email']] = $row['userid'];
                $array_sale_ids[$row['userid']] = nv_show_name_user($row['first_name'], $row['last_name'], $row['username']);
            }

            // Tìm xem sale gửi email này là ai
            $insert_data['sale_id'] = isset($array_sale_emails[$insert_data['from_address']]) ? $array_sale_emails[$insert_data['from_address']] : 0;
            $data['lead_id'] = $data['opportunitie_id'] = [];
            $data['sale_id'] = $insert_data['sale_id'];

            $data['assign_to'] = $data['status'] = $data['to_opportunitie'] = $data['to_lead'] = 0;

            // Biến này dùng để xác định có đẩy lại oppotunity không
            $repush_oppotunity = true;

            // Khi xác định được sale gửi thì tìm cơ hội và lead
            if (!empty($insert_data['sale_id'])) {
                $array_to = array_unique(array_filter(explode(',', $insert_data['send_to'])));

                if (!empty($array_to)) {
                    // Duyệt từng email nhận để tìm
                    foreach ($array_to as $to_mail) {
                        // Tìm cơ hội ứng với một địa chỉ email nhận trước
                        $sql = "SELECT id FROM " . NV_PREFIXLANG . "_" . $module_data . "_opportunities
                        WHERE caregiver_id=" . $insert_data['sale_id'] . " AND (
                            email=" . $db->quote($to_mail) . " OR FIND_IN_SET(" . $db->quote($to_mail) . ", sub_email)
                        ) ORDER BY CASE
                            WHEN active=1 THEN 2
                            WHEN active=2 THEN 1
                            ELSE 0
                        END DESC, timecreate DESC LIMIT 1";
                        $opportunitie_id = (int) $db->query($sql)->fetchColumn();
                        if (!empty($opportunitie_id) and !isset($data['opportunitie_id'][$opportunitie_id])) {
                            $data['opportunitie_id'][$opportunitie_id] = $opportunitie_id;
                        }

                        // Nếu email nhận này không tìm thấy cơ hội thì tìm sang lead
                        if (empty($opportunitie_id)) {
                            $sql = "SELECT id FROM " . NV_PREFIXLANG . "_" . $module_data . "_leads
                            WHERE caregiver_id=" . $insert_data['sale_id'] . " AND (
                                email=" . $db->quote($to_mail) . " OR FIND_IN_SET(" . $db->quote($to_mail) . ", sub_email)
                            ) ORDER BY CASE
                                WHEN active=1 THEN 1
                                ELSE 0
                            END DESC, timecreate DESC LIMIT 1";
                            $lead_id = (int) $db->query($sql)->fetchColumn();
                            if (!empty($lead_id) and !isset($data['lead_id'][$lead_id])) {
                                $data['lead_id'][$lead_id] = $lead_id;
                            }
                        }
                    }
                }
            } elseif (!empty($insert_data['from_address'])) {
                /*
                 * Không xác định được sale thì có thể là khách cũ gửi và khách mới gửi
                 * Khách mới có thể là spam
                 */

                /*
                 * Lead:
                    $nv_Lang->getModule('status0') - 'Chưa có người chăm sóc';
                    $nv_Lang->getModule('status1') - 'Đang chăm sóc';
                    $nv_Lang->getModule('status2') - 'Đã chuyển thành cơ hội kinh doanh';
                    $nv_Lang->getModule('status3') - 'Không có nhu cầu';
                    $nv_Lang->getModule('status4') - 'Leads mới';

                    Cơ hội:
                    $nv_Lang->getModule('status_opportunities1') - 'Đang chăm sóc';
                    $nv_Lang->getModule('status_opportunities2') - 'Thành công';
                    $nv_Lang->getModule('status_opportunities3') - 'Không thành công';
                    $nv_Lang->getModule('status_opportunities4') - 'Cơ hội mới tạo';
                 */

                $opportunitie = $lead = [];

                // Tìm cơ hội ứng với email này
                $sql = "SELECT id, caregiver_id, active FROM " . NV_PREFIXLANG . "_" . $module_data . "_opportunities
                WHERE (
                    email=" . $db->quote($insert_data['from_address']) . " OR FIND_IN_SET(" . $db->quote($insert_data['from_address']) . ", sub_email)
                ) ORDER BY CASE
                    WHEN updatetime>0 THEN updatetime
                    ELSE timecreate
                END DESC LIMIT 1";
                $opportunitie = $db->query($sql)->fetch();

                // Tìm lead ứng với email này (không lấy lead không có nhu cầu)
                if (empty($opportunitie)) {
                    $lead = $this->getLead($insert_data['from_address'], 0);
                    if (empty($lead)) {
                        $lead = $this->getLead($insert_data['from_address'], 1);
                    }
                    if (!empty($this->error)) {
                        return $this->result->setCode('1004')->setMessage($this->error)->getResult();
                    }
                }

                if (!empty($opportunitie)) {
                    // Cơ hội có sale chăm sóc
                    if (isset($array_sale_ids[$opportunitie['caregiver_id']])) {
                        // Gán cho sale đó xử lý
                        $data['assign_to'] = $opportunitie['caregiver_id'];
                    }
                    $data['to_opportunitie'] = $opportunitie['id'];

                    // Không đẩy lại oppotunity nếu oppotunity bị close
                    if ($opportunitie['active'] == 3) {
                        $repush_oppotunity = false;
                    } else {
                        // Đẩy lại cơ hội thì tự động loại bỏ email
                        $data['status'] = ImapMail::STATUS_EXCLUDE;
                    }
                } elseif (!empty($lead)) {
                    // Lead có sale chăm sóc
                    if (isset($array_sale_ids[$lead['caregiver_id']])) {
                        // Gán cho sale đó xử lý
                        $data['assign_to'] = $lead['caregiver_id'];
                    }
                    $data['to_lead'] = $lead['id'];
                }
            }
        } catch (Exception $e) {
            return $this->result->setCode('1004')->setMessage(print_r($e, true))->getResult();
        }

        $insert_data['lead_id'] = empty($data['lead_id']) ? '' : implode(',', $data['lead_id']);
        $insert_data['opportunitie_id'] = empty($data['opportunitie_id']) ? '' : implode(',', $data['opportunitie_id']);
        $insert_data['assign_to'] = $data['assign_to'];
        $insert_data['status'] = $data['status'];
        $insert_data['to_opportunitie'] = $data['to_opportunitie'];
        $insert_data['to_lead'] = $data['to_lead'];

        // Xác định Unique ID
        $unique_id = [];
        $unique_id[] = $data['id'];
        $unique_id[] = $data['mailbox_folder'];
        $unique_id[] = $data['subject'];
        $unique_id[] = $data['from_address'];
        $unique_id[] = $data['send_to'];
        $unique_id[] = $data['send_cc'];
        $unique_id[] = $data['send_bcc'];
        $unique_id[] = $data['message_id'];
        $unique_id = md5(implode('|', $unique_id));

        try {
            // Tìm xem email này đã ghi nhận chưa
            $sql = "SELECT email_id FROM " . NV_PREFIXLANG . "_" . $module_data . "_emails
            WHERE unique_id=" . $db->quote($unique_id) . " AND imap_account=" . $db->quote($data['imap_account']);
            $email_id = $email_id_old = $db->query($sql)->fetchColumn();

            $db->beginTransaction();

            if (empty($email_id)) {
                // Thêm mới bản ghi email
                $sql = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_emails (
                    imap_account, id, unique_id, has_attachments, imap_path, mailbox_folder, is_draft,
                    mail_date, organization, subject, sort_content, from_host, from_name,
                    from_address, sender_host, sender_name, sender_address, send_to, send_cc,
                    send_bcc, reply_to, message_id, sale_id, lead_id, opportunitie_id, assign_to, status, to_opportunitie, to_lead
                ) VALUES (
                    :imap_account, :id, " . $db->quote($unique_id) . ", :has_attachments, :imap_path, :mailbox_folder, :is_draft,
                    :mail_date, :organization, :subject, :sort_content, :from_host, :from_name,
                    :from_address, :sender_host, :sender_name, :sender_address, :send_to, :send_cc,
                    :send_bcc, :reply_to, :message_id, :sale_id, :lead_id, :opportunitie_id, :assign_to, :status, :to_opportunitie, :to_lead
                )";

                $email_id = $db->insert_id($sql, 'email_id', $insert_data);
                if (empty($email_id)) {
                    throw new \Exception('Insert DB error');
                }

                $log = [
                    'new_id' => $email_id,
                    'account' => $insert_data['imap_account'],
                    'mail_id' => $insert_data['id'],
                    'unique_id' => $unique_id,
                    'sale_id' => $insert_data['sale_id'],
                    'lead_id' => $insert_data['lead_id'],
                    'opportunitie_id' => $insert_data['opportunitie_id']
                ];
                nv_insert_logs(NV_LANG_DATA, $module_name, 'Imap:InsertRow', json_encode($log));

                // Lưu detail
                $sql = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_emails_detail (
                    email_id, text_plain, text_html
                ) VALUES (
                    :email_id, :text_plain, :text_html
                )";
                $stmt = $db->prepare($sql);
                $stmt->bindParam(':email_id', $email_id, PDO::PARAM_INT);
                $stmt->bindParam(':text_plain', $data['text_plain'], PDO::PARAM_STR);
                $stmt->bindParam(':text_html', $data['text_html'], PDO::PARAM_STR);
                $stmt->execute();

                nv_insert_logs(NV_LANG_DATA, $module_name, 'Imap:InsertDetail', $email_id);
            } else {
                // Cập nhật lại bản ghi email
                $keys_check[] = 'sale_id';
                $keys_check[] = 'lead_id';
                $keys_check[] = 'opportunitie_id';
                $keys_check[] = 'assign_to';
                $keys_check[] = 'status';
                $keys_check[] = 'to_opportunitie';
                $keys_check[] = 'to_lead';
                $sqls = [];
                foreach ($keys_check as $key) {
                    if ($key != 'imap_account' and $key != 'id' and $key != 'text_plain' and $key != 'text_html') {
                        $sqls[] = $key . '=:' . $key;
                    }
                }

                $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_emails SET " . implode(', ', $sqls) . " WHERE email_id=" . $email_id;
                $stmt = $db->prepare($sql);
                foreach ($keys_check as $key) {
                    if ($key != 'imap_account' and $key != 'id' and $key != 'text_plain' and $key != 'text_html') {
                        $stmt->bindParam(':' . $key, $insert_data[$key], PDO::PARAM_STR);
                    }
                }
                $stmt->execute();

                $log = [
                    'new_id' => $email_id,
                    'account' => $insert_data['imap_account'],
                    'mail_id' => $insert_data['id'],
                    'unique_id' => $unique_id,
                    'sale_id' => $insert_data['sale_id'],
                    'lead_id' => $insert_data['lead_id'],
                    'opportunitie_id' => $insert_data['opportunitie_id']
                ];
                nv_insert_logs(NV_LANG_DATA, $module_name, 'Imap:UpdateRow', json_encode($log));

                // Cập nhật detail
                $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_emails_detail SET
                    text_plain=:text_plain, text_html=:text_html
                WHERE email_id=:email_id";
                $stmt = $db->prepare($sql);
                $stmt->bindParam(':email_id', $email_id, PDO::PARAM_INT);
                $stmt->bindParam(':text_plain', $data['text_plain'], PDO::PARAM_STR);
                $stmt->bindParam(':text_html', $data['text_html'], PDO::PARAM_STR);
                $stmt->execute();

                nv_insert_logs(NV_LANG_DATA, $module_name, 'Imap:UpdateDetail', $email_id);
            }

            // Ghi vào lịch sử chăm sóc nếu thêm mới
            if (empty($email_id_old)) {
                if (!empty($data['opportunitie_id'])) {
                    $source = 2;
                    foreach ($data['opportunitie_id'] as $sourceid) {
                        // Ghi vào lịch sử chăm sóc cơ hội
                        $sql = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_comment (
                            source, sourceid, post_id, timecreate, update_time, note, schedule, id_mail
                        ) VALUES (
                            " . $source . ", " . $sourceid . ", " . $insert_data['sale_id'] . ",
                            " . $insert_data['mail_date'] . ", 0, " . $db->quote($insert_data['subject']) . ", 0, " . $email_id . "
                        )";
                        $db->query($sql);

                        // Cập nhật thời gian cuối chăm sóc của cơ hội
                        $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_opportunities SET last_comment=" . $insert_data['mail_date'] . ", elasticsearch=0
                        WHERE id=" . $sourceid . " AND last_comment<" . $insert_data['mail_date'];
                        $db->query($sql);
                    }

                    $log = [
                        'new_id' => $email_id,
                        'opportunitie_id' => $data['opportunitie_id']
                    ];
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Imap:CommentOpp', json_encode($log));
                }

                if (!empty($data['lead_id'])) {
                    $source = 1;
                    foreach ($data['lead_id'] as $sourceid) {
                        // Ghi vào lịch sử chăm sóc lead
                        $sql = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_comment (
                            source, sourceid, post_id, timecreate, update_time, note, schedule, id_mail
                        ) VALUES (
                            " . $source . ", " . $sourceid . ", " . $insert_data['sale_id'] . ",
                            " . $insert_data['mail_date'] . ", 0, " . $db->quote($insert_data['subject']) . ", 0, " . $email_id . "
                        )";
                        $db->query($sql);

                        // Cập nhật thời gian cuối chăm sóc lead
                        $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_leads SET last_comment=" . $insert_data['mail_date'] . ", elasticsearch=0
                        WHERE id=" . $sourceid . " AND last_comment<" . $insert_data['mail_date'];
                        $db->query($sql);
                    }

                    $log = [
                        'new_id' => $email_id,
                        'lead_id' => $data['lead_id']
                    ];
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Imap:CommentLead', json_encode($log));
                }
            }

            // Log gửi mail đến
            $log = new Log($nv_Lang->getModule('log_imap_mail'));
            $logRow = new LogRow();

            // Thời gian gửi đên
            $logRow->setMessage($nv_Lang->getModule('log_sendto_time'), nv_date('H:i d/m/Y', $insert_data['mail_date']));
            $log->add($logRow);

            // Link xem và xử lý
            $logRow->setLink($insert_data['subject'], $module_name, 'imapdetail', ('email_id=' . $email_id), 1, '1,2', $insert_data['assign_to']);
            $log->add($logRow);

            // Tự động giao email cho
            if (!empty($insert_data['assign_to'])) {
                $logRow->setMessage($nv_Lang->getModule('log_assign_mail'), $array_sale_ids[$insert_data['assign_to']]);
                $log->add($logRow);
            }

            $log = $log->toString();

            // Đẩy lại lead
            if (!empty($insert_data['to_lead'])) {
                $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_leads SET updatetime=" . NV_CURRENTTIME . ", elasticsearch=0 WHERE id=" . $insert_data['to_lead'];
                $db->query($sql);

                // Ghi lại log nếu đẩy lên
                $sql = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_alllogs (
                    userid, log_area, log_key, log_time, log_data, leads_id, oppotunities_id
                ) VALUES (
                    0, 1, 'SYS_PUSH_FROM_IMAP_MAIL', " . NV_CURRENTTIME . ",
                   " . $db->quote($log) . ", " . $insert_data['to_lead'] . ", 0
                )";
                $db->query($sql);
            }

            // Đẩy lại cơ hội
            if (!empty($insert_data['to_opportunitie']) and $repush_oppotunity) {
                $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_opportunities SET updatetime=" . NV_CURRENTTIME . ", elasticsearch=0 WHERE id=" . $insert_data['to_opportunitie'];
                $db->query($sql);

                // Ghi lại log nếu đẩy lên
                $sql = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_alllogs (
                    userid, log_area, log_key, log_time, log_data, leads_id, oppotunities_id
                ) VALUES (
                    0, 1, 'SYS_PUSH_FROM_IMAP_MAIL', " . NV_CURRENTTIME . ",
                   " . $db->quote($log) . ", 0, " . $insert_data['to_opportunitie'] . "
                )";
                $db->query($sql);
            }

            $db->commit();
        } catch (Exception $e) {
            $db->rollBack();
            trigger_error(print_r($insert_data, true));
            return $this->result->setCode('1003')->setMessage(print_r($e, true))->getResult();
        }

        $this->result->setSuccess();
        return $this->result->getResult();
    }

    /**
     * @param string $email
     * @param number $mode 0 là lấy 1 lead cập nhật mới nhất, 1 là lấy tạo mới nhất
     * @return array
     */
    private function getLead(string $email, $mode = 0)
    {
        global $db;

        $where = [];
        $where['OR'][] = [
            '=' => [
                'email' => $db->quote($email)
            ]
        ];
        $where['OR'][] = [
            'FIND_IN_SET' => [
                'sub_email' => $db->quote($email)
            ]
        ];
        $where['AND'][] = [
            '!=' => [
                'active' => 3
            ]
        ];
        if ($mode == 0) {
            $where['AND'][] = [
                '>' => [
                    'updatetime' => 0
                ]
            ];
        } else {
            $where['AND'][] = [
                '<=' => [
                    'updatetime' => 0
                ]
            ];
        }

        $array_order = [];
        if ($mode == 0) {
            $array_order['updatetime'] = 'DESC';
        } else {
            $array_order['timecreate'] = 'DESC';
        }
        $params = [
            'where' => $where,
            'order' => $array_order,
            'perpage' => 1
        ];
        $api = json_decode(nv_local_api('ListAllLeads', $params, $this->admin_id, 'crmbidding'), true);
        if (!is_array($api) or $api['status'] != 'success') {
            $this->error = is_array($api) ? ($api['message'] ?: 'Api ListAllLeads error but no message') : 'Api ListAllLeads error';
            return [];
        }

        return empty($api['data']) ? [] : array_shift($api['data']);
    }
}
