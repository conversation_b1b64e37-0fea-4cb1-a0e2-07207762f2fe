<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 2-1-2010 15:5
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('groups');
$contents = '';

// Lay danh sach nhom
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups ORDER BY weight ASC';
$result = $db->query($sql);
$groupsList = array();
$groupcount = 0;
$weight_siteus = 0;
$checkEmptyGroup = 0;
while ($row = $result->fetch()) {
    $row['weight'] = ++$weight_siteus;
    ++$checkEmptyGroup;
    $groupsList[$row['group_id']] = $row;
}

// Neu khong co nhom => chuyen den trang tao nhom
if (!$checkEmptyGroup and !$nv_Request->isset_request('add', 'get')) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&add');
}

// chuyển nhóm
if ($nv_Request->isset_request('changegroups', 'post,get')) {
    $group_id = $nv_Request->get_int('groups', 'post,get');
    $userid = $nv_Request->get_int('userid', 'post');

    if (!isset($groupsList[$group_id]) or !defined('NV_IS_SPADMIN')) {
        die('ERROR');
    }
    try {
        $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users SET group_id=' . $group_id . ' WHERE userid=' . $userid;
        $db->query($sql);
    } catch (PDOException $e) {
        die('ERROR');
    }

    $nv_Cache->delMod($module_name);
    nv_insert_logs(NV_LANG_DATA, $module_name, $nv_Lang->getModule('changeGroupsale'), 'group_id: ' . $group_id, $admin_info['userid']);
    die('OK');
}

// Thay đổi thứ tự nhóm
if ($nv_Request->isset_request('cWeight, id', 'post')) {
    $group_id = $nv_Request->get_int('id', 'post');
    $cWeight = $nv_Request->get_int('cWeight', 'post');
    if (!isset($groupsList[$group_id]) or !defined('NV_IS_SPADMIN')) {
        die('ERROR');
    }

    $cWeight = min($cWeight, sizeof($groupsList));
    if ($cWeight < 1) {
        $cWeight = 1;
    }

    $sql = 'SELECT group_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups WHERE group_id!=' . $group_id . ' ORDER BY weight ASC';
    $result = $db->query($sql);

    $weight = 0;
    while ($row = $result->fetch()) {
        ++$weight;
        if ($weight == $cWeight) {
            ++$weight;
        }
        $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups SET weight=' . $weight . ' WHERE group_id=' . $row['group_id'];
        $db->query($sql);
    }
    $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups SET weight=' . $cWeight . ' WHERE group_id=' . $group_id;
    $db->query($sql);

    $nv_Cache->delMod($module_name);
    nv_insert_logs(NV_LANG_DATA, $module_name, $nv_Lang->getModule('changeGroupWeight'), 'group_id: ' . $group_id, $admin_info['userid']);
    die('OK');
}

// Thay doi tinh trang hien thi cua nhom
if ($nv_Request->isset_request('act', 'post')) {
    $group_id = $nv_Request->get_int('act', 'post');
    if (!isset($groupsList[$group_id]) or !defined('NV_IS_SPADMIN')) {
        die('ERROR|' . $groupsList[$group_id]['act']);
    }

    $act = $groupsList[$group_id]['act'] ? 0 : 1;
    $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups SET act=' . $act . ' WHERE group_id=' . $group_id;
    $db->query($sql);

    $nv_Cache->delMod($module_name);
    nv_insert_logs(NV_LANG_DATA, $module_name, $nv_Lang->getModule('ChangeGroupAct'), 'group_id: ' . $group_id, $admin_info['userid']);
    die('OK|' . $act);
}

// Xoa nhom
if ($nv_Request->isset_request('del', 'post')) {
    $group_id = $nv_Request->get_int('del', 'post', 0);

    if (!isset($groupsList[$group_id]) or !defined('NV_IS_SPADMIN')) {
        die($nv_Lang->getModule('error_group_not_found'));
    }

    $db->query('DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups WHERE group_id = ' . $group_id);
    $db->query('DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $group_id);

    // Cập nhật lại thứ tự
    $sql = 'SELECT group_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups ORDER BY weight ASC';
    $result = $db->query($sql);

    $weight = 0;
    while ($row = $result->fetch()) {
        ++$weight;
        $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups SET weight=' . $weight . ' WHERE group_id=' . $row['group_id'];
        $db->query($sql);
    }

    $nv_Cache->delMod($module_name);
    nv_insert_logs(NV_LANG_DATA, $module_name, $nv_Lang->getModule('delGroup'), 'group_id: ' . $group_id, $admin_info['userid']);
    die('OK');
}

// Them thanh vien vao nhom
if ($nv_Request->isset_request('gid,uid', 'post')) {
    $gid = $nv_Request->get_int('gid', 'post', 0);
    $uid = $nv_Request->get_int('uid', 'post', 0);
    if (!isset($groupsList[$gid])) {
        die($nv_Lang->getModule('error_group_not_found'));
    }

    $query = $db->query('SELECT COUNT(*) FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $uid);
    if ($query->fetchColumn()) {
        $data = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id=' . $gid . ' AND userid=' . $uid)->fetchColumn();
        if (empty($data)) {
            $db->query("INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_groups_users ( group_id, userid, time_requested ) VALUES ( " . $gid . ", " . $uid . ", " . NV_CURRENTTIME . ")");
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups SET numbers = numbers+1 WHERE group_id=' . $gid);
        } else {
            die($nv_Lang->getModule('search_not_result'));
        }
    }

    $nv_Cache->delMod($module_name);
    nv_insert_logs(NV_LANG_DATA, $module_name, $nv_Lang->getModule('addMemberToGroup'), 'Member Id: ' . $uid . ' group ID: ' . $gid, $admin_info['userid']);

    die('OK');
}

// Loai thanh vien khoi nhom
if ($nv_Request->isset_request('gid,exclude', 'post')) {
    $gid = $nv_Request->get_int('gid', 'post', 0);
    $uid = $nv_Request->get_int('exclude', 'post', 0);
    if (!isset($groupsList[$gid])) {
        die($nv_Lang->getModule('error_group_not_found'));
    }

    $row = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id=' . $gid . ' AND userid=' . $uid)->fetch();
    if (!empty($row)) {
        $db->query('DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $gid . ' AND userid = ' . $uid);

        $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups SET numbers = numbers-1 WHERE group_id=' . $gid);
    } else {
        die($nv_Lang->getModule('UserNotInGroup'));
    }

    $nv_Cache->delMod($module_name);
    nv_insert_logs(NV_LANG_DATA, $module_name, $nv_Lang->getModule('exclude_user2'), 'Member Id: ' . $uid . ' group ID: ' . $gid, $admin_info['userid']);
    die('OK');
}

// Thang cap thanh vien
if ($nv_Request->isset_request('gid,promote', 'post')) {
    $gid = $nv_Request->get_int('gid', 'post', 0);
    $uid = $nv_Request->get_int('promote', 'post', 0);
    if (!isset($groupsList[$gid])) {
        die($nv_Lang->getModule('error_group_not_found'));
    }

    $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users SET is_leader = 1 WHERE group_id = ' . $gid . ' AND userid=' . $uid);

    $nv_Cache->delMod($module_name);
    nv_insert_logs(NV_LANG_DATA, $module_name, $nv_Lang->getModule('promote'), 'Member Id: ' . $uid . ' group ID: ' . $gid, $admin_info['userid']);
    die('OK');
}

// Giang cap quan tri
if ($nv_Request->isset_request('gid,demote', 'post')) {
    $gid = $nv_Request->get_int('gid', 'post', 0);
    $uid = $nv_Request->get_int('demote', 'post', 0);
    if (!isset($groupsList[$gid])) {
        die($nv_Lang->getModule('error_group_not_found'));
    }

    $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users SET is_leader = 0 WHERE group_id = ' . $gid . ' AND userid=' . $uid);

    $nv_Cache->delMod($module_name);
    nv_insert_logs(NV_LANG_DATA, $module_name, $nv_Lang->getModule('demote'), 'Member Id: ' . $uid . ' group ID: ' . $gid, $admin_info['userid']);
    die('OK');
}

$nv_Lang->setModule('nametitle', ($global_config['name_show'] == 0 ? $nv_Lang->getModule('lastname_firstname') : $nv_Lang->getModule('firstname_lastname')));

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('TEMPLATE', $global_config['module_theme']);
$xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE);
$xtpl->assign('URL_USER', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=users&' . NV_OP_VARIABLE);
$xtpl->assign('OP', $op);

// Xử lý cấu hình thành viên
if ($nv_Request->isset_request('configmember', 'post, get')) {
    $id = $nv_Request->get_int('id', 'post, get', 0);
    $gid = $nv_Request->get_int('gid', 'post', 0);
    if ($id > 0) {
        $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $gid . ' AND userid = ' . $id . '';
        $result = $db->query($sql);
        if (!$row = $result->fetch()) {
            die('ERR');
        }
        if ($nv_Request->isset_request('submit', 'post, get')) {
            $name = $nv_Request->get_title('name', 'post, get', '');
            $row['config'] = json_decode($row['config'], true);

            if ($name == 'move_leads_time' or $name == 'move_leads_method' or $name == 'extension_voicecloud' ) {
                $row['config'][$name] = $nv_Request->get_int('val', 'post, get', 0);
            } else {
                if (isset($row['config'][$name])) {
                    $row['config'][$name] = $row['config'][$name] == 1 ? 0 : 1;
                } else {
                    $row['config'][$name] = 1;
                }
            }
            if ($name == 'set_order') {
                $_config = $row['config'][$name] == 1;
            }

            $row['config'] = json_encode($row['config']);
            /**
             * By Trí:khi cập nhật ở 1 nhóm bất kỳ thì config của user đó sẽ cập nhật lại theo ở các nhóm
             */
            // $exc = $db->exec('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users SET config=' . $db->quote($row['config']) . ' WHERE userid=' . $id . ' AND group_id = ' . $gid);
            $exc = $db->exec('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users SET config=' . $db->quote($row['config']) . ' WHERE userid=' . $id);
            if ($exc) {
                // xử lý thứ tự
                if ($name == 'set_order') {
                    $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_crmbidding_groups_users ORDER BY weight ASC';
                    $_query = $db->query($_sql);
                    $array_user_id_users_all = $array_user_id_users_not_set_order = array();
                    while ($_row = $_query->fetch()) {
                        $_row['config'] = json_decode($_row['config'], true);
                        if (isset($_row['config']['set_order']) and $_row['config']['set_order'] == 1) {
                            $array_user_id_users_all[$_row['userid']] = $_row['userid'];
                        } else {
                            $array_user_id_users_not_set_order[$_row['userid']] = $_row['userid'];
                        }
                    }

                    // bỏ khỏi danh sách chia đơn
                    if ($_config == 0) {
                        $weight = 0;
                        $sql = 'SELECT weight FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE userid =' . $db->quote($id);
                        $result = $db->query($sql);
                        list ($weight) = $result->fetch(3);

                        if ($weight > 0) {
                            $sql = 'SELECT userid, weight FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE weight >' . $weight;
                            $result = $db->query($sql);
                            while (list ($u_id, $weight) = $result->fetch(3)) {
                                $weight--;
                                $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users SET weight=' . $weight . ' WHERE userid=' . intval($u_id));
                            }
                        }

                        $weight = 0;
                    } else {
                        // thêm vào danh sách chia đơn
                        $weight = $db->query('SELECT max(weight) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users')->fetchColumn();
                        $weight = intval($weight) + 1;
                    }
                    $sql = 'UPDATE ' . NV_PREFIXLANG . '_crmbidding_groups_users SET weight=' . $weight . ' WHERE userid=' . $id;
                    $db->query($sql);
                }

                nv_insert_logs(NV_LANG_DATA, $module_name, NV_LANG_DATA, "Cấu hình quyền hạn sale", $admin_info['userid']);
                die('OK');
            }
        }

        if ($nv_Request->isset_request('submit_percent', 'post, get')) {
            $ids = $nv_Request->get_array('ids', 'post', array());
            $data = array();
            foreach ($ids as $key) {
                $data[$key]['date_from'] = $nv_Request->get_title('date_from_' . $key, 'post', 0);
                $data[$key]['date_to'] = $nv_Request->get_title('date_to_' . $key, 'post', 0);

                if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $data[$key]['date_from'], $m)) {
                    $data[$key]['date_from'] = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
                } else {
                    $data[$key]['date_from'] = 0;
                }
                if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $data[$key]['date_to'], $m)) {
                    $data[$key]['date_to'] = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
                } else {
                    $data[$key]['date_to'] = 0;
                }

                $data[$key]['percent_referral'] = $nv_Request->get_float('percent_referral_' . $key, 'post', 0);
                $data[$key]['percent_order'] = $nv_Request->get_float('percent_order_' . $key, 'post', 0);
                $data[$key]['percent_suport'] = $nv_Request->get_float('percent_suport_' . $key, 'post', 0);
                $data[$key]['percent_discount'] = $nv_Request->get_float('percent_discount_' . $key, 'post', 0);
                $data[$key]['percent_wallet'] = $nv_Request->get_float('percent_wallet_' . $key, 'post', 0);
            }

            $data = json_encode($data);
            /**
             * By Trí:khi cập nhật ở 1 nhóm bất kỳ thì config của user đó sẽ cập nhật lại theo ở các nhóm
             */
            // $exc = $db->exec('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users SET config_percent=' . $db->quote($data) . ' WHERE userid=' . $id . ' AND group_id = ' . $gid);
            $exc = $db->exec('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users SET config_percent=' . $db->quote($data) . ' WHERE userid=' . $id);
            nv_insert_logs(NV_LANG_DATA, $module_name, NV_LANG_DATA, "Cấu hình % chiết khấu", $admin_info['userid']);
            nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&userlist=' . $gid);
        }

        $row['config'] = json_decode($row['config'], true);
        $row['config']['show_chart'] = $row['config']['show_chart'] == 1 ? 'checked="checked"' : '';
        $row['config']['view_leads_new'] = $row['config']['view_leads_new'] == 1 ? 'checked="checked"' : '';
        $row['config']['set_leads'] = $row['config']['set_leads'] == 1 ? 'checked="checked"' : '';
        $row['config']['set_order'] = $row['config']['set_order'] == 1 ? 'checked="checked"' : '';
        $row['config']['sale_move_leads'] = $row['config']['sale_move_leads'] == 1 ? 'checked="checked"' : '';
        $row['config']['sale_view_messages'] = $row['config']['sale_view_messages'] == 1 ? 'checked="checked"' : '';
        $row['config']['sale_view_zalo'] = $row['config']['sale_view_zalo'] == 1 ? 'checked="checked"' : '';
        $row['config']['share_messenger_zalo'] = $row['config']['share_messenger_zalo'] == 1 ? 'checked="checked"' : '';
        $row['config']['manage_econtract'] = $row['config']['manage_econtract'] == 1 ? 'checked="checked"' : '';
        $row['config']['manage_einvoice'] = $row['config']['manage_einvoice'] == 1 ? 'checked="checked"' : '';
        if (isset($row['config']['move_leads_method'])) {
            $row['config']['move_leads_method' . $row['config']['move_leads_method']] = 'checked="checked"';
        }

        $row['config_percent'] = json_decode($row['config_percent'], true);
        if (!empty($row['config_percent'])) {
            $a = 0;
            foreach ($row['config_percent'] as $key => $value) {
                $value['date_from'] = $value['date_from'] == 0 ? '' : nv_date('d/m/Y', $value['date_from']);
                $value['date_to'] = $value['date_to'] == 0 ? '' : nv_date('d/m/Y', $value['date_to']);
                $xtpl->assign('VALUE', $value);
                $xtpl->assign('KEY', $key);
                $a = $key;
                $xtpl->parse('config.loop');
            }
            $xtpl->assign('CONFIG_WEIGHT_COUNT', $a + 1);
        } else {
            $xtpl->assign('KEY', 1);
            $xtpl->assign('CONFIG_WEIGHT_COUNT', 1);
            $xtpl->parse('config.loop');
        }

        $xtpl->assign('ROW', $row);
        $xtpl->assign('URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&userlist=' . $gid);
        $xtpl->assign('ID_CONFIG', $id);
        $xtpl->assign('GID_CONFIG', $gid);
        $xtpl->parse('config');
        $xtpl->out('config');
    }
    die('');
}

// Danh sach thanh vien (AJAX)
if ($nv_Request->isset_request('listUsers', 'get')) {
    $group_id = $nv_Request->get_int('listUsers', 'get', 0);
    $page = $nv_Request->get_int('page', 'get', 1);
    $type = $nv_Request->get_title('type', 'get', '');
    $per_page = 15;
    $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=groups&listUsers=' . $group_id;

    if (!isset($groupsList[$group_id])) {
        die($nv_Lang->getModule('error_group_not_found'));
    }
    $xtpl->assign('GID', $group_id);
    $title = $groupsList[$group_id]['title'];

    $array_userid = array();
    $array_number = array();
    $group_users = array();

    // Danh sách quản trị nhóm
    if (empty($type) or $type == 'leaders') {
        $db->sqlreset()
            ->select('COUNT(*)')
            ->from(NV_PREFIXLANG . '_' . $module_data . '_groups_users')
            ->where('group_id=' . $group_id . ' AND is_leader=1');
        $array_number['leaders'] = $db->query($db->sql())
            ->fetchColumn();
        if ($array_number['leaders']) {
            $db->select('userid')
                ->limit($per_page)
                ->offset(($page - 1) * $per_page);
            $result = $db->query($db->sql());
            while ($row = $result->fetch()) {
                $group_users['leaders'][] = $row['userid'];
                $array_userid[] = $row['userid'];
            }
            $result->closeCursor();
        }
    }

    // Danh sách thành viên của nhóm
    if (empty($type) or $type == 'members') {
        $db->sqlreset()
            ->select('COUNT(*)')
            ->from(NV_PREFIXLANG . '_' . $module_data . '_groups_users')
            ->where('group_id=' . $group_id . ' AND is_leader=0');
        $array_number['members'] = $db->query($db->sql())
            ->fetchColumn();
        if ($array_number['members']) {
            $db->select('userid')
                ->limit($per_page)
                ->offset(($page - 1) * $per_page);
            $result = $db->query($db->sql());
            while ($row = $result->fetch()) {
                $group_users['members'][] = $row['userid'];
                $array_userid[] = $row['userid'];
            }
            $result->closeCursor();
        }
    }

    if (!empty($group_users)) {
        $sql = 'SELECT userid, username, first_name, last_name, email FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $array_userid) . ')';
        $result = $db->query($sql);
        $array_userid = array();
        while ($row = $result->fetch()) {
            $array_userid[$row['userid']] = $row;
        }
        foreach ($group_users as $_type => $arr_userids) {
            $xtpl->assign('PTITLE', sprintf($nv_Lang->getModule($_type . '_in_group_caption'), $title, number_format($array_number[$_type], 0, ',', '.')));
            foreach ($arr_userids as $_userid) {

                $row = $array_userid[$_userid];
                $row['full_name'] = nv_show_name_user($row['first_name'], $row['last_name'], $row['username']);
                $xtpl->assign('LOOP', $row);
                $xtpl->parse('listUsers.' . $_type . '.loop.tools');
                $xtpl->parse('listUsers.' . $_type . '.loop');
            }

            $generate_page = nv_generate_page($base_url . '&type=' . $_type, $array_number[$_type], $per_page, $page, 'true', 'false', 'nv_urldecode_ajax', 'id_' . $_type);
            if (!empty($generate_page)) {
                $xtpl->assign('PAGE', $generate_page);
                $xtpl->parse('listUsers.' . $_type . '.page');
            }
            $xtpl->parse('listUsers.' . $_type);
        }

        if (empty($type) or $type == 'leaders') {
            // Đánh số lại số thành viên
            $numberusers = 0;
            if (isset($array_number['members'])) {
                $numberusers += $array_number['members'];
            }
            if (isset($array_number['leaders'])) {
                $numberusers += $array_number['leaders'];
            }
            if ($numberusers != $groupsList[$group_id]['numbers']) {
                $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_groups SET numbers = ' . $numberusers . ' WHERE group_id=' . $group_id);
            }
        }
    }

    foreach ($groupsList as $_group_id => $_value) {
        if ($group_id != $_group_id) {
            $xtpl->assign('OPTION', array(
                'key' => $_group_id,
                'title' => $_value['title']
            ));
            $xtpl->parse('listUsers.loop_Groups');
        }
    }

    $xtpl->parse('listUsers');
    $xtpl->out('listUsers');
    exit();
}

// Danh sach thanh vien
if ($nv_Request->isset_request('userlist', 'get')) {
    $group_id = $nv_Request->get_int('userlist', 'get', 0);
    if (!isset($groupsList[$group_id])) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    }

    $filtersql = ' userid NOT IN (SELECT userid FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id=' . $group_id . ')';
    $xtpl->assign('FILTERSQL', $crypt->encrypt($filtersql, NV_CHECK_SESSION));
    $xtpl->assign('GID', $group_id);

    $xtpl->parse('userlist.adduser');
    $xtpl->parse('userlist');
    $contents = $xtpl->text('userlist');

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_admin_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

// Them + sua nhom
if ($nv_Request->isset_request('add', 'get') or $nv_Request->isset_request('edit, id', 'get')) {
    if (defined('NV_IS_SPADMIN')) {
        $post = array();
        $post['id'] = $nv_Request->get_int('id', 'get');

        if ($nv_Request->isset_request('edit', 'get')) {
            if (empty($post['id']) or !isset($groupsList[$post['id']])) {
                nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
            }

            $xtpl->assign('PTITLE', $nv_Lang->getModule('nv_admin_edit'));
            $xtpl->assign('ACTION_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&edit&id=' . $post['id']);
            $log_title = $nv_Lang->getModule('nv_admin_edit');
        } else {
            $xtpl->assign('PTITLE', $nv_Lang->getModule('nv_admin_add'));
            $xtpl->assign('ACTION_URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&add');
            $log_title = $nv_Lang->getModule('nv_admin_add');
        }

        if ($nv_Request->isset_request('save', 'post')) {
            // Sửa / Thêm full thông tin
            $post['title'] = $nv_Request->get_title('title', 'post', '', 1);
            if (empty($post['title'])) {
                die($nv_Lang->getModule('title_empty'));
            }

            // Kiểm tra trùng tên nhóm
            $stmt = $db->prepare('SELECT group_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups WHERE title LIKE :title AND group_id!= ' . intval($post['id']) . '');
            $stmt->bindParam(':title', $post['title'], PDO::PARAM_STR);
            $stmt->execute();
            if ($stmt->fetchColumn()) {
                die(sprintf($nv_Lang->getModule('error_title_exists'), $post['title']));
            }

            $post['description'] = $nv_Request->get_title('description', 'post', '', 1);
            $post['exp_time'] = $nv_Request->get_title('exp_time', 'post', '');

            if (preg_match('/^([\d]{1,2})\/([\d]{1,2})\/([\d]{4})$/', $post['exp_time'], $matches)) {
                $post['exp_time'] = mktime(23, 59, 59, $matches[2], $matches[1], $matches[3]);
            } else {
                $post['exp_time'] = 0;
            }

            $post['email'] = $nv_Request->get_title('email', 'post', '', 1);
            if (!empty($post['email']) and ($error_xemail = nv_check_valid_email($post['email'])) != '') {
                die($error_xemail);
            }

            // lấy thông tin cấu hình phân quyền
            $post['config']['access_groups_add'] = $nv_Request->get_int('access_groups_add', 'post', 0);
            $post['config']['access_groups_del'] = $nv_Request->get_int('access_groups_del', 'post', 0);
            $post['config']['type'] = $nv_Request->get_string('type', 'post', 'sale');
            $post['config'] = serialize($post['config']);

            // Thông tin của tất cả các nhóm kể cả các nhóm hệ thống
            if (isset($post['id'])) {
                if ($nv_Request->isset_request('add', 'get')) {
                    $weight = $db->query("SELECT max(weight) FROM " . NV_PREFIXLANG . '_' . $module_data . "_groups")->fetchColumn();
                    $weight = intval($weight) + 1;

                    $_sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_groups ( title, email, description, add_time, exp_time, weight, act, numbers, config) VALUES (:title, :email, :description, " . NV_CURRENTTIME . ", " . $post['exp_time'] . ", " . $weight . ", 1, 0, :config )";

                    $data_insert = array();
                    $data_insert['title'] = $post['title'];
                    $data_insert['email'] = $post['email'];
                    $data_insert['description'] = $post['description'];
                    $data_insert['config'] = $post['config'];

                    $ok = $post['id'] = $db->insert_id($_sql, 'group_id', $data_insert);
                } else {
                    // Sửa nhóm tự tạo
                    $stmt = $db->prepare("UPDATE " . NV_PREFIXLANG . '_' . $module_data . "_groups SET
                        title = :title,
                        email = :email,
                        description = :description,
                        exp_time ='" . $post['exp_time'] . "',
                        config = :config
                    WHERE group_id = " . $post['id']);

                    $stmt->bindParam(':title', $post['title'], PDO::PARAM_STR);
                    $stmt->bindParam(':email', $post['email'], PDO::PARAM_STR);
                    $stmt->bindParam(':description', $post['description'], PDO::PARAM_STR);
                    $stmt->bindParam(':config', $post['config'], PDO::PARAM_STR);

                    $ok = $stmt->execute();
                }
            }

            if ($ok) {
                $nv_Cache->delMod($module_name);
                nv_insert_logs(NV_LANG_DATA, $module_name, $log_title, 'Id: ' . $post['id'], $admin_info['userid']);
                die('OK');
            } else {
                die($nv_Lang->getModule('errorsave'));
            }
        }

        if ($nv_Request->isset_request('edit', 'get')) {
            $post = $groupsList[$post['id']];
            $post['id'] = $post['group_id'];

            if (empty($post['config'])) {
                $post['config']['access_groups_add'] = $post['config']['access_groups_del'] = 1;
                $post['config']['type'] = 'sale';
            } else {
                $post['config'] = unserialize($post['config']);
            }
        } else {
            $post['title'] = $post['email'] = $post['description'] = $post['exp_time'] = '';
            $post['id'] = 0;
            $post['config']['access_groups_add'] = $post['config']['access_groups_del'] = 1;
            $post['config']['type'] = 'sale';
        }

        $post['config']['access_groups_add'] = $post['config']['access_groups_add'] ? ' checked="checked"' : '';
        $post['config']['access_groups_del'] = $post['config']['access_groups_del'] ? ' checked="checked"' : '';

        $post['config']['check_sale'] = (isset($post['config']['type']) && $post['config']['type'] == 'sale') || !isset($post['config']['type']) ? ' checked="checked"' : '';
        $post['config']['check_marketing'] = isset($post['config']['type']) && $post['config']['type'] == 'marketing' ? ' checked="checked"' : '';

        $post['exp_time'] = $post['exp_time'] > 0 ? nv_date('d/m/Y', $post['exp_time']) : '';
        $xtpl->assign('CONFIG', $post['config']);
        $xtpl->assign('DATA', $post);

        $xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
        $xtpl->assign('NV_LANG_INTERFACE', NV_LANG_INTERFACE);

        $xtpl->parse('add.basic_infomation.email');
        $xtpl->parse('add.basic_infomation');
        $xtpl->parse('add.config');

        $xtpl->parse('add');
        $contents = $xtpl->text('add');
    } else {
        $contents = $nv_Lang->getGlobal('admin_no_allow_func');
    }

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_admin_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

// Danh sach nhom (AJAX)
if ($nv_Request->isset_request('list', 'get')) {
    $weight_op = 1;
    $allGroupCount = sizeof($groupsList);
    foreach ($groupsList as $group_id => $values) {
        $xtpl->assign('GROUP_ID', $group_id);
        $link_userlist = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;userlist=' . $group_id;

        $loop = array(
            'title' => $values['title'],
            'add_time' => nv_date('d/m/Y H:i', $values['add_time']),
            'exp_time' => !empty($values['exp_time']) ? nv_date('d/m/Y H:i', $values['exp_time']) : $nv_Lang->getGlobal('unlimited'),
            'number' => number_format($values['numbers']),
            'act' => $values['act'] ? ' checked="checked"' : '',
            'link_userlist' => $link_userlist
        );

        if (defined('NV_IS_SPADMIN')) {
            $_bg = empty($global_config['idsite']) ? 1 : $weight_op;

            for ($i = $_bg; $i <= $allGroupCount; $i++) {
                $opt = array(
                    'value' => $i,
                    'selected' => $i == ($_bg + $values['weight'] - 1) ? ' selected="selected"' : ''
                );
                $xtpl->assign('NEWWEIGHT', $opt);
                $xtpl->parse('list.loop.weight.loop');
            }
            $xtpl->parse('list.loop.weight');
            $xtpl->parse('list.loop.action.delete');
            $xtpl->parse('list.loop.action');
        } else {
            ++$weight_op;
            $xtpl->assign('WEIGHT_TEXT', $values['weight']);
            $xtpl->parse('list.loop.weight_text');

            $loop['act'] .= ' disabled="disabled"';
            $loop['title'] = $nv_Lang->getModule('level' . $group_id);
        }
        $xtpl->assign('LOOP', $loop);
        $xtpl->parse('list.loop');
    }

    if (defined('NV_IS_SPADMIN')) {
        $xtpl->parse('list.action_js');
    }

    $xtpl->parse('list');
    $contents = $xtpl->text('list');

    include NV_ROOTDIR . '/includes/header.php';
    echo ($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

if (defined('NV_IS_SPADMIN')) {
    $xtpl->parse('main.addnew');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
