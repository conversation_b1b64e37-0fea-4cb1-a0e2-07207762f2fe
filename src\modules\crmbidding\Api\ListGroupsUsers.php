<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use NukeViet\Dauthau\Condition;
use NukeViet\Dauthau\Order;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class ListGroupsUsers implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'user';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $module_config, $module_name, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $array_where = $nv_Request->get_array('where', 'post');
        if (!is_array($array_where)) {
            return $this->result->setError()
                ->setCode('2001')
                ->setMessage('Param where invalid to json')
                ->getResult();
        }

        $array_order = $nv_Request->get_array('order', 'post');
        if (!is_array($array_order)) {
            return $this->result->setError()
                ->setCode('2002')
                ->setMessage('Param order invalid to json')
                ->getResult();
        }

        // key cho phép trong where
        $keys_check = [
            'group_id',
            'userid',
            'is_leader',
            'time_requested',
            'config',
            'config_percent',
            'leads_to',
            'weight'
        ];

        // key cho phép hiển thị, cũng là key order
        $keys_view = [
            'group_id',
            'userid',
            'is_leader',
            'time_requested',
            'config',
            'config_percent',
            'leads_to',
            'weight'
        ];

        $where = array();

        if (!empty($array_where)) {
            // check $field
            foreach ($array_where as $keys) {
                foreach ($keys as $key) {
                    $operator = array_key_first($key);
                    $field = array_key_first($key[$operator]);
                    if (!in_array($field, $keys_check)) {
                        return $this->result->setError()
                            ->setCode('2003')
                            ->setMessage('Missing field ' . $field . ' in data')
                            ->getResult();
                    }
                }
            }

            $condition = new Condition();
            $where[] = $condition->toSqlString($array_where);
        }

        if (!empty($array_order)) {
            // check $field
            foreach ($array_order as $field => $type) {
                if (!in_array($field, $keys_view)) {
                    return $this->result->setError()
                        ->setCode('2004')
                        ->setMessage('Missing field ' . $field . ' in data order')
                        ->getResult();
                }
            }

            $order = new Order();
            $order_string = $order->toSqlString($array_order);
        }
        try {
            $db->sqlreset();
            $db->select(implode(',', $keys_view))->from('' . NV_PREFIXLANG . '_' . $module_data . '_groups_users');
            if (!empty($where)) {
                $db->where(implode(' AND ', $where));
            }
            $db->order($order_string);

            $sth = $db->prepare($db->sql());
            $sth->execute();
            $arr_data = $arr_data_groups = [];
            // Lấy dữ liệu admin
            while ($view = $sth->fetch()) {
                if ($view['is_leader'] == 1) {
                    $arr_data_groups[$view['userid']][] = $view['group_id'];
                    $view['array_user'] = [];
                }
                $view['arr_config'] = json_decode($view['config'], true);
                $arr_data[] = $view;
            }
            // Cập nhật array_user cho các leader
            foreach ($arr_data as &$view) {
                if (isset($arr_data_groups[$view['userid']])) {
                    $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id IN (' . implode(',', $arr_data_groups[$view['userid']]) . ') AND userid != ' . $view['userid']);
                    $arr_member = [];
                    while ($_user_info = $result->fetch()) {
                        $_user_info['arr_config'] = json_decode($_user_info['config'], true);
                        $arr_member[$_user_info['userid']] = $_user_info;
                    }
                    $view['array_user'] = $arr_member;
                }
            }

            if (!empty($arr_data)) {
                $this->result->setSuccess();
                $this->result->set('data', $arr_data);
            } else {
                $this->result->setSuccess()
                    ->setCode('4000')
                    ->setMessage($nv_Lang->getModule('api_error_400'));
            }
        } catch (PDOException $e) {
            $this->result->setError()
                ->setCode('3000')
                ->setMessage(print_r($e, true));
        }

        return $this->result->getResult();
    }
}
