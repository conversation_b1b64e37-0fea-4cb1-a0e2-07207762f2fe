<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:39:33 GMT
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Module\crmbidding\Log;
use NukeViet\Module\crmbidding\LogRow;

$row = [];
$error = [];
$leadsid = $nv_Request->get_int('leadsid', 'post,get', 0);

$params_leads = [
    'leadid' => $leadsid
];
$row = nv_local_api('GetDetailLeads', $params_leads, $admin_info['username'], 'crmbidding');
$row = json_decode($row, true);
if ($row['code'] == "0000") {
    $row = $row['data'];
} else {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
}
$lead = $row;

$caregiver_id_leads = [];
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE is_leader =1 AND userid = ' . $admin_info['userid'];
$result = $db->query($sql);
while ($row_groups_users = $result->fetch()) {
    $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $row_groups_users['group_id'] . ' AND userid != ' . $admin_info['userid'];
    $_result = $db->query($_sql);
    while ($_row_groups_users = $_result->fetch()) {
        $caregiver_id_leads[$_row_groups_users['userid']] = $_row_groups_users['userid'];
    }
}
$caregiver_id_leads[$admin_info['userid']] = $admin_info['userid'];

// check quyền chăm sóc leads khi mở
if ($row['caregiver_id'] != 0 and !in_array($row['caregiver_id'], $caregiver_id_leads) and !defined('NV_IS_SPADMIN')) {
    die('Không có quyền truy cập. Leads đã được người khác chăm sóc.');
}

if ($nv_Request->isset_request('submit', 'post')) {
    $row['name'] = nv_substr($nv_Request->get_title('name', 'post', ''),0 , 254);
    $row['phone'] = nv_substr($nv_Request->get_title('phone', 'post', ''),0, 254);
    $row['sub_phone'] = nv_substr($nv_Request->get_title('sub_phone', 'post', ''), 0, 254);
    $row['email'] = nv_substr($nv_Request->get_title('email', 'post', ''), 0, 254);
    $row['sub_email'] = $nv_Request->get_textarea('sub_email', '', NV_ALLOWED_HTML_TAGS);
    $row['address'] = nv_substr($nv_Request->get_title('address', 'post', ''), 0, 254);
    $row['tax'] = nv_substr($nv_Request->get_title('tax', 'post', ''), 0 , 254);
    $row['company_name'] = nv_substr($nv_Request->get_title('company_name', 'post', ''), 0, 254);
    $row['address_company'] = nv_substr($nv_Request->get_title('address_company', 'post', ''),0 , 254);
    $row['about'] = $nv_Request->get_textarea('about', '', NV_ALLOWED_HTML_TAGS);
    $row['siteid'] = $nv_Request->get_int('siteid', 'post', 0);
    $row['prefix_lang'] = $nv_Request->get_int('prefix_lang', 'post', 0);

    if ($row['name'] == '') {
        $error[] = $nv_Lang->getModule('error_required_name');
    }
    if ($row['phone'] == '' and $row['email'] == '') {
        $error[] = $nv_Lang->getModule('error_required_phone');
    } elseif ($row['phone'] != '' && !phonecheck($row['phone'], $row['prefix_lang'])) {
        $error[] = $nv_Lang->getModule('error_phone_number');
    } elseif ($row['email'] != '' && nv_check_valid_email($row['email']) != '') {
        $error[] = $nv_Lang->getModule('error_email');
    }

    if ($row['sub_email'] != '') {
        $row['sub_email'] = str_replace(';', ',', $row['sub_email']);
        $row['sub_email'] = str_replace("\n", ',', $row['sub_email']);
        $_arr_email = array();
        $list_mail = explode(',', $row['sub_email']);
        foreach ($list_mail as $_mail) {
            $_mail = trim($_mail);
            if (($check_valid_email = nv_check_valid_email($_mail)) != '') {
                $error[] = sprintf($nv_Lang->getModule('error_subemail'), $_mail);
            } elseif (!in_array($_mail, $_arr_email)) {
                $_arr_email[] = $_mail;
            }
        }
        $row['sub_email'] = implode(',', $_arr_email);
    }

    if (empty($error)) {
        try {
            $other_data = [
                'leadsid' => $leadsid,
                'user_id' => $row['user_id'],
                'label' => $row['label'],
                'sub_phone' => $row['sub_phone'],
                'sub_email' => $row['sub_email'],
                'address' => $row['address'],
                'tax' => $row['tax'],
                'company_name' => $row['company_name'],
                'address_company' => $row['address_company'],
                'affilacate_id' => $row['affilacate_id'],
                'caregiver_id' => $row['caregiver_id'],
                'about' => $row['about'],
                'source_leads' => $row['source_leads'],
                'convert_contact' => $row['convert_contact'],
                'convert_organization' => $row['convert_organization']
            ];

            $_params_insert = [
                'name' => $row['name'],
                'phone' => $row['phone'],
                'email' => $row['email'],
                'admin_id' => $admin_info['userid'],
                'siteid' => $row['siteid'],
                'prefix_lang' => $row['prefix_lang'],
                'otherdata' => $other_data
            ];
            $status_insert = nv_local_api('CreateOpportunities', $_params_insert, $admin_info['username'], 'crmbidding');
            $status_insert = json_decode($status_insert, true);
            if ($status_insert['code'] == "0000") {
                nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=opportunities_info&id=' . $status_insert['OpportunitiesID']);
            } else {
                $error[] = $status_insert['message'];
            }
        } catch (PDOException $e) {
            trigger_error($e);
            die($nv_Lang->getModule('error_request'));
        }
    }
}
$row['sub_email'] = nv_htmlspecialchars(nv_br2nl($row['sub_email']));

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('LEADSID', $leadsid);
$xtpl->assign('ROW', $row);

if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

foreach ($array_lang as $key => $value) {
    if ($key == $row['prefix_lang']) {
        $row['title_lang'] = $value;
    }

    $xtpl->assign('OPTION', [
        'key' => $key,
        'title' => $value,
        'selected' => ($key == $row['prefix_lang']) ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.select_prefix_lang');
}

foreach ($array_site as $key => $value) {
    $xtpl->assign('OPTION', [
        'key' => $key,
        'title' => $value,
        'selected' => ($key == $row['siteid']) ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.select_siteid');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');
die(nv_admin_theme($contents, false));
