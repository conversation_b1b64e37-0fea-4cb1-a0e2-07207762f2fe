<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>

<div class="panel">
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}"> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}"> <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}">
        <div class="form-group">
            <label>{LANG.time_from}:</label> <input class="form-control w100 uidatepicker" type="text" value="{ARRAY_SEARCH.time_from}" name="time_from" maxlength="10" autocomplete="off">
        </div>
        <div class="form-group">
            <label>{LANG.to}:</label> <input class="form-control w100 uidatepicker" type="text" value="{ARRAY_SEARCH.time_to}" name="time_to" maxlength="10" autocomplete="off">
        </div>
        <div class="form-group">
            <input class="btn btn-primary" type="submit" value="{LANG.search_submit}">
        </div>
    </form>
</div>

<div class="row">
    <div class="col-md-11">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover text-center">
                <caption>{LANG.top_10_point_in}</caption>
                <thead>
                    <tr>
                        <th class="w50 text-center">{LANG.number}</th>
                        <th class="w100 text-center">{LANG.username}</th>
                        <th class="w100 text-center">{LANG.point_in_total}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: point_in -->
                    <tr>
                        <td>{POINT_IN.stt}</td>
                        <td>{POINT_IN.fullname}</td>
                        <td><a href="#" class="view" data-href="{POINT_IN.link_point_in}">{POINT_IN.s_point_in}</a></td>
                    </tr>
                    <!-- END: point_in -->
                </tbody>
            </table>
        </div>
    </div>

    <div class="col-md-11 col-md-offset-1">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover text-center">
                <caption>{LANG.top_10_point_out}</caption>
                <thead>
                    <tr>
                        <th class="w50 text-center">{LANG.number}</th>
                        <th class="w100 text-center">{LANG.username}</th>
                        <th class="w100 text-center">{LANG.point_out}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: point_out -->
                    <tr>
                        <td>{POINT_OUT.stt}</td>
                        <td>{POINT_OUT.fullname}</td>
                        <td><a href="#" class="view" data-href="{POINT_OUT.link_point_out}">{POINT_OUT.s_point_out}</a></td>
                    </tr>
                    <!-- END: point_out -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-11">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover">
                <caption>{LANG.static_type_transaction}</caption>
                <thead>
                    <tr>
                        <th class="w50 text-center">{LANG.number}</th>
                        <th class="text-center">{LANG.type_transaction}</th>
                        <th class="w100 text-center">{LANG.point_out}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: type_transaction -->
                    <tr>
                        <td>{TYPE_TRANSACTION.stt}</td>
                        <td>{TYPE_TRANSACTION.title}</td>
                        <td class="text-center"> <a href="#" class="view" data-href="{TYPE_TRANSACTION.link}">{TYPE_TRANSACTION.s_total_point}</a></td>
                    </tr>
                    <!-- END: type_transaction -->
                </tbody>
            </table>
        </div>
    </div>

    <div class="col-md-11 col-md-offset-1">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover">
                <caption>{LANG.static}</caption>
                <thead>
                    <tr>
                        <th>{LANG.type_transaction}</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{LANG.type_transaction_max_use_sum}</td>
                        <td>{TYPE_TRANSACTION_MAX_USE_SUM}</td>
                    </tr>
                    <tr>
                        <td>{LANG.type_transaction_min_use_sum}</td>
                        <td>{TYPE_TRANSACTION_MIN_USE_SUM}</td>
                    </tr>
                    <tr>
                        <td>{LANG.type_transaction_max_use_count}</td>
                        <td>{TYPE_TRANSACTION_MAX_USE_COUNT}</td>
                    </tr>
                    <tr>
                        <td>{LANG.type_transaction_min_use_count}</td>
                        <td>{TYPE_TRANSACTION_MIN_USE_COUNT}</td>
                    </tr>
                    <tr>
                        <td>{LANG.sum_reward}</td>
                        <td>{SUM_REWARD}</td>
                    </tr>
                    <tr>
                        <td>{LANG.sum_expired}</td>
                        <td>{SUM_EXPIRED}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function() {
        $('.view').click(function() {
            var href = $(this).attr('data-href');
            nv_open_browse(href, 'NVImg', 1200, 600, 'resizable=no,scrollbars=no,toolbar=no,location=no,status=no');
            return;
        });
        $('.uidatepicker').datepicker({ showOn : "both", dateFormat : "dd/mm/yy", changeMonth : true, changeYear : true, showOtherMonths : true, buttonImage : nv_base_siteurl + "assets/images/calendar.gif", buttonImageOnly : true });
    });
</script>
<!-- END: main -->
