<!-- BEGIN: main -->

<div class="alert alert-info">
    <strong>Thống kê trên toàn hệ thống:</strong>
        <ul>
            <li>{LANG.num_activate}: {num_activate}</li>
            <li>{LANG.num_left}: {num_left}</li>
            <li>{LANG.money_left}: {money_left}</li>
        </ul>
</div>

<script type="text/javascript" src="{NV_BASE_SITEURL}themes/dauthau/plugins/daterangepicker/moment.min.js"></script>
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/dauthau/plugins/daterangepicker/daterangepicker.js"></script>
<link type="text/css" rel="stylesheet" href="{NV_BASE_SITEURL}themes/dauthau/plugins/daterangepicker/daterangepicker.css" />


<div class="panel">
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline" id="ltablesearch">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}">
        <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
        <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}">
        <label>{LANG.select_time}:</label>
        <div class="form-group">
            <div class="col-xs-24">
                <input type="hidden" name="sfrom" value="{FROM}" data-default="{FROM_DEFAULT}" />
                <input type="hidden" name="sto" value="{TO}" data-default="{TO_DEFAULT}" />
                <input class="form-control search_range" type="text" value="{FROM} - {TO}">
            </div>
        </div>

        <div class="form-group">
            <input class="btn btn-primary" type="submit" value="{LANG.search_submit}">
        </div>
    </form>
</div>


<div class="well">
    <strong>{lang_static_renewal}</strong>
    <div class="row">
        <ul>
            <li>{LANG.money_out}: {money_out}</li>
            <li>{LANG.money_in}: {money_in}</li>
        </ul>
    </div>
</div>

<style>
table thead tr td, table thead tr th {
    vertical-align: middle !important;
}
</style>
<script type="text/javascript">
var formObject = $("[id=ltablesearch]");
    function bl_setDaterangepicker(_options) {
        // Menu khoảng tìm kiếm
        var ranges = {};
        ranges['{LANG.this_month}'] = [ moment().startOf('month'), moment().endOf('month') ];
        ranges['{LANG.last_3_months}'] = [ moment().startOf('quarter'), moment().endOf('quarter') ];
        ranges['{LANG.this_year}'] = [ moment().startOf('year'), moment().endOf('year') ];
        ranges['{LANG.last_all_days}'] = [ moment('{MINDATE}', "DD/MM/YYYY"), moment() ];

        var calendar_options = { showDropdowns : true, locale : { customRangeLabel : '{LANG.custom_range}', format : 'DD/MM/YYYY', help : '' }, ranges : ranges, startDate : moment().subtract(14, 'days'), endDate : moment(), opens : 'right', drops : "auto", alwaysShowCalendars : true, };

        $.extend(calendar_options, _options);

        $(".search_range", formObject).daterangepicker(calendar_options, function(start, end, label) {
            $("[name=sfrom]", formObject).val(start.format('DD/MM/YYYY'));
            $("[name=sto]", formObject).val(end.format('DD/MM/YYYY'))
        });
    }
    $(function() {
        bl_setDaterangepicker({ startDate : $("[name=sfrom]", formObject).val(), endDate : $("[name=sto]", formObject).val() });
    });
</script>
<!-- END: main -->
