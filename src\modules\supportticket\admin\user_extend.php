<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$error = $userOptions = $row = [];
$userid_check = $nv_Request->get_int('userid', 'post,get', 0);
$config_for = $nv_Request->get_int('config_for', 'post,get', 0);
if (!defined('NV_IS_SPADMIN')) {
    $userid_check = $config_for = 0;
}

$userid = ($userid_check == 0 && $config_for == 0) ? $admin_info['userid'] : $userid_check;
$user_extend = get_user_extend($userid);

if ($nv_Request->isset_request('submit', 'post')) {
    $row['prefix_state'] = $nv_Request->get_title('prefix_state', 'post', 'off');
    $row['prefix_vi'] = nv_substr($nv_Request->get_title('prefix_vi', 'post', ''), 0, 100);
    $row['prefix_en'] = nv_substr($nv_Request->get_title('prefix_en', 'post', ''), 0, 100);
    $row['position_state'] = $nv_Request->get_title('position_state', 'post', 'off');
    $row['position_vi'] = nv_substr($nv_Request->get_title('position_vi', 'post', ''), 0, 100);
    $row['position_en'] = nv_substr($nv_Request->get_title('position_en', 'post', ''), 0, 100);
    $row['signature_state'] = $nv_Request->get_title('signature_state', 'post', 'off');
    $row['signature_vi'] = $nv_Request->get_editor('signature_vi', '', NV_ALLOWED_HTML_TAGS);
    $row['signature_en'] = $nv_Request->get_editor('signature_en', '', NV_ALLOWED_HTML_TAGS);
    $row['userid'] = $nv_Request->get_int('userid', 'post', 0);
    if ($row['prefix_state'] === 'on' && (empty($row['prefix_vi']) || empty($row['prefix_en']))) {
        $error[] = $nv_Lang->getModule('error_required_prefix');
    }
    if ($row['position_state'] === 'on' && (empty($row['position_vi']) || empty($row['position_en']))) {
        $error[] = $nv_Lang->getModule('error_required_position');
    }
    if ($row['signature_state'] === 'on' && (empty($row['signature_vi']) || empty($row['signature_en']))) {
        $error[] = $nv_Lang->getModule('error_required_signature');
    }

    if (empty($error)) {
        try {
            if (empty($user_extend)) {
                $stmt = $db->prepare('INSERT INTO ' . $db_config['prefix'] . '_' . $module_data . '_user_extend
                    (userid, prefix_state, prefix_vi, prefix_en, position_state, position_vi, position_en, signature_state, signature_vi, signature_en)
                    VALUES (' . $row['userid'] . ', :prefix_state, :prefix_vi, :prefix_en, :position_state, :position_vi, :position_en, :signature_state, :signature_vi, :signature_en)'
                );
            } else {
                $stmt = $db->prepare('UPDATE ' . $db_config['prefix'] . '_' . $module_data . '_user_extend
                SET prefix_state = :prefix_state, prefix_vi = :prefix_vi, prefix_en = :prefix_en, position_state = :position_state,
                position_vi = :position_vi, position_en = :position_en, signature_state = :signature_state, signature_vi = :signature_vi, signature_en = :signature_en
                WHERE userid=' . $row['userid']);
            }
            $stmt->bindParam(':prefix_state', $row['prefix_state'], PDO::PARAM_STR);
            $stmt->bindParam(':prefix_vi', $row['prefix_vi'], PDO::PARAM_STR);
            $stmt->bindParam(':prefix_en', $row['prefix_en'], PDO::PARAM_STR);
            $stmt->bindParam(':position_state', $row['position_state'], PDO::PARAM_STR);
            $stmt->bindParam(':position_vi', $row['position_vi'], PDO::PARAM_STR);
            $stmt->bindParam(':position_en', $row['position_en'], PDO::PARAM_STR);
            $stmt->bindParam(':signature_state', $row['signature_state'], PDO::PARAM_STR);
            $stmt->bindParam(':signature_vi', $row['signature_vi'], PDO::PARAM_STR);
            $stmt->bindParam(':signature_en', $row['signature_en'], PDO::PARAM_STR);

            $exc = $stmt->execute();
            if ($exc) {
                $nv_Cache->delMod($module_name);
                if (empty($user_extend)) {
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Thêm thông tin mở rộng user', ' ', $admin_info['userid']);
                } else {
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Sửa thông tin mở rộng cho user', 'ID: ' . $admin_info['userid'], $admin_info['userid']);
                }
                if ($config_for != 0 && $userid_check != 0) {
                    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&userid='. $userid_check .'&config_for=' .$config_for);
                } else {
                    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
                }
            }
        } catch(PDOException $e) {
            trigger_error($e->getMessage());
        }
    }
} elseif (!empty($user_extend)) {
    $row = $user_extend;
} else {
    $row['prefix_state'] = 'off';
    $row['signature_vi'] = '';
    $row['prefix_en'] = '';
    $row['position_state'] = 'off';
    $row['position_vi'] = '';
    $row['position_en'] = '';
    $row['signature_state'] = 'off';
    $row['signature_vi'] = '';
    $row['signature_en'] = '';
}

$row['signature_vi'] = nv_htmlspecialchars($row['signature_vi']);
$row['signature_en'] = nv_htmlspecialchars($row['signature_en']);
$row['prefix_state'] = $row['prefix_state'] == 'on' ? 'checked="checked"' : '';
$row['position_state'] = $row['position_state'] == 'on' ? 'checked="checked"' : '';
$row['signature_state'] = $row['signature_state'] == 'on' ? 'checked="checked"' : '';

// Lấy danh sách admin
foreach ($array_admin_users as $value) {
    $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
    $userOptions[] = [
        'key' => $value['userid'],
        'name' => $value['username'] . ' (' . $fullname . ')',
        'selected' => $value['userid'] == $userid_check ? true : false
    ];
}

$form_action = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op;
$tpl = new \NukeViet\Template\NVSmarty();
$tpl->setTemplateDir(NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$tpl->assign('LANG', $nv_Lang);
$tpl->assign('USER_OPTIONS', $userOptions);
$tpl->assign('SHOW_USER', defined('NV_IS_SPADMIN'));
$tpl->assign('CHECKED_CONFIG', $config_for == 1 ? 'checked' : '');
$tpl->assign('BASE_URL', $base_url);
$tpl->assign('FORM_ACTION', $form_action);
$tpl->assign('USER_ID', $userid);
$tpl->assign('DATA', $row);
$tpl->assign('ERROR', implode('<br />', $error));

if (!defined('NV_EDITOR')) {
    define('NV_EDITOR', 'ckeditor5-classic');
}
require_once NV_ROOTDIR . '/' . NV_EDITORSDIR . '/' . NV_EDITOR . '/nv.php';

if (nv_function_exists('nv_aleditor')) {
    $signature_vi_editor = nv_aleditor('signature_vi', '100%', '300px', $row['signature_vi']);
    $tpl->assign('SIGNATURE_VI', $signature_vi_editor);
    $signature_en_editor = nv_aleditor('signature_en', '100%', '300px', $row['signature_en']);
    $tpl->assign('SIGNATURE_EN', $signature_en_editor);
}

$contents = $tpl->fetch('user_extend.tpl');
$page_title = $nv_Lang->getModule('user_extend') . ': ' . get_user_info($userid)['fullname'];

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
