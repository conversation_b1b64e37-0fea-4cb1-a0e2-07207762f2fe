<!-- BEGIN: main -->
<script src="{NV_BASE_SITEURL}themes/{TEMPLATE}/images/{MODULE_FILE}/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js"></script>
<script src="{NV_BASE_SITEURL}themes/{TEMPLATE}/images/{MODULE_FILE}/plugins/bootstrap-datepicker/locales/bootstrap-datepicker.{NV_LANG_INTERFACE}.min.js"></script>
<link rel="stylesheet" type="text/css" href="{NV_BASE_SITEURL}themes/{TEMPLATE}/images/{MODULE_FILE}/plugins/bootstrap-datepicker/css/bootstrap-datepicker.min.css">
<link rel="stylesheet" type="text/css" href="{NV_BASE_SITEURL}themes/{TEMPLATE}/images/{MODULE_FILE}/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css">
<link type="text/css" href="{NV_BASE_SITEURL}themes/{TEMPLATE}/images/{MODULE_FILE}/plugins/apexcharts/apexcharts.css" rel="stylesheet">
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/{TEMPLATE}/images/{MODULE_FILE}/plugins/apexcharts/apexcharts.min.js"></script>

<form action="{NV_BASE_ADMINURL}index.php" method="get">
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}">
    <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
    <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}">
    <div class="imap-stat-form">
        <div class="imap-stat-form-inner">
            <div class="form-group">
                <div class="input-daterange input-group">
                    <input type="text" class="form-control" name="f" autocomplete="off" value="{SEARCH.from}">
                    <span class="input-group-addon">{LANG.to}</span>
                    <input type="text" class="form-control" name="t" autocomplete="off" value="{SEARCH.to}">
                </div>
            </div>
            <div class="text-center">
                <select class="form-control" name="a">
                    <option value="0">{LANG.imapstat_all_account}</option>
                    <!-- BEGIN: account -->
                    <option value="{ACCOUNT.key}"{ACCOUNT.selected}>{ACCOUNT.title}</option>
                    <!-- END: account -->
                </select>
            </div>
        </div>
    </div>
    <div class="form-group text-center">
        <button type="submit" class="btn btn-primary">
            <i class="fa fa-pie-chart" aria-hidden="true"></i> {LANG.view_static_title}
        </button>
    </div>
</form>
<script type="text/javascript">
$(document).ready(function() {
    $('.input-daterange').datepicker({
        format: "dd-mm-yyyy",
        language: "{NV_LANG_INTERFACE}",
        autoclose: true
    });
});
</script>
<!-- BEGIN: chart -->
<div id="chart-imapmail"></div>
<script type="text/javascript">
$(document).ready(function() {
    var options = {
        series: [{
            name: '',
            data: {SERIES_DATA}
        }],
        chart: {
            height: 350,
            type: 'bar',
            toolbar: {
                show: false
            }
        },
        plotOptions: {
            bar: {
                borderRadius: 2,
                dataLabels: {
                    position: 'top',
                },
            }
        },
        dataLabels: {
            enabled: true,
            offsetY: -20,
            style: {
                colors: ["#304758"]
            }
        },
        xaxis: {
            categories: {CATEGORIES},
            position: 'top',
            axisBorder: {
                show: false
            },
            axisTicks: {
                show: false
            },
            crosshairs: {
                fill: {
                    type: 'gradient',
                    gradient: {
                        colorFrom: '#D8E3F0',
                        colorTo: '#BED1E6',
                        stops: [0, 100],
                        opacityFrom: 0.4,
                        opacityTo: 0.5,
                    }
                }
            },
            tooltip: {
                enabled: true,
            },
            labels: {
                offsetY: -2
            }
        },
        yaxis: {
            axisBorder: {
                show: false
            },
            axisTicks: {
                show: false,
            },
            labels: {
                show: false,
            }
        },
    };

    var chart = new ApexCharts(document.querySelector("#chart-imapmail"), options);
    chart.render();
});
</script>
<!-- END: chart -->
<!-- BEGIN: nodata -->
<div class="alert alert-info">{LANG.no_data_for_chart}</div>
<!-- END: nodata -->
<!-- BEGIN: others -->
<h1>{LANG.imapstat_others}</h1>
<div class="panel panel-default">
    <div class="list-group">
        <!-- BEGIN: loop -->
        <div class="list-group-item">
            <div class="row">
                <div class="col-xs-10 col-sm-8 col-md-6 col-lg-3">
                    <div class="text-right">
                        <label class="label label-danger">{ROW.nummail}</label>
                    </div>
                </div>
                <div class="col-xs-14 col-sm-16 col-md-18 col-lg-21">
                    {ROW.from_address}
                </div>
            </div>
        </div>
        <!-- END: loop -->
    </div>
</div>
<!-- END: others -->
<!-- END: main -->
