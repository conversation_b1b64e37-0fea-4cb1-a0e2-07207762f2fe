<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Apr 20, 2010 10:47:41 AM
 */

if (!defined('NV_IS_MOD_ELINK')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

$page_title = $module_info['site_title'];
$description = $keywords = 'no';

if (!defined('NV_IS_USER')) {
    $link_redirect = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    nv_redirect_location($link_redirect);
}

// Load doanh thu tổng quan
if (isset($_POST['getstatovervieworders']) and $nv_Request->get_title('getstatovervieworders', 'post', '') == NV_CHECK_SESSION) {
    $respon = [
        'message' => '',
        'updated_time' => '',
        'content' => ''
    ];
    $reload = $nv_Request->get_absint('reload', 'post', 0);

    $cacheFile = 'statoverview' . $user_info['userid'] . '_' . NV_LANG_DATA . '_' . NV_CACHE_PREFIX . '.cache';
    $cacheTTL = 3600; // 1 tiếng cache
    if (!$reload and ($cache = $nv_Cache->getItem($module_name, $cacheFile, $cacheTTL)) != false) {
        $respon['content'] = $cache;
        $respon['updated_time'] = nv_date('H:i d/m/Y', filemtime(NV_ROOTDIR . '/' . NV_CACHEDIR . '/' . $module_name . '/' . $cacheFile));
    } else {
        $stat = [];

        // Load site DauThau.info
        $site = $global_array_sites[1];
        $api = new DoApi(NV_SERVER_PROTOCOL . '://' . $site['sitedomain'] . '/api.php', $site['api_key'], $site['api_secret']);
        $api->setModule('bidding')
        ->setLang('vi')
        ->setAction('GetStatOverviewOrders')
        ->setData([
            'userid' => $user_info['userid'],
            'max_official_promo_value' => $global_array_config['max_official_promo_value'],
            'discount_official' => $global_array_config['discount_official'],
            'max_freelance_promo_value' => $global_array_config['max_freelance_promo_value'],
            'discount_freelance' => $global_array_config['discount_freelance'],
        ]);
        $result_api = $api->execute();
        $error = $api->getError();

        if (!empty($error)) {
            $respon['message'] = $error;
            nv_jsonOutput($respon);
        }
        if ($result_api['status'] != 'success') {
            $respon['message'] = $result_api['message'] ?: 'Api error with no respon';
            nv_jsonOutput($respon);
        }
        $stat = $result_api['data'];

        // Load site DauThau.Net
        $site = $global_array_sites[2];
        $api = new DoApi(NV_SERVER_PROTOCOL . '://' . $site['sitedomain'] . '/api.php', $site['api_key'], $site['api_secret']);
        $api->setModule('dn')
        ->setLang('vi')
        ->setAction('GetStatOverviewOrders')
        ->setData([
            'userid' => $user_info['userid'],
            'max_official_promo_value' => $global_array_config['max_official_promo_value'],
            'discount_official' => $global_array_config['discount_official'],
            'max_freelance_promo_value' => $global_array_config['max_freelance_promo_value'],
            'discount_freelance' => $global_array_config['discount_freelance'],
        ]);
        $result_api = $api->execute();
        $error = $api->getError();

        if (!empty($error)) {
            $respon['message'] = $error;
            nv_jsonOutput($respon);
        }
        if ($result_api['status'] != 'success') {
            $respon['message'] = $result_api['message'] ?: 'Api error with no respon';
            nv_jsonOutput($respon);
        }

        foreach ($stat as $key => $val) {
            $stat[$key] += $result_api['data'][$key];
        }
        if ($stat['promotion_total_paid_orders'] != $user_info['total_revenue']) {
            $db->exec('UPDATE ' . NV_USERS_GLOBALTABLE . ' SET total_revenue = ' . $db->quote($stat['promotion_total_paid_orders']) . ' WHERE userid = ' . $user_info['userid']);
        }

        $respon['content'] = nv_elink_theme_overview_orders($stat);
        $nv_Cache->setItem($module_name, $cacheFile, $respon['content'], $cacheTTL);

        $respon['updated_time'] = nv_date('H:i d/m/Y', NV_CURRENTTIME);
    }

    nv_jsonOutput($respon);
}

$array = [];
$array['affiliate_enabled'] = nv_user_in_groups($global_array_config['affiliate_code_groups']);
$array['promotion_enabled'] = nv_user_in_groups($global_array_config['groups_allowed_string']);

// Một số thống kê Affiliate Code
if ($array['affiliate_enabled']) {
    $array['affiliate_code'] = $user_info['username'];
    $array['affiliate_link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=affiliate';
    $array['affiliate_link_users'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=affiliate-users';
    $array['affiliate_visitors'] = $db->query("SELECT scount FROM " . $db_config['prefix'] . "_" . $module_data . "_statistics WHERE stype=0 AND sval=" . $db->quote($array['affiliate_code']) . " AND islink=''")->fetchColumn();
    $array['affiliate_users'] = $db->query("SELECT COUNT(tb1.pre_uid) FROM " . $db_config['prefix'] . "_" . $module_data . "_affiliate_set tb1, " . NV_USERS_GLOBALTABLE . " tb2 WHERE tb1.pre_uid=tb2.userid AND tb1.pri_uid=" . $user_info['userid'])->fetchColumn();
}
// Một số thống kê Promotion Code
if ($array['promotion_enabled']) {
    $array['promotion_link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=promotion';
    $array['promotion_number'] = $db->query("SELECT COUNT(promo_code) FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE userid=" . $user_info['userid'])->fetchColumn();
    $array['promotion_visitors'] = $db->query("SELECT SUM(scount) FROM " . $db_config['prefix'] . "_" . $module_data . "_statistics WHERE stype=1 AND sval IN(SELECT promo_code FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE userid=" . $user_info['userid'] . ") AND islink=''")->fetchColumn();

    // Số lượt sử dụng
    $sql = "SELECT COUNT(tb1.id) FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code_use tb1, " . NV_USERS_GLOBALTABLE . " tb2
    WHERE tb1.promo_id IN(SELECT promo_id FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE userid=" . $user_info['userid'] . ") AND tb1.status=1
    AND tb1.use_userid=tb2.userid";
    $array['promotion_used'] = $db->query($sql)->fetchColumn();

}

// 15 link đích truy cập gần đây nhất
$array['statistics'] = [];
$array['statistics_link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=affiliate-links&amp;t=1';
$sql = "SELECT slink, scount, last_update FROM " . $db_config['prefix'] . "_" . $module_data . "_statistics
WHERE (
    (stype=0 AND sval=" . $db->quote($array['affiliate_code']) . ")
    OR (stype=1 AND sval IN (SELECT promo_code FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE userid=" . $user_info['userid'] . "))
) AND islink!='' AND referornot=0 ORDER BY last_update DESC LIMIT  15";
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $row['slink'] = nv_htmlspecialchars($row['slink']);
    $array['statistics'][] = $row;
}

// 15 link nguồn truy cập gần đây nhất
$array['statistics_ref'] = [];
$array['statistics_link_ref'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=affiliate-links&amp;t=2';
$sql = "SELECT sreflink, scount, last_update FROM " . $db_config['prefix'] . "_" . $module_data . "_statistics
WHERE (
    (stype=0 AND sval=" . $db->quote($array['affiliate_code']) . ")
    OR (stype=1 AND sval IN (SELECT promo_code FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE userid=" . $user_info['userid'] . "))
) AND islink!='' AND referornot=1 ORDER BY last_update DESC LIMIT  15";
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $row['slink'] = $row['sreflink'];
    $row['slink'] = nv_htmlspecialchars($row['slink']);
    $array['statistics_ref'][] = $row;
}

$array['orders_link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=all-orders';

$contents = nv_elink_theme_main($array);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
