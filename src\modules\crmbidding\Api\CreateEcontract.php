<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use NukeViet\Api\DoApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CreateEcontract implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'econtracts';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $array_site, $array_user_id_users, $nv_Lang, $module_config;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $error = '';
        $error_code = '0000';

        // Xử lý xử liệu đầu vào
        $order_id = $nv_Request->get_int('order_id', 'post', 0);
        if (empty($order_id)) {
            $error = 'Mã đơn hàng không hợp lệ';
            $error_code = '4000';
        }
        // Kiểm tra nếu đơn hàng này đã có hợp đồng rồi thì chuyển hướng đến chi tiết của hợp đồng
        $check_econtract_id = $db->query('SELECT econtract_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE order_id=' . $order_id)->fetchColumn();
        if ($check_econtract_id) {
            $error = 'Đã có hợp đồng cho đơn hàng này';
            $error_code = '4001';
        }

        $order_data = [];
        // Lấy thông tin đơn hàng
        $where = [];
        $where['AND'] = [
            ['=' => ['id' => $order_id]]
        ];
        $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
        $api_dtinfo->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingOrder')
            ->setData([
                'where' => $where,
                'show_customs_log' => 1
            ]);
        $_result = $api_dtinfo->execute();
        $error = $api_dtinfo->getError();
        if (empty($error) and $_result['status'] == 'success' and !empty($_result['data'])) {
            if (isset($_result['data'][$order_id])) {
                $order_data = $_result['data'][$order_id];
            }
        }
        if (empty($order_data)) {
            $error = 'Không thấy thông tin đơn hàng';
            $error_code = '4003';
        }

        if (empty($error)) {
            $order_data['code'] = sprintf('BDH%010s', $order_data['id']);
            // Khởi tạo $econtract mặc định rỗng (tạo mới)
            $econtract = [
                'current_version' => 0,
                'tax_code' => '',
                'c_name' => '',
                'representative' => '',
                'jobtitle' => '',
                'c_address' => '',
                'phone' => '',
                'email' => '',
                'authorization_letter' => '',
                'receiver' => '',
                'receiver_phone' => '',
                'receiver_address' => '',
                'cccd' => '',
                'customer_type' => 1,
                'contract_no' => '',
                'contract_path' => '',
                'status' => 1,
                'term_changed' => 0,
                'term_changed_notes' => '',
                'stage' => 1,
                'stage_next' => 0,
                'uploader_id' => 0,
                'customer_id' => 0,
                'created_at' => 0,
                'updated_at' => 0,
            ];

            $econtract['c_name'] = $order_data['customs_log'][0]['name'] ?? '';
            $econtract['c_address'] = $order_data['customs_log'][0]['address_org'] ?? '';
            $econtract['tax_code'] = $order_data['customs_log'][0]['tax'] ?? '';
            $econtract['phone'] = $order_data['customs_log'][0]['phone'] ?? '';
            $econtract['email'] = $order_data['customs_log'][0]['email'] ?? '';
            $econtract['customer_id'] = $order_data['userid'];
            $econtract['uploader_id'] = $order_data['caregiver_id']; // Lưu id của người tạo hợp đồng chính là người chăm sóc đơn hàng
            $econtract['username'] = $econtract['fullname'] = 'N/A';
            if ($econtract['customer_id']) {
                $customer = $db->query('SELECT userid, username, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $econtract['customer_id'])->fetch() ?: null;
                if ($customer) {
                    $econtract['username'] = $customer['username'];
                }
            }
            $econtract['created_at'] = $econtract['updated_at'] = NV_CURRENTTIME;
            $_vips_vi = $_vips_en = [];
            foreach ($order_data['customs_log'] as $custom_vip) {
                if ($custom_vip['prefix_lang'] == 0) {
                    $_vips_vi[] = $custom_vip['vip'];
                } else {
                    $_vips_en[] = $custom_vip['vip'];
                }
            }
            $econtract['vips_vi'] = implode(',', $_vips_vi);
            $econtract['vips_en'] = implode(',', $_vips_en);

            // Mã hợp đồng: contract_no
            $econtract_next_no = str_pad($module_config[$module_name]['econtract_next_no'] ?? 1, 2, '0', STR_PAD_LEFT);
            $econtract['contract_no'] = $econtract_next_no . '/HĐ' . date('Y') . '/DAUTHAU.INFO';
            // Kiểm tra xem hợp đồng đã tồn tại hay chưa
            $check_contract_no = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE contract_no=' . $db->quote($econtract['contract_no']))->fetchColumn();
            if ($check_contract_no) {
                $econtract_next_no++;
                $econtract['contract_no'] = str_pad($econtract_next_no, 2, '0', STR_PAD_LEFT) . '/HĐ' . date('Y') . '/DAUTHAU.INFO';
            }

            // Mã đề nghị thanh toán
            $payment_proposal_number = str_pad($module_config[$module_name]['econtract_payment_proposal'] ?? 1, 2, '0', STR_PAD_LEFT);
            $econtract['payment_proposal_no'] = $payment_proposal_number . '/DNTT' . date('Y') . '/DAUTHAU.INFO';
            $check_payment_proposal = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE payment_proposal_no=' . $db->quote($econtract['payment_proposal_no']))->fetchColumn();
            if ($check_payment_proposal) {
                $payment_proposal_number++;
                $econtract['payment_proposal_no'] = str_pad($payment_proposal_number, 2, '0', STR_PAD_LEFT) . '/DNTT' . date('Y') . '/DAUTHAU.INFO';
            }

            $data_vip = array_merge($_vips_vi, $_vips_en);
            $data_vip = array_unique($data_vip);
            $data_vip = array_map(function ($_vip) {
                global $global_arr_vip;
                return $global_arr_vip[$_vip];
            }, $data_vip);

            $_total = $_discount = $_total_end = 0;
            if (!empty($order_data['customs_log'])) {
                foreach ($order_data['customs_log'] as $custom_vip) {
                    $_total += $custom_vip['vip_price'];
                    $_discount += $custom_vip['discount'];
                    $_total_end += ($custom_vip['vip_price'] - $custom_vip['discount']);
                }
            }

            $econtract['contract_data'] = json_encode([
                'data_vip' => implode(', ', $data_vip),
                'content' => '',
                'total_service' => $_total,
                'promotion' => $_discount,
                'total_payment' => $_total_end,
            ]);

            try {
                $db->beginTransaction();
                // 2. Lưu hợp đồng: econtracts
                $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts (
                    tax_code, c_name, representative, jobtitle, c_address, phone, email, authorization_letter, receiver, receiver_phone, receiver_address, cccd, customer_type, contract_no, contract_path, status, stage, stage_next, uploader_id, customer_id, created_at, updated_at, vips_vi, vips_en, contract_data, payment_proposal_no)
                    VALUES (:tax_code, :c_name, :representative, :jobtitle, :c_address, :phone, :email, :authorization_letter, :receiver, :receiver_phone, :receiver_address, :cccd, :customer_type, :contract_no, :contract_path, :status, :stage, :stage_next, :uploader_id, :customer_id, :created_at, :updated_at, :vips_vi, :vips_en, :contract_data, :payment_proposal_no)');

                $stmt->bindParam(':tax_code', $econtract['tax_code'], PDO::PARAM_STR);
                $stmt->bindParam(':c_name', $econtract['c_name'], PDO::PARAM_STR);
                $stmt->bindParam(':representative', $econtract['representative'], PDO::PARAM_STR);
                $stmt->bindParam(':jobtitle', $econtract['jobtitle'], PDO::PARAM_STR);
                $stmt->bindParam(':c_address', $econtract['c_address'], PDO::PARAM_STR);
                $stmt->bindParam(':phone', $econtract['phone'], PDO::PARAM_STR);
                $stmt->bindParam(':email', $econtract['email'], PDO::PARAM_STR);
                $stmt->bindParam(':authorization_letter', $econtract['authorization_letter'], PDO::PARAM_STR);
                $stmt->bindParam(':receiver', $econtract['receiver'], PDO::PARAM_STR);
                $stmt->bindParam(':receiver_phone', $econtract['receiver_phone'], PDO::PARAM_STR);
                $stmt->bindParam(':receiver_address', $econtract['receiver_address'], PDO::PARAM_STR);
                $stmt->bindParam(':cccd', $econtract['cccd'], PDO::PARAM_STR);
                $stmt->bindParam(':customer_type', $econtract['customer_type'], PDO::PARAM_INT);
                $stmt->bindParam(':contract_no', $econtract['contract_no'], PDO::PARAM_STR);
                $stmt->bindParam(':contract_path', $econtract['contract_path'], PDO::PARAM_STR);
                $stmt->bindParam(':status', $econtract['status'], PDO::PARAM_INT);
                $stmt->bindParam(':stage', $econtract['stage'], PDO::PARAM_INT);
                $stmt->bindParam(':stage_next', $econtract['stage_next'], PDO::PARAM_INT);
                $stmt->bindParam(':uploader_id', $econtract['uploader_id'], PDO::PARAM_INT);
                $stmt->bindParam(':customer_id', $econtract['customer_id'], PDO::PARAM_INT);
                $stmt->bindParam(':created_at', $econtract['created_at'], PDO::PARAM_INT);
                $stmt->bindParam(':updated_at', $econtract['updated_at'], PDO::PARAM_INT);
                $stmt->bindParam(':vips_vi', $econtract['vips_vi'], PDO::PARAM_STR);
                $stmt->bindParam(':vips_en', $econtract['vips_en'], PDO::PARAM_STR);
                $stmt->bindParam(':contract_data', $econtract['contract_data'], PDO::PARAM_STR);
                $stmt->bindParam(':payment_proposal_no', $econtract['payment_proposal_no'], PDO::PARAM_STR);
                $stmt->execute();
                $econtract['id'] = $db->lastInsertId();

                // Cập nhật lại config: $module_config[$module_name]['econtract_next_no'] => xóa cache
                $econtract_next_no++;
                $payment_proposal_number++;
                $sql = '
                    UPDATE ' . NV_CONFIG_GLOBALTABLE . '
                    SET
                        config_value = CASE
                            WHEN config_name = ' . $db->quote('econtract_next_no') . ' THEN ' . $econtract_next_no . '
                            WHEN config_name = ' . $db->quote('econtract_payment_proposal') . ' THEN ' . $payment_proposal_number . '
                        END
                    WHERE lang=' . $db->quote(NV_LANG_DATA) . '
                    AND module=' . $db->quote($module_name) . '
                    AND config_name IN (' . $db->quote('econtract_next_no') . ', ' . $db->quote('econtract_payment_proposal') . ')';
                $db->query($sql);
                $nv_Cache->delMod($module_name);

                // 3. Lưu đơn hàng: econtract_orders
                $db->query('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders (
                    econtract_id, order_id, site_id, user_id, username, vips_vi, vips_en
                ) VALUES (
                    ' . $econtract['id'] . ',
                    ' . $order_data['id'] . ',
                    1,
                    ' . $econtract['customer_id'] . ',
                    ' . $db->quote($econtract['username'] ?? '') . ',
                    ' . $db->quote($econtract['vips_vi']) . ',
                    ' . $db->quote($econtract['vips_en']) . '
                )');

                // 4. Tạo phiên bản hợp đồng: econtract_versions => update current_version trong econtracts
                $version_id = $db->insert_id('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions (
                    econtract_id, user_id, version, pdf_path, contract_data, created_at
                ) VALUES (
                    ' . $econtract['id'] . ',
                    0,
                    0,
                    ' . $db->quote('') . ',
                    ' . $db->quote(json_encode($econtract)) . ',
                    ' . NV_CURRENTTIME . '
                )', 'id');
                $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET current_version=' . $version_id . ' WHERE id=' . $econtract['id']);

                // 5. Tạo log hợp đồng: econtract_logs
                $db->query('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_logs (econtract_id, version_id, action, user_id, action_desc_vi, action_desc_en, log_visible, created_at) VALUES (' . $econtract['id'] . ', ' . $version_id . ', 0, 0, ' . $db->quote('Tạo hợp đồng mới từ đơn hàng: ' . $order_data['code']) . ', ' . $db->quote('Create new econtract from order: ' . $order_data['code']) . ', 1, ' . NV_CURRENTTIME . ')');

                $db->commit();

                // Gọi API tạo log cho đơn hàng
                $logApi = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                $logApi = $logApi->setModule('bidding')
                    ->setLang('vi')
                    ->setAction('CreateBiddingAllLogs')
                    ->setData([
                        'userid' => 0,
                        'log_area' => 1,
                        'log_key' => 'LOG_CHANGE_ORDER_CONTRACT',
                        'log_time' => NV_CURRENTTIME,
                        'log_data' => [
                            [$nv_Lang->getModule('log_update_econtract_success'), $nv_Lang->getModule('new')],
                            [$nv_Lang->getModule('contract_no') . ': ', $econtract['contract_no']]
                        ],
                        'order_id' => $order_data['id']
                    ])->execute();

                $this->result->setSuccess();
                $this->result->set('EcontractID', $econtract['id']);
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode($error_code)
                ->setMessage($error)
                ->getResult();
        }
        return $this->result->getResult();
    }
}
