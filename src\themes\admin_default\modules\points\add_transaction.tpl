<!-- BEGIN: main -->
    <!-- BEGIN: error -->
    <div class="alert alert-danger">{ERROR}</div>
    <!-- END: error -->
<div class="panel panel-default">
    <div class="panel-body">
        <form class="form-horizontal" action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
            <div class="col-md-8">
                <p class="note__red">{LANG.note__point}  </p>
                <!-- BEGIN: loop -->
                    <span class="note_point"><i class="fa fa-circle" aria-hidden="true"></i>&nbsp;{ROW.value} {MONEY_UNIT} <i class="fa fa-arrow-right" aria-hidden="true"></i> {ROW.key} {LANG.valuePoint}</span>
                <!-- END: loop-->
            </div>
            <div class="col-md-12 col-lg-offset-2">
                <div class="form-group">
                    <label><strong>{LANG.account}</strong> <span class="red">(*)</span></label>
                    <div class="input-group mw250">
                        <input class="form-control" type="text" name="account" id="account" value="{ROW.account} <!-- BEGIN: show_username--> {USERNAME} <!-- END: show_username-->"/>
                        <div class="input-group-btn">
                            <input class="btn btn-default" type="button" name="selectaccount" id="selectaccount" value="{LANG.select}"/>
                        </div>
                    </div>
                    <span class="help-block help-block-wallet">{LANG.addtran_help_account}</span>
                </div>

                <div class="form-group">
                    <label><strong>{LANG.hinhthuc}</strong> </label>
                    <div class="input-group mw250">
                        <div class="convert__money">
                            <label for="donate_apart">
                                <input type="radio" name="note_point" value="-1" id="donate_apart"> {LANG.tran_custom}
                            </label>

                            <label for="donate_add">
                                <input type="radio" name="note_point" value="1" id="donate_add" checked> {LANG.shlech}
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label><strong>{LANG.type}</strong> <span class="red">(*)</span></label>
                    <select class="form-control" title="{LANG.loaigiaodich}" name="typeadd" >
                        <option value="+">Cộng điểm (+)</option>
                        <option value="-">Trừ điểm (-)</option>
                    </select>

                </div>

                <div class="form-group">
                    <label><strong>{LANG.money_transaction}</strong> <span class="red">(*)</span></label>
                    <input class="form-control mw250" type="text" name="money_transaction" maxlength="7" value="{ROW.money_transaction}" />
                    <div class="selectPoint">
                        <select class="form-control" name="moneyConfig">
                            <option value="0">---{LANG.option_pointin}---</option>
                            <!-- BEGIN: config_point -->
                                <option value="{ROW.key}">{ROW.keyFomat} {LANG.valuePoint}</option>
                            <!-- END: config_point -->
                        </select>
                        <p class="convert__point">{LANG.convert_point}: <span id="convert__money"></span></p>
                    </div>
                </div>

                <div class="form-group">
                    <label><strong>{LANG.transaction_info}</strong> <span class="red">(*)</span></label>
                    <textarea class="form-control" style="height:100px;" cols="75" rows="5" name="transaction_info">{ROW.transaction_info}</textarea>
                </div>

                <div class="form-group">
                    <label><strong>{LANG.expired_admin}</strong> <span class="red">(*)</span></label>
                    <input class="form-control mw250" type="text" name="expired" value="{CONFIG.expired}" />
                </div>

                <div class="form-group text-center"><input class="btn btn-primary" name="submit" type="submit" value="{LANG.save}" />
                </div>
            </div>
        </form>
    </div>
</div>
<input type="hidden" value='{CONFIG_POINT}' id="config_point">
<input type="hidden" value='{MONEY_UNIT}' id="unit">
<script type="text/javascript">
$(document).ready(function() {
    $("#selectaccount").click( function() {
        nv_open_browse( "{NV_BASE_ADMINURL}index.php?" + nv_name_variable + "=users&" + nv_fc_variable + "=getuserid&area=account&return=username", "NVImg", "850", "600", "resizable=no,scrollbars=no,toolbar=no,location=no,status=no" );
        return false;
    });
});
</script>
<!-- END: main -->
