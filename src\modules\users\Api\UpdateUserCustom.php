<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\users\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use PDO;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdateUserCustom implements IApi
{
    private $result;

    /**
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     * @return string
     */
    public static function getCat()
    {
        return 'user';
    }

    /**
     * {@inheritDoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     * {@inheritDoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $nv_Request, $db, $db_config, $nv_Lang;

        if (!function_exists('taxcodecheck2')) {
            require NV_ROOTDIR . '/includes/plugin/taxcodecheck.php';
        }

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        $admin_id = Api::getAdminId();

        $array = [];
        // Lấy SĐT nếu có
        if ($nv_Request->isset_request('phone', 'post')) {
            $array['phone'] = $nv_Request->get_title('phone', 'post', '');
        }
        // Lấy MST nếu có
        if ($nv_Request->isset_request('mst', 'post')) {
            $array['mst'] = $nv_Request->get_title('mst', 'post', '');
        }
        // Lấy marketing_types nếu có
        if ($nv_Request->isset_request('marketing_types', 'post')) {
            $array['marketing_types'] = $nv_Request->get_title('marketing_types', 'post', '');
        }
        // Lấy custom_types nếu có
        if ($nv_Request->isset_request('custom_types', 'post')) {
            $array['custom_types'] = $nv_Request->get_title('custom_types', 'post', '');
        }

        // Không có dữ liệu
        if (empty($array)) {
            return $this->result->setCode('5001')->setMessage('No data')->getResult();
        }

        // Lấy thành viên cần cập nhật và mã check
        $hashreceive = $nv_Request->get_title('checkhash', 'post', '');
        $username = $nv_Request->get_title('username', 'post', '');

        $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . " WHERE username=:username AND active=1";
        $stmt = $db->prepare($sql);
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        $stmt->execute();
        $array_user = $stmt->fetch();

        if (empty($array_user)) {
            return $this->result->setCode('5002')->setMessage($nv_Lang->getModule('api_user_not_exists'))->getResult();
        }

        // Tạo mã check để so sánh
        ksort($array);
        $checkhash = $array_user['userid'] . $array_user['username'];
        foreach ($array as $key => $value) {
            $checkhash .= $key . '=' . $value;
        }
        $checkhash = sha1($checkhash);

        if (strcasecmp($checkhash, $hashreceive) !== 0) {
            return $this->result->setCode('5002')->setMessage($nv_Lang->getModule('api_user_error_hash'))->getResult();
        }

        // Kiểm tra đầu vào
        if (isset($array['phone']) and !phonecheck($array['phone'])) {
            return $this->result->setCode('5003')->setMessage($nv_Lang->getModule('api_user_error_phone'))->getResult();
        }
        if (isset($array['mst']) and !taxcodecheck2($array['mst'])) {
            return $this->result->setCode('5003')->setMessage($nv_Lang->getModule('api_user_error_mst'))->getResult();
        }
        /**
         * @since 22/06/2021 marketing_types cho phép trống
         */
        if (isset($array['marketing_types'])) {
            /*
            if (empty($array['marketing_types'])) {
                return $this->result->setCode('5004')->setMessage($nv_Lang->getModule('api_user_error_mt'))->getResult();
            }*/
            $marketing_types = empty($array['marketing_types']) ? [] : explode(',', $array['marketing_types']);
            if (!empty($marketing_types) and array_diff($marketing_types, [1, 3, 4, 5, 6]) !== []) {
                return $this->result->setCode('5005')->setMessage($nv_Lang->getModule('api_user_error_mt2'))->getResult();
            }
        }
        if (isset($array['custom_types'])) {
            if (empty($array['custom_types'])) {
                return $this->result->setCode('5006')->setMessage($nv_Lang->getModule('api_user_error_ct'))->getResult();
            }
            $custom_types = explode(',', $array['custom_types']);
            if (array_diff($custom_types, [1, 3, 4, 5, 6, 7]) !== []) {
                return $this->result->setCode('5007')->setMessage($nv_Lang->getModule('api_user_error_ct2'))->getResult();
            }
        }

        // Cập nhật dữ liệu user_info
        $sql = "UPDATE " . $db_config['prefix'] . "_" . $module_data . "_info SET ";
        $array_update = [];

        if (isset($array['phone'])) {
            $array_update[] = 'phone=:phone';
        }
        if (isset($array['mst'])) {
            $array_update[] = 'mst=:mst';
        }
        if (isset($array['marketing_types'])) {
            $array_update[] = 'marketing_types=:marketing_types';
        }
        if (isset($array['custom_types'])) {
            $array_update[] = 'custom_types=:custom_types';
        }
        $sql .= implode(', ', $array_update);
        $sql .= " WHERE userid=" . $array_user['userid'];
        $stmt = $db->prepare($sql);
        if (isset($array['phone'])) {
            $stmt->bindParam(':phone', $array['phone'], PDO::PARAM_STR);
        }
        if (isset($array['mst'])) {
            $stmt->bindParam(':mst', $array['mst'], PDO::PARAM_STR);
        }
        if (isset($array['marketing_types'])) {
            $stmt->bindParam(':marketing_types', $array['marketing_types'], PDO::PARAM_STR);
        }
        if (isset($array['custom_types'])) {
            $stmt->bindParam(':custom_types', $array['custom_types'], PDO::PARAM_STR);
        }
        $stmt->execute();

        // Cập nhật dữ liệu last update
        $sql = "UPDATE " . $db_config['prefix'] . "_" . $module_data . " SET last_update=:last_update WHERE userid=:userid";
        $sth = $db->prepare($sql);
        $current_time = NV_CURRENTTIME;
        $sth->bindParam(':last_update', $current_time, PDO::PARAM_INT);
        $sth->bindParam(':userid', $array_user['userid'], PDO::PARAM_INT);
        $sth->execute();

        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
