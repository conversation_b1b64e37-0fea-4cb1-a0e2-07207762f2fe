<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\users\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use PDO;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetOauth implements IApi
{
    private $result;

    /**
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_GOD;
    }

    /**
     * @return string
     */
    public static function getCat()
    {
        return 'user';
    }

    /**
     * {@inheritDoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     * {@inheritDoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $nv_Request, $db, $db_config, $crypt;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        $admin_id = Api::getAdminId();

        $opid = $nv_Request->get_title('opid', 'post', '');
        $server = $nv_Request->get_title('server', 'post', '');
        if (empty($opid) or empty($server)) {
            return $this->result->setCode('0001')->setMessage('Invalid data')->getResult();
        }

        $opid = $crypt->hash($opid);
        $stmt = $db->prepare('SELECT a.userid AS uid, b.email AS uemail, b.active AS uactive, b.safemode AS safemode
        FROM ' . NV_USERS_GLOBALTABLE . '_openid a
        INNER JOIN ' . NV_USERS_GLOBALTABLE . ' b ON a.userid=b.userid
        WHERE a.openid=:openid AND a.opid= :opid');
        $stmt->bindParam(':openid', $server, PDO::PARAM_STR);
        $stmt->bindParam(':opid', $opid, PDO::PARAM_STR);
        $stmt->execute();
        list($user_id, $op_email, $user_active, $safemode) = $stmt->fetch(3);

        $this->result->set('data', [$user_id, $op_email, $user_active, $safemode]);
        return $this->result->setSuccess()->getResult();
    }
}
