<!-- BEGIN: main -->
<div class="panel panel-default well">
    <div class="panel-heading">{LANG.select_field_check}</div>
    <div class="panel-body">
        <form class="form-horizontal" action="{FORM_ACTION}" method="post">
            <div class="row">
                <div class="col-md-4">
                    <input type="text" name="q_search" class="form-control" value="{Q}" placeholder="{LANG.search_title}">
                </div>
                <!-- BEGIN: field -->
                <div class="col-md-2">
                    <label><input type="radio" name="field[]" value="{FIELD.key}"{FIELD.checked}>{FIELD.title}</label>
                </div>
                <!-- END: field -->
            </div>
            <div class="row text-center">
                <button type="submit" name="submit" class="btn savebtn btn-primary" value="1">{LANG.search_submit}</button>
                <a href="{LINK_UNDO_MERGE}" class="btn savebtn btn-success">{LANG.merger_undo}</a>
            </div>
        </form>
    </div>
</div>
<!-- BEGIN: success -->
    <div class="alert alert-success">{SUCCESS}</div>
    <script type="text/javascript">
       $(document).ready(function($) {
            $(".alert-success").fadeOut(3000);
            setTimeout(function() {
                location.href = location.href;
            }, 1500);
       });
    </script>
<!-- END: success -->
<!-- BEGIN: error -->
<div class="alert alert-warning">{ERROR}</div>
<!-- END: error -->
<form action="{FORM_ACTION}" method="post">
    <input type="hidden" name="merger" value="1">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th class="w50">{LANG.number}</th>
                    <th class="w100">ID</th>
                    <!-- BEGIN: type1 -->
                    <th class="w150">{LANG.source_leads}</th>
                    <!-- END: type1 -->
                    <th class="">{LANG.name}</th>
                    <th class="">{LANG.email}</th>
                    <th class="">{LANG.sub_email}</th>
                    <th class="">{LANG.phone}</th>
                    <th class="">{LANG.sub_phone}</th>
                    <th class="">{LANG.tax}</th>
                    <th class="">{LANG.caregiver_id}</th>
                    <th class="w150">{LANG.merger_select}</th>
                    <th>&nbsp;</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <!-- BEGIN: row -->
                <tr>
                    <td rowspan="{rowspan}" class="text-center" style="vertical-align: middle;">{DATA.stt}</td>
                    <td>{DATA_ROW.id}</td>
                    <!-- BEGIN: type1 -->
                    <td>{DATA_ROW.source_leads}</td>
                    <!-- END: type1 -->
                    <td>{DATA_ROW.name}</td>
                    <td>{DATA_ROW.email}</td>
                    <td>{DATA_ROW.sub_email}</td>
                    <td>{DATA_ROW.phone}</td>
                    <td>{DATA_ROW.sub_phone}</td>
                    <td>{DATA_ROW.tax}</td>
                    <td>{DATA_ROW.caregiver_name}</td>
                    <td class="text-center"><label><input type="checkbox" name="rowcheck[]" value="{DATA_ROW.id}"></label></td>
                    <td rowspan="{rowspan}" class="text-center" style="vertical-align: middle;"><button type="submit" name="submit" class="btn savebtn btn-success" value="1">{LANG.merger_btn}</button></td>
                </tr>
                <!-- END: row -->
                <!-- BEGIN: row2 -->
                <tr>
                    <td>{DATA_ROW.id}</td>
                    <!-- BEGIN: type1 -->
                    <td>{DATA_ROW.source_leads}</td>
                    <!-- END: type1 -->
                    <td>{DATA_ROW.name}</td>
                    <td>{DATA_ROW.email}</td>
                    <td>{DATA_ROW.sub_email}</td>
                    <td>{DATA_ROW.phone}</td>
                    <td>{DATA_ROW.sub_phone}</td>
                    <td>{DATA_ROW.tax}</td>
                    <td>{DATA_ROW.caregiver_name}</td>
                    <td class="text-center"><label><input type="checkbox" name="rowcheck[]" value="{DATA_ROW.id}"></label></td>
                </tr>
                <!-- END: row2 -->
                <!-- END: loop -->
            </tbody>
            <!-- BEGIN: generate_page -->
            <tfoot>
                <tr>
                    <td class="text-center text-mdi" colspan="6">{NV_GENERATE_PAGE}</td>
                </tr>
            </tfoot>
            <!-- END: generate_page -->
        </table>
    </div>
</form>
<!-- END: main -->

<!-- BEGIN: merger -->
<div class="panel panel-default well">
    <div class="panel-heading">{LANG.select_field_merge} (<i>{LANG.merge_guide_note}</i>)</div>
    <div class="panel-body">
        <form class="form-horizontal" action="{FORM_ACTION}" method="post">
            <input type="hidden" name="merger_submit" value="1">
            <input type="hidden" name="merged_phone" value="">
            <input type="hidden" name="merged_email" value="">
            <input type="hidden" name="merged_comment" value="">
            
            <input type="hidden" name="merged_relate_phone" value="">
            <input type="hidden" name="merged_relate_email" value="">
            <input type="hidden" name="merged_relate_comment" value="">
            
            <input type="hidden" name="all_orderid" value="">
            <input type="hidden" name="all_orderid_dtnet" value="">
            <input type="hidden" name="all_customs_id" value="">
            
            <!-- BEGIN: lead -->
            <input type="hidden" name="leadsmerger[]" value="{LEADS.id}">
            <!-- END: lead -->
            <!-- BEGIN: relate -->
            <input type="hidden" name="relatemerger[]" value="{INFO_RELATE.id}">
            <!-- END: relate -->

            <!-- BEGIN: main_info -->
            <center><h1><b>{TITLE_MAIN}</b></h1></center>
            <div class="table-responsive">
                <table class="table table-striped table-bordered table-hover">
                    <thead>
                        <tr>
                            <th class="w50 text-center">{LANG.field}</th>
                            <!-- BEGIN: loopthead -->
                            <th class="w50 text-center">ROW {I} <input name="check_all" class="check_all" type="radio" value="{I}" onclick="duCheckAll({I});" />
                            </th>
                            <!-- END: loopthead -->
                        </tr>
                    </thead>
                    <tbody>
                        <!-- BEGIN: loopfield -->
                        <tr>
                            <td style="padding-left: 20px">
                                {FIELD_MERGER}
                                <div>
                                    <!-- BEGIN: keep_both_phone -->
                                    <input type="checkbox" name="keep_both_phone" value="1" id="keep_both_phone"> <label for="keep_both_phone"><i>Giữ cả 2 (trường "bị merge" sẽ được lưu vào 'Điện thoại khác')</i></label>
                                    <!-- END: keep_both_phone -->
                                    <!-- BEGIN: keep_both_email -->
                                    <input type="checkbox" name="keep_both_email" value="1" id="keep_both_email"> <label for="keep_both_email"><i>Giữ cả 2 (trường "bị merge" sẽ được lưu vào 'Email khác')</i></label>
                                    <!-- END: keep_both_email -->
                                    <!-- BEGIN: keep_both_comment -->
                                    <input type="checkbox" name="keep_both_comment" value="1" id="keep_both_comment"> <label for="keep_both_comment"><i>Giữ cả 2 (được nối nhau bằng dấu phẩy)</i></label>
                                    <!-- END: keep_both_comment -->
                                </div>
                            </td>
                            <!-- BEGIN: looprow -->
                            <td style="padding-left: 40px">
                            <label>
                                <input type="radio" class="idcheck_{J}" name="{FIELD}" value="{DATA_FIELD}" {checked} onclick="uncheckAll();">
                                <!-- BEGIN: field_total -->
                                {DATA_FIELD_TITLE}
                                <!-- END: field_total -->
                                <ol>
                                <!-- BEGIN: loop_comment -->
                                <li>{DATA_COMMENT.id} - {DATA_COMMENT.timecreate} - {DATA_COMMENT.note}</li>
                                <!-- END: loop_comment -->
                                </ol>
                            </label>
                            </td>
                            <!-- END: looprow -->
                        </tr>
                        <!-- END: loopfield -->
                    </tbody>
                </table>
            </div>
            <!-- END: main_info -->
            <!-- BEGIN: relate_info -->
            <center><h1><b>{TITLE_RELATE}</b></h1></center> 
            <div class="table-responsive">
                <table class="table table-striped table-bordered table-hover">
                    <thead>
                        <tr>
                            <th class="w50 text-center">{LANG.field}</th>
                            <!-- BEGIN: loopthead_relate -->
                            <th class="w50 text-center">ROW {II} <input name="checkAllRelate" class="checkAllRelate" type="radio" value="{II}" onclick="check_all_relate({II});" />
                            </th>
                            <!-- END: loopthead_relate -->
                        </tr>
                    </thead>
                    <tbody>
                        <!-- BEGIN: loopfield_relate -->
                        <tr>
                            <td style="padding-left: 20px">
                                {FIELD_MERGER_RELATE}
                                <div>
                                    <!-- BEGIN: keep_both_relate_phone -->
                                    <input type="checkbox" name="keep_both_relate_phone" value="1" id="keep_both_relate_phone"> <label for="keep_both_relate_phone"><i>Giữ cả 2 (trường "bị merge" sẽ được lưu vào 'Điện thoại khác')</i></label>
                                    <!-- END: keep_both_relate_phone -->
                                    <!-- BEGIN: keep_both_relate_email -->
                                    <input type="checkbox" name="keep_both_relate_email" value="1" id="keep_both_relate_email"> <label for="keep_both_relate_email"><i>Giữ cả 2 (trường "bị merge" sẽ được lưu vào 'Email khác')</i></label>
                                    <!-- END: keep_both_relate_email -->
                                    <!-- BEGIN: keep_both_relate_comment -->
                                    <input type="checkbox" name="keep_both_relate_comment" value="1" id="keep_both_relate_comment"> <label for="keep_both_relate_comment"><i>Giữ cả 2</i></label>
                                    <!-- END: keep_both_relate_comment -->
                                </div>
                            </td>
                            <!-- BEGIN: looprow -->
                            <td style="padding-left: 40px">
                                <label>
                                    <input type="radio" class="idcheck_relate{JJ}" name="{FIELD_RELATE}" value="{DATA_FIELD_RELATE}" {checked_relate} onclick="uncheckAllRelate();">
                                    <!-- BEGIN: field_total -->
                                    {DATA_FIELD_TITLE_RELATE}
                                    <!-- END: field_total -->
                                    <ol>
                                        <!-- BEGIN: loop_comment -->
                                        <li>{DATA_COMMENT.id} - {DATA_COMMENT.timecreate} - {DATA_COMMENT.note}</li>
                                        <!-- END: loop_comment -->
                                    </ol>
                                </label>
                            </td>
                            <!-- END: looprow -->
                        </tr>
                        <!-- END: loopfield_relate -->
                    </tbody>
                </table>
            </div>
            <!-- END: relate_info -->

            <div class="row text-center">
                <button type="submit" name="submit" class="btn savebtn btn-primary" value="1">{LANG.save}</button>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript">
    function duCheckAll(stt) {
        $('.idcheck_'+stt).prop('checked', true);
    }
    function uncheckAll(stt) {
        $('.check_all').prop('checked', false);
        getMergedFields();
    }
    function check_all_relate(stt) {
        $('.idcheck_relate'+stt).prop('checked', true);
    }
    function uncheckAllRelate() {
        $('.checkAllRelate').prop('checked', false);
        getMergedRelateFields();
    }
    function getMergedFields() {
        let merged = {
            phone: [],
            email: [],
            comment: [],
        };
        
        // debugger
        for (const phone of $('[name=phone]')) {
            if ( !$(phone).prop('checked') && $(phone).val() ) {
                merged.phone.push($(phone).val());
            }
        }
        for (const email of $('[name=email]')) {
            if ( !$(email).prop('checked') && $(email).val() ) {
                merged.email.push($(email).val());
            }
        }
        for (const comment of $('[name=comment]')) {
            if ( !$(comment).prop('checked') && $(comment).val() ) {
                merged.comment.push($(comment).val());
            }
        }
        $('[name=merged_phone]').val(merged.phone.join(','));
        $('[name=merged_email]').val(merged.email.join(','));
        $('[name=merged_comment]').val(merged.comment.join(','));
    }
    
    function getOrderCustomFields() {
        let all = {
            orderid: [],
            orderid_dtnet: [],
            customs_id: [],
        };
        for (const orderid of $('[name=orderid]')) {
            if ( $(orderid).val() ) {
                all.orderid.push($(orderid).val());
            }
        }
        for (const orderid_dtnet of $('[name=orderid_dtnet]')) {
            if ( $(orderid_dtnet).val() ) {
                all.orderid_dtnet.push($(orderid_dtnet).val());
            }
        }
        for (const customs_id of $('[name=customs_id]')) {
            if ( $(customs_id).val() ) {
                all.customs_id.push($(customs_id).val());
            }
        }
        $('[name=all_orderid]').val(all.orderid.join(','));
        $('[name=all_orderid_dtnet]').val(all.orderid_dtnet.join(','));
        $('[name=all_customs_id]').val(all.customs_id.join(','));
    }
    
    function getMergedRelateFields() {
        let mergedRelate = {
            phone_relate: [],
            email_relate: [],
            comment_relate: [],
        };
        
        // debugger
        for (const phone of $('[name=phone_relate]')) {
            if ( !$(phone).prop('checked') && $(phone).val() ) {
                mergedRelate.phone_relate.push($(phone).val());
            }
        }
        for (const email of $('[name=email_relate]')) {
            if ( !$(email).prop('checked') && $(email).val() ) {
                mergedRelate.email_relate.push($(email).val());
            }
        }
        for (const comment of $('[name=comment_relate]')) {
            if ( !$(comment).prop('checked') && $(comment).val() ) {
                mergedRelate.comment_relate.push($(comment).val());
            }
        }
        
        // debugger
        $('[name=merged_relate_phone]').val(mergedRelate.phone_relate.join(','));
        $('[name=merged_relate_email]').val(mergedRelate.email_relate.join(','));
        $('[name=merged_relate_comment]').val(mergedRelate.comment_relate.join(','));
    }
    
    $(document).ready(function () {
        getMergedFields();
        getMergedRelateFields();
        getOrderCustomFields();
    })
</script>
<!-- END: merger -->
 