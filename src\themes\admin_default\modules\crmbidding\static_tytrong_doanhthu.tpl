<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">

<div class="panel">
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}">
        <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
        <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}">
        <div class="form-group">
            <label>{LANG.time_from}:</label> <input class="form-control w100 uidatepicker" type="text" value="{ARRAY_SEARCH.time_from}" name="time_from" maxlength="10" autocomplete="off">
        </div>
        <div class="form-group">
            <label>{LANG.to}:</label> <input class="form-control w100 uidatepicker" type="text" value="{ARRAY_SEARCH.time_to}" name="time_to" maxlength="10" autocomplete="off">
        </div>
        <!-- BEGIN: view_static -->
        <div class="form-group">
            <span>
                {LANG.display_sale_confirm_orders}
                <input type="checkbox" value="1" class="form-control" name="display_confirm_the_order" {DISPLAY_CONFIRM_ORDER} />
            </span>
        </div>
        <div class="form-group">
            <select class="form-control" id="element_group_id" name="group_id" style="width: 200px;">
                <option value="-1">{LANG.group_id_all}</option>
                <!-- BEGIN: loop_admin_group -->
                <option value="{OPTION_GROUP.key}"{OPTION_GROUP.selected}>{OPTION_GROUP.title}</option>
                <!-- END: loop_admin_group -->
            </select>
        </div>
        <!-- END: view_static -->
        <div class="form-group">
            <select class="form-control" name="admin_id" style="width: 250px;">
            </select>
        </div>
        <div class="form-group">
            <input class="btn btn-primary" type="submit" value="{LANG.search_submit}">
        </div>
    </form>
</div>

<div class="table-responsive">
    <table class="table table-striped table-bordered table-hover text-center">
        <caption>{LANG.bang_ty_trong}</caption>
        <thead>
            <tr>
                <th rowspan="2" class="w50 text-center">{LANG.number}</th>
                <th rowspan="2" class="w100 text-center">{LANG.vip}</th>
                <th colspan="2" class="w50 text-center">{LANG.total_service_option}</th>
                <th colspan="2" class="w100 text-center">{LANG.cac_goi_moi}</th>
                <th colspan="2" class="w100 text-center">{LANG.cac_goi_gia_han}</th>
                <th colspan="2" class="w100 text-center">Gói VIP gia hạn sau 1 năm</th>
            </tr>
            <tr>
                <th class="w100 text-center">{LANG.number_vip}</th>
                <th class="w100 text-center">{LANG.doanh_so_thuc}</th>
                
                <th class="w100 text-center">{LANG.number_vip}</th>
                <th class="w100 text-center">{LANG.doanh_so_thuc}</th>
                
                <th class="w100 text-center">{LANG.number_vip}</th>
                <th class="w100 text-center">{LANG.doanh_so_thuc}</th>
                
                <th class="w100 text-center">{LANG.number_vip}</th>
                <th class="w100 text-center">{LANG.doanh_so_thuc}</th>
                
            </tr>
        </thead>
        <tbody>
            <!-- BEGIN: loopvip -->
            <tr>
                <td>{DATA_VIP.stt}</td>
                <td>{DATA_VIP.vip_title}</td>
                
                <!-- BEGIN: has_link -->
                <td><a class="view_leads" href="#" data-href="{DATA_VIP.link_vip}">{DATA_VIP.num}</a></td>
                <td><a class="view_leads" href="#" data-href="{DATA_VIP.link_order}">{DATA_VIP.total_end}</a></td>
                <!-- END: has_link -->
                 
                <!-- BEGIN: havent_link -->
                <td>{DATA_VIP.num}</td>
                <td>{DATA_VIP.total_end}</td>
                <!-- END: havent_link -->
                  
                <td>{DATA_VIP.num_vip_new}</td>
                <td>{DATA_VIP.total_end_vip_new}</td>
                <td>{DATA_VIP.num_vip_renewal}</td>
                <td>{DATA_VIP.total_end_vip_renewal}</td>
                <td>{DATA_VIP.num_vip_1year_renewal}</td>
                <td>{DATA_VIP.total_end_vip_1year_renewal}</td>
            </tr>
            <!-- END: loopvip -->
        </tbody>
        <tfoot>
            <tr class="red">
                <th colspan=2 class="text-center">{LANG.total}:</th>
                <th class="text-center">{STATIC_VIP.num}</th>
                <th class="text-center">{STATIC_VIP.total_end}</th>
                <th class="text-center">{STATIC_VIP.num_vip_new}</th>
                <th class="text-center">{STATIC_VIP.total_end_vip_new}</th>
                
                <th class="text-center">{STATIC_VIP.num_vip_renewal}</th>
                <th class="text-center">{STATIC_VIP.total_end_vip_renewal}</th>
                
                <th class="text-center">{STATIC_VIP.num_vip_1year_renewal}</th>
                <th class="text-center">{STATIC_VIP.total_end_vip_1year_renewal}</th>
            </tr>
        </tfoot>
    </table>
</div>

<script type="text/javascript">
    var isFirstRun = true;
    var old_groupId = -1;
    function getAdmin() {
        var groupId = $('[id="element_group_id"]').val();
        $("[name='group_id']").val(groupId);

        if (isFirstRun || (groupId > 0 && groupId != old_groupId) || groupId == -1) {
            isFirstRun = false;
            old_groupId = groupId;

            // Xử lý giá trị mặc định khi chọn nhóm
            $('select[name="admin_id"]').val('-1');
            $('select[name="admin_id"]').empty();
            $('select[name="admin_id"]').append('<option value="-1">{LANG.admin_id_all}</option>');

            $.ajax({
                type: "POST",
                url: window.location.href,
                data: "&get_admin=1&groupId=" + groupId,
                dataType: "json",
                success: function(data) {
                    $.each(data.list_admin, function(key, value) {
                        var option = $('<option></option>').val(value.id).html(value.title);
                        if (value.id == {ARRAY_SEARCH.admin_id}) {
                            option.attr("selected", "selected");
                        }
                        $('select[name="admin_id"]').append(option);
                    });
                }
            });
        }
    }
    $(document).ready(function($) {
        getAdmin();
        $("select[name='admin_id']").select2();
        $("select[name='group_id']").select2();
        $('[id="element_group_id"]').on('change', function() {
            getAdmin();
        });
    });
    $('.view_leads').click(function() {
        var href = $(this).attr('data-href');
        nv_open_browse(href, 'NVImg', 1200, 600,
                'resizable=no,scrollbars=no,toolbar=no,location=no,status=no');
        return;
    });
    $(document).ready(function() {
        $('.uidatepicker').datepicker({
            showOn : "both",
            dateFormat : "dd/mm/yy",
            changeMonth : true,
            changeYear : true,
            showOtherMonths : true,
            buttonImage : nv_base_siteurl + "assets/images/calendar.gif",
            buttonImageOnly : true
        });
    });
</script>
<!-- END: main -->
