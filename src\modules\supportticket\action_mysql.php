<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */
if (!defined('NV_IS_FILE_MODULES')) {
    exit('Stop!!!');
}

$sql_drop_module = [];

// $sql_drop_module[] = "DROP TABLE IF EXISTS " . $db_config['prefix'] . "_" . $module_data . "_cat";
// $sql_drop_module[] = "DROP TABLE IF EXISTS " . $db_config['prefix'] . "_" . $module_data . "_row";
// $sql_drop_module[] = "DROP TABLE IF EXISTS " . $db_config['prefix'] . "_" . $module_data . "_row_log";
// $sql_drop_module[] = "DROP TABLE IF EXISTS " . $db_config['prefix'] . "_" . $module_data . "_label";

$sql_create_module = $sql_drop_module;

$sql_create_module[] = "CREATE TABLE IF NOT EXISTS " . $db_config['prefix'] . "_" . $module_data . "_cat (
  cat_id SMALLINT(5) NOT NULL AUTO_INCREMENT,
	title_vi VARCHAR(249) NOT NULL DEFAULT '',
	title_en VARCHAR(249) NOT NULL DEFAULT '',
	is_point TINYINT(4) NOT NULL DEFAULT '0',
	weight SMALLINT(5) NOT NULL DEFAULT '0',
	active TINYINT(1) NULL DEFAULT '1',
	PRIMARY KEY (cat_id),
  	INDEX weight (weight),
	INDEX active (active)
) ENGINE=InnoDB";

$sql_create_module[] = "CREATE TABLE IF NOT EXISTS " . $db_config['prefix'] . "_" . $module_data . "_label (
  label_id TINYINT(3) UNSIGNED NOT NULL AUTO_INCREMENT,
	title_vi VARCHAR(249) NOT NULL DEFAULT '',
	title_en VARCHAR(249) NOT NULL DEFAULT '',
	color VARCHAR(50) NOT NULL DEFAULT '',
	active TINYINT(1) NULL DEFAULT '1',
	weight TINYINT(3) NOT NULL DEFAULT '0',
	PRIMARY KEY (label_id),
	INDEX weight (weight),
	INDEX active (active)
) ENGINE=InnoDB";

$sql_create_module[] = "CREATE TABLE IF NOT EXISTS " . $db_config['prefix'] . "_" . $module_data . "_row (
  	id INT(11) NOT NULL AUTO_INCREMENT,
	title VARCHAR(249) NOT NULL DEFAULT '',
	content TEXT,
	cat_id SMALLINT(5) UNSIGNED NOT NULL DEFAULT '0',
	customer_id MEDIUMINT(8) UNSIGNED NOT NULL DEFAULT '0',
	vip_id INT(11) UNSIGNED NOT NULL DEFAULT '0',
	order_id INT(11) UNSIGNED NOT NULL DEFAULT '0',
	label_ids VARCHAR(200) NOT NULL DEFAULT '',
	assignee_to VARCHAR(200) NOT NULL DEFAULT '',
	file_attach TEXT,
	status TINYINT(4) UNSIGNED NOT NULL DEFAULT '0',
	add_userid MEDIUMINT(8) UNSIGNED NOT NULL DEFAULT '0',
	add_time INT(11) UNSIGNED NOT NULL DEFAULT '0',
	edit_time INT(11) UNSIGNED NOT NULL DEFAULT '0',
	PRIMARY KEY (id),
	KEY cat_id (cat_id),
	KEY customer_id (customer_id),
	KEY vip_id (vip_id),
	KEY order_id (order_id),
	KEY status (status),
	KEY add_userid (add_userid)
) ENGINE=InnoDB";

$sql_create_module[] = "CREATE TABLE IF NOT EXISTS " . $db_config['prefix'] . "_" . $module_data . "_row_log (
	log_id INT(11) NOT NULL AUTO_INCREMENT,
	ticket_id INT(11) UNSIGNED NOT NULL DEFAULT '0',
	reply_userid MEDIUMINT(8) UNSIGNED NOT NULL DEFAULT '0',
	content TEXT,
	file_attach TEXT,
	add_time INT(11) UNSIGNED NOT NULL DEFAULT '0',
	edit_time INT(11) UNSIGNED NOT NULL DEFAULT '0',
	PRIMARY KEY (log_id),
	KEY ticket_id (ticket_id),
	KEY reply_userid (reply_userid)
) ENGINE=InnoDB";

$sql_create_module[] = "CREATE TABLE IF NOT EXISTS " . $db_config['prefix'] . "_" . $module_data . "_alllog (
	id INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
	userid MEDIUMINT(8) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Tài khoản thực hiện việc log. 0 là hệ thống',
	log_area TINYINT(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '0 là khu vực ngoài site, 1 là quản trị xảy ra log',
	log_key VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'Khóa log, đặt tùy ý',
	log_time INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Thời gian ghi log',
	log_data MEDIUMTEXT NOT NULL COMMENT 'Dữ liệu log, ghi dạng json_encode',
	ticket_id INT(11) UNSIGNED NOT NULL DEFAULT '0',
	vips_id INT(11) NOT NULL DEFAULT '0' COMMENT 'id gói vip',
	PRIMARY KEY (id),
	INDEX userid (userid),
	INDEX log_area (log_area),
	INDEX log_key (log_key),
	INDEX log_time (log_time),
	INDEX vips_id (vips_id),
	INDEX ticket_id (ticket_id)
) ENGINE=InnoDB";

$sql_create_module[] = "INSERT INTO nv4_config (lang, module, config_name, config_value) VALUES ('" . $lang . "', 'supportticket', 'email_support', '<EMAIL>')";

$sql_create_module[] = "ALTER TABLE " . $db_config['prefix'] . "_" . $module_data . "_row ADD COLUMN IF NOT EXISTS point_price DOUBLE UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Điểm cần thanh toán' AFTER status;";
$sql_create_module[] = "ALTER TABLE " . $db_config['prefix'] . "_" . $module_data . "_row ADD COLUMN IF NOT EXISTS payment_status TINYINT(4) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Trạng thái thanh toán 1:đã 0:chưa' AFTER point_price, ADD INDEX payment_status (payment_status);";
