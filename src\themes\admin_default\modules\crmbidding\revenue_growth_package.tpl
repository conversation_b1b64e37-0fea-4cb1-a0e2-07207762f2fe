<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<link type="text/css" href="{NV_BASE_SITEURL}themes/{TEMPLATE}/images/{MODULE_FILE}/plugins/apexcharts/apexcharts.css" rel="stylesheet">
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/{TEMPLATE}/images/{MODULE_FILE}/plugins/apexcharts/apexcharts.min.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<div class="well">
    <form action="{NV_BASE_ADMINURL}index.php" method="get">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" />
        <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" />
        <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
        <div class="row">
            <div class="col-sm-12 col-md-6">
                <div class="form-group">
                    <label for="type_chart"><strong>{LANG.display_type}:</strong></label>
                    <select class="form-control" name="type_chart" id="type_chart">
                        <!-- BEGIN: type -->
                        <option value="{TYPE.key}" {TYPE.selected}>{TYPE.value}</option>
                        <!-- END: type -->
                    </select>
                </div>
            </div>
            <div class="col-sm-12 col-md-6" id="num_months_group">
                 <div class="form-group">
                    <label for="num_months"><strong>{LANG.num_month}:</strong></label>
                    <select class="form-control" name="num_months" id="num_months">
                        <!-- BEGIN: month -->
                        <option value="{MONTH}" {SELECTED}>{MONTH}</option>
                        <!-- END: month -->
                    </select>
                 </div>
            </div>
            <div class="col-sm-12 col-md-6" id="num_years_group">
                 <div class="form-group">
                    <label for="num_years"><strong>{LANG.num_year}:</strong></label>
                    <select class="form-control" name="num_years" id="num_years">
                         <!-- BEGIN: year -->
                         <option value="{YEAR}" {SELECTED}>{YEAR}</option>
                         <!-- END: year -->
                    </select>
                 </div>
            </div>
            <div class="col-sm-12 col-md-6">
                 <div class="form-group">
                     <label for="vip"><strong>{LANG.product_package}:</strong></label>
                     <select class="form-control" name="vip[]" id="vip" multiple="multiple">
                         <!-- BEGIN: vip -->
                         <option value="{VIP.key}" {VIP.selected}>{VIP.value}</option>
                         <!-- END: vip -->
                     </select>
                 </div>
            </div>
            <div class="col-sm-12 col-md-6">
                 <div class="form-group form-group-label">
                     <input class="btn btn-primary" type="submit" value="{LANG.search_submit}" />
                     <a href="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&{NV_NAME_VARIABLE}={MODULE_NAME}&{NV_OP_VARIABLE}={OP}" class="btn btn-default">{LANG.clear_search}</a>
                 </div>
            </div>
        </div>
    </form>
</div>
<div id="chart"></div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        $('#vip').select2();
        function toggleNumFields() {
            var typeChart = document.getElementById('type_chart').value;
            var numMonthsGroup = document.getElementById('num_months_group');
            var numYearsGroup = document.getElementById('num_years_group');

            if (typeChart == '1') {
                numMonthsGroup.style.display = 'block';
                numYearsGroup.style.display = 'none';
            } else if (typeChart == '2') {
                numMonthsGroup.style.display = 'none';
                numYearsGroup.style.display = 'block';
            }
        }

        document.getElementById('type_chart').addEventListener('change', toggleNumFields);
        toggleNumFields();

        var options = {
            chart: {
                type: 'area',
                stacked: true,
            },
            colors: [
                '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF', '#8B0000', '#008000',
                '#00008B', '#FF4500', '#FFD700', '#800080', '#ADFF2F', '#FF6347', '#2E8B57', '#6A5ACD',
                '#20B2AA', '#FF69B4', '#B22222', '#5F9EA0', '#DC143C', '#6495ED', '#9932CC', '#FF7F50',
                '#DAA520', '#00CED1', '#8FBC8F', '#9400D3', '#FF8C00', '#E9967A', '#7B68EE', '#4682B4',
                '#D2691E', '#B0C4DE', '#FF1493', '#556B2F', '#FF4500', '#2F4F4F', '#8B4513', '#FFDEAD',
            ],
            series: {SERIES},
            xaxis: {
                categories: {CATEGORIES},
            },
            yaxis: {
                labels: {
                    formatter: function (value) {
                        return new Intl.NumberFormat('vi-VN', {
                            style: 'currency',
                            currency: 'VND',
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0
                        }).format(value).replace('₫', 'đ');
                    }
                },
            },
            tooltip: {
                y: {
                    formatter: function(value) {
                        return new Intl.NumberFormat('vi-VN', {
                            style: 'currency',
                            currency: 'VND',
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0
                        }).format(value).replace('₫', 'đ');
                    }
                }
            },
            dataLabels: {
               enabled: false
            },
            legend: {
                position: 'right',
                horizontalAlign: 'center',
                floating: false,
                offsetY: 0,
                offsetX: 0,
                itemMargin: {
                    vertical: 7,
                    horizontal: 7
                }
            }
        };

        var chart = new ApexCharts(document.querySelector("#chart"), options);
        chart.render();
    });
</script>
<!-- END: main -->
