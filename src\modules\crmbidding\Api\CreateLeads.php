<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CreateLeads implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'leads';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $array_site, $array_user_id_users, $array_groups_leads, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $error = '';
        $error_code = '0000';
        $admin_id = $nv_Request->get_int('admin_id', 'post', '0');

        $row['source_leads'] = $nv_Request->get_int('source_leads', 'post', '0');
        $row['chatgpt_userid'] = $nv_Request->get_title('chatgpt_userid', 'post', '');
        $row['name'] = $nv_Request->get_title('name', 'post', '');
        $row['phone'] = $nv_Request->get_title('phone', 'post', '');
        $row['email'] = strtolower($nv_Request->get_title('email', 'post', ''));
        $row['siteid'] = $nv_Request->get_int('siteid', 'post', 0);
        $row['prefix_lang'] = $nv_Request->get_int('prefix_lang', 'post', 0);
        $row['status_leads'] = $nv_Request->get_int('status_leads', 'post', 0);

        if (empty($row['source_leads'])) {
            $error_code = '2001';
            $error = $nv_Lang->getModule('error_required_source_leads');
        } else if (!in_array($row['source_leads'], array_keys($array_groups_leads))) {
            $error_code = '2002';
            $error = $nv_Lang->getModule('source_leads_not_defined');
        } else if ($row['name'] == '') {
            $error_code = '2003';
            $error = $nv_Lang->getModule('error_required_name');
        } else if ($row['phone'] == '' and $row['email'] == '') {
            $error_code = '2004';
            $error = $nv_Lang->getModule('error_required_phone');
        } elseif ($row['phone'] != '' && !phonecheck($row['phone'], $row['prefix_lang'])) {
            $error_code = '2005';
            $error = $nv_Lang->getModule('error_phone_number');
        } elseif ($row['email'] != '' && nv_check_valid_email($row['email']) != '') {
            $error_code = '2006';
            $error = $nv_Lang->getModule('error_email');
        } else if (!in_array($row['siteid'], array_keys($array_site))) {
            $error_code = '2010';
            $error = $nv_Lang->getModule('siteid_not_defined');
        } else if ($admin_id > 0 and !isset($array_user_id_users[$admin_id])) {
            $error_code = '2011';
            $error = $nv_Lang->getModule('admin_id_not_defind');
        }

        $otherdata = $nv_Request->get_array('otherdata', 'post');
        if (!is_array($otherdata)) {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Param otherdata invalid to array')
                ->getResult();
        }

        $keys_check = [
            'user_id',
            'sub_phone',
            'sub_email',
            'address',
            'tax',
            'company_name',
            'address_company',
            'affilacate_id',
            'caregiver_id',
            'about',
            'businessid',
            'solicitorid',
            'teleproid',
            'phone_search',
            'sub_phone_search',
            'convert_contact',
            'convert_organization',
            'profile_id'
        ];
        $array_sql = [];
        if (!empty($otherdata)) {
            // check $field
            foreach ($otherdata as $key => $value) {
                if (!in_array($key, $keys_check)) {
                    return $this->result->setError()
                        ->setCode('2009')
                        ->setMessage('Missing field ' . $key . ' in otherdata')
                        ->getResult();
                }
                $array_sql[$key] = $key;
            }
        }

        $row = array_merge($row, $otherdata);

        if (isset($row['sub_email']) and $row['sub_email'] != '') {
            $row['sub_email'] = strtolower($row['sub_email']);
            $row['sub_email'] = str_replace(" ", "", $row['sub_email']);
            $row['sub_email'] = str_replace(';', ',', $row['sub_email']);
            $row['sub_email'] = str_replace("\n", ',', $row['sub_email']);
            $_arr_email = array();
            $list_mail = explode(',', $row['sub_email']);
            foreach ($list_mail as $_mail) {
                $_mail = trim($_mail);
                if ((nv_check_valid_email($_mail)) != '') {
                    $error_code = '2008';
                    $error = sprintf($nv_Lang->getModule('error_subemail'), $_mail);
                } elseif (!in_array($_mail, $_arr_email)) {
                    $_arr_email[] = $_mail;
                }
            }
            $otherdata['sub_email'] = implode(',', $_arr_email);
        }

        if (!isset($row['phone_search'])) {
            $row['phone_search'] = 0;
            if (preg_match('/(\d{9})$/', $row['phone'], $m)) {
                $row['phone_search'] = $m[0];
            }
        }
        if (isset($row['sub_phone']) && $row['sub_phone'] != '') {
            $row['sub_phone'] = str_replace(' ', '', $row['sub_phone']);
            $row['sub_phone'] = str_replace(';', ',', $row['sub_phone']);
            $row['sub_phone'] = str_replace("\n", ',', $row['sub_phone']);
            $_arr_phone = $sub_phone_search = [];
            $list_phone = explode(',', $row['sub_phone']);
            foreach ($list_phone as $key => $_phone) {
                $_phone = trim($_phone);
                $_arr_phone[] = $_phone;
                if (!phonecheck($_phone)) {
                    $error_code = '2007';
                    $error = sprintf($nv_Lang->getModule('error_sub_phone'), $_phone);
                } else {
                    if (preg_match('/(\d{9})$/', $_phone, $m)) {
                        $sub_phone_search[$key] = $m[0];
                    }
                }
            }
            $otherdata['sub_phone'] = implode(',', $_arr_phone);
            if (!isset($row['sub_phone_search'])) {
                $row['sub_phone_search'] = implode(',', $sub_phone_search);
                $otherdata['sub_phone_search'] = $row['sub_phone_search'];
                $array_sql['sub_phone_search'] = 'sub_phone_search';
            }
        }

        if (isset($row['tax']) and taxcodecheck2($row['tax']) == false) {
            $error = $nv_Lang->getModule('error_tax');
            $error_code = '2012';
        }

        // nhà thầu, bmt, tự tìm, Messenger cho phép nhập người chăm sóc
        if ($row['source_leads'] != 2 and $row['source_leads'] != 15 and $row['source_leads'] != 7 and $row['source_leads'] != 4 and $row['source_leads'] != 11) {
            $row['affilacate_id'] = 0;
        }

        $_row = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_leads WHERE active=1 AND name=' . $db->quote($row['name']) . ' AND phone = ' . $db->quote($row['phone']) . ' AND email =' . $db->quote($row['email']) . ' AND source_leads='. $row['source_leads'])
            ->fetch();
        if (!empty($_row)) {
            $error_code = '4002';
            $error = $nv_Lang->getModule('error_duplicate');
        }

        if (empty($error)) {
            try {
                $row['timecreate'] = $row['updatetime'] = $row['first_time'] = $row['activity_time'] = NV_CURRENTTIME;
                if (!empty($array_sql)) {
                    $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_leads (source_leads, chatgpt_userid, name, phone, phone_search, email, siteid, prefix_lang, status, timecreate, updatetime, active, first_time, activity_time, ' . implode(',', $array_sql) . ') VALUES (:source_leads, :chatgpt_userid, :name, :phone, :phone_search, :email, :siteid, :prefix_lang, :status, :timecreate, :updatetime, :active, :first_time, :activity_time, :' . implode(', :', $array_sql) . ')');
                } else {
                    $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_leads (source_leads, chatgpt_userid, name, phone, phone_search, email, siteid, prefix_lang, status, timecreate, updatetime, active, first_time, activity_time) VALUES (:source_leads, :chatgpt_userid, :name, :phone, :phone_search, :email, :siteid, :prefix_lang, :status, :timecreate, :updatetime, :active, :first_time, :activity_time)');
                }

                $stmt->bindParam(':source_leads', $row['source_leads'], PDO::PARAM_INT);
                $stmt->bindParam(':chatgpt_userid', $row['chatgpt_userid'], PDO::PARAM_STR);
                $stmt->bindParam(':name', $row['name'], PDO::PARAM_STR);
                $stmt->bindParam(':phone', $row['phone'], PDO::PARAM_STR);
                $stmt->bindParam(':phone_search', $row['phone_search'], PDO::PARAM_STR);
                $stmt->bindParam(':email', $row['email'], PDO::PARAM_STR);
                if (isset($otherdata['caregiver_id']) and $otherdata['caregiver_id'] > 0) {
                    $stmt->bindValue(':status', 1, PDO::PARAM_INT);
                    $stmt->bindParam(':first_time', $row['first_time'], PDO::PARAM_INT);
                } else {
                    $stmt->bindValue(':status', 4, PDO::PARAM_INT);
                    $stmt->bindValue(':first_time', 0, PDO::PARAM_INT);
                }
                if ($row['status_leads'] > 0) {
                    $stmt->bindValue(':status', $row['status_leads'], PDO::PARAM_INT);
                }

                $stmt->bindValue(':active', 1, PDO::PARAM_INT);
                $stmt->bindParam(':timecreate', $row['timecreate'], PDO::PARAM_INT);
                $stmt->bindParam(':updatetime', $row['updatetime'], PDO::PARAM_INT);
                $stmt->bindParam(':activity_time', $row['activity_time'], PDO::PARAM_INT);
                $stmt->bindParam(':siteid', $row['siteid'], PDO::PARAM_INT);
                $stmt->bindParam(':prefix_lang', $row['prefix_lang'], PDO::PARAM_INT);

                if (!empty($otherdata)) {
                    foreach ($otherdata as $key => $value) {
                        $stmt->bindValue(':' . $key, $value, PDO::PARAM_STR);
                    }
                }

                $exc = $stmt->execute();
                $id = $db->lastInsertId();
                if ($exc and $id > 0) {
                    $log_data = [
                        $nv_Lang->getModule('log_insert_leads_info')
                    ];

                    $sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (userid, log_area, log_key, log_time, log_data, leads_id) VALUES (" . $admin_id . ", 1, 'LOG_ADMIN_UPDATE_LEADS_INFO', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $id . ")";
                    $db->query($sql);

                    $nv_Cache->delMod($module_name);
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Add Leads', 'ID: ' . $id, $admin_id);
                    // gọi code đẩy dữ liệu lên ES luôn
                    update_es();
                    $this->result->setSuccess();
                    $this->result->set('leadsid', $id);
                } else {
                    $this->result->setError()
                        ->setCode('4001')
                        ->setMessage('Leadsid not response');
                }
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode($error_code)
                ->setMessage($error)
                ->getResult();
        }
        return $this->result->getResult();
    }
}
