<?php
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));

require NV_CONSOLE_DIR . '/server.php';
require NV_ROOTDIR . '/includes/mainfile.php';

$db->query('TRUNCATE TABLE ' . $db_config['prefix'] . '_api_role');
$db->query('TRUNCATE TABLE ' . $db_config['prefix'] . '_api_role_credential');
$db->query('TRUNCATE TABLE ' . $db_config['prefix'] . '_api_user');

$sth = $db->prepare('INSERT IGNORE INTO ' . $db_config['prefix'] . '_api_role (
    role_id, role_md5title, role_type, role_object, role_title, role_description, role_data, addtime, edittime, flood_rules
) VALUES (
    :role_id, :role_md5title, :role_type, :role_object, :role_title, :role_description, :role_data, :addtime, :edittime, \'[]\'
)');

$db_slave->sqlreset()->select('*')->from(NV_AUTHORS_GLOBALTABLE . '_api_role')->order('role_id ASC');
$result = $db_slave->query($db_slave->sql());
while ($row = $result->fetch()) {
    $row['role_md5title'] = md5($row['role_title']);
    $row['role_type'] = 'private';
    $row['role_object'] = 'admin';
    $row['role_data'] = !empty($row['role_data']) ? unserialize($row['role_data']) : [];
    $row['role_data'] = json_encode($row['role_data']);

    $sth->bindParam(':role_id', $row['role_id'], PDO::PARAM_INT);
    $sth->bindParam(':role_md5title', $row['role_md5title'], PDO::PARAM_STR);
    $sth->bindParam(':role_type', $row['role_type'], PDO::PARAM_STR);
    $sth->bindParam(':role_object', $row['role_object'], PDO::PARAM_STR);
    $sth->bindParam(':role_title', $row['role_title'], PDO::PARAM_STR);
    $sth->bindParam(':role_description', $row['role_description'], PDO::PARAM_STR);
    $sth->bindParam(':role_data', $row['role_data'], PDO::PARAM_STR);
    $sth->bindParam(':addtime', $row['addtime'], PDO::PARAM_INT);
    $sth->bindParam(':edittime', $row['edittime'], PDO::PARAM_INT);
    $sth->execute();
    echo 'Thêm row vào bảng ' . $db_config['prefix'] . "_api_role<br/>\n";
}
echo "<br/>\n";

$sth = $db->prepare('INSERT IGNORE INTO ' . $db_config['prefix'] . '_api_role_credential (
    userid, role_id, addtime
) VALUES (
    :userid, :role_id, :addtime
)');

$api_user = [];
$api_credentials = [];
$db_slave->sqlreset()->select('*')->from(NV_AUTHORS_GLOBALTABLE . '_api_credential');
$result = $db_slave->query($db_slave->sql());
while ($row = $result->fetch()) {
    $row['userid'] = $row['admin_id'];
    $row['api_roles'] = array_filter(array_map('intval', explode(',', $row['api_roles'])));
    $row['ips'] = !empty($row['credential_ips']) ? $row['credential_ips'] : '[]';
    if (!isset($api_user[$row['userid']])) {
        $api_user[$row['userid']] = [
            'ident' => $row['credential_ident'],
            'secret' => $row['credential_secret'],
            'ips' => $row['ips'],
            'method' => $row['auth_method'],
            'addtime' => $row['addtime']
        ];
    }

    foreach ($row['api_roles'] as $role_id) {
        if (!isset($api_credentials[$row['userid'] . '_' . $role_id])) {
            $sth->bindParam(':userid', $row['userid'], PDO::PARAM_INT);
            $sth->bindParam(':role_id', $role_id, PDO::PARAM_INT);
            $sth->bindParam(':addtime', $row['addtime'], PDO::PARAM_INT);
            $sth->execute();
            echo 'Thêm row vào bảng ' . $db_config['prefix'] . "_api_role_credential<br/>\n";
            $api_credentials[$row['userid'] . '_' . $role_id] = 0;
        }
    }
}
echo "<br/>\n";

if (!empty($api_user)) {
    $sth = $db->prepare('INSERT IGNORE INTO ' . $db_config['prefix'] . '_api_user (
        userid, ident, secret, ips, method, addtime
    ) VALUES (
        :userid, :ident, :secret, :ips, :method, :addtime
    )');

    foreach ($api_user as $userid => $row) {
        $sth->bindParam(':userid', $userid, PDO::PARAM_INT);
        $sth->bindParam(':ident', $row['ident'], PDO::PARAM_STR);
        $sth->bindParam(':secret', $row['secret'], PDO::PARAM_STR);
        $sth->bindParam(':ips', $row['ips'], PDO::PARAM_STR);
        $sth->bindParam(':method', $row['method'], PDO::PARAM_STR);
        $sth->bindParam(':addtime', $row['addtime'], PDO::PARAM_INT);
        $sth->execute();
        echo 'Thêm row vào bảng ' . $db_config['prefix'] . "_api_user<br/>\n";
    }
}
echo "<br/>\n";
echo "Kết thúc chuyển CSDL<br/>\n";

echo "\n<br><br>Cập nhật trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
die("Thống kê xong");