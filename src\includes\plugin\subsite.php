<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_MAINFILE')) {
    exit('Stop!!!');
}

/**
 * Xử lý hệ thống cho các miền con (chỉ xử lý trên site).
 *
 * Nếu module supportticket => Bắt buộc chạy tên miền support.dauthau.net
 * Nếu module calendar, redday => Bắt buộc chạy tên miền lich.dauthau.net
 *
 * Các module khác thì chạy theo tên miền truy cập
 */
nv_add_hook($module_name, 'modify_global_config', $priority, function () {
    if (!defined('NV_SYSTEM') or defined('IS_DEV_LOCAL')) {
        return;
    }
    global $global_config, $nv_Request;

    $domain_define = [
        'lich.dauthau.net' => [
            'home' => 'calendar',
            'modules' => [
                'calendar',
                'redday',
            ]
        ],
        'support.dauthau.net' => [
            'home' => 'supportticket',
            'modules' => [
                'supportticket',
            ]
        ],
    ];
    $module_name = $nv_Request->get_title(NV_NAME_VARIABLE, 'get,post', '');

    if (!empty($module_name)) {
        foreach ($domain_define as $domain => $modules) {
            if (in_array($module_name, $modules['modules']) and NV_SERVER_NAME != $domain) {
                nv_redirect_location('https://' . $domain . '/');
            }
        }
    }

    if (!isset($domain_define[NV_SERVER_NAME])) {
        return;
    }

    $global_config['site_home_module'] = $domain_define[NV_SERVER_NAME]['home'];
});
