<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Tue, 30 Jan 2018 01:28:23 GMT
 */
use NukeViet\Api\DoApi;
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$table_orders_general = NV_PREFIXLANG . '_bidding_orders_general';
$arr_status = [
    0 => $nv_Lang->getModule('history_payment_no'),
    1 => $nv_Lang->getModule('history_payment_send'),
    2 => $nv_Lang->getModule('history_payment_check'),
    3 => $nv_Lang->getModule('history_payment_cancel'),
    4 => $nv_Lang->getModule('history_payment_yes')
];

$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_crmbidding_groups_users';
$result = $db->query($sql);
$array_groups_users = array();
while ($row = $result->fetch()) {
    $row['config'] = json_decode($row['config'], true);
    $array_groups_users[$row['userid']] = $row;
}

$array_search = array();
$array_search['static_time_from'] = $nv_Request->get_title('static_time_from', 'post,get', '');
$array_search['static_time_to'] = $nv_Request->get_title('static_time_to', 'post,get', '');
$array_search['create_from'] = $nv_Request->get_title('create_from', 'post,get', '');
$array_search['create_to'] = $nv_Request->get_title('create_to', 'post,get', '');
$array_search['admin_id'] = $nv_Request->get_int('admin_id', 'post,get', -1);
$array_search['chot_don'] = $nv_Request->get_int('admin_id', 'post,get', -1);

$crm_config = $module_config['crmbidding'];
$arr_admin_view = $arr_admin_view_tmp = [];
if ($crm_config['view_static'] == 2 and !defined('NV_IS_SPADMIN')) {
    if (isset($array_groups_users[$admin_info['userid']]) and $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 0) {
        if ($array_groups_users[$admin_info['userid']]['is_leader'] == 1) {
            $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_crmbidding_groups_users WHERE group_id = ' . $array_groups_users[$admin_info['userid']]['group_id'] . ' AND userid != ' . $admin_info['userid'];
            $_result = $db->query($_sql);
            while ($_row_groups_users = $_result->fetch()) {
                $arr_admin_view[$_row_groups_users['userid']] = $_row_groups_users['userid'];
            }
            $arr_admin_view[$admin_info['userid']] = $admin_info['userid'];
            $arr_admin_view_tmp = $arr_admin_view;
            if ($array_search['admin_id'] != 0) {
                $arr_admin_view = [];
            }
        } else {
            $array_search['admin_id'] = $admin_info['userid'];
        }
    }
}
$q = $nv_Request->get_title('q', 'get', '');
$showheader = $nv_Request->get_int('showheader', 'post,get', 1);
$userid = $nv_Request->get_int('userid', 'post,get', 0);
$opportunities_id = $nv_Request->get_int('opportunities_id', 'post,get', 0);
$vip = $nv_Request->get_title('vip', 'get', '');
$renewal = $nv_Request->get_int('renewal', 'get', 2);
$viewall = $nv_Request->get_int('viewall', 'get', 0);
$search_admin = $nv_Request->get_int('caregiver_id', 'get', 0);
// $search_status = $nv_Request->get_int('search_status', 'get', -1);
$search_website = $nv_Request->get_int('search_website', 'get', -1);

$show_order_admin = $nv_Request->get_int('show_order_admin', 'get', 0);

if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['static_time_from'], $m)) {
    $sfrom = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
} else {
    $sfrom = 0;
}
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['static_time_to'], $m)) {
    $sto = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
} else {
    $sto = 0;
}
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['create_from'], $m)) {
    $create_from = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
} else {
    $create_from = 0;
}
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['create_to'], $m)) {
    $create_to = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
} else {
    $create_to = 0;
}
$source_money = $nv_Request->get_int('source_money', 'get', -1);
$num_items = 0;
$per_page = 50;
$page = $nv_Request->get_int('page', 'get', 1);
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;

$customer_search = [];
if (!empty($q)) {
    // Tìm thông tin đơn hàng theo email, name, ...etc
    $base_url .= '&amp;q=' . urlencode($q);
    $sql = "SELECT userid, username, first_name, last_name, email FROM " . NV_USERS_GLOBALTABLE . " WHERE username LIKE '%" . $db->dblikeescape($q) . "%' OR first_name LIKE '%" . $db->dblikeescape($q) . "%' OR last_name LIKE '%" . $db->dblikeescape($q) . "%' OR email LIKE '%" . $db->dblikeescape($q) . "%'";
    if (preg_match('/^[0-9\+\-]+$/', $q)) { // Nếu chuỗi tìm kiếm là dãy số thì tìm theo sđt, mst
        $q2 = preg_replace('/^\+84/', '', $q);
        $sql = "SELECT userid FROM " . $db_config['prefix'] . "_users_info WHERE phone LIKE '%" . $db->dblikeescape($q2) . "%' OR mst LIKE '%" . $db->dblikeescape($q2) . "%'";
    }
    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        $customer_search[] = $row['userid'];
    }
}
$where = [];
if ($search_admin != 0) {
    $base_url .= '&amp;a=' . $search_admin;
    if ($search_admin < 0) {
        $where['AND'][] = [
            '=' => [
                'caregiver_id' => 0
            ]
        ];
    } else {
        $where['AND'][] = [
            '=' => [
                'caregiver_id' => $search_admin
            ]
        ];
    }
}

if ($array_search['chot_don'] > -1) {
    $base_url .= '&amp;chot_don=' . $array_search['chot_don'];
    if ($array_search['chot_don'] < 0) {
        $where['AND'][] = [
            '=' => [
                'admin_id' => 0
            ]
        ];
    } else {
        $where['AND'][] = [
            '=' => [
                'admin_id' => $array_search['chot_don']
            ]
        ];
        // $where[] = 'tb1.caregiver_id=' . $search_admin;
    }
}

if (!empty($customer_search)) {
    $where['AND'][] = [
        'IN' => [
            'userid' => '(' . implode(',', $customer_search) . ')'
        ]
    ];
    // $where[] = 'tb1.customer_id IN (' . implode(',', $customer_search) . ')';
}
// if ($search_status > -1) {
// $base_url .= '&amp;search_status=' . $search_status;
// // $where[] = 'status=' . $search_status;
// $where['AND'][] = [
// '=' => [
// 'status' => $search_status
// ]
// ];
// }

if ($search_website > -1) {
    $base_url .= '&amp;search_website=' . $search_website;
    // $where[] = 'siteid=' . $search_website;
    $where['AND'][] = [
        '=' => [
            'siteid' => $search_website
        ]
    ];
}

if ($create_from > 0 and $create_to > 0) {
    $base_url .= '&create_from=' . $array_search['create_from'] . '&create_to=' . $array_search['create_to'];
    // $where[] = 'static_time >= ' . $sfrom . ' AND static_time <=' . $sto . '';
    $where['AND'][] = [
        '>=' => [
            'add_time' => $create_from
        ],
        '<=' => [
            'add_time' => $create_to
        ]
    ];
}
if ($vip) {
    $base_url .= '&amp;vip=' . $vip;
    // $where[] = "vip = " . $vip;
    $where['AND'][] = [
        '=' => [
            'vip' => $vip == 'nopphixacthuc' ? '' : $vip
        ]
    ];
    if ($renewal == 1) {
        // $where[] = "is_renewal = 1";
        $where['AND'][] = [
            '=' => [
                'is_renewal' => 1
            ]
        ];
    } else if ($renewal == 0) {
        // $where[] = "is_renewal = 0";
        $where['OR'][] = [
            '=' => [
                'is_renewal' => 0
            ]
        ];

        $where['OR'][] = [
            '=' => [
                'is_renewal' => 2
            ]
        ];
    }
}

if (!empty($arr_admin_view)) {
    // $where[] = ' (admin_id IN (' . implode(',', $arr_admin_view) . ') OR caregiver_id IN (' . implode(',', $arr_admin_view) . '))';
    $where['OR'][] = [
        'IN' => [
            'admin_id' => '(' . implode(',', $arr_admin_view) . ')'
        ]
    ];
    $where['OR'][] = [
        'IN' => [
            'caregiver_id' => '(' . implode(',', $arr_admin_view) . ')'
        ]
    ];
}

// } else if ($array_search['admin_id'] > 0) {
// // $where[] = ' (admin_id = ' . $array_search['admin_id'] . ' OR caregiver_id = ' . $array_search['admin_id'] . ')';
// $where['OR'][] = [
// '=' => [
// 'admin_id' => $array_search['admin_id'],
// ],
// ];
// $where['OR'][] = [
// '=' => [
// 'caregiver_id' => $array_search['admin_id'],
// ],
// ];
// }
if ($show_order_admin == 2) {
    $where['AND'][] = [
        '=' => [
            'official_collaborator' => 1
        ]
    ];
}
// if ($show_order_admin == 1 and $caregiver_id > 0) {
// $base_url .= '&amp;caregiver_id=' . $caregiver_id . '&amp;showheader=' . $showheader . '&show_order_admin=1&static_time_from=' . $array_search['static_time_from'] . '&static_time_to=' . $array_search['static_time_to'];
// // $where[] = '(caregiver_id=' . $caregiver_id . ' OR admin_id=' . $caregiver_id . ') AND status IN (1,4) AND is_expired = 0';
// $where['OR'][] = [
// '=' => [
// 'caregiver_id' => $caregiver_id,
// ],
// ];
// $where['OR'][] = [
// '=' => [
// 'admin_id' => $caregiver_id,
// ],
// ];
// $where['AND'][] = [
// 'IN' => [
// 'status' => '(1, 4)',
// ],
// ];
// $where['AND'][] = [
// '=' => [
// 'is_expired' => 0,
// ],
// ];
// } else if ($show_order_admin == 1 and $caregiver_id == 0) {
// $base_url .= '&amp;caregiver_id=' . $caregiver_id . '&amp;showheader=' . $showheader . '&show_order_admin=1&static_time_from=' . $array_search['static_time_from'] . '&static_time_to=' . $array_search['static_time_to'];
// // $where[] = '(affiliate_userid=' . $caregiver_id . ' OR caregiver_id=' . $caregiver_id . ' OR admin_id=' . $caregiver_id . ') AND status IN (1,4) AND is_expired = 0';
// $where['OR'][] = [
// '=' => [
// 'affiliate_userid' => $caregiver_id,
// ],
// ];
// $where['OR'][] = [
// '=' => [
// 'caregiver_id' => $caregiver_id,
// ],
// ];
// $where['OR'][] = [
// '=' => [
// 'admin_id' => $caregiver_id,
// ],
// ];
// $where['AND'][] = [
// 'IN' => [
// 'status' => '(1, 4)',
// ],
// ];
// $where['AND'][] = [
// '=' => [
// 'is_expired' => 0,
// ],
// ];
// }
if ($sfrom > 0 and $sto > 0) {
    $base_url .= '&static_time_from=' . $array_search['static_time_from'] . '&static_time_to=' . $array_search['static_time_to'];
    // $where[] = 'static_time >= ' . $sfrom . ' AND static_time <=' . $sto . '';
    $where['AND'][] = [
        '>=' => [
            'static_time' => $sfrom
        ]
    ];

    $where['AND'][] = [
        '<=' => [
            'static_time' => $sto
        ]
    ];
}

$base_url .= '&showheader=' . $showheader;
if ($source_money != -1) {
    $base_url .= '&source_money=' . $source_money;
    // $where[] = 'source_money = ' . $source_money . '';
    $where['AND'][] = [
        '=' => [
            'source_money' => $source_money
        ]
    ];
}

$array_order['add_time'] = 'DESC';

$params_info = [
    'userid' => $admin_info['userid'],
    'page' => $nv_Request->get_int('page', 'post,get', 1),
    'perpage' => $per_page,
    'order' => $array_order
];

if (!empty($where)) {
    $params_info['where'] = $where;
}
if (!empty($q) && empty($customer_search)) {
    $data_info = [];
} else {
    $json_info = nv_local_api('ListAllOrdersGeneral', $params_info, 'admin', 'crmbidding');
    $data_info = json_decode($json_info, true);
}

if (!empty($data_info['data'])) {
    $num_items = $data_info['total'];
    $row_order = $data_info['data'];
    $page = $data_info['page'];
    $per_page = $data_info['perpage'];
}
$array = [];
$array_userids = $array_business = $array_soclictor = [];
$array_admin = array(
    0 => [
        $nv_Lang->getModule('marketing'),
        $nv_Lang->getModule('marketing')
    ]
);

if (!empty($row_order)) {
    /* $arr_orders_general = [];
    $count_order = [];
    foreach ($row_order as $row) {
        $arr_orders_general[$row['order_id']] = $row['order_id'];
        if (isset($count_order[$row['order_id']])) {
            ++$count_order[$row['order_id']];
        } else {
            $count_order[$row['order_id']] = 1;
        }

        $row_order[$row['id']]['count'] = $count_order[$row['order_id']];
    }

    if (!empty($arr_orders_general)) {
        $where = [];
        $where['AND'][] = [
            'IN' => [
                'id' => '(' . implode(',', $arr_orders_general) . ')'
            ]
        ];
        $infoAPI = [
            'where' => $where
        ];

        $api = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
        $api->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingOrder')
            ->setData($infoAPI);
        $result = $api->execute();
        $error = $api->getError();
        if (empty($error) and $result['status'] == 'success') {
            if (isset($result['data'])) {
                $arr_orders = $result['data'];
            }
        }
    } */

    foreach ($row_order as $row) {
        /* if ($row['siteid'] == 1 and isset($arr_orders[$row['order_id']])) {
            $row['discount_excess'] = $arr_orders[$row['order_id']]['discount_excess'] / $row['count'];
        } */

        $array[$row['order_id'] . $row['vip'] . $row['type_export']] = $row;
        if (!empty($row['userid'])) {
            $array_userids[$row['userid']] = $row['userid'];
        }

        if (!empty($row['business_id']) and $row['vip'] == 33) {
            $array_business[$row['business_id']] = $row['business_id'];
        }
        if (!empty($row['business_id']) and $row['vip'] == 44) {
            $array_soclictor[$row['business_id']] = $row['business_id'];
        }

        if (!empty($row['admin_id']) and !isset($array_admin[$row['admin_id']])) {
            $array_admin[$row['admin_id']] = [
                '',
                ''
            ];
        }
        if (!empty($row['caregiver_id']) and !isset($array_admin[$row['caregiver_id']])) {
            $array_admin[$row['caregiver_id']] = [
                '',
                ''
            ];
        }
        if (!empty($row['affiliate_userid']) and !isset($array_admin[$row['affiliate_userid']])) {
            $array_admin[$row['affiliate_userid']] = [
                '',
                ''
            ];
        }
        if (!empty($row['promo_userid']) and !isset($array_admin[$row['promo_userid']])) {
            $array_admin[$row['promo_userid']] = [
                '',
                ''
            ];
        }
    }
}

// Lấy các thành viên thanh toán của giao dịch này
$array_users = [];
if (!empty($array_userids)) {
    $sql = "SELECT userid, username, first_name, last_name, email FROM " . NV_USERS_GLOBALTABLE . " WHERE userid IN(" . implode(',', $array_userids) . ")";
    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        $array_users[$row['userid']] = $row;
    }
}

// Xác định người phụ trách
$sql = 'SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (SELECT admin_id FROM ' . NV_AUTHORS_GLOBALTABLE . ' WHERE is_suspend = 0) AND active = 1';
$result = $db->query($sql);
while ($_user_info = $result->fetch()) {
    $array_admin[$_user_info['userid']][0] = nv_show_name_user($_user_info['first_name'], $_user_info['last_name'], $_user_info['username']);
    $array_admin[$_user_info['userid']][1] = $_user_info['username'];
    if (!empty($arr_admin_view_tmp)) {
        if (in_array($_user_info['userid'], $arr_admin_view_tmp)) {
            $array_admin_global[$_user_info['userid']] = $_user_info;
        }
    } else {
        $array_admin_global[$_user_info['userid']] = $_user_info;
    }
}

// Lấy các nhà thầu thanh toán của giao dịch này
$arr_business = [];
if (!empty($array_business)) {
    $_where = [];
    $_where['AND'][] = [
        'IN' => [
            'id' => '(' . implode(',', $array_business) . ')'
        ]
    ];
    $params_business = [
        'userid' => $admin_info['userid'],
        'page' => 1,
        'perpage' => 50,
        'where' => $_where
    ];

    $api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
    $api->setModule('businesslistings')
        ->setLang('vi')
        ->setAction('ListAllBusinessListings')
        ->setData($params_business);
    $result_array_business = $api->execute();
    if (!empty($result_array_business['data'])) {
        foreach ($result_array_business['data'] as $key => $row) {
            $arr_business[$row['id']] = $row;
        }
    }
}

$arr_soclictor = [];
if (!empty($array_soclictor)) {
    $_where = [];
    $_where['AND'][] = [
        'IN' => [
            'id' => '(' . implode(',', $array_soclictor) . ')'
        ]
    ];
    $params_business = [
        'userid' => $admin_info['userid'],
        'page' => 1,
        'perpage' => 50,
        'where' => $_where
    ];

    $api = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
    $api->setModule('bidding')
        ->setLang('vi')
        ->setAction('ListAllSolicitor')
        ->setData($params_business);
    $result_array_business = $api->execute();
    if (!empty($result_array_business['data'])) {
        foreach ($result_array_business['data'] as $key => $row) {
            $arr_soclictor[$row['id']] = $row;
        }
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('Q', $q);
$xtpl->assign('ARRAY_SEARCH', $array_search);
$static_total = array();
$static_total['money'] = 0;
$static_total['discount'] = 0;
$static_total['total'] = 0;
$static_total['price_reduce'] = 0;
$static_total['total_end'] = 0;
$static_total['discount_excess'] = 0;
$static_total['introduce_value'] = 0;
$static_total['successful_value'] = 0;
$static_total['caregiver_value'] = 0;
$static_total['total_percent'] = 0;
$stt = ($page - 1) * $per_page;

foreach ($array as $view) {
    if (defined('NV_IS_SPADMIN') or $view['caregiver_id'] == $admin_info['userid'] or $array_groups_users[$admin_info['userid']]['config']['show_chart'] == 1 or $array_groups_users[$admin_info['userid']]['is_leader'] == 1) {
        if ($view['siteid'] == 2) {
            $view['link_detail'] = URL_DTNET_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dn&amp;' . NV_OP_VARIABLE . '=payment&amp;vieworderid=' . $view['order_id'];
        } else {
            $view['link_detail'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;vieworderid=' . $view['order_id'];
        }
    } else {
        $view['link_detail'] = '#';
    }
    $stt = $stt + 1;
    $view['stt'] = $stt;
    $view['add_time'] = nv_date('H:i, d/m/Y', $view['add_time']);
    $view['static_time'] = $view['static_time'] ? nv_date('d/m/Y', $view['static_time']) : '-';

    if (empty($view['promo_code'])) {
        $view['promo_code'] = $nv_Lang->getModule('order_no_promocode');
    } else {
        $view['promo_code'] = $nv_Lang->getModule('order_promocode') . ' <strong class="text-info">' . $view['promo_code'] . '</strong>';
    }

    $view['discount_excess'] = isset($view['discount_excess']) ? $view['discount_excess'] : 0;

    $static_total['money'] += $view['money'];
    $static_total['discount'] += $view['discount'];
    $static_total['total'] += $view['total'];
    $static_total['price_reduce'] += $view['price_reduce'];
    $static_total['total_end'] += $view['total_end'];
    $static_total['discount_excess'] += $view['discount_excess'];
    $static_total['introduce_value'] += $view['introduce_value'];
    $static_total['successful_value'] += $view['successful_value'];
    $static_total['caregiver_value'] += $view['caregiver_value'];

    $view['total_percent'] = $view['introduce_value'] + $view['successful_value'] + $view['caregiver_value'];
    $static_total['total_percent'] += $view['total_percent'];

    $view['money'] = number_format($view['money'], 0, ',', '.');
    $view['total'] = number_format($view['total'], 0, ',', '.');
    $view['discount'] = number_format($view['discount'], 0, ',', '.');
    $view['total_end'] = number_format($view['total_end'], 0, ',', '.');
    $view['price_reduce'] = number_format($view['price_reduce'], 0, ',', '.');
    $view['discount_excess'] = number_format($view['discount_excess'], 0, ',', '.');

    $view['affilicate_value'] = number_format($view['affilicate_value'], 0, ',', '.');
    $view['introduce_value'] = number_format($view['introduce_value'], 0, ',', '.');
    $view['successful_value'] = number_format($view['successful_value'], 0, ',', '.');
    $view['caregiver_value'] = number_format($view['caregiver_value'], 0, ',', '.');

    $view['full_name_affiliate_userid'] = (isset($array_admin[$view['affiliate_userid']]) and $array_admin[$view['affiliate_userid']][0] != '') ? $array_admin[$view['affiliate_userid']][0] : $nv_Lang->getModule('marketing');

    $view['full_name_admin'] = isset($array_admin[$view['admin_id']]) ? $array_admin[$view['admin_id']][0] : $nv_Lang->getModule('marketing');
    $view['username_admin'] = isset($array_admin[$view['admin_id']]) ? $array_admin[$view['admin_id']][1] : $nv_Lang->getModule('marketing');
    $view['full_name_caregiver_id'] = isset($array_admin[$view['caregiver_id']]) ? $array_admin[$view['caregiver_id']][0] : '';
    $view['username_caregiver_id'] = isset($array_admin[$view['caregiver_id']]) ? $array_admin[$view['caregiver_id']][1] : '';
    $view['siteid'] = isset($array_site[$view['siteid']]) ? $array_site[$view['siteid']] : '';

    $xtpl->assign('VIEW', $view);

    if (!empty($view['business_id']) and isset($arr_business[$view['business_id']]) and $view['source_leads'] == 2) {
        $xtpl->assign('BINFO', $arr_business[$view['business_id']]);
        $xtpl->parse('main.loop.business');
    } else if (!empty($view['business_id']) and isset($arr_soclictor[$view['business_id']]) and $view['source_leads'] == 15) {
        $xtpl->assign('SINFO', $arr_soclictor[$view['business_id']]);
        $xtpl->parse('main.loop.soclictor');
    } else if ($view['userid'] and isset($array_users[$view['userid']])) {
        $xtpl->assign('USERINFO', $array_users[$view['userid']]);
        $xtpl->parse('main.loop.user');
    } else {
        $xtpl->parse('main.loop.visitor');
    }

    if ($view['affilicate_value'] != '') {
        $xtpl->parse('main.loop.affilicate_value');
    }

    $xtpl->assign('STATUS', $arr_status[$view['status']]);

    // Hiển thị mã giao dịch và link giao dịch
    if (!empty($view['transaction_id'])) {
        $xtpl->assign('TRANSACTION_CODE', sprintf('%010s', $view['transaction_id']));
        if (!defined('NV_IS_SPADMIN')) {
            $xtpl->parse('main.loop.transaction');
        } else {
            $xtpl->assign('TRANSACTION_LINK', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=wallet&amp;' . NV_OP_VARIABLE . '=viewtransaction&amp;id=' . $view['transaction_id']);
            $xtpl->parse('main.loop.transaction_link');
        }
    }

    $xtpl->parse('main.loop');
}

$link_order_info = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment';
$link_order_net = URL_DTNET_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dn&amp;' . NV_OP_VARIABLE . '=payment';

$payment_note_order = sprintf($nv_Lang->getModule('payment_note_order'), $link_order_info, $link_order_net, $link_order_info, $link_order_net);
if (!empty($payment_note_order)) {
    $xtpl->assign('payment_note_order', $payment_note_order);
    $xtpl->parse('main.payment_note_order');
}

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
$xtpl->assign('NUM_ITEMS', $num_items);

foreach ($static_total as $key => $value) {
    $static_total[$key] = number_format($value);
}
$xtpl->assign('STATIC_TOTAL', $static_total);

if (!empty($generate_page)) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}
// Xuất danh sách admin ra
foreach ($array_admin_global as $_user_info) {
    $fullname = nv_show_name_user($_user_info['first_name'], $_user_info['last_name'], $_user_info['userid']);
    $xtpl->assign('ADMIN', array(
        'key' => $_user_info['userid'],
        'title' => $_user_info['username'] . ' (' . $fullname . ')',
        'selected_search' => $search_admin == $_user_info['userid'] ? ' selected="selected"' : ''
    ));
    $xtpl->assign('CHOT_DON', array(
        'key' => $_user_info['userid'],
        'title' => $_user_info['username'] . ' (' . $fullname . ')',
        'selected_search' => $array_search['chot_don'] == $_user_info['userid'] ? ' selected="selected"' : ''
    ));
    $xtpl->parse('main.loop_admin');
    $xtpl->parse('main.search.loop_admin_search');
    $xtpl->parse('main.search.loop_chot_don');
}

// foreach ($arr_status as $key => $value) {
// $xtpl->assign('STATUS', array(
// 'key' => $key,
// 'title' => $value,
// 'selected_search' => $search_status == $key ? ' selected="selected"' : ''
// ));
// $xtpl->parse('main.search.loop_status');
// }

foreach ($array_site as $key => $value) {
    $xtpl->assign('WEB', array(
        'key' => $key,
        'title' => $value,
        'selected_web' => $search_website == $key ? ' selected="selected"' : ''
    ));
    $xtpl->parse('main.search.loop_website');
}
if ($showheader) {
    // Hiển thị nút thêm đơn hàng với admin tối cao
    if (defined('NV_IS_GODADMIN')) {
        $xtpl->parse('main.search.addOrder');
    }
    $xtpl->parse('main.search');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('payment');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';
