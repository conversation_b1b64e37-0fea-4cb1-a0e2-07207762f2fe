<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 16 Jan 2018 07:25:26 GMT
 */
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE') or !defined('NV_IS_MODADMIN')) die('Stop!!!');
define('NV_IS_FILE_ADMIN', true);

require NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

$allow_func = array(
    'main',
    'config',
    'transaction',
    'add_transaction',
    'statistical',
    'statistical_sales',
    'statistical_point',
    'statistical_give_points',
    'static',
);


/**
 * nv_show_log()
 *
 * @param mixed $data
 * @return
 */
function nv_show_log($data)
{
    global $module_name, $module_data, $module_file, $global_config, $module_info, $nv_Lang;

    $xtpl = new XTemplate('list_log_point.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $stt = 0;
    foreach ($data as $key => $view) {
        $stt = $stt + 1;
        $view['stt'] = $stt;
        $view['created_time'] = date('d/m/Y H:i:s',  $view['created_time']);
        $view['status'] = ($view['status'] == -1) ? $nv_Lang->getModule('minus_point') : $nv_Lang->getModule('plus_point');
        $view['class'] = ($view['status'] == -1) ? 'label-danger' : 'label-primary';
        $view['point_total'] = number_format($view['point_total']);
        $xtpl->assign("VIEW", $view);
        $xtpl->parse('main.loop');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}
