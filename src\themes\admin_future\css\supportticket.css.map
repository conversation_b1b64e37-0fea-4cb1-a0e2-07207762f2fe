{"version": 3, "sources": ["../../../../scss/admin_future/supportticket.scss", "supportticket.css"], "names": [], "mappings": "AAAA;;;;;;;EAAA;AAgBA;EACI,gBAAA;ACPJ;;ADSA;EACI,mBAAA;ACNJ", "file": "supportticket.css", "sourcesContent": ["/**\n * NukeViet Content Management System\n * @version 4.x\n * <AUTHOR> <<EMAIL>>\n * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved\n * @license GNU/GPL version 2 or any later version\n * @see https://github.com/nukeviet The NukeViet CMS GitHub project\n */\n\n// Define variable\n@import \"../functions\";\n@import \"variables\";\n@import \"variables-dark\";\n@import \"../../node_modules/bootstrap/scss/functions\";\n@import \"../../node_modules/bootstrap/scss/variables\";\n\n.mt-10 {\n    margin-top: 10px;\n}\n.mb-20 {\n    margin-bottom: 10px;\n}\n", "/**\n * NukeViet Content Management System\n * @version 4.x\n * <AUTHOR> <<EMAIL>>\n * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved\n * @license GNU/GPL version 2 or any later version\n * @see https://github.com/nukeviet The NukeViet CMS GitHub project\n */\n.mt-10 {\n  margin-top: 10px;\n}\n\n.mb-20 {\n  margin-bottom: 10px;\n}\n\n/*# sourceMappingURL=supportticket.css.map */\n"]}