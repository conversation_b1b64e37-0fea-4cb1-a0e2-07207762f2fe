<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

// Change status
if ($nv_Request->isset_request('change_status', 'post, get')) {
    $label_id = $nv_Request->get_int('label_id', 'post, get', 0);
    $content = 'NO_' . $label_id;

    $query = 'SELECT active FROM ' . TB_TICKET_LABEL . ' WHERE label_id=' . $label_id;
    $row = $db->query($query)->fetch();
    if (isset($row['active'])) {
        $active = ($row['active']) ? 0 : 1;
        $query = 'UPDATE ' . TB_TICKET_LABEL . ' SET active=' . intval($active) . ' WHERE label_id=' . $label_id;
        $db->query($query);
        $content = 'OK_' . $label_id;
    }
    $nv_Cache->delMod($module_name);
    include NV_ROOTDIR . '/includes/header.php';
    echo $content;
    include NV_ROOTDIR . '/includes/footer.php';
}

if ($nv_Request->isset_request('ajax_action', 'post')) {
    $label_id = $nv_Request->get_int('label_id', 'post', 0);
    $new_vid = $nv_Request->get_int('new_vid', 'post', 0);
    $content = 'NO_' . $label_id;
    if ($new_vid > 0) {
        $sql = 'SELECT label_id FROM ' . TB_TICKET_LABEL . ' WHERE label_id!=' . $label_id . ' ORDER BY weight ASC';
        $result = $db->query($sql);
        $weight = 0;
        while ($row = $result->fetch()) {
            ++$weight;
            if ($weight == $new_vid) {
                ++$weight;
            }
            $sql = 'UPDATE ' . TB_TICKET_LABEL . ' SET weight=' . $weight . ' WHERE label_id=' . $row['label_id'];
            $db->query($sql);
        }
        $sql = 'UPDATE ' . TB_TICKET_LABEL . ' SET weight=' . $new_vid . ' WHERE label_id=' . $label_id;
        $db->query($sql);
        $content = 'OK_' . $label_id;
    }
    $nv_Cache->delMod($module_name);
    include NV_ROOTDIR . '/includes/header.php';
    echo $content;
    include NV_ROOTDIR . '/includes/footer.php';
}

if ($nv_Request->isset_request('delete_label_id', 'get') and $nv_Request->isset_request('delete_checkss', 'get')) {
    $label_id = $label_id_log = $nv_Request->get_int('delete_label_id', 'get');
    $delete_checkss = $nv_Request->get_string('delete_checkss', 'get');
    if ($label_id > 0 and $delete_checkss == md5($label_id . NV_CACHE_PREFIX . $client_info['session_id'])) {
        $weight = 0;
        $sql = 'SELECT weight FROM ' . TB_TICKET_LABEL . ' WHERE label_id =' . $db->quote($label_id);
        $result = $db->query($sql);
        list($weight) = $result->fetch(3);

        $db->query('DELETE FROM ' . TB_TICKET_LABEL . '  WHERE label_id = ' . $db->quote($label_id));
        if ($weight > 0)         {
            $sql = 'SELECT label_id, weight FROM ' . TB_TICKET_LABEL . ' WHERE weight >' . $weight;
            $result = $db->query($sql);
            while (list($label_id, $weight) = $result->fetch(3)) {
                $weight--;
                $db->query('UPDATE ' . TB_TICKET_LABEL . ' SET weight=' . $weight . ' WHERE label_id=' . intval($label_id));
            }
        }
        $nv_Cache->delMod($module_name);
        nv_insert_logs(NV_LANG_DATA, $module_name, 'Delete Label', 'ID: ' . $label_id_log, $admin_info['userid']);
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    }
}

$row = [];
$error = [];
$row['label_id'] = $nv_Request->get_int('label_id', 'post,get', 0);
if ($nv_Request->isset_request('submit', 'post')) {
    $row['title_vi'] = nv_substr($nv_Request->get_title('title_vi', 'post', ''), 0, 249);
    $row['title_en'] = nv_substr($nv_Request->get_title('title_en', 'post', ''), 0, 249);
    $row['color'] = $nv_Request->get_title('color', 'post', '');

    if (empty($row['title_vi'])) {
        $error[] = $nv_Lang->getModule('error_required_label_vi');
    } elseif (empty($row['title_en'])) {
        $error[] = $nv_Lang->getModule('error_required_label_en');
    }

    if (empty($error)) {
        try {
            if (empty($row['label_id'])) {
                $stmt = $db->prepare('INSERT INTO ' . TB_TICKET_LABEL . ' (title_vi, title_en, color, active, weight) VALUES (:title_vi, :title_en, :color, :active, :weight)');

                $stmt->bindValue(':active', 1, PDO::PARAM_INT);

                $weight = $db->query('SELECT max(weight) FROM ' . TB_TICKET_LABEL)->fetchColumn();
                $weight = intval($weight) + 1;
                $stmt->bindParam(':weight', $weight, PDO::PARAM_INT);


            } else {
                $stmt = $db->prepare('UPDATE ' . TB_TICKET_LABEL . ' SET title_vi = :title_vi, title_en = :title_en, color = :color WHERE label_id=' . $row['label_id']);
            }
            $stmt->bindParam(':title_vi', $row['title_vi'], PDO::PARAM_STR);
            $stmt->bindParam(':title_en', $row['title_en'], PDO::PARAM_STR);
            $stmt->bindParam(':color', $row['color'], PDO::PARAM_STR);

            $exc = $stmt->execute();
            $id = $db->lastInsertId();
            if ($exc) {
                $nv_Cache->delMod($module_name);
                if (empty($row['label_id'])) {
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Add Label', 'ID: ' . $id, $admin_info['userid']);
                } else {
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Edit Label', json_encode($row), $admin_info['userid']);
                }
                nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
            }
        } catch(PDOException $e) {
            trigger_error($e->getMessage());
        }
    }
} elseif ($row['label_id'] > 0) {
    $row = $db->query('SELECT * FROM ' . TB_TICKET_LABEL . ' WHERE label_id=' . $row['label_id'])->fetch();
    if (empty($row)) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    }
} else {
    $row['label_id'] = 0;
    $row['title_vi'] = '';
    $row['title_en'] = '';
    $row['color'] = '';
}

// Fetch Limit
$show_view = false;
if (!$nv_Request->isset_request('id', 'post,get')) {
    $show_view = true;
    $per_page = 20;
    $page = $nv_Request->get_int('page', 'post,get', 1);
    $db->sqlreset()
        ->select('COUNT(*)')
        ->from('' . $db_config['prefix'] . '_' . $module_data . '_label');
    $sth = $db->prepare($db->sql());
    $sth->execute();
    $num_items = $sth->fetchColumn();

    $db->select('*')
        ->order('weight ASC')
        ->limit($per_page)
        ->offset(($page - 1) * $per_page);
    $sth = $db->prepare($db->sql());
    $sth->execute();
}

$xtpl = new XTemplate('label.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_ADMIN_THEME', 'admin_default');
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('ROW', $row);
$xtpl->assign('CAPTION', $row['label_id'] > 0 ? $nv_Lang->getModule('edit_label') : $nv_Lang->getModule('add_label'));


if ($show_view) {
    $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.view.generate_page');
    }
    $number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;
    while ($view = $sth->fetch()) {
        for($i = 1; $i <= $num_items; ++$i) {
            $xtpl->assign('WEIGHT', [
                'key' => $i,
                'title' => $i,
                'selected' => ($i == $view['weight']) ? ' selected="selected"' : ''
            ]);
            $xtpl->parse('main.view.loop.weight_loop');
        }
        $xtpl->assign('CHECK', $view['active'] == 1 ? 'checked' : '');
        $view['link_edit'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;label_id=' . $view['label_id'];
        $view['link_delete'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;delete_label_id=' . $view['label_id'] . '&amp;delete_checkss=' . md5($view['label_id'] . NV_CACHE_PREFIX . $client_info['session_id']);
        $xtpl->assign('VIEW', $view);
        $xtpl->parse('main.view.loop');
    }
    $xtpl->parse('main.view');
}


if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('label');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
