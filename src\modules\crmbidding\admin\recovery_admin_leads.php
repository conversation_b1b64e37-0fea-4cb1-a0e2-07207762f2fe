<?php
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

// chỉ tuyenhv sử dụng chức năng này để đỡ các quản trị khác bấm nhầm
if ($admin_info['admin_id'] != 8223) {
    die('Stop!!!');
}

$a = 0;
$caregiver_id = $nv_Request->get_int('caregiver_id', 'post, get', 0);
if ($caregiver_id > 0) {
    $_sql = 'SELECT id, opportunities_id FROM ' . NV_PREFIXLANG . '_crmbidding_leads WHERE caregiver_id = ' . $caregiver_id . ' ORDER BY id ASC LIMIT 200';
    $result = $db->query($_sql);
    $array_lead = $array_lead_opportunities_id = [];
    while ($_row = $result->fetch()) {
        if ($_row['opportunities_id'] > 0) {
            $array_lead_opportunities_id[$_row['id']] = $_row['id'];
        } else {
            $array_lead[$_row['id']] = $_row['id'];
        }
    }

    $log_data1 = [
        'Thu hồi leads do nhân viên nghỉ việc'
    ];

    if (!empty($array_lead)) {
        $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_crmbidding_leads SET caregiver_id = 0, status=0, updatetime = ' . NV_CURRENTTIME . ', elasticsearch = 9 WHERE id IN (' . implode(',', $array_lead) . ')');
        $exc = $stmt->execute();
        $arry_log = [];
        if ($exc) {
            foreach ($array_lead as $leads_id) {
                $arry_log[] = "(0, 1, 'LOG_SYS_UPDATE_LEADS_INFO', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data1)) . ", " . $leads_id . ")";
            }
            if (!empty($arry_log)) {
                $sql = "INSERT INTO " . NV_PREFIXLANG . "_crmbidding_alllogs (userid, log_area, log_key, log_time, log_data, leads_id) VALUES " . implode(',', $arry_log);
                $db->query($sql);
            }
        }
        ++$a;
    } else {
        $a = -1;
    }

    // lead đã lên cơ hội thì thu hồi ở cơ hội đã tạo
    if (!empty($array_lead_opportunities_id)) {
        // thu hồi ở cơ hội đã tạo
        $_opportunities = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_crmbidding_opportunities WHERE leadsid IN (' . implode(',', $array_lead_opportunities_id) . ')');
        $array_opportunities = [];
        while ($opportunities = $_opportunities->fetch()) {
            $array_opportunities[$opportunities['id']] = $opportunities['id'];
        }
        if (!empty($array_opportunities)) {
            $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_crmbidding_opportunities SET caregiver_id = 0, status=0, updatetime = ' . NV_CURRENTTIME . ', elasticsearch = 9 WHERE id IN (' . implode(',', $array_opportunities) . ') ');
            $exc = $stmt->execute();
            $arry_log = [];
            if ($exc) {
                foreach ($array_opportunities as $opportunities_id) {
                    $arry_log[] = "(0, 1, 'LOG_SYS_UPDATE_OPPOTUNITIES_INFO', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data1)) . ", " . $opportunities_id . ")";
                }
                if (!empty($arry_log)) {
                    $sql = "INSERT INTO " . NV_PREFIXLANG . "_crmbidding_alllogs (userid, log_area, log_key, log_time, log_data, leads_id) VALUES " . implode(',', $arry_log);
                    $db->query($sql);
                }
            }
        }
        ++$a;
    } else {
        $a = -1;
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);

foreach ($array_user_id_users as $value) {
    $value['selected'] = $value['userid'] == $caregiver_id ? 'selected="selected"' : '';
    $value['title'] = nv_show_name_user($value['first_name'], $value['last_name'], $value['username']);
    $xtpl->assign('CAREGIVER_ID', $value);
    $xtpl->parse('main.caregiver_id');
}

if ($a == -1) {
    $xtpl->parse('main.sussess');
} else {
    $xtpl->parse('main.reload');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('recovery_admin_leads');
include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';


