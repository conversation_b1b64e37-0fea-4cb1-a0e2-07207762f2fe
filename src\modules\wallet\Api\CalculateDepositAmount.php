<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */
namespace NukeViet\Module\wallet\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CalculateDepositAmount implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'stat';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        require_once NV_ROOTDIR . '/modules/' . $module_file . '/init.php';

        $array = [];

        // Lấy các biến để xác định được đơn hàng
        $array['from'] = $nv_Request->get_absint('from', 'post', 0);
        $array['to'] = $nv_Request->get_absint('to', 'post', 0);
        $array['userid'] = $nv_Request->get_absint('userid', 'post', 0);
        $array['except_list'] = trim($nv_Request->get_title('except_list', 'post', ''), ',');

        if (!preg_match('/^[0-9\,]+$/', $array['except_list'])) {
            return $this->result->setCode('1001')
                ->setMessage($nv_Lang->getModule('apierr_id_list'))
                ->getResult();
        }

        $sql = "SELECT COUNT(id) as recharge_day, SUM(money_total) as total_day
        FROM " . $db_config['prefix'] . "_" . $module_data . "_transaction
        WHERE status = 1 AND transaction_status = 4 AND transaction_time >= " . $array['from'] . " AND
        transaction_time <=" . $array['to'] . " AND (adminid = " . $array['userid'] . " OR customer_id = " . $array['userid'] . ")
        AND userid NOT IN (" . $array['except_list'] . ") GROUP BY adminid";
        $stat = $db->query($sql)->fetch();

        $this->result->set('data', $stat ?: [
            'recharge_day' => 0,
            'total_day' => 0
        ]);
        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
