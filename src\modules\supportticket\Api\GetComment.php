<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\supportticket\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetComment implements IApi
{

    private $result;
    private $reply_userid;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'ticket';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];

        $ticket_id = $nv_Request->get_int('ticket_id', 'post', 0);
        $log_id = $nv_Request->get_int('log_id', 'post', 0);
        $userid = $nv_Request->get_int('userid', 'post', 0);
        if ($ticket_id > 0) {
            $row = $db->query('SELECT * FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $ticket_id . ' AND log_id=' . $log_id)->fetch();
            if (empty($row)) {
                $this->result->setError()
                    ->setCode('4000')
                    ->setMessage($nv_Lang->getModule('api_error_400'));
            } else {
                $this->reply_userid = $row['reply_userid'];
                $this->result->setSuccess();
                $this->result->set('data', $row);
                $this->result->set('canEdit', $this->canEdit($userid));
            }
        } else {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Comment is not exist')
                ->getResult();
        }
        return $this->result->getResult();
    }

    private function canEdit($userid)
    {
        if ($userid == $this->reply_userid || defined('NV_IS_SPADMIN')) {
            return true;
        }

        return false;
    }
}
