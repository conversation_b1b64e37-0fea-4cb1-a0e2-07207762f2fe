<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\points\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdatePoint implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'point';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        require_once NV_ROOTDIR . '/modules/points/points.class.php';
        $point_object = new \nukeviet_points();

        $site_id = $nv_Request->get_absint('site_id', 'post', 0);
        $point = $nv_Request->get_int('point', 'post', 0);
        $userid = $nv_Request->get_absint('userid', 'post', 0);
        $message = html_entity_decode($nv_Request->get_title('message', 'post', ''));
        $status = $nv_Request->get_bool('status', 'post', true);
        $admin_id = $nv_Request->get_absint('admin_id', 'post', 0);
        $is_reward = $nv_Request->get_int('is_reward', 'post', 0);
        $expired = $nv_Request->get_absint('expired', 'post', 0);
        $sub_status = $nv_Request->get_absint('sub_status', 'post', 0);
        $type_transaction = $nv_Request->get_absint('type_transaction', 'post', 0);

        $log_id = $point_object->update($site_id, $point, $userid, $message, $status, $admin_id, $is_reward, $expired, $sub_status, $type_transaction);

        if ($point_object->isError()) {
            return $this->result->setCode('1001')->setMessage($log_id)->getResult();
        }

        $this->result->set('log_id', $log_id);
        $this->result->set('points', $point_object->my_point($userid));
        $this->result->setSuccess();

        return $this->result->getResult();
    }
}
