<!-- BEGIN: add -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<div id="pageContent">
    <form id="addCat" method="post" action="{ACTION_URL}">
        <h3 class="myh3">{PTITLE}</h3>
        <!-- BEGIN: basic_infomation -->
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover">
                <colgroup>
                    <col class="w300" />
                    <col />
                </colgroup>
                <tbody>
                    <tr>
                        <td>{LANG.title} <span style="color: red">*</span>:
                        </td>
                        <td><input title="{LANG.title}" class="form-control" type="text" name="title" value="{DATA.title}" maxlength="240" /></td>
                    </tr>
                    <tr>
                        <td>{LANG.group_description}:</td>
                        <td><input title="{LANG.group_description}" class="form-control" type="text" name="description" value="{DATA.description}" maxlength="255" /></td>
                    </tr>
                    <tr>
                        <td>{LANG.exp_time}:</td>
                        <td>
                            <div class="input-group w250 pull-left">
                                <input type="text" name="exp_time" class="form-control" value="{DATA.exp_time}" maxlength="10" />
                                <div class="input-group-btn">
                                    <button type="button" class="btn btn-default" data-toggle="opendatepicker">
                                        <i class="fa fa-calendar"></i>
                                    </button>
                                </div>
                            </div> <label class="control-label-inline">{LANG.emptyIsUnlimited}</label>
                        </td>
                    </tr>
                    <!-- BEGIN: email -->
                    <tr>
                        <td>{LANG.email}:</td>
                        <td><input title="{LANG.email}" class="form-control email required" id="email_iavim" type="text" name="email" value="{DATA.email}" maxlength="240" /></td>
                    </tr>
                    <!-- END: email -->
                </tbody>
            </table>
        </div>
        <!-- END: basic_infomation -->
        <!-- BEGIN: config -->
        <table class="table table-striped table-bordered table-hover">
            <caption>
                <em class="fa fa-file-text-o">&nbsp;</em>{LANG.access_caption_leader}
            </caption>
            <thead>
                <tr class="text-center">
                    <th class="text-center">{LANG.access_groups_add}</th>
                    <th class="text-center">{LANG.access_groups_del}</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="text-center"><input type="checkbox" {CONFIG.access_groups_add} value="1" name="access_groups_add"></td>
                    <td class="text-center"><input type="checkbox" {CONFIG.access_groups_del} value="1" name="access_groups_del"></td>
                </tr>
            </tbody>
        </table>

        <table class="table table-striped table-bordered table-hover">
            <caption>
                <em class="fa fa-users">&nbsp;</em>{LANG.config_group_type}
            </caption>
            <thead>
                <tr class="text-center">
                    <th class="text-center">{LANG.config_is_sale}</th>
                    <th class="text-center">{LANG.config_is_marketing}</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="text-center"><input type="radio" {CONFIG.check_sale} value="sale" name="type"></td>
                    <td class="text-center"><input type="radio" {CONFIG.check_marketing} value="marketing" name="type"></td>
                </tr>
            </tbody>
        </table>
        <!-- END: config -->
        <input type="hidden" name="save" value="1" />
        <p class="text-center">
            <input name="submit" type="submit" value="{LANG.save}" class="btn btn-primary w100" style="margin-top: 10px" />
        </p>
    </form>
</div>
<script type="text/javascript">
    //<![CDATA[
    $(document).ready(function() {
        $('[name="exp_time"]').datepicker({
            showOn : "both",
            dateFormat : "dd/mm/yy",
            changeMonth : true,
            changeYear : true,
            showOtherMonths : true,
            buttonImage : null,
            buttonImageOnly : true,
            buttonText: null
        });
    });
    $("form#addCat").submit(function() {
        var a = $("input[name=title]").val(), a = trim(a);
        $("input[name=title]").val(a);
        if (a == "") {
            return alert("{LANG.title_empty}"), $("input[name=title]").select(), false
        }
        var a = $(this).serialize(), b = $(this).attr("action");
        $("input[name=submit]").attr("disabled", "disabled");
        $.ajax({
            type : "POST",
            url : b,
            data : a,
            success : function(a) {
                a == "OK" ? window.location.href = "{MODULE_URL}={OP}" : (alert(a), $("input[name=submit]").removeAttr("disabled"))
            }
        });
        return false
    });
    //]]>
</script>
<!-- END: add -->
<!-- BEGIN: list -->
<div class="table-responsive">
    <table class="table table-striped table-bordered table-hover">
        <col class="w100" />
        <col span="6" />
        <thead>
            <tr class="text-center">
                <th>{LANG.weight}</th>
                <th>{LANG.title}</th>
                <th class="text-center">{LANG.add_time}</th>
                <th class="text-center">{LANG.exp_time}</th>
                <th class="text-center">{LANG.users}</th>
                <th class="text-center">{GLANG.active}</th>
                <th class="text-center">{GLANG.actions}</th>
            </tr>
        </thead>
        <tbody>
            <!-- BEGIN: loop -->
            <tr class="text-center">
                <td>
                    <!-- BEGIN: weight --> <select name="w_{GROUP_ID}" class="form-control newWeight">
                        <!-- BEGIN: loop -->
                        <option value="{NEWWEIGHT.value}"{NEWWEIGHT.selected}>{NEWWEIGHT.value}</option>
                        <!-- END: loop -->
                </select> <!-- END: weight --> <!-- BEGIN: weight_text --> {WEIGHT_TEXT} <!-- END: weight_text -->
                </td>
                <td class="text-left"><a title="{LANG.users}" href="{LOOP.link_userlist}">{LOOP.title}</a></td>
                <td>{LOOP.add_time}</td>
                <td>{LOOP.exp_time}</td>
                <td>{LOOP.number}</td>
                <td><input name="a_{GROUP_ID}" type="checkbox" class="act" value="1" {LOOP.act} /></td>
                <td>
                    <!-- BEGIN: action --> <a href="{MODULE_URL}={OP}&edit&id={GROUP_ID}" class="btn btn-default btn-xs"><i class="fa fa-edit fa-fw"></i>{GLANG.edit}</a> <!-- BEGIN: delete --> <a class="del btn btn-danger btn-xs" href="{GROUP_ID}"><i class="fa fa-trash-o fa-fw"></i>{GLANG.delete}</a> <!-- END: delete --> <!-- END: action -->
                </td>
            </tr>
            <!-- END: loop -->
        </tbody>
    </table>
</div>
<!-- BEGIN: action_js -->
<script type="text/javascript">
//<![CDATA[
$("a.del").click(function() {
    confirm("{LANG.delConfirm} ?") && $.ajax({
        type : "POST",
        url : "{MODULE_URL}={OP}",
        data : "del=" + $(this).attr("href"),
        success : function(a) {
            a == "OK" ? window.location.href = window.location.href : alert(a)
        }
    });
    return false
});
$("select.newWeight").change(function() {
    var a = $(this).attr("name").split("_"), b = $(this).val(), c = this, a = a[1];
    $("#pageContent input, #pageContent select").attr("disabled", "disabled");
    $.ajax({
        type : "POST",
        url : "{MODULE_URL}={OP}",
        data : "cWeight=" + b + "&id=" + a,
        success : function(a) {
            a == "OK" ? $("div#pageContent").load("{MODULE_URL}={OP}&list&random=" + nv_randomPassword(10)) : alert("{LANG.errorChangeWeight}");
            $("#pageContent input, #pageContent select").removeAttr("disabled")
        }
    });
    return false
});

$("input.act").change(function() {
    var a = $(this).attr("name").split("_"), a = a[1], b = this;
    $("#pageContent input, #pageContent select").attr("disabled", "disabled");
    $.ajax({
        type : "POST",
        url : "{MODULE_URL}={OP}",
        data : "act=" + a + "&rand=" + nv_randomPassword(10),
        success : function(a) {
            a = a.split("|");
            $("#pageContent input, #pageContent select").removeAttr("disabled");
            a[0] == "ERROR" && (a[1] == "1" ? $(b).prop("checked", true) : $(b).prop("checked", false));

        }
    });
    return !1;
});
//]]>
</script>
<!-- END: action_js -->
<!-- END: list -->

<!-- BEGIN: main -->
<div class="myh3">{GLANG.mod_groups}</div>
<div id="pageContent"></div>
<!-- BEGIN: addnew -->
<div id="ablist">
    <input name="addNew" type="button" value="{LANG.nv_admin_add}" class="btn btn-default" />
</div>
<!-- END: addnew -->
<script type="text/javascript">
    //<![CDATA[
    $(function() {
        $("div#pageContent").load("{MODULE_URL}={OP}&list&random=" + nv_randomPassword(10));
    });
    $("input[name=addNew]").click(function() {
        window.location.href = "{MODULE_URL}={OP}&add";
        return !1;
    });
    //]]>
</script>
<!-- END: main -->
<!-- BEGIN: listUsers -->
<!-- BEGIN: leaders -->
<div id="id_leaders">
    <h3 class="myh3">{PTITLE}</h3>
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <col class="w50" />
            <col span="3" />
            <col class="" />
            <thead>
                <tr>
                    <th class="text-center">{LANG.userid}</th>
                    <th>{LANG.account}</th>
                    <th>{LANG.nametitle}</th>
                    <th>{LANG.email}</th>
                    <th class="text-center">{GLANG.actions}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="text-center">{LOOP.userid}</td>
                    <td><a title="{LANG.detail}" href="{URL_USER}=edit&userid={LOOP.userid}">{LOOP.username}</a></td>
                    <td>{LOOP.full_name}</td>
                    <td><a href="mailto:{LOOP.email}">{LOOP.email}</a></td>
                    <td class="text-center">
                        <!-- BEGIN: tools --> <i class="fa fa-cogs fa-lg"></i> <a class="config" href="javascript:void(0);" data-id="{LOOP.userid}">{LANG.config}</a> <i class="fa fa-star-half-o fa-lg"></i> <a class="demote" href="javascript:void(0);" data-id="{LOOP.userid}">{LANG.demote}</a> <em class="fa fa-trash-o fa-lg">&nbsp;</em> <a class="deleteleader" href="javascript:void(0);" title="{LOOP.userid}">{LANG.exclude_user2}</a> <i class="fa fa-reply fa-lg"></i> <a href="javascript:void(0);" title="{LOOP.userid}" data-toggle="cGroups" data-id="{LOOP.userid}">{LANG.move_groups}</a> <!-- END: tools -->
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
    <!-- BEGIN: page -->
    <div class="text-center">{PAGE}</div>
    <!-- END: page -->
</div>
<script type="text/javascript">
//<![CDATA[
$("a.deleteleader").click(function() {
    confirm("{LANG.delConfirm} ?") && $.ajax({
        type : "POST",
        url : "{MODULE_URL}={OP}",
        data : "gid={GID}&exclude=" + $(this).attr("title"),
        success : function(a) {
            a == "OK" ? $("div#pageContent").load("{MODULE_URL}={OP}&listUsers={GID}&random=" + nv_randomPassword(10)) : alert(a);
        }
    });
    return !1;
});
$("a.demote").click(function() {
    $.ajax({
        type : "POST",
        url : "{MODULE_URL}={OP}",
        data : "gid={GID}&demote=" + $(this).data("id"),
        success : function(a) {
            a == "OK" ? $("div#pageContent").load("{MODULE_URL}={OP}&listUsers={GID}&random=" + nv_randomPassword(10)) : alert(a);
        }
    });
    return !1;
});

$("a.config").click(function() {
     $.post(script_name + '?' + nv_name_variable + '=crmbidding&' + nv_fc_variable + '=groups&nocache=' + new Date().getTime(), 'gid={GID}&id='+$(this).data("id")+'&configmember=1', function(res) {
         if (res !='') {
             $('.modal-body').empty();
             modalShow('', res);
         }
     });
    return !1;
});
//]]>
</script>
<!-- END: leaders -->
<!-- BEGIN: members -->
<div id="id_members">
    <h3 class="myh3">{PTITLE}</h3>
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <col class="w50" />
            <col span="3" />
            <col class="" />
            <thead>
                <tr>
                    <th class="text-center">{LANG.userid}</th>
                    <th>{LANG.account}</th>
                    <th>{LANG.nametitle}</th>
                    <th>{LANG.email}</th>
                    <th class="text-center">{GLANG.actions}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="text-center">{LOOP.userid}</td>
                    <td><a title="{LANG.detail}" href="{URL_USER}=edit&userid={LOOP.userid}">{LOOP.username}</a></td>
                    <td>{LOOP.full_name}</td>
                    <td><a href="mailto:{LOOP.email}">{LOOP.email}</a></td>
                    <td class="text-center">
                        <!-- BEGIN: tools --> <i class="fa fa-cogs fa-lg"></i> <a class="config" href="javascript:void(0);" data-id="{LOOP.userid}">{LANG.config}</a> <i class="fa fa-star fa-lg"></i> <a class="promote" href="javascript:void(0);" data-id="{LOOP.userid}">{LANG.promote}</a> <i class="fa fa-trash-o fa-lg"></i> <a class="deletemember" href="javascript:void(0);" title="{LOOP.userid}">{LANG.exclude_user2}</a> <i class="fa fa-reply fa-lg"></i> <a href="javascript:void(0);" title="{LOOP.userid}" data-toggle="cGroups" data-id="{LOOP.userid}">{LANG.move_groups}</a> <!-- END: tools -->
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
    <!-- BEGIN: page -->
    <div class="text-center">{PAGE}</div>
    <!-- END: page -->
</div>
<script type="text/javascript">
//<![CDATA[
$("a.deletemember").click(function() {
    confirm("{LANG.delConfirm} ?") && $.ajax({
        type : "POST",
        url : "{MODULE_URL}={OP}",
        data : "gid={GID}&exclude=" + $(this).attr("title"),
        success : function(a) {
            a == "OK" ? $("div#pageContent").load("{MODULE_URL}={OP}&listUsers={GID}&random=" + nv_randomPassword(10)) : alert(a);
        }
    });
    return !1;
});
$("a.promote").click(function() {
    $.ajax({
        type : "POST",
        url : "{MODULE_URL}={OP}",
        data : "gid={GID}&promote=" + $(this).data("id"),
        success : function(a) {
            a == "OK" ? $("div#pageContent").load("{MODULE_URL}={OP}&listUsers={GID}&random=" + nv_randomPassword(10)) : alert(a);
        }
    });
    return !1;
});
$("a.config").click(function() {
     $.post(script_name + '?' + nv_name_variable + '=crmbidding&' + nv_fc_variable + '=groups&nocache=' + new Date().getTime(), 'gid={GID}&id='+$(this).data("id")+'&configmember=1', function(res) {
         if (res !='') {
             $('.modal-body').empty();
             modalShow('', res);
         }
    });
   return !1;
});
//]]>
</script>
<!-- END: members -->
<select class="hidden" id="tmpGroups">
    <!-- BEGIN: loop_Groups -->
    <option value="{OPTION.key}">{OPTION.title}</option>
    <!-- END: loop_Groups -->
</select>
<div class="modal fade" tabindex="-1" role="dialog" id="ChangeGroups" data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" onclick="modalHide();" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">
                    <strong>{LANG.change_manager}</strong>
                </h4>
            </div>
            <div class="modal-body">
                <select class="form-control" id="NewGroups">
                </select>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" onclick="modalHide();">{LANG.cancel}</button>
                <button type="button" class="btn btn-primary" id="SaveNewGroups">{LANG.save_info}</button>
            </div>
        </div>
    </div>
</div>
<script>
    $(document).ready( function() {
        $('#ChangeGroups').on(
            'show.bs.modal',
            function(e) {
                $('#NewGroups').html($('#tmpGroups').html());
                $('#NewGroups').find(
                    'option[value="'
                        + $('#ChangeGroups').data('adminid') + '"]')
                    .prop('selected', true);
            });
        $('[data-toggle="cGroups"]').on('click', function(e) {
            e.preventDefault();
            $('#ChangeGroups').data('userid', $(this).data('id'));
            $('#ChangeGroups').modal('show');
        });
        $('#SaveNewGroups').on('click', function(e) {
            e.preventDefault();
            $.post(script_name + '?' + nv_lang_variable + '='
                + nv_lang_data + '&' + nv_name_variable + '='
                + nv_module_name + '&' + nv_fc_variable
                + '=groups&nocache=' + new Date().getTime(),
                'changegroups=1&userid='
                    + $('#ChangeGroups').data('userid')
                    + '&groups=' + $('#NewGroups').val(),
                function(res) {
                    if (res == 'OK') {
                        location.reload();
                        return;
                    } else {
                        alert(res);
                    }
              });
        });
    });
</script>
<!-- END: listUsers -->

<!-- BEGIN: userlist -->
<!-- BEGIN: adduser -->
<div id="ablist" class="form-inline">
    {LANG.search_id}: <input title="{LANG.search_id}" class="form-control txt" type="text" name="uid" id="uid" value="" maxlength="11" style="width: 50px" /> <input class="btn btn-primary" name="addUser" type="button" value="{LANG.addMemberToGroup}" /> <input class="btn btn-success" name="searchUser" type="button" value="{GLANG.search}" />
</div>
<!-- END: adduser -->
<div id="pageContent">&nbsp;</div>
<script type="text/javascript">
    //<![CDATA[
    $(function() {
        $("div#pageContent").load("{MODULE_URL}={OP}&listUsers={GID}&random=" + nv_randomPassword(10));
    });
    $("input[name=searchUser]").click(function() {
        nv_open_browse("{URL_USER}=getuserid&area=uid&filtersql={FILTERSQL}", "NVImg", 850, 420, "resizable=no,scrollbars=no,toolbar=no,location=no,status=no");
        return false;
    });
    $("input[name=addUser]").click(function() {
        var a = $("#ablist input[name=uid]").val(), a = intval(a);
        a == 0 && ( a = "");
        $("#ablist input[name=uid]").val(a);
        if (a == "") {
            return alert("{LANG.choiceUserID}"), $("#ablist input[name=uid]").select(), false;
        }
        $("#pageContent input, #pageContent select").attr("disabled", "disabled");
        $.ajax({
            type : "POST",
            url : "{MODULE_URL}={OP}",
            data : "gid={GID}&uid=" + a + "&rand=" + nv_randomPassword(10),
            success : function(a) {
                a == "OK" ? ($("#ablist input[name=uid]").val(""), $("div#pageContent").load("{MODULE_URL}={OP}&listUsers={GID}&random=" + nv_randomPassword(10))) : alert(a);
            }
        });
        return !1;
    });
    //]]>
</script>
<!-- END: userlist -->

<!-- BEGIN: config -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<table class="table table-striped table-bordered table-hover">
    <caption>
        <em class="fa fa-file-text-o">&nbsp;</em>{LANG.config_member}
    </caption>
    <tbody>
        <tr>
            <th class="text-center">{LANG.view_static_title}</th>
            <td class="text-center"><input type="checkbox" {ROW.config.show_chart} value="1" id="show_chart" name="show_chart" onclick="nv_change_label({ROW.group_id}, {ROW.userid}, 'show_chart');"></td>
            <th class="text-center">{LANG.reach_new_change}</th>
            <td class="text-center"><input type="checkbox" {ROW.config.view_leads_new} value="1" id="view_leads_new" name="view_leads_new" onclick="nv_change_label({ROW.group_id}, {ROW.userid}, 'view_leads_new');"></td>
        </tr>
        <tr>
            <th class="text-center">{LANG.chia_lead_user}</th>
            <td class="text-center"><input type="checkbox" {ROW.config.set_leads} value="1" id="set_leads" name="set_leads" onclick="nv_change_label({ROW.group_id}, {ROW.userid}, 'set_leads');"></td>
            <th class="text-center">{LANG.chia_don_hang}</th>
            <td class="text-center"><input type="checkbox" {ROW.config.set_order} value="1" id="set_order" name="set_order" onclick="nv_change_label({ROW.group_id}, {ROW.userid}, 'set_order');"></td>
        </tr>
        <tr>
            <th class="text-center">{LANG.sale_move_leads}</th>
            <td class="text-center"><input type="checkbox" {ROW.config.sale_move_leads} value="1" id="sale_move_leads" name="sale_move_leads" onclick="nv_change_label({ROW.group_id}, {ROW.userid}, 'sale_move_leads');"></td>

            <th class="text-center">{LANG.move_leads_time}</th>
            <td class="text-center"><input type="text" class="form-control" value="{ROW.config.move_leads_time}" id="move_leads_time" name="move_leads_time" onchange="nv_change_label({ROW.group_id}, {ROW.userid}, 'move_leads_time');"></td>
        </tr>
        <tr>
            <th class="text-center">{LANG.move_leads_method}</th>
            <td class="text-center"><label><input type="radio" {ROW.config.move_leads_method1} value="1" id="move_leads_method" name="move_leads_method" onclick="nv_change_label({ROW.group_id}, {ROW.userid}, 'move_leads_method');">{LANG.move_leads_method1}</label> <br /> <label><input type="radio" {ROW.config.move_leads_method2} value="2" id="move_leads_method" name="move_leads_method" onclick="nv_change_label({ROW.group_id}, {ROW.userid}, 'move_leads_method');">{LANG.move_leads_method2}</label></td>
        
            <th class="text-center">{LANG.extension_voicecloud}</th>
            <td class="text-center"><input type="text" class="form-control" value="{ROW.config.extension_voicecloud}" id="extension_voicecloud" name="extension_voicecloud" onchange="nv_change_label({ROW.group_id}, {ROW.userid}, 'extension_voicecloud');"></td>
        </tr>
        
        <tr>
            <th class="text-center">{LANG.share_messenger_zalo}</th>
            <td class="text-center"><input type="checkbox" {ROW.config.share_messenger_zalo} value="1" id="share_messenger_zalo" name="share_messenger_zalo" onclick="nv_change_label({ROW.group_id}, {ROW.userid}, 'share_messenger_zalo');"></td>
        </tr>

        <tr>
            <th class="text-center">{LANG.title_view_lead_messages}</th>
            <td class="text-center"><input type="checkbox" {ROW.config.sale_view_messages} value="1" id="sale_view_messages" name="sale_view_messages" onclick="nv_change_label({ROW.group_id}, {ROW.userid}, 'sale_view_messages');"></td>
            
            <th class="text-center">{LANG.title_sale_view_zalo}</th>
            <td class="text-center"><input type="checkbox" {ROW.config.sale_view_zalo} value="1" id="sale_view_zalo" name="sale_view_zalo" onclick="nv_change_label({ROW.group_id}, {ROW.userid}, 'sale_view_zalo');"></td>
        </tr>

        <tr>
            <th class="text-center">{LANG.manage_econtract}</th>
            <td class="text-center"><input type="checkbox" {ROW.config.manage_econtract} value="1" id="manage_econtract" name="manage_econtract" onclick="nv_change_label({ROW.group_id}, {ROW.userid}, 'manage_econtract');"></td>

            <th class="text-center">{LANG.manage_einvoice}</th>
            <td class="text-center"><input type="checkbox" {ROW.config.manage_einvoice} value="1" id="manage_einvoice" name="manage_einvoice" onclick="nv_change_label({ROW.group_id}, {ROW.userid}, 'manage_einvoice');"></td>
        </tr>
    </tbody>
</table>

<style>
.form-inline .form-control {
    width: 100% !important;
}
</style>
<form class="form-inline" action="{URL}" method="post">
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}"> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}"> <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}"> <input type="hidden" name="configmember" value="1"> <input type="hidden" name="id" value="{ID_CONFIG}"> <input type="hidden" name="gid" value="{GID_CONFIG}">
    <table class="table table-striped table-bordered table-hover table-reponsive">
        <caption>
            <em class="fa fa-file-text-o">&nbsp;</em>{LANG.setting_chiet_khau}
        </caption>
        <thead>
            <tr class="text-center">
                <th class="text-center" colspan="2">{LANG.log_time}</th>
                <th class="text-center" rowspan="2">{LANG.affilacate}</th>
                <th class="text-center" rowspan="2">{LANG.chot_don}</th>
                <th class="text-center" rowspan="2">{LANG.support}</th>
                <th class="text-center" rowspan="2">{LANG.discount_order}</th>
                <th class="text-center" rowspan="2">{LANG.discount_wallet}</th>
                <th class="text-center" rowspan="2"></th>
            </tr>
            <tr class="text-center">
                <th class="text-center">{LANG.from}</th>
                <th class="text-center">{LANG.to}</th>
            </tr>
        </thead>
        <tbody id="id-area">
            <!-- BEGIN: loop -->
            <tr id="weight_{KEY}">
                <td class="text-center"><input class="form-control w100 uidatepicker" type="text" value="{VALUE.date_from}" name="date_from_{KEY}" maxlength="10" autocomplete="off"></td>
                <td class="text-center"><input class="form-control w100 uidatepicker" type="text" value="{VALUE.date_to}" name="date_to_{KEY}" maxlength="10" autocomplete="off"></td>
                <td class="text-center"><input type="text" class="form-control" value="{VALUE.percent_referral}" name="percent_referral_{KEY}" id="percent_referral_{KEY}"></td>
                <td class="text-center"><input type="text" class="form-control" value="{VALUE.percent_order}" name="percent_order_{KEY}" id="percent_order_{KEY}"></td>
                <td class="text-center"><input type="text" class="form-control" value="{VALUE.percent_suport}" name="percent_suport_{KEY}" id="percent_suport_{KEY}"></td>
                <td class="text-center"><input type="text" class="form-control" value="{VALUE.percent_discount}" name="percent_discount_{KEY}" id="percent_discount_{KEY}"></td>
                <td class="text-center"><input type="text" class="form-control" value="{VALUE.percent_wallet}" name="percent_wallet_{KEY}" id="percent_wallet_{KEY}"></td>
                <td class="text-center"><em class="fa fa-trash-o fa-lg">&nbsp;</em><a href="javascript:void(0);" onclick="nv_remove_item({KEY});">{LANG.delete}</a> <input type="hidden" name="ids[]" value="{KEY}" /></td>
            </tr>
            <!-- END: loop -->
        </tbody>
        <tfoot>
            <tr>
                <!-- <td class="text-center" colspan="5"><input type="button" class="btn btn-primary" value="Cập nhật" name="submit_percent" onclick="nv_submit_percent({ROW.group_id}, {ROW.userid});"></td> -->
                <td class="text-center" colspan="6"><input type="submit" class="btn btn-primary" value="{LANG.update}" name="submit_percent"></td>
                <td colspan="1"><a href="javascript:void(0)" class="btn btn-success" onclick="nv_add_items()">Thêm</a></td>
            </tr>
        </tfoot>
    </table>
</form>
<script type="text/javascript">
    $(document).ready(function() {
        $('.uidatepicker').datepicker({
            showOn : "both",
            dateFormat : "dd/mm/yy",
            changeMonth : true,
            changeYear : true,
            showOtherMonths : true,
            buttonImage : null,
            buttonImageOnly : true,
            buttonText: null
        });
    });

    var num = {CONFIG_WEIGHT_COUNT}
    function nv_add_items() {
        num += 1;
        var html = '';
        html += '<tr id="weight_' + num + '">';
        html += '<td class="text-center"><input class="form-control w100 uidatepicker" type="text" value="{ARRAY_SEARCH.time_to}" name="date_from_' + num + '" maxlength="10" autocomplete="off"></td>';
        html += '<td class="text-center"><input class="form-control w100 uidatepicker" type="text" value="{ARRAY_SEARCH.time_to}" name="date_to_' + num + '" maxlength="10" autocomplete="off"></td>';
        html += '<td class="text-center"><input type="text" class="form-control" value="{ROW.config_percent.percent_referral}" name="percent_referral_' + num + '" id="percent_referral_' + num + '"></td>';
        html += '<td class="text-center"><input type="text" class="form-control" value="{ROW.config_percent.percent_order}" name="percent_order_' + num + '" id="percent_order_' + num + '"></td>';
        html += '<td class="text-center"><input type="text" class="form-control" value="{ROW.config_percent.percent_suport}" name="percent_suport_' + num + '" id="percent_suport_' + num + '"></td>';
        html += '<td class="text-center"><input type="text" class="form-control" value="{ROW.config_percent.percent_discount}" name="percent_discount_' + num + '" id="percent_discount_' + num + '"></td>';
        html += '<td class="text-center"><input type="text" class="form-control" value="{ROW.config_percent.percent_wallet}" name="percent_wallet_' + num + '" id="percent_wallet_' + num + '"></td>';
        html += '<td class="text-center"><em class="fa fa-trash-o fa-lg">&nbsp;</em><a href="javascript:void(0);" onclick="nv_remove_item(' + num + ');">Xóa</a> <input type="hidden" name="ids[]" value="' + num + '" /></td>';
        html += '</tr>';
        $('#id-area').append(html);
    }
    function nv_remove_item(num) {
        $('#weight_' + num).remove();
    }

function nv_change_label(gid, id, name) {
    var val = $('#'+name).is(':checked') ? true : false;
    if (name == 'move_leads_method') {
        val = $('input[name=move_leads_method]:checked').val();
    } else if (name == 'move_leads_time' || name == 'extension_voicecloud') {
        val = $('#'+name).val();
    }

    if (confirm(nv_is_change_act_confirm[0])) {
        var nv_timer = nv_settimeout_disable(name, 5000);
        $.post(script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=groups&nocache=' + new Date().getTime(), 'gid='+gid+'&id='+id+'&configmember=1&submit=1&name='+name+'&val='+val, function(res) {
            if (res != 'OK') {
                alert(nv_is_change_act_confirm[2]);
            }
        });
    } else{
        $('#' + name).prop('checked', new_status ? false : true);
    }
    return;
}
function nv_submit_percent(gid, id) {
    var percent_referral = $('#percent_referral ').val();
    var percent_order = $('#percent_order ').val();
    var percent_suport = $('#percent_suport ').val();
    $.post(script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=groups&nocache=' + new Date().getTime(), 'gid='+gid+'&id='+id+'&configmember=1&submit_percent=1&percent_referral=' + percent_referral + '&percent_order=' + percent_order + '&percent_suport=' + percent_suport, function(res) {
        if (res == 'OK') {
            alert("Cập nhật thành công!");
        }
    });
    return;
}
</script>
<!-- END: config -->
