<?php

/*
 * Ch<PERSON>ơng trình lì xì đầu năm cho khách hàng:
 * Tặng 100 điểm cho toàn bộ tài khoản người dùng trên DauThau.info
 * Tặng 200 điểm cho khách hàng sử dụng các gói phần mềm của DauThau.info nhưng hiện tại đã hết hạn
 * Tặng 300 điểm cho khách hàng đang sử dụng gói phần mềm bất kỳ của DauThau.info
 * Thời gian sử dụng điểm: Trong vòng 30 ngày, từ ngày 20/02/2023 đến ngày 20/03/2023
 *
 */
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __FILE__), PATHINFO_DIRNAME) . '/..')));

require NV_CONSOLE_DIR . '/server.php';
require NV_ROOTDIR . '/includes/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

$last_userid = 0;
$filelog = NV_CONSOLE_DIR . '/tool_2024/tangdiem02_2024.txt';
if (file_exists($filelog)) {
    $last_userid = intval(file_get_contents($filelog));
}

$sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . " WHERE userid > " . $last_userid . " AND active = 1 ORDER BY userid ASC LIMIT 100";
$result = $db->query($sql);
$arr_tmp = [];
while ($row = $result->fetch()) {
    $arr_tmp[$row['userid']] = $row;
}
$giveexpired = mktime(23, 59, 59, 03, 21, 2024); // 21/03/2023
if (empty($arr_tmp)) {
    echo "Đã chạy hết!!";
    exit(1);
} else {
    foreach ($arr_tmp as $userid => $row) {
        $point = 100;
        $message_log = 'Hệ sinh thái Đấu thầu lì xì đầu năm';

        // insert trực tiếp vào bảng nv4_points_users_tmp
        $sql = "INSERT INTO nv4_points_users_tmp (site_id, userid, pre_uid, type, status, addtime, givepoints, givemessage, givelog, giveexpired, key_crawl) VALUES (1, " . $userid . ", 0, 109, 0, " . NV_CURRENTTIME . ", " . $point . ", " . $db->quote($message_log) . ", " . $db->quote($message_log) . ", " . $giveexpired . ", '')";
        $exec = $db->exec($sql);
        if ($exec) {
            echo "Userid: " . $userid . ": " . $point . " điểm \n";
        }
        file_put_contents($filelog, $userid);
    }
    echo "Xong " . $userid . "\n";
}
