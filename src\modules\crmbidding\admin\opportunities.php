<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$showheader = $nv_Request->get_int('showheader', 'post,get', 1);
if ($nv_Request->isset_request('delete_id', 'get') and $nv_Request->isset_request('delete_checkss', 'get')) {
    $id = $nv_Request->get_int('delete_id', 'get');
    $delete_checkss = $nv_Request->get_string('delete_checkss', 'get');
    if ($id > 0 and $delete_checkss == md5($id . NV_CACHE_PREFIX . $client_info['session_id'])) {
        $update_data = [
            'active' => 0
        ];
        $_params_update = [
            'opportunitiesid' => $id,
            'data' => $update_data,
            'admin_id' => $admin_info['userid']
        ];
        $status_update = nv_local_api('UpdateOpportunities', $_params_update, $admin_info['username'], 'crmbidding');
        $update_data = $_params_update = [];
        nv_insert_logs(NV_LANG_DATA, $module_name, 'Delete Leads', 'ID: ' . $id, $admin_info['userid']);
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&showheader=' . $showheader);
    }
}

$row = [];
$error = [];

$q = $nv_Request->get_title('q', 'post,get');
$status = $nv_Request->get_title('status', 'post,get', -1);
$caregiver_id = $nv_Request->get_int('caregiver_id', 'post,get', 0);
$label_search = $nv_Request->get_int('label', 'post,get', 0);
$page = $nv_Request->get_int('page', 'get', 1);
$orderby_name = $nv_Request->get_int('orderby_name', 'post,get', 0);
$orderby_phone = $nv_Request->get_int('orderby_phone', 'post,get', 0);
$orderby_email = $nv_Request->get_int('orderby_email', 'post,get', 0);
$orderby_status = $nv_Request->get_int('orderby_status', 'post,get', 0);
$orderby_schedule = $nv_Request->get_int('orderby_schedule', 'post,get', 0);

$orderby_time = $nv_Request->get_int('orderby_time', 'post,get', 0);
$orderby_affc = $nv_Request->get_int('orderby_affc', 'post,get', 0);
$orderby_care = $nv_Request->get_int('orderby_care', 'post,get', 0);
$orderby_siteid = $nv_Request->get_int('orderby_siteid', 'post,get', 0);
$orderby_source_leads = $nv_Request->get_int('orderby_source_leads', 'post,get', 0);

$search_time_type = $nv_Request->get_int('search_time_type', 'post,get', 0);
$array_search = array();
$array_search['time_from'] = $nv_Request->get_title('time_from', 'post,get', '');
$array_search['time_to'] = $nv_Request->get_title('time_to', 'post,get', '');
$array_search['care_time'] = $nv_Request->get_int('care_time', 'post,get', '');
$array_search['siteid'] = $nv_Request->get_int('siteid', 'post,get', '-1');
$array_search['prefix_lang'] = $nv_Request->get_int('prefix_lang', 'post,get', '-1');
$array_search['source_leads'] = $nv_Request->get_int('source_leads', 'post,get', '-1');
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_from'], $m)) {
    $sfrom = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
} else {
    $sfrom = 0;
}
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_to'], $m)) {
    $sto = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
} else {
    $sto = 0;
}

$array_care_time = array(
    1 => '1 ngày',
    3 => '3 ngày',
    5 => '5 ngày',
    7 => '7 ngày',
    9 => '9 ngày',
    15 => '15 ngày'
);

// các user mà sale làm trưởng nhóm
$caregiver_id_leads = array();
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE userid = ' . $admin_info['userid'];
$result = $db->query($sql);
$admin_config = [];
$view_lead_messages = 0;
while ($row_groups_users = $result->fetch()) {
    $admin_config = json_decode($row_groups_users['config'], true);
    // Nếu có cấu hình có lead đó thì xem có đang bật cấu hình xem nguồn lead messages k?
    $view_lead_messages = $admin_config['sale_view_messages'] ?? 0;
    if ($row_groups_users['is_leader'] == 1) {
        $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $row_groups_users['group_id'] . ' AND userid != ' . $admin_info['userid'];
        $_result = $db->query($_sql);
        while ($_row_groups_users = $_result->fetch()) {
            $caregiver_id_leads[$_row_groups_users['userid']] = $_row_groups_users['userid'];
        }
    }
}
$caregiver_id_leads[$admin_info['userid']] = $admin_info['userid'];

// Fetch Limit
$show_view = false;
if (!$nv_Request->isset_request('id', 'post,get')) {
    $where = [];
    $params = [
        'userid' => $admin_info['userid'],
        'page' => $nv_Request->get_int('page', 'post,get', 1),
        'perpage' => 50
    ];
    $show_view = true;

    if (!empty($q)) {
        $_q = trim($db->quote($q), "'");
        $where['OR'][] = [
            'like' => [
                'name' => "%" . $_q . "%"
            ]
        ];
        $where['OR'][] = [
            'like' => [
                'phone' => "%" . $_q . "%"
            ]
        ];
        // chuẩn hóa số điện thoại
        if (phonecheck($_q)) {
            $_tmp_phone = $_q;
            if (preg_match('/(\d{9})$/', $_q, $m)) {
                $_tmp_phone = $m[0];
            }
            $_tmp_phone = preg_replace('/[^0-9]/', '', $_tmp_phone);
            $_tmp_phone = (int) $_tmp_phone;
            $where['OR'][] = [
                'like' => [
                    'phone' => "%" . $_tmp_phone . "%"
                ]
            ];
            $where['OR'][] = [
                '=' => [
                    'phone_search' => $_tmp_phone
                ]
            ];
            $where['OR'][] = [
                'FIND_IN_SET' => [
                    'sub_phone' => $_tmp_phone
                ]
            ];
        }

        $where['OR'][] = [
            'like' => [
                'email' => "%" . $_q . "%"
            ]
        ];
        $where['OR'][] = [
            'like' => [
                'tax' => "%" . $_q . "%"
            ]
        ];
        $where['OR'][] = [
            'FIND_IN_SET' => [
                'sub_phone' => $_q
            ]
        ];
        $where['OR'][] = [
            'FIND_IN_SET' => [
                'sub_email' => $_q
            ]
        ];

        // tìm cơ hội theo username của thành viên
        $lead_user = get_user_by_username($q);
        if (!empty($lead_user)) {
            $where['OR'][] = [
                '=' => [
                    'user_id' => $lead_user['userid']
                ]
            ];
        }
    }

    if ($array_search['care_time'] > 0) {
        $time = NV_CURRENTTIME - 86400 * $array_search['care_time'];
        $where['AND'][] = [
            '<=' => [
                'updatetime' => $time
            ]
        ];
    }

    if ($array_search['siteid'] != '-1') {
        $where['AND'][] = [
            '=' => [
                'siteid' => $array_search['siteid']
            ]
        ];
    }
    if ($array_search['prefix_lang'] != '-1') {
        $where['AND'][] = [
            '=' => [
                'prefix_lang' => $array_search['prefix_lang']
            ]
        ];
    }
    if ($array_search['source_leads'] != '-1') {
        $where['AND'][] = [
            '=' => [
                'source_leads' => $array_search['source_leads']
            ]
        ];
    }
    if ($sto > 0 and $sfrom > 0) {
        if ($search_time_type == 1) {
            $where['AND'][] = [
                '>=' => [
                    'timecreate' => $sfrom
                ]
            ];
            $where['AND'][] = [
                '<=' => [
                    'timecreate' => $sto
                ]
            ];
        } else if ($search_time_type == 2) {
            $where['AND'][] = [
                '>=' => [
                    'schedule' => $sfrom
                ]
            ];
            $where['AND'][] = [
                '<=' => [
                    'schedule' => $sto
                ]
            ];
        } else if ($search_time_type == 3) {
            // Thống kê theo ghi chú mới nhất
            // Tìm các opportunities có ghi chú trong khoảng thời gian được chọn
            $comment_sql = 'SELECT DISTINCT c.sourceid
                           FROM ' . NV_PREFIXLANG . '_' . $module_data . '_comment c
                           WHERE c.source = 2
                           AND c.timecreate >= ' . $sfrom . '
                           AND c.timecreate <= ' . $sto;

            // Thêm điều kiện lọc theo người chăm sóc nếu có
            if (defined('NV_IS_SPADMIN')) {
                if ($caregiver_id > 0) {
                    $comment_sql .= ' AND c.post_id = ' . $caregiver_id;
                } else if ($caregiver_id == '-2') {
                    $comment_sql .= ' AND c.post_id = 0';
                }
            } else {
                if ($caregiver_id > 0 and isset($caregiver_id_leads[$caregiver_id])) {
                    $comment_sql .= ' AND c.post_id = ' . $caregiver_id;
                } else if (!empty($caregiver_id_leads)) {
                    $comment_sql .= ' AND c.post_id IN (' . implode(',', $caregiver_id_leads) . ')';
                }
            }

            $comment_result = $db->query($comment_sql);
            $opportunity_ids = [];
            while ($row = $comment_result->fetch()) {
                $opportunity_ids[] = $row['sourceid'];
            }

            if (!empty($opportunity_ids)) {
                $where['AND'][] = [
                    'IN' => [
                        'id' => '(' . implode(',', $opportunity_ids) . ')'
                    ]
                ];
            } else {
                // Nếu không có ghi chú nào trong khoảng thời gian, trả về kết quả rỗng
                $where['AND'][] = [
                    '=' => [
                        'id' => 0
                    ]
                ];
            }
        } else {
            $where['AND'][] = [
                '>=' => [
                    'updatetime' => $sfrom
                ]
            ];
            $where['AND'][] = [
                '<=' => [
                    'updatetime' => $sto
                ]
            ];
        }
    }
    if ($status != -1) {
        $where['AND'][] = [
            '=' => [
                'status' => $status
            ]
        ];
    }

    if ($label_search > 0) {
        $where['AND'][] = [
            'FIND_IN_SET' => [
                'label' => $label_search
            ]
        ];
    }

    // Chỉ áp dụng bộ lọc caregiver_id nếu không phải thống kê theo ghi chú
    // Vì khi search_time_type = 3, đã lọc theo người chăm sóc trong query comment rồi
    if ($search_time_type != 3) {
        if (defined('NV_IS_SPADMIN')) { // điều hành chung dc xem toàn bộ, hoặc xem theo bộ lọc người chăm sóc
            if ($caregiver_id > 0) {
                $where['AND'][] = [
                    '=' => [
                        'caregiver_id' => $caregiver_id
                    ]
                ];
            } else if ($caregiver_id == '-2') {
                $where['AND'][] = [
                    '=' => [
                        'caregiver_id' => 0
                    ]
                ];
            }
        } else { // sale được cấp quản trị
            if ($caregiver_id > 0 and isset($caregiver_id_leads[$caregiver_id])) { // sale xem leads của các thành viên nhóm mình
                $where['AND'][] = [
                    '=' => [
                        'caregiver_id' => $caregiver_id
                    ]
                ];
            } else { // mặc định được xem theo các leads mới, và xem theo nhóm sale
                if (isset($admin_config['view_leads_new']) and $admin_config['view_leads_new'] == 1) {
                    $caregiver_id_leads[] = 0;
                }

                // Nếu bật cấu hình xem nguồn leads messages thì cho phép xem
                if ($view_lead_messages) {
                    if ($array_search['source_leads'] == -1 and $group_leads == -1) {
                        $where['AND_OR'][] = [
                            '=' => [
                                'source_leads' => 4
                            ]
                        ];
                    }
                    $where['AND_OR'][] = [
                        'IN' => [
                            'caregiver_id' => '(' . implode(',', $caregiver_id_leads) . ')'
                        ]
                    ];
                } else {
                    $where['AND'][] = [
                        'IN' => [
                            'caregiver_id' => '(' . implode(',', $caregiver_id_leads) . ')'
                        ]
                    ];
                }
            }
        }
    }

    if ($nv_Request->isset_request('view_leads', 'post,get')) {
        $where = [];
        $group_leads = $nv_Request->get_int('group_leads', 'post,get', -1);
        $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_leads WHERE active=1 AND timecreate >= ' . $sfrom . ' AND timecreate <=' . $sto . ' AND source_leads = ' . $group_leads;
        if ($caregiver_id > 0) {
            $sql .= ' AND caregiver_id = ' . $caregiver_id;
        }
        $result = $db->query($sql);
        while ($row = $result->fetch()) {
            $array_leads_all[$row['id']] = $row['id'];
        }

        if (!empty($array_leads_all)) {
            $where['AND'][] = [
                'IN' => [
                    'leadsid' => '(' . implode(',', $array_leads_all) . ')'
                ]
            ];
        }
    }

    if (!empty($where)) {
        $params['where'] = $where;
    }

    if ($orderby_name > 0) {
        $order['name'] = $orderby_name == 1 ? 'ASC' : 'DESC';
    }
    if ($orderby_phone > 0) {
        $order['phone'] = $orderby_phone == 1 ? 'ASC' : 'DESC';
    }
    if ($orderby_email > 0) {
        $order['email'] = $orderby_email == 1 ? 'ASC' : 'DESC';
    }
    if ($orderby_status > 0) {
        $order['status'] = $orderby_status == 1 ? 'ASC' : 'DESC';
    }
    if ($orderby_schedule > 0) {
        $order['schedule'] = $orderby_schedule == 1 ? 'ASC' : 'DESC';
    }
    if ($orderby_time > 0) {
        $order['updatetime'] = $orderby_time == 1 ? 'ASC' : 'DESC';
    }
    if ($orderby_affc > 0) {
        $order['affilacate_id'] = $orderby_affc == 1 ? 'ASC' : 'DESC';
    }
    if ($orderby_care > 0) {
        $order['caregiver_id'] = $orderby_care == 1 ? 'ASC' : 'DESC';
    }
    if ($orderby_siteid > 0) {
        $order['siteid'] = $orderby_siteid == 1 ? 'ASC' : 'DESC';
    }
    if ($orderby_source_leads > 0) {
        $order['source_leads'] = $orderby_source_leads == 1 ? 'ASC' : 'DESC';
    }

    if (!empty($order)) {
        $params['order'] = $order;
    } else {
        $order['activity_time'] = 'DESC';
        $order['updatetime'] = 'DESC';
        $order['status'] = 'ASC';
        $order['schedule'] = 'ASC';
        $params['order'] = $order;
    }
    if ($page > 20) {
        $btn = '<div class="margin-top"><a class="btn btn-primary" onclick="window.history.go(-1); return false;">' . $nv_Lang->getModule('back') . '</a></div>';
        $contents = nv_theme_alert($nv_Lang->getModule('info'), $nv_Lang->getModule('note_max_searchpage') . $btn);
        include NV_ROOTDIR . '/includes/header.php';
        echo nv_admin_theme($contents);
        include NV_ROOTDIR . '/includes/footer.php';
    }
    $data_oppotunitie = nv_local_api('ListAllOpportunities', $params, $admin_info['username'], 'crmbidding');
    $data_oppotunitie = json_decode($data_oppotunitie, true);
    $params = $order = [];
    if (isset($data_oppotunitie['code']) and $data_oppotunitie['code'] == '0000') {
        $per_page = $data_oppotunitie['perpage'];
        $page = $data_oppotunitie['page'];
        $num_items = $data_oppotunitie['total'];
    } else {
        $per_page = 1;
        $page = 1;
        $num_items = 0;
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('ROW', $row);
$xtpl->assign('Q', $q);
$xtpl->assign('page', $page);
$xtpl->assign('LINK_DUPLICATE', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=duplicate&type=2');
$xtpl->assign('LINK_UNDO_MERGE', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=undo_merge&type=2');

$xtpl->assign('NOT_CAREGIVERID', $caregiver_id == '-2' ? 'selected="selected"' : '');

if ($show_view) {
    $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&showheader=' . $showheader;
    if (!empty($q)) {
        $base_url .= '&q=' . $q;
    }
    if (!empty($group_leads)) {
        $base_url .= '&group_leads=' . $group_leads . '&view_leads=1';
    }
    if ($status != -1) {
        $base_url .= '&status=' . $status;
    }
    if (!empty($caregiver_id)) {
        $base_url .= '&caregiver_id=' . $caregiver_id;
    }
    if (!empty($label_search)) {
        $base_url .= '&label=' . $label_search;
    }
    if (!empty($array_search['source_leads'])) {
        $base_url .= '&source_leads=' . $array_search['source_leads'];
    }
    if ($array_search['prefix_lang'] != -1) {
        $base_url .= '&prefix_lang=' . $array_search['prefix_lang'];
    }
    $show_advance_search = 'hidden';
    if ($sfrom > 0 && $sto > 0) {
        $show_advance_search = '';
        $xtpl->assign('TIME_FROM', $array_search['time_from']);
        $xtpl->assign('TIME_TO', $array_search['time_to']);
        $base_url .= '&time_from=' . $array_search['time_from'] . '&time_to=' . $array_search['time_to'] . '&search_time_type=' . $search_time_type;
    }
    if ($array_search['care_time'] > 0) {
        $show_advance_search = '';
        $base_url .= '&care_time=' . $array_search['care_time'];
    }

    $xtpl->assign('time_update_selected', $search_time_type == 0 ? 'selected' : '');
    $xtpl->assign('time_create_selected', $search_time_type == 1 ? 'selected' : '');
    $xtpl->assign('shedule_selected', $search_time_type == 2 ? 'selected' : '');
    $xtpl->assign('comment_selected', $search_time_type == 3 ? 'selected' : '');

    $xtpl->assign('ADVANDE_HIDDEN', $show_advance_search);
    if ($show_advance_search == '') {
        $xtpl->assign('HIDDEN_BTN', 'hidden');
    } else {
        $xtpl->assign('HIDDEN_BTN', '');
    }

    $link_orderby_name = $base_url . '&orderby_name=1';
    $link_orderby_phone = $base_url . '&orderby_phone=1';
    $link_orderby_email = $base_url . '&orderby_email=1';
    $link_orderby_status = $base_url . '&orderby_status=1';
    $link_orderby_schedule = $base_url . '&orderby_schedule=1';
    $link_orderby_time = $base_url . '&orderby_time=1';
    $link_orderby_affc = $base_url . '&orderby_affc=1';
    $link_orderby_care = $base_url . '&orderby_care=1';
    $link_orderby_siteid = $base_url . '&orderby_siteid=1';
    $link_orderby_source_leads = $base_url . '&orderby_source_leads=1';

    if ($orderby_name > 0) {
        if ($orderby_name == 1) {
            $link_orderby_name_desc = $base_url . '&orderby_name=2' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_NAME_DESC', $link_orderby_name_desc);
            $xtpl->assign('ORDER_BY_NAME', $link_orderby_name_desc);
            $xtpl->parse('main.name.desc');
        } else {
            $link_orderby_name_asc = $base_url . '&orderby_name=1' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_NAME_ASC', $link_orderby_name_asc);
            $xtpl->assign('ORDER_BY_NAME', $link_orderby_name_asc);
            $xtpl->parse('main.name.asc');
        }
        $xtpl->parse('main.name');
        $base_url .= '&orderby_name=' . $orderby_name;
    } else {
        $xtpl->assign('ORDER_BY_NAME', $link_orderby_name . '&page=' . $page);
    }

    if ($orderby_phone > 0) {
        if ($orderby_phone == 1) {
            $link_orderby_phone_desc = $base_url . '&orderby_phone=2' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_PHONE_DESC', $link_orderby_phone_desc);
            $xtpl->assign('ORDER_BY_PHONE', $link_orderby_phone_desc);
            $xtpl->parse('main.phone.desc');
        } else {
            $link_orderby_phone_asc = $base_url . '&orderby_phone=1' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_PHONE_ASC', $link_orderby_phone_asc);
            $xtpl->assign('ORDER_BY_PHONE', $link_orderby_phone_asc);
            $xtpl->parse('main.phone.asc');
        }
        $xtpl->parse('main.phone');
        $base_url .= '&orderby_phone=' . $orderby_phone;
    } else {
        $xtpl->assign('ORDER_BY_PHONE', $link_orderby_phone . '&page=' . $page);
    }

    if ($orderby_email > 0) {
        if ($orderby_email == 1) {
            $link_orderby_email_desc = $base_url . '&orderby_email=2' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_EMAIL_DESC', $link_orderby_email_desc);
            $xtpl->assign('ORDER_BY_EMAIL', $link_orderby_email_desc);
            $xtpl->parse('main.email.desc');
        } else {
            $link_orderby_email_asc = $base_url . '&orderby_email=1' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_EMAIL_ASC', $link_orderby_email_asc);
            $xtpl->assign('ORDER_BY_EMAIL', $link_orderby_email_asc);
            $xtpl->parse('main.email.asc');
        }
        $xtpl->parse('main.email');
        $base_url .= '&orderby_email=' . $orderby_email;
    } else {
        $xtpl->assign('ORDER_BY_EMAIL', $link_orderby_email . '&page=' . $page);
    }

    // $orderby_status
    if ($orderby_status == 1) {
        $link_orderby_status_desc = $base_url . '&orderby_status=2' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_STATUS_DESC', $link_orderby_status_desc);
        $xtpl->assign('ORDER_BY_STATUS', $link_orderby_status_desc);
        $xtpl->parse('main.status.desc');
    } else {
        $link_orderby_status_asc = $base_url . '&orderby_status=1' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_STATUS_ASC', $link_orderby_status_asc);
        $xtpl->assign('ORDER_BY_STATUS', $link_orderby_status_asc);
        $xtpl->parse('main.status.asc');
    }
    $xtpl->parse('main.status');
    $base_url .= '&orderby_status=' . $orderby_status;

    // orderby_schedule
    if ($orderby_schedule == 1) {
        $link_orderby_schedule_desc = $base_url . '&orderby_schedule=2' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_SCHEDULE_DESC', $link_orderby_schedule_desc);
        $xtpl->assign('ORDER_BY_SCHEDULE', $link_orderby_schedule_desc);
        $xtpl->parse('main.schedule.desc');
    } else {
        $link_orderby_schedule_asc = $base_url . '&orderby_schedule=1' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_SCHEDULE_ASC', $link_orderby_schedule_asc);
        $xtpl->assign('ORDER_BY_SCHEDULE', $link_orderby_schedule_asc);
        $xtpl->parse('main.schedule.asc');
    }
    $xtpl->parse('main.schedule');
    $base_url .= '&orderby_schedule=' . $orderby_schedule;

    if ($orderby_time == 1 or $orderby_time == 0) {
        $link_orderby_time_desc = $base_url . '&orderby_time=2' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_TIME_DESC', $link_orderby_time_desc);
        $xtpl->assign('ORDER_BY_TIME', $link_orderby_time_desc);
        $xtpl->parse('main.updatetime.desc');
    } else {
        $link_orderby_time_asc = $base_url . '&orderby_time=1' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_TIME_ASC', $link_orderby_time_asc);
        $xtpl->assign('ORDER_BY_TIME', $link_orderby_time_asc);
        $xtpl->parse('main.updatetime.asc');
    }
    $xtpl->parse('main.updatetime');
    $base_url .= '&orderby_time=' . $orderby_time;

    if ($orderby_affc > 0) {
        if ($orderby_affc == 1) {
            $link_orderby_affc_desc = $base_url . '&orderby_affc=2' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_AFFC_DESC', $link_orderby_affc_desc);
            $xtpl->assign('ORDER_BY_AFFC', $link_orderby_affc_desc);
            $xtpl->parse('main.affilacate.desc');
        } else {
            $link_orderby_affc_asc = $base_url . '&orderby_affc=1' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_AFFC_ASC', $link_orderby_affc_asc);
            $xtpl->assign('ORDER_BY_AFFC', $link_orderby_affc_asc);
            $xtpl->parse('main.affilacate.asc');
        }
        $xtpl->parse('main.affilacate');
        $base_url .= '&orderby_affc=' . $orderby_affc;
    } else {
        $xtpl->assign('ORDER_BY_AFFC', $link_orderby_affc . '&page=' . $page);
    }
    if ($orderby_care > 0) {
        if ($orderby_care == 1) {
            $link_orderby_care_desc = $base_url . '&orderby_care=2' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_CARE_DESC', $link_orderby_care_desc);
            $xtpl->assign('ORDER_BY_CARE', $link_orderby_care_desc);
            $xtpl->parse('main.care.desc');
        } else {
            $link_orderby_care_asc = $base_url . '&orderby_care=1' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_CARE_ASC', $link_orderby_care_asc);
            $xtpl->assign('ORDER_BY_CARE', $link_orderby_care_asc);
            $xtpl->parse('main.care.asc');
        }
        $xtpl->parse('main.care');
        $base_url .= '&orderby_care=' . $orderby_care;
    } else {
        $xtpl->assign('ORDER_BY_CARE', $link_orderby_care . '&page=' . $page);
    }

    if ($orderby_siteid > 0) {
        if ($orderby_siteid == 1) {
            $link_orderby_siteid_desc = $base_url . '&orderby_siteid=2' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_SITEID_DESC', $link_orderby_siteid_desc);
            $xtpl->assign('ORDER_BY_SITEID', $link_orderby_siteid_desc);
            $xtpl->parse('main.siteid.desc');
        } else {
            $link_orderby_siteid_asc = $base_url . '&orderby_siteid=1' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_SITEID_ASC', $link_orderby_siteid_asc);
            $xtpl->assign('ORDER_BY_SITEID', $link_orderby_siteid_asc);
            $xtpl->parse('main.siteid.asc');
        }
        $xtpl->parse('main.siteid');
        $base_url .= '&orderby_siteid=' . $orderby_siteid;
    } else {
        $xtpl->assign('ORDER_BY_SITEID', $link_orderby_siteid . '&page=' . $page);
    }

    if ($orderby_source_leads > 0) {
        if ($orderby_source_leads == 1) {
            $link_orderby_source_leads_desc = $base_url . '&orderby_source_leads=2' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_SOURCE_LEADS_DESC', $link_orderby_source_leads_desc);
            $xtpl->assign('ORDER_BY_SOURCE_LEADS', $link_orderby_source_leads_desc);
            $xtpl->parse('main.source_leads.desc');
        } else {
            $link_orderby_source_leads_asc = $base_url . '&orderby_source_leads=1' . '&page=' . $page;
            $xtpl->assign('ORDER_BY_SOURCE_LEADS_ASC', $link_orderby_source_leads_asc);
            $xtpl->assign('ORDER_BY_SOURCE_LEADS', $link_orderby_source_leads_asc);
            $xtpl->parse('main.source_leads.asc');
        }
        $xtpl->parse('main.source_leads');
        $base_url .= '&orderby_source_leads=' . $orderby_source_leads;
    } else {
        $xtpl->assign('ORDER_BY_SOURCE_LEADS', $link_orderby_source_leads . '&page=' . $page);
    }

    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
    }
    $number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;
    if (!empty($data_oppotunitie['data'])) {
        foreach ($data_oppotunitie['data'] as $key => $view) {
            $view['number'] = $number++;
            $view['status'] = $array_status_opportunities[$view['status']];
            if (!empty($view['label_arr'])) {
                foreach ($view['label_arr'] as $label) {
                    $xtpl->assign('LABEL', $label);
                    $xtpl->parse('main.loop.label');
                }
            }
            $view['timecreate'] = $view['timecreate'] != 0 ? nv_date('H:i d/m/Y', $view['timecreate']) : '';
            $view['updatetime'] = $view['updatetime'] != 0 ? nv_date('H:i d/m/Y', $view['updatetime']) : '';
            $view['activity_time'] = (!empty($view['activity_time']) && $view['activity_time'] != 0) ? nv_date('H:i d/m/Y', $view['activity_time']) : '';

            if ($view['schedule'] != 0) {
                $xtpl->assign('SCHEDULE', nv_date('d/m/Y', $view['schedule']));
                $xtpl->parse('main.loop.schedule');
            }
            $view['prefix_lang_letter'] = (isset($view['prefix_lang']) and $view['prefix_lang'] == 1) ? '(' . $nv_Lang->getModule('lang_en') . ')' : '(' . $nv_Lang->getModule('lang_vi') . ')';

            if ($view['affilacate_id'] > 0) {
                $affilacate_user = get_user($view['affilacate_id']);
                $view['affilacate_id'] = isset($affilacate_user['username']) ? nv_show_name_user($affilacate_user['first_name'], $affilacate_user['last_name'], $affilacate_user['username']) : '';
            } else {
                $view['affilacate_id'] = '';
            }

            $view['caregiver_id'] = isset($array_user_id_users[$view['caregiver_id']]['username']) ? nv_show_name_user($array_user_id_users[$view['caregiver_id']]['first_name'], $array_user_id_users[$view['caregiver_id']]['last_name'], $array_user_id_users[$view['caregiver_id']]['username']) : '';
            $view['siteid'] = isset($array_site[$view['siteid']]) ? $array_site[$view['siteid']] : '';

            $view['link_edit'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $view['id'];
            $view['link_view'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=opportunities_info&amp;id=' . $view['id'] . '&showheader=' . $showheader;
            $view['link_delete'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;delete_id=' . $view['id'] . '&amp;delete_checkss=' . md5($view['id'] . NV_CACHE_PREFIX . $client_info['session_id']);
            $view['source_leads_title'] = !empty($view['source_leads']) ? $array_groups_leads[$view['source_leads']]['title'] : '';
            $xtpl->assign('VIEW', $view);
            $xtpl->parse('main.loop');
        }
    } else {
        $xtpl->parse('main.empty');
    }
}

if (defined('NV_IS_SPADMIN')) {
    foreach ($array_user_id_users as $value) {
        $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
        $value['selected'] = $value['userid'] == $caregiver_id ? 'selected="selected"' : '';
        $value['title'] = $value['username'] . ' (' . $fullname . ')';
        $xtpl->assign('CAREGIVER_ID', $value);
        $xtpl->parse('main.search.caregiver_id');
    }
} else {
    foreach ($array_user_id_users as $value) {
        $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
        $value['selected'] = $value['userid'] == $caregiver_id ? 'selected="selected"' : '';
        $value['title'] = $value['username'] . ' (' . $fullname . ')';
        $xtpl->assign('CAREGIVER_ID', $value);
        if (isset($caregiver_id_leads[$value['userid']])) {
            $xtpl->parse('main.search.caregiver_id');
        }
    }
}

foreach ($array_label as $value) {
    $value['selected'] = $value['id'] == $label_search ? 'selected="selected"' : '';
    $xtpl->assign('LABEL', $value);
    $xtpl->parse('main.search.label');
}

unset($array_status_opportunities[0]);
foreach ($array_status_opportunities as $key => $value) {
    $xtpl->assign('STATUS', array(
        'id' => $key,
        'title' => $value,
        'selected' => $key == $status ? 'selected="selected"' : ''
    ));
    $xtpl->parse('main.search.status');
}
foreach ($array_care_time as $key => $value) {
    $xtpl->assign('CARE_TIME', array(
        'id' => $key,
        'title' => $value,
        'selected' => $key == $array_search['care_time'] ? 'selected="selected"' : ''
    ));
    $xtpl->parse('main.search.care_time');
}
foreach ($array_site as $key => $value) {
    $xtpl->assign('OPTION', [
        'key' => $key,
        'title' => $value,
        'selected' => ($key == $array_search['siteid']) ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.search.select_siteid');
}
foreach ($array_lang as $key => $value) {
    $xtpl->assign('OPTION', [
        'key' => $key,
        'title' => $value,
        'selected' => ($key == $array_search['prefix_lang']) ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.search.select_prefix_lang');
}
foreach ($array_groups_leads as $key => $value) {
    $xtpl->assign('OPTION', [
        'key' => $key,
        'title' => $value['title'],
        'selected' => ($key == $array_search['source_leads']) ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.search.select_source_leads');
}

if ($showheader) {
    $xtpl->parse('main.search');
}

if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('opportunities');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';
