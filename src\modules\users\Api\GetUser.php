<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\users\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use PDO;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetUser implements IApi
{
    private $result;

    /**
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_GOD;
    }

    /**
     * @return string
     */
    public static function getCat()
    {
        return 'user';
    }

    /**
     * {@inheritDoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     * {@inheritDoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $nv_Request, $db, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        $admin_id = Api::getAdminId();

        $userid = $nv_Request->get_absint('userid', 'post', 0);
        if (empty($userid)) {
            return $this->result->setCode('5101')->setMessage('No userid')->getResult();
        }

        // Lấy thông tin cơ bản
        $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . " WHERE userid=:userid";
        $stmt = $db->prepare($sql);
        $stmt->bindParam(':userid', $userid, PDO::PARAM_INT);
        $stmt->execute();
        $array_user = $stmt->fetch();

        if (empty($array_user)) {
            return $this->result->setCode('5102')->setMessage($nv_Lang->getModule('api_user_not_exists'))->getResult();
        }

        // Lấy tùy biến dữ liệu
        $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_info WHERE userid=:userid";
        $stmt = $db->prepare($sql);
        $stmt->bindParam(':userid', $userid, PDO::PARAM_INT);
        $stmt->execute();
        $array_user_info = $stmt->fetch();

        $userinfo = [
            'username' => $array_user['username'],
            'email' => $array_user['email'],
            'first_name' => $array_user['first_name'],
            'last_name' => $array_user['last_name'],
            'gender' => $array_user['gender'],
            'last_update' => $array_user['last_update']
        ];
        if (!empty($array_user_info)) {
            unset($array_user_info['userid']);
            $userinfo = array_merge($userinfo, $array_user_info);
        }

        $this->result->set('userinfo', $userinfo);

        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
