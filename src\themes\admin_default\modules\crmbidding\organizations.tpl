<!-- BEGIN: main -->
    <!-- BEGIN: view_orgainzation -->
        <div class="well">
            <form action="{NV_BASE_ADMINURL}index.php" method="get">
                <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" />
                <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" />
                <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
                <div class="row">
                    <div class="col-xs-24 col-md-6">
                        <div class="form-group">
                            <input class="form-control" type="text" value="{Q}" name="q" maxlength="255" placeholder="{LANG.search_title}" />
                        </div>
                    </div>
                    <div class="col-xs-12 col-md-3">
                        <div class="form-group">
                            <button class="btn btn-primary btn--social" name="search" type="submit" value="{LANG.search_submit}"><i class="fa fa-search" aria-hidden="true"></i> {LANG.search_submit}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post" class="list_tochuc">
            <div class="box__title"> 
                <h2 class="title__page title__page_organization">{LANG.title_list_orag}</h2>
                {NUMBER_RESULT}
                <a href="{URL_ADD}" id="add_tochuc" class="btn btn-success btn--social">{LANG.add_orag}</a>
            </div>
            <div class="table-responsive">
                <table class="table table-striped table-bordered table-hover">
                    <thead>
                        <tr class="tr__search">
                            <th class="w100 text-center"> 
                                <div><span>{LANG.number}</span></div> 
                            </th>
                            <th class="filter_organizationname">
                                <div>
                                    <a href="{URL_ORDER.URL_NAME}">
                                        {LANG.organizationname}
                                        <i class="fa fa-sort-alpha-asc" aria-hidden="true"></i>
                                        <i class="fa fa-sort-alpha-desc" aria-hidden="true"></i>
                                    </a>
                                </div>
                            </th>
                            <th class="filter_primaryphone">
                                <div>
                                    <a href="{URL_ORDER.URL_PHONE}">
                                        {LANG.primaryphone}
                                        <i class="fa fa-sort-alpha-asc" aria-hidden="true"></i>
                                        <i class="fa fa-sort-alpha-desc" aria-hidden="true"></i>
                                    </a>
                                </div>
                            </th>
                            <th class="filter_primaryemail">
                                <div>
                                    <a href="{URL_ORDER.URL_EMAIL}">
                                        {LANG.primaryemail}
                                        <i class="fa fa-sort-alpha-asc" aria-hidden="true"></i>
                                        <i class="fa fa-sort-alpha-desc" aria-hidden="true"></i>
                                    </a>
                                </div>
                            </th>
                            <th class="w100 text-center filter_active">
                                <div>
                                    <a href="{URL_ORDER.URL_STATUS}">
                                        {LANG.trangthai}
                                        <i class="fa fa-sort-alpha-asc" aria-hidden="true"></i>
                                        <i class="fa fa-sort-alpha-desc" aria-hidden="true"></i>
                                    </a>
                                </div>
                            </th>
                            <th class="w150 text-center">
                                <div><span>{LANG.action}</span><span></div>
                            </th>
                        </tr>
                    </thead>
                    <!-- BEGIN: generate_page -->
                    <tfoot>
                        <tr>
                            <td class="text-center" colspan="6">{NV_GENERATE_PAGE}</td>
                        </tr>
                    </tfoot>
                    <!-- END: generate_page -->
                    <tbody>
                        <!-- BEGIN: loop -->
                        <tr>
                            <td class="text-center"> {VIEW.stt} </td>
                            <td> <div class="wrap__text"><a href="{VIEW.link_view}">{VIEW.organizationname}</a></div></td>
                            <td> {VIEW.primaryphone} </td>
                            <td> {VIEW.primaryemail} </td>
                            <td class="text-center">
                                <label class="checkbox__active">
                                    <input type="checkbox" id="change_status_{VIEW.id}" onclick="check_active({VIEW.id});" {VIEW.active}>&nbsp;
                                    <span class="label {VIEW.class}">{VIEW.textActive}</span>
                                </label>
                            </td>
                            <td class="text-center">
                                <div class="box__action">
                                    <a href="{VIEW.link}" class="btn btn-primary btn-sm btn--social"><i class="fa fa-edit fa-lg">&nbsp;</i> {LANG.edit}</a>
                                    <button type="submit" class="btn btn-danger btn-sm btn--social" onclick="return confirm(nv_is_del_confirm[0]);" value="{VIEW.id}" name="deleteOrganzation"><i class="fa fa-trash-o fa-lg">&nbsp;</i> {LANG.delete}</button>
                                </div>
                        </tr>
                        <!-- END: loop -->
                    </tbody>
                </table>
            </div>
        </form>
    <!-- BEGIN: nodata -->
        <div class="alert alert-warning">{NODATA}</div>
    <!-- END: nodata -->

    <!-- END: view_orgainzation -->

    <!-- BEGIN: success -->
        <div class="alert alert-success">{SUCCESS}</div>
        <script type="text/javascript">
           $(document).ready(function($) {
                $(".alert-success").fadeOut(3000);
                setTimeout(function() {
                    location.href = location.href;
                }, 1000);
           });

        </script>
    <!-- END: success -->

    <!-- BEGIN: error -->
        <div class="alert alert-warning">{ERROR}</div>
    <!-- END: error --> 
    <div id="toast"></div>
    <script type="text/javascript">
        $("[class*='filter_']").find(".fa-sort-alpha-desc").hide();
        // Thay đổi hoạt động
        function check_active(id) {
            if (confirm(nv_is_change_act_confirm[0])) {
                var nv_timer = nv_settimeout_disable('change_status_' + id, 2000);
                $.post(script_name + '?' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=organizations&nocache=' + new Date().getTime(), 'change_status=1&id='+id, function(res) {
                    var r_split = res.split('_');
                    if (r_split[0] != 'OK') {
                        alert(nv_is_change_act_confirm[2]);
                    } else {
                        location.reload();
                    }
                });
            }
            else{
                $('#change_status_' + id).prop('checked', new_status ? false : true);
            }
            return;
        }

        filter = "{FILTER}";
        order = "{ORDER_SORT}";
        if (order == 1) {
            $(".filter_" + filter).find(".fa-sort-alpha-asc").show();
            $(".filter_" + filter).find(".fa-sort-alpha-desc").hide();
        } else {
            $(".filter_" + filter).find(".fa-sort-alpha-asc").hide();
            $(".filter_" + filter).find(".fa-sort-alpha-desc").show();
        }
    </script>
<!-- END: main -->