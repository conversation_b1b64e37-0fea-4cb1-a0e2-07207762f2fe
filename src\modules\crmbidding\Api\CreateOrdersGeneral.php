<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CreateOrdersGeneral implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'ordersgeneral';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $array_groups_users, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $error = '';
        $error_code = '0000';
        $admin_id = $nv_Request->get_int('adminid', 'post', '0');
        $row['siteid'] = $nv_Request->get_int('siteid', 'post', 0);
        $row['order_id'] = $nv_Request->get_int('order_id', 'post', 0);
        $row['vip'] = $nv_Request->get_title('vip', 'post', '');
        $row['type_export'] = $nv_Request->get_int('type_export', 'post', 0);
        $row['userid'] = $nv_Request->get_int('userid', 'post', 0);
        $row['profileid'] = $nv_Request->get_int('profileid', 'post', 0);
        $row['affiliate_userid'] = $nv_Request->get_int('affiliate_userid', 'post', 0);
        $row['official_collaborator'] = $nv_Request->get_int('official_collaborator', 'post', 0);
        $row['promo_userid'] = $nv_Request->get_int('promo_userid', 'post', 0);
        $row['admin_id'] = $nv_Request->get_int('admin_id', 'post', 0);
        $row['caregiver_id'] = $nv_Request->get_int('caregiver_id', 'post', 0);
        $row['money'] = $nv_Request->get_int('money', 'post', 0);
        $row['discount'] = $nv_Request->get_int('discount', 'post', 0);
        $row['total'] = $nv_Request->get_int('total', 'post', 0);
        $row['price_reduce'] = $nv_Request->get_int('price_reduce', 'post', 0);
        $row['total_end'] = $nv_Request->get_int('total_end', 'post', 0);
        $row['price_reduce'] = $nv_Request->get_int('price_reduce', 'post', 0);
        $row['discount_excess'] = $nv_Request->get_int('discount_excess', 'post', 0);

        $row['status'] = $nv_Request->get_int('status', 'post', 0);
        $row['is_expired'] = $nv_Request->get_int('is_expired', 'post', 0);
        $row['add_time'] = $nv_Request->get_int('add_time', 'post', 0);
        $row['static_time'] = $nv_Request->get_int('static_time', 'post', 0);
        $row['is_renewal'] = $nv_Request->get_int('is_renewal', 'post', 0);
        $row['source_leads'] = $nv_Request->get_int('source_leads', 'post', 0);
        $row['invoice_number'] = $nv_Request->get_int('invoice_number', 'post', 0);
        $row['taxes_fees'] = $nv_Request->get_int('taxes_fees', 'post', 0);
        $row['add_adminid'] = $nv_Request->get_int('add_adminid', 'post', 0);
        $row['source_money'] = $nv_Request->get_int('source_money', 'post', 0);
        $row['business_id'] = $nv_Request->get_int('business_id', 'post', 0);
        $row['noi_dung'] = $nv_Request->get_title('noi_dung', 'post', '');
        $row['transaction_id'] = $nv_Request->get_absint('transaction_id', 'post', 0);

        if ($row['siteid'] == 0) {
            $error_code = '2003';
            $error = $nv_Lang->getModule('siteid_not_defined');
        } else if ($row['userid'] == 0 and $row['profileid'] == 0) {
            $error_code = '2004';
            $error = $nv_Lang->getModule('error_required_userid');
        } elseif ($row['order_id'] == 0) {
            $error_code = '2005';
            $error = $nv_Lang->getModule('error_required_orderid');
        }

        if ($row['siteid'] == 1 and $row['vip'] == '') {
            $error_code = '2006';
            $error = $nv_Lang->getModule('error_required_vip');
        }

        // tính hoa hồng
        $affilicate_value = 0;
        $introduce_value = 0;
        $successful_value = 0;
        $caregiver_value = 0;

        if ($row['affiliate_userid'] > 0 and empty($array_groups_users[$row['affiliate_userid']]) and $row['is_renewal'] == 0) {
            // cộng tác viên bên ngoài, chính thức 30%, k chính thức 10%.
            // chỉ tính hưởng 1 lần cho đơn hàng đầu tiên, các đơn gia hạn tiếp theo tính cho sale
            if ($row['official_collaborator'] == 1) {
                $affilicate_value = $row['total'] * 30 / 100;
            } else {
                $affilicate_value = $row['total'] * 10 / 100;
            }

            // CTV chỉ dc nhận hoa hồng theo khoản chênh với giảm giá
            $_affilicate_value = $affilicate_value - $row['discount'];
            $affilicate_value = $_affilicate_value < 0 ? 0 : $_affilicate_value;

            // đơn hàng CTV thì hoa hồng giới thiệu, chốt đơn = 0, chăm sóc = %/số tiền tổng - trả cho CTV
            $introduce_value = 0;
            $successful_value = 0;

            // fix riêng đơn hàng 221974 dauthau.info do CTV lách luật, tiến đã duyệt
            if ($row['order_id'] == 221974) {
                $percent_order = 4;
                // giá trị mặc định
                // tính % chiết khấu theo cấu hình thời gian
                if (isset($array_groups_users[$row['admin_id']])) {
                    foreach ($array_groups_users[$row['admin_id']]['config_percent'] as $config_percent) {
                        if ($row['static_time'] >= $config_percent['date_from'] and $row['static_time'] <= $config_percent['date_to']) {
                            $percent_order = $config_percent['percent_order'];
                            break;
                        }
                    }
                }
                $successful_value = ($row['total_end'] - $affilicate_value) * $percent_order / 100;
            }

            $percent_suport = 2.5;
            if (isset($array_groups_users[$row['caregiver_id']])) {
                foreach ($array_groups_users[$row['caregiver_id']]['config_percent'] as $config_percent) {
                    if ($row['static_time'] >= $config_percent['date_from'] and $row['static_time'] <= $config_percent['date_to']) {
                        $percent_suport = $config_percent['percent_suport'];
                        break;
                    }
                }
            }
            // 12.000.000
            // $affilicate_value : 3.600.000
            // $caregiver_value: 12.000.000 - 3.600.000 = 8400000 /2.5%: 210.000
            $caregiver_value = ($row['total_end'] - $affilicate_value) * $percent_suport / 100;
        } else {
            if ($row['is_renewal'] == 1) {
                $percent_referral = 0;
            } else {
                $percent_referral = 3.5;
                if (isset($array_groups_users[$row['affiliate_userid']])) {
                    foreach ($array_groups_users[$row['affiliate_userid']]['config_percent'] as $config_percent) {
                        if ($row['static_time'] >= $config_percent['date_from'] and $row['static_time'] <= $config_percent['date_to']) {
                            $percent_referral = $config_percent['percent_referral'];
                            break;
                        }
                    }
                }
                $introduce_value = $row['total_end'] * $percent_referral / 100;
            }

            $percent_order = 4;
            // giá trị mặc định
            // tính % chiết khấu theo cấu hình thời gian
            if (isset($array_groups_users[$row['admin_id']])) {
                foreach ($array_groups_users[$row['admin_id']]['config_percent'] as $config_percent) {
                    if ($row['static_time'] >= $config_percent['date_from'] and $row['static_time'] <= $config_percent['date_to']) {
                        $percent_order = $config_percent['percent_order'];
                        break;
                    }
                }
            }
            $percent_suport = 2.5;
            if (isset($array_groups_users[$row['caregiver_id']])) {
                foreach ($array_groups_users[$row['caregiver_id']]['config_percent'] as $config_percent) {
                    if ($row['static_time'] >= $config_percent['date_from'] and $row['static_time'] <= $config_percent['date_to']) {
                        $percent_suport = $config_percent['percent_suport'];
                        break;
                    }
                }
            }

            $successful_value = $row['total_end'] * $percent_order / 100;
            $caregiver_value = $row['total_end'] * $percent_suport / 100;
        }

        $_query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE siteid=' . $db->quote($row['siteid']) . ' AND order_id = ' . $db->quote($row['order_id']) . ' AND vip =' . $db->quote($row['vip']) . ' AND type_export =' . $db->quote($row['type_export']) . ' AND userid =' . $db->quote($row['userid']) . ' AND profileid =' . $db->quote($row['profileid']));
        while ($_row = $_query->fetch()) {
            if (!empty($_row)) {
                $db->query('DELETE FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE id=' . $_row['id']);
            }
        }

        if (empty($error)) {
            try {
                $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_bidding_orders_general (
                    siteid, order_id, vip, type_export, userid, profileid, affiliate_userid,
                    official_collaborator, promo_userid, admin_id, caregiver_id, money,
                    discount, total, price_reduce, total_end, affilicate_value, introduce_value,
                    successful_value, caregiver_value, discount_excess, status, is_expired,
                    add_time, static_time, is_renewal, source_leads, invoice_number,
                    taxes_fees, add_adminid, source_money, noi_dung, business_id, transaction_id
                ) VALUES (
                    :siteid, :order_id, :vip, :type_export, :userid, :profileid,
                    :affiliate_userid, :official_collaborator, :promo_userid,
                    :admin_id, :caregiver_id, :money, :discount, :total, :price_reduce,
                    :total_end, :affilicate_value, :introduce_value, :successful_value,
                    :caregiver_value, :discount_excess, :status, :is_expired,
                    :add_time, :static_time, :is_renewal, :source_leads, :invoice_number,
                    :taxes_fees, :add_adminid, :source_money, :noi_dung, :business_id, :transaction_id
                )');
                $stmt->bindParam(':siteid', $row['siteid'], PDO::PARAM_STR);
                $stmt->bindParam(':order_id', $row['order_id'], PDO::PARAM_STR);
                $stmt->bindParam(':vip', $row['vip'], PDO::PARAM_STR);
                $stmt->bindParam(':type_export', $row['type_export'], PDO::PARAM_STR);
                $stmt->bindParam(':userid', $row['userid'], PDO::PARAM_STR);
                $stmt->bindParam(':profileid', $row['profileid'], PDO::PARAM_STR);
                $stmt->bindParam(':affiliate_userid', $row['affiliate_userid'], PDO::PARAM_STR);
                $stmt->bindParam(':official_collaborator', $row['official_collaborator'], PDO::PARAM_STR);
                $stmt->bindParam(':promo_userid', $row['promo_userid'], PDO::PARAM_STR);
                $stmt->bindParam(':admin_id', $row['admin_id'], PDO::PARAM_STR);
                $stmt->bindParam(':caregiver_id', $row['caregiver_id'], PDO::PARAM_STR);
                $stmt->bindParam(':money', $row['money'], PDO::PARAM_STR);
                $stmt->bindParam(':discount', $row['discount'], PDO::PARAM_STR);
                $stmt->bindParam(':total', $row['total'], PDO::PARAM_STR);
                $stmt->bindParam(':price_reduce', $row['price_reduce'], PDO::PARAM_STR);
                $stmt->bindParam(':total_end', $row['total_end'], PDO::PARAM_STR);

                $stmt->bindParam(':affilicate_value', $affilicate_value, PDO::PARAM_STR);
                $stmt->bindParam(':introduce_value', $introduce_value, PDO::PARAM_STR);
                $stmt->bindParam(':successful_value', $successful_value, PDO::PARAM_STR);
                $stmt->bindParam(':caregiver_value', $caregiver_value, PDO::PARAM_STR);

                $stmt->bindParam(':discount_excess', $row['discount_excess'], PDO::PARAM_STR);

                $stmt->bindParam(':status', $row['status'], PDO::PARAM_STR);
                $stmt->bindParam(':is_expired', $row['is_expired'], PDO::PARAM_STR);
                $stmt->bindParam(':add_time', $row['add_time'], PDO::PARAM_STR);
                $stmt->bindParam(':static_time', $row['static_time'], PDO::PARAM_STR);
                $stmt->bindParam(':is_renewal', $row['is_renewal'], PDO::PARAM_STR);
                $stmt->bindParam(':source_leads', $row['source_leads'], PDO::PARAM_STR);
                $stmt->bindParam(':invoice_number', $row['invoice_number'], PDO::PARAM_STR);
                $stmt->bindParam(':taxes_fees', $row['taxes_fees'], PDO::PARAM_STR);
                $stmt->bindParam(':add_adminid', $row['add_adminid'], PDO::PARAM_STR);
                $stmt->bindParam(':source_money', $row['source_money'], PDO::PARAM_STR);
                $stmt->bindParam(':noi_dung', $row['noi_dung'], PDO::PARAM_STR);
                $stmt->bindParam(':business_id', $row['business_id'], PDO::PARAM_STR);
                $stmt->bindParam(':transaction_id', $row['transaction_id'], PDO::PARAM_INT);

                $exc = $stmt->execute();
                $id = $db->lastInsertId();
                if ($exc and $id > 0) {

                    // đơn hàng thành công thì thêm gói vip vào thông tin người dùng
                    $_query_all = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE userid =' . $db->quote($row['userid']));
                    $_arr_vip = [];
                    $revenue = 0;
                    while ($_order = $_query_all->fetch()) {
                        $_arr_vip[$_order['vip']] = $_order['vip'];
                        $revenue += $_order['total_end'];
                    }

                    if (!empty($_arr_vip)) {
                        $db->query("UPDATE nv4_users_info SET vip = " . $db->quote(implode(',', $_arr_vip)) . ", revenue = " . $revenue . ", saleid = " . $row['caregiver_id'] . " WHERE userid = " . $row['userid']);
                    }

                    $nv_Cache->delMod($module_name);
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Add OrderGeneral', $id, $admin_id);
                    $this->result->setSuccess();
                    $this->result->set('ID', $id);
                    $this->result->set('Orderid: ', $row['order_id']);
                } else {
                    $this->result->setError()
                        ->setCode('4001')
                        ->setMessage('ID not response');
                }
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode($error_code)
                ->setMessage($error)
                ->getResult();
        }
        return $this->result->getResult();
    }
}
