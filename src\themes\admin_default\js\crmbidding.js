span__data = $(".span__data");
for (var i = 0; i < span__data.length; i++) "" !== span__data.eq(i).text() && 0 != span__data.eq(i).text() || span__data.eq(i).parent().hide();
$("#huy").hide();
$(".view_ordervip").hide();

function nv_delele_telepro_jobs(a) {
    confirm(nv_is_del_confirm[0]) && $.post(script_name + "?" + nv_lang_variable + "=" + nv_lang_data + "&" + nv_name_variable + "=" + nv_module_name + "&" + nv_fc_variable + "=telepro-jobs&nocache=" + (new Date).getTime(), "delete=1&id=" + a, function(b) {
        "OK" == b.split("_")[0] ? location.reload() : alert(nv_is_del_confirm[2])
    })
}

// Toast Function by Lâm
function toast({title = '', messages = '', type = 'info', duration = 3000}) {
    const main = document.getElementById('toast');
    if (main) {
        const toast = document.createElement('div');

        // Auto remove toast
        main.appendChild(toast);
        const autoRemove = setTimeout(function(){
            main.removeChild(toast);
        }, duration + 1000);

        // Cemove toast onclick
        toast.onclick = function(e) {
            if (e.target.closest('.toast__close')) {
                main.removeChild(toast);
                clearTimeout(autoRemove);
            }
        }

        const icons = {
            success: 'fa fa-check',
            info: 'fa-info-circle',
            warning: 'fa fa-exclamation-triangle',
            error: 'fa fa-exclamation-triangle',
        }
        const icon = icons[type];
        toast.classList.add('toast', 'toast--' + type);
        const delay = (duration/1000).toFixed(2);
        toast.innerHTML = `
        <div class="toast__icon">
            <i class="` + icon + `"></i>
        </div>

        <div class="toast__body">
            <h3 class="toast__title">` + title + `</h3>
            <p class="toast__msg">` + messages + `</p>
        </div>

        <div class="toast__close">
            <i class="fa fa-times"></i>
        </div>
        `;

    }
}

function nv_delele_telepro_logs(a) {
    confirm(nv_is_del_confirm[0]) && $.post(script_name + "?" + nv_lang_variable + "=" + nv_lang_data + "&" + nv_name_variable + "=" + nv_module_name + "&" + nv_fc_variable + "=telepro-logs&nocache=" + (new Date).getTime(), "delete=1&id=" + a, function(b) {
        "OK" == b.split("_")[0] ? location.reload() : alert(nv_is_del_confirm[2])
    })
}

function nv_telepro_logs_action(a, b, c) {
    a = a["idcheck[]"];
    b = "";
    if (a.length)
        for (var d = 0; d < a.length; d++) a[d].checked && (b = b + a[d].value + ",");
    else a.checked && (b = b + a.value + ",");
    "" != b ? "delete" == document.getElementById("action-of-telepro-logs").value && confirm(nv_is_del_confirm[0]) && $.post(script_name + "?" + nv_lang_variable + "=" + nv_lang_data + "&" + nv_name_variable + "=" + nv_module_name + "&" + nv_fc_variable + "=telepro-logs&nocache=" + (new Date).getTime(), "delete=1&listid=" + b, function(e) {
        "OK" == e.split("_")[0] ? location.reload() :
            alert(nv_is_del_confirm[2])
    }) : alert(c)
}
$(document).ready(function() {
    $(".show_detail").click(function(a) {
        $(this).parent().find("i").toggleClass("iconUp");
        $(this).parent().find("i").attr("class").split(" ")[2] ? $(this).parent().parent().parent().parent().find(".view_ordervip").hide() : $(this).parent().parent().parent().parent().find(".view_ordervip").show();
        $(this).parent().parent().parent().parent().find(".view_ordervip").slideToggle(500)
    });
    $(".show_detail_order").click(function(a) {
        a = $(this);
        a.parent().find("i").toggleClass("iconUp");
        a.parent().find("i").attr("class").split(" ")[2] ?
            a.parent().parent().parent().parent().find(".view_ordervip").hide() : a.parent().parent().parent().parent().find(".view_ordervip").show();
        a.parent().parent().parent().parent().find(".view_ordervip").slideToggle(500)
    });
    $(".slideDiv").click(function(a) {
        $id = $(this).attr("id");
        $(this).find(".fa-angle-double-right").toggleClass("iconDown");
        $(this).parent().parent().find(".panel-body").slideToggle(400)
    });
    $(".viewmore").click(function(a) {
        $(this).closest("tr").find(".view_ordervip").slideToggle(500)
    });
    $(".listOrder").find("i").toggleClass("iconDown");
    $(".title_list").click(function(a) {
        a = $(this).attr("class").split(" ")[1];
        $("#" + a).slideToggle(500);
        $(this).find("i").toggleClass("iconDown")
    });
    $(".btn__downleft").click(function(a) {
        a = $(this).attr("class").split(" ")[2];
        $("#" + a).slideToggle(500);
        $(this).find(".i").toggleClass("iconDown")
    });
    $(".ttcomment").click(function(a) {
        a = $(this).attr("class").split(" ")[1];
        $("#" + a).slideToggle(500);
        $(this).find(".icon").toggleClass("iconDown")
    });
    $("#myInputTB").on("keyup", function() {
        var a = $(this).val().toLowerCase();
        $("#tbody tr").filter(function() {
            $(this).toggle(-1 < $(this).text().toLowerCase().indexOf(a))
        })
    });
    $("#search-order").on("keyup", function() {
        var a = $(this).val().toLowerCase();
        $(".tt__order .showtt__order").filter(function() {
            console.log($(this).text().toLowerCase(), a);
            $(this).toggle(-1 < $(this).text().toLowerCase().indexOf(a))
        })
    });
    $("#search-vip").on("keyup", function() {
        var a = $(this).val().toLowerCase();
        $("#tt__vip .showtt__vip").filter(function() {
            $(this).toggle(-1 < $(this).text().toLowerCase().indexOf(a))
        })
    });
    $(".menu-icon-tools").on("click", function(a) {
        a.preventDefault();
        a = $(this).offset().top - $($(this).data("parent")).offset().top + $(this).find("i").height() - 4;
        var b = $(this).offset().left - $($(this).data("parent")).offset().left - 200 + $(this).find("i").width();
        $($(this).attr("href")).css({
            top: a + "px",
            left: b + "px"
        });
        $(".dropdown-parent-item:not(" + $(this).data("parent") + ")").removeClass("open");
        $($(this).data("parent")).toggleClass("open")
    });
    $("document,body").on("click", function(a) {
        $(a.target).closest(".menu-icon-tools").length ||
            $(a.target).closest(".dropdown-menu-item").length || $(a.target).is(".menu-icon-tools") || $(".dropdown-parent-item").removeClass("open")
    });
    $(window).on("resize", function() {
        $(".dropdown-parent-item").removeClass("open")
    });
    $('[data-toggle="ajActionReload"]').on("click", function(a) {
        a.preventDefault();
        var b = $(this);
        if (b.data("busy")) return alert(b.data("mbusy")), !1;
        confirm(b.data("msg")) && (b.data("busy", !0), $.ajax({
            type: "POST",
            url: script_name + "?" + nv_lang_variable + "=" + nv_lang_data + "&" + nv_name_variable + "=" +
                nv_module_name + "&" + nv_fc_variable + "=" + b.data("op") + "&nocache=" + (new Date).getTime(),
            data: b.data("action") + '=' + b.data("tokend") + '&id=' + b.data("id"),
            dataType: "json",
            cache: !1,
            success: function(c) {
                b.data("busy", !1);
                if (!c.success) return alert(c.text), 0;
                c.redirect ? window.location = c.redirect : b.data("redirect") ? window.location = b.data("redirect") : location.reload()
            },
            error: function(c, d, e) {
                b.data("busy", !1);
                alert("Request Error!!!");
                console.log(c, d, e)
            }
        }))
    });
    $('[data-toggle="toTop"]').on("click", function(a) {
        a.preventDefault();
        $("html, body").animate({
            scrollTop: 0
        }, 200)
    });
    $("#note").hide();
    $(".support__action_edit").click(function(a) {
        $("#huy").show(100);
        $("html, body").animate({
            scrollTop: $("#support_customer").offset().top
        }, "slow");
        userid = $(this).attr("data-userid");
        admin_id = $(this).attr("data-admin_id");
        note = $(this).attr("data-note");
        vip = $(this).attr("data-vip");
        id = $(this).attr("data-id");
        $("#vip").val(vip).trigger("change");
        $("#comment").text($("#bt_up").val());
        $("#comment").attr("data-status", "edit");
        $("#comment").attr("data-id",
            id);
        $("#comment").attr("data-admin_id", admin_id);
        $("#comment").attr("data-source", $(this).attr("data-source"));
        $("#source").attr("data-source", $(this).attr("data-source"));
        3 != $(this).attr("data-source") ? ($("#note").show(), $("#note").val(note), $("#content").hide(), $("#content").val(""), $("#selectVip").hide()) : ($("#note").hide(), $("#note").val(""), $("#content").val(note), $("#content").show(), $("#selectVip").show())
    });
    $("#huy").click(function(a) {
        $("#content").val("");
        $("#vip").val("0_0").trigger("change");
        $("#huy").hide(100);
        $("#comment").text($("#bt_note").val());
        $("#comment").attr("data-status", "add");
        $("#comment").attr("data-id", 0);
        $("#comment").attr("data-admin_id", 0);
        $("#source").attr("data-source", 0);
        $("#comment").attr("data-source", 0);
        $("#content").show();
        $("#selectVip").show();
        $("#note").hide();
        $("#note").val("")
    });
    $("#comment").click(function(a) {
        nv_settimeout_disable("comment", 4E3);
        a = $("#vip").val();
        $.ajax({
            url: window.location.href,
            type: "POST",
            data: {
                action: "saveComment",
                vip: a,
                userid: $(this).val(),
                content: $("#content").val(),
                status: $(this).attr("data-status"),
                id: $(this).attr("data-id"),
                admin_id: $(this).attr("data-admin_id"),
                source: $(this).attr("data-source"),
                note: $("#note").val()
            }
        }).done(function(b) {
            "requied" == b.status ? ($(".error").html(b.mess), toast({
                    title: b.res,
                    messages: b.mess,
                    type: "error"
                })) : "success" == b.status ? ($(".error").html(""), toast({
                    title: b.res,
                    messages: b.mess,
                    type: "success"
                }), $("#content").val(""), $("#vip").val(0).trigger("change"), setTimeout(function() {
                    location.reload()
                }, 300)) :
                console.log(b.res)
        }).fail(function() {
            console.log("error")
        })
    });
    $('[data-action="syncUser"]').on("click", function(a) {
        a.preventDefault();
        var b = $(this),
            c = $(".fa", b);
        c.is(".fa-spin") || (b.prop("disabled", !0), c.addClass("fa-spin"), $.ajax({
            type: "POST",
            url: script_name + "?" + nv_lang_variable + "=" + nv_lang_data + "&" + nv_name_variable + "=" + nv_module_name + "&nocache=" + (new Date).getTime(),
            data: {
                sync_user: b.data("tokend"),
                userid: b.data("userid")
            },
            dataType: "json",
            cache: !1,
            success: function(d) {
                b.prop("disabled", !1);
                c.removeClass("fa-spin");
                alert(d.message);
                d.success && location.reload()
            },
            error: function(d, e, f) {
                alert("Request Error!!!");
                console.log(d, e, f);
                b.prop("disabled", !1);
                c.removeClass("fa-spin")
            }
        }))
    })
});

// Custom function to separate comma
function nv_separateComma(val, separator  = '.') {
    if (val === Infinity) return val;

    let decimalPoint = ',';
    if (nv_lang_data !== 'vi'){
        decimalPoint = '.';
        separator = ',';
    }

    // remove sign if negative
    var sign = 1;
    if (val < 0) {
        sign = -1;
        val = -val;
    }
    // trim the number decimal point if it exists
    let num = val.toString().includes(decimalPoint) ? val.toString().split(decimalPoint)[0] : val.toString();
    let len = num.toString().length;
    let result = '';
    let count = 1;

    for (let i = len - 1; i >= 0; i--) {
        result = num.toString()[i] + result;
        if (count % 3 === 0 && count !== 0 && i !== 0) {
            result = separator  + result;
        }
        count++;
    }

    // add number after decimal point
    if (val.toString().includes(decimalPoint)) {
        result = result + decimalPoint + val.toString().split(decimalPoint)[1];
    }
    // return result with - sign if negative
    return sign < 0 ? '-' + result : result;
}
