<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

$lang_translator['author'] = 'VINADES.,JSC <<EMAIL>>';
$lang_translator['createdate'] = '16/01/2018, 07:25';
$lang_translator['copyright'] = '@Copyright (C) 2018 VINADES.,JSC. All rights reserved';
$lang_translator['info'] = '';
$lang_translator['langtype'] = 'lang_module';

$lang_module['main'] = 'Home';
$lang_module['points'] = 'Point';
$lang_module['points_1'] = 'point';
$lang_module['point_info'] = 'Point management';
$lang_module['point_block'] = 'Features not supported';
$lang_module['acount'] = 'Account';
$lang_module['money'] = 'Balance in wallet';
$lang_module['point'] = 'Existing points';
$lang_module['exchange_point'] = 'Buy points';
$lang_module['exchange_point_expired'] = 'Buy more points, expiry date of points %s';
$lang_module['exchange_select'] = 'Select additional purchase levels';
$lang_module['exchange'] = 'Buy more';
$lang_module['confirm_change'] = 'Are you sure to buy extra points.';
$lang_module['err_not_money'] = 'You do not have enough money to buy these points, please recharge your wallet. Press OK to move to the deposit page';
$lang_module['err_exchange'] = 'Transaction fail.';
$lang_module['ok_exchange'] = 'Transaction success.';
$lang_module['point_history'] = 'Buy / use points history';
$lang_module['payclass_error_save_transaction'] = 'Error: không tạo được giao dịch';
$lang_module['payclass_error_update_account'] = 'Error: không cập nhật được điểm';
$lang_module['stt'] = 'N.o';
$lang_module['created_time'] = 'Time';
$lang_module['point_total'] = 'Point';
$lang_module['message'] = 'Details';
$lang_module['menu'] = 'Points exchange on';
$lang_module['login_require'] = 'You need to login to use this function';
$lang_module['points_mail'] = 'Number of points deducted for the feature of sending mail / month';
$lang_module['points_filter'] = 'Points minus when adding 1 filter';
$lang_module['points_follow'] = 'Points minus when adding a tracking message';
$lang_module['points_downloadfile'] = 'Points minus when download file';
$lang_module['file_size_mb'] = '*MB: Megabyte, unit to calculate the size of the downloaded file';
$lang_module['points_view'] = 'View detailed information of the contractor (any information: invitation to bid, announcement time of the contractor selection plan, Bid opening results, Result of contractor selection.... called 1 bid information)';
$lang_module['points_view_adv'] = 'View advanced bid information (information hidden in bid details, VIEWEB/VIP registration required)';
$lang_module['expired_point'] = '%s point date %s has expired';
$lang_module['give_point'] = 'Give points to other accounts';
$lang_module['user_receive_point'] = 'Choose a point recipient';
$lang_module['type_user_receive'] = 'Recipient\'s email';
$lang_module['note_give_point'] = 'Note: Each time give a minimum of %s points. Recipient will receive %s bonus points';
$lang_module['confirm_give_point'] = 'You definitely want to donate points.';
$lang_module['num_point_give'] = 'Enter the number of bonus points';
$lang_module['require_points_user'] = 'Please enter enough information to give points!';
$lang_module['message_give_point'] = 'Give points for accounts with email %s';
$lang_module['message_received_point'] = 'Get points from account %s';
$lang_module['ok_give'] = 'Give points for success!';
$lang_module['err_give'] = 'An error occurred during giving points!';
$lang_module['err_point_invalid'] = 'Invalid points!';
$lang_module['err_miss_point'] = 'You don\'t have enough points';
$lang_module['err_not_active'] = 'The give point function has not been activated yet!';
$lang_module['err_email_invalid'] = 'Email invalidate!';
$lang_module['err_email_not_found'] = 'Email not found!';
$lang_module['subject_send_point'] = 'Notice you are awarded points!';
$lang_module['content_send_point'] = 'You have just received %s points from your %s account. To view your score and score history click here: %s';
$lang_module['reward_points'] = 'Bonus/gift points';
$lang_module['new_user'] = 'New members registered';
$lang_module['new_phone'] = 'Declare your phone number for the first time';
$lang_module['new_tax'] = 'Declare tax code for the first time';
$lang_module['new_phone_aff'] = 'Referred person declares phone number for the first time (referral bonus)';
$lang_module['new_tax_aff'] = 'Referred person declares tax code for the first time (referral bonus)';
$lang_module['new_user_aff'] = 'New user created via affiliate code (referral bonus)';
$lang_module['new_login'] = 'Every first time login of the day';
$lang_module['login_10m'] = 'After 10 minutes of online login on the site';
$lang_module['register_vip'] = 'When registering any service package';
$lang_module['register_vip_note'] = 'VND/1 successful order';
$lang_module['expired_time'] = 'Expiration time of gift points (days)';
$lang_module['expired'] = 'Expiration time of points (days)';
$lang_module['expired_day'] = 'Expired time';
$lang_module['day'] = 'Day';
$lang_module['given_points'] = 'Bonus points for members';
$lang_module['points_goods_download'] = 'Number of points deducted for each block of goods';
$lang_module['aff_points'] = 'Bonus points from <a href="https://dauthau.asia/en/collaborator/introducing-dauthau-info-affiliate-program.html">Affiliate Marketing program</a>';
$lang_module['notifi_countdown'] = 'In <span class="time_down">3s</span> we will redirect you to a page for you to work on';
$lang_module['paygate_error_site_id'] = 'Error: Site does not exist';
$lang_module['paygate_error_site_id1'] = 'Error: No site specified';
$lang_module['Confirm_give_point_1'] = 'You are sure to donate ';
$lang_module['Confirm_give_point_2'] = 'points for email accounts';
$lang_module['Confirm_give_point_3'] = '. The recipient will receive ';
$lang_module['Confirm_give_point_4'] = 'point';
$lang_module['point_givelog0'] = 'Give points to new members, expiry date of gift points to: %s';
$lang_module['point_givemessage0'] = '+ <b> % s </b> points for new members';
$lang_module['point_givelog1'] = 'Give points when declaring a phone number, expiry date of the points to: %s';
$lang_module['point_givemessage1'] = '+ <b> %s </b> points for the first member who declares a phone number';
$lang_module['point_givelog2'] = 'Give points when declaring tax code, expiry date of points to: %s';
$lang_module['point_givemessage2'] = '+ <b> %s </b> points for members who declare their tax identification numbers for the first time';
$lang_module['point_givelog3'] = 'Give points for first login of the day, expiry date of points to: %s';
$lang_module['point_givemessage3'] = '+ <b> %s </b> points for first login of the day';
$lang_module['point_givelog4'] = 'Give points when online for 10 minutes, expiry date of points to: %s';
$lang_module['point_givemessage4'] = '+ <b> %s </b> points for members when online for 10 minutes';
$lang_module['point_givelog5'] = 'Get points for referring your account <b>%s</b> register via referral code, expiry date for points: %s';
$lang_module['point_givemessage5'] = '+ <b> %s </b> points when referring account <b>%s</b> system registration.';
$lang_module['point_givelog6'] = 'Points are awarded when the referrer declares a phone number, the expiry date of the points to: %s';
$lang_module['point_givemessage6'] = '+ <b> %s </b> points when the referrer declares a phone number.';
$lang_module['point_givelog7'] = 'Points are awarded when the referrer declares a tax identification number, the expiry date of the points is to: %s';
$lang_module['point_givemessage7'] = '+ <b> %s </b> points when the referrer declares a tax identification number.';
$lang_module['online10'] = 'How to get more points: <br />- Online on site 10 minutes to get extra %s points.';
$lang_module['message_point_add_link'] = 'You have received +%s points due to the request to update data by %s, the system checked, there was a change in data, +%s points returned due to previous changes. that is deducted first for the update request, details here: %s';
$lang_module['message_point_add'] = 'You have received +%s points due to the request to update the data of %s, the system checked, there was a change in data, +%s points returned due to previous changes. that preempts the update request.';
$lang_module['point_crawl_plus'] = 'The number of points added when pressing the update button again has changed data';
$lang_module['note_point_crawl_plus'] = 'Included plus points: Deducted points + added points when unwrapping';
$lang_module['point_crawl_mintus'] = 'Update any information without data being changed';
$lang_module['error_select_point'] = 'You need to choose the number of points to buy more';
$lang_module['re_is_by_point'] = 'You need to select the number of additional points to purchase';
$lang_module['config'] = 'Cấu hình';
$lang_module['save'] = 'Lưu';
$lang_module['view'] = 'Xem';
$lang_module['module_config'] = 'Cấu hình module';
$lang_module['choose'] = 'Chọn';
$lang_module['cancel'] = 'Hủy';
$lang_module['config_exchange'] = 'Cấu hình nạp điểm';
$lang_module['exchange_money'] = 'Số tiền VNĐ';
$lang_module['exchange_point_admin'] = 'Số điểm nhận được';
$lang_module['cfg_exchange_add'] = 'Thêm tỉ lệ';
$lang_module['cfg_exchange_remove'] = 'Xóa';
$lang_module['acountuser'] = 'Quản lý điểm';
$lang_module['account'] = 'Tài khoản';
$lang_module['user_full_name'] = 'Họ tên';
$lang_module['user_email'] = 'Email';
$lang_module['user_birthday'] = 'Ngày sinh';
$lang_module['user_phone'] = 'Số điện thoại';
$lang_module['datecreate'] = 'Ngày tạo';
$lang_module['date_expired'] = 'Ngày hết hạn';
$lang_module['updatetime'] = 'Ngày cập nhật';
$lang_module['function'] = 'Chức năng';
$lang_module['editacount'] = 'Cập nhật tài khoản';
$lang_module['search_title'] = 'Nhập từ khóa tìm kiếm (username, email)';
$lang_module['search_submit'] = 'Tìm kiếm';
$lang_module['transaction'] = 'Lịch sử giao dịch';
$lang_module['usertransaction'] = 'Người thực hiện giao dịch';
$lang_module['add_transaction'] = 'Tạo giao dịch';
$lang_module['money_transaction'] = 'Số điểm';
$lang_module['transaction_info'] = 'Nội dung';
$lang_module['error_required_money_transaction'] = 'Lỗi: chưa nhập số điểm giao dịch';
$lang_module['error_required_transaction_info'] = 'Lỗi: chưa nhập nội dung';
$lang_module['error_required_customer'] = 'Lỗi: Chưa chọn thành viên';
$lang_module['error_exists_customer'] = 'Lỗi: Thành viên %s không tồn tại';
$lang_module['error_max_int'] = 'Lỗi: Số điểm quá lớn vui lòng nhập số điểm hợp lệ (nhỏ hơn 10.000.000 điểm)';
$lang_module['select'] = 'Chọn';
$lang_module['type'] = 'Loại giao dịch';
$lang_module['reward_points_config'] = 'Cấu hình tặng điểm';
$lang_module['note_reward'] = 'Chỉ áp dụng cho các thành viên thường (thành viên không sử dụng gói nào, với các thành viên đang sử dụng gói VIEWEB hoặc gói VIP thì không áp dụng, thành viên đã đăng ký VIEWEB/VIP mà hết hạn thì lại được áp dụng';
$lang_module['purchased_points_conf'] = 'Cấu hình điểm mua (Điểm thành viên sử dụng tiền để mua.)';
$lang_module['given_points_conf'] = 'Cấu hình điểm tặng (Điểm hệ thống tặng cho thành viên khi hoàn thành 1 nhiệm vụ theo quy định.)';
$lang_module['mem_given_points_conf'] = 'Cấu hình tính năng thành viên cho điểm nhau';
$lang_module['bonus_points_conf'] = 'Cấu hình điểm thưởng (Điểm mà thành viên được thưởng từ chương trình Afiliate Marketing.)';
$lang_module['expired_admin'] = 'Thời gian hết hạn của điểm mua (ngày)';
$lang_module['expired_aff'] = 'Thời gian hết hạn của điểm thưởng từ chương trình Afiliate Marketing (ngày)';
$lang_module['statistical'] = 'Thống kê điểm';
$lang_module['name_stt'] = 'STT';
$lang_module['name_customer'] = 'Khách hàng';
$lang_module['point_in'] = 'Số điểm nạp';
$lang_module['point_out'] = 'Số điểm sử dụng';
$lang_module['total_point'] = 'Số điểm còn lại';
$lang_module['total_tang'] = 'Số điểm được tặng';
$lang_module['name_satus'] = 'Trạng thái';
$lang_module['time_update'] = 'Thời gian cập nhật điểm';
$lang_module['active'] = 'Hiệu lực';
$lang_module['note_active'] = 'Hết hạn';
$lang_module['list_customer'] = 'Danh sách khách hàng';
$lang_module['top_customer_point'] = 'Top 10 khách hàng có nhiểu điểm nhất';
$lang_module['top_sales_transaction'] = 'Top 10 sales có giao dịch nhiều nhất';
$lang_module['log_customer_point'] = 'Lịch sử điểm của khách hàng';
$lang_module['minus_point'] = 'Trừ điểm';
$lang_module['plus_point'] = 'Cộng điểm';
$lang_module['time'] = 'Thời gian';
$lang_module['nodata'] = 'Không có dữ liệu';
$lang_module['note_seach'] = 'Ghi chú: Nếu tích vào tháng sẽ hiển thị dữ liệu tất cả các tháng đó';
$lang_module['sort'] = 'Lọc theo';
$lang_module['month'] = 'Tháng';
$lang_module['btn_search'] = 'Tìm kiếm';
$lang_module['all'] = 'Tất cả';
$lang_module['acoount_search'] = 'Họ và tên hoặc tài khoản';
$lang_module['donate_add'] = 'Tặng tiền';
$lang_module['donate_apart'] = 'Trừ tiền';
$lang_module['convert_point'] = 'Quy đổi ra số tiền';
$lang_module['valuePoint'] = 'Điểm';
$lang_module['note__point'] = 'Ghi chú: <br> 1. <b>Điều chỉnh số liệu lệch</b>: bạn được nhập tự do số điểm, hệ thống không tác động vào ví tiền. <br> 2. <b>Khi giao dịch hộ khách hàng</b>: bạn phải chọn mốc điểm theo cấu hình, hệ thống sẽ trừ tiền hoặc cộng tiền vào ví của khách hàng trùy loại giao dịch +/- <br> 3. Nếu lựa chọn <b>Giao dịch hộ khách hàng</b> và loại giao dịch là <b>Cộng điểm</b> thì hệ thống sẽ tác động trừ tiền vào tài khoản của khách hàng và ngược lại';
$lang_module['first'] = 'Họ và tên';
$lang_module['account_customer'] = 'Tài khoản khách hàng';
$lang_module['account_admin'] = 'Tài khoản sale';
$lang_module['time_add_point'] = 'Thời gian giao  dịch gần nhất';
$lang_module['statistical_sales'] = 'Thống kê sale';
$lang_module['add_point'] = 'Cộng điểm';
$lang_module['sum_point_in'] = 'Tổng số điểm giao dịch';
$lang_module['list_sales'] = 'Danh sách giao dịch của Sales';
$lang_module['name_sale'] = 'Sale';
$lang_module['statistical_tran'] = 'Thống kê giao dịch';
$lang_module['sum_tran'] = 'Tổng giao dịch trong tháng';
$lang_module['sum_tran_all'] = 'Tổng tất cả cả giao dịch';
$lang_module['point_total_now'] = 'Số điểm hiện tại';
$lang_module['mess_transaction'] = 'Perform point deposit transaction through Sale';
$lang_module['user_tran'] = 'Khách hàng thực hiện giao dịch nhiều nhất';
$lang_module['num_tran'] = 'Số giao dịch';
$lang_module['error_not_point'] = 'Lỗi: Không thể nạp điểm';
$lang_module['error_not_money'] = 'Lỗi: Khách hàng <b>%s</b> đang có <b>%s</b> - không đủ số dư để thực hiện giao dịch này';
$lang_module['tacdong_money'] = 'Tác động tới tiền khách hàng';
$lang_module['detai_tran'] = 'Thông tin chi tiết giao dịch';
$lang_module['hinhthuc'] = 'Hình thức giao dịch';
$lang_module['addMoney'] = 'Cộng tiền';
$lang_module['minusMoney'] = 'Trừ tiền';
$lang_module['tran_custom'] = 'Giao dịch hộ khách hàng';
$lang_module['shlech'] = 'Điều chỉnh số liệu lệch';
$lang_module['option_pointin'] = 'Chọn số điểm nạp';
$lang_module['limit_give_points'] = 'Giới hạn điểm tối thiểu';
$lang_module['ratio_give_points'] = 'Tỷ lệ điểm tặng (%)';
$lang_module['use_give_points'] = 'Tính năng tặng điểm';
$lang_module['user_not_exist'] = 'Lỗi: Tên người dùng không tồn tại.';
$lang_module['statistical_point'] = 'Thống kê điểm';
$lang_module['statistical_caption'] = 'Thống kê trên toàn hệ thống từ ngày %s đến ngày %s';
$lang_module['point_in_total'] = 'Tổng số điểm nạp vào';
$lang_module['point_left_total'] = 'Tổng số điểm chưa sử dụng';
$lang_module['point_left'] = 'Số điểm chưa sử dụng';
$lang_module['this_month'] = 'Tháng hiện tại';
$lang_module['last_3_months'] = 'Quý hiện tại';
$lang_module['this_year'] = 'Năm hiện tại';
$lang_module['last_all_days'] = 'Toàn bộ';
$lang_module['custom_range'] = 'Khác';
$lang_module['select_time'] = 'Chọn khoảng thời gian';
$lang_module['api_point'] = 'Điểm';
$lang_module['api_point_UpdatePoint'] = 'Cập nhật điểm (UpdatePoint)';
$lang_module['api_point_GetRewardMessages'] = 'Lấy thông báo được tặng điểm (GetRewardMessages)';
$lang_module['api_point_GetAllExchange'] = 'Lấy quy đổi điểm (GetAllExchange)';
$lang_module['api_point_GiveTempPoints'] = 'Đánh dấu tặng điểm (GiveTempPoints)';
$lang_module['api_point_GetAllConfig'] = 'Lấy cấu hình điểm (GetAllConfig)';
$lang_module['api_err_site'] = 'site_id chưa chỉ định hoặc không tồn tại';
$lang_module['api_err_give1'] = 'Vui lòng chỉ định số điểm tặng';
$lang_module['api_err_give2'] = 'Vui lòng khai báo lý do tặng';
$lang_module['api_err_give3'] = 'Vui lòng chỉ định thời gian hết hạn điểm tặng';
$lang_module['api_err_give4'] = 'Đã tặng điểm ngày 10/10 rồi!!';
$lang_module['title_crawl'] = 'Cấu hình tặng điểm khi bóc tin';
$lang_module['title_hethanpoint'] = 'Thời gian hết hạn sử dụng điểm';
$lang_module['title_note_solicitor'] = 'Bên mời thầu có mã cơ quan hoặc mã định danh: ';
$lang_module['title_note_khlcnt'] = 'Kế hoạch lựa chọn nhà thầu có số KHLCNT: ';
$lang_module['title_note_tbmt'] = 'Thông báo mời thầu có số TBMT: ';
$lang_module['title_note_kqmt'] = 'Kết quả mở thầu có số TBMT: ';
$lang_module['title_note_kqlcnt'] = 'Kết quả lựa chọn nhà thầu có số TBMT: ';
$lang_module['title_note_business'] = 'Nhà thầu có số DKKD hoặc mã định danh: ';
$lang_module['title_note_duancb'] = 'Dự án đầu tư phát triển có mã dự án: ';
$lang_module['title_staticcal_give_point'] = 'Thống kê tặng điểm';
$lang_module['account_give_point'] = 'Tài khoản tặng';
$lang_module['receive_give_point'] = 'Tài khoản được tặng';
$lang_module['give_point_admin'] = 'Số điểm tặng';
$lang_module['sodiemnhan'] = 'Số điểm nhận được';
$lang_module['tongdiemtang'] = 'Tổng điểm đã tặng';
$lang_module['tongdiemnhan'] = 'Tổng điểm đã nhận';
$lang_module['title_gd_td'] = 'Các giao dịch tặng điểm và trừ điểm';
$lang_module['updattime_new'] = 'Thời gian giao dịch mới nhất';
$lang_module['diem'] = 'điểm';
$lang_module['msg_104'] = '+ In celebration of National Digital Transformation Day on 10/10, you have been rewarded with 110 points. These points will allow you to have access to advanced tender information, download bidding documents quickly, and experience VIP features...';
$lang_module['msg_105'] = '+ In celebration of Vietnamese Entrepreneurs Day on 13/10, you have been rewarded with %s points. You can find event details <a href="%s">here</a>';
$lang_module['msg_108'] = '+ <b>%s</b> points. You can find event details <a href="%s">here</a>';
$lang_module['msg_109'] = '+ <b>%s</b> points on the occasion of the New Year. You can find event details <a href="%s">here</a>';
$lang_module['msg_113'] = '+ <b>%s</b> points on the occasion of the New Year. You can find event details <a href="%s">here</a>';
$lang_module['conversion_factor'] = 'I: Number of bidding packages that the contractor participates in.<br>J=K=L=10 if the contractor is a listed company (J), construction organization (K), has bidding violations (L).<br>X: number of bidding violations.';
$lang_module['expired_used_point'] = 'Point expiry date';
$lang_module['group_feature_other_en'] = 'Other Features';
$lang_module['viewpoint'] = 'View point management';

$lang_module['search_from'] = 'Time from statistics';
$lang_module['search_to'] = 'Statistics arrival time';
$lang_module['search_error'] = 'Error: Search interval limited to 1 year';
