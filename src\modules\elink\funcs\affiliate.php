<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Apr 20, 2010 10:47:41 AM
 */

if (!defined('NV_IS_MOD_ELINK')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

$page_title = $nv_Lang->getModule('affiliate_manager');
$description = $keywords = 'no';

if (!defined('NV_IS_USER')) {
    $link_redirect = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($client_info['selfurl']);
    nv_redirect_location($link_redirect);
}

$array = [];
$array['affiliate_enabled'] = nv_user_in_groups($global_array_config['affiliate_code_groups']);
$array['promotion_enabled'] = nv_user_in_groups($global_array_config['groups_allowed_string']);

if (!$array['affiliate_enabled']) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}

$array_mod_title[] = array(
    'catid' => 0,
    'title' => $page_title,
    'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op
);

$array['affiliate_code'] = $user_info['username'];

// Tạo link chứa
if ($nv_Request->isset_request('creataffiliatelink', 'post')) {
    if (!defined('NV_IS_AJAX')) {
        die('Wrong URL!!!');
    }

    $respon = [
        'status' => 'ERROR',
        'message' => 'ERROR',
        'link' => 'ERROR'
    ];

    $link = str_replace('&amp;', '&', $nv_Request->get_title('link', 'post', ''));
    $url_data = parse_url($link);

    // Các domain được phép tạo link
    $allowed_server = [];
    foreach ($global_array_sites as $site) {
        $allowed_server[] = $site['sitedomain'];
    }

    if (empty($link) or $url_data === false or empty($url_data['path'])) {
        $respon['message'] = $nv_Lang->getModule('aff_error_link1');
    } elseif (isset($url_data['host']) and !in_array($url_data['host'], $allowed_server)) {
        $respon['message'] = $nv_Lang->getModule('aff_error_link2') . ' ' . implode(', ', $allowed_server);
    } else {
        $query_string = [];
        if (!empty($url_data['query'])) {
            parse_str(urldecode($url_data['query']), $query_string);
        }
        unset($query_string['uid']);
        $query_string['uid'] = $array['affiliate_code'];
        $respon['status'] = 'SUCCESS';

        $respon['link'] = '';
        if (isset($url_data['host'])) {
            if (isset($url_data['scheme'])) {
                $respon['link'] .= $url_data['scheme'] . '://';
            }
            $respon['link'] .= $url_data['host'];
        } else {
            $respon['link'] = NV_MY_DOMAIN;
        }
        $respon['link'] .= $url_data['path'] . '?' . http_build_query($query_string);
    }

    nv_jsonOutput($respon);
}

// 15 người giới thiệu gần đây nhất
$array['users'] = [];
$array['users_link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=affiliate-users';
$sql = "SELECT tb2.userid, tb2.email, tb2.first_name, tb2.last_name, tb2.regdate FROM " . $db_config['prefix'] . "_" . $module_data . "_affiliate_set tb1,
" . NV_USERS_GLOBALTABLE . " tb2 WHERE tb1.pre_uid=tb2.userid AND tb1.pri_uid=" . $user_info['userid'] . "
ORDER BY tb2.regdate DESC LIMIT 15";
$array_userids = [];
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $array['users'][$row['userid']] = $row;
    $array_userids[$row['userid']] = $row['userid'];
}

$array_user_phone = [];
$array_user_vip = [];
if (!empty($array_userids)) {
    // Lấy số điện thoại các thành viên trên
    $sql = "SELECT userid, phone FROM " . NV_USERS_GLOBALTABLE . "_info WHERE userid IN(" . implode(',', $array_userids) . ")";
    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        $array_user_phone[$row['userid']] = $row['phone'];
    }

    // Lấy các gói VIP
    $arr_where = [];
    foreach ($array_userids as $uid) {
        $arr_where['OR'][] = [
            '=' => [
                'user_id' => $uid
            ]
        ];
    }
    $arr_where['AND_OR'][] = [
        '=' => [
            'status' => 1
        ]
    ];
    $arr_where['AND_OR'][] = [
        '=' => [
            'status' => 2
        ]
    ];
    $params_customs = [
        'where' => $arr_where,
        'page' => 1,
        'per_page' => sizeof($array_userids)
    ];

    $api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
    $api->setModule('bidding')
    ->setLang('vi')
    ->setAction('ListBiddingCustoms')
    ->setData($params_customs);
    $result_customs = $api->execute();

    if (is_array($result_customs) and !empty($result_customs['data'])) {
        foreach ($result_customs['data'] as $row) {
            if (!isset($array_user_vip[$row['user_id']][$row['vip']]) or $array_user_vip[$row['user_id']][$row['vip']]['status'] > $row['status']) {
                $array_user_vip[$row['user_id']][$row['vip']] = $row;
            }
        }
    }
}

// Lấy số mã khuyến mãi đã tạo và số mã khuyến mãi mặc định
$array['promotion_numbers'] = 0;
$array['promotion_default'] = '';
if ($array['promotion_enabled']) {
    $array['promotion_numbers'] = $db->query("SELECT COUNT(promo_id) FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE userid=" . $user_info['userid'])->fetchColumn();
    $array['promotion_default'] = $db->query("SELECT promo_code FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE userid=" . $user_info['userid'] . " AND is_affiliate=1")->fetchColumn();
}

$contents = nv_elink_theme_affiliate($array, $array_user_phone, $array_user_vip);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
