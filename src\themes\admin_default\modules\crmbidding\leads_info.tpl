<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css">
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<!-- BEGIN: error -->
<div class="alert alert-danger">{ERROR}</div>
<!-- END: error -->
<!-- BEGIN: prompt_to_combine -->
    <div class="alert alert-warning">{LANG.prompt_to_combine}</div>
<!-- END: prompt_to_combine -->

<!-- BEGIN: success -->
<div class="alert alert-success">{SUCCESS}</div>
<script type="text/javascript">
   $(document).ready(function($) {
        setTimeout(function() {
            location.href = location.href;
        }, 5000);
   });
</script>
<!-- END: success -->

<div class="row">
    <div class="col-md-14">
        <div class="panel panel-default">
            <div class="panel-body">
                <form class="form-horizontal" action="{FORM_ACTION}" method="post">
                    <div class="row">
                        <h2 class="text-info col-md-8">
                            {LANG.leads_info}
                            <!-- BEGIN: label -->
                            <span style="background-color: {LABEL.color}"><i class="fa fa-flag"></i> {LABEL.title}</span><br />
                            <!-- END: label -->
                            <!-- BEGIN: schedule -->
                            <span style="color: red;"><i class="fa fa-bell"></i> {LANG.schedule}: {SCHEDULE} </span> <br />
                            <!-- END: schedule -->
                        </h2>
                        <div class="col-md-16 text-right">
                            <!-- BEGIN: show_link_detai_customer -->
                            <a href="{LINK_VIEW_MANAGER_CUSTOMER}" class="btn btn-primary" data-toggle="tooltip" title="{LANG.manage_customer}"> <i class="fa fa-google-wallet" aria-hidden="true"></i>
                            </a>
                            <!-- END: show_link_detai_customer -->

                            <!-- BEGIN: convert_contact -->
                            <a href="{LINK_CONVERT_CONTACT}" class="btn btn-primary" data-toggle="tooltip" title="{LANG.convert_contact}"> <i class="fa fa-address-book"></i>
                            </a>
                            <!-- END: convert_contact -->
                            <!-- BEGIN: view_contact -->
                            <a href="{LINK_VIEW_CONTACT}" class="btn btn-primary" data-toggle="tooltip" title="{LANG.view_contact}"> <i class="fa fa-address-book"></i>
                            </a>
                            <!-- END: view_contact -->

                            <!-- BEGIN: convert_org -->
                            <a href="{LINK_CONVERT_ORG}" class="btn btn-primary" data-toggle="tooltip" title="{LANG.convert_org}"> <i class="fa fa-users"></i>
                            </a>
                            <!-- END: convert_org -->
                            <!-- BEGIN: view_org -->
                            <a href="{LINK_VIEW_ORG}" class="btn btn-primary" data-toggle="tooltip" title="{LANG.view_org}"> <i class="fa fa-users"></i>
                            </a>
                            <!-- END: view_org -->

                            <!-- BEGIN: accept -->
                            <button type="button" class="btn btn-accept btn-primary" data-toggle="tooltip" title="{LANG.nhan_lead}">
                                <i class="fa fa-check-circle"></i>
                            </button>
                            <!-- END: accept -->
                            <button type="button" class="btn btn-label btn-primary" data-toggle="tooltip" title="{LANG.menu_label}">
                                <i class="fa fa-star"></i>
                            </button>
                            <!-- BEGIN: fa_user -->
                            <button type="button" class="btn btn-primary btnusers" data-toggle="tooltip" title="{LANG.opportunities_info_user}">
                                <i class="fa fa-user"></i>
                            </button>
                            <!-- END: fa_user -->
                            <!-- BEGIN: opportunities_id -->
                            <a href="{LINK_OPPOTUNITIES}" class="btn btn-primary" data-toggle="tooltip" title="{LANG.opportunities}"> <i class="fa fa-cubes"></i>
                            </a>
                            <!-- END: opportunities_id -->
                            <!-- BEGIN: no_opportunities_id -->
                            <button type="button" class="btn btn-opportunities btn-primary" data-toggle="tooltip" title="{LANG.opportunities}">
                                <i class="fa fa-cubes"></i>
                            </button>
                            <!-- END: no_opportunities_id -->
                            <button type="button" class="btn editbtn btn-primary {hidden_edit}" data-toggle="tooltip" title="{GLANG.edit}">
                                <i class="fa fa-pencil" aria-hidden="true"></i>
                            </button>
                            <button type="submit" name="submit" class="btn savebtn btn-primary hidden" value="1">
                                <i class="fa fa-floppy-o" aria-hidden="true"></i>
                            </button>
                            <button type="button" class="btn deletebtn btn-danger hidden">
                                <i class="fa fa-times" aria-hidden="true"></i>
                            </button>
                            <!-- BEGIN: sync_user_btn -->
                            <button type="button" class="btn btn-info" data-action="syncUser" data-userid="{ROW.user_id}" data-tokend="{TOKEND}" data-toggle="tooltip" title="{LANG.sync_user}">
                                <i class="fa fa-retweet" aria-hidden="true"></i>
                            </button>
                            <!-- END: sync_user_btn -->
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-md-12 col-sm-24">
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.name}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary textinfo">{ROW.name}</strong> <input class="form-control inputinfo hidden required" type="text" name="name" value="{ROW.name}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">
                                    {LANG.phone} <i class="fa fa-info-circle red" data-original-title="Giá trị Số điện thoại hoặc email có thể nhập 1 trong 2. {LANG.info_phone}" data-toggle="tooltip"></i>
                                </div>
                                <div class="col-sm-18 col-md-16">
                                    <strong id="phone" class="text-primary textinfo">{ROW.phone}</strong> <input class="form-control inputinfo hidden required" type="text" name="phone" value="{ROW.phone}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.sub_phone}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong id="sub_phone" class="text-primary textinfo">{ROW.sub_phone}</strong> <input class="form-control inputinfo hidden" type="text" name="sub_phone" value="{ROW.sub_phone}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.address}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary textinfo">{ROW.address}</strong> <input class="form-control inputinfo hidden" type="text" name="address" value="{ROW.address}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.company_name}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary textinfo">{ROW.company_name}</strong> <input class="form-control inputinfo hidden" type="text" name="company_name" value="{ROW.company_name}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.address_company}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary textinfo">{ROW.address_company}</strong> <input class="form-control inputinfo hidden" type="text" name="address_company" value="{ROW.address_company}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.tax}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong id="tax" class="text-primary textinfo">{ROW.tax}</strong> <input class="form-control inputinfo hidden" type="text" name="tax" value="{ROW.tax}" />
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.website}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong id="siteid" class="text-primary textinfo">{ROW.title_siteid} {ROW.prefix_lang_letter}</strong> <select class="form-control inputinfo hidden" name="siteid">
                                        <!-- BEGIN: select_siteid -->
                                        <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                                        <!-- END: select_siteid -->
                                    </select> <br /> <select class="form-control inputinfo hidden" name="prefix_lang">
                                        <!-- BEGIN: select_prefix_lang -->
                                        <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                                        <!-- END: select_prefix_lang -->
                                    </select>
                                </div>
                            </div>
                            <!-- BEGIN: ext_uinfo -->
                            <!-- BEGIN: loop -->
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{F_NAME}</div>
                                <div class="col-sm-18 col-md-16">
                                    <div class="textinfo">
                                        <!-- BEGIN: items -->
                                        <ul class="pl-4 mb-0">
                                            <!-- BEGIN: item -->
                                            <li><strong class="text-primary">{F_VALUE}</strong></li>
                                            <!-- END: item -->
                                        </ul>
                                        <!-- END: items -->
                                        <!-- BEGIN: na -->
                                        <strong class="text-primary">N/A</strong>
                                        <!-- END: na -->
                                    </div>
                                    <div class="inputinfo hidden">
                                        <!-- BEGIN: edititem -->
                                        <div class="checkbox">
                                            <label> <input type="checkbox" name="{F_KEY}[]" {F_INPUT_CHECKED} value="{F_INPUT_KEY}"> {F_VALUE}
                                            </label>
                                        </div>
                                        <!-- END: edititem -->
                                    </div>
                                </div>
                            </div>
                            <!-- END: loop -->
                            <!-- END: ext_uinfo -->
                        </div>
                        <div class="col-md-12 col-sm-24">
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.source_leads}</div>
                                <div class="col-sm-18 col-md-16">
                                    <!-- BEGIN: no_user -->
                                    <strong class="text-primary textinfo"> {ROW.source_leads} </strong>
                                    <!-- END: no_user -->
                                    <!-- BEGIN: view_detail_bussiness -->
                                    <i><a href="{LINK_DETAIL_BUSINESS}">{LANG.view_detail_business}</a></i>
                                    <!-- END: view_detail_bussiness -->
                                    <!-- BEGIN: btn_telepro -->
                                    <strong class="text-primary textinfo btn_telepro"> {ROW.source_leads} <i class="fa fa-phone"></i>
                                    </strong>
                                    <!-- END: btn_telepro -->

                                    <select class="form-control inputinfo hidden" name="source_leads">
                                        <!-- BEGIN: source_leads -->
                                        <option value="{GROUPS_LEADS.id}"{GROUPS_LEADS.selected}>{GROUPS_LEADS.title}</option>
                                        <!-- END: source_leads -->
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row gr_business_id">
                                <label class="col-sm-6 col-md-8 control-label">{LANG.select_business} <span class="red" id="required_business">(*)</span></label>
                                <div class="col-sm-18 col-md-16 textinfo">
                                    <strong class="text-primary">{ROW.business_name}</strong>
                                </div>
                                <div class="col-sm-18 col-md-16 inputinfo hidden">
                                    <select class="form-control" name="business_id" id="business_id">
                                        <option value="">---</option>
                                        <!-- BEGIN: select_business_id -->
                                        <option value="{OPTION.key}"{OPTION.selected_business_id}>{OPTION.title}</option>
                                        <!-- END: select_business_id -->
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row gr_solicitor_id">
                                <label class="col-sm-6 col-md-8 control-label">{LANG.select_solicitor} <span class="red" id="required_solicitor">(*)</span></label>
                                <div class="col-sm-18 col-md-16 textinfo">
                                    <strong class="text-primary">{ROW.solicitor_name}</strong>
                                </div>
                                <div class="col-sm-18 col-md-16 inputinfo hidden">
                                    <select class="form-control" name="solicitor_id" id="solicitor_id">
                                        <option value="">---</option>
                                        <!-- BEGIN: select_solicitor_id -->
                                        <option value="{OPTION.key}"{OPTION.selected_solicitor_id}>{OPTION.title}</option>
                                        <!-- END: select_solicitor_id -->
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.email}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong id="email" class="text-primary textinfo text-break">{ROW.email}</strong> <input class="form-control inputinfo hidden required" type="text" name="email" value="{ROW.email}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.sub_email}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong id="sub_email" class="text-primary textinfo">{ROW.sub_email}</strong> <input class="form-control inputinfo hidden" type="text" name="sub_email" value="{ROW.sub_email}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.timecreate}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary">{ROW.timecreate}</strong>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.updatetime}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary">{ROW.updatetime}</strong>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.activity_time}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary">{ROW.activity_time}</strong>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.status}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary">{ROW.status}</strong>
                                    <!-- <select class="form-control inputinfo hidden" name="status">
                                        BEGIN: status
                                        <option value="{STATUS.id}"{STATUS.selected}>{STATUS.title}</option>
                                        END: status
                                    </select> -->
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.affilacate_id}</div>
                                <div class="col-sm-18 col-md-16">
                                    <!-- BEGIN: no_affilacate -->
                                    <strong class="text-primary">{ROW.affilacate_title}</strong>
                                    <!-- END: no_affilacate -->
                                    <!-- BEGIN: affilacate -->
                                    <strong class="text-primary textinfo">{ROW.affilacate_title}</strong> <select class="form-control inputinfo hidden" name="affilacate_id">
                                        <option value="0">---</option>
                                        <!-- BEGIN: affilacate_id -->
                                        <option value="{CAREGIVER_ID.userid}"{CAREGIVER_ID.selected_affilacate}>{CAREGIVER_ID.title}</option>
                                        <!-- END: affilacate_id -->
                                    </select>
                                    <!-- END: affilacate -->
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.caregiver_id}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary textinfo">{ROW.caregiver_title}</strong> <select class="form-control inputinfo hidden" name="caregiver_id">
                                        <option value="0">---</option>
                                        <!-- BEGIN: caregiver_id -->
                                        <option value="{CAREGIVER_ID.userid}"{CAREGIVER_ID.selected}>{CAREGIVER_ID.title}</option>
                                        <!-- END: caregiver_id -->
                                    </select>
                                </div>
                            </div>
                            <!-- BEGIN: starttime -->
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.starttime}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary">{STARTTIME}</strong>
                                </div>
                            </div>
                            <!-- END: starttime -->
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.about}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary textinfo">{ROW.about}</strong>
                                    <textarea class="form-control inputinfo hidden" name="about" rows="6">{ROW.about}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-24 text-right">
                            <button type="submit" name="submit" class="btn savebtn btn-primary hidden" value="1">
                                <i class="fa fa-floppy-o" aria-hidden="true"></i>
                            </button>
                            <button type="button" class="btn deletebtn btn-danger hidden">
                                <i class="fa fa-times" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-body">
                <h2 class="text-info">{LANG.caregiver_history}</h2>
                <hr>
                <!-- BEGIN: another_hot_lead -->
                <div class="alert alert-danger">{ANOTHER_HOT_LEAD}</div>
                <!-- END: another_hot_lead -->
                <!-- BEGIN: comment_form -->
                <div class="form-group col-md-24">
                    <form class="form-horizontal" action="{FORM_ACTION}" method="post" class="form-inline">
                        <label class="col-md-24"><b>{LANG.note}</b></label>
                        <div class="form-group col-md-24">
                            <textarea class="form-control" name="note" rows="4">{EDIT_LOG.note}</textarea>
                        </div>
                        <div class="form-group col-md-12">
                            <label><b>{LANG.schedule}</b></label> <input class="form-control w100 uidatepicker" style="display: inline-block;" type="text" value="{EDIT_LOG.schedule}" name="schedule" maxlength="10" autocomplete="off">
                        </div>
                        <div class="form-group col-md-12">
                            <!-- BEGIN: cb_status0 -->
                            <label><b><input class="form-control" type="checkbox" value="0" name="cb_status">{LANG.status0}</b></label> <br>
                            <!-- END: cb_status0 -->
                            <!-- BEGIN: cb_status1 -->
                            <label><b><input class="form-control" type="checkbox" value="1" name="cb_status">{LANG.status1}</b></label> <br>
                            <!-- END: cb_status1 -->
                            <!-- BEGIN: cb_status3 -->
                            <label><b><input class="form-control" type="checkbox" value="3" name="cb_status">{LANG.status3}</b></label> <br>
                            <!-- END: cb_status3 -->
                        </div>
                        <div class="form-group col-md-24" style="text-align: right;">
                            <!-- BEGIN: add_comment -->
                            <input class="btn btn-primary" name="save" type="submit" value="{LANG.comment}" />
                            <!-- END: add_comment -->
                            <!-- BEGIN: edit_comment -->
                            <input type="hidden" name="comentid" value="{EDIT_LOG.id}" /> <input class="btn btn-primary" name="save" type="submit" value="{LANG.update}" />
                            <!-- END: edit_comment -->
                        </div>
                    </form>
                </div>
                <!-- END: comment_form -->
                <!-- BEGIN: comment_alert -->
                <div class="form-group col-md-24">
                    <div class="alert alert-danger">{LANG.comment_err}</div>
                </div>
                <!-- END: comment_alert -->
                <!-- BEGIN: comment -->
                <div class="table-responsive col-md-24">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th class="text-nowrap w150">{LANG.timecreate}/{LANG.updatetime}</th>
                                <th class="text-nowrap w150">{LANG.log_user}</th>
                                <th class="text-nowrap">{LANG.log_data}</th>
                                <th class="text-nowrap"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- BEGIN: loop -->
                            <tr>
                                <td>{LOG_COMMENT.timecreate}<br />{LOG_COMMENT.update_time}
                                </td>
                                <td>{LOG_COMMENT.fullname}</td>
                                <td>
                                    <!-- BEGIN: schedule --> <i class="fa fa-clock"></i><b>{LANG.schedule}</b>: {LOG_COMMENT.schedule_title} <br /> <!-- END: schedule --> <!-- BEGIN: send_mail --> <i class="fa fa-paper-plane-o" aria-hidden="true"></i> {LANG.sendmail_to_customer}: <a data-toggle="openMail" href="{LINK_EMAIL}">{LOG_COMMENT.note}</a> <!-- END: send_mail --> <!-- BEGIN: plain_text -->{LOG_COMMENT.note}<!-- END: plain_text -->
                                </td>
                                <td width="50px">
                                    <!-- BEGIN: edit --> <a href="{LOG_COMMENT.action}" title="{LANG.update}"> <i class="fa fa-edit fa-lg" aria-hidden="true"></i>
                                </a> <!-- END: edit -->
                                </td>
                            </tr>
                            <!-- END: loop -->
                        </tbody>
                    </table>
                </div>
                <!-- END: comment -->
            </div>
        </div>
    </div>
    <link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
    <script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
    <script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
    <script type="text/javascript">
    function unsubscribe() {
        $.post(script_name + '?' + nv_name_variable + '=crmbidding&' + nv_fc_variable + '=unsubscribe&nocache=' + new Date().getTime(), 'id={ROW.id}&type=1', function(res) {
            $('#unsubscribe').html(res);
            $('#unsubscribe .fa-angle-double-down').addClass('hidden');
            $('#unsubscribe .fa-angle-double-up').removeClass('hidden');
            $('#unsubscribe .loading').addClass('hidden');
        });
    }
    $(document).ready(function() {
        $('#required_business').addClass('hidden');
        $('.gr_business_id').hide();
        $('#required_solicitor').addClass('hidden');
        $('.gr_solicitor_id').hide();
        $("#business_id").select2({
            language: "vi",
            width: '100%',
            allowClear: true,
            placeholder: '{LANG.select_business}',
            ajax: {
            url: script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=add&get_bussiness=1',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q_select: params.term, // search term
                        type: 1
                    };
                },
                processResults: function (data, params) {
                    return {
                        results: data,
                        pagination: {
                            more: (params.page * 30) < data.total_count
                        }
                    };
                },
                cache: true
            },
            escapeMarkup: function (markup) { return markup; },
            minimumInputLength: 0,
            templateResult: formatRepo, // omitted for brevity, see the source of this page
            templateSelection: formatRepoSelection // omitted for brevity, see the source of this page
        });
        $("#solicitor_id").select2({
            language: "vi",
            width: '100%',
            allowClear: true,
            placeholder: '{LANG.select_solicitor}',
            ajax: {
            url: script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=add&get_bussiness=1',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q_select: params.term, // search term
                        type: 2
                    };
                },
                processResults: function (data, params) {
                    return {
                        results: data,
                        pagination: {
                            more: (params.page * 30) < data.total_count
                        }
                    };
                },
                cache: true
            },
            escapeMarkup: function (markup) { return markup; },
            minimumInputLength: 0,
            templateResult: formatRepo, // omitted for brevity, see the source of this page
            templateSelection: formatRepoSelectionS // omitted for brevity, see the source of this page
        });
        function formatRepo (repo) {
            if (repo.loading) {
                return "Loading....";
            }
            return repo.title;
        }

        function formatRepoSelection (repo) {
            return repo.title || "{LANG.select_business}";
        }
        function formatRepoSelectionS (repo) {
            return repo.title || "{LANG.select_solicitor}";
        }
        $('.uidatepicker').datepicker({
            showOn : "both",
            dateFormat : "dd/mm/yy",
            changeMonth : true,
            changeYear : true,
            showOtherMonths : true,
            buttonImage : nv_base_siteurl + "assets/images/calendar.gif",
            buttonImageOnly : true,
            maxDate : "+1m",
            minDate : "0d"
        });
        <!-- BEGIN: edit -->
        $('.editbtn').click();
        <!-- END: edit -->

        // Mở mail
        $('[data-toggle="openMail"]').on('click', function(e) {
            e.preventDefault();
            var w = 600;
            var h = 800;
            if (w > $(window).width()) {
                w = $(window).width() - 10;
            }
            if (h > $(window).height()) {
                h = $(window).height() - 10;
            }
            nv_open_browse($(this).attr('href'), 'NVImg', w, h, "resizable=no,scrollbars=no,toolbar=no,location=no,status=no");
        });
        // Xử lý xoá kí tự không phải số và dấu + trong mục điện thoại
        $('input[name="phone"]').on('change', function() {
            $(this).val($(this).val().replace(/[^0-9\+]/gm, ''));
        });
        $('input[name="sub_phone"]').on('change', function() {
            $(this).val($(this).val().replace(/[^0-9\+\,]/gm, ''));
        });
    });
    $('.editbtn').click(function() {
        $('.savebtn').removeClass('hidden');
        $('.deletebtn').removeClass('hidden');
        $('.editbtn').addClass('hidden');
        $('#required_business').removeClass('hidden');

        $('.inputinfo').removeClass('hidden');
        $('.textinfo').addClass('hidden');
        $("select[name='caregiver_id']").select2();
        $("select[name='affilacate_id']").select2();
    });
    $('.deletebtn').click(function() {
        $('#required_business').addClass('hidden');
        $('.savebtn').addClass('hidden');
        $('.deletebtn').addClass('hidden');
        $('.editbtn').removeClass('hidden');

        $('.inputinfo').addClass('hidden');
        $('.textinfo').removeClass('hidden');
        $("select[name='caregiver_id']").select2('destroy');
        $("select[name='affilacate_id']").select2('destroy');
    });
    $('select[name="source_leads"]').on('change', function() {
        if($('select[name="source_leads"]').val() == 2) {
            $('.gr_business_id').show();
        } else {
            $('.gr_business_id').hide();
        }
        if($('select[name="source_leads"]').val() == 15) {
            $('.gr_solicitor_id').show();
        } else {
            $('.gr_solicitor_id').hide();
        }
    });
    $('.btnusers').click(
        function() {
            $.post(script_name + '?' + nv_name_variable + '=crmbidding&'
                + nv_fc_variable + '=leads_info&nocache='
                + new Date().getTime(), 'id={ROW.id}&view_users=1',
                function(res) {
                    $('.modal-body').empty();
                    //modalShow('{LANG.opportunities_info_user}', res);
                    modalShow('{LANG.opportunities_info_user}', res, () => {}, () => {
                        location.reload();
                    });
                });
            return;
        });
    $('.btn_telepro').click( function() {
        $.post(script_name + '?' + nv_name_variable + '=crmbidding&' + nv_fc_variable + '=telepro&nocache=' + new Date().getTime(), 'id={ROW.teleproid}&showheader=0&phone={ROW.phone}&email={ROW.email}', function(res) {
            $('.modal-body').empty();
           // modalShow('{LANG.telepro}', res);
            modalShow('{LANG.telepro}', res, () => {}, () => {
                location.reload();
            });
        });
        return;
    });

    $('.btn-opportunities').click( function() {
        $.post(script_name + '?' + nv_name_variable + '='
            + nv_module_name + '&' + nv_fc_variable
            + '=opportunities_add&nocache=' + new Date().getTime(),
            'leadsid={ROW.id}', function(res) {
                //modalShow('{LANG.opportunities_add}', res);
                modalShow('{LANG.opportunities_add}', res, () => {}, () => {
                    location.reload();
                });
            });
        return;
    });

    $('.btn-label').click(function() {
        $.post(script_name + '?' + nv_name_variable + '='
            + nv_module_name + '&' + nv_fc_variable
            + '=label&nocache=' + new Date().getTime(),
            'leadsid={ROW.id}&showleads=1', function(res) {
                //modalShow('{LANG.menu_label}', res);
                modalShow('{LANG.menu_label}', res, () => {}, () => {
                    location.reload();
                });
            });
        return;
    });

    var is_show_duplicate = 1;
    $('.showleadsduplicate').click(function() {
        if ($('#info .content_duplicate').hasClass('in')) {
            $('#info .content_duplicate').removeClass('in');
            $('#duplicate .fa-angle-double-down').removeClass('hidden');
            $('#duplicate .fa-angle-double-up').addClass('hidden');
            if (is_show_duplicate == 1) {
                $('.content_duplicate').html('');
            } else {
                $('.content_duplicate').addClass('hidden');
            }
        } else {
            $('#info .content_duplicate').addClass('in');
            $('#duplicate .fa-angle-double-down').addClass('hidden');
            $('#duplicate .fa-angle-double-up').removeClass('hidden');
            if (is_show_duplicate == 1) {
                $('#duplicate .loading').removeClass('hidden');
                checkduplicate(1);
                is_show_duplicate  = 0;
            } else {
                $('.content_duplicate').removeClass('hidden');
            }
        }
        return;
    });

    $('.showleadsunsubscribe').click(function() {
        if ($('.sunsubscribe_collapse').length == 0) { 
            $('#unsubscribe .loading').removeClass('hidden');               
            unsubscribe();
        }
        return;
    });
    
    function checkduplicate(next) {
        $.post(script_name + '?' + nv_name_variable + '=crmbidding&' + nv_fc_variable + '=check_duplicate&nocache=' + new Date().getTime(), 'id={ROW.id}&type=1&next=' + next, function(res) {
            $('.content_duplicate').append(res.data);
            if (res.next == 5) {
                $('#duplicate .loading').addClass('hidden');
            } else {
                checkduplicate(res.next);
            }
        });
    }

    $('.btn-accept').click(function() {
        $.post(script_name + '?' + nv_name_variable + '='
            + nv_module_name + '&' + nv_fc_variable
            + '=leads_info&nocache='
            + new Date().getTime(), 'id={ROW.id}&accept=1',
            function(res) {
                if (res == 'OK') {
                    window.location.href = script_name + '?'
                    + nv_name_variable + '=crmbidding&'
                    + nv_fc_variable
                    + '=leads_info&id={ROW.id}';
                } else {
                    alert(res);
                }
            });
        return;
    });

</script>
    <!-- BEGIN: selected_edit -->
    <script type="text/javascript">
    $(document).ready(function() {
        $("#business_id").select2("trigger", "select", {
            data: { id: "{BUSINESS_EDIT.id}", title: "{BUSINESS_EDIT.companyname}" }
        });
    });
</script>
    <!-- END: selected_edit -->
    <!-- BEGIN: selected_sol_edit -->
    <script type="text/javascript">
    $(document).ready(function() {
        $("#solicitor_id").select2("trigger", "select", {
            data: { id: "{SOLICITOR_EDIT.id}", title: "{SOLICITOR_EDIT.title}" }
        });
    });
</script>
    <!-- END: selected_sol_edit -->
    <!-- BEGIN: business_id -->
    <script type="text/javascript">
    $(document).ready(function() {
        $('.gr_business_id').show();
    });
</script>
    <!-- END: business_id -->
    <!-- BEGIN: solicitor_id -->
    <script type="text/javascript">
    $(document).ready(function() {
        $('.gr_solicitor_id').show();
    });
</script>
    <!-- END: solicitor_id -->
    <div class="col-md-10">
        <div class="panel panel-default">
            <div class="panel-body bidding-form" id="info">
                <div class="col-md-24 col-sm-24" id="duplicate">
                    <h2 class="text-info showleadsduplicate">
                        <a href="#">{LANG.leads_duplicate} ({LANG.click_view}) <i class="fa fa-angle-double-down"></i> <i class="fa fa-angle-double-up hidden"></i></a>
                    </h2>
                    <hr>
                    <div class="form-group text-center loading hidden">
                        <i class="fa fa-spinner fa-spin" aria-hidden="true"></i>
                    </div>
                    <div class="form-group content_duplicate"></div>
                </div>
                <div class="col-md-24 col-sm-24" id="unsubscribe">
                    <h2 class="text-info showleadsunsubscribe">
                        <a href="#">{LANG.leads_unsubscribe} <i class="fa fa-angle-double-down"></i> <i class="fa fa-angle-double-up hidden"></i></a>
                    </h2>
                    <hr>
                    <div class="form-group text-center loading hidden">
                        <i class="fa fa-spinner fa-spin" aria-hidden="true"></i>
                    </div>
                </div>
                <div class="col-md-24 col-sm-24">
                    <h2 class="text-info">{LANG.update_leads_history}</h2>
                    <hr>
                    <div class="table-responsive log_history">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th class="text-nowrap w100">{LANG.log_time}</th>
                                    <th class="text-nowrap">{LANG.log_user}</th>
                                    <th class="text-nowrap">{LANG.log_data}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- BEGIN: logall -->
                                <tr>
                                    <td>{LOG.log_time}</td>
                                    <td>{LOG.user}</td>
                                    <td>
                                        <!-- BEGIN: data -->
                                        <div>
                                            <!-- BEGIN: sarray -->
                                            {LOG_DATA_SHOW.0} <strong>{LOG_DATA_SHOW.1}</strong>
                                            <!-- END: sarray -->
                                            <!-- BEGIN: sstring -->
                                            {LOG_DATA_SHOW}
                                            <!-- END: sstring -->
                                            <!-- BEGIN: other1 -->
                                            &nbsp; <a data-toggle="collapse" href="#logOther{LOG.id}" aria-expanded="false" aria-controls="logOther{LOG.id}"><i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
                                            <!-- END: other1 -->
                                        </div> <!-- BEGIN: other -->
                                        <div class="collapse" id="logOther{LOG.id}">
                                            <ul class="logotherlists">
                                                <!-- BEGIN: loop -->
                                                <li>
                                                    <!-- BEGIN: sarray --> {LOG_DATA_OTHER.0} <strong>{LOG_DATA_OTHER.1}</strong> <!-- END: sarray --> <!-- BEGIN: sstring --> {LOG_DATA_OTHER} <!-- END: sstring -->
                                                </li>
                                                <!-- END: loop -->
                                            </ul>
                                        </div> <!-- END: other --> <!-- END: data -->
                                    </td>
                                </tr>
                                <!-- END: logall -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-md-24 col-sm-24">
                    <p class="text-danger">
                        <b>Giải thích ý nghĩa các trạng thái leads:</b>
                    </p>
                    <ul>
                        <li><b>Leads mới</b>: leads được hệ thống tạo tự động.</li>
                        <li><b>Chưa có người chăm sóc</b>: các leads được sales không chăm sóc nhả ra. Mặc định hệ thống sẽ k cho phép sale nhả leads</li>
                        <li><b>Đang chăm sóc</b>: Sale đang chăm sóc. Trạng thái này hệ thống tự ghi nhận</li>
                        <li><b>Đã chuyển thành cơ hội kinh doanh</b>: Leads được chuyển lên cơ hội. Trạng thái này tự động.</li>
                        <li><b>Không có nhu cầu</b>: khách k có nhu cầu. sale tự set trạng thái này.</li>
                    </ul>
                </div>
                <div class="col-md-24 col-sm-24">
                    <p class="text-danger">
                        <b>Cách nhận leads</b>
                    </p>
                    <ul>
                        <li>Click nút <b>Nhận leads</b><i class="fa fa-check-circle"></i></li>
                        <li>Sửa người chăm sóc về mình</li>
                        <li>Comment nội dung chăm sóc</li>
                    </ul>
                </div>
                <div class="col-md-24 col-sm-24">
                    <p class="text-danger">
                        <b>Điều kiện convert thành cơ hội kinh doanh</b>
                    </p>
                    <ul>
                        <li>Phải nhận leads, leads chuyển về người chăm sóc</li>
                        <li>Lead cần có comment (ghi chú) nội dung chăm khách</li>
                        <li>Lead cần có số điện thoại</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: main -->
