<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_url = $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
$array_search = [];
$where = [];

$array_search['type'] = $nv_Request->get_title('type', 'get', 'pending');
$array_search['refund_status'] = $nv_Request->get_title('refund_status', 'get', 0);
$curent_from = date('d-m-Y', strtotime('first day of this month'));
$curent_to = date('d-m-Y', strtotime('now'));
$array_search['time_from'] = $nv_Request->get_title('time_from', 'get', $curent_from);
$array_search['time_to'] = $nv_Request->get_title('time_to', 'get', $curent_to);
$array_search['u_time_from'] = nv_d2u_get($array_search['time_from']);
$array_search['u_time_to'] = nv_d2u_get($array_search['time_to'], 23, 59, 59);

$base_url .= '&amp;type=' . $array_search['type'];

$where['AND'][] = [
    '>' => [
        'refund_status' => 0
    ]
];
$where['AND'][] = [
    '=' => [
        'delete_time' => 0
    ]
];

if ($array_search['type'] == 'pending') {
    $where['AND'][] = [
        'IN' => [
            'refund_status' => '(' . RefundStatus::Open->value .',' . RefundStatus::Process->value . ')'
        ]
    ];
} else {
    $where['AND'][] = [
        'IN' => [
            'refund_status' => '(' . RefundStatus::Accept->value .',' . RefundStatus::Refuse->value . ')'
        ]
    ];
}

if ($array_search['u_time_from'] > 0) {
    $base_url .= '&amp;time_from=' . urlencode($array_search['time_from']);
    $where['AND'][] = [
        '>=' => [
            'rating_add_time' => $array_search['u_time_from']
        ]
    ];
}

if ($array_search['u_time_to'] > 0) {
    $base_url .= '&amp;time_to=' . urlencode($array_search['time_to']);
    $where['AND'][] = [
        '<=' => [
            'rating_add_time' => $array_search['u_time_to']
        ]
    ];
}

if ($array_search['refund_status'] > 0) {
    $base_url .= '&amp;refund_status=' . $array_search['refund_status'];
    $where['AND'][] = [
        '=' => [
            'refund_status' => $array_search['refund_status']
        ]
    ];
}

// Fetch Limit
$per_page = 20;
$page = $nv_Request->get_int('page', 'post,get', 1);

$params = [
    'page' => $page,
    'perpage' => $per_page
];

// Nếu có điều kiện where thì gán
if (!empty($where)) {
    $params['where'] = $where;
}

// GỌI API
$List = nv_local_api('ListAllComment', $params, $admin_info['username'], $module_name);
$ListAllComment = json_decode($List, true);
$data = [];
$generate_page = '';
if ($ListAllComment['status'] == 'success') {
    foreach ($ListAllComment['data'] as $view) {
        $view['link_detail'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail_paid&amp;id=' . $view['ticket_id'];
        $view['refund_status'] = RefundStatus::tryFrom(intval($view['refund_status']))?->getLabel();
        $view['reply_user'] = $view['reply_userid'] == 0 ? $nv_Lang->getModule('role_ai') : get_user_info($view['reply_userid'])['fullname'];
        $view['rating_time'] = ($view['rating_edit_time'] > 0) ? nv_datetime_format($view['rating_edit_time']) : nv_datetime_format($view['rating_add_time']);
        $data[] = $view;
    }
    $generate_page = nv_generate_page($base_url, $ListAllComment['total'], $ListAllComment['perpage'], $ListAllComment['page']);
}

$template = get_tpl_dir([$global_config['module_theme'], $global_config['admin_theme']], 'admin_default', '/modules/' . $module_file . '/refunds.tpl');
$tpl = new \NukeViet\Template\NVSmarty();
$tpl->registerPlugin('modifier', 'nformat', 'nv_number_format');
$tpl->registerPlugin('modifier', 'dformat', 'nv_datetime_format');
$tpl->setTemplateDir(NV_ROOTDIR . '/themes/' . $template . '/modules/' . $module_file);
$tpl->assign('LANG', $nv_Lang);
$tpl->assign('MODULE_NAME', $module_name);
$tpl->assign('OP', $op);
$tpl->assign('SEARCH', $array_search);
$tpl->assign('COMMENTS', $data);
$tpl->assign('PAGINATION', $generate_page);
$tpl->assign('TAB_PENDING', [
    'url' => $page_url . '&amp;type=pending',
    'active' => $array_search['type'] == 'pending' ? 'active' : '',
]);
$tpl->assign('TAB_DONE', [
    'url' => $page_url . '&amp;type=done',
    'active' => $array_search['type'] == 'done' ? 'active' : '',
]);
$tpl->assign('URL', $base_url);
$array_refund_status = RefundStatus::cases();
$tpl->assign('REFUND_STATUS', $array_refund_status);

$page_title = $nv_Lang->getModule('refund_request');
$contents = $tpl->fetch('refunds.tpl');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
