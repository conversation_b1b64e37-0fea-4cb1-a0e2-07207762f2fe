<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\elink\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdatePromotionStatistics implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'stat';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $global_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        // Các site thanh toán
        $sql = "SELECT * FROM " . $db_config['prefix'] . "_wallet_sites ORDER BY weight ASC";
        $global_array_sites = $nv_Cache->db($sql, 'id', 'wallet');

        $array = [];
        $array['site_id'] = $nv_Request->get_absint('site_id', 'post', 0);
        $array['code'] = $nv_Request->get_title('code', 'post', '');
        $array['selfurl'] = $nv_Request->get_title('selfurl', 'post', '');
        $array['referer'] = $nv_Request->get_title('referer', 'post', '');
        $array['time'] = $nv_Request->get_absint('time', 'post', 0) ?: NV_CURRENTTIME;
        $array['ip'] = $nv_Request->get_title('ip', 'post', '');
        $array['stat_link'] = (int) $nv_Request->get_bool('stat_link', 'post', false);
        $array['stat_referer'] = (int) $nv_Request->get_bool('stat_referer', 'post', false);

        if (empty($array['code']) or !preg_match('/^([a-zA-Z0-9\-\_]+)$/', $array['code'])) {
            return $this->result->setCode('1001')->setMessage($nv_Lang->getModule('apierror_procode'))->getResult();
        }
        if (!empty($array['site_id']) and !isset($global_array_sites[$array['site_id']])) {
            return $this->result->setCode('1002')->setMessage($nv_Lang->getModule('apierror_site_exists'))->getResult();
        }
        if (empty($array['stat_link']) and empty($array['stat_referer'])) {
            return $this->result->setCode('1003')->setMessage($nv_Lang->getModule('apierror_no_stat'))->getResult();
        }
        if ($array['stat_link'] and empty($array['selfurl'])) {
            return $this->result->setCode('1004')->setMessage($nv_Lang->getModule('apierror_selfurl_empty'))->getResult();
        }
        if ($array['stat_referer'] and empty($array['referer'])) {
            return $this->result->setCode('1005')->setMessage($nv_Lang->getModule('apierror_referer_empty'))->getResult();
        }

        // Kiểm tra mã khuyến mãi
        $userid = $db->query("SELECT userid FROM " . $db_config['prefix'] . "_elink_promotion_code WHERE promo_code=" . $db->quote($array['code']))->fetchColumn();
        if (empty($userid)) {
            return $this->result->setCode('1004')->setMessage($nv_Lang->getModule('apierror_procode_exists'))->getResult();
        }

        $client_url_key = md5(nv_strtolower($array['selfurl']));
        $client_referer_key = md5(nv_strtolower($array['referer']));

        // Lưu và thống kê link đích truy cập Promotion Code
        if ($array['stat_link']) {
            try {
                $sql = "UPDATE " . $db_config['prefix'] . "_elink_statistics SET scount=scount+1, last_update=" . $array['time'] . "
                WHERE stype=1 AND sval=" . $db->quote($array['code']) . " AND islink=" . $db->quote($client_url_key) . ' AND referornot=0';
                if (!$db->exec($sql)) {
                    $sql = "INSERT INTO " . $db_config['prefix'] . "_elink_statistics (
                        site_id, userid, stype, sval, islink, referornot, slink, sreflink, scount, last_update, access_ip
                    ) VALUES (
                        " . $array['site_id'] . ", " . $userid . ", 1, " . $db->quote($array['code']) . ", " . $db->quote($client_url_key) . ", 0,
                        " . $db->quote($array['selfurl']) . ", " . $db->quote($array['referer']) . ", 1, " . $array['time'] . ",
                        " . $db->quote($array['ip']) . "
                    )";
                    $db->query($sql);
                }
            } catch (\Exception $e) {
                trigger_error(print_r($e, true));
            }
        }

        // Lưu và thống kê link nguồn truy cập Promotion Code
        if ($array['stat_referer']) {
            try {
                $sql = "UPDATE " . $db_config['prefix'] . "_elink_statistics SET scount=scount+1, last_update=" . $array['time'] . "
                WHERE stype=1 AND sval=" . $db->quote($array['code']) . " AND islink=" . $db->quote($client_referer_key) . ' AND referornot=1';
                if (!$db->exec($sql)) {
                    $sql = "INSERT INTO " . $db_config['prefix'] . "_elink_statistics (
                        site_id, userid, stype, sval, islink, referornot, slink, sreflink, scount, last_update, access_ip
                    ) VALUES (
                        " . $array['site_id'] . ", " . $userid . ", 1, " . $db->quote($array['code']) . ", " . $db->quote($client_referer_key) . ", 1,
                        " . $db->quote($array['selfurl']) . ", " . $db->quote($array['referer']) . ", 1, " . $array['time'] . ",
                        " . $db->quote($array['ip']) . "
                    )";
                    $db->query($sql);
                }
            } catch (\Exception $e) {
                trigger_error(print_r($e, true));
            }
        }

        // Số truy cập chung cho mã giới thiệu
        try {
            $sql = "UPDATE " . $db_config['prefix'] . "_elink_statistics SET scount=scount+1, last_update=" . $array['time'] . "
            WHERE stype=1 AND sval=" . $db->quote($array['code']) . " AND islink='' AND referornot=0";
            if (!$db->exec($sql)) {
                $sql = "INSERT INTO " . $db_config['prefix'] . "_elink_statistics (
                    site_id, userid, stype, sval, islink, slink, referornot, scount, last_update, access_ip
                ) VALUES (
                    " . $array['site_id'] . ", " . $userid . ", 1, " . $db->quote($array['code']) . ", '', '', 0, 1, " . $array['time'] . ",
                    " . $db->quote($array['ip']) . "
                )";
                $db->query($sql);
            }
        } catch (\Exception $e) {
            trigger_error(print_r($e, true));
        }

        $this->result->set('data', [
            'stat_link' => $array['stat_link'],
            'stat_referer' => $array['stat_referer']
        ]);
        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
