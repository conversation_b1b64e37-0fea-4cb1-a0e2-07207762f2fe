<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript">
    $(document).ready(function($) {
        $("select[name='assignee']").select2();
    });
    $(document).ready(function($) {
        $("select[name='add_user']").select2();
    });
</script>

<ul class="nav nav-tabs nav-pills">
    <li role="presentation" class="{TAB_NEW}">
        <a href="{URL_NEW}">{LANG.new_request}</a>
    </li>
    <li role="presentation" class="{TAB_PROCESS}">
        <a href="{URL_PROCESS}">{LANG.process_request}</a>
    </li>
    <li role="presentation" class="{TAB_DONE}">
        <a href="{URL_DONE}">{LANG.done_request}</a>
    </li>
    <!-- BEGIN: can_delete -->
    <li role="presentation" class="{TAB_DELETED}">
        <a href="{URL_DELETED}">{LANG.ticket_deleted}</a>
    </li>
    <!-- END: can_delete -->
</ul>

<div class="well">
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline">
        <input type="hidden" name="{NV_LANG_VARIABLE}"  value="{NV_LANG_DATA}" />
        <input type="hidden" name="{NV_NAME_VARIABLE}"  value="{MODULE_NAME}" />
        <input type="hidden" name="{NV_OP_VARIABLE}"  value="{OP}" />
        <input type="hidden" name="type" value="{SEARCH.type}" />
        <div class="row">
            <div class="form-group margintop10">
                <a class="btn btn-primary" href="{LINK_ADD}" title="{LANG.add}"><i class="fa fa-plus"></i></a>
            </div>
            <div class="form-group margintop10">
                <input class="form-control" type="text" value="{SEARCH.q}" name="q" maxlength="255" placeholder="{LANG.search_title}" />
            </div>
            <div class="form-group margintop10" style="min-width: 300px;">
                <select id="element_customer_id" name="customer" class="form-control">
                    <option value="0">{LANG.customer_any}</option>
                    <!-- BEGIN: select_customer -->
                    <option value="{CUSTOMER.userid}" selected="selected">{CUSTOMER.title}</option>
                    <!-- END: select_customer -->
                </select>
            </div>
            <div class="form-group margintop10" style="min-width: 120px; max-width: 500px;">
                <select class="form-control" name="cat">
                    <option value="0">{LANG.search_cat}</option>
                    <!-- BEGIN: select_cat_id -->
                    <option value="{OPTION.key}" {OPTION.selected}>{OPTION.title}</option>
                    <!-- END: select_cat_id -->
                </select>
            </div>
            <div class="form-group margintop10" style="min-width: 120px; max-width: 250px;">
                <select class="form-control" name="label" style="width: 100%;">
                    <option value="-1">{LANG.search_label}</option>
                    <!-- BEGIN: select_label_ids -->
                    <option value="{OPTION.key}" {OPTION.selected}>{OPTION.title}</option>
                    <!-- END: select_label_ids -->
                </select>
            </div>
            <div class="form-group margintop10" style="min-width: 250px; max-width: 350px;">
                <select class="form-control" name="add_user" style="width: 100%;">
                    <option value="-1">{LANG.search_add_user}</option>
                    <!-- BEGIN: select_add_user -->
                    <option value="{OPTION_ADD.key}" {OPTION_ADD.selected_add}>{OPTION_ADD.title}</option>
                    <!-- END: select_add_user -->
                </select>
            </div>
            <!-- BEGIN: assignee_user -->
            <div class="form-group margintop10" style="min-width: 250px; max-width: 350px;">
                <select class="form-control" name="assignee" style="width: 100%;">
                    <option value="-1">{LANG.search_assignee}</option>
                    <!-- BEGIN: select_assignee -->
                    <option value="{OPTION_ASSIGN.key}" {OPTION_ASSIGN.selected}>{OPTION_ASSIGN.title}</option>
                    <!-- END: select_assignee -->
                </select>
            </div>
            <!-- END: assignee_user -->
            <!-- BEGIN: type -->
            <div class="form-group margintop10" style="min-width: 120px; max-width: 250px;">
                <select class="form-control" name="status" style="width: 100%">
                    <option value="0">{LANG.search_status}</option>
                    <!-- BEGIN: select_status -->
                    <option value="{OPTION.key}" {OPTION.selected}>{OPTION.title}</option>
                    <!-- END: select_status -->
                </select>
            </div>
            <!-- END: type -->
            <div class="form-group margintop10" id="btn_advance_search">
                <a class="btn btn-primary" href="#" onclick="show_advance_search();" title="{LANG.advance_search}"><i class="fa fa-ellipsis-h"></i></a>
            </div>
            <div class="form-group margintop10">
                <input class="btn btn-primary" type="submit" value="{LANG.search_submit}" />
            </div>
        </div>
        <div class="row hidden" id="advance_search">
            <div class="form-group margintop10">
                <select class="form-control" name="time_type">
                    <option value="1"{edit_time_selected}>{LANG.search_type_edit_time}</option>
                    <option value="2"{add_time_selected}>{LANG.search_type_add_time}</option>
                </select>
            </div>
            <div class="form-group margintop10">
                <label>{LANG.time_from}:</label> <input class="form-control w100 uidatepicker" type="text" value="{SEARCH.time_from}" name="time_from" maxlength="10" autocomplete="off">
            </div>
            <div class="form-group margintop10">
                <label>{LANG.time_to}:</label> <input class="form-control w100 uidatepicker" type="text" value="{SEARCH.time_to}" name="time_to" maxlength="10" autocomplete="off">
            </div>
        </div>
    </form>
</div>
<!-- BEGIN: view -->
<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th class="text-center">{LANG.ticket_id}</th>
                    <th class="w500">{LANG.ticket_title}</th>
                    <th>{LANG.cat_id}</th>
                    <th>{LANG.status}</th>
                    <th>{LANG.label_ids}</th>
                    <th>{LANG.assignee_to}</th>
                    <th>{LANG.add_time} /<br/>{LANG.edit_time}</th>
                </tr>
            </thead>
            <!-- BEGIN: generate_page -->
            <tfoot>
                <tr>
                    <td class="text-center" colspan="12">{NV_GENERATE_PAGE}</td>
                </tr>
            </tfoot>
            <!-- END: generate_page -->
            <tbody>
                <!-- BEGIN: loop -->
                <tr id="ticket-{VIEW.id}">
                    <td class="text-center"><a href="{VIEW.link_detail}">#{VIEW.id}</a> </td>
                    <td> <a href="{VIEW.link_detail}">{VIEW.title}</a> </td>
                    <td> {VIEW.cat_title} </td>
                    <td> {VIEW.status_display} </td>
                    <td><span style="background-color: {VIEW.label_color}" class="label label-default">{VIEW.label}</span></td>
                    <td> {VIEW.assignee} </td>
                    <td> {VIEW.add_time}<br/>{VIEW.edit_time} </td>
                    <!-- BEGIN: can_delete -->
                    <td><em class="fa fa-trash-o fa-lg"></em> <a href="javascript:;" onclick="delete_ticket('{LINK_DELETE}', 'Bạn có chắc chắn muốn xóa yêu cầu này không?');">{LANG.delete}</a></td>
                    <!-- END: can_delete -->
                    <!-- BEGIN: can_restore -->
                    <td><em class="fa fa-clock-o fa-lg"></em> <a href="javascript:;" onclick="restore_ticket('{LINK_RESTORE}', 'Bạn có muốn khôi phục lại yêu cầu đã xóa?');">{LANG.restore}</a></td>
                    <!-- END: can_restore -->
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
</form>
<!-- END: view -->
<script>
    function show_advance_search() {
        if ($("#advance_search").hasClass("hidden")) {
            $("#advance_search").removeClass("hidden");
        } else {
            $("#advance_search").addClass("hidden");
        }
    }
    $(document).ready(function() {
        $('.uidatepicker').datepicker({ showOn : "both", dateFormat : "dd/mm/yy", changeMonth : true, changeYear : true, showOtherMonths : true, buttonImage : nv_base_siteurl + "assets/images/calendar.gif", buttonImageOnly : true });

        // Lấy thông tin khách hàng
        $('#element_customer_id').select2({
            width: '100%',
            language: '{NV_LANG_INTERFACE}',
            ajax: {
                delay: 250,
                cache: false,
                type: 'POST',
                url: script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '={OP}&nocache=' + new Date().getTime(),
                dataType: 'json',
                data: function (params) {
                    return {
                        q: params.term,
                        ajax_get_customer: '{NV_CHECK_SESSION}',
                        page: params.page || 1
                    };
                }
            },
            placeholder: '{LANG.customer_pick}',
            minimumInputLength: 2
        });
    });
</script>
<!-- END: main -->
