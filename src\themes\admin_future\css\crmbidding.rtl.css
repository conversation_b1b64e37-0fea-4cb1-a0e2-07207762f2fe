/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */
.w50 {
  width: 50px;
}

.w160 {
  width: 160px;
}

.w120 {
  width: 120px;
}

.link_manager {
  margin-bottom: 2px;
}

.btn__downleft i {
  transition: all 0.3s;
}

.iconDown {
  transform: rotate(0);
}

.textinfo {
  overflow-wrap: break-word;
}

.timeline {
  list-style: none;
  padding: 20px 0 20px;
  position: relative;
}

.timeline:before {
  top: 0;
  bottom: 0;
  position: absolute;
  content: " ";
  width: 3px;
  background-color: #eeeeee;
  right: 50%;
  margin-right: -1.5px;
}

.timeline > li {
  /* margin-bottom: 20px; */
  position: relative;
  transition: 0.5s;
}

.timeline > li:before,
.timeline > li:after {
  content: " ";
  display: table;
}

.timeline > li:after {
  clear: both;
}

.timeline > li:before,
.timeline > li:after {
  content: " ";
  display: table;
}

.timeline > li:after {
  clear: both;
}

.timeline > li > .timeline-panel {
  width: 50%;
  float: right;
  border: 1px solid rgba(212, 212, 212, 0.1294117647);
  border-radius: 2px;
  padding: 20px;
  position: relative;
  /* -webkit-box-shadow: 0 1px 6px rgb(0 0 0 / 18%); */
  /* box-shadow: 0 1px 6px rgb(0 0 0 / 18%); */
  background: rgba(230, 233, 241, 0.2784313725);
  border-left: none;
  border-right: none;
}

.timeline > li.timeline-inverted + li:not(.timeline-inverted),
.timeline > li:not(.timeline-inverted) + li.timeline-inverted {
  margin-top: -16px;
}

.timeline > li.not_display {
  /* margin-top: 10px !important; */
}

.timeline > li:not(.timeline-inverted) {
  padding-left: 90px;
}

.timeline > li.timeline-inverted {
  padding-right: 90px;
}

.timeline > li > .timeline-panel:before {
  position: absolute;
  top: 26px;
  left: -15px;
  display: inline-block;
  border-top: 15px solid transparent;
  border-right: 15px solid rgba(212, 212, 212, 0.1294117647);
  border-left: 0 solid rgba(212, 212, 212, 0.1294117647);
  border-bottom: 15px solid transparent;
  content: " ";
}

.timeline > li > .timeline-panel:after {
  position: absolute;
  top: 27px;
  left: -14px;
  display: inline-block;
  border-top: 14px solid transparent;
  border-right: 14px solid rgba(212, 212, 212, 0.1294117647);
  border-left: 0 solid rgba(212, 212, 212, 0.1294117647);
  border-bottom: 14px solid transparent;
  content: " ";
}

.timeline > li > .timeline-badge {
  color: #fff;
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 1.4em;
  text-align: center;
  position: absolute;
  top: 16px;
  right: 51%;
  margin-right: -30px;
  background-color: #999999;
  z-index: 100;
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
  border-bottom-left-radius: 50%;
  border-bottom-right-radius: 50%;
}

.timeline > li.timeline-inverted > .timeline-panel {
  float: left;
}

.timeline > li.timeline-inverted > .timeline-panel:before {
  border-right-width: 0;
  border-left-width: 15px;
  right: -15px;
  left: auto;
}

.timeline > li.timeline-inverted > .timeline-panel:after {
  border-right-width: 0;
  border-left-width: 14px;
  right: -14px;
  left: auto;
}

.timeline-badge.primary {
  background-color: #2e6da4 !important;
}

.timeline-badge.success {
  background-color: #3f903f !important;
}

.timeline-badge.warning {
  background-color: #f0ad4e !important;
}

.timeline-badge.danger {
  background-color: #d9534f !important;
}

.timeline-badge.info {
  background-color: #5bc0de !important;
}

.timeline-title {
  margin-top: 0;
  color: inherit;
}

.timeline-body > p,
.timeline-body > ul {
  margin-bottom: 0;
}

.timeline-body > p + p {
  margin-top: 5px;
}

.panel__htkh {
  /* box-shadow: 0px 4px 10px 3px #ccc; */
  border: 1px solid #d9d9d9;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
}

.panel__htkh .panel-heading {
  color: #424242;
  background-color: #dde4f5;
  border-color: #dde4f5;
  cursor: pointer;
}

#info {
  /* padding: 9px; */
  /* border: 1px solid #ffa8a8; */
  /* max-height: 700px; */
  /* overflow-y: scroll; */
}

.timeline-title {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

#info::-webkit-scrollbar-track,
.info_product::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f5f5f5;
}

#info::-webkit-scrollbar,
.info_product::-webkit-scrollbar {
  width: 3px;
  background-color: #f5f5f5;
}

#info::-webkit-scrollbar-thumb,
.info_product::-webkit-scrollbar-thumb {
  background-color: #f79633;
}

#info .header {
  overflow: hidden;
  padding: 20px 0;
}

#info .show_log {
  margin-bottom: 0;
  display: inline-block;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

#info .show_log a span {
  padding: 8px;
  /* background: whitesmoke; */
}

.hidentime {
  /* top: -16px !important; */
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  margin-top: 10px;
  font-size: 15px;
  text-align: center;
  width: 100%;
  display: flex;
  justify-content: center;
}

.li__time_custom {
  z-index: 9999;
  padding-left: 0 !important;
  width: 100%;
}

.li__time_custom:nth-child(1) i {
  margin-bottom: 5px;
}

.li__time_custom i {
  padding: 10px;
  font-size: 18px;
  background: #ebebeb;
  border-radius: 50%;
}

.not_display1 {
  display: none;
}

.timeline .showtimeline {
  display: block;
  animation: 0.6s fadeIn linear;
}

.timeline .showtimeline .timeline-panel {
  background: rgba(253, 243, 243, 0.4901960784);
}

.timeline-title .fa-ellipsis-h {
  font-size: 19px;
  margin-right: 8px;
  margin-top: 6px;
}

.btn--social {
  position: relative;
  text-decoration: none;
  text-transform: uppercase;
  font-family: sans-serif;
  overflow: hidden;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
  border: none;
  margin-bottom: 5px;
  display: inline-flex;
  align-items: center;
  outline: none !important;
}

.btn--social i {
  margin-left: 4px;
}

.btn--social:before {
  content: "";
  position: absolute;
  top: 0;
  right: -15px;
  width: 50%;
  height: 100%;
  transform: skew(-46deg);
  background: rgba(255, 255, 255, 0.2);
}

.btn--social:after {
  content: "";
  position: absolute;
  top: 0;
  right: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(-90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: 0.5s;
}

.btn--social:hover:after {
  right: 100%;
  border: none;
}

.support {
  max-height: 500px;
  overflow-x: hidden;
  padding: 17px 20px;
}

.support::-webkit-scrollbar-track,
.info_product::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f5f5f5;
}

.support::-webkit-scrollbar,
.info_product::-webkit-scrollbar {
  width: 3px;
  background-color: #f5f5f5;
}

.support::-webkit-scrollbar-thumb,
.info_product::-webkit-scrollbar-thumb {
  background-color: #f79633;
}

.row__support {
  padding: 16px 10px;
  border: 1px solid rgba(204, 204, 204, 0.4509803922);
  margin-bottom: 20px;
  position: relative;
  transition: transform 0.6s;
  animation: fadeIn linear 1s;
}

.support__stt {
  position: absolute;
  top: -6px;
  right: -6px;
  height: 20px;
  width: 20px;
  background: #fdf500;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  border-radius: 2px;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
}

.support__action_edit {
  position: absolute;
  top: -11px;
  left: -3px;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
  padding: 4px 5px 4px 0px;
  width: auto;
}
/*# sourceMappingURL=crmbidding.rtl.css.map */