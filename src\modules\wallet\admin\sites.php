<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('site_manager');

// Thay đổi thứ tự
if ($nv_Request->get_title('changeweight', 'post', '') === NV_CHECK_SESSION) {
    $id = $nv_Request->get_absint('id', 'post', 0);
    $new_weight = $nv_Request->get_absint('new_weight', 'post', 0);

    // Kiểm tra tồn tại
    $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_sites WHERE id=" . $id;
    $array = $db->query($sql)->fetch();
    if (empty($array)) {
        nv_htmlOutput('NO_' . $id);
    }
    if (empty($new_weight)) {
        nv_htmlOutput('NO_' . $id);
    }

    $sql = "SELECT id FROM " . $db_config['prefix'] . "_" . $module_data . "_sites WHERE id!=" . $id . " ORDER BY weight ASC";
    $result = $db->query($sql);

    $weight = 0;
    while ($row = $result->fetch()) {
        ++$weight;
        if ($weight == $new_weight) {
            ++$weight;
        }
        $sql = "UPDATE " . $db_config['prefix'] . "_" . $module_data . "_sites SET weight=" . $weight . " WHERE id=" . $row['id'];
        $db->query($sql);
    }

    $sql = "UPDATE " . $db_config['prefix'] . "_" . $module_data . "_sites SET weight=" . $new_weight . " WHERE id=" . $id;
    $db->query($sql);

    nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_CHANGE_WEIGHT_site', json_encode($array), $admin_info['admin_id']);
    $nv_Cache->delMod($module_name);
    nv_htmlOutput('OK_' . $id);
}

// Thay đổi hoạt động
if ($nv_Request->get_title('changestatus', 'post', '') === NV_CHECK_SESSION) {
    $id = $nv_Request->get_absint('id', 'post', 0);

    // Kiểm tra tồn tại
    $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_sites WHERE id=" . $id;
    $array = $db->query($sql)->fetch();
    if (empty($array)) {
        nv_htmlOutput('NO_' . $id);
    }

    $status = empty($array['status']) ? 1 : 0;

    $sql = "UPDATE " . $db_config['prefix'] . "_" . $module_data . "_sites SET status = " . $status . " WHERE id = " . $id;
    $db->query($sql);

    nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_CHANGE_STATUS_site', json_encode($array), $admin_info['admin_id']);
    $nv_Cache->delMod($module_name);

    nv_htmlOutput("OK");
}

// Xóa
if ($nv_Request->get_title('delete', 'post', '') === NV_CHECK_SESSION) {
    $id = $nv_Request->get_absint('id', 'post', 0);

    // Kiểm tra tồn tại
    $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_sites WHERE id=" . $id;
    $array = $db->query($sql)->fetch();
    if (empty($array)) {
        nv_htmlOutput('NO_' . $id);
    }

    // Xóa
    $sql = "DELETE FROM " . $db_config['prefix'] . "_" . $module_data . "_sites WHERE id=" . $id;
    $db->query($sql);

    // Cập nhật thứ tự
    $sql = "SELECT id FROM " . $db_config['prefix'] . "_" . $module_data . "_sites ORDER BY weight ASC";
    $result = $db->query($sql);
    $weight = 0;

    while ($row = $result->fetch()) {
        ++$weight;
        $sql = "UPDATE " . $db_config['prefix'] . "_" . $module_data . "_sites SET weight=" . $weight . " WHERE id=" . $row['id'];
        $db->query($sql);
    }

    nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_DELETE_site', json_encode($array), $admin_info['admin_id']);
    $nv_Cache->delMod($module_name);

    nv_htmlOutput("OK");
}

$array = $error = [];
$is_submit_form = $is_edit = false;
$id = $nv_Request->get_absint('id', 'get', 0);

if (!empty($id)) {
    $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_sites WHERE id = " . $id;
    $result = $db->query($sql);
    $array = $result->fetch();

    if (empty($array)) {
        nv_info_die($nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_content'));
    }

    $is_edit = true;
    $caption = $nv_Lang->getModule('site_edit');
    $form_action = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $id;
} else {
    $array = [
        'id' => 0,
        'title' => '',
        'description' => '',
        'api_key' => '',
        'api_secret' => '',
    ];

    $caption = $nv_Lang->getModule('site_add');
    $form_action = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
}

if ($nv_Request->get_title('save', 'post', '') === NV_CHECK_SESSION) {
    $is_submit_form = true;
    $array['title'] = nv_substr($nv_Request->get_title('title', 'post', ''), 0, 190);
    $array['description'] = $nv_Request->get_string('description', 'post', '');
    $array['sitedomain'] = nv_substr($nv_Request->get_title('sitedomain', 'post', ''), 0, 190);
    $array['adminurl'] = nv_substr($nv_Request->get_title('adminurl', 'post', ''), 0, 100);
    $array['api_key'] = nv_substr($nv_Request->get_title('api_key', 'post', ''), 0, 50);
    $array['api_secret'] = nv_substr($nv_Request->get_title('api_secret', 'post', ''), 0, 50);

    // Xử lý dữ liệu
    $array['description'] = nv_nl2br(nv_htmlspecialchars(strip_tags($array['description'])), '<br />');

    if (empty($array['title'])) {
        $error[] = $nv_Lang->getModule('site_error_title');
    }

    if (empty($array['sitedomain'])) {
        $error[] = $nv_Lang->getModule('site_error_sitedomain1');
    } else {
        $sitedomain = $array['sitedomain'];
        if (!preg_match('/^https*\:\/\//i', $sitedomain)) {
            $sitedomain = 'http://' . $sitedomain;
        }
        $sitedomain = parse_url($sitedomain);
        if (empty($sitedomain) or !isset($sitedomain['scheme'], $sitedomain['host'])) {
            $error[] = $nv_Lang->getModule('site_error_sitedomain2');
        } else {
            if (!nv_is_url($sitedomain['scheme'] . '://' . $sitedomain['host'])) {
                $error[] = $nv_Lang->getModule('site_error_sitedomain2');
            } else {
                $sitedomain = nv_strtolower($sitedomain['host']);

                // Kiểm tra trùng
                $is_exists = false;
                $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_sites WHERE sitedomain = :sitedomain" . ($id ? ' AND id != ' . $id : '');
                $sth = $db->prepare($sql);
                $sth->bindParam(':sitedomain', $sitedomain, PDO::PARAM_STR);
                $sth->execute();
                if ($sth->fetchColumn()) {
                    $is_exists = true;
                }

                if ($is_exists) {
                    $error[] = $nv_Lang->getModule('site_error_exists');
                }
            }
        }
    }

    if (empty($error)) {
        if (!$id) {
            $sql = "SELECT MAX(weight) weight FROM " . $db_config['prefix'] . "_" . $module_data . "_sites";
            $weight = intval($db->query($sql)->fetchColumn()) + 1;

            $sql = "INSERT INTO " . $db_config['prefix'] . "_" . $module_data . "_sites (
                title, sitedomain, adminurl, api_key, api_secret, description, weight, add_time, edit_time
            ) VALUES (
                :title, :sitedomain, :adminurl, :api_key, :api_secret, :description, " . $weight . ", " . NV_CURRENTTIME . ", 0
            )";
        } else {
            $sql = "UPDATE " . $db_config['prefix'] . "_" . $module_data . "_sites SET
                title = :title, sitedomain=:sitedomain, adminurl=:adminurl,
                api_key=:api_key, api_secret=:api_secret, description = :description,
                edit_time = " . NV_CURRENTTIME . "
            WHERE id = " . $id;
        }

        try {
            $sth = $db->prepare($sql);
            $sth->bindParam(':title', $array['title'], PDO::PARAM_STR);
            $sth->bindParam(':sitedomain', $sitedomain, PDO::PARAM_STR);
            $sth->bindParam(':adminurl', $array['adminurl'], PDO::PARAM_STR);
            $sth->bindParam(':api_key', $array['api_key'], PDO::PARAM_STR);
            $sth->bindParam(':api_secret', $array['api_secret'], PDO::PARAM_STR);
            $sth->bindParam(':description', $array['description'], PDO::PARAM_STR, strlen($array['description']));
            $sth->execute();

            if ($id) {
                nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_EDIT_SITE', json_encode($array), $admin_info['userid']);
            } else {
                nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_ADD_SITE', json_encode($array), $admin_info['userid']);
            }

            $nv_Cache->delMod($module_name);
            nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
        } catch (PDOException $e) {
            trigger_error(print_r($e, true));
            $error[] = $nv_Lang->getModule('errorsave');
        }
    }
}

$array['description'] = nv_br2nl($array['description']);

$xtpl = new XTemplate('sites.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('CAPTION', $caption);
$xtpl->assign('FORM_ACTION', $form_action);
$xtpl->assign('DATA', $array);

// Xuất danh sách
$sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_sites ORDER BY weight ASC";
$array_sites = $db->query($sql)->fetchAll();
$num = sizeof($array_sites);

foreach ($array_sites as $row) {
    $row['url_edit'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $row['id'];
    $row['status_render'] = empty($row['status']) ? '' : ' checked="checked"';

    for ($i = 1; $i <= $num; ++$i) {
        $xtpl->assign('WEIGHT', [
            'w' => $i,
            'selected' => ($i == $row['weight']) ? ' selected="selected"' : ''
        ]);

        $xtpl->parse('main.loop.weight');
    }

    $xtpl->assign('ROW', $row);
    $xtpl->parse('main.loop');
}

// Hiển thị lỗi
if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

// Hiển thị nút thêm
if (!$is_edit) {
    $xtpl->parse('main.add_btn');
}

// Cuộn đến form
if ($is_submit_form or $is_edit) {
    $xtpl->parse('main.scroll');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
