<?php
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __FILE__), PATHINFO_DIRNAME) . '/..')));

require NV_CONSOLE_DIR . '/server.php';
require NV_ROOTDIR . '/includes/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

$last_id = 0;
$filelog = NV_CONSOLE_DIR . '/tool_2024/fix_point_log.txt';
if (file_exists($filelog)) {
    $last_id = intval(file_get_contents($filelog));
} else {
    $last_id = $db->query("SELECT MIN(id) FROM nv4_points_log")->fetchColumn();
}

$last_id_max = $last_id + 1000;
$sql = "SELECT * FROM nv4_points_log WHERE id > " . $last_id . " AND id <= " . $last_id_max . " LIMIT 1000";
$result = $db->query($sql);
$arr_tmp = [];
while ($row = $result->fetch()) {
    $arr_tmp[$row['id']] = $row;
}
if (empty($arr_tmp)) {
    $max_id = $db->query("SELECT MAX(id) FROM nv4_points_log")->fetchColumn();
    if ($last_id_max < $max_id) {
        $last_id = $last_id_max;
    } else {
        echo "Đã chạy hết!!";
        exit(1);
    }
    echo "chạy đến " . $last_id . "\n";
} else {
    foreach ($arr_tmp as $row) {
        $last_id = $row['id'];
        $update = '';
        if ($row['status'] == 1) {
            if ($row['admin_id'] == 0 and $row['is_reward'] == 0 and $row['site_id'] == 0 and $row['customerid'] == 0) {
                $update = 'customerid = userid';
            } else if (strpos($row['message'], 'Nhận điểm từ tài khoản') !== false) {
                $update = 'customerid = admin_id, sub_status = 2, admin_id = 0';
            } else if (strpos($row['message'], 'Thưởng điểm cho tài khoản') !== false) {
                $update = 'is_reward = 1';
            }
        } else {
            if (strpos($row['message'], 'Tặng điểm cho tài khoản') !== false) {
                $update = 'customerid = admin_id, type_transaction = 1, admin_id = 0';
            } else if (strpos($row['message'], 'đã hết hạn sử dụng') !== false or strpos($row['message'], 'has expired') !== false) {
                $update = 'type_transaction = 0';
            } else if (strpos($row['message'], 'Mua thêm bộ lọc') !== false or strpos($row['message'], 'Buy more filters') !== false) {
                $update = 'type_transaction = 2';
            } else if (strpos($row['message'], 'Kích hoạt tính năng gửi mail cho bộ lọc') !== false or strpos($row['message'], 'Enable emailing for the filter') !== false) {
                $update = 'type_transaction = 3';
            } else if (strpos($row['message'], 'Bạn vừa mua thêm tin theo dõi của') !== false or strpos($row['message'], 'Bạn vừa mua thêm lượt theo dõi') !== false or strpos($row['message'], 'You just bought more followers') !== false) {
                $update = 'type_transaction = 4';
            } else if (strpos($row['message'], 'điểm của bạn khi bạn xem giá trúng thầu của hàng hóa') !== false or strpos($row['message'], 'when you see the winning bid price of the goods') !== false) {
                $update = 'type_transaction = 7';
            } else if (strpos($row['message'], 'Phục hồi Tải nhanh hồ sơ gói thầu') !== false or strpos($row['message'], 'Recovery Quick load of') !== false or strpos($row['message'], 'Tải nhanh hồ sơ gói thầu của') !== false or strpos($row['message'], 'Quick download of') !== false) {
                $update = 'type_transaction = 10';
            } else if (strpos($row['message'], 'điểm do bạn có hành động ấn cập nhật lại dữ liệu của') !== false or strpos($row['message'], 'points because you clicked to update the data of') !== false) {
                $update = 'type_transaction = 11';
            } else if (strpos($row['message'], 'Tổng số lượng gói thầu tham gia trong từng năm của mỗi nhà thầu') !== false) {
                $update = 'type_transaction = 12';
            } else if (strpos($row['message'], 'Tổng giá trị trúng thầu trong từng năm của mỗi nhà thầu') !== false) {
                $update = 'type_transaction = 13';
            } else if (strpos($row['message'], 'Tổng giá trị phát sinh bảo lãnh dự thầu của mỗi nhà thầu trong từng năm') !== false) {
                $update = 'type_transaction = 14';
            } else if (strpos($row['message'], 'Tổng giá gói thầu của mỗi nhà thầu trong từng năm') !== false) {
                $update = 'type_transaction = 15';
            } else if (strpos($row['message'], 'Tổng giá dự thầu của mỗi nhà thầu trong từng năm') !== false) {
                $update = 'type_transaction = 16';
            } else if (strpos($row['message'], 'Mua thêm tài khoản phụ') !== false or strpos($row['message'], 'Buy more sub-accounts') !== false) {
                $update = 'type_transaction = 19';
            } else if (strpos($row['message'], 'Download file PDF báo cáo của nhà thầu mất') !== false or strpos($row['message'], 'Download PDF file of contractor') !== false or strpos($row['message'], 'Download file PDF báo cáo của bên mời thầu mất') !== false or strpos($row['message'], 'Download PDF file of the tenderer') !== false) {
                $update = 'type_transaction = 20';
            } else if (strpos($row['message'], 'Thay đổi chuyên viên hỗ trợ gói') !== false or strpos($row['message'], 'Change package support specialist') !== false) {
                $update = 'type_transaction = 21';
            } else if (strpos($row['message'], 'Mua thêm dung lượng upload file') !== false or strpos($row['message'], 'Purchase additional file upload capacity') !== false) {
                $update = 'type_transaction = 106';
            } else if (strpos($row['message'], 'Mua lượt đếm số nhà thầu nhận tin thầu') !== false or strpos($row['message'], 'Buy counts of contractors receiving bids') !== false) {
                $update = 'type_transaction = 104';
            } else if (strpos($row['message'], 'Mua thêm lượt theo dõi tin') !== false or strpos($row['message'], 'Purchase more bid information follows') !== false) {
                $update = 'type_transaction = 102';
            } else if (strpos($row['message'], 'Mua lượt gửi email mời thầu') !== false or strpos($row['message'], 'Buy bids email submissions') !== false) {
                $update = 'type_transaction = 105';
            } else if (strpos($row['message'], 'Download dữ liệu doanh nghiệp') !== false or strpos($row['message'], 'Download business data') !== false) {
                $update = 'type_transaction = 108';
            } else if (strpos($row['message'], 'lượt nhận mail') !== false or strpos($row['message'], 'mail recipients') !== false) {
                $update = 'type_transaction = 103';
            } else if (strpos($row['message'], 'Thay đổi chuyên viên hỗ trợ gói') !== false or strpos($row['message'], 'Change package support specialist') !== false) {
                $update = 'type_transaction = 109';
            }
        }
        if ($update != '') {
            $db->query("UPDATE nv4_points_log SET " . $update . " WHERE id =" . $row['id']);
            echo "Update: " . $row['id'] . "\n";
        } elseif (!empty($row['message'])) {
            file_put_contents(NV_CONSOLE_DIR . '/tool_2024/fix_point_log.noupdate', $row['id'] . "\n", FILE_APPEND);
        }
    }
    echo "Xong " . $last_id . "\n";
}

if ($last_id > 0) {
    file_put_contents($filelog, $last_id);
}

/*
# Sql fix lại các giao dịch
# nhận điểm tặng nhau SELECT * FROM `nv4_points_log` WHERE status = 1 AND message LIKE 'Nhận điểm từ tài khoản%' ORDER BY `nv4_points_log`.`admin_id` DESC;
UPDATE `nv4_points_log` SET customerid = admin_id, sub_status = 2, admin_id = 0 WHERE status = 1 AND message LIKE 'Nhận điểm từ tài khoản%';

# trừ điểm tặng nhau SELECT * FROM `nv4_points_log` WHERE status = -1 AND message LIKE 'Tặng điểm cho tài khoản%' ORDER BY `nv4_points_log`.`admin_id` DESC;
UPDATE `nv4_points_log` SET customerid = admin_id, type_transaction = 1, admin_id = 0 WHERE status = -1 AND message LIKE 'Tặng điểm cho tài khoản%';

# giao dịch mua thêm điểm của người dùng SELECT * FROM `nv4_points_log` WHERE status = 1 AND admin_id =0 AND is_reward = 0 AND site_id =0 AND customerid =0;
UPDATE `nv4_points_log` SET customerid = userid WHERE status = 1 AND admin_id =0 AND is_reward = 0 AND site_id =0 AND customerid =0;

# hệ thống tặng: SELECT * FROM `nv4_points_log` WHERE message like '%Thưởng điểm cho tài khoản%';
UPDATE `nv4_points_log` SET is_reward = 1 WHERE message LIKE '%Thưởng điểm cho tài khoản%';

# 2 - Số điểm trừ khi thêm 1 bộ lọc 50 điểm
UPDATE `nv4_points_log` SET type_transaction = 2 WHERE message LIKE 'Mua thêm bộ lọc%';
UPDATE `nv4_points_log` SET type_transaction = 2 WHERE message LIKE 'Buy more filters%';

# 3 - Số điểm trừ cho tính năng gửi mail/ tháng/ bộ lọc 200 điểm
UPDATE `nv4_points_log` SET type_transaction = 3 WHERE message LIKE 'Kích hoạt tính năng gửi mail cho bộ lọc:%';
UPDATE `nv4_points_log` SET type_transaction = 3 WHERE message LIKE 'Enable emailing for the filter:%';

# 4 - Số điểm trừ khi thêm 1 tin theo dõi 20 điểm
UPDATE `nv4_points_log` SET type_transaction = 4 WHERE message LIKE 'Bạn vừa mua thêm tin theo dõi của%';
UPDATE `nv4_points_log` SET type_transaction = 4 WHERE message LIKE 'Bạn vừa mua thêm lượt theo dõi%';
UPDATE `nv4_points_log` SET type_transaction = 4 WHERE message LIKE 'You just bought more followers%';

# 5 - Số điểm trừ khi xuất dữ liệu cho mỗi block hàng hóa khi đã đăng ký 1 trong các gói VIP sau: VIP 1, VIP 2, VIP 3, VIP 4, VIP 5, VIP 7 1 điểm
# 6 - Số điểm trừ khi xuất dữ liệu cho mỗi block hàng hóa khi chưa đăng ký gói VIP 1, VIP 2, VIP 3, VIP 4, VIP 5, VIP 7 10 điểm
# 2 loại trên message chung nhau nên k xử lý dc

# 7 - Số điểm trừ khi xem giá một mặt hàng hoá 1 điểm
UPDATE `nv4_points_log` SET type_transaction = 7 WHERE message LIKE '%điểm của bạn khi bạn xem giá trúng thầu của hàng hóa%';
UPDATE `nv4_points_log` SET type_transaction = 7 WHERE message LIKE '%when you see the winning bid price of the goods%';

#  8 - Xem chi tiết thông tin thầu (thông tin bất kỳ: TBMT, KHLCNT, KQMT, KQLCNT, TBMĐT.... gọi là 1 thông tin thầu) 1 điểm
#  9 - Xem thông tin thầu nâng cao (các thông tin bị ẩn trong chi tiết thông tin thầu, bắt phải đăng ký VIEWEB/VIP) 1 điểm points_view_adv
#  2 message giống nhau lên k tách dc,

# 10 - Số điểm trừ khi tải file 10 + (5 * số MB / 10) điểm
UPDATE `nv4_points_log` SET type_transaction = 10 WHERE message LIKE 'Phục hồi Tải nhanh hồ sơ gói thầu%';
UPDATE `nv4_points_log` SET type_transaction = 10 WHERE message LIKE 'Recovery Quick load of%';
UPDATE `nv4_points_log` SET type_transaction = 10 WHERE message LIKE 'Tải nhanh hồ sơ gói thầu của%';
UPDATE `nv4_points_log` SET type_transaction = 10 WHERE message LIKE 'Quick download of%';

# 11 - Số điểm trừ khi ấn nút cập nhật lại mà không có dữ liệu bị thay đổi
UPDATE `nv4_points_log` SET type_transaction = 11 WHERE message LIKE '%điểm do bạn có hành động ấn cập nhật lại dữ liệu của%';
UPDATE `nv4_points_log` SET type_transaction = 11 WHERE message LIKE '%points because you clicked to update the data of%';

# 12 - Mua tính năng nâng cao gói X2 Tổng số lượng gói thầu tham gia trong từng năm của mỗi nhà thầu
UPDATE `nv4_points_log` SET type_transaction = 12 WHERE message LIKE '%Tổng số lượng gói thầu tham gia trong từng năm của mỗi nhà thầu%';

# 13 - Mua tính năng nâng cao gói X2 Tổng giá trị trúng thầu trong từng năm của mỗi nhà thầu
UPDATE `nv4_points_log` SET type_transaction = 13 WHERE message LIKE '%Tổng giá trị trúng thầu trong từng năm của mỗi nhà thầu%';

# 14 - Mua tính năng nâng cao gói X2 Tổng giá trị phát sinh bảo lãnh dự thầu của mỗi nhà thầu trong từng năm
UPDATE `nv4_points_log` SET type_transaction = 14 WHERE message LIKE '%Tổng giá trị phát sinh bảo lãnh dự thầu của mỗi nhà thầu trong từng năm%';

# 15 - Mua tính năng nâng cao gói X2 Tổng giá gói thầu của mỗi nhà thầu trong từng năm
UPDATE `nv4_points_log` SET type_transaction = 15 WHERE message LIKE '%Tổng giá gói thầu của mỗi nhà thầu trong từng năm%';

# 16 - Mua tính năng nâng cao gói X2 Tổng giá dự thầu của mỗi nhà thầu trong từng năm
UPDATE `nv4_points_log` SET type_transaction = 16 WHERE message LIKE '%Tổng giá dự thầu của mỗi nhà thầu trong từng năm%';

#  19 - Số điểm khi add thêm 1 tài khoản phụ
UPDATE `nv4_points_log` SET type_transaction = 19 WHERE message LIKE 'Mua thêm tài khoản phụ%';
UPDATE `nv4_points_log` SET type_transaction = 19 WHERE message LIKE 'Buy more sub-accounts, term%';

# 20 - tải file báo cáo của gói T100
UPDATE `nv4_points_log` SET type_transaction = 20 WHERE message LIKE 'Download file PDF báo cáo của nhà thầu mất%';
UPDATE `nv4_points_log` SET type_transaction = 20 WHERE message LIKE 'Download PDF file of contractor%';
UPDATE `nv4_points_log` SET type_transaction = 20 WHERE message LIKE 'Download file PDF báo cáo của bên mời thầu mất%';
UPDATE `nv4_points_log` SET type_transaction = 20 WHERE message LIKE 'Download PDF file of the tenderer%';

# 21 - Thay đổi chuyên viên hỗ trợ
UPDATE `nv4_points_log` SET type_transaction = 21 WHERE message LIKE 'Thay đổi chuyên viên hỗ trợ gói%';
UPDATE `nv4_points_log` SET type_transaction = 21 WHERE message LIKE 'Change package support specialist%';

#dauthau.net
# 106 -Gói dung lượng upload (Số điểm/1MB) 10 Điểm
UPDATE `nv4_points_log` SET type_transaction = 106 WHERE message = 'Mua thêm dung lượng upload file';
UPDATE `nv4_points_log` SET type_transaction = 106 WHERE message = 'Purchase additional file upload capacity';

# 104 -Số điểm dùng để kiểm tra số nhà thầu nhận tin 10 Điểm
UPDATE `nv4_points_log` SET type_transaction = 104 WHERE message = 'Mua lượt đếm số nhà thầu nhận tin thầu';
UPDATE `nv4_points_log` SET type_transaction = 104 WHERE message = 'Buy counts of contractors receiving bids ';

# 102 -Số điểm trừ khi thêm 1 tin theo dõi 20 Điểm
UPDATE `nv4_points_log` SET type_transaction = 102 WHERE message = 'Mua thêm lượt theo dõi tin';
UPDATE `nv4_points_log` SET type_transaction = 102 WHERE message = 'Purchase more bid information follows';

# 105 -Số điểm dùng để mua thêm lượt gửi email mời thầu 20 Điểm
UPDATE `nv4_points_log` SET type_transaction = 105 WHERE message = 'Mua lượt gửi email mời thầu';
UPDATE `nv4_points_log` SET type_transaction = 105 WHERE message = 'Buy bids email submissions ';

# 107 - Xem thông tin nâng cao của doanh nghiệp Xem %s tại %s có MST: %s
# 108 - Download file danh sách nhà thầu
UPDATE `nv4_points_log` SET type_transaction = 108 WHERE message LIKE 'Download dữ liệu doanh nghiệp%';
UPDATE `nv4_points_log` SET type_transaction = 108 WHERE message LIKE 'Download business data%';

#  103 -Số điểm dùng để lượt email nhận tin thầu 20 Điểm
UPDATE `nv4_points_log` SET type_transaction = 103 WHERE message LIKE '%lượt nhận mail%';
UPDATE `nv4_points_log` SET type_transaction = 103 WHERE message LIKE '%mail recipients%';

#  109 - Thay đổi chuyên viên hỗ trợ
UPDATE `nv4_points_log` SET type_transaction = 109 WHERE message LIKE 'Thay đổi chuyên viên hỗ trợ gói%';
UPDATE `nv4_points_log` SET type_transaction = 109 WHERE message LIKE 'Change package support specialist%';
 */
