<?php

/**
 * Tính năng quản lý hóa đơn điện tử
 * API: L<PERSON><PERSON> thông tin hóa đơn điện tử thông qua ID đơn hàng
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2024 Huỳnh Quốc Đạt. All rights reserved
 * @createdAt Mon, 15 Apr 2024 11:55:00
 */

namespace NukeViet\Module\crmbidding\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetEinvoiceByOrder implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'einvoices';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];

        $order_id = $nv_Request->get_int('order_id', 'post', '0');
        if ($order_id > 0) {
            try {
                $row = $db->query('SELECT t1.id,t1.einvoice_no,t1.uploader_id,t1.created_at,t1.updated_at FROM ' . NV_PREFIXLANG . '_' . $module_data . '_einvoices t1 LEFT JOIN ' . NV_PREFIXLANG . '_' . $module_data . '_einvoice_orders t2 ON t1.id=t2.einvoice_id WHERE t2.order_id=' . $order_id)->fetch();
                if (empty($row)) {
                    $this->result->setSuccess()
                        ->setCode('4000')
                        ->setMessage($nv_Lang->getModule('api_error_400'));
                } else {
                    // Xử lý dữ liệu
                    $_uploader = $db->query('SELECT userid, first_name, last_name, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $row['uploader_id'])->fetch();
                    $row['uploader_username'] = $_uploader['username'];
                    $row['uploader_fullname'] = nv_show_name_user($_uploader['first_name'], $_uploader['last_name'], $_uploader['userid']);

                    $this->result->setSuccess();
                    $this->result->set('data', $row);
                }
            } catch (PDOException $e) {
                trigger_error($e);
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('`order_id` is integer, more than 0')
                ->getResult();
        }
        return $this->result->getResult();
    }
}
