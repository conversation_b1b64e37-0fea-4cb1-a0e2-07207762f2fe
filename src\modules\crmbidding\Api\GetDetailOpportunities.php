<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetDetailOpportunities implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'opportunities';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $array_label, $array_user_id_users, $array_groups_leads, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        $opportunitiesid = $nv_Request->get_int('opportunitiesid', 'post', '0');
        if ($opportunitiesid > 0) {
            try {
                require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';
                $row = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities WHERE active=1 AND id=' . $opportunitiesid)->fetch();
                if (empty($row)) {
                    $this->result->setSuccess()
                        ->setCode('4000')
                        ->setMessage($nv_Lang->getModule('api_error_400'));
                } else {
                    // xử lý dữ liệu thô
                    $row['timecreate_display'] = $row['timecreate'] > 0 ? nv_date('h:i d/m/Y', $row['timecreate']) : '';
                    $row['updatetime_display'] = $row['updatetime'] > 0 ? nv_date('h:i d/m/Y', $row['updatetime']) : '';
                    $row['last_comment_display'] = $row['last_comment'] > 0 ? nv_date('h:i d/m/Y', $row['last_comment']) : '';
                    $row['schedule_display'] = $row['schedule'] > 0 ? nv_date('h:i d/m/Y', $row['schedule']) : '';
                    // $row['first_time_display'] = $row['first_time'] > 0 ? nv_date('h:i d/m/Y', $row['first_time']) : '';
                    $row['status_display'] = $nv_Lang->getModule('status_opportunities' . $row['status']);

                    if (isset($array_user_id_users[$row['affilacate_id']])) {
                        $affilacate_id = $array_user_id_users[$row['affilacate_id']];
                        $row['affilacate_id_fullname'] = nv_show_name_user($affilacate_id['first_name'], $affilacate_id['last_name']);
                        $row['affilacate_id_show_name'] = $affilacate_id['username'];
                    }
                    if (isset($array_user_id_users[$row['caregiver_id']])) {
                        $caregiver_id = $array_user_id_users[$row['caregiver_id']];
                        $row['caregiver_id_fullname'] = nv_show_name_user($caregiver_id['first_name'], $caregiver_id['last_name']);
                        $row['caregiver_id_show_name'] = $caregiver_id['username'];
                    }
                    $row['source_leads_display'] = isset($array_groups_leads[$row['source_leads']]['title']) ? $array_groups_leads[$row['source_leads']]['title'] : '';

                    if ($row['label'] != '') {
                        $row['label_arr'] = explode(',', $row['label']);
                        foreach ($row['label_arr'] as $key => $label) {
                            $row['label_arr'][$key] = $array_label[$label];
                        }
                    }

                    $this->result->setSuccess();
                    $this->result->set('data', $row);
                }
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Opportunitiesid is integer, more than 0')
                ->getResult();
        }
        return $this->result->getResult();
    }
}
