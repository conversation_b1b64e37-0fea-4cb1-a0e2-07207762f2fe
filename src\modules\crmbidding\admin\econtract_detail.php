<?php

/**
 * T<PERSON>h năng quản lý hợp đồng điện tử
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2024 Hu<PERSON><PERSON> Quốc Đạt. All rights reserved
 * @createdAt Mon, 15 Apr 2024 11:55:00
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

$is_manage_econtract = isset($my_admin_config['manage_econtract']) && ($my_admin_config['manage_econtract'] == 1); // Phân quyền: Quản lý TẤT CẢ hợp đồng

// Chức năng: xem chi tiết (mặc định); cập nhật thông tin; xem trước
$act = $nv_Request->get_string('action', 'get', 'detail');

// ID hợp đồng
$id = $nv_Request->get_int('id', 'get', 0);
if (empty($id)) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_draft');
}

$econtract = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $id . ' AND status != ' . EcontractStatus::Cancel->value)->fetch();
if (!$econtract) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_draft');
}

if (!isset($my_managed_users[$econtract['uploader_id']]) && !defined('NV_IS_SPADMIN') && !$is_manage_econtract) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_draft');
}

// Thay đổi hình thức xem hợp đồng
if ($nv_Request->isset_request('change_type_econtract', 'post, get')) {
    $econtract_id = $nv_Request->get_int('econtract_id', 'post, get', 0);
    $type_econtract = $nv_Request->get_int('type_econtract', 'post, get', 0);
    $content = 'NO_' . $type_econtract;

    $query = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $econtract_id;
    $row = $db->query($query)->fetch();
    if (isset($row)) {
        $query = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET type_econtract=' . intval($type_econtract) . ' WHERE id=' . $econtract_id;
        $db->query($query);
        //Log cập nhật
        $changed_data['type_econtract'] = [
            'old' => $row['type_econtract'],
            'new' => $type_econtract
        ];
        // Lấy thông tin admin thao tác
        $info_admin_action_log = $nv_Lang->getModule('$info_admin_action_log');
        $admin_action_log = $db->query('SELECT userid, username, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $admin_info['userid'])->fetch() ?: null;
        if ($admin_action_log) {
            $info_admin_action_log = nv_show_name_user($admin_action_log['first_name'], $admin_action_log['last_name'], $admin_action_log['username']);
        }
        $log_action_vi = '<strong>' . $info_admin_action_log . '</strong> Thay đổi hình thức xem hợp đồng';
        $log_action_en = '<strong>' . $info_admin_action_log . '</strong> Change the contract viewing form';

        create_log_econtract([
            'econtract_id' => $row['id'],
            'version_id' => $row['current_version'],
            'action' => 1,
            'user_id' => $admin_info['userid'],
            'action_desc_vi' => $log_action_vi,
            'action_desc_en' => $log_action_en,
            'changed_data' => json_encode($changed_data, JSON_UNESCAPED_UNICODE),
            'log_visible' => 1,
            'created_at' => NV_CURRENTTIME
        ]);
        $content = 'OK_' . $type_econtract;
    }
    include NV_ROOTDIR . '/includes/header.php';
    echo $content;
    include NV_ROOTDIR . '/includes/footer.php';
}

/**
 * Bổ sung thông tin
 * - Thời gian lập biên bản
 * - Thời gian giao hàng
 * - Tài khoản
 * - Mật khẩu
 */
if ($nv_Request->isset_request('update_econtract', 'post') == NV_CHECK_SESSION) {
    $row_update['econtract_id'] = $nv_Request->get_int('econtract_id', 'post', 0);
    $row_update['acceptance_time'] = $nv_Request->get_title('acceptance_time', 'post', 0);
    $row_update['delivery_time'] = $nv_Request->get_title('delivery_time', 'post', 0);
    $row_update['account'] = $nv_Request->get_title('account', 'post', '');
    $row_update['password'] = $nv_Request->get_title('password', 'post', '');

    $check_econtract = $db->query('SELECT id, acceptance_time, delivery_time, account, password, signing_time
        FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts
        WHERE id=' . $row_update['econtract_id'])->fetch();

    if (!$check_econtract) {
        nv_jsonOutput([
            'status' => false,
            'message' => 'Hợp đồng không tồn tại',
        ]);
    }

    if (preg_match("/^(\d{1,2})[-\/](\d{1,2})[-\/](\d{4})$/", $row_update['acceptance_time'], $m)) {
        $row_update['acceptance_time'] = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
    } else {
        $row_update['acceptance_time'] = 0;
    }

    if (preg_match("/^(\d{1,2})[-\/](\d{1,2})[-\/](\d{4})$/", $row_update['delivery_time'], $m)) {
        $row_update['delivery_time'] = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
    } else {
        $row_update['delivery_time'] = 0;
    }

    if ($row_update['acceptance_time'] <= 0) {
        $error = 'Lỗi: Chưa nhập thời gian lập biên bản';
    } elseif ($row_update['delivery_time'] <= 0) {
        $error = 'Lỗi: Chưa nhập thời gian giao hàng';
    } elseif ($row_update['acceptance_time'] > $row_update['delivery_time']) {
        $error = 'Lỗi: Thời gian bàn giao phải sau thời gian lập biên bản';
    } elseif ($check_econtract['signing_time'] > $row_update['delivery_time']) {
        $error = 'Lỗi: Thời gian bàn giao phải sau thời gian ký hợp đồng';
    }

    if (!empty($error)) {
        nv_jsonOutput([
            'status' => false,
            'message' => $error,
        ]);
    }

    $changed_fields = ['acceptance_time', 'delivery_time', 'account', 'password'];
    foreach ($changed_fields as $field) {
        if ($check_econtract[$field] != $row_update[$field]) {
            $changed_data[$field] = [
                'old' => $check_econtract[$field],
                'new' => $row_update[$field]
            ];
        }
    }

    // Cập nhật thông tin
    $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET
            acceptance_time=:acceptance_time,
            delivery_time=:delivery_time,
            account=:account,
            password=:password
            WHERE id=' . $row_update['econtract_id']);

    // Bind data
    $stmt->bindParam(':acceptance_time', $row_update['acceptance_time'], PDO::PARAM_STR);
    $stmt->bindParam(':delivery_time', $row_update['delivery_time'], PDO::PARAM_STR);
    $stmt->bindParam(':account', $row_update['account'], PDO::PARAM_STR);
    $stmt->bindParam(':password', $row_update['password'], PDO::PARAM_STR);

    if ($stmt->execute()) {
        create_log_econtract([
            'econtract_id' => $row_update['econtract_id'],
            'version_id' => $check_econtract['current_version'],
            'action' => 1,
            'user_id' => $admin_info['userid'],
            'action_desc_vi' => 'Cập nhật thông tin biên bản bàn giao',
            'action_desc_en' => 'Update handover minutes information',
            'changed_data' => json_encode($changed_data, JSON_UNESCAPED_UNICODE),
            'log_visible' => 1,
            'created_at' => NV_CURRENTTIME
        ]);

        nv_jsonOutput([
            'status' => true,
            'message' => 'Cập nhật thành công',
        ]);
    } else {
        nv_jsonOutput([
            'status' => false,
            'message' => 'Cập nhật không thành công',
        ]);
    }
}

// Duyệt hợp đồng tải lên của khách
if ($act == 'approve') {
    if ($econtract['stage'] == 4) {
        $econtract['customer_signed'] = 1;
        $econtract['updated_at'] = NV_CURRENTTIME;
        if ($econtract['hstdt_signed']) {
            $econtract['status'] = 5;
            $econtract['stage'] = 5;
            $econtract['stage_next'] = 5;
        } else {
            $econtract['status'] = 3;
            $econtract['stage'] = 3;
            $econtract['stage_next'] = 5;
        }

        try {
            $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET customer_signed=:customer_signed, status=:status, stage=:stage, stage_next=:stage_next, updated_at=:updated_at WHERE id=' . $id);

            // Bind data
            $stmt->bindParam(':customer_signed', $econtract['customer_signed'], PDO::PARAM_INT);
            $stmt->bindParam(':status', $econtract['status'], PDO::PARAM_INT);
            $stmt->bindParam(':stage', $econtract['stage'], PDO::PARAM_INT);
            $stmt->bindParam(':stage_next', $econtract['stage_next'], PDO::PARAM_INT);
            $stmt->bindParam(':updated_at', $econtract['updated_at'], PDO::PARAM_INT);
            $stmt->execute();

            // 4. Lưu log
            $log_action_vi = 'Duyệt hợp đồng khách đã ký';
            $log_action_en = 'Approving signed contract by customer';
            $db->query('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_logs (econtract_id, version_id, action, user_id, action_desc_vi, action_desc_en, log_visible, created_at) VALUES (' . $id . ', ' . $econtract['current_version'] . ', 0, ' . $admin_info['userid'] . ', ' . $db->quote($log_action_vi) . ', ' . $db->quote($log_action_en) . ', 1, ' . NV_CURRENTTIME . ')');
        } catch (\Throwable $th) {
            trigger_error($th->getMessage());
        }
    }
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $id . '&action=preview&version=' . $econtract['current_version']);
}

$type_view = $nv_Request->get_title('type_view', 'get', '');
if ($act == 'preview' && $type_view == 'view_draft' && $nv_Request->isset_request('save_draft', 'post') == NV_CHECK_SESSION) {
    $econtract_id = $nv_Request->get_int('contract_id', 'post', 0);

    $cache_file = 'econtract_' . $econtract_id . '_' . NV_LANG_DATA . '.cache';
    $cache_data = $nv_Cache->getItem($module_name, $cache_file);
    if ($cache_data == false) {
        nv_jsonOutput([
            'status' => false,
            'message' => 'Đã có lỗi xử lý với dữ liệu xem trước, vui lòng thử lại sau',
        ]);
    }

    $row_new = json_decode($cache_data, true);

    $row_update = [
        'id' => $econtract['id'],
        'contract_no' => $econtract['contract_no'],
        'type_econtract' => $econtract['type_econtract'],
        'customer_type' => $row_new['customer_type'],
        'authorization_letter' => $row_new['authorization_letter'],
        'c_name' => $row_new['c_name'],
        'phone' => $row_new['phone'],
        'email' => $row_new['email'],
        'receiver' => $row_new['receiver'],
        'receiver_phone' => $row_new['receiver_phone'],
        'receiver_address' => $row_new['receiver_address'],
        'c_address' => $row_new['c_address'],
        'term_changed' => $row_new['term_changed'],
        'term_changed_notes' => $row_new['term_changed_notes'],
        'contract_path' => $row_new['contract_path'],
        'updated_at' => NV_CURRENTTIME,
    ];

    if ($row_new['customer_type'] == 1) {
        $row_update['cccd'] = '';
        $row_update['tax_code'] = $row_new['tax_code'];
        $row_update['representative'] = $row_new['representative'];
        $row_update['jobtitle'] = $row_new['jobtitle'];
    } else {
        $row_update['cccd'] = $row_new['cccd'];
        $row_update['tax_code'] = '';
        $row_update['representative'] = '';
        $row_update['jobtitle'] = '';
    }

    if ($row_update['customer_type'] == 1 && !empty($row_update['tax_code']) && !taxcodecheck2($row_update['tax_code'])) {
        $error = 'Lỗi: MST không hợp lệ';
    } elseif ($row_update['customer_type'] == 0 && !empty($row_update['cccd']) && !cccdCheck($row_update['cccd'])) {
        $error = 'Lỗi: CCCD không hợp lệ';
    } elseif (!empty($row_update['phone']) && !phonecheck($row_update['phone'])) {
        $error = 'Lỗi: Số điện thoại không hợp lệ';
    } elseif (!empty($row_update['receiver_phone']) && !phonecheck($row_update['receiver_phone'])) {
        $error = 'Lỗi: Số điện thoại người nhận không hợp lệ';
    } elseif (!empty($row_update['email']) && !filter_var($row_update['email'], FILTER_VALIDATE_EMAIL)) {
        $error = 'Lỗi: Email không hợp lệ';
    }

    if (!empty($error)) {
        nv_jsonOutput([
            'status' => false,
            'message' => $error,
        ]);
    }
    $_vips_vi = $_vips_en = $data_vip = [];
    if (!empty($row_update['order_ids'])) {
        $where = [];
        $where['AND'] = [
            ['IN' => ['id' => '(' . implode(',', $order_ids) . ')']]
        ];
        $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
        $api_dtinfo->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingOrder')
            ->setData([
                'where' => $where,
                'show_customs_log' => 1
            ]);
        $result = $api_dtinfo->execute();
        if (!empty($api_dtinfo->getError())) {
            $error = $api_dtinfo->getError();
        }
        if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
            foreach ($result['data'] as $_order) {
                if (isset($all_array_user_id_users[$_order['caregiver_id']])) {
                    $econtract['uploader_id'] = $_order['caregiver_id'];
                }
                $_total += $_order['money'];
                $_discount += $_order['discount'];
                $_total_end += $_order['total'];

                $_order['vips_vi'] = $_order['vips_en'] = [];
                if (!empty($_order['customs_log'])) {
                    foreach ($_order['customs_log'] as $custom_vip) {
                        $data_vip[] = [$custom_vip['vip_name'], $custom_vip['numbers_year']];
                        if ($custom_vip['prefix_lang'] == 0) {
                            $_vips_vi[] = $custom_vip['vip'];
                            $_order['vips_vi'][] = $custom_vip['vip'];
                        } else {
                            $_vips_en[] = $custom_vip['vip'];
                            $_order['vips_en'][] = $custom_vip['vip'];
                        }
                    }
                }
                $_order['username'] = $db->query('SELECT username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $_order['userid'])->fetchColumn() ?: '';

                // Xử lý và lưu các thông tin liên quan đến đơn hàng cần sử dụng bên dưới
                $data_orders[$_order['id']] = [
                    'order_id' => $_order['id'],
                    'user_id' => $_order['userid'],
                    'username' => $_order['username'],
                    'vips_vi' => $_order['vips_vi'],
                    'vips_en' => $_order['vips_en']
                ];
            }
        }
    }

    $row_update['uploader_id'] = $admin_info['userid'];
    $row_update['vips_vi'] = implode(',', $_vips_vi);
    $row_update['vips_en'] = implode(',', $_vips_en);
    $row_update['contract_data'] = json_encode([
        'data_vip' => convert_data_vip($data_vip),
        'content' => '',
        'total_service' => $_total ?? 0,
        'promotion' => $_discount ?? 0,
        'total_payment' => $_total_end ?? 0,
    ]);

    $row_update['status'] = 0;
    $row_update['stage'] = 0;
    $row_update['stage_next'] = 3;
    // Cập nhật hợp đồng
    $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET
        tax_code=:tax_code, c_name=:c_name, representative=:representative, jobtitle=:jobtitle,
        c_address=:c_address, phone=:phone, email=:email, authorization_letter=:authorization_letter,
        receiver=:receiver, receiver_phone=:receiver_phone, receiver_address=:receiver_address,
        cccd=:cccd, customer_type=:customer_type, contract_path=:contract_path, status=:status,
        stage=:stage, stage_next=:stage_next, uploader_id=:uploader_id, customer_id=:customer_id,
        term_changed=:term_changed, term_changed_notes=:term_changed_notes, updated_at=:updated_at,
        vips_vi=:vips_vi, vips_en=:vips_en, contract_data=:contract_data
        WHERE id=' . $econtract_id);

    $stmt->bindParam(':tax_code', $row_update['tax_code'], PDO::PARAM_STR);
    $stmt->bindParam(':c_name', $row_update['c_name'], PDO::PARAM_STR);
    $stmt->bindParam(':representative', $row_update['representative'], PDO::PARAM_STR);
    $stmt->bindParam(':jobtitle', $row_update['jobtitle'], PDO::PARAM_STR);
    $stmt->bindParam(':c_address', $row_update['c_address'], PDO::PARAM_STR);
    $stmt->bindParam(':phone', $row_update['phone'], PDO::PARAM_STR);
    $stmt->bindParam(':email', $row_update['email'], PDO::PARAM_STR);
    $stmt->bindParam(':authorization_letter', $row_update['authorization_letter'], PDO::PARAM_STR);
    $stmt->bindParam(':receiver', $row_update['receiver'], PDO::PARAM_STR);
    $stmt->bindParam(':receiver_phone', $row_update['receiver_phone'], PDO::PARAM_STR);
    $stmt->bindParam(':receiver_address', $row_update['receiver_address'], PDO::PARAM_STR);
    $stmt->bindParam(':cccd', $row_update['cccd'], PDO::PARAM_STR);
    $stmt->bindParam(':customer_type', $row_update['customer_type'], PDO::PARAM_INT);
    $stmt->bindParam(':contract_path', $row_update['contract_path'], PDO::PARAM_STR);
    $stmt->bindParam(':status', $row_update['status'], PDO::PARAM_INT);
    $stmt->bindParam(':stage', $row_update['stage'], PDO::PARAM_INT);
    $stmt->bindParam(':stage_next', $row_update['stage_next'], PDO::PARAM_INT);
    $stmt->bindParam(':uploader_id', $row_update['uploader_id'], PDO::PARAM_INT);
    $stmt->bindParam(':customer_id', $econtract['customer_id'], PDO::PARAM_INT);
    $stmt->bindParam(':term_changed', $row_update['term_changed'], PDO::PARAM_INT);
    $stmt->bindParam(':term_changed_notes', $row_update['term_changed_notes'], PDO::PARAM_STR);
    $stmt->bindParam(':updated_at', $row_update['updated_at'], PDO::PARAM_INT);
    $stmt->bindParam(':vips_vi', $row_update['vips_vi'], PDO::PARAM_STR);
    $stmt->bindParam(':vips_en', $row_update['vips_en'], PDO::PARAM_STR);
    $stmt->bindParam(':contract_data', $row_update['contract_data'], PDO::PARAM_STR);
    $stmt->execute();

    // Lưu version mới
    $changed_fields = ['tax_code', 'c_name', 'representative', 'jobtitle', 'c_address', 'phone', 'email', 'authorization_letter', 'cccd', 'customer_type', 'contract_path', 'term_changed', 'term_changed_notes'];
    foreach ($changed_fields as $field) {
        if ($econtract[$field] != $row_update[$field]) {
            $changed_data[$field] = [
                'old' => $econtract[$field],
                'new' => $row_update[$field]
            ];
        }
    }
    if (!empty($changed_data)) {
        $max_code_ver = $db->query('SELECT MAX(version) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions WHERE econtract_id=' . $econtract_id)->fetchColumn() ?: 0;
        $version_id = $db->insert_id('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions (econtract_id, user_id, version, pdf_path, contract_data, created_at
        ) VALUES (
            ' . $econtract_id . ',
            ' . $admin_info['userid'] . ',
            ' . ($max_code_ver + 1) . ',
            ' . $db->quote($row_update['contract_path']) . ',
            ' . $db->quote(json_encode($row_update)) . ',
            ' . NV_CURRENTTIME . '
        )', 'id');
        $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET current_version=' . $version_id . ' WHERE id=' . $econtract_id);
    } else {
        // Nếu không thì cập nhật contract_data vào version hiện tại
        $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions SET pdf_path=' . $db->quote($row_update['contract_path']) . ', contract_data=' . $db->quote(json_encode($row_update)) . ' WHERE id=' . $row_update['current_version']);
    }

    // Tạo log hợp đồng: econtract_logs
    $info_admin_action_log = $nv_Lang->getModule('$info_admin_action_log');
    $admin_action_log = $db->query('SELECT userid, username, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $admin_info['userid'])->fetch() ?: null;
    if ($admin_action_log) {
        $info_admin_action_log = nv_show_name_user($admin_action_log['first_name'], $admin_action_log['last_name'], $admin_action_log['username']);
    }
    $log_action_vi = '<strong>' . $info_admin_action_log . '</strong> Cập nhật thông tin hợp đồng <strong>' . $econtract['contract_no'] . '</strong>';
    $log_action_en = '<strong>' . $info_admin_action_log . '</strong> Update econtract information <strong>' . $econtract['contract_no'] . '</strong>';
    $db->query('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_logs (econtract_id, version_id, action, user_id, action_desc_vi, action_desc_en, changed_data, log_visible, created_at) VALUES (' . $econtract_id . ', ' . $version_id . ', 1, ' . $admin_info['userid'] . ', ' . $db->quote($log_action_vi) . ', ' . $db->quote($log_action_en) . ',' . $db->quote(json_encode($changed_data)) . ', 1, ' . NV_CURRENTTIME . ')');

    // Khi lưu thành công thì xóa file cache
    $nv_Cache->delItem($module_name, $cache_file);

    nv_jsonOutput([
        'status' => true,
        'message' => 'Lưu thành công' . $row_new['contract_path'],
        'url_reload' => NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $econtract['id']
    ]);
}

// Lấy danh sách các link mẫu văn bảng đã tải lên
$data_cp = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts_additional WHERE econtract_id=' . $econtract['id'])->fetch();

// Lấy danh sách đơn hàng đang gắn vào hợp đồng
$orders = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id=' . $id)->fetchAll();
$order_ids = $custom_vips = [];
$_total = $_discount = $_total_end = 0;
$list_order = '';
if (!empty($orders)) {
    foreach ($orders as $order) {
        $order_ids[] = $order['order_id'];
    }
    $order_ids = array_unique($order_ids);

    // Lấy thông tin list đơn hàng
    $list_order = implode(', ', array_map(function ($order) {
        return sprintf('BDH%010s', $order['order_id']);
    }, $orders));
}

$econtract['sale_name'] = '';
if (!empty($order_ids)) {
    // Lấy ds đơn hàng để hiện khu vực "Thông tin danh sách các sản phẩm, dịch vụ"
    $where = [];
    $where['AND'] = [
        ['IN' => ['id' => '(' . implode(',', $order_ids) . ')']]
    ];
    $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
    $api_dtinfo->setModule('bidding')
        ->setLang('vi')
        ->setAction('ListBiddingOrder')
        ->setData([
            'where' => $where,
            'show_customs_log' => 1
        ]);
    $result = $api_dtinfo->execute();
    $error = $api_dtinfo->getError();
    if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
        $data_vip = [];
        foreach ($result['data'] as $_order) {
            $_total += $_order['money'];
            $_discount += $_order['discount'];
            $_total_end += $_order['total'];

            if (!empty($_order['customs_log'])) {
                // Gán sale_name cho đơn hàng
                if (empty($econtract['sale_name']) && !empty($_order['caregiver_id'])) {
                    // Nếu hợp đồng chưa có người chăm sóc thì gán người chăm sóc của đơn hàng
                    if ($econtract['uploader_id'] == 0) {
                        $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET uploader_id=' . $_order['caregiver_id'] . ' WHERE id=' . $econtract['id']);
                    }
                    if (isset($all_array_user_id_users[$_order['caregiver_id']])) {
                        $uploader = $all_array_user_id_users[$_order['caregiver_id']];
                        $econtract['sale_name'] = nv_show_name_user($uploader['first_name'], $uploader['last_name'], $uploader['userid']);
                    }
                }
                // Trường hợp nếu chưa có người chăm sóc caregiver_id = 0 thì sale_name sẽ là người chốt đơn
                if (empty($econtract['sale_name']) && !empty($_order['admin_id']) && $_order['caregiver_id'] == 0) {
                    if (isset($all_array_user_id_users[$_order['admin_id']])) {
                        $uploader = $all_array_user_id_users[$_order['admin_id']];
                        $econtract['sale_name'] = nv_show_name_user($uploader['first_name'], $uploader['last_name'], $uploader['userid']);
                    }
                }
                foreach ($_order['customs_log'] as $custom_vip) {
                    $data_vip[] = [$custom_vip['vip_name'], $custom_vip['numbers_year']];
                    $custom_vip['title'] = $global_arr_vip[$custom_vip['vip']] . (($custom_vip['prefix_lang'] ?? 0) == 0 ? ' (Tiếng Việt)' : ' (Tiếng Anh)');
                    $custom_vip['vip_price_format'] = nv_currency_format($custom_vip['vip_price'] * $custom_vip['numbers_year']);
                    $custom_vips[] = $custom_vip;
                }
            }
        }

        $econtract['contract_data'] = json_encode([
            'data_vip' => convert_data_vip($data_vip),
            'content' => '',
            'total_service' => $_total,
            'promotion' => $_discount,
            'total_payment' => $_total_end,
        ]);
    }
}

$list_info_payment = [
    'total' => str_replace('đ', ' VNĐ', nv_currency_format($_total)),
    'discount' => str_replace('đ', ' VNĐ', nv_currency_format($_discount)),
    'total_end' => str_replace('đ', ' VNĐ', nv_currency_format($_total_end)),
    'total_end_word' => numberToWords($_total_end),
];

if (empty($econtract['sale_name'])) {
    $econtract['sale_name'] = 'N/A';
}

// Thông tin báo giá
$econtract['link_payment_proposal'] = '';
$row_econtract = $econtract;
// Lấy danh sách phiên bản hợp đồng
$versions = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions WHERE econtract_id=' . $id . ' ORDER BY id DESC')->fetchAll();
$version_ids = [];
if (!empty($versions)) {
    foreach ($versions as $_version) {
        $version_ids[] = $_version['id'];
        if ($row_econtract['current_version'] == $_version['id']) {
            $row_econtract['version_code'] = str_pad($_version['version'] ?? 0, 2, '0', STR_PAD_LEFT);
        }
    }
}
// Phiên bản hiện tại của hợp đồng
$version_id = $nv_Request->get_int('version', 'get', $econtract['current_version']);
// Nếu version_id không thuộc hợp đồng thì chuyển hướng về phiên bản hiện tại của hợp đồng
if (!in_array($version_id, $version_ids) && !empty($version_ids)) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $econtract['id'] . '&version=' . $econtract['current_version']);
}

// Nếu hợp đồng đã hoàn thành rồi thì không cho xử lý gì thêm nữa
if ($econtract['status'] == EContractStatus::Done->value && $econtract['stage'] == EContractStage::Done->value && in_array($act, ['upload'])) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $econtract['id']);
}

if ($version_id) {
    $viewing_version = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions WHERE id=' . $version_id)->fetch();
    if ($viewing_version) {
        if ($version_id != $econtract['current_version']) {
            $econtract_viewing = json_decode($viewing_version['contract_data'] ?? [], true);
            $econtract_viewing['current_version'] = $econtract['current_version'];
            $econtract_viewing['contract_data'] = $econtract['contract_data'];
            $econtract = $econtract_viewing;
        }
        $econtract['version_data'] = $viewing_version;
        $econtract['version_data']['code'] = str_pad($econtract['version_data']['version'] ?? 0, 2, '0', STR_PAD_LEFT);
    }
}

/**
 * XỬ LÝ CHỌN PHIÊN BẢN CHÍNH THỨC
 * 1. Kiểm tra nếu đã là Phiên bản chính thức thì thôi, nếu chưa thì tiếp tục
 * 2. Lưu data row hiện tại vào phiên bản chính thức hiện tại
 * 3. Load data từ phiên bản được chọn (contract_data) ghi đè và Lưu rows, current_version = id phiên bản được chọn
 * 4. Lưu log
 */
if ($act == 'setversion') {
    // 1. Kiểm tra nếu đã là Phiên bản chính thức thì thôi, nếu chưa thì tiếp tục
    if ($version_id != $econtract['current_version']) {
        // 2. Lưu data row hiện tại vào phiên bản chính thức hiện tại
        try {
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions SET pdf_path=' . $db->quote($row_econtract['contract_path']) . ', contract_data=' . $db->quote(json_encode($row_econtract)) . ' WHERE id=' . $row_econtract['current_version']);

            // 3. Load data từ phiên bản được chọn (contract_data) ghi đè và Lưu rows, current_version = id phiên bản được chọn
            $econtract = [
                'current_version' => $version_id,
                'tax_code' => $econtract_viewing['tax_code'] ?? '',
                'c_name' => $econtract_viewing['c_name'] ?? '',
                'representative' => $econtract_viewing['representative'] ?? '',
                'jobtitle' => $econtract_viewing['jobtitle'] ?? '',
                'c_address' => $econtract_viewing['c_address'] ?? '',
                'phone' => $econtract_viewing['phone'] ?? '',
                'email' => $econtract_viewing['email'] ?? '',
                'authorization_letter' => $econtract_viewing['authorization_letter'] ?? '',
                'receiver' => $econtract_viewing['receiver'] ?? '',
                'receiver_phone' => $econtract_viewing['receiver_phone'] ?? '',
                'receiver_address' => $econtract_viewing['receiver_address'] ?? '',
                'cccd' => $econtract_viewing['cccd'] ?? '',
                'customer_type' => $econtract_viewing['customer_type'] ?? 1,
                'contract_path' => $econtract_viewing['contract_path'] ?? '',
                'status' => $econtract_viewing['status'] ?? 1,
                'stage' => $econtract_viewing['stage'] ?? 1,
                'stage_next' => $econtract_viewing['stage_next'] ?? 0,
                'uploader_id' => $econtract_viewing['uploader_id'] ?? $admin_info['userid'],
                'customer_id' => $econtract_viewing['customer_id'] ?? 0,
                'term_changed' => $econtract_viewing['term_changed'] ?? 0,
                'term_changed_notes' => $econtract_viewing['term_changed_notes'] ?? '',
                'updated_at' => NV_CURRENTTIME,
                'vips_vi' => $econtract_viewing['vips_vi'] ?? '',
                'vips_en' => $econtract_viewing['vips_en'] ?? '',
                'contract_data' => $econtract_viewing['contract_data'] ?? '',
            ];

            $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET current_version=:current_version, tax_code=:tax_code, c_name=:c_name, representative=:representative, jobtitle=:jobtitle, c_address=:c_address, phone=:phone, email=:email, authorization_letter=:authorization_letter, receiver=:receiver, receiver_phone=:receiver_phone, receiver_address=:receiver_address, cccd=:cccd, customer_type=:customer_type, contract_path=:contract_path, status=:status, stage=:stage, stage_next=:stage_next, uploader_id=:uploader_id, customer_id=:customer_id, term_changed=:term_changed, term_changed_notes=:term_changed_notes, updated_at=:updated_at, vips_vi=:vips_vi, vips_en=:vips_en, contract_data=:contract_data WHERE id=' . $id);

            // Bind data
            $stmt->bindParam(':current_version', $econtract['current_version'], PDO::PARAM_INT);
            $stmt->bindParam(':tax_code', $econtract['tax_code'], PDO::PARAM_STR);
            $stmt->bindParam(':c_name', $econtract['c_name'], PDO::PARAM_STR);
            $stmt->bindParam(':representative', $econtract['representative'], PDO::PARAM_STR);
            $stmt->bindParam(':jobtitle', $econtract['jobtitle'], PDO::PARAM_STR);
            $stmt->bindParam(':c_address', $econtract['c_address'], PDO::PARAM_STR);
            $stmt->bindParam(':phone', $econtract['phone'], PDO::PARAM_STR);
            $stmt->bindParam(':email', $econtract['email'], PDO::PARAM_STR);
            $stmt->bindParam(':authorization_letter', $econtract['authorization_letter'], PDO::PARAM_STR);
            $stmt->bindParam(':receiver', $econtract['receiver'], PDO::PARAM_STR);
            $stmt->bindParam(':receiver_phone', $econtract['receiver_phone'], PDO::PARAM_STR);
            $stmt->bindParam(':receiver_address', $econtract['receiver_address'], PDO::PARAM_STR);
            $stmt->bindParam(':cccd', $econtract['cccd'], PDO::PARAM_STR);
            $stmt->bindParam(':customer_type', $econtract['customer_type'], PDO::PARAM_INT);
            $stmt->bindParam(':contract_path', $econtract['contract_path'], PDO::PARAM_STR);
            $stmt->bindParam(':status', $econtract['status'], PDO::PARAM_INT);
            $stmt->bindParam(':stage', $econtract['stage'], PDO::PARAM_INT);
            $stmt->bindParam(':stage_next', $econtract['stage_next'], PDO::PARAM_INT);
            $stmt->bindParam(':uploader_id', $econtract['uploader_id'], PDO::PARAM_INT);
            $stmt->bindParam(':customer_id', $econtract['customer_id'], PDO::PARAM_INT);
            $stmt->bindParam(':term_changed', $econtract['term_changed'], PDO::PARAM_INT);
            $stmt->bindParam(':term_changed_notes', $econtract['term_changed_notes'], PDO::PARAM_STR);
            $stmt->bindParam(':updated_at', $econtract['updated_at'], PDO::PARAM_INT);
            $stmt->bindParam(':vips_vi', $econtract['vips_vi'], PDO::PARAM_STR);
            $stmt->bindParam(':vips_en', $econtract['vips_en'], PDO::PARAM_STR);
            $stmt->bindParam(':contract_data', $econtract['contract_data'], PDO::PARAM_STR);
            $stmt->execute();

            // 4. Lưu log
            $log_action_vi = 'Chọn <strong>' . $row_econtract['contract_no'] . '-' . str_pad($viewing_version['version'] ?? 0, 2, '0', STR_PAD_LEFT) . '</strong> làm phiên bản chính thức';
            $log_action_en = 'Set <strong>' . $row_econtract['contract_no'] . '-' . str_pad($viewing_version['version'] ?? 0, 2, '0', STR_PAD_LEFT) . '</strong> is official version';
            $db->query('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_logs (econtract_id, version_id, action, user_id, action_desc_vi, action_desc_en, log_visible, created_at) VALUES (' . $id . ', ' . $version_id . ', 0, ' . $admin_info['userid'] . ', ' . $db->quote($log_action_vi) . ', ' . $db->quote($log_action_en) . ', 1, ' . NV_CURRENTTIME . ')');
        } catch (\Throwable $th) {
            trigger_error($th->getMessage());
        }
    }
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $id . '&version=' . $econtract['current_version']);
}

// Xử lý trình lãnh đạo ký
require_once NV_ROOTDIR . '/modules/' . $module_file . '/admin/econtract_sign.php';

// Xử lý tải hợp đồng
if ($act == 'download') {
    /**
     * TODO: Xử lý tải hợp đồng
     * - Nếu có file hợp đồng thì tải về
     * - Không có file thì convert HTML sang PDF và tải
     */

    if (!empty($econtract['contract_path'])) {
        $file_path = NV_ROOTDIR . '/' . $econtract['contract_path'];
        if (file_exists($file_path)) {
            load_file($file_path);
        }
    } else {
        // Convert HTML to PDF
        export_pdf(
            load_preview_econtract($econtract, $list_order),
            $econtract['contract_no'],
            $nv_Request->isset_request('view', 'get'),
            $act
        );

        create_log_econtract([
            'econtract_id' => $econtract['id'],
            'version_id' => $econtract['current_version'],
            'action' => 1,
            'user_id' => $admin_info['userid'],
            'action_desc_vi' => 'Tải hợp đồng <strong>' . $econtract['contract_no'] . '</strong>',
            'action_desc_en' => 'Download econtract <strong>' . $econtract['contract_no'] . '</strong>',
            'changed_data' => '',
            'log_visible' => 1,
            'created_at' => NV_CURRENTTIME
        ]);
    }
} else if ($act == 'download_proposal') {
    // Đề nghị thanh toán
    if (!empty($data_cp['contract_path_dntt'])) {
        $file_path_dntt = NV_ROOTDIR . '/' . $data_cp['contract_path_dntt'];
        if (file_exists($file_path_dntt)) {
            load_file($file_path_dntt);
        }
    } else {
        export_pdf(
            load_payment_proposal($econtract, $data_vip, $_total_end, $list_order),
            $econtract['payment_proposal_no'],
            $nv_Request->isset_request('view', 'get'),
            $act
        );
    }
} else if ($act == 'download_quote') {
    // Báo giá gói phần mềm
    if (!empty($data_cp['contract_path_quote'])) {
        $file_path_quote = NV_ROOTDIR . '/' . $data_cp['contract_path_quote'];
        if (file_exists($file_path_quote)) {
            load_file($file_path_quote);
        }
    } else {
        export_pdf(
            load_price_quote($econtract, $list_order, $custom_vips, $list_info_payment),
            'Báo giá hợp đồng ' . $econtract['contract_no'],
            $nv_Request->isset_request('view', 'get'),
            $act
        );
    }
} else if ($act == 'download_purchase_order') {
    // Đơn đặt hàng
    if (!empty($data_cp['contract_path_order'])) {
        $file_path_quote = NV_ROOTDIR . '/' . $data_cp['contract_path_order'];
        if (file_exists($file_path_quote)) {
            load_file($file_path_quote);
        }
    }
} else if ($act == 'download_contract_liquidation') {
    if (!empty($data_cp['contract_path_liquidation'])) {
        $file_path_liquidation = NV_ROOTDIR . '/' . $data_cp['contract_path_liquidation'];
        if (file_exists($file_path_liquidation)) {
            load_file($file_path_liquidation);
        }
    } else {
        export_pdf(
            load_contract_liquidation($econtract),
            'Biên bản thanh lý hợp đồng ' . $econtract['contract_no'],
            $nv_Request->isset_request('view', 'get'),
            $act
        );
    }
} else if ($act == 'download_acceptance_report') {
    if (!empty($data_cp['contract_path_acceptance'])) {
        $file_path_acceptance = NV_ROOTDIR . '/' . $data_cp['contract_path_acceptance'];
        if (file_exists($file_path_acceptance)) {
            load_file($file_path_acceptance);
        } else {
            $error_upload = $nv_Lang->getModule('error_file_not_found');
        }
    } else {
        export_pdf(
            load_acceptance_report($econtract, $custom_vips),
            'Biên bản bàn giao ' . $econtract['contract_no'],
            $nv_Request->isset_request('view', 'get'),
            $act
        );
    }
} else if ($act == 'download_all') {
    download_all_files($econtract, $data_cp, $custom_vips, $list_order, $_total_end, $data_vip, $list_info_payment);
}

$error_upload = '';
$_order_data = end($orders);

// Khởi tạo thư mục upload
$array_upload = ['upload', 'upload_proposal', 'upload_price_quote', 'upload_purchase_order', 'upload_contract_liquidation', 'upload_acceptance_report'];
if (in_array($act, $array_upload)) {
    $path_to_upload_contract = create_folder_econtracts($_order_data['username']);
}

// Xử lý upload HĐ đã ký
if ($act == 'upload' && $nv_Request->isset_request('save', 'post')) {
    if (empty($_FILES['contract_path']['tmp_name'])) {
        $error_upload = $nv_Lang->getModule('error_choose_file_contract');
    } else {
        // Đổi tên file để lưu
        $_FILES['contract_path']['name'] = $econtract['id'] . '-' . NV_CURRENTTIME . '-' . $_FILES['contract_path']['name'];
        // Lưu tệp
        $upload = new NukeViet\Files\Upload();
        $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
        // Lưu file hợp đồng
        $upload_contract_info = $upload->save_file($_FILES['contract_path'], $path_to_upload_contract, false, $global_config['nv_auto_resize']);

        if (!empty($upload_contract_info['error'])) {
            $error_upload = $upload_contract_info['error'];
        } elseif (!in_array($upload_contract_info['ext'], ['pdf', 'doc', 'docx', 'zip'])) {
            $error_upload = $nv_Lang->getModule('error_file_type');
        }

        if (empty($error_upload)) {
            $econtract['contract_path'] = NV_UPLOADS_DIR . '/econtracts/' . strtolower(change_alias($_order_data['username'])) . '/' . $upload_contract_info['basename'];
            $econtract['send_email'] = false;
            // Xử lý cập nhật lại dữ liệu
            $contract_status = $nv_Request->get_int('contract_status', 'post', 0);
            switch ($contract_status) {
                case 1: // HSTDT đã ký
                    $econtract['status'] = EContractStatus::HSTDTSigned->value;
                    $econtract['hstdt_signed'] = 1;
                    $econtract['customer_signed'] = 0;
                    $econtract['stage'] = EContractStage::CustomerSignatureRequired->value;
                    $econtract['stage_next'] = EContractStage::CustomerContractReview->value;

                    // Gửi email / inform thông báo cho khách hàng: có hợp đồng cần ký
                    $econtract['send_email'] = true;
                    break;

                case 2: // 2 bên đã ký
                    $econtract['status'] = EContractStatus::Done->value;
                    $econtract['hstdt_signed'] = 1;
                    $econtract['customer_signed'] = 1;
                    $econtract['stage'] = EContractStage::Done->value;
                    $econtract['stage_next'] = EContractStage::Done->value;

                    // Gửi email / inform thông báo cho khách hàng: có hợp đồng cần ký
                    $econtract['send_email'] = true;
                    break;

                case 0: // Chưa bên nào ký
                default:
                    $econtract['hstdt_signed'] = 0;
                    $econtract['customer_signed'] = 0;
                    $econtract['status'] = EContractStatus::Incomplete->value;
                    $econtract['stage'] = EContractStage::Negotiating->value;
                    $econtract['stage_next'] = EContractStage::SupplementingInfo->value;
                    break;
            }

            try {
                $db->beginTransaction();
                // 1. Lưu hợp đồng: econtracts
                $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET contract_path=:contract_path, status=:status, stage=:stage, stage_next=:stage_next, updated_at=:updated_at, hstdt_signed=:hstdt_signed, customer_signed=:customer_signed, signing_time=:signing_time WHERE id=' . $id);
                $econtract['updated_at'] = NV_CURRENTTIME;
                $econtract['signing_time'] = NV_CURRENTTIME;
                $stmt->bindParam(':contract_path', $econtract['contract_path'], PDO::PARAM_STR);
                $stmt->bindParam(':status', $econtract['status'], PDO::PARAM_INT);
                $stmt->bindParam(':hstdt_signed', $econtract['hstdt_signed'], PDO::PARAM_INT);
                $stmt->bindParam(':customer_signed', $econtract['customer_signed'], PDO::PARAM_INT);
                $stmt->bindParam(':stage', $econtract['stage'], PDO::PARAM_INT);
                $stmt->bindParam(':stage_next', $econtract['stage_next'], PDO::PARAM_INT);
                $stmt->bindParam(':updated_at', $econtract['updated_at'], PDO::PARAM_INT);
                $stmt->bindParam(':signing_time', $econtract['signing_time'], PDO::PARAM_INT);
                $stmt->execute();

                // 2. Tải lên HĐ đã ký thì tạo phiên bản mới
                if (isset($econtract['version_data'])) {
                    unset($econtract['version_data']);
                }
                $max_code_ver = $db->query('SELECT MAX(version) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions WHERE econtract_id=' . $econtract['id'])->fetchColumn() ?: 0;
                $version_id = $db->insert_id('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions (
                    econtract_id, user_id, version, pdf_path, contract_data, created_at
                ) VALUES (
                    ' . $econtract['id'] . ',
                    ' . $admin_info['userid'] . ',
                    ' . ($max_code_ver + 1) . ',
                    ' . $db->quote($econtract['contract_path']) . ',
                    ' . $db->quote(json_encode($econtract)) . ',
                    ' . NV_CURRENTTIME . '
                )', 'id');
                if ($version_id) {
                    $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET current_version=' . $version_id . ' WHERE id=' . $econtract['id']);
                }

                // 3. Tạo log hợp đồng: econtract_logs
                $link_to_contract = NV_BASE_SITEURL . $econtract['contract_path'];
                $log_desc_vi = 'Tải lên hợp đồng đã ký: <a href="' . $link_to_contract . '" target="_blank">Hợp đồng đính kèm</a>';
                $log_desc_en = 'Upload signed contract: <a href="' . $link_to_contract . '" target="_blank">Attachment</a>';
                $db->query('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_logs (econtract_id, version_id, action, user_id, action_desc_vi, action_desc_en, log_visible, created_at) VALUES (' . $id . ', ' . $version_id . ', 1, ' . $admin_info['userid'] . ', ' . $db->quote($log_desc_vi) . ', ' . $db->quote($log_desc_en) . ', 1, ' . NV_CURRENTTIME . ')');

                // Lấy thông tin email của sale để cc
                $_ccmail = $db->query('SELECT email FROM ' . NV_USERS_GLOBALTABLE . ' where userid= ' . $econtract['uploader_id'])->fetchColumn();

                // 4. Gửi mail cho khách hàng báo HSTDT đã kí
                if ($econtract['send_email'] == true) {
                    //username
                    $username_sign = $db->query('SELECT username FROM ' . NV_USERS_GLOBALTABLE . ' where userid= ' . $econtract['customer_id'])->fetchColumn();
                    //link xem chi tiết
                    $link = SITE_ID_DOMAIN . NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['doc-econtract'] . '/' . $econtract['id'];
                    //đủ thông tin nhận bản cứng thì cc cho văn phòng

                    if (!empty($econtract['receiver']) && !empty($econtract['receiver_phone']) && !empty($econtract['receiver_address'])) {
                        $_ccmail .= ',<EMAIL>';
                    }
                    // Nội dung mail
                    $subject_sign = 'Hợp đồng ' . $econtract['contract_no'] . ' giữa Hệ sinh thái Đấu Thầu và ' . $econtract['c_name'] . ' đã hoàn tất - Yêu cầu xác nhận và ký số!';
                    $messages_sign = 'Kính gửi Quý khách hàng ' . $econtract['c_name'] . ' (tên đăng nhập: ' . $username_sign . ')!<br/><br/>';
                    $messages_sign .= 'Hệ sinh thái Đấu Thầu xin trân trọng thông báo, hợp đồng ' . nv_autoLinkDisable($econtract['contract_no']) . ' giữa Hệ sinh thái Đấu Thầu và Quý khách đã hoàn tất và sẵn sàng để ký kết.<br/><br/>';
                    $messages_sign .= 'Để tạo điều kiện thuận lợi cho Quý khách, Hệ sinh thái Đấu Thầu đã gửi kèm bản hợp đồng điện tử ngay trong email này, Quý khách vui lòng click vào link bên dưới để xem thông tin chi tiết!<br/><br/>';
                    $messages_sign .= '<div style="text-align: center;"><a href="' . $link . '" target="_blank" style="display: inline-block; background-color: #007bff; color: #fff; text-decoration: none; padding: 10px 15px; border-radius: 5px; font-size: 14px; font-weight: bold; text-align: center;">Xem chi tiết</a></div><br/><br/>';
                    $messages_sign .= 'Việc ký kết hợp đồng trực tuyến sẽ giúp tiết kiệm thời gian và đảm bảo tính bảo mật cao. Quý khách chỉ cần tải về, kiểm tra và thực hiện ký số.<br/><br/>';
                    $messages_sign .= 'Hệ sinh thái Đấu Thầu rất mong nhận được sự hợp tác của Quý khách để hợp đồng sớm có hiệu lực.<br/><br/>';
                    $messages_sign .= 'Xin trân trọng cảm ơn!</br><br/>';
                    $messages_sign .= '<em><b><u>Lưu ý:</u></b> Quý khách hàng có thể <a href="https://dauthau.asia/news/tu-lieu-cho-nha-thau/tim-hieu-ve-hop-dong-dien-tu-289.html" target="_blank">tìm hiểu về hợp đồng điện tử</a> và cách <a href="https://dauthau.asia/news/blog/thong-bao-trien-khai-hop-dong-dien-tu-de-ky-ket-va-gia-han-hop-dong-phan-mem-dauthau-info-288.html" target="_blank">triển khai hợp đồng điện tử để ký kết và gia hạn hợp đồng phần mềm DauThau.info</a> để quá trình thực hiện được dễ dàng và hiệu quả hơn.</em></br><br/>';
                    $messages_sign .= '<em>DauThau.info hiện có cung cấp chữ ký số (chứng thư số) WINCA. Khi mua chữ ký số tại DauThau.info, Quý khách sẽ được tặng điểm vào tài khoản và giảm đến 20% giá trị chứng thư số, xem chi tiết <a href="https://dauthau.asia/news/blog/tang-400-diem-va-giam-den-20-khi-mua-chung-thu-so-tai-dauthau-info-740.html" target="_blank">tại đây!</a></em></br>';
                    // Gửi mail
                    nv_pending_mail($subject_sign, $messages_sign, $econtract['email'], $_ccmail);
                }

                // 5. Gửi mail tới sale khi BLĐ đã ký
                if (!empty($_ccmail)) {
                    $link_admin = NV_MAIN_DOMAIN . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=preview&id=' . $econtract['id'];
                    // Nội dung mail
                    $subject = 'Thông báo kiểm tra hợp đồng ' . $econtract['contract_no'] . ' đã được Ban lãnh đạo ký và tải lên hệ thống';
                    $messages = 'Hệ thống gửi thông báo!<br/>';
                    $messages .= 'Kiểm tra lại file hợp đồng ' . nv_autoLinkDisable($econtract['contract_no']) . ' đã được Ban lãnh đạo ký và tải lên hệ thống.<br/>';
                    $messages .= 'Để xem thông tin chi tiết, mời nhấp vào đây: <a href="' . $link_admin . '">' . $link_admin . '</a>';
                    // Gửi mail
                    nv_pending_mail($subject, $messages, $_ccmail);

                    // Gửi thông báo tới sale khi khách hàng tải hợp đồng lên
                    nv_insert_notification($module_name, '', array(
                        'type' => 3,
                        'content' => 'Hợp dồng <strong>' . $econtract['contract_no'] . '</strong> đã được Ban lãnh đạo ký và tải lên hệ thống. Vui lòng kiểm tra lại thông tin hợp đồng!',
                    ), $econtract['id'], $econtract['uploader_id'], 0, 1, 0);
                }
                $db->commit();
                // Chuyển hướng về trang chi tiết hợp đồng
                nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $econtract['id']);
            } catch (Exception $th) {
                $error_upload = 'Đã xảy ra lỗi: ' . $th->getMessage() . '<br>' .
                    ' tại dòng ' . $th->getLine();
            }
        }
    }
} else if ($act == 'upload_proposal' && $nv_Request->isset_request('save', 'post')) {
    if (empty($_FILES['contract_path']['tmp_name'])) {
        $error_upload = $nv_Lang->getModule('error_choose_file_contract');
    } else {
        // Đổi tên file để lưu
        $_FILES['contract_path']['name'] = $econtract['id'] . '-' . NV_CURRENTTIME . '-' . $_FILES['contract_path']['name'];
        // Lưu tệp
        $upload = new NukeViet\Files\Upload();
        $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
        // Lưu file hợp đồng
        $upload_dntt_info = $upload->save_file($_FILES['contract_path'], $path_to_upload_contract, false, $global_config['nv_auto_resize']);

        if (!empty($upload_dntt_info['error'])) {
            $error_upload = $upload_dntt_info['error'];
        } elseif (!in_array($upload_dntt_info['ext'], ['pdf', 'doc', 'docx', 'zip'])) {
            $error_upload = $nv_Lang->getModule('error_file_type');
        }

        if (empty($error_upload)) {
            $contract_path_dntt = NV_UPLOADS_DIR . '/econtracts/' . strtolower(change_alias($_order_data['username'])) . '/' . $upload_dntt_info['basename'];

            $insert_additional = update_econtracts_additional($econtract['id'], $econtract['current_version'], 'contract_path_dntt', $contract_path_dntt);
            if ($insert_additional) {
                nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $econtract['id']);
            } else {
                $error_upload = 'Đã xảy ra lỗi khi tải file lên hệ thống';
            }
        }
    }
} else if ($act == 'upload_price_quote' && $nv_Request->isset_request('save', 'post')) {
    if (empty($_FILES['contract_path']['tmp_name'])) {
        $error_upload = $nv_Lang->getModule('error_choose_file_contract');
    } else {
        // Đổi tên file để lưu
        $_FILES['contract_path']['name'] = $econtract['id'] . '-' . NV_CURRENTTIME . '-' . $_FILES['contract_path']['name'];
        // Lưu tệp
        $upload = new NukeViet\Files\Upload();
        $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
        // Lưu file hợp đồng
        $upload_quote_info = $upload->save_file($_FILES['contract_path'], $path_to_upload_contract, false, $global_config['nv_auto_resize']);

        if (!empty($upload_quote_info['error'])) {
            $error_upload = $upload_quote_info['error'];
        } elseif (!in_array($upload_quote_info['ext'], ['pdf', 'doc', 'docx', 'zip'])) {
            $error_upload = $nv_Lang->getModule('error_file_type');
        }

        if (empty($error_upload)) {
            $contract_path_quote = NV_UPLOADS_DIR . '/econtracts/' . strtolower(change_alias($_order_data['username'])) . '/' . $upload_quote_info['basename'];

            $insert_additional = update_econtracts_additional($econtract['id'], $econtract['current_version'], 'contract_path_quote', $contract_path_quote);
            if ($insert_additional) {
                nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $econtract['id']);
            } else {
                $error_upload = 'Đã xảy ra lỗi khi tải file lên hệ thống';
            }
        }
    }
} else if ($act == 'upload_contract_liquidation' && $nv_Request->isset_request('save', 'post')) {
    if (empty($_FILES['contract_path']['tmp_name'])) {
        $error_upload = $nv_Lang->getModule('error_choose_file_contract');
    } else {
        // Đổi tên file để lưu
        $_FILES['contract_path']['name'] = $econtract['id'] . '-' . NV_CURRENTTIME . '-' . $_FILES['contract_path']['name'];
        // Lưu tệp
        $upload = new NukeViet\Files\Upload();
        $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
        // Lưu file hợp đồng
        $upload_liquidation_info = $upload->save_file($_FILES['contract_path'], $path_to_upload_contract, false, $global_config['nv_auto_resize']);

        if (!empty($upload_liquidation_info['error'])) {
            $error_upload = $upload_liquidation_info['error'];
        } elseif (!in_array($upload_liquidation_info['ext'], ['pdf', 'doc', 'docx', 'zip'])) {
            $error_upload = $nv_Lang->getModule('error_file_type');
        }

        if (empty($error_upload)) {
            $contract_path_liquidation = NV_UPLOADS_DIR . '/econtracts/' . strtolower(change_alias($_order_data['username'])) . '/' . $upload_liquidation_info['basename'];

            $insert_additional = update_econtracts_additional($econtract['id'], $econtract['current_version'], 'contract_path_liquidation', $contract_path_liquidation);
            if ($insert_additional) {
                nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $econtract['id']);
            } else {
                $error_upload = 'Đã xảy ra lỗi khi tải file lên hệ thống';
            }
        }
    }
} else if ($act == 'upload_acceptance_report' && $nv_Request->isset_request('save', 'post')) {
    if (empty($_FILES['contract_path']['tmp_name'])) {
        $error_upload = $nv_Lang->getModule('error_choose_file_contract');
    } else {
        // Đổi tên file để lưu
        $_FILES['contract_path']['name'] = $econtract['id'] . '-' . NV_CURRENTTIME . '-' . $_FILES['contract_path']['name'];
        // Lưu tệp
        $upload = new NukeViet\Files\Upload();
        $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
        // Lưu file hợp đồng
        $upload_acceptance_report_info = $upload->save_file($_FILES['contract_path'], $path_to_upload_contract, false, $global_config['nv_auto_resize']);

        if (!empty($upload_acceptance_report_info['error'])) {
            $error_upload = $upload_acceptance_report_info['error'];
        } elseif (!in_array($upload_acceptance_report_info['ext'], ['pdf', 'doc', 'docx', 'zip'])) {
            $error_upload = $nv_Lang->getModule('error_file_type');
        }

        if (empty($error_upload)) {
            $contract_path_acceptance_report = NV_UPLOADS_DIR . '/econtracts/' . strtolower(change_alias($_order_data['username'])) . '/' . $upload_acceptance_report_info['basename'];

            $insert_additional = update_econtracts_additional($econtract['id'], $econtract['current_version'], 'contract_path_acceptance', $contract_path_acceptance_report);
            if ($insert_additional) {
                nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $econtract['id']);
            } else {
                $error_upload = 'Đã xảy ra lỗi khi tải file lên hệ thống';
            }
        }
    }
} else if ($act == 'upload_purchase_order' && $nv_Request->isset_request('save', 'post')) {
    if (empty($_FILES['contract_path']['tmp_name'])) {
        $error_upload = $nv_Lang->getModule('error_choose_file_contract');
    } else {
        // Đổi tên file để lưu
        $_FILES['contract_path']['name'] = $econtract['id'] . '-' . NV_CURRENTTIME . '-' . $_FILES['contract_path']['name'];
        // Lưu tệp
        $upload = new NukeViet\Files\Upload();
        $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
        // Lưu file hợp đồng
        $upload_order_info = $upload->save_file($_FILES['contract_path'], $path_to_upload_contract, false, $global_config['nv_auto_resize']);

        if (!empty($upload_order_info['error'])) {
            $error_upload = $upload_order_info['error'];
        } elseif (!in_array($upload_order_info['ext'], ['pdf', 'doc', 'docx', 'zip'])) {
            $error_upload = $nv_Lang->getModule('error_file_type');
        }

        if (empty($error_upload)) {
            $contract_path_order = NV_UPLOADS_DIR . '/econtracts/' . strtolower(change_alias($_order_data['username'])) . '/' . $upload_order_info['basename'];

            $insert_additional = update_econtracts_additional($econtract['id'], $econtract['current_version'], 'contract_path_order', $contract_path_order);
            if ($insert_additional) {
                nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $econtract['id']);
            } else {
                $error_upload = 'Đã xảy ra lỗi khi tải file lên hệ thống';
            }
        }
    }
}

// Lấy danh sách nhật ký hoạt động của hợp đồng
$logs = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_logs WHERE econtract_id=' . $id . ' ORDER BY created_at DESC')->fetchAll();

// Hiển thị các thông tin sidebar bên phải
$econtract['updated_at'] = nv_datetime_format($econtract['updated_at']);

$econtract['status_label'] = EContractStatus::tryFrom($econtract['status'])?->getLabel() ?? '';
$econtract['stage_label'] = EContractStage::tryFrom($econtract['stage'])?->getLabel() ?? '';
if (!$econtract['stage'] && !$econtract['stage_next']) {
    $econtract['stage_next'] = 1;
}
$econtract['stage_next_label'] = EContractStage::tryFrom($econtract['stage_next'])?->getLabel() ?? '';

$xtpl = new XTemplate('econtract_detail.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('TEMPLATE', $global_config['module_theme']);
$xtpl->assign('OP', $op);
$xtpl->assign('TOKEN', NV_CHECK_SESSION);

// Xử lý url
$admin_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE;
$xtpl->assign('DETAIL_URL', $admin_url . '=econtract_detail&id=' . $id . '&version=' . $version_id);
$xtpl->assign('PREVIEW_URL', $admin_url . '=econtract_detail&action=preview&id=' . $id . '&version=' . $version_id);
$xtpl->assign('EDIT_URL', $admin_url . '=econtract_content&id=' . $id . '&version=' . $version_id);
$xtpl->assign('DOWNLOAD_URL', $admin_url . '=econtract_detail&action=download&id=' . $id . '&version=' . $version_id);
$xtpl->assign('UPLOAD_URL', $admin_url . '=econtract_detail&action=upload&id=' . $id . '&version=' . $version_id);
$xtpl->assign('APPROVE_URL', $admin_url . '=econtract_detail&action=approve&id=' . $id . '&version=' . $version_id);
$xtpl->assign('BACK_URL', $admin_url . '=econtract_draft');
$xtpl->assign('SETVERSION_URL', $admin_url . '=econtract_detail&action=setversion&id=' . $id . '&version=' . $version_id);
// Xử lý download file
if (!empty($order_ids)) {
    // Xử lý download nhiều file cùng lúc
    $xtpl->assign('DOWNLOAD_ALL_URL', $admin_url . '=econtract_detail&action=download_all&id=' . $id . '&version=' . $version_id);
    $actions = [
        'payment_proposal' => [
            'title_label' => 'Đề nghị thanh toán',
            'download_key' => 'contract_path_dntt',
            'link_text' => $econtract['payment_proposal_no'],
            'download_action' => 'download_proposal',
            'upload_action' => 'upload_proposal',
        ],
        'price_quote' => [
            'title_label' => 'Báo giá',
            'download_key' => 'contract_path_quote',
            'link_text' => 'Báo giá HĐ' . $econtract['contract_no'],
            'download_action' => 'download_quote',
            'upload_action' => 'upload_price_quote',
        ],
        'purchase_order' => [
            'title_label' => 'Đơn đặt hàng',
            'download_key' => 'contract_path_order',
            'link_text' => 'Đơn đặt hàng ' . $econtract['contract_no'],
            'download_action' => 'download_purchase_order',
            'upload_action' => 'upload_purchase_order',
        ],
        'contract_liquidation' => [
            'title_label' => 'Biên bản thanh lý hợp đồng',
            'download_key' => 'contract_path_liquidation',
            'link_text' => 'Biên bản thanh lý hợp đồng ' . $econtract['contract_no'],
            'download_action' => 'download_contract_liquidation',
            'upload_action' => 'upload_contract_liquidation',
        ],
        'acceptance_report' => [
            'title_label' => 'Biên bản bàn giao',
            'download_key' => 'contract_path_acceptance',
            'link_text' => 'Biên bản bàn giao ' . $econtract['contract_no'],
            'download_action' => 'download_acceptance_report',
            'upload_action' => 'upload_acceptance_report',
        ]
    ];

    foreach ($actions as $action => $conf) {
        $xtpl->assign('TITLE_LABEL', $conf['title_label']);
        $link_detail = $admin_url . '=econtract_detail&action=' . $action . '&id=' . $id . '&version=' . $version_id;
        $label_uploaded = !empty($data_cp[$conf['download_key']]) ? ' <span class="label label-info">Đã tải lên</span>' : '';
        /**
         * Trường hợp đơn đặt hàng => Tải file lên
         * - Xem thông tin file
         * - Tải file về
         */
        $exc_action = in_array($action, ['payment_proposal', 'price_quote', 'contract_liquidation', 'acceptance_report']);
        if (($action == 'purchase_order' && !empty($data_cp['contract_path_order'])) || $exc_action) {
            $xtpl->assign('TITLE_PREVEW_DETAIL', '<a href="' . $link_detail . '">' . $conf['link_text'] . '</a>' . $label_uploaded);
            $xtpl->parse('main.report_action.loop.show_title');
            $xtpl->assign('LINK_DETAIL', $admin_url . '=econtract_detail&action=' . $action . '&id=' . $id . '&version=' . $version_id);
            $xtpl->assign('LINK_DOWNLOAD', $admin_url . '=econtract_detail&action=' . $conf['download_action'] . '&id=' . $id . '&version=' . $version_id);
            $xtpl->parse('main.report_action.loop.show_action');
        }
        $xtpl->assign('LINK_UPLOAD', $admin_url . '=econtract_detail&action=' . $conf['upload_action'] . '&id=' . $id . '&version=' . $version_id);

        if ($action == 'acceptance_report') {
            $xtpl->parse('main.report_action.loop.act_edit');
        }

        $xtpl->parse('main.report_action.loop');
    }
    $xtpl->parse('main.report_action');
}

if (!empty($orders)) {
    foreach ($orders as $order) {
        if (empty($econtract['customer_id'])) {
            $econtract['customer_id'] = $order['user_id'];
        }
        $order['code'] = sprintf('BDH%010s', $order['order_id']);
        $order['url_detail'] = URL_DTINFO_ADMIN . 'index.php?language=vi&nv=bidding&op=payment&vieworderid=' . $order['order_id'];
        $xtpl->assign('ORDER', $order);
        $xtpl->parse('main.show_orders.loop_order');
    }
    $xtpl->parse('main.show_orders');
    if (!empty($custom_vips)) {
        $custom_vips_processed = group_combo_orders($custom_vips);
        foreach ($custom_vips_processed as $custom_vip) {
            if ($custom_vip['vip'] == 88) {
                $custom_vip['title_type'] = $custom_vip['type_export'] == 1 ? '- Quá khứ' : '- Hiện tại gia hạn theo năm';
            } else {
                $custom_vip['title_type'] = '';
            }
            $custom_vip['code'] = sprintf('BDH%010s', $custom_vip['order_id']);
            $xtpl->assign('ORDER_ITEM', $custom_vip);
            if ($custom_vip['rowspan'] > 0) {
                $xtpl->parse('main.view_detail.order_inlist.loop_item.price');
            }
            $xtpl->parse('main.view_detail.order_inlist.loop_item');
        }
        $xtpl->assign('TOTAL', nv_currency_format($_total));
        $xtpl->assign('DISCOUNT', nv_currency_format($_discount));
        $xtpl->assign('TOTAL_END', nv_currency_format($_total_end));
        $xtpl->parse('main.view_detail.order_inlist');
    } else {
        $xtpl->parse('main.view_detail.no_order_inlist');
    }
} elseif (!empty($econtract['merged_econtract_id'])) {
    $merged_contract_no = $db->query('SELECT contract_no FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id= ' . $econtract['merged_econtract_id'])->fetchColumn();
    $xtpl->assign('MERGE_CONTRACT_TEXT', 'Hợp đồng đã được gộp chung: <a href="' . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $econtract['merged_econtract_id'] . '">' . $merged_contract_no . '</a>');
    $xtpl->parse('main.merged_contract');
}
// Thông tin mẫu hợp đồng
$econtract['type_econtract'] = $nv_Lang->getModule('type_econtract_' . $econtract['type_econtract']);

$econtract['username'] = $econtract['fullname'] = 'N/A';
if ($econtract['customer_id']) {
    $customer = $db->query('SELECT userid, username, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $econtract['customer_id'])->fetch() ?: null;
    if ($customer) {
        $econtract['username'] = $customer['username'];
        $econtract['fullname'] = nv_show_name_user($customer['first_name'], $customer['last_name'], $customer['username']);
    }
}
$econtract['acceptance_time'] = nv_date('d/m/Y', ($econtract['acceptance_time']));
$econtract['delivery_time'] = nv_date('d/m/Y', ($econtract['delivery_time']));
$xtpl->assign('ECONTRACT', $econtract);

// Hiển thị cột trái theo chức năng
switch ($act) {
    case 'preview':
        $page_title = 'Xem trước hợp đồng: ' . $econtract['contract_no'];

        if ($type_view == 'view_draft') {
            $cache_file = 'econtract_' . $econtract['id'] . '_' . NV_LANG_DATA . '.cache';
            if (($cache_data = $nv_Cache->getItem($module_name, $cache_file)) != false) {
                $data_array = json_decode($cache_data, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $econtract = $data_array;
                    if ($nv_Request->get_int('view', 0)) {
                        export_pdf(
                            load_preview_econtract($econtract, $list_order),
                            $econtract['contract_no'],
                            $nv_Request->isset_request('view', 'get'),
                            1
                        );
                    }

                    $econtract_content_preview = '<iframe style="height: calc(100vh - 50px);" scrolling="yes" src="' . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=preview&id=' . $id . '&version=' . $version_id . '&type_view=view_draft&view=1#toolbar=0&navpanes=0" width="100%"></iframe>';
                }
            }
        } else {
            if (!empty($econtract['contract_path'])) {
                // Kiểm tra đường dẫn file
                $file_path = NV_ROOTDIR . '/' . $econtract['contract_path'];
                if (file_exists($file_path) && strtolower(pathinfo($file_path, PATHINFO_EXTENSION)) === 'pdf') {
                    $econtract_content_preview = '<iframe style="height: calc(100vh - 50px);" scrolling="yes" src="/' . $econtract['contract_path'] . '#toolbar=0&navpanes=0" width="100%"></iframe>';
                } else {
                    $econtract_content_preview = load_preview_econtract($econtract, $list_order);
                }
            }

            if (empty($econtract_content_preview)) {
                $econtract_content_preview = '<iframe style="height: calc(100vh - 50px);" scrolling="yes" src="' . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=download&id=' . $id . '&version=' . $version_id . '&view=1#toolbar=0&navpanes=0" width="100%"></iframe>';
            }
        }
        if ($econtract['current_version'] == $version_id) {
            if ($econtract['stage'] == 4) {
                $xtpl->parse('main.preview.show.show_version_actions.approve_button');
            }
            $xtpl->parse('main.preview.show.show_version_actions');
        }
        if (!empty($econtract_content_preview)) {
            if ($type_view == 'view_draft') {
                $xtpl->assign('SAVE_DRAFT', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=preview&id=' . $id . '&version=' . $version_id . '&type_view=view_draft');
                $xtpl->parse('main.preview.show.action_save_draft');
                $xtpl->parse('main.preview.show.temp');
            } else {
                $xtpl->parse('main.preview.show.show_label');
            }
            $xtpl->assign('ECONTRACT_CONTENT', $econtract_content_preview);
            $xtpl->parse('main.preview.show');
        } else {
            $xtpl->parse('main.preview.empty');
        }

        $xtpl->parse('main.preview');
        break;

    case 'upload':
    case 'upload_proposal':
    case 'upload_price_quote':
    case 'upload_purchase_order':
    case 'upload_contract_liquidation':
    case 'upload_acceptance_report':
        if ($act == 'upload') {
            $page_title_type = 'Hợp đồng: ' . $econtract['contract_no'] . ' - Tải lên HĐ đã ký';
            $link_upload_url = $admin_url . '=econtract_detail&action=upload&id=' . $id . '&version=' . $version_id;
            $link_download_url = $admin_url . '=econtract_detail&action=download&id=' . $id . '&version=' . $version_id;
            $link_preview_url = $admin_url . '=econtract_detail&action=preview&id=' . $id . '&version=' . $version_id;
        } else if ($act == 'upload_proposal') {
            $page_title_type = 'Hợp đồng: ' . $econtract['contract_no'] . ' - Tải lên Đề nghị thanh toán';
            $link_upload_url = $admin_url . '=econtract_detail&action=upload_proposal&id=' . $id . '&version=' . $version_id;
            $link_download_url = $admin_url . '=econtract_detail&action=download_proposal&id=' . $id . '&version=' . $version_id;
            $link_preview_url = $admin_url . '=econtract_detail&action=payment_proposal&id=' . $id . '&version=' . $version_id;
        } else if ($act == 'upload_price_quote') {
            $page_title_type = 'Hợp đồng: ' . $econtract['contract_no'] . ' - Tải lên Báo giá gói phần mềm';
            $link_upload_url = $admin_url . '=econtract_detail&action=upload_price_quote&id=' . $id . '&version=' . $version_id;
            $link_download_url = $admin_url . '=econtract_detail&action=download_quote&id=' . $id . '&version=' . $version_id;
            $link_preview_url = $admin_url . '=econtract_detail&action=price_quote&id=' . $id . '&version=' . $version_id;
        } else if ($act == 'upload_purchase_order') {
            $page_title_type = 'Hợp đồng: ' . $econtract['contract_no'] . ' - Đơn đặt hàng';
            $link_upload_url = $admin_url . '=econtract_detail&action=upload_purchase_order&id=' . $id . '&version=' . $version_id;
            $link_download_url = $admin_url . '=econtract_detail&action=download_purchase_order&id=' . $id . '&version=' . $version_id;
            $link_preview_url = $admin_url . '=econtract_detail&action=purchase_order&id=' . $id . '&version=' . $version_id;
        } else if ($act == 'upload_contract_liquidation') {
            $page_title_type = 'Hợp đồng: ' . $econtract['contract_no'] . ' - Biên bản thanh lý hợp đồng';
            $link_upload_url = $admin_url . '=econtract_detail&action=upload_contract_liquidation&id=' . $id . '&version=' . $version_id;
            $link_download_url = $admin_url . '=econtract_detail&action=download_contract_liquidation&id=' . $id . '&version=' . $version_id;
            $link_preview_url = $admin_url . '=econtract_detail&action=contract_liquidation&id=' . $id . '&version=' . $version_id;
        } else if ($act == 'upload_acceptance_report') {
            $page_title_type = 'Hợp đồng: ' . $econtract['contract_no'] . ' - Biên bản bàn giao';
            $link_upload_url = $admin_url . '=econtract_detail&action=upload_acceptance_report&id=' . $id . '&version=' . $version_id;
            $link_download_url = $admin_url . '=econtract_detail&action=download_acceptance_report&id=' . $id . '&version=' . $version_id;
            $link_preview_url = $admin_url . '=econtract_detail&action=acceptance_report&id=' . $id . '&version=' . $version_id;
        }

        $page_title = $page_title_type;
        $xtpl->assign('MAX_FILE_ECONTRACT_SIZE', MAX_FILE_ECONTRACT_SIZE);
        $xtpl->assign('UPLOAD_URL', $link_upload_url);
        $exc_action = in_array($act, ['upload', 'upload_proposal', 'upload_price_quote', 'upload_contract_liquidation', 'upload_acceptance_report']);
        if (($act == 'upload_purchase_order' && !empty($data_cp['contract_path_order'])) || $exc_action) {
            $xtpl->assign('DOWNLOAD_URL', $link_download_url);
            $xtpl->assign('PREVIEW_URL', $link_preview_url);
            $xtpl->parse('main.upload.show_act');
        }

        /**
         * Nếu trang upload hợp đồng:
         * - Thêm chọn trạng thái hợp đồng
         * - Thêm button Bổ sung thông tin
         * - Trình lãnh đạo ký
         */
        if ($act == 'upload') {
            $status_econtract[0] = 'Chưa ký';
            if ($econtract['customer_signed'] == 0) {
                $status_econtract[1] = 'HSTDT đã ký';
            }
            $status_econtract[2] = '2 bên đã ký';
            foreach ($status_econtract as $key => $value) {
                $xtpl->assign('STATUS', [
                    'key' => $key,
                    'value' => $value,
                ]);
                $xtpl->parse('main.upload.upload_status.status');
            }
            $xtpl->parse('main.upload.upload_status');

            $xtpl->assign('EDIT_URL', $admin_url . '=econtract_content&id=' . $id . '&version=' . $version_id);
            $xtpl->parse('main.upload.upload_btn');
        }

        if (!empty($error_upload)) {
            $xtpl->assign('ERROR', $error_upload);
            $xtpl->parse('main.upload.error');
        }

        $xtpl->parse('main.upload');
        break;
    case 'payment_proposal':
    case 'price_quote':
    case 'contract_liquidation':
    case 'acceptance_report':
        /**
         * Danh sách các mẫu tài liệu đi kèm với hợp đồng
         * Trong đó:
         * - title_suffix: Tên hiển thị trang
         * - file_path_key: Tên đường dẫn lưu file trong DB
         * - iframe_action: Tên action để load iframe
         */
        $mapActToProps = [
            'payment_proposal' => [
                'title_suffix' => 'Đề nghị thanh toán',
                'file_path_key' => 'contract_path_dntt',
                'iframe_action' => 'download_proposal'
            ],
            'price_quote' => [
                'title_suffix' => 'Báo giá gói phần mềm',
                'file_path_key' => 'contract_path_quote',
                'iframe_action' => 'download_quote'
            ],
            'purchase_order' => [
                'title_suffix' => 'Đơn hàng',
                'file_path_key' => 'contract_path_order',
                'iframe_action' => 'download_purchase_order'
            ],
            'contract_liquidation' => [
                'title_suffix' => 'Biên bản thanh lý hợp đồng',
                'file_path_key' => 'contract_path_liquidation',
                'iframe_action' => 'download_contract_liquidation'
            ],
            'acceptance_report' => [
                'title_suffix' => 'Biên bản bàn giao',
                'file_path_key' => 'contract_path_acceptance',
                'iframe_action' => 'download_acceptance_report'
            ],
        ];

        if (isset($mapActToProps[$act])) {
            $config_contract = $mapActToProps[$act];

            $page_title_type = $config_contract['title_suffix'] . ' - Hợp đồng' . $econtract['contract_no'];
            $link_preview_url = $admin_url . '=econtract_detail&action=' . $act . '&id=' . $id . '&version=' . $version_id;

            if (!empty($data_cp[$config_contract['file_path_key']])) {
                $file_path = NV_ROOTDIR . '/' . $data_cp[$config_contract['file_path_key']];
                $file_ext = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

                if (file_exists($file_path) && $file_ext === 'pdf') {
                    $data_report_document = '<iframe style="height: calc(100vh - 50px);" scrolling="yes" src="/' . $data_cp[$config_contract['file_path_key']] . '#toolbar=0&navpanes=0" width="100%"></iframe>';
                } else {
                    $data_report_document = load_iframe($config_contract['iframe_action'], $id, $version_id);
                }
            } else {
                $data_report_document = load_iframe($config_contract['iframe_action'], $id, $version_id);
            }
        }

        $page_title = $page_title_type;
        $xtpl->assign('PREVIEW_REPORT_URL', $link_preview_url);
        // Link download các mẫu đi kèm
        $xtpl->assign('DOWNLOAD_QUOTE_URL', $admin_url . '=econtract_detail&action=download_quote&id=' . $id . '&version=' . $version_id);
        $xtpl->assign('DOWNLOAD_PROPOSAL_URL', $admin_url . '=econtract_detail&action=download_proposal&id=' . $id . '&version=' . $version_id);
        if (!empty($data_cp['contract_path_order'])) {
            $xtpl->assign('DOWNLOAD_PURCHASE_ORDER_URL', $admin_url . '=econtract_detail&action=download_purchase_order&id=' . $id . '&version=' . $version_id);
            $xtpl->parse('main.report_document.act_order');
        }
        $xtpl->assign('DOWNLOAD_CONTRACT_LUIDATION_URL', $admin_url . '=econtract_detail&action=download_contract_liquidation&id=' . $id . '&version=' . $version_id);
        $xtpl->assign('DOWNLOAD_ACCEPTANCE_REPORT_URL', $admin_url . '=econtract_detail&action=download_acceptance_report&id=' . $id . '&version=' . $version_id);
        $xtpl->assign('ECONTRACT_REPORT_DOCUMENT', $data_report_document);
        $xtpl->parse('main.report_document');
        break;
    case 'detail':
    default:
        $page_title = $nv_Lang->getModule('contract') . ': ' . $econtract['contract_no'];
        if ($econtract['customer_type'] == 1) {
            if (!empty($econtract['authorization_letter'])) {
                $xtpl->parse('main.view_detail.customer_type_1.show_label_authorized');
            }
            $xtpl->parse('main.view_detail.customer_type_1');
        } else {
            $xtpl->parse('main.view_detail.customer_type_0');
        }
        if ($econtract['term_changed']) {
            $xtpl->parse('main.view_detail.term_changed');
        }

        if ($econtract['status'] != EContractStatus::Done->value && $econtract['stage'] != EContractStage::Done->value) {
            if ($econtract['current_version'] == $version_id) {
                $xtpl->parse('main.view_detail.view_detail_act.show_version_actions');
            }
            $xtpl->parse('main.view_detail.view_detail_act');
            $xtpl->parse('main.view_detail.view_detail_act_change');
        }
        $xtpl->parse('main.view_detail');
        break;
}

if ($econtract['term_changed']) {
    $xtpl->parse('main.show_label_term_changed');
}

if (!$version_id || $version_id == $econtract['current_version']) {
    $xtpl->parse('main.show_label_current_version');
} else {
    $xtpl->parse('main.show_action_choose_version');
}

// Hiển thị danh sách phiên bản
if (!empty($versions)) {
    foreach ($versions as $version) {
        $version['is_current_class'] = $version['id'] == $econtract['current_version'] ? 'current-version' : '';
        $version['is_current_label'] = $version['id'] == $econtract['current_version'] ? '<span class="label label-success">Ph.bản chính thức</span>' : '';
        $version['is_viewing_class'] = $version['id'] == $version_id ? 'viewing' : '';
        $version['is_viewing_label'] = $version['id'] == $version_id ? '<span class="label label-warning">Đang xem</span>' : '';
        $version['created_at'] = nv_datetime_format($version['created_at']);
        $version['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&id=' . $id . '&version=' . $version['id'];
        $version['code'] = $econtract['contract_no'] . '-' . str_pad($version['version'] ?? 0, 2, '0', STR_PAD_LEFT);
        $version['contract_data'] = json_decode($version['contract_data'], true);
        $version['status_label'] = '<strong>' . (EContractStatus::tryFrom($version['id'] == $econtract['current_version'] ? $econtract['status'] : $version['contract_data']['status'])?->getLabel() ?? '') . '</strong>';
        if ($version['contract_data']['term_changed']) {
            $version['status_label'] .= ' (Điều khoản hợp đồng đã thay đổi)';
        }
        // Link tệp HĐ đính kèm
        if (!empty($version['pdf_path']) && file_exists(NV_ROOTDIR . '/' . $version['pdf_path'])) {
            $version['link_file'] = NV_MY_DOMAIN . '/' . $version['pdf_path'];
        } else {
            $version['link_file'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=download&id=' . $id . '&version=' . $version['id'];
        }
        $xtpl->assign('VERSION', $version);
        $xtpl->parse('main.show_version.loop_version');
    }
    $xtpl->parse('main.show_version');
}

// Hiển thị danh sách nhật ký hoạt động
if (!empty($logs)) {
    foreach ($logs as $log) {
        $log['fullname'] = 'Hệ thống';
        if ($log['user_id']) {
            $logger = $db->query('SELECT userid, username, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $log['user_id'])->fetch() ?: null;
            if ($logger) {
                $log['fullname'] = nv_show_name_user($logger['first_name'], $logger['last_name'], $logger['username']);
            }
        }
        $log['action_desc'] = $log['action_desc_' . NV_LANG_INTERFACE] ?? '...';
        $check_log_content = json_decode($log['action_desc'], true);
        $log['action_desc'] = is_array($check_log_content) ? $check_log_content[0] : $log['action_desc'];
        $log['created_at'] = nv_datetime_format($log['created_at']);
        $xtpl->assign('LOG', $log);
        if (!empty($log['changed_data'])) {
            $log['changed_data'] = str_replace('\"', '"', $log['changed_data']);
            $log['changed_data'] = trim($log['changed_data'], "'");
            $changed_datas = json_decode($log['changed_data'], true);
            if (is_array($changed_datas)) {
                foreach ($changed_datas as $field => $value_change) {
                    $xtpl->assign('CHANGED_KEY', '- ' . $nv_Lang->getModule($field));
                    if ($field == 'contract_path') {
                        $value_change['old'] = empty($value_change['old']) ? '' : basename($value_change['old']);
                        $value_change['new'] = empty($value_change['new']) ? '' : basename($value_change['new']);
                    }
                    if ($field == 'status') {
                        $value_change['old'] = EContractStatus::tryFrom($value_change['old'])?->getLabel() ?? '';
                        $value_change['new'] = EContractStatus::tryFrom($value_change['new'])?->getLabel() ?? '';
                    }
                    if ($field == 'stage' || $field == 'stage_next') {
                        $value_change['old'] = EContractStage::tryFrom($value_change['old'])?->getLabel() ?? '';
                        $value_change['new'] = EContractStage::tryFrom($value_change['new'])?->getLabel() ?? '';
                    }
                    if ($field == 'customer_signed' || $field == 'hstdt_signed') {
                        $value_change['old'] = $value_change['old'] ? 'Đã ký' : 'Chưa ký';
                        $value_change['new'] = $value_change['new'] ? 'Đã ký' : 'Chưa ký';
                    }
                    if ($field == 'customer_type') {
                        $value_change['old'] = $value_change['old'] ? $nv_Lang->getModule('company') : $nv_Lang->getModule('individual');
                        $value_change['new'] = $value_change['new'] ? $nv_Lang->getModule('company') : $nv_Lang->getModule('individual');
                    }
                    $xtpl->assign('CHANGED_DATA', $value_change);
                    $xtpl->parse('main.show_logs.loop_log.detail_log.loop');
                }
                $xtpl->parse('main.show_logs.loop_log.detail_log');
                $xtpl->parse('main.show_logs.loop_log.detail_log_label');
            }
        }
        $xtpl->parse('main.show_logs.loop_log');
    }
    $xtpl->parse('main.show_logs');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';

function load_preview_econtract($econtract, $list_order)
{
    global $nv_Lang, $db;
    $array_data_sell = [
        'represent' => 'Phạm Đức Tiến',
        'position' => 'Giám đốc',
        'address' => 'Tầng 6, tòa nhà hỗn hợp Sông Đà, Số 131 Đường Trần Phú, Phường Văn Quán, Quận Hà Đông, Thành phố Hà Nội, Việt Nam.',
        'phone' => '024.8888.4288',
        'hotline' => '0904.634.288',
        'tax_code' => '**********',
        'account_no' => '**********',
        'in' => 'Ngân hàng TMCP Ngoại Thương Việt Nam - Chi nhánh VCB Tây HN - PGD Nam Thanh Xuân.',
        'email' => '<EMAIL>',
        'website' => 'www.dauthau.asia'
    ];

    // Danh sách link
    $array_data_link['link_term_conditions'] = '<a href="https://dauthau.asia/siteterms/terms-and-conditions.html">https://dauthau.asia/siteterms/terms-and-conditions.html</a>';
    $array_data_link['link_service'] = '<a href="https://dauthau.asia/siteterms/service-commitment.html">https://dauthau.asia/siteterms/service-commitment.html</a>';
    $array_data_link['link_privacy'] = '<a href="https://dauthau.asia/siteterms/privacy.html">https://dauthau.asia/siteterms/privacy.html</a>';
    $array_data_link['link_copyright'] = '<a href="https://dauthau.asia/siteterms/copyright.html">https://dauthau.asia/siteterms/copyright.html</a>';
    $array_data_link['link_siteterms'] = '<a href="https://dauthau.asia/siteterms/yeu-cau-he-thong-cua-phan-mem-dauthau-info.html">https://dauthau.asia/siteterms/yeu-cau-he-thong-cua-phan-mem-dauthau-info.html</a>';
    $array_data_link['link_support'] = '<a href="https://support.dauthau.net/vi/supportticket/add/">https://support.dauthau.net/vi/supportticket/add/</a>';
    $array_data_link['link_cloudflare'] = '<a href="https://developers.cloudflare.com/ssl/ssl-tls/browser-compatibility">https://developers.cloudflare.com/ssl/ssl-tls/browser-compatibility</a>';
    $array_data_link['link_huong_dan_truy_cap'] = '<a href="https://dauthau.asia/news/tin-tuc/huong-dan-khach-vip-truy-cap-dauthau-info-tu-dia-chi-ip-nuoc-ngoai-863.html">tại đây</a>';
    $array_data_link['link_nukeviet'] = '<a href="https://www.nukeviet.vn/">www.nukeviet.vn</a>';
    $array_data_link['link_msc'] = '<a href="http://muasamcong.mpi.gov.vn">http://muasamcong.mpi.gov.vn</a>';
    $array_data_link['link_tra_cuu'] = '<a href="http://tracuunnt.gdt.gov.vn">http://tracuunnt.gdt.gov.vn</a>';
    $array_data_link['link_bang_gia'] = '<a href="https://dauthau.asia/page/bang-gia.html">https://dauthau.asia/page/bang-gia.html</a>';
    $array_data_link['link_bo_luat_dan_su'] = '<a href="https://thuvienphapluat.vn/van-ban/Quyen-dan-su/Bo-luat-dan-su-2015-296215.aspx">Bộ luật dân sự</a>';
    $array_data_link['link_dang_ky_kinh_doanh'] = '<a href="www.dangkykinhdoanh.gov.vn">www.dangkykinhdoanh.gov.vn</a>';
    $array_data_link['link_yc_khong_su_dung_phan_mem'] = '<a href="https://dauthau.asia/news/blog/dauthau-info-ngung-quet-du-lieu-366.html">yêu cầu từ Bộ KH&ĐT về việc không được sử dụng phần mềm để tiếp cận</a>';
    $array_data_link['link_info_tiet_lo_bi_mat_cong_nghe'] = '<a href="https://dauthau.asia/news/tu-lieu-cho-nha-thau/dauthau-info-tiet-lo-bi-mat-cong-nghe-nhap-lieu-tu-dong-498.html">truy cập thủ công trước khi được xử lý tự động</a>';

    // Danh sách liên hệ
    $array_contact = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ];

    $econtract['c_name'] = mb_strtoupper($econtract['c_name'], 'UTF-8');
    // Lấy thời gian hiện tại cho gần ngày với ngày ký hợp đồng
    $econtract['create_time'] = 'ngày ' . date('d') . ' tháng ' . date('m') . ' năm ' . date('Y');
    $contract_data = json_decode($econtract['contract_data'] ?? '[]', true);
    $econtract['vip'] = $contract_data['data_vip']['vip'] ?? '';
    $econtract['money'] = empty($contract_data['total_payment']) ? '0 VNĐ' : str_replace('đ', ' VNĐ', nv_currency_format($contract_data['total_payment']));
    $econtract['money_text'] = numberToWords($contract_data['total_payment'] ?? 0);
    $econtract['content'] = $contract_data['content'] ?? '';
    $econtract['contract_time'] = $contract_data['data_vip']['year'] ?? '';
    $econtract['content'] = $list_order;

    if ($econtract['type_econtract'] == 0) {
        $xtpl = new XTemplate('econtract.tpl', NV_ROOTDIR . '/assets/tpl'); // Hợp đồng đầy đủ
    } else {
        $xtpl = new XTemplate('econtract_short.tpl', NV_ROOTDIR . '/assets/tpl'); // Hợp dồng thu gọn
    }
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('ECONTRACT_DATA', $econtract);
    $xtpl->assign('ECONTRACT_DATA_SELL', $array_data_sell);
    $xtpl->assign('ECONTRACT_DATA_LINK', $array_data_link);
    if (!empty($array_contact)) {
        $links = [];
        foreach ($array_contact as $contact) {
            $links[] = '<a href="mailto:' . $contact . '">' . $contact . '</a>';
        }
        if (count($links) == 1) {
            $array_contact = $links[0];
        } elseif (count($links) == 2) {
            $array_contact = implode(' và ', $links);
        } else {
            $array_contact = implode(', ', array_slice($links, 0, -1)) . ' và ' . end($links);
        }
        $xtpl->assign('LIST_CONTACT', $array_contact);
        $xtpl->parse('main.list_contact');
        $xtpl->parse('main.list_contact_e');
        $xtpl->parse('main.list_contact_buy');
    }

    if ($econtract['customer_type'] == 1 && !empty($econtract['representative'])) {
        $xtpl->assign('REPRESENTATIVE', $econtract['representative']);
        $xtpl->parse('main.customer_representative');
    }

    if ($econtract['customer_type'] == 1 && !empty($econtract['jobtitle'])) {
        $xtpl->assign('JOBTITLE', $econtract['jobtitle']);
        $xtpl->parse('main.customer_jobtitle');
    }

    if ($econtract['customer_type'] == 1 && !empty($econtract['authorization_letter'])) {
        $xtpl->assign('AUTHORIZATION_LETTER', $nv_Lang->getModule('customer_authorization_letter', $econtract['authorization_letter']));
        $xtpl->parse('main.customer_authorization_letter');
    }

    if ($econtract['customer_type'] == 1 && !empty($econtract['tax_code'])) {
        $xtpl->assign('TAX_CODE', $econtract['tax_code']);
        $xtpl->parse('main.customer_type_1');
    }

    if ($econtract['customer_type'] == 0 && !empty($econtract['cccd'])) {
        $xtpl->assign('CCCD', $econtract['cccd']);
        $xtpl->parse('main.customer_type_0');
    }

    if (!empty($econtract['bank_account'])) {
        $xtpl->parse('main.bank_account');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function load_payment_proposal($econtract, $data_vip, $_total_end, $list_order)
{
    global $global_config, $module_file;

    $xtpl = new XTemplate('econtract_payment_proposal.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);

    $location_time = 'Hà Nội, ngày ' . date('d') . ' tháng ' . date('m') . ' năm ' . date('Y');

    // Xử lý hiển thị dữ liệu
    $list_vip = '';
    if (!empty($data_vip)) {
        $list_vip = convert_data_vip($data_vip)['vip'];
    }
    $xtpl->assign('ECONTRACT', $econtract);
    $xtpl->assign('LOCATION_TIME', $location_time);
    $xtpl->assign('LIST_VIP', $list_vip);
    $xtpl->assign('TOTAL_PAYMENT', str_replace('đ', ' VNĐ', nv_currency_format($_total_end)));
    $xtpl->assign('LIST_ORDER', $list_order);

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function load_price_quote($econtract, $list_order, $custom_vips, $list_info_payment)
{
    global $global_config, $module_file, $db, $nv_Lang, $all_array_user_id_users;

    $xtpl = new XTemplate('econtract_price_quote.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('ECONTRACT', $econtract);
    $xtpl->assign('LIST_ORDER', $list_order);
    $xtpl->assign('LIST_INFO_PAYMENT', $list_info_payment);

    $location_time = 'Hà Nội, ngày ' . date('d') . ' tháng ' . date('m') . ' năm ' . date('Y');
    $xtpl->assign('LOCATION_TIME', $location_time);

    // Lấy thông tin của sale
    if ($econtract['uploader_id'] != 0) {
        $uploader = $all_array_user_id_users[$econtract['uploader_id']];
        $sale_name = nv_show_name_user($uploader['first_name'], $uploader['last_name'], $uploader['userid']);
        $sale_phone = $db->query('SELECT phone FROM ' . NV_USERS_GLOBALTABLE . '_info WHERE userid=' . $econtract['uploader_id'])->fetchColumn();
        $contract_sale = $nv_Lang->getModule('econtract_sale_name', $sale_phone, $sale_name);
        $xtpl->assign('SALE_CONTRACT', $contract_sale);
        $xtpl->parse('main.sale_contract');
    }

    // Lấy báo giá từ đơn hàng
    $custom_vip_prices = [];
    foreach ($custom_vips as $c_vip) {
        if (!empty($c_vip['vip']) && !empty($c_vip['vip_price'])) {
            $custom_vip_prices[$c_vip['vip']] = $c_vip['vip_price'];
        }
    }

    // Lấy config giá
    $params = [
        'name' => 'bidding',
        'site' => 'dauthau.info'
    ];
    $api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
    $api->setLang('vi')
        ->setAction('ListModuleConfig')
        ->setData($params);
    $result_api = $api->execute();

    if ($result_api['status'] == 'success') {
        foreach ($result_api['data'] as $k => $v) {
            if (strpos($k, 'price_vip') === 0) {
                $check_vip[] = [
                    'config_name' => $k,
                    'config_value' => $v
                ];
            }
        }
    }
    // Xử lý hiển thị báo giá gói vip
    $list_vip_econtract = array_column($custom_vips, 'vip');

    $vip_map = [
        99 => 'main.loop_vip.vip99',
        19 => 'main.loop_vip.vip19',
        1  => 'main.loop_vip.vip1_2',
        2  => 'main.loop_vip.vip1_2',
        7  => 'main.loop_vip.vip7',
        8  => 'main.loop_vip.vip8',
        11 => 'main.loop_vip.vip1_2_qt',
        21 => 'main.loop_vip.vip1_2_qt',
        5  => 'main.loop_vip.vip5',
        3  => 'main.loop_vip.vip3',
        31 => 'main.loop_vip.vip31',
        88 => 'main.loop_vip.vip88',
        89 => 'main.loop_vip.vip89',
        101 => 'main.loop_vip.vip101',
        77 => 'main.loop_vip.vip77',
        100 => 'main.loop_vip.vip100',
        32 => 'main.loop_vip.vip32',
        66 => 'main.loop_vip.vip66_68',
        68 => 'main.loop_vip.vip66_68',
        'x3' => 'main.loop_vip.vipx3',
        4  => 'main.loop_vip.vip4',
        69 => 'main.loop_vip.vip69',
        6  => 'main.loop_vip.vip6',
    ];

    $vip_filtered = [];
    $vip_needed = [
        'price_vip99',
        'price_vip19',
        'price_vip1',
        'price_vip2',
        'price_vip7',
        'price_vip8',
        'price_vip11',
        'price_vip21',
        'price_vip5',
        'price_vip3',
        'price_vip31',
        'price_vip77',
        'price_vip32',
        'price_vip66',
        'price_vip68',
        'price_vip4',
        'price_vip69',
        'price_vip6',
    ];

    $vip_88_keys = [
        'price_vip88_month',
        'price_vip88_old',
        'price_vip88_year',
        'price_vip88_vip12_month',
        'price_vip88_vip12_old',
        'price_vip88_vip12_year',
        'price_vip88_vip7_old',
        'price_vip88_vip7_month',
        'price_vip88_vip7_year'
    ];

    $vip_100_keys = [
        'price_vip100',
        'price_vip3_100'
    ];

    $vip_101_keys = [
        'price_vip101',
        'price_vip12_101',
        'price_vip7_101'
    ];

    foreach ($check_vip as $vip_data) {
        $config_name = $vip_data['config_name'];
        $config_value = $vip_data['config_value'];

        // Xử lý các VIP thông thường
        if (in_array($config_name, $vip_needed)) {
            if (preg_match('/price_vip(\d+)/', $config_name, $matches)) {
                $vip_id = (int) $matches[1];
                $vip_filtered[$vip_id] = $config_value;
            }
        } else if (in_array($config_name, $vip_88_keys)) {
            $vip_filtered[88][$config_name] = $config_value;
        } else if (in_array($config_name, $vip_100_keys)) {
            $vip_filtered[100][$config_name] = $config_value;
        } else if (in_array($config_name, $vip_101_keys)) {
            $vip_filtered[101][$config_name] = $config_value;
        }
    }

    // Xử lý hiển thị báo giá theo từng gói vip
    $parsed_templates = [];
    $iconCheck = 'data:image/png;base64,' . base64_encode(file_get_contents(NV_ROOTDIR . '/assets/images/check.png'));

    foreach ($list_vip_econtract as $vip) {
        if (isset($vip_map[$vip]) && !in_array($vip_map[$vip], $parsed_templates)) {
            if ($vip == 1 || $vip == 2) {
                $xtpl->assign('ICON_CHECK', $iconCheck);
                $xtpl->assign('VIP_PRICE1', number_format(isset($custom_vip_prices[1]) ? $custom_vip_prices[1] : $vip_filtered[1], 0, ',', '.'));
                $xtpl->assign('VIP_PRICE2', number_format(isset($custom_vip_prices[2]) ? $custom_vip_prices[2] : $vip_filtered[2], 0, ',', '.'));
            } else if ($vip == 66 || $vip == 68) {
                $xtpl->assign('ICON_CHECK', $iconCheck);
                $xtpl->assign('VIP_PRICE66', number_format(isset($custom_vip_prices[66]) ? $custom_vip_prices[66] : $vip_filtered[66], 0, ',', '.'));
                $xtpl->assign('VIP_PRICE68', number_format(isset($custom_vip_prices[68]) ? $custom_vip_prices[68] : $vip_filtered[68], 0, ',', '.'));
            } else if ($vip == 88) {
                $xtpl->assign('VIP_PRICE88_12', number_format($vip_filtered[88]['price_vip88_vip12_old'], 0, ',', '.'));
                $xtpl->assign('VIP_PRICE88_12_MONTH', number_format($vip_filtered[88]['price_vip88_vip12_month'], 0, ',', '.'));
                $xtpl->assign('VIP_PRICE88_12_YEAR', number_format($vip_filtered[88]['price_vip88_vip12_year'], 0, ',', '.'));
                $xtpl->assign('VIP_PRICE88_7', number_format($vip_filtered[88]['price_vip88_vip7_old'], 0, ',', '.'));
                $xtpl->assign('VIP_PRICE88_7_MONTH', number_format($vip_filtered[88]['price_vip88_vip7_month'], 0, ',', '.'));
                $xtpl->assign('VIP_PRICE88_7_YEAR', number_format($vip_filtered[88]['price_vip88_vip7_year'], 0, ',', '.'));
                $xtpl->assign('VIP_PRICE88', number_format($vip_filtered[88]['price_vip88_old'], 0, ',', '.'));
                $xtpl->assign('VIP_PRICE88_MONTH', number_format($vip_filtered[88]['price_vip88_month'], 0, ',', '.'));
                $xtpl->assign('VIP_PRICE88_YEAR', number_format($vip_filtered[88]['price_vip88_year'], 0, ',', '.'));
            } else if ($vip == 100) {
                $xtpl->assign('VIP_PRICE100', number_format($vip_filtered[100]['price_vip100'], 0, ',', '.'));
                $xtpl->assign('VIP_PRICE100_3', number_format($vip_filtered[100]['price_vip3_100'], 0, ',', '.'));
            } else if ($vip == 101) {
                $xtpl->assign('VIP_PRICE101', number_format($vip_filtered[101]['price_vip101'], 0, ',', '.'));
                $xtpl->assign('VIP_PRICE101_1_2', number_format($vip_filtered[101]['price_vip12_101'], 0, ',', '.'));
                $xtpl->assign('VIP_PRICE101_7', number_format($vip_filtered[101]['price_vip7_101'], 0, ',', '.'));
            } else if ($vip == 11 || $vip == 21) {
                $xtpl->assign('VIP_PRICE11', number_format(isset($custom_vip_prices[11]) ? $custom_vip_prices[11] : $vip_filtered[11], 0, ',', '.'));
                $xtpl->assign('VIP_PRICE21', number_format(isset($custom_vip_prices[21]) ? $custom_vip_prices[21] : $vip_filtered[21], 0, ',', '.'));
            } else {
                $xtpl->assign('VIP_PRICE', number_format(isset($custom_vip_prices[$vip]) ? $custom_vip_prices[$vip] : $vip_filtered[$vip], 0, ',', '.'));
            }
            $xtpl->parse($vip_map[$vip]);
            $parsed_templates[] = $vip_map[$vip];
        }
        $xtpl->parse('main.loop_vip');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function export_pdf($html, $filename, $type_view, $data_act)
{
    global $global_config;

    // Cấu hình xuất file pdf
    $config_mpdf = [
        'fontdata' => [
            'timesnewroman' => [
                'R' => 'SVN-Times-New-Roman.ttf',    // Regular
                'B' => 'SVN-Times-New-Roman-Bold.ttf',  // Bold
                'I' => 'SVN-Times-New-Roman-Italic.ttf',   // Italic
                'BI' => 'SVN-Times-New-Roman-Bold-Italic.ttf', // Bold Italic
            ],
        ],
        'default_font' => 'timesnewroman',
    ];

    if ($data_act == 'download_quote') {
        $config_mpdf = array_merge($config_mpdf, [
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_top' => 0,
            'margin_bottom' => 0,
            'margin_left' => 15,
            'margin_right' => 15,
        ]);
    }

    $mpdf = new \Mpdf\Mpdf($config_mpdf);

    // Thiết lập số trang ở footer
    if ($data_act == 'download') {
        $mpdf->defaultfooterline = false;
        $mpdf->AddPage('', '', '', '', 'off');

        $footer = '<div style="text-align: right; font-size: 10pt; font-family: \'Times New Roman\', sans-serif; color: grey;">
            {PAGENO}
        </div>';

        $mpdf->setFooter($footer);
    } else if ($data_act == 'download_quote') {
        $headerImagePath = NV_ROOTDIR . '/' . NV_ASSETS_DIR . '/images/header-bg-DauThau.info.jpg';
        $footerImagePath = NV_ROOTDIR . '/' . NV_ASSETS_DIR . '/images/footer-bg-DauThau.info.jpg';

        $mpdf->SetHTMLHeader('
            <div style="position: absolute; top: 0; left: 0; width: 100%; text-align: center;">
                <img src="' . $headerImagePath . '" style="width: 100%; height: auto; display: block;" />
            </div>
        ');

        $mpdf->SetHTMLFooter('
            <div style="position: absolute; bottom: 0; left: 0; width: 100%; text-align: center;">
                <img src="' . $footerImagePath . '" style="width: 100%; height: auto; display: block;" />
            </div>
        ');

        $mpdf->SetMargins(20, 20, 30);
        $mpdf->SetAutoPageBreak(true, 25);
    } else if ($data_act == 'download_contract_liquidation') {
        $mpdf->defaultfooterline = false;
        $mpdf->AddPage('', '', '', '', 'off');
        $footer = '<div style="text-align: center; font-size: 10pt; font-family: \'Times New Roman\', sans-serif; color: black;">
            {PAGENO}
        </div>';
        $mpdf->setFooter($footer);
    }

    $mpdf->WriteHTML($html);
    if ($type_view) {
        $mpdf->Output();
    } else {
        $mpdf->Output($filename . '.pdf', 'D');
    }
}

function create_folder_econtracts($customer_name)
{
    $base = NV_UPLOADS_REAL_DIR . '/econtracts';
    $alias = strtolower(change_alias($customer_name));
    $fullPath = $base . '/' . $alias;

    if (!is_dir($base)) {
        nv_mkdir(NV_UPLOADS_REAL_DIR, 'econtracts');
    }
    if (!is_dir($fullPath)) {
        nv_mkdir($base, $alias);
    }

    return $fullPath;
}

function load_iframe($action, $id, $version_id)
{
    global $module_name;
    $link = '<iframe style="height: calc(100vh - 50px);" scrolling="yes" src="' . NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=econtract_detail&action=' . $action . '&id=' . $id . '&version=' . $version_id . '&view=1#toolbar=0&navpanes=0" width="100%"></iframe>';
    return $link;
}

function load_contract_liquidation($econtract)
{
    global $global_config, $module_file, $nv_Lang;

    $econtract['c_name'] = mb_strtoupper($econtract['c_name'], 'UTF-8');
    $contract_data = json_decode($econtract['contract_data'] ?? '[]', true);
    $econtract['vip'] = $contract_data['data_vip']['vip'] ?? '';
    $econtract['money'] = empty($contract_data['total_payment']) ? '0 VNĐ' : str_replace('đ', ' VNĐ', nv_currency_format($contract_data['total_payment']));
    $econtract['money_text'] = numberToWords($contract_data['total_payment'] ?? 0);
    $econtract['content'] = $contract_data['content'] ?? '';
    $econtract['contract_time'] = $contract_data['data_vip']['year'] ?? '';

    $xtpl = new XTemplate('contract_liquidation_minutes.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file . '/docs/');
    $xtpl->assign('ECONTRACT', $econtract);

    // Ngày ký hợp đồng
    $contract_time = date('d/m/Y');
    $xtpl->assign('CONTRACT_TIME', $contract_time);

    $location_time = 'Hôm nay, ngày ' . date('d') . ' tháng ' . date('m') . ' năm ' . date('Y') . ', chúng tôi gồm:';
    $xtpl->assign('LOCATION_TIME', $location_time);

    if ($econtract['customer_type'] == 1 && !empty($econtract['representative'])) {
        $xtpl->assign('REPRESENTATIVE', $econtract['representative']);
        $xtpl->parse('main.customer_representative');
    }

    if ($econtract['customer_type'] == 1 && !empty($econtract['jobtitle'])) {
        $xtpl->assign('JOBTITLE', $econtract['jobtitle']);
        $xtpl->parse('main.customer_jobtitle');
    }

    if ($econtract['customer_type'] == 1 && !empty($econtract['authorization_letter'])) {
        $xtpl->assign('AUTHORIZATION_LETTER', $nv_Lang->getModule('customer_authorization_letter', $econtract['authorization_letter']));
        $xtpl->parse('main.customer_authorization_letter');
    }

    if ($econtract['customer_type'] == 1 && !empty($econtract['tax_code'])) {
        $xtpl->assign('TAX_CODE', $econtract['tax_code']);
        $xtpl->parse('main.customer_type_1');
    }

    if ($econtract['customer_type'] == 0 && !empty($econtract['cccd'])) {
        $xtpl->assign('CCCD', $econtract['cccd']);
        $xtpl->parse('main.customer_type_0');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function load_acceptance_report($econtract, $custom_vips)
{
    global $global_config, $module_file, $nv_Lang;

    $xtpl = new XTemplate('acceptance_report.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file . '/docs/');
    $econtract['c_name'] = mb_strtoupper($econtract['c_name'], 'UTF-8');
    $xtpl->assign('ECONTRACT', $econtract);

    $location_time = 'Hà Nội, ngày ' . date('d') . ' tháng ' . date('m') . ' năm ' . date('Y') . ', chúng tôi gồm:';
    $xtpl->assign('LOCATION_TIME', $location_time);
    // Ngày ký hợp đồng
    $contract_time = nv_date('d/m/Y', $econtract['signing_time']);;
    $xtpl->assign('CONTRACT_TIME', $contract_time);

    if ($econtract['customer_type'] == 1 && !empty($econtract['representative'])) {
        $xtpl->assign('REPRESENTATIVE', $econtract['representative']);
        $xtpl->parse('main.customer_representative');
    }

    if ($econtract['customer_type'] == 1 && !empty($econtract['jobtitle'])) {
        $xtpl->assign('JOBTITLE', $econtract['jobtitle']);
        $xtpl->parse('main.customer_jobtitle');
    }

    if ($econtract['customer_type'] == 1 && !empty($econtract['authorization_letter'])) {
        $xtpl->assign('AUTHORIZATION_LETTER', $nv_Lang->getModule('customer_authorization_letter', $econtract['authorization_letter']));
        $xtpl->parse('main.customer_authorization_letter');
    }

    if ($econtract['customer_type'] == 1 && !empty($econtract['tax_code'])) {
        $xtpl->assign('TAX_CODE', $econtract['tax_code']);
        $xtpl->parse('main.customer_type_1');
    }

    if ($econtract['customer_type'] == 0 && !empty($econtract['cccd'])) {
        $xtpl->assign('CCCD', $econtract['cccd']);
        $xtpl->parse('main.customer_type_0');
    }

    // Thời gian bàn giao
    $time_acceptance = nv_date('d/m/Y', $econtract['acceptance_time']);
    $xtpl->assign('TIME_ACCEPTANCE', $time_acceptance);

    // Tính năng gói vip
    $list_title_sufffix = 'Tính năng đi kèm của gói vip';
    $account_type = 'Tài khoản thường';
    if (!empty($custom_vips)) {
        $account_type = 'Tài khoản VIP';
        $list_title_sufffix = [];
        $list_vip_econtract = array_column($custom_vips, 'vip');
        foreach ($list_vip_econtract as $vip) {
            switch ($vip) {
                case 99:
                    $title_suffix = '<strong>VIEWEB</strong> - Tra cứu thông tin thầu online';
                    break;
                case 19:
                    $title_suffix = '<strong>PRO1</strong> - Săn thông tin thầu cơ bản';
                    break;
                case 1:
                    $title_suffix = '<strong>VIP1</strong> - "Săn" thông báo mời thầu mua sắm công và mua sắm tư nhân';
                    break;
                case 2;
                    $title_suffix = '<strong>VIP2</strong> - "Săn" kế hoạch lựa chọn nhà thầu mua sắm công và mua sắm tư nhân';
                    break;
                case 7:
                    $title_suffix = '<strong>VIP7</strong> - "Săn" kết quả lựa chọn nhà thầu';
                    break;
                case 8:
                    $title_suffix = '<strong>VIP8</strong> - "Săn" yêu cầu báo giá mua sắm công';
                    break;
                case 11:
                    $title_suffix = '<strong>VIP1QT</strong> - "Săn" thông báo mời thầu quốc tế tại Việt Nam';
                    break;
                case 21:
                    $title_suffix = '<strong>VIP2QT</strong> - "Săn" kế hoạch mời thầu quốc tế tại Việt Nam';
                    break;
                case 5:
                    $title_suffix = '<strong>VIP5</strong> - "Săn" gói thầu có nguồn vốn ngoài phạm vi điều chỉnh của luật đấu thầu (WB, ODA, ADB, UNICEF, UNDP...)';
                    break;
                case 3:
                    $title_suffix = '<strong>VIP3</strong> - Đọc vị đối thủ cạnh tranh và bên mời thầu';
                    break;
                case 31:
                    $title_suffix = '<strong>VIP 3 Plus</strong> - Tải không giới hạn file báo cáo PDF của các nhà thầu';
                    break;
                case 88:
                    $title_suffix = '<strong>X1</strong> - Xuất kết quả đấu thầu ra excel';
                    break;
                case 89:
                    $title_suffix = '<strong>X2</strong> - Xuất dữ liệu nhà thầu ra file Excel';
                    break;
                case 101:
                    $title_suffix = '<strong>X4</strong> - Trích xuất dữ liệu hàng hóa mua sắm công ra file Excel';
                    break;
                case 77:
                    $title_suffix = '<strong>T0</strong> - Tải không giới hạn hồ sơ mời thầu';
                    break;
                case 100:
                    $title_suffix = '<strong>T100</strong> - Tải 100 báo cáo PDF "Kết quả hoạt động của doanh nghiệp tại thị trường Mua sắm công';
                    break;
                case 32:
                    $title_suffix = '<strong>PLP Report</strong> - Báo cáo kết quả hoạt động kinh doanh trên thị trường mua sắm công của các công ty niêm yết trên thị trường chứng khoán';
                    break;
                case 66:
                    $title_suffix = '<strong>API VIP</strong> - Phần mềm truy vấn dữ liệu thầu theo thời gian thực qua giao diện lập trình ứng dụng';
                    break;
                case 68:
                    $title_suffix = '<strong>API PRO</strong> - Phần mềm truy vấn dữ liệu thầu theo thời gian thực qua giao diện lập trình ứng dụng';
                    break;
                case 4:
                    $title_suffix = '<strong>VIP4</strong> - Trợ lý số cho nhà đầu tư thời đại 4.0';
                    break;
                case 69:
                    $title_suffix = '<strong>SIEUVIP</strong> - Tất cả trong 1 (VIP1 + VIP2 + VIP3 + X1 + T0 + VIP8)';
                    break;
                case 6:
                    $title_suffix = '<strong>VIP6</strong> -  Phần mềm Săn tài sản Đấu Giá';
                    break;
                default:
                    $title_suffix = '';
                    break;
            }
            $list_title_sufffix[] = $title_suffix;
        }
        if (count($list_title_sufffix) == 1) {
            $list_title_sufffix = $list_title_sufffix[0];
        } elseif (count($list_title_sufffix) == 2) {
            $list_title_sufffix = implode(' và ', $list_title_sufffix);
        } else {
            $list_title_sufffix = implode(', ', array_slice($list_title_sufffix, 0, -1)) . ' và ' . end($list_title_sufffix);
        }
    }
    $xtpl->assign('ACCOUNT_TYPE', $account_type);
    $xtpl->assign('VIP_PACKAGE_SUFFIX', $list_title_sufffix);

    $xtpl->parse('main');
    return $xtpl->text('main');
}

function update_econtracts_additional($econtract_id, $version_id, $field_update, $file_path)
{
    global $db, $module_data, $admin_info;

    // Kiểm tra tồn tại
    $check_econtract = $db->query('SELECT id, ' . $field_update . ' FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts_additional WHERE econtract_id=' . $econtract_id)->fetch();

    if ($check_econtract) {
        $stmt_update = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts_additional SET ' . $field_update . '=:' . $field_update . ', updated_at=:updated_at WHERE id=' . $check_econtract['id']);
        $stmt_update->bindValue(':updated_at', NV_CURRENTTIME, PDO::PARAM_INT);
    } else {
        $stmt_update = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts_additional (econtract_id, ' . $field_update . ', created_at) VALUES (:econtract_id, :' . $field_update . ', :created_at)');
        $stmt_update->bindValue(':econtract_id', $econtract_id, PDO::PARAM_INT);
        $stmt_update->bindValue(':created_at', NV_CURRENTTIME, PDO::PARAM_INT);
    }
    $stmt_update->bindParam(':' . $field_update . '', $file_path, PDO::PARAM_STR);
    $exc_update = $stmt_update->execute();


    if ($exc_update) {
        $link_path = NV_BASE_SITEURL . $file_path;

        switch ($field_update) {
            case 'contract_path_dntt':
                $title_vi = 'Tải lên đề nghị thanh toán đã ký: <a href="' . $link_path . '" target="_blank">Hợp đồng đính kèm</a>';
                $title_en = 'Upload signed payment request: <a href="' . $link_path . '" target="_blank">Attachment</a>';
                break;
            case 'contract_path_quote':
                $title_vi = 'Tải lên báo giá: <a href="' . $link_path . '" target="_blank">Hợp đồng đính kèm</a>';
                $title_en = 'Upload quotation: <a href="' . $link_path . '" target="_blank">Attachment</a>';
                break;
            case 'contract_path_order':
                $title_vi = 'Tải lên đơn hàng: <a href="' . $link_path . '" target="_blank">Hợp đồng đính kèm</a>';
                $title_en = 'Upload order: <a href="' . $link_path . '" target="_blank">Attachment</a>';
                break;
            case 'contract_path_acceptance':
                $title_vi = 'Tải lên biên bản nghiệm thu: <a href="' . $link_path . '" target="_blank">Hợp đồng đính kèm</a>';
                $title_en = 'Upload acceptance minutes: <a href="' . $link_path . '" target="_blank">Attachment</a>';
                break;
            case 'contract_path_liquidation':
                $title_vi = 'Tải lên biên bản thanh lý hợp đồng: <a href="' . $link_path . '" target="_blank">Hợp đồng đính kèm</a>';
                $title_en = 'Upload contract liquidation minutes: <a href="' . $link_path . '" target="_blank">Attachment</a>';
                break;
            default:
                break;
        }

        create_log_econtract([
            'econtract_id' => $econtract_id,
            'version_id' => $version_id,
            'action' => 1,
            'user_id' => $admin_info['userid'],
            'action_desc_vi' => $title_vi,
            'action_desc_en' => $title_en,
            'changed_data' => '',
            'log_visible' => 1,
            'created_at' => NV_CURRENTTIME
        ]);
        return true;
    }
    return false;
}

function load_file($file_path)
{
    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . basename($file_path) . '"');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($file_path));
    flush(); // Flush system output buffer
    readfile($file_path);
    header('Location: ' . $_SERVER['HTTP_REFERER']);
    exit; // Dừng lại sau khi tải xong
}

function download_all_files($econtract, $data_cp, $custom_vips, $list_order, $_total_end, $data_vip, $list_info_payment)
{
    // Create temporary directory
    $base = NV_UPLOADS_REAL_DIR . '/econtracts';
    if (!is_dir($base)) {
        nv_mkdir(NV_UPLOADS_REAL_DIR, 'econtracts');
    }

    $temp_dir = $base . '/' . $econtract['id'];
    if (!is_dir($temp_dir)) {
        // tạo thư mục tạm, không có index.html
        mkdir($temp_dir, 0777, true);
        if (!is_writable($temp_dir)) {
            @chmod($temp_dir, 0777);
        }
    }

    $files_to_zip = [];

    // Xử lý đề nghị thanh toán
    if (!empty($data_cp['contract_path_dntt'])) {
        $file_path = NV_ROOTDIR . '/' . $data_cp['contract_path_dntt'];
        if (file_exists($file_path)) {
            $tmp_file = $temp_dir . '/De_nghi_thanh_toan.pdf';
            copy($file_path, $tmp_file);
            $files_to_zip[] = [
                'path' => $tmp_file,
                'name' => 'De_nghi_thanh_toan.pdf'
            ];
        }
    } else {
        $content = load_payment_proposal($econtract, $data_vip, $_total_end, $list_order);
        $tmp_file = $temp_dir . '/De_nghi_thanh_toan.pdf';
        export_pdf_to_file($content, $tmp_file, '');
        $files_to_zip[] = [
            'path' => $tmp_file,
            'name' => 'De_nghi_thanh_toan.pdf'
        ];
    }

    // Xử lý báo giá
    if (!empty($data_cp['contract_path_quote'])) {
        $file_path = NV_ROOTDIR . '/' . $data_cp['contract_path_quote'];
        if (file_exists($file_path)) {
            $tmp_file = $temp_dir . '/Bao_gia.pdf';
            copy($file_path, $tmp_file);
            $files_to_zip[] = [
                'path' => $tmp_file,
                'name' => 'Bao_gia.pdf'
            ];
        }
    } else {
        $content = load_price_quote($econtract, $list_order, $custom_vips, $list_info_payment);
        $tmp_file = $temp_dir . '/Bao_gia.pdf';
        export_pdf_to_file($content, $tmp_file, 'download_quote');
        $files_to_zip[] = [
            'path' => $tmp_file,
            'name' => 'Bao_gia.pdf'
        ];
    }

    // Xử lý biên bản thanh lý hợp đồng
    if (!empty($data_cp['contract_path_liquidation'])) {
        $file_path = NV_ROOTDIR . '/' . $data_cp['contract_path_liquidation'];
        if (file_exists($file_path)) {
            $tmp_file = $temp_dir . '/Bien_ban_thanh_ly.pdf';
            copy($file_path, $tmp_file);
            $files_to_zip[] = [
                'path' => $tmp_file,
                'name' => 'Bien_ban_thanh_ly.pdf'
            ];
        }
    } else {
        $content = load_contract_liquidation($econtract);
        $tmp_file = $temp_dir . '/Bien_ban_thanh_ly.pdf';
        export_pdf_to_file($content, $tmp_file, 'download_contract_liquidation');
        $files_to_zip[] = [
            'path' => $tmp_file,
            'name' => 'Bien_ban_thanh_ly.pdf'
        ];
    }

    // Xử lý biên bản giao nhận
    if (!empty($data_cp['contract_path_acceptance'])) {
        $file_path = NV_ROOTDIR . '/' . $data_cp['contract_path_acceptance'];
        if (file_exists($file_path)) {
            $tmp_file = $temp_dir . '/Bien_ban_ban_giao.pdf';
            copy($file_path, $tmp_file);
            $files_to_zip[] = [
                'path' => $tmp_file,
                'name' => 'Bien_ban_ban_giao.pdf'
            ];
        }
    } else {
        $content = load_acceptance_report($econtract, $custom_vips);
        $tmp_file = $temp_dir . '/Bien_ban_ban_giao.pdf';
        export_pdf_to_file($content, $tmp_file, '');
        $files_to_zip[] = [
            'path' => $tmp_file,
            'name' => 'Bien_ban_ban_giao.pdf'
        ];
    }

    // Xử lý đơn đặt hàng
    if (!empty($data_cp['contract_path_order'])) {
        $file_path = NV_ROOTDIR . '/' . $data_cp['contract_path_order'];
        if (file_exists($file_path)) {
            $tmp_file = $temp_dir . '/Don_dat_hang.pdf';
            copy($file_path, $tmp_file);
            $files_to_zip[] = [
                'path' => $tmp_file,
                'name' => 'Don_dat_hang.pdf'
            ];
        }
    }

    // Tạo thư mục tạm thời chứa các file pdf
    $zip_file = $temp_dir . '/hop_dong_' . change_alias($econtract['contract_no']) . '.zip';

    $zip = new ZipArchive();
    if ($zip->open($zip_file, ZipArchive::CREATE) === TRUE) {
        foreach ($files_to_zip as $file) {
            $zip->addFile($file['path'], $file['name']);
        }
        $zip->close();

        header('Content-Type: application/zip');
        header('Content-Disposition: attachment; filename="hop_dong_' . change_alias($econtract['contract_no']) . '.zip"');
        header('Content-Length: ' . filesize($zip_file));
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        readfile($zip_file);

        // Xóa file tạm
        foreach ($files_to_zip as $file) {
            @unlink($file['path']);
        }
        @unlink($zip_file);
        rmdir($temp_dir);
        exit;
    }

    die('Không thể tải xuống file zip');
}

// Thêm hàm export PDF to file
function export_pdf_to_file($html, $file_path, $download_type)
{
    $config_mpdf = [
        'fontdata' => [
            'timesnewroman' => [
                'R' => 'SVN-Times-New-Roman.ttf',
                'B' => 'SVN-Times-New-Roman-Bold.ttf',
                'I' => 'SVN-Times-New-Roman-Italic.ttf',
                'BI' => 'SVN-Times-New-Roman-Bold-Italic.ttf',
            ],
        ],
        'default_font' => 'timesnewroman',
    ];

    $mpdf = new \Mpdf\Mpdf($config_mpdf);

    if ($download_type == 'download_quote') {
        $headerImagePath = NV_ROOTDIR . '/' . NV_ASSETS_DIR . '/images/header-bg-DauThau.info.jpg';
        $footerImagePath = NV_ROOTDIR . '/' . NV_ASSETS_DIR . '/images/footer-bg-DauThau.info.jpg';

        $mpdf->SetHTMLHeader('
            <div style="position: absolute; top: 0; left: 0; width: 100%; text-align: center;">
                <img src="' . $headerImagePath . '" style="width: 100%; height: auto; display: block;" />
            </div>
        ');

        $mpdf->SetHTMLFooter('
            <div style="position: absolute; bottom: 0; left: 0; width: 100%; text-align: center;">
                <img src="' . $footerImagePath . '" style="width: 100%; height: auto; display: block;" />
            </div>
        ');

        $mpdf->SetMargins(20, 20, 30);
        $mpdf->SetAutoPageBreak(true, 25);
    } else if ($download_type == 'download_contract_liquidation') {
        $mpdf->defaultfooterline = false;
        $mpdf->AddPage('', '', '', '', 'off');
        $footer = '<div style="text-align: center; font-size: 10pt; font-family: \'Times New Roman\', sans-serif; color: black;">
            {PAGENO}
        </div>';
        $mpdf->setFooter($footer);
    }

    $mpdf->WriteHTML($html);
    $mpdf->Output($file_path, 'F');
}
