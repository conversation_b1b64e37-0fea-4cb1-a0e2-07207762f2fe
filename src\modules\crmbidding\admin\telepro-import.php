<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use PhpOffice\PhpSpreadsheet\IOFactory;

$set_active_op = 'import';
$page_title = $nv_Lang->getModule('teleimport');

// Tải mẫu
if ($nv_Request->isset_request('template', 'get')) {
    $file_src = NV_ROOTDIR . '/modules/' . $module_file . '/excel/data_telepro_cong_viec.xlsx';
    $directory = NV_ROOTDIR . '/modules/' . $module_file . '/excel';
    $download = new NukeViet\Files\Download($file_src, $directory, 'data_telepro.xlsx', true, 0);
    $download->download_file();
    exit();
}

// Lấy danh sách công việc
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_jobs ORDER BY title ASC';
$array_telepro_jobs = $nv_Cache->db($sql, 'id', $module_name);

$post = [];
$error = [];
$import_result = [
    'num_read' => 0,
    'num_error' => 0,
    'num_install' => 0,
    'num_update' => 0
];

$post['existstype'] = $nv_Request->get_absint('existstype', 'post', 0);
$post['errorhandler'] = $nv_Request->get_absint('errorhandler', 'post', 0);
$post['job_id'] = $nv_Request->get_absint('job_id', 'post', 0);
if (!isset($array_telepro_jobs[$post['job_id']])) {
    $post['job_id'] = 0;
}
$import_result_show = false;

if ($nv_Request->isset_request('save', 'post')) {
    if (!is_dir(NV_ROOTDIR . '/vendor/phpoffice/phpspreadsheet')) {
        trigger_error('No phpspreadsheet lib. Run command &quot;composer require phpoffice/phpspreadsheet&quot; to instal phpspreadsheet', 256);
    }
    if ($sys_info['allowed_set_time_limit']) {
        set_time_limit(0);
    }
    if ($sys_info['ini_set_support']) {
        $memoryLimitMB = (integer)ini_get('memory_limit');
        if ($memoryLimitMB < 1024) {
            ini_set("memory_limit", "1024M");
        }
    }

    if (!isset($_FILES, $_FILES['excel'], $_FILES['excel']['tmp_name'])) {
        $error[] = $nv_Lang->getModule('teleimport_error_file');
    } elseif (!is_uploaded_file($_FILES['excel']['tmp_name'])) {
        $error[] = $nv_Lang->getModule('teleimport_error_file');
    }

    if (empty($error)) {
        $upload = new NukeViet\Files\Upload(array('documents'), $global_config['forbid_extensions'], $global_config['forbid_mimes'], NV_UPLOAD_MAX_FILESIZE, NV_MAX_WIDTH, NV_MAX_HEIGHT);
        $upload->setLanguage(\NukeViet\Core\Language::$lang_global);
        $upload_info = $upload->save_file($_FILES['excel'], NV_ROOTDIR . '/' . NV_TEMP_DIR, false, $global_config['nv_auto_resize']);

        if (!empty($upload_info['error'])) {
            $error[] = $upload_info['error'];
        } elseif (!in_array($upload_info['ext'], array('xls', 'xlsx'))) {
            $error[] = $nv_Lang->getModule('teleimport_error_file1');
        }
    }
    if (empty($post['job_id'])) {
        $error[] = $nv_Lang->getModule('teleimport_error_job');
    }

    if (empty($error)) {
        $spreadsheet = IOFactory::load($upload_info['name']);
        $sheet = $spreadsheet->getActiveSheet();

        $highestRow = $sheet->getHighestRow();
        $row_index = 3;

        $array = [];
        for ($row = $row_index; $row <= $highestRow; ++$row) {
            $read = [];

            $read['masodn'] = trim((string) $sheet->getCell('B' . $row)->getCalculatedValue());
            $read['name'] = trim($sheet->getCell('C' . $row)->getCalculatedValue());
            $read['phone'] = str_replace(['.', '-'], '', trim($sheet->getCell('D' . $row)->getCalculatedValue()));
            $read['address'] = trim($sheet->getCell('E' . $row)->getCalculatedValue());
            $read['nopphi'] = trim($sheet->getCell('F' . $row)->getCalculatedValue());

            // Check lỗi
            $line_error = false;
            if (!empty($read['masodn']) and !taxcodecheck2($read['masodn'])) {
                $line_error = true;
                if (empty($post['errorhandler'])) {
                    $error[] = sprintf($nv_Lang->getModule('teleimport_error_msdn'), $read['masodn'], $row);
                }
            }
            if (empty($read['name'])) {
                $line_error = true;
                if (empty($post['errorhandler'])) {
                    $error[] = sprintf($nv_Lang->getModule('teleimport_error_name'), $row);
                }
            }
            if (empty($read['phone'])) {
                $line_error = true;
                if (empty($post['errorhandler'])) {
                    $error[] = sprintf($nv_Lang->getModule('teleimport_error_phone1'), $row);
                }
            } elseif (!phonecheck($read['phone'])) {
                $line_error = true;
                if (empty($post['errorhandler'])) {
                    $error[] = sprintf($nv_Lang->getModule('teleimport_error_phone2'), $read['phone'], $row);
                }
            }
            if (empty($line_error)) {
                $array[] = $read;
            } else {
                $import_result['num_error']++;
            }
            $import_result['num_read']++;
        }
        if (empty($error) and empty($array)) {
            $error[] = $nv_Lang->getModule('teleimport_error_filecontent');
        }
    }

    if (empty($error)) {
        foreach ($array as $row) {
            $sql = 'SELECT id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro
            WHERE job_id=' . $post['job_id'] . ' AND phone=' . $row['phone'];
            $is_exists = $db->query($sql)->fetch();

            if (!$is_exists) {
                // Thêm mới
                $sql = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_telepro (
                    phone, name, masodn, address, nopphi, job_id, add_time, recording_isset
                ) VALUES (
                    " . $db->quote($row['phone']) . ",
                    " . $db->quote($row['name']) . ",
                    " . $db->quote($row['masodn']) . ",
                    " . $db->quote($row['address']) . ",
                    " . $db->quote($row['nopphi']) . ",
                    " . $post['job_id'] . ",
                    " . NV_CURRENTTIME . ", 0
                )";
                $db->query($sql);
                $import_result['num_install']++;
            } elseif ($post['existstype'] == 1) {
                // Cập nhật
                $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_telepro SET
                    name=" . $db->quote($row['name']) . ",
                    masodn=" . $db->quote($row['masodn']) . ",
                    address=" . $db->quote($row['address']) . ",
                    nopphi=" . $db->quote($row['nopphi']) . ",
                    update_time=" . NV_CURRENTTIME . "
                WHERE phone=" . $db->quote($row['phone']) . " AND job_id=" . $post['job_id'];
                $db->query($sql);
                $import_result['num_update']++;
            }
        }
        $import_result_show = true;
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('FORM_ACTION', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);
$xtpl->assign('LINK_TEMPLATE', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;template=1');

// Xuất hình thức xử lý trùng lặp
for ($i = 0; $i <= 1; $i++) {
    $existstype = array(
        'key' => $i,
        'title' => $nv_Lang->getModule('import_sel_existstype' . $i),
        'selected' => $i == $post['existstype'] ? ' selected="selected"' : ''
    );

    $xtpl->assign('EXISTSTYPE', $existstype);
    $xtpl->parse('main.existstype');
}

// Xuất hình thức xử lý lỗi
for ($i = 0; $i <= 1; $i++) {
    $errorhandler = [
        'key' => $i,
        'title' => $nv_Lang->getModule('teleimport_errorhandler' . $i),
        'selected' => $i == $post['errorhandler'] ? ' selected="selected"' : ''
    ];

    $xtpl->assign('ERRORHANDLER', $errorhandler);
    $xtpl->parse('main.errorhandler');
}

// Xuất công việc
foreach ($array_telepro_jobs as $job) {
    $job['selected'] = $job['id'] == $post['job_id'] ? ' selected="selected"' : '';
    $xtpl->assign('JOB', $job);
    $xtpl->parse('main.job');
}

// Xuất lỗi
if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

// Xuất kết quả
if ($import_result_show) {
    $xtpl->assign('NUM_READ', number_format($import_result['num_read'], 0, ',', '.'));
    $xtpl->assign('NUM_INSTALL', number_format($import_result['num_install'], 0, ',', '.'));
    $xtpl->assign('NUM_UPDATE', number_format($import_result['num_update'], 0, ',', '.'));
    $xtpl->assign('NUM_ERROR', number_format($import_result['num_error'], 0, ',', '.'));
    $xtpl->parse('main.result');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
