<!-- BEGIN: main -->
<!-- BEGIN: form -->
<form action="{NV_BASE_ADMINURL}index.php" method="get">
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" />
    <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" />
    <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
    <div class="row">
        <div class="col-sm-12 col-md-6">
            <div class="form-group">
                <label for="ipt_q"><strong>{LANG.keywords}:</strong></label>
                <input id="ipt_q" class="form-control" type="text" value="{SEARCH.q}" name="q" maxlength="255" placeholder="{LANG.search_title}">
            </div>
        </div>
        <div class="col-sm-12 col-md-6">
            <div class="form-group">
                <label for="ipt_job_id"><strong>{LANG.telepro_in_job}:</strong></label>
                <select class="form-control" id="ipt_job_id" name="job_id">
                    <option value="-1">----</option>
                    <option value="0"{SELECTED_JOB_NONE}>{LANG.telepro_in_job0}</option>
                    <!-- BEGIN: job -->
                    <option value="{JOB.id}"{JOB.selected}>{JOB.title}</option>
                    <!-- END: job -->
                </select>
            </div>
        </div>
        <div class="col-sm-12 col-md-6">
            <div class="form-group">
                <label for="ipt_api_status"><strong>{LANG.telepro_api_status}:</strong></label>
                <select class="form-control" id="ipt_api_status" name="api_status">
                    <option value="0">----</option>
                    <!-- BEGIN: api_status -->
                    <option value="{API_STATUS.key}"{API_STATUS.selected}>{API_STATUS.title}</option>
                    <!-- END: api_status -->
                </select>
            </div>
        </div>
        <div class="col-sm-12 col-md-6">
            <div class="form-group form-group-label">
                <input class="btn btn-primary" type="submit" value="{LANG.search_submit}" />
            </div>
        </div>
    </div>
</form>
<div class="form-group clearfix">
    <div class="pull-right">
        <a href="{LINK_IMPORT}" class="btn btn-info btn-sm"><i class="fa fa-file-excel-o"></i> {LANG.teleimport}</a>
        <!-- BEGIN: admin_links -->
        <a href="{LINK_JOBS}" class="btn btn-success btn-sm"><i class="fa fa-briefcase"></i> {LANG.telejob1}</a>
        <a href="{LINK_LOGS}" class="btn btn-default btn-sm"><i class="fa fa-history"></i> {LANG.telelog}</a>
        <!-- END: admin_links -->
    </div>
    <div class="pull-left">
        {LANG.num_items} <strong class="text-danger">{NUMITEMS}</strong>
    </div>
</div>
<!-- END: form -->
<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th style="width: 1%;" class="text-center">{LANG.number}</th>
                    <th style="width: 24%;" class="text-center">{LANG.name_customer}</th>
                    <th style="width: 13%;" class="text-center">{LANG.telepro_job_title}</th>
                    <th style="width: 13%;" class="text-center">{LANG.timecall}</th>
                    <th style="width: 11%;" class="text-center">{LANG.status_telepro}</th>
                    <th style="width: 12%;" class="text-center">{LANG.thoigian_nghetuvan}</th>
                    <th style="width: 13%;" class="text-center">{LANG.note_telepro}</th>
                    <th style="width: 13%;" class="text-center">{LANG.recording}</th>
                </tr>
            </thead>
            <!-- BEGIN: generate_page -->
            <tfoot>
                <tr>
                    <td class="text-center" colspan="9">{NV_GENERATE_PAGE}</td>
                </tr>
            </tfoot>
            <!-- END: generate_page -->
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="text-center">{VIEW.number}</td>
                    <td>
                        <strong>{VIEW.name}</strong>
                        <div class="text-nowrap">
                            <i class="fa fa-phone-square text-primary" aria-hidden="true" data-toggle="tooltip" title="{LANG.phone}"></i> <a href="tel:{VIEW.phone}">{VIEW.phone}</a>
                        </div>
                        <div class="text-nowrap">
                            <i class="fa fa-envelope text-primary" aria-hidden="true" data-toggle="tooltip" title="{LANG.email}"></i> <a href="mailto:{VIEW.email}">{VIEW.email}</a>
                        </div>
                        <!-- BEGIN: status0 -->
                        <span class="btn btn-xs btn-info cursor-default">{LANG.telepro_api_status0}</span>
                        <!-- END: status0 -->
                        <!-- BEGIN: status1 -->
                        <span class="btn btn-xs btn-success cursor-default" title="{VIEW.api_time}" data-toggle="tooltip">{LANG.telepro_api_status1}</span>
                        <!-- END: status1 -->
                        <!-- BEGIN: statuserror -->
                        <span class="btn btn-xs btn-danger cursor-default" role="button" data-toggle="modal" data-target="#mdErrorApi{VIEW.id}">{LANG.telepro_api_status2} #{VIEW.api_status}</span>
                        <!-- START FORFOOTER -->
                        <div class="modal" id="mdErrorApi{VIEW.id}" tabindex="-1" role="dialog" aria-labelledby="mdErrorApi{VIEW.id}">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-body">
                                        <pre><code>{VIEW.api_text}</code></pre>
                                    </div>
                                    <div class="modal-footer">
                                        <div class="text-center">
                                            <a href="#" onclick="modalHide();"><i class="fa fa-2x fa-times-circle text-danger"></i></a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END FORFOOTER -->
                        <!-- END: statuserror -->
                        <!-- BEGIN: tools -->
                        <div class="btn-group">
                            <button type="button" class="btn btn-default btn-xs dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                {LANG.tools1} <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a href="#" data-toggle="synccontact" data-id="{VIEW.id}"><i class="fa fa-fw text-center fa-retweet"></i> {LANG.telepro_sync}</a></li>
                                <li><a href="#" data-toggle="viewcalls" data-id="{VIEW.id}"><i class="fa fa-fw text-center fa-phone"></i> {LANG.telepro_view_calls}</a></li>
                            </ul>
                        </div>
                        <!-- END: tools -->
                    </td>
                    <td class="text-center">{VIEW.job_title}</td>
                    <td class="text-center">{VIEW.timecall}</td>
                    <td>{VIEW.status}</td>
                    <td class="text-center">{VIEW.thoigian_nghetuvan}</td>
                    <td>{VIEW.note}</td>
                    <td>
                        <!-- BEGIN: recording -->
                        <audio controls>
                            <source src="{AUDIO}" type="audio/ogg">
                            <source src="{AUDIO}" type="audio/wav">
                            Your browser does not support the audio element.
                        </audio>
                        <!-- END: recording -->
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
</form>
<!-- BEGIN: js_data -->
<!-- START FORFOOTER -->
<div class="modal" tabindex="-1" role="dialog" id="mdContactDetail">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" onclick="modalHide();" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><span class="h1"></span></h4>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>
<!-- END FORFOOTER -->
<script type="text/javascript">
$(document).ready(function() {
    var md = $('#mdContactDetail');
    // Đồng bộ trạng thái cuộc gọi
    $('[data-toggle="synccontact"]').on('click', function(e) {
        e.preventDefault();
        md.data('mode', 'synccontact');
        md.data('id', $(this).data('id'));
        $('.modal-dialog', md).removeClass('modal-lg');
        md.modal('show');
    });

    // Xem danh sách cuộc gọi
    $('[data-toggle="viewcalls"]').on('click', function(e) {
        e.preventDefault();
        md.data('mode', 'viewcalls');
        md.data('id', $(this).data('id'));
        $('.modal-dialog', md).addClass('modal-lg');
        md.modal('show');
    });

    md.on('hidden.bs.modal', function() {
        $('.modal-body', md).html('');
    });

    md.on('show.bs.modal', function() {
        $('.modal-body', md).html('<div class="text-center"><i class="fa fa-spin fa-spinner fa-2x"></i></div>');

        if (md.data('mode') == 'synccontact') {
            $('.h1', md).html('{LANG.telepro_sync1}');
        } else {
            $('.h1', md).html('{LANG.telepro_view_calls1}');
        }

        $.post(
            script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=telepro&nocache=' + new Date().getTime(),
            'ajaxmode=' + md.data('mode') + '&id=' + md.data('id'), function(res) {
            $('.modal-body', md).html(res);
            if (md.data('mode') == 'synccontact') {
                setTimeout(function() {
                    location.reload();
                }, 3000);
            }
        });
    });
});
</script>
<!-- END: js_data -->
<!-- END: main -->

<!-- BEGIN: calls -->
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th class="text-nowrap">{LANG.stt}</th>
                <th class="text-nowrap">{LANG.timecall}</th>
                <th class="text-nowrap">{LANG.nhan_vien}</th>
                <th class="text-nowrap">{LANG.status_telepro}</th>
                <th class="text-nowrap">{LANG.note_telepro}</th>
                <th class="text-nowrap">{LANG.recording}</th>
            </tr>
        </thead>
        <tbody>
            <!-- BEGIN: loop -->
            <tr>
                <td class="text-nowrap">{STT}</td>
                <td class="text-nowrap">{CALL.timecall}</td>
                <td>{CALL.agent_fullname}</td>
                <td>{CALL.status}</td>
                <td>{CALL.note}</td>
                <td>
                    <audio controls>
                            <source src="{CALL.recording}" type="audio/ogg">
                            <source src="{CALL.recording}" type="audio/wav">
                            Your browser does not support the audio element.
                    </audio>
                </td>
            </tr>
            <!-- END: loop -->
        </tbody>
    </table>
</div>
<!-- END: calls -->
