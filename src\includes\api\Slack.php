<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Api;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class Slack implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'webtools';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $nv_Request;

        $message = $nv_Request->get_title('message', 'post,get', '');
        $channel = $nv_Request->get_title('channel', 'post,get', '');
        $icon_emoji = $nv_Request->get_title('icon_emoji', 'post,get', ':timer_clock');

        $channel_allow = [
            'kythuat-truongnhom',
            '#vinades',
            'dauthau-info',
            'dauthau-kythuat',
            'hotro-edugate'
        ];
        if (!empty($message) and in_array($channel, $channel_allow)) {
            $data = "payload=" . json_encode(array(
                "channel" => $channel,
                "text" => $message,
                "icon_emoji" => $icon_emoji
            ));

            // You can get your webhook endpoint from your Slack settings
            $ch = curl_init("*****************************************************************************");
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $result = curl_exec($ch);
            curl_close($ch);

            $this->result->setMessage($result);
            $this->result->setSuccess();
        } else {
            $this->result->setError();
            $this->result->setMessage('Kênh không đúng hoặc không có nội dung');
        }
        return $this->result->getResult();
    }
}
