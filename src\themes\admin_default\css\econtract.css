.mb-3 {
    margin-bottom: 15px;
}

h2.section-title {
    font-size: 1.35em;
    font-weight: 700;
    margin-bottom: 15px;
    border-bottom: 1px solid #dcdcdc;
    padding-bottom: 8px;
}

h2.section-title>.help-text {
    font-size: 13px;
    font-weight: 400;
    color: #eea236;
}

.row.econtract-form-info {
    margin-top: 15px;
    margin-bottom: 15px;
}

.row.econtract-form-info>div {
    margin-bottom: 15px;
}

.row.econtract-form-info>div>.row {
    padding: 0 20px;
    display: flex;
    align-items: center;
}

/* .row.econtract-form-info.two-cols>div:nth-child(2n+1)>.row {
    padding-left: 0px;
} */

span.required {
    color: red;
}


.econtract-form-info-2 .row {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

#view-econtract-detail>div:first-child {
    padding-right: 20px;
}

#view-econtract-detail>div:last-child {
    padding-left: 5px;
    border-left: 1px solid #dcdcdc;
}

#view-econtract-detail .right-sidebar {
    padding-left: 10px;
}

.right-sidebar .box-info-item {
    border-bottom: 1px solid #dcdcdc;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.box-info-item h3.sidebar-title {
    font-weight: 700;
}

.box-info-item .box-content ul {
    padding-left: 20px;
}


.box-info-item .box-content ul>li {
    margin-bottom: 5px;
}


.box-info-item .box-content ul.list-order-label {
    padding-left: 0;
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.box-info-item .box-content ul.list-order-label li {
    margin: 0;
}

.box-info-item .box-content ul.list-order-label li a {
    text-decoration: none;
}

#content-econtract-preview {
    /* border: 1px solid #dcdcdc; */
    border-radius: 6px;
    position: relative;
    overflow: hidden;
}

#content-econtract-preview .label-preview {
    position: absolute;
    top: 35px;
    right: -80px;
    background: #ff7373;
    transform: rotate(45deg);
    width: 250px;
    text-align: center;
    color: #fff;
    font-size: 13px;
    padding: 5px 0 3px;
    opacity: 0.75;
    text-transform: uppercase;
}

#content-econtract-preview .label-preview-alert {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    background: #ff7373;
    text-align: center;
    color: #fff;
    font-size: 13px;
    padding: 5px 0 3px;
    opacity: 0.9;
}

#content-econtract-preview .contract-wrapper {
    max-height: calc(100vh - 190px);
    overflow-y: auto;
}

#content-econtract-preview .contract-wrapper * {
    font-family: 'Times New Roman', Times, serif;
    font-size: 16px;
}

#content-econtract-preview .contract-wrapper .text-highlight {
    color: red;
}

#content-econtract-preview .preview-econtract-actions {
    position: absolute;
    display: flex;
    top: 10px;
    left: 10px;
    flex-direction: column;
    gap: 8px;
    padding: 15px 15px 15px 0;
}

#content-econtract-preview .preview-econtract-actions>a {
    height: 30px;
    width: 30px;
    border-radius: 50%;
    background: #3b58e2;
    text-align: center;
    line-height: 30px;
    color: #fff;
    transition: all 0.2s;
    text-decoration: none;
}

#content-econtract-preview .preview-econtract-actions>a>span {
    display: none;
}

#content-econtract-preview .preview-econtract-actions:hover>a {
    width: auto;
    border-radius: 6px;
    padding: 0 10px;
    transition: all 0.2s;
    text-align: left;
}

#content-econtract-preview .preview-econtract-actions:hover>a>em {
    width: 20px;
}

#content-econtract-preview .preview-econtract-actions:hover>a>span {
    display: inline-block;
    transition: all 0.3s;
}

.row.econtract-form-info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.sum-total-orders {
    font-size: 15px;
    margin-bottom: 35px;
}

.sum-total-orders .row {
    margin-bottom: 15px;
}

.sum-total-orders .row>div:first-child {
    padding-right: 30px;
}

.form-check .form-check-label {
    cursor: pointer;
}

.top-action-buttons {
    margin-bottom: 20px;
}

.top-action-buttons .btn {
    margin: 0 3px 5px 0;
}

/* Responsive styling */
@media (max-width: 991px) {
    .top-action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
    }

    .top-action-buttons .btn {
        flex: 1 1 auto;
        min-width: 140px;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .top-action-buttons .pull-right {
        float: none !important;
    }
}

@media (max-width: 767px) {
    .top-action-buttons .btn {
        min-width: 120px;
        padding: 5px 8px;
        font-size: 12px;
    }

    .top-action-buttons .btn em {
        margin-right: 3px;
    }
}

.bottom-action-buttons {
    margin-top: 30px;
    border-top: 1px solid #dcdcdc;
    padding-top: 20px;
    text-align: center;
}

.label-current-version {
    margin-left: 5px;
}

.upload-form-dnd {
    border: 1px dotted #dcdcdc;
    border-radius: 10px;
    width: 100%;
    padding: 20px;
    margin: 15px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    cursor: pointer;
    box-shadow: 0 0 5px 1px #ececec;
    font-size: 13px;
    line-height: 24px;
    color: #888;
}

.upload-form-dnd em.fa {
    font-size: 35px;
    margin-bottom: 10px;
}

.upload-form-dnd label.btn {
    margin-top: 10px;
}

.upload-box {
    border: 1px dotted #dcdcdc;
    border-radius: 10px;
    width: 100%;
    padding: 20px;
    margin: 15px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    cursor: pointer;
    box-shadow: 0 0 5px 1px #ececec;
    font-size: 13px;
    line-height: 24px;
    color: #888;
    text-align: center;
}

.upload-box.dragging {
    border-color: #00bcd4;
    background-color: #f4f4f4;
}

.upload-placeholder i {
    font-size: 3rem;
}

.upload-placeholder p {
    transition: opacity 0.3s;
}

.upload-box.dragging .upload-placeholder p {
    opacity: 0;
}

.file-item {
    border: 1px solid #eeeeee;
    padding: 5px 10px;
    border-radius: 6px;
    display: flex;
    align-items: center;
}

.file-item:hover {
    border: 1px solid #d1d1d1;
    background-color: #f3f3f3;
}

.file-item button {
    margin-left: auto;
    width: 25px;
    height: 25px;
    text-align: center;
    line-height: 25px;
    padding: 0;
}

#file-input {
    display: none;
}

#file-list {
    margin-top: 10px;
    margin-bottom: 20px;
}

#file-list p {
    margin: 10px 0;
    border: 1px dotted #dcdcdc;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 0 5px 1px #ececec;
}

.list-versions {
    list-style: none;
    padding-left: 3px !important;
    max-height: 200px;
    overflow-y: auto;
}

.list-versions .version-item {
    margin-bottom: 8px !important;
    color: #888;
    border-bottom: 0.5px dotted #dcdcdc;
}

.list-versions .version-item a {
    color: #888;
    text-decoration: none;
    opacity: 0.8;
}

.list-versions .version-item a:hover {
    opacity: 1;
}

.list-versions .version-item:last-child {
    border-bottom: none;
}

.list-versions .version-item.viewing a {
    color: #e2783b;
}

.list-versions .version-item.current-version a {
    color: darkgreen;
}

.list-versions .version-item a.text-muted {
    color: #888;
}

.list-versions .version-item .label {
    margin-left: 5px;
}

.list-versions .version-item h4 {
    font-size: 14px;
    margin-bottom: 5px;
    font-weight: 500;
}

.list-versions .version-item h4>em {
    font-size: 18px;
    margin-right: 5px;
}

.list-versions .version-item p {
    font-size: 12px;
}

.list-versions .version-item.current-version h4 {
    font-weight: 700;
}

.box-info-item .box-content ul.list-logs {
    max-height: 200px;
    overflow-y: auto;
}

/* Customize styles econtract */
.contract-wrapper h1,
.contract-wrapper h2,
.contract-wrapper h3,
.contract-wrapper h4,
.contract-wrapper h5,
.contract-wrapper h6 {
    font-weight: 700;
}

/* modalChooseOrders */
#modalChooseOrders .table-responsive {
    max-height: calc(100vh - 300px);
    overflow-y: auto;
    border: 1px solid #dcdcdc;
    border-left: none;
}

.report-download-all {
    text-align: right;
    margin: 5px 8px 5px 0;
}

.email-field {
    word-break: break-all;
    overflow-wrap: anywhere;
    white-space: normal;
}
