<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetViewEmail implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'leads';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $module_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $email = $nv_Request->get_typed_array('email', 'post', 'title', []);

        if (!isset($module_config[$module_name])) {
            $result_config = $db->query('SELECT * FROM nv4_config WHERE module = ' . $db->quote($module_name) . ' AND lang = "vi"');
            while ($row_config = $result_config->fetch()) {
                $module_config[$module_name][$row_config['config_name']] = $row_config['config_value'];
            }
            $result_config->closeCursor();
        }

        if (!empty($email)) {
            try {
                // Kiểm tra những email bị trùng
                $email = array_unique($email);
                $arrCheck = $arrEmail = $arrIDLeads= $arrIdOpportunities = [];
                foreach ($email as $k => $v) {
                    // Xử lý và kiểm tra email
                    $check = nv_check_valid_email($v, true);

                    // Kiểm tra hợp chuẩn email
                    if (!empty($check[0])) {
                        $arrCheck[] = "Email: " . $v . " invalidate";
                    } else {
                        $arrEmail[] = $v;
                    }
                }

                // Trả về những email không đúng định dạng
                if (!empty($arrCheck)) {
                    $this->result->setError();
                    $this->result->setCode('2003');
                    $this->result->setMessage($nv_Lang->getModule('email_invalidate'));
                    $this->result->set('email', $arrEmail);
                    $this->result->set('email_invalidate', $arrCheck);
                    return $this->result->getResult();
                }

                $arrMailLeads = $arrOpportunities = [];
                // foreach ($arrEmail as $k => $v) {
                //     // Lấy ID leads
                //     $getEmail = $db->query("SELECT id, activity_time, updatetime FROM " . NV_PREFIXLANG . "_" . $module_data . "_leads WHERE status != 2 and email LIKE " . $db->quote('%' . $v . '%') . " OR sub_email LIKE " . $db->quote('%' . $v . '%'))->fetchAll();
                //     if (!empty($getEmail)) {
                //         foreach ($getEmail as $v1) {
                //             $arrMailLeads[] = $v1;
                //         }
                //     }

                //     // Lấy ID cơ hội
                //     $getEmail = $db->query("SELECT id, activity_time, updatetime FROM " . NV_PREFIXLANG . "_" . $module_data . "_opportunities WHERE email LIKE " . $db->quote('%' . $v . '%') . " OR sub_email LIKE " . $db->quote('%' . $v . '%'))->fetchAll();
                //     if (!empty($getEmail)) {
                //         foreach ($getEmail as $v1) {
                //             $arrOpportunities[] = $v1;
                //         }
                //     }
                // }
                // Kiểm tra lead
                // kết nối tới ElasticSearh
                $nukeVietElasticSearh = new \NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_host'], $module_config[$module_name]['elas_port'], 'crm_leads', $module_config[$module_name]['elas_user'], $module_config[$module_name]['elas_pass']);
                $search_elastic = [];
                foreach ($arrEmail as $k => $v) {
                    $search_elastic['should'][] = [
                        "match_phrase" => [
                            "email" => $v
                        ]
                    ];
                    $search_elastic['should'][] = [
                        "match_phrase" => [
                            "sub_email" => $v
                        ]
                    ];
                }
                $search_elastic['must_not'][] = [
                    "match" => [
                        "status" => 2
                    ]
                ];
                if (!empty($search_elastic['should'])) {
                    $search_elastic['minimum_should_match'] = '1';
                    $search_elastic['boost'] = '1.0';
                }
                $array_query_elastic['query']['bool'] = $search_elastic;
                $array_query_elastic['size'] = 10000;
                $array_query_elastic['_source'] = ['id', 'activity_time', 'updatetime','email'];

                $response = $nukeVietElasticSearh->search_data('', $array_query_elastic);

                foreach ($response['hits']['hits'] as $value) {
                    if (!empty($value['_source'])) {
                        $arrMailLeads[] = $value['_source'];
                    }
                }

                unset($nukeVietElasticSearh);
                // Kiểm tra cơ hội
                // kết nối tới ElasticSearh
                $nukeVietElasticSearh = new \NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_host'], $module_config[$module_name]['elas_port'], 'crm_opportunities', $module_config[$module_name]['elas_user'], $module_config[$module_name]['elas_pass']);
                $search_elastic = $array_query_elastic = [];
                foreach ($arrEmail as $k => $v) {
                    $search_elastic['should'][] = [
                        "match_phrase" => [
                            "email" => $v
                        ]
                    ];
                    $search_elastic['should'][] = [
                        "match_phrase" => [
                            "sub_email" => $v
                        ]
                    ];
                }
                if (!empty($search_elastic['should'])) {
                    $search_elastic['minimum_should_match'] = '1';
                    $search_elastic['boost'] = '1.0';
                }
                $array_query_elastic['query']['bool'] = $search_elastic;
                $array_query_elastic['size'] = 10000;
                $array_query_elastic['_source'] = ['id', 'activity_time', 'updatetime'];

                $response = $nukeVietElasticSearh->search_data('', $array_query_elastic);

                foreach ($response['hits']['hits'] as $value) {
                    if (!empty($value['_source'])) {
                        $arrOpportunities[] = $value['_source'];
                    }
                }

                $i_lead = $i_opportunities = 0;

                // Cập nhật thời gian Leads
                if (!empty($arrMailLeads)) {
                    foreach ($arrMailLeads as $k => $v) {
                        if (!empty($v)) {
                            $time = NV_CURRENTTIME;
                            if ($v['updatetime'] > $v['activity_time']) {
                                $time = $v['updatetime'];
                            }
                            $exe = $db->query("UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_leads SET activity_time = " . $time . ", elasticsearch = 0 WHERE id = " . $v['id']);
                            $i_lead++;
                        }
                    }
                }

                // Cập nhật thời gian cơ hội
                if (!empty($arrOpportunities)) {
                    foreach ($arrOpportunities as $k => $v) {
                        if (!empty($v)) {
                            $time = NV_CURRENTTIME;
                            if ($v['updatetime'] > $v['activity_time']) {
                                $time = $v['updatetime'];
                            }

                            $exe = $db->query("UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_opportunities SET activity_time = " . $time . ", elasticsearch = 0 WHERE id = " . $v['id']);
                            $i_opportunities++;
                        }
                    }
                }

                $this->result->setSuccess();
                $this->result->setCode('4000');
                $this->result->setMessage($nv_Lang->getModule('title_success'));
                $this->result->set('num_leas', $i_lead . ' rows');
                $this->result->set('num_opportunities', $i_opportunities . ' rows');
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode('2001')
                ->setMessage('Email cannot empty. The argument passed is an array')
                ->getResult();
        }
        return $this->result->getResult();
    }
}
