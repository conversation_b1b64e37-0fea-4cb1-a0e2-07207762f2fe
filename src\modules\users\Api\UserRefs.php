<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\users\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use PDOException;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UserRefs implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'user';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $nv_Request, $db;
        $module_info = Api::getModuleInfo();
        $module_file = $module_info['module_file'];

        try {
            $userid = $nv_Request->get_int('userid', 'post', 0);
            if (empty($userid)) {
                return $this->result->setError()
                                    ->setCode('1101')
                                    ->setMessage('userid is not defined')
                                    ->getResult();
            }

            $sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . "_refs WHERE userid=" . $userid;
            $res=$db->query($sql);
            $ref_arr = $res->fetch();

            if (!empty($ref_arr)) {
                return $this->result->setSuccess()
                                    ->setCode('1100')
                                    ->set('data', $ref_arr)
                                    ->getResult();
            } else {
                return $this->result->setError()
                                    ->setCode('1105')
                                    ->setMessage("userid " . $userid . " not found")
                                    ->getResult();
            }
        } catch (PDOException $e) {
            $this->result->setError();
            $this->result->setCode('3001');
            $this->result->setMessage(print_r($e, true));
            return $this->result->getResult();
        }

        return $this->result->getResult();
    }
}
