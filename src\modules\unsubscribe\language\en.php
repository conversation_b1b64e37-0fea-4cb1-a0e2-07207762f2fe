<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

$lang_translator['author'] = 'VINADES.,JSC <<EMAIL>>';
$lang_translator['createdate'] = '04/03/2010, 15:22';
$lang_translator['copyright'] = 'Copyright (C) 2009-2021 VINADES.,JSC. All rights reserved';
$lang_translator['info'] = '';
$lang_translator['langtype'] = 'lang_module';

$lang_module['notice'] = 'Notification';
$lang_module['unsub_email'] = 'Unsubscribe from Email';
$lang_module['info_unsub'] = '<div class="text-center font-size-lg m-bottom"><i class="fa fa-exclamation-circle"></i></div>We apologize for bothering you with inappropriate information that does not match your interests. If you <strong>do not want to receive similar information like the email you just received</strong>, please click the button below to unsubscribe from similar information!';
$lang_module['other'] = 'Other';
$lang_module['unsub'] = 'Unsubscribe';
$lang_module['unknown_error'] = '<div class="form-alert"><div class="font-size-lg m-bottom"><i class="fa fa-times-circle"></i></div>An error occurred, please try again later.</div>';
$lang_module['duplicate_error'] = '<div class="form-alert"><div class="font-size-lg m-bottom"><i class="fa fa-times-circle"></i></div>You have already unsubscribed from this type of email for the email address <strong>%s</strong>.</div>';
$lang_module['unsub_success'] = '<div class="form-alert"><div class="font-size-lg m-bottom"><i class="fa fa-check-circle"></i></div>Successfully unsubscribed from email.</div>';
$lang_module['main'] = 'Email Unsubscribe List';
$lang_module['reason'] = 'Unsubscribe Reason';
$lang_module['search_type'] = 'Email Type';
$lang_module['search_method'] = 'Search by';
$lang_module['submit'] = 'Search';
$lang_module['search_key'] = 'Search keyword';
$lang_module['range_time_active'] = 'Unsubscribe Time';
$lang_module['from'] = 'From';
$lang_module['to'] = 'To';
$lang_module['delete'] = 'Delete';
$lang_module['search_note'] = 'Search keyword should not exceed 64 characters and should not contain HTML tags';
$lang_module['funcs'] = 'Functions';
$lang_module['search_id'] = 'Account ID';
$lang_module['search_account'] = 'Account';
$lang_module['search_name'] = 'Account Name';
$lang_module['search_mail'] = 'Account Email';
$lang_module['userid'] = 'ID';
$lang_module['account'] = 'Account';
$lang_module['name'] = 'Name';
$lang_module['first_name'] = 'First Name';
$lang_module['last_name'] = 'Last Name';
$lang_module['email'] = 'Email';
$lang_module['add_date'] = 'Unsubscribe Date';
$lang_module['firstname_lastname'] = 'First Name and Last Name';
$lang_module['lastname_firstname'] = 'Last Name and First Name';
$lang_module['addreason'] = 'Add Reason';
$lang_module['savereason'] = 'Save Reason';
$lang_module['deletereason'] = 'Delete Reason';
$lang_module['errornotitle'] = 'Error: You have not entered the reason for unsubscribing from email';
$lang_module['save'] = 'Save';
$lang_module['weight'] = 'Position';
$lang_module['errorsave'] = 'System error: Failed to update content, please check if the title is duplicated';
