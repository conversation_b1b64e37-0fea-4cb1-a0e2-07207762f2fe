<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2023 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 27 Feb 2023 09:02:35 GMT
 */

if (!defined('NV_SYSTEM')) {
    die('Stop!!!');
}
require NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

define('NV_IS_MOD_SUPPORTTICKET', true);

// Lấy đường dẫn ảnh
function get_image_src($image)
{
    global $module_upload, $global_config;
    if (empty($image)) {
        $image_src = NV_BASE_SITEURL . 'themes/' . $global_config['site_theme'] . '/images/no_image.gif';
    } else {
        $image_src = NV_BASE_SITEURL . NV_UPLOADS_DIR . '/' . $module_upload . '/' . $image;
    }

    return $image_src;
}

// Hàm xây dựng cây phân cấp từ mảng comments
function build_comment_tree($allComments, $parentId = 0)
{
    $branch = [];
    if (isset($allComments[$parentId])) {
        foreach ($allComments[$parentId] as $comment) {
            $children = build_comment_tree($allComments, $comment['log_id']);
            if ($children) {
                $comment['children'] = $children;
            }
            $comment['children'] = $children;
            $branch[] = $comment;
        }
    }
    return $branch;
}

// Gắn cây phân cấp comment con vào comment cấp đầu tiên
function attach_comment_children($comments, $allComments)
{
    foreach ($comments as &$comment) {
        // Kiểm tra xem comment hiện tại có comment con không
        if (isset($allComments[$comment['log_id']])) {
            // Nếu có, gắn comment con và tiếp tục đệ quy
            $comment['children'] = build_comment_tree($allComments, $comment['log_id']);
        } else {
            // Nếu không có comment con, đặt children là một mảng trống
            $comment['children'] = [];
        }
    }
    return $comments;
}

//Render câu hỏi/yêu cầu bổ sung
function get_question_component($comment)
{
    global $module_info;
    $xtpl = new XTemplate('question.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme'] . '/comment');
    $comment['time'] = nv_date('H:i:s d/m/Y', $comment['add_time']);
    $xtpl->assign('COMMENT', $comment);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    //Hiển thị ảnh đính kèm của ticket
    $comment['file_attach'] = decode_file($comment['file_attach']);
    if (sizeof($comment['file_attach']) > 0) {
        foreach ($comment['file_attach'] as $file) {
            $attached = [];
            $attached['src'] = get_image_src($file);
            $attached['name'] = $file;
            if (file_type(getextension($file)) == 'image') {
                $xtpl->assign('IMAGE_CMT', $attached);
                $xtpl->parse('main.detail_attach.loop_image');
            } else {
                $xtpl->assign('FILE_CMT', $attached);
                $xtpl->parse('main.detail_attach.loop_file');
            }
        }
        $xtpl->parse('main.detail_attach');
    }
    $xtpl->parse('main');

    return $xtpl->text('main');
}

//Render câu trả lời bổ sung ý cho câu trả lời chính
function get_answer_additional_component($comment, $parent_comment)
{
    global $module_info, $nv_Lang;
    $xtpl = new XTemplate('answer-additional.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme'] . '/comment');
    $comment['time'] = empty($comment['edit_time']) ? nv_date('H:i:s d/m/Y', $comment['add_time']) : nv_date('H:i:s d/m/Y', $comment['edit_time']);
    $expert = get_user_info($comment['reply_userid']);
    $comment['caption'] = $nv_Lang->getModule('reply_extent_from_expert', $expert['fullname']);
    $comment['content'] = get_content_component($comment, $parent_comment);
    $xtpl->assign('COMMENT', $comment);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->parse('main');

    return $xtpl->text('main');
}

//Render nội dung câu trả lời. Nếu chưa thanh toán thì content sẽ bị ẩn đi
function get_content_component($comment, $parent_comment = [])
{
    global $module_info;
    $xtpl = new XTemplate('content.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme'] . '/comment');

    $xtpl->assign('COMMENT', $comment);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    if (!empty($parent_comment)) {
        $comment = $parent_comment;
    }
    if ($comment['comment_type'] == CommentType::Expert->value && $comment['payment_status'] == PaymentStatus::Process->value && $comment['point_quote'] > 0) {
        $xtpl->parse('main.hide_content');
    } else {
        $xtpl->parse('main.show_content');
    }
    $xtpl->parse('main');

    return $xtpl->text('main');
}

//Render form thanh toán cho comment của chuyên gia
function get_payment_component($comment, $customs_points)
{
    global $module_info, $nv_Lang;
    $xtpl = new XTemplate('payment.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme'] . '/comment');

    if ($customs_points['point_total'] < $comment['point_quote']) {
        $comment['point_final'] = $nv_Lang->getModule('notify_insufficient_point_view', NV_BASE_SITEURL . NV_LANG_DATA . '/points/#muadiem');
        $comment['error_class'] = ' error';
    } else {
        $comment['point_final'] = $nv_Lang->getModule('notify_point_for_view', $comment['point_quote']);
        $comment['error_class'] = '';
    }
    $xtpl->assign('COMMENT', $comment);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('TOKEN', NV_CHECK_SESSION);
    $xtpl->parse('main');

    return $xtpl->text('main');
}

//Render html rating cho comment
function get_rating_component($ticket, $comment, $display_rating, $display_additional)
{
    global $module_info;
    $rating = [
        'display_general' => ($display_rating == 'none' && $display_additional == 'none' && $comment['comment_type'] == CommentType::AI->value) ? 'none' : 'flex',
        'display_rating' => $display_rating,
        'display_additional' => $display_additional,
    ];
    $xtpl = new XTemplate('rating.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme'] . '/comment');
    $xtpl->assign('RATING', $rating);
    $xtpl->assign('COMMENT', $comment);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    for ($i = 1; $i <= 5; $i++) {
        $star = [
            'value' => $i,
            'checked' => (isset($comment['rating_number']) && $comment['rating_number'] == $i) ? 'checked="checked"' : '',
            'disabled' => (in_array($comment['refund_status'], [RefundStatus::Accept->value, RefundStatus::Refuse->value]) || ($ticket['status'] == TicketStatus::Close->value && $comment['rating_number'] > 0)) ? 'disabled' : '',
        ];
        $xtpl->assign('STAR', $star);
        $xtpl->parse('main.star');
    }
    if ($comment['rating_number'] > 0) {
        $xtpl->assign('REVIEW', get_review_component($ticket, $comment));
        $xtpl->parse('main.review');
    }
    if ($comment['comment_type'] == CommentType::AI->value || empty($comment['comment_type'])) {
        $xtpl->parse('main.ai_additional');
    } else {
        $xtpl->parse('main.expert_additional');
    }
    if ($comment['comment_type'] == CommentType::Expert->value && $comment['payment_status'] == PaymentStatus::Process->value && $comment['point_quote'] > 0) {
        $xtpl->parse('main.payment');
    }
    $xtpl->parse('main');

    return $xtpl->text('main');
}

//Render form review cho comment
function get_review_form_component($ticket, $comment, $rating)
{
    global $module_info, $nv_Lang;
    $xtpl = new XTemplate('form-review.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme'] . '/comment');
    $xtpl->assign('RATING', $rating);
    $comment['submit_text'] = $comment['rating_number'] > 0 ? $nv_Lang->getModule('save') : $nv_Lang->getModule('send_review');
    $comment['submit_icon'] = $comment['rating_number'] > 0 ? 'fa-save' : 'fa-paper-plane';
    $xtpl->assign('COMMENT', $comment);
    $xtpl->assign('TOKEN', NV_CHECK_SESSION);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    //Chỉ ticket chưa đóng và comment trả lời trong vòng 7 ngày mới được yêu cầu hoàn điểm
    if ($ticket['status'] != TicketStatus::Close->value && NV_CURRENTTIME < ($comment['add_time'] + 604800)) {
        $refund = [
            'display' => $rating < 3 ? 'inline-block' : 'none',
            'checked' => $comment['refund_status'] > 0 ? 'checked' : '',
            'value' => $rating < 3 ? 1 : 0,
        ];
        $xtpl->assign('REFUND', $refund);
        $xtpl->parse('main.refund');
    }
    $xtpl->parse('main');

    return $xtpl->text('main');
}

//Render đánh giá đã được gửi
function get_review_component($ticket, $comment)
{
    global $module_info;
    $xtpl = new XTemplate('review.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme'] . '/comment');
    $comment['rating_time'] = nv_date('H:i:s d/m/Y', $comment['rating_add_time']);
    $comment['reply_time'] = nv_date('H:i:s d/m/Y', $comment['reply_add_time']);
    $xtpl->assign('COMMENT', $comment);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    //Ticket chưa đóng hoặc comment chưa xử lý hoàn điểm thì mới hiển thị nút sửa
    if ($ticket['status'] != TicketStatus::Close->value && !in_array($comment['refund_status'], [RefundStatus::Accept->value, RefundStatus::Refuse->value])) {
        $xtpl->assign('ICON_EDIT', NV_STATIC_URL . 'themes/default/images/supportticket/edit.svg');
        $xtpl->parse('main.edit');
    }

    $refund_status = RefundStatus::tryFrom($comment['refund_status']);
    if ($comment['rating_number'] < 3 && $comment['refund_status'] > 0) {
        if ($refund_status == RefundStatus::Accept || $refund_status == RefundStatus::Refuse) {
            $refund = [
                'label' => $refund_status?->getLabel(),
                'class' => $refund_status?->getClass(),
            ];
            $xtpl->assign('REFUND', $refund);
            $xtpl->parse('main.refund.processed');
        } else {
            $xtpl->parse('main.refund.processing');
        }
        $xtpl->parse('main.refund');
    }

    //Hiển thị phản hồi về việc hoàn điểm của admin 2*
    if ($refund_status == RefundStatus::Accept || $refund_status == RefundStatus::Refuse) {
        $xtpl->parse('main.reply');
    }

    $xtpl->parse('main');

    return $xtpl->text('main');
}
