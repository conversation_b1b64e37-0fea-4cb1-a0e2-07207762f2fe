<!-- BEGIN: main -->
<link rel="stylesheet" href="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/select2/select2.min.css">
<link type="text/css" href="{NV_BASE_SITEURL}{NV_ASSETS_DIR}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{NV_BASE_SITEURL}{NV_ASSETS_DIR}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{NV_BASE_SITEURL}{NV_ASSETS_DIR}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<div id="users">
    <div class="well">
        <form action="{FORM_ACTION}" method="get">
            <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" /> 
            <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" /> 
            <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
            <input name="{NV_NAME_VARIABLE}" type="hidden" value="{MODULE_NAME}" />
            <div class="row">
                <div class="col-xs-12 col-md-7">
                    <div class="form-group">
                        <input class="form-control" type="text" name="value" value="{SEARCH_VALUE}" id="f_value" placeholder="{LANG.stat_search_key}" />
                    </div>
                </div>
                <div class="col-xs-12 col-md-7">
                    <div class="form-group">
                        <select class="form-control" name="method" id="f_method">
                            <option value="">---{LANG.stat_search_type}---</option>
                            <!-- BEGIN: method -->
                            <option value="{METHODS.key}"{METHODS.selected}>{METHODS.value}</option>
                            <!-- END: method -->
                        </select>
                    </div>
                </div>
                <div class="col-xs-12 col-md-3">
                    <div class="form-group">
                        <input class="btn btn-primary" type="submit" value="{LANG.stat_submit}" />
                    </div>
                </div>
                <div class="col-xs-24 col-md-24">
                    <div class="form-group">
                        <input class="form-control" type="checkbox" name="not_activated" value=1 {NOT_ACTIVATED} /><label for="not_activated">{LANG.stat_not_activated}</label>
                    </div>
                </div>
                <div class="col-xs-24 col-md-24"><label>{LANG.stat_time}</label> <em>{LANG.stat_time_comment}</em>
                    <div class="form-group reg-date">
                        <label class="control-label col-sm-3 col-md-1" for="stat_from">{LANG.stat_from}:</label>
                        <div class="col-sm-9 col-md-5">
                            <div class="form-pickdate">
                                <input name="from" id="stat-from" class="form-control" value="{time_from}" maxlength="10" type="text" autocomplete="off">
                            </div>
                        </div>
                        <label class="control-label col-sm-3 col-md-1" for="stat_to">{LANG.stat_to}:</label>
                        <div class="col-sm-9 col-md-5">
                            <div class="form-pickdate">
                                <input name="to" id="stat-to" class="form-control" value="{time_to}" maxlength="10" type="text" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <label id="stat-note"><em>{LANG.stat_search_note}</em></label>
        </form>
    </div>
    <div class="stat-sum">
        <label>{LANG.stat_sum}</label>
        <div class="users-sum"><em>{LANG.stat_users_sum} : {users_sum}</em></div>
        <div class="business-sum"><em>{LANG.stat_business_sum} : {business_sum}</em></div>
    </div>
    <form>
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover">
                <caption><em class="fa fa-file-text-o">&nbsp;</em>{TABLE_CAPTION}</caption>
                <thead>
                    <tr>
                        <th class="w20"><a href="{HEAD.taxcode.href}">{HEAD.taxcode.title}</a></th>
                        <th class="w50"><a href="{HEAD.userid.href}">{HEAD.userid.title}</a></th>
                        <th><a href="{HEAD.username.href}">{HEAD.username.title}</a> / <a href="{HEAD.full_name.href}">{HEAD.full_name.title}</a></th>
                        <th><a href="{HEAD.email.href}">{HEAD.email.title}</a></th>
                        <th><a href="{HEAD.regdate.href}">{HEAD.regdate.title}</a></th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: xusers -->
                    <tr>
                        <td class="align-middle">{CONTENT_TD.taxcode}</td>
                        <td class="align-middle"> {CONTENT_TD.userid} </td>
                        <td>
                            <!-- BEGIN: is_admin -->
                            <img style="vertical-align:middle;" alt="{CONTENT_TD.level}" src="{NV_BASE_SITEURL}themes/{NV_ADMIN_THEME}/images/{CONTENT_TD.img}.png" width="38" height="18" />
                            <!-- END: is_admin -->
                            <!-- BEGIN: view --><a href="javascript:void(0);" onclick="viewUser('{CONTENT_TD.link}')">{CONTENT_TD.username}</a><!-- END: view -->
                            <!-- BEGIN: show -->{CONTENT_TD.username}<!-- END: show -->
                            <div class="mt-1">{CONTENT_TD.full_name}</div>
                        </td>
                        <td>
                            <a href="mailto:{CONTENT_TD.email}">{CONTENT_TD.email}</a>
                        </td>
                        <td>
                            <span class="text-info">{CONTENT_TD.regdate}</span>
                            <div class="mt-1">{CONTENT_TD.active_obj}</div>
                        </td>
                    </tr>
                    <!-- END: xusers -->
                </tbody>
                <!-- BEGIN: footer -->
                <tfoot>
                    <tr>
                        <td colspan="7">
                            <input type="hidden" name="checkss" value="{CHECKSESS}" />
                            <!-- BEGIN: generate_page -->
                            {GENERATE_PAGE}
                            <!-- END: generate_page -->
                        </td>
                    </tr>
                </tfoot>
                <!-- END: footer -->
            </table>
        </div>
    </form>
</div>
<script type="text/javascript">
var export_note = '{LANG.export_note}';
var export_complete = '{LANG.export_complete}';

$(document).ready(function() {
    const _current_time = new Date();

    $("#stat-to").datepicker({
        showOn : "both",
        dateFormat : "dd/mm/yy",
        changeMonth : true,
        changeYear : true,
        showOtherMonths : true,
        buttonImage : nv_base_siteurl + "assets/images/calendar.gif",
        buttonImageOnly : true,
        yearRange: "-99:+0",
        maxDate: _current_time,
        beforeShow: function() {
            setTimeout(function(){
                $('.ui-datepicker').css('z-index', 99);
            }, 0);
        }
    });

    $("#stat-from").datepicker({
        showOn : "both",
        dateFormat : "dd/mm/yy",
        changeMonth : true,
        changeYear : true,
        showOtherMonths : true,
        buttonImage : nv_base_siteurl + "assets/images/calendar.gif",
        buttonImageOnly : true,
        yearRange: "-99:+0",
        maxDate: _current_time,
        beforeShow: function() {
            setTimeout(function(){
                $('.ui-datepicker').css('z-index', 99);
            }, 0);
        }
    });
});
</script>
<!-- END: main -->
