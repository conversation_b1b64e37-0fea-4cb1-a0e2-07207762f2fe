<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2023 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 27 Feb 2023 09:02:35 GMT
 */

if (!defined('NV_IS_MOD_SUPPORTTICKET')) {
    die('Stop!!!');
}

if (!defined('NV_IS_USER')) {
    $redirect = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=" . $module_info['alias']['detail'], true);
    Header("Location: " . NV_BASE_SITEURL . "index.php?" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($redirect));
    die();
}

$array_data = [];

$ticket_id = $nv_Request->get_int('id', 'get,post', 0);
$row = $db->query('SELECT * FROM ' . TB_TICKET_ROW . ' WHERE id=' . $ticket_id . ' AND is_paid=0 AND delete_time=0')->fetch();
if (empty($row) || $row['customer_id'] != $user_info['userid'] || $row['is_paid'] != 0) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
} else {
    $array_data = $row;
}

// Mở lại ticket
if ($nv_Request->isset_request('change_ticket_status', 'post, get')) {
    $response = [
        'status' => 'error',
        'message' => ''
    ];
    if (!empty($array_data)) {
        $log_data = [
            $nv_Lang->getModule('customer_reopen_ticket')
        ];
        $sql = "INSERT INTO " . $db_config['prefix'] . '_' . $module_data . "_alllog (userid, log_area, log_key, log_time, log_data, ticket_id) VALUES (" . $user_info['userid'] . ", 0, 'LOG_USER_PAYMENT_TICKET', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $row['id'] . ")";
        $db->query($sql);
        $query = 'UPDATE ' . TB_TICKET_ROW . ' SET
            status = ' . TicketStatus::Process->value . ', status_client = ' . TicketStatusClient::Process->value . ',
            edit_time=' . NV_CURRENTTIME . ', activity_time=' . NV_CURRENTTIME . ' WHERE id=' . $array_data['id'];
        $db->query($query);
        $response = [
            'status' => 'success',
            'message' => $nv_Lang->getModule('ticket_reopen')
        ];
    }
    // Gữi thông báo tới admin người chăm sóc khi ticket được mở lại
    reopen_notification_to_caregiver($row);
    nv_jsonOutput($response);
}
// Change notify status
if ($nv_Request->isset_request('change_notify_status', 'post, get')) {
    $response = ['status' => 'error'];
    if (!empty($array_data)) {
        $notify = ($array_data['notify']) ? 0 : 1;
        $query = 'UPDATE ' . TB_TICKET_ROW . ' SET notify=' . intval($notify) . ' WHERE id=' . $array_data['id'];
        $db->query($query);
        $response = ['status' => 'success'];
    }
    nv_jsonOutput($response);
}

$page_title = $row['title'] . ' - ' . $module_config[$module_name]['sitename'];
$key_words = $module_info['keywords'];

$array_mod_title[] = array(
    'title' => $row['title']
);

$per_page = 10;
$page = $nv_Request->get_int('page', 'post,get', 1);
$db->sqlreset()
    ->select('COUNT(log_id)')
    ->from(TB_TICKET_LOG)
    ->where('ticket_id=' . $array_data['id']);
$sth = $db->prepare($db->sql());
$sth->execute();
$total = $sth->fetchColumn();

//Thanh toán
if (!class_exists('nukeviet_points')) {
    require_once NV_ROOTDIR . '/modules/points/points.class.php';
}
$nv_points = new nukeviet_points();
$customs_points = $nv_points->my_point($user_info['userid']);

if ($nv_Request->isset_request('payment', 'post') && $nv_Request->get_string('point_token', 'post', '') == NV_CHECK_SESSION) {
    $ticket_id = $nv_Request->get_int('ticket_id', 'post', 0);
    $row = $db->query('SELECT * FROM ' . TB_TICKET_ROW . ' WHERE id=' . $ticket_id . ' AND customer_id=' . $user_info['userid'] . ' AND delete_time=0')->fetch();
    if (empty($row)) {
        die();
    }

    $message = $nv_Lang->getModule('payment_log');
    $payment_confirm = $nv_Request->get_int('payment_confirm', 'post', 0);
    $display_point = $nv_Request->get_int('display_point', 'post', 0);

    if ($display_point != $row['point_price']) {
        $response = [
            'status' => 'changed',
            'message' => $nv_Lang->getModule('payment_changed_price')
        ];
    } else {
        $stmt = $db->prepare('UPDATE ' . TB_TICKET_ROW . ' SET payment_status=:payment_status, payment_time=:payment_time, status_client=:status_client, status=:status, edit_time=' . NV_CURRENTTIME . '  WHERE id=' . $ticket_id);

        $has_log = false;
        if ($payment_confirm == 1) {
            $update_point = $nv_points->update(0, $row['point_price'], $user_info['userid'], $message);
            if ($nv_points->isError()) {
                $response = [
                    'status' => 'error',
                    'message' => $update_point
                ];
                $stmt->bindParam(':payment_status', $row['payment_status'], PDO::PARAM_INT);
                $stmt->bindParam(':status_client', $row['status'], PDO::PARAM_INT);
                $stmt->bindParam(':status', $row['status'], PDO::PARAM_INT);
                $stmt->bindValue(':payment_time', NV_CURRENTTIME, PDO::PARAM_INT);
            } else {
                $response = [
                    'status' => 'success',
                    'message' => $nv_Lang->getModule('payment_success')
                ];
                $stmt->bindValue(':payment_status', 1, PDO::PARAM_INT);
                $stmt->bindValue(':status_client', TicketStatusClient::Process->value, PDO::PARAM_INT);
                $stmt->bindParam(':status', TicketStatus::Process->value, PDO::PARAM_INT);
                $stmt->bindValue(':payment_time', 0, PDO::PARAM_INT);
                //Gửi mail/notification khách đã thanh toán
                payment_notification_to_admin($row);
                payment_notification_to_customer($row, true);
                //Ghi log
                $has_log = true;
                $log_data = [
                    $nv_Lang->getModule('log_payment_point')
                ];
            }
        } else {
            $response = [
                'status' => 'close',
                'message' => $nv_Lang->getModule('payment_close')
            ];
            $stmt->bindValue(':payment_status', 0, PDO::PARAM_INT);
            $stmt->bindValue(':status', TicketStatus::Close->value, PDO::PARAM_INT); //Đóng ticket
            $stmt->bindValue(':payment_time', 0, PDO::PARAM_INT);
            $stmt->bindValue(':status_client', TicketStatusClient::Close->value, PDO::PARAM_INT); //Đóng ticket
            //Ghi log
            $has_log = true;
            $log_data = [
                $nv_Lang->getModule('log_payment_close')
            ];
        }
        $exc = $stmt->execute();
    }

    if (!empty($row['id']) && !empty($user_info['userid']) && $has_log) {
        $sql = "INSERT INTO " . $db_config['prefix'] . '_' . $module_data . "_alllog (userid, log_area, log_key, log_time, log_data, ticket_id) VALUES (" . $user_info['userid'] . ", 0, 'LOG_USER_PAYMENT_TICKET', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $row['id'] . ")";
        $db->query($sql);
    }

    nv_jsonOutput($response);
}


if ($nv_Request->isset_request('close', 'post') && $nv_Request->get_string('close_token', 'post', '') == NV_CHECK_SESSION) {
    $ticket_id = $nv_Request->get_int('ticket_id', 'post', 0);
    $row = $db->query('SELECT * FROM ' . TB_TICKET_ROW . ' WHERE id=' . $ticket_id . ' AND customer_id=' . $user_info['userid'] . ' AND delete_time=0')->fetch();
    if (empty($row)) {
        die();
    }

    $stmt = $db->prepare('UPDATE ' . TB_TICKET_ROW . ' SET status=:status, status_client=:status_client, edit_time=' . NV_CURRENTTIME . ', activity_time=' . NV_CURRENTTIME . ' WHERE id=' . $ticket_id);

    $stmt->bindValue(':status', TicketStatus::Close->value, PDO::PARAM_INT); //Đóng ticket
    $stmt->bindValue(':status_client', TicketStatusClient::Close->value, PDO::PARAM_INT); //Đóng ticket
    $exc = $stmt->execute();

    if (!empty($row['id']) && !empty($user_info['userid']) && $exc) {
        close_notification_to_caregiver($row);
        $response = [
            'status' => 'success',
            'message' => $nv_Lang->getModule('ticket_closed')
        ];
        $log_data = [
            $nv_Lang->getModule('log_ticket_close')
        ];
        $sql = "INSERT INTO " . $db_config['prefix'] . '_' . $module_data . "_alllog (userid, log_area, log_key, log_time, log_data, ticket_id) VALUES (" . $user_info['userid'] . ", 0, 'LOG_USER_PAYMENT_TICKET', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $row['id'] . ")";
        $db->query($sql);
        nv_jsonOutput($response);
    }
}

//Comment
$error = $comment = $attach_files = [];
if ($nv_Request->isset_request('commentSubmit', 'post') and $array_data['customer_id'] == $user_info['userid'] and $array_data['status'] != TicketStatusClient::Close->value) {
    $comment['ticket_id'] = $array_data['id'];
    $comment['reply_userid'] = $user_info['userid'];
    $comment['content'] = $nv_Request->get_editor('commentContent', '', NV_ALLOWED_HTML_TAGS);
    $comment['file_attach'] = $_FILES['file_attach'] ?? [];
    $comment['comment_type'] = CommentType::Customer->value;

    if (empty($comment['content'])) {
        $error = $nv_Lang->getModule('error_required_comment_content');
    }

    if (!empty($comment['file_attach'])) {
        $count_files = count($comment['file_attach']['name']);
        if ($count_files > NV_ATTACH_LIMITED) {
            $error = sprintf($nv_Lang->getModule('error_upload_limited'), NV_ATTACH_LIMITED);
        } else {
            for ($i = 0; $i < $count_files; $i++) {
                $file['name'] = $comment['file_attach']['name'][$i];
                $file['type'] = $comment['file_attach']['type'][$i];
                $file['size'] = $comment['file_attach']['size'][$i];
                $file['tmp_name'] = $comment['file_attach']['tmp_name'][$i];
                $file['error'] = $comment['file_attach']['error'][$i];
                $attach_files[] = $file;
            }
        }
    }

    if (!empty($attach_files)) {
        $uploaded = multi_upload($attach_files);
        if ($uploaded['status'] == 'error') {
            $error = $uploaded['message'];
        }
    }

    if (empty($error)) {
        $log_id = 0;
        $ticket_id = $comment['ticket_id'];
        $reply_userid = $comment['reply_userid'];
        $content = $comment['content'];
        $comment_type = $comment['comment_type'];
        $file_attach = '';
        if (isset($uploaded) and $uploaded['status'] == 'success') {
            $file_attach = serialize($uploaded['data']);
        }

        $current_time = NV_CURRENTTIME;
        if ($log_id == 0) {
            $stmt = $db->prepare('INSERT INTO ' . TB_TICKET_LOG . ' (content, ticket_id, file_attach, reply_userid, display_userid, add_time, comment_type, ticket_add_time) VALUES (:content, :ticket_id, :file_attach, :reply_userid, :display_userid, :add_time, :comment_type, :ticket_add_time)');

            $stmt->bindParam(':reply_userid', $reply_userid, PDO::PARAM_INT);
            $stmt->bindParam(':display_userid', $reply_userid, PDO::PARAM_INT);
            $stmt->bindParam(':ticket_id', $ticket_id, PDO::PARAM_INT);
            $stmt->bindParam(':add_time', $current_time, PDO::PARAM_INT);
            $stmt->bindParam(':comment_type', $comment_type, PDO::PARAM_INT);
            $stmt->bindParam(':ticket_add_time', $array_data['add_time'], PDO::PARAM_INT);
        } else {
            $stmt = $db->prepare('UPDATE ' . TB_TICKET_LOG . ' SET content = :content, file_attach = :file_attach, edit_time = :edit_time WHERE log_id=' . $log_id);

            $stmt->bindParam(':edit_time', $current_time, PDO::PARAM_INT);
        }
        $stmt->bindParam(':content', $content, PDO::PARAM_STR, strlen($content));
        $stmt->bindParam(':file_attach', $file_attach, PDO::PARAM_STR);

        $exc = $stmt->execute();
        if ($exc) {
            $nv_Cache->delMod($module_name);
            if (empty($log_id)) {
                // issue 2145: Bổ sung status "Khách đã trả lời" khi khách reply
                $stmt = $db->prepare('UPDATE ' . TB_TICKET_ROW . ' SET last_comment_userid = :last_comment_userid, last_comment_time = :last_comment_time, status = :status, status_client = :status_client, activity_time = :activity_time WHERE id=' . $ticket_id);
                $stmt->bindValue(':status', TicketStatus::Process->value);
                $stmt->bindValue(':status_client', TicketStatusClient::Process->value);
                $stmt->bindParam(':last_comment_userid', $reply_userid, PDO::PARAM_INT);
                $stmt->bindValue(':last_comment_time', NV_CURRENTTIME, PDO::PARAM_INT);
                $stmt->bindValue(':activity_time', NV_CURRENTTIME, PDO::PARAM_INT);
                $exc = $stmt->execute();

                $log_id = $db->lastInsertId();
                nv_insert_logs(NV_LANG_DATA, $module_name, 'Add log', ' ', $reply_userid);
                // Ghi log
                $log_data = [
                    LogKey::CustomerReply->getLabel(),
                    [
                        $nv_Lang->getModule('log_data'),
                        $comment['content'],
                    ]
                ];
                add_ticket_logs($user_info['userid'], LogKey::CustomerReply->value, $log_data, $ticket_id);

                comment_notification_to_caregiver($array_data);
                nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail'] . '&id=' . $array_data['id']);
            } else {
                nv_insert_logs(NV_LANG_DATA, $module_name, 'Edit log', 'ID: ' . $log_id, $reply_userid);
            }
        } else {
            multi_upload_unlink($uploaded['dir']);
        }
    }
}

//Xem ảnh
if ($nv_Request->isset_request('preview', 'get') and $array_data['customer_id'] == $user_info['userid']) {
    $file = $nv_Request->get_string('preview', 'get', '');
    $extension = pathinfo(NV_UPLOADS_REAL_DIR . '/' . $module_upload . '/' . $file, PATHINFO_EXTENSION);
    $extension = $extension == 'jpg' ? 'jpeg' : $extension;

    if (file_type($extension) == 'image') {
        $path = NV_UPLOADS_REAL_DIR . '/' . $module_upload . '/' . $file;
        if (!file_exists($path)) {
            header('HTTP/1.0 404 Not Found');
            exit;
        }

        header('Pragma: public');
        header('Expires: 0');
        header('Cache-Control: public');
        header('Content-Description: File Transfer');
        header('Content-Type: image/' . $extension);
        header('Content-Length: ' . filesize($path));

        readfile($path);
    } else {
        $file_info = pathinfo(NV_UPLOADS_REAL_DIR . '/' . $module_upload . '/' . $file);
        $download = new NukeViet\Files\Download(NV_UPLOADS_REAL_DIR . '/' . $module_upload . '/' . $file, $file_info['dirname'], $file_info['basename'], true);
        $download->download_file();
    }
    exit;
}

//List cmt
$db->select('*')
    ->order('log_id DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$sth = $db->prepare($db->sql());
$sth->execute();
$i = ($page - 1) * $per_page;
$array_comment = [];
$customer_info = get_user_info($array_data['customer_id']);
while ($cmt = $sth->fetch()) {
    $cmt['stt'] = $i + 1;
    $reply_userid = $cmt['display_userid'] > 0 ? $cmt['display_userid'] : $cmt['reply_userid'];
    $get_user_info = ($cmt['reply_userid'] == $array_data['customer_id'] &&  $cmt['display_userid'] == 0) ? $customer_info : get_user_info($reply_userid);
    $cmt['add_time'] = nv_date('d/m/Y H:i:s', $cmt['add_time']);
    $cmt['file_attach'] = !empty($cmt['file_attach']) ? unserialize($cmt['file_attach']) : [];
    $cmt['user_extend'] = get_user_extend($reply_userid);
    $cmt['display_position'] = display_position($cmt);
    $cmt['display_fullname'] = display_fullname($cmt, $get_user_info);
    $array_comment[$cmt['log_id']] = $cmt;
    ++$i;
}
$array_comment = array_reverse($array_comment);

if ($page > 1 and empty($array_comment)) {
    if (empty($array_comment)) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
    }
    $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
}
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail'] . '&amp;id=' . $ticket_id;
$generate_page = nv_generate_page($base_url, $total, $per_page, $page);

// lấy page_url
$page_url = $base_url;
if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
}
$canonicalUrl = getCanonicalUrl($page_url);
$urlappend = '&amp;page=';
// Kiểm tra đánh số trang
betweenURLs($page, ceil($total / $per_page), $base_url, $urlappend, $prevPage, $nextPage);

$user_vips = get_user_vips($array_data['customer_id']);
$user_orders = get_user_orders($array_data['customer_id']);
$assignee_list = [];
if (!empty($array_data['assignee_to'])) {
    $array_data['assignee_to'] = explode(',', $array_data['assignee_to']);
    foreach ($array_data['assignee_to'] as $userid) {
        $get_user_info = get_user_info($userid);
        if (!empty($get_user_info)) {
            $assignee_list[] = $get_user_info['fullname'];
        }
    }
}
$array_data['assignee_list'] = empty($assignee_list) ? '' : implode(', ', $assignee_list);

// #issue 2273: Bổ sung tính năng mở lại ticket
$time_allowed_open = (86400 * $module_config[$module_name]['open_reply']) + $array_data['activity_time'];
$is_open_ticket = false;
if ($array_data['status'] == TicketStatus::Close->value && $array_data['status_client'] == TicketStatusClient::Close->value && $time_allowed_open > NV_CURRENTTIME) {
    $is_open_ticket = true;
}

$contents = nv_theme_supportticket_detail($array_data, $array_comment, $comment, $error, $page_url, $generate_page, $customer_info, $customs_points, $attach_files, $user_vips, $user_orders, $is_open_ticket);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
