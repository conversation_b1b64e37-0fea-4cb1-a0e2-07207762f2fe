<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_MAINFILE')) {
    die('Stop!!!');
}

define('NV_MOD_REDDAY', 'nv4_vi_redday');

/**
 * Hàm trả về mảng chứa thời gian bắt đầu và kết thúc của tháng
 * @param int $timestamp
 * @return number[]
 */
function getMonthRange(?int $timestamp = 0)
{
    if (empty($timestamp)) {
        $timestamp = NV_CURRENTTIME;
    }

    $month = intval(date('n', $timestamp));
    $year = intval(date('Y', $timestamp));
    $numDay = cal_days_in_month(CAL_GREGORIAN, $month, $year);

    $startMonth = mktime(0, 0, 0, $month, 1, $year);
    $endMonth = mktime(23, 59, 59, $month, $numDay, $year);

    $startMonth -= (date('N', $startMonth) - 1) * 86400;
    $endMonth += (7 - date('N', $endMonth)) * 86400;

    $weeks = ($endMonth - $startMonth + 1) / 86400 / 7;
    if ($weeks < 6) {
        $endMonth += (7 * 86400);
    }

    return [$startMonth, $endMonth];
}
