<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdateOrganizations implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'organizations';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';
        global $array_user_id_users;

        $row = [];
        $error = '';
        $error_code = '0000';
        $organizationsid = $nv_Request->get_int('organizationsid', 'post', '0');
        if ($organizationsid > 0) {
            $row_old = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_organizations WHERE id=' . $organizationsid)->fetch();
            if (empty($row_old)) {
                return $this->result->setError()
                    ->setCode('2004')
                    ->setMessage('Organizations not exit')
                    ->getResult();
            }

            $admin_id = $nv_Request->get_int('admin_id', 'post', '0');

            $data = $nv_Request->get_array('data', 'post');
            if (!is_array($data)) {
                return $this->result->setError()
                    ->setCode('2000')
                    ->setMessage('Param data invalid to json')
                    ->getResult();
            }
            $row = $data;

            $keys_check = [
                'tax',
                'organizationname',
                'shortname',
                'website',
                'primaryphone',
                'secondaryphone',
                'fax',
                'primaryemail',
                'secondaryemail',
                'employees',
                'address',
                'trading_address',
                'represent_name',
                'represent_position',
                'represent_address',
                'description',
                'active',
                'convert_leads'
            ];

            if (!empty($data)) {
                // check $field
                $array_sql = [];
                foreach ($data as $key => $value) {
                    if (!in_array($key, $keys_check)) {
                        return $this->result->setError()
                            ->setCode('2002')
                            ->setMessage('Missing field ' . $key . ' in otherdata')
                            ->getResult();
                    }
                    $array_sql[$key] = $key . '=:' . $key;
                }

                if ($row['organizationname'] == '') {
                    $error_code = '2003';
                    $error = $nv_Lang->getModule('error_required_orgainzation');
                } else if ($row['primaryphone'] == '' and $row['primaryemail'] == '') {
                    $error_code = '2004';
                    $error = $nv_Lang->getModule('error_required_phone');
                } elseif ($row['primaryphone'] != '' && !phonecheck($row['primaryphone'])) {
                    $error_code = '2005';
                    $error = sprintf($nv_Lang->getModule('error_sub_phone'), $row['primaryphone']);
                    // $error = $nv_Lang->getModule('error_phone_number');
                } elseif ($row['primaryemail'] != '' && nv_check_valid_email($row['primaryemail']) != '') {
                    $error_code = '2006';
                    $error = $nv_Lang->getModule('error_email');
                } else if ($admin_id > 0 and !isset($array_user_id_users[$admin_id])) {
                    $error_code = '2011';
                    $error = $nv_Lang->getModule('admin_id_not_defind');
                }

                if (isset($row['secondaryemail']) and $row['secondaryemail'] != '') {
                    $row['secondaryemail'] = str_replace(';', ',', $row['secondaryemail']);
                    $row['secondaryemail'] = str_replace("\n", ',', $row['secondaryemail']);
                    $_arr_email = array();
                    $list_mail = explode(',', $row['secondaryemail']);
                    foreach ($list_mail as $_mail) {
                        $_mail = trim($_mail);
                        if (($check_valid_email = nv_check_valid_email($_mail)) != '') {
                            $error_code = '2008';
                            $error = sprintf($nv_Lang->getModule('error_subemail'), $_mail);
                        } elseif (!in_array($_mail, $_arr_email)) {
                            $_arr_email[] = $_mail;
                        }
                    }
                    $row['secondaryemail'] = implode(',', $_arr_email);
                }

                if (isset($row['secondaryphone']) and $row['secondaryphone'] != '') {
                    $row['secondaryphone'] = str_replace(';', ',', $row['secondaryphone']);
                    $row['secondaryphone'] = str_replace("\n", ',', $row['secondaryphone']);
                    $_arr_phone = array();
                    $list_phone = explode(',', $row['secondaryphone']);
                    foreach ($list_phone as $_phone) {
                        $_phone = trim($_phone);
                        $_arr_phone[] = $_phone;
                    }

                    if ($row['secondaryphone'] != '' && !phonecheck($row['secondaryphone'])) {
                        $error_code = '2007';
                        $error = sprintf($nv_Lang->getModule('error_sub_phone'), $row['secondaryphone']);
                    }

                    $row['secondaryphone'] = implode(',', $_arr_phone);
                }

                if (isset($row['tax']) and taxcodecheck2($row['tax']) == false) {
                    $error = $nv_Lang->getModule('error_tax');
                    $error_code = '2014';
                }

                if (empty($error)) {
                    try {
                        $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_organizations SET ' . implode(',', $array_sql) . ', updatetime=' . NV_CURRENTTIME . '  WHERE id=' . $organizationsid);

                        foreach ($row as $key => $value) {
                            $stmt->bindValue(':' . $key, $value, PDO::PARAM_STR);
                        }
                        $exc = $stmt->execute();
                        if ($exc) {
                            // ghi log
                            $log_data = [
                                $nv_Lang->getModule('log_update_organizations')
                            ];

                            foreach ($row as $key => $value) {
                                if ($row_old[$key] != $row[$key]) {
                                    $log_data[] = [
                                        $nv_Lang->getModule($key),
                                        $row_old[$key] . ' =&gt; ' . $row[$key]
                                    ];
                                }
                            }

                            if (sizeof($log_data) > 1) { // không có gì thay đổi thì k ghi log
                                $sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (userid, log_area, log_key, log_time, log_data, organizationsid) VALUES (" . $admin_id . ", 1, 'LOG_ADMIN_UPDATE_ORGANIZATIONS', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $organizationsid . ")";
                                $db->query($sql);
                            }
                            $nv_Cache->delMod($module_name);
                            nv_insert_logs(NV_LANG_DATA, $module_name, 'Edit organizations Info', 'ID: ' . $organizationsid, $admin_id);
                            $this->result->setSuccess();
                            $this->result->set('organizationsid', $organizationsid);
                        }
                    } catch (PDOException $e) {
                        $this->result->setError()
                            ->setCode('3000')
                            ->setMessage(print_r($e, true));
                    }
                } else {
                    return $this->result->setError()
                        ->setCode($error_code)
                        ->setMessage($error)
                        ->getResult();
                }
            } else {
                return $this->result->setError()
                    ->setCode('2001')
                    ->setMessage('Param data empty')
                    ->getResult();
            }
        } else {
            return $this->result->setError()
                ->setCode('2003')
                ->setMessage('organizationsid is integer, more than 0' . $organizationsid)
                ->getResult();
        }

        return $this->result->getResult();
    }
}
