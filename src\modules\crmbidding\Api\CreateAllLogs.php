<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CreateAllLogs implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'logs';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $error = '';
        $error_code = '0000';

        $row['userid'] = $nv_Request->get_int('userid', 'post', 0);
        $row['log_area'] = $nv_Request->get_int('log_area', 'post', 0);
        $row['log_key'] = $nv_Request->get_title('log_key', 'post', '');
        $row['log_time'] = $nv_Request->get_int('log_time', 'post', 0);
        $row['log_data'] = $nv_Request->get_array('log_data', 'post', []);
        $row['leads_id'] = $nv_Request->get_int('leads_id', 'post', 0);
        $row['oppotunities_id'] = $nv_Request->get_int('oppotunities_id', 'post', 0);
        $row['vips_id'] = $nv_Request->get_int('vips_id', 'post', 0);
        $row['orderid'] = $nv_Request->get_int('orderid', 'post', 0);
        $row['log_data'] = json_encode($row['log_data']);
        $row['profile_active_id'] = $nv_Request->get_int('profile_active_id', 'post', 0);
        $row['log_type'] = $nv_Request->get_int('log_type', 'post', 0);
        $row['caregiver_id'] = $nv_Request->get_int('caregiver_id', 'post', 0);

        if ($row['userid'] < 0) {
            $error_code = '2001';
            $error = $nv_Lang->getModule('error_invalid_userid');
        } elseif ($row['log_area'] < 0 or $row['log_area'] > 1) {
            $error_code = '2002';
            $error = $nv_Lang->getModule('error_invalid_log_area');
        } elseif ($row['log_key'] == '') {
            $error_code = '2003';
            $error = $nv_Lang->getModule('error_required_log_key');
        } elseif ($row['log_time'] < 0) {
            $error_code = '2004';
            $error = $nv_Lang->getModule('error_invalid_log_time');
        } elseif ($row['log_data'] == '') {
            $error_code = '2005';
            $error = $nv_Lang->getModule('error_required_log_data');
        } elseif ($row['leads_id'] < 0) {
            $error_code = '2006';
            $error = $nv_Lang->getModule('error_invalid_leads_id');
        } elseif ($row['profile_active_id'] < 0) {
            $error_code = '2009';
            $error = $nv_Lang->getModule('error_invalid_profile_active_id');
        } elseif ($row['oppotunities_id'] < 0) {
            $error_code = '2007';
            $error = $nv_Lang->getModule('error_invalid_oppotunities_id');
        } elseif ($row['orderid'] < 0) {
            $error_code = '2008';
            $error = $nv_Lang->getModule('error_invalid_order_id');
        }

        if (empty($error)) {
            try {
                $stmt = $db->prepare("INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (userid, log_area, log_key, log_time, log_data, leads_id, oppotunities_id, vips_id, profile_active_id, orderid, log_type, caregiver_id) VALUES (:userid, :log_area, :log_key, :log_time, :log_data, :leads_id, :oppotunities_id, :vips_id, :profile_active_id, :orderid, :log_type, :caregiver_id)");

                $stmt->bindParam(':userid', $row['userid'], PDO::PARAM_INT);
                $stmt->bindParam(':log_area', $row['log_area'], PDO::PARAM_INT);
                $stmt->bindParam(':log_key', $row['log_key'], PDO::PARAM_STR);
                $stmt->bindParam(':log_time', $row['log_time'], PDO::PARAM_INT);
                $stmt->bindParam(':log_data', $row['log_data'], PDO::PARAM_STR);
                $stmt->bindParam(':leads_id', $row['leads_id'], PDO::PARAM_INT);
                $stmt->bindParam(':oppotunities_id', $row['oppotunities_id'], PDO::PARAM_INT);
                $stmt->bindParam(':vips_id', $row['vips_id'], PDO::PARAM_INT);
                $stmt->bindParam(':profile_active_id', $row['profile_active_id'], PDO::PARAM_INT);
                $stmt->bindParam(':orderid', $row['orderid'], PDO::PARAM_INT);
                $stmt->bindParam(':log_type', $row['log_type'], PDO::PARAM_INT);
                $stmt->bindParam(':caregiver_id', $row['caregiver_id'], PDO::PARAM_INT);

                $exc = $stmt->execute();
                $id = $db->lastInsertId();
                if ($exc and $id > 0) {

                    $this->result->setSuccess();
                    $this->result->set('logsid', $id);
                } else {
                    $this->result->setError()
                        ->setCode('4001')
                        ->setMessage('Logsid not response');
                }
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode($error_code)
                ->setMessage($error)
                ->getResult();
        }
        return $this->result->getResult();
    }
}
