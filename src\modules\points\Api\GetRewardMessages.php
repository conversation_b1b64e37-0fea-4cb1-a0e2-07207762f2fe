<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\points\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetRewardMessages implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'point';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        // Các site thanh toán
        $sql = "SELECT * FROM " . $db_config['prefix'] . "_wallet_sites ORDER BY weight ASC";
        $global_array_sites = $nv_Cache->db($sql, 'id', 'wallet');

        $array = [];
        $array['site_id'] = $nv_Request->get_absint('site_id', 'post', 0);
        $array['userid'] = $nv_Request->get_absint('userid', 'post', 0);
        $array['limit'] = $nv_Request->get_absint('limit', 'post', 0) ?: 5;
        if ($array['limit'] > 1000) {
            $array['limit'] = 5;
        }

        if (!isset($global_array_sites[$array['site_id']])) {
            return $this->result->setCode('1001')->setMessage($nv_Lang->getModule('api_err_site'))->getResult();
        }

        $sql = "SELECT expired_time, give_type, message_reward, online10
        FROM " . $db_config['prefix'] . "_" . $module_data . "_reward_messages
        WHERE userid=" . $array['userid'] . " AND status=0 AND expired_time > " . NV_CURRENTTIME . "
        ORDER BY add_time ASC LIMIT " . $array['limit'];
        $this->result->set('data', $db->query($sql)->fetchAll() ?: []);
        $this->result->setSuccess();

        // Cập nhật thông báo đã đọc
        $sql = "UPDATE " . $db_config['prefix'] . "_" . $module_data . "_reward_messages
        SET status=1 WHERE userid=" . $array['userid'] . " AND status=0 ORDER BY add_time ASC LIMIT " . $array['limit'];
        $numchange = $db->exec($sql);

        // Tính toán xem thành viên này còn thông báo không
        if (empty($numchange)) {
            $sql = "UPDATE " . $db_config['prefix'] . "_" . $module_data . "_customs
            SET have_msg=0, updatetime=" . NV_CURRENTTIME . "
            WHERE userid=" . $array['userid'];
            if ($db->exec($sql)) {
                $nv_Cache->delMod($module_name);
            }
        }

        return $this->result->getResult();
    }
}
