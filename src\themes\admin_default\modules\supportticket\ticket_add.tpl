<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<link type="text/css" href="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/i18n/{NV_LANG_INTERFACE}.js"></script>
<!-- BEGIN: error -->
<div class="alert alert-warning">{ERROR}</div>
<!-- END: error -->
<!-- BEGIN: success -->
<div class="alert alert-success">{SUCCESS}</div>
<!-- END: success -->
<div class="panel panel-default">
    <div class="panel-body">
        <form class="form-horizontal" action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post" enctype="multipart/form-data">
            <input type="hidden" name="id" value="{ROW.id}" />
            <div class="form-group">
                <label class="col-sm-5 col-md-3 control-label"><strong>{LANG.cat_id}</strong> <span class="red">(*)</span></label>
                <div class="col-sm-19 col-md-20">
                    <select class="form-control" name="cat_id" id="cat_id">
                        <option value=""> --- </option>
                        <!-- BEGIN: select_cat_id -->
                        <option value="{OPTION.key}" {OPTION.selected}>{OPTION.title}</option>
                        <!-- END: select_cat_id -->
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-5 col-md-3 control-label"><strong>{LANG.request_title}</strong> <span class="red">(*)</span></label>
                <div class="col-sm-19 col-md-20">
                    <input class="form-control" type="text" name="title" value="{ROW.title}" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-5 col-md-3 control-label"><strong>{LANG.ticket_content}</strong> <span class="red">(*)</span></label>
                <div class="col-sm-19 col-md-20">
                    {CONTENT}
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-5 col-md-3 control-label"><strong>{LANG.customer_id}</strong><span class="red">(*)</span></label>
                <div class="col-sm-19 col-md-20">
                    <select id="element_customer_id" name="customer_id" class="form-control">
                        <option value="0">{LANG.customer_any}</option>
                        <!-- BEGIN: select_customer -->
                        <option value="{CUSTOMER.userid}" selected="selected">{CUSTOMER.title}</option>
                        <!-- END: select_customer -->
                    </select>
                </div>
            </div>
            <div class="form-group" id="vip-group">
                <label class="col-sm-5 col-md-3 control-label"><strong>{LANG.vip_id}</strong></label>
                <div class="col-sm-19 col-md-20">
                    <select class="form-control" name="vip_id">
                        <option value="0">{LANG.select_vip}</option>
                    </select>
                </div>
            </div>
            <div class="form-group" id="order-group">
                <label class="col-sm-5 col-md-3 control-label"><strong>{LANG.order_id}</strong></label>
                <div class="col-sm-19 col-md-20">
                    <select class="form-control" name="order_id">
                        <option value="0">{LANG.select_order}</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-5 col-md-3 control-label"><strong>{LANG.label_ids}</strong> <span class="red">(*)</span></label>
                <div class="col-sm-19 col-md-20">
                    <select class="form-control" name="label_ids[]" multiple="multiple">
                        <option value=""> --- </option>
                        <!-- BEGIN: select_label_ids -->
                        <option value="{OPTION.key}" {OPTION.selected}>{OPTION.title}</option>
                        <!-- END: select_label_ids -->
                    </select>
                </div>
            </div>
            <!--
            <div class="form-group">
                <label class="col-sm-5 col-md-3 control-label"><strong>{LANG.assignee_to}</strong></label>
                <div class="col-sm-19 col-md-20">
                    <select class="form-control" name="assignee_to[]" multiple="multiple">
                        <!-- BEGIN: select_assignee -->
                            <option value="{OPTION.key}" {OPTION.selected}>{OPTION.title}</option>
                        <!-- END: select_assignee -->
                    </select>
                </div>
            </div>
            -->
            <div class="form-group">
                <label class="col-sm-5 col-md-3 control-label"><strong>{LANG.ticket_attach}</strong></label>
                <div class="col-sm-19 col-md-20">
                    <button type="button" class="btn-sm btn btn-success" data-toggle="rowElementAdd">
                        <i class="fa fa-plus-circle" aria-hidden="true"></i> {LANG.ticket_attach_add}
                    </button>
                    <span class="help-block">{LANG.allowed_file_extensions}</span>
                    <span class="help-block">{LIMITED_FILE_SIZE}</span>
                    <table class="table table-striped table-bordered table-hover">
                        <tbody id="element-rows-container">
                            <!-- BEGIN: files -->
                            <tr data-toggle="rowElement" data-offset="{FILE}">
                                <td>
                                    <input type="file" class="form-control" name="file_attach[]"/>
                                </td>
                                <th style="width: 30px">
                                    <div class="input-group-btn">
                                        <button class="btn btn-danger" data-toggle="rowElementDel" data-offset="{FILE}"><i class="fa fa-trash"></i></button>
                                    </div>
                                </td>
                            </tr>
                            <!-- END: files -->
                        <tbody>
                    </table>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-5 col-md-3 control-label"><strong>{LANG.send_notify}</strong></label>
                <div class="col-sm-19 col-md-20">
                    <div class="checkbox">
                        <!-- BEGIN: select_notify -->
                        <label><input class="form-control" type="radio" name="send_notify" value="{OPTION.key}" {OPTION.checked}>{OPTION.title} &nbsp;</label>
                        <!-- END: select_notify -->
                    </div>
                </div>
            </div>
            <div class="form-group" style="text-align: center"><input class="btn btn-primary" id="btnAddTicket" name="submit" type="submit" value="{LANG.send}" /></div>
        </form>
    </div>
</div>
<script type="text/javascript">
    $("select[name='assignee_to[]']").select2({
        placeholder: "{LANG.assignee_to}"
    });
    $("select[name='label_ids[]']").select2({
        placeholder: "{LANG.label_ids}"
    });

    // Lấy thông tin khách hàng
    $('#element_customer_id').select2({
        width: '100%',
        language: '{NV_LANG_INTERFACE}',
        ajax: {
            delay: 250,
            cache: false,
            type: 'POST',
            url: script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '={OP}&nocache=' + new Date().getTime(),
            dataType: 'json',
            data: function (params) {
                return {
                    q: params.term,
                    ajax_get_customer: '{NV_CHECK_SESSION}',
                    page: params.page || 1
                };
            }
        },
        placeholder: '{LANG.customer_pick}',
        minimumInputLength: 2
    });

    var isFirstRun = true;
    function getCustomer() {
        var userId = $('[id="element_customer_id"]').val();
        $("[name='customer_id']").val(userId);
        if (isFirstRun || (userId > 0 && userId != old_userId)) {
            isFirstRun = false;

            old_userId = userId;
            $('select[name="vip_id"]').empty();
            $('select[name="order_id"]').empty();
            $.ajax({
                type: "POST",
                url: window.location.href,
                data: "&getuser=1&userid=" + userId,
                dataType: "json",
                success: function(data) {
                    $.each(data.vips, function (key, value) {
                        var option = $('<option></option>').val(value.id).html(value.title);
                        if (value.id == {ROW.vip_id}) {
                            option.attr("selected", "selected");
                        }
                        $('select[name="vip_id"]').append(option);
                    });

                    $.each(data.orders, function (key, value) {
                        var display_title = key > 0 ? '#' + value.id + ' ' + value.title : value.title;
                        var option = $('<option></option>').val(value.id).html(display_title);
                        if (value.id == {ROW.order_id}) {
                            option.attr("selected", "selected");
                        }
                        $('select[name="order_id"]').append(option);
                    });
                }
            });
        }
    }
    getCustomer();
    setInterval(function() {
        getCustomer();
    }, 1000);
</script>
<script type="text/javascript">
    $(document).ready(function() {
        var row_count = {TOTAL_FILE};
        // Xử lý hiển thị select Gói Víp hoặc Đơn hàng
        var selectElement = document.getElementById('cat_id');
        $('#vip-group, #order-group').hide();
        selectElement.addEventListener('change', function() {
            $.ajax({
                type: 'POST',
                url: window.location.href,
                data: '&getcatid=1&cat_id=' + selectElement.value,
                dataType: 'json',
                success: function(data) {
                    if (data.is_customer == 1) {
                        $('#vip-group, #order-group').show();
                    } else {
                        $('#vip-group, #order-group').hide();
                    }
                }
            });
        });

        $(document).delegate('[data-toggle="rowElementDel"]', 'click', function(e) {
            e.preventDefault();
            var offset = $(this).data('offset');
            $('[data-toggle="rowElement"][data-offset="' + offset + '"]').remove();
        });
        $('[data-toggle="rowElementAdd"]').on('click', function(e) {
            e.preventDefault();
            var item_count = $('[data-toggle="rowElement"]').length;
            if (item_count >= {ATTACH_LIMITED}) {
                alert('{ATTACH_LIMITED_MESSAGE}');
            } else {
                var html = '\
                <tr data-toggle="rowElement" data-offset="' + row_count + '">\
                    <td>\
                        <input type="file" class="form-control" name="file_attach[]"/>\
                    </td>\
                    <th style="width: 30px">\
                        <div class="input-group-btn">\
                            <button class="btn btn-danger" data-toggle="rowElementDel" data-offset="' + row_count + '"><i class="fa fa-trash"></i></button>\
                        </div>\
                    </td>\
                </tr>';
                $('#element-rows-container').append(html);
                row_count++;
            }
        });

        //Cảnh báo nếu file quá dung lượng, phòng trường hợp file lớn bị web server chặn lại trước khi php xử lý
        $('#btnAddTicket').on('click', function(event) {
            var filesInput = $('input[name="file_attach[]"]');
            var isSizeExceeded = false;
            filesInput.each(function() {
                var files = this.files;
                for (var i = 0; i < files.length; i++) {
                    var fileSize = files[i].size;
                    var maxSize = {NV_UPLOAD_MAX_FILESIZE}
                    if (fileSize > maxSize) {
                        alert('File ' + files[i].name + ' {LANG.error_file_size}');
                        $(this).val('');
                        isSizeExceeded = true;
                        break;
                    }
                }
            });

            if (isSizeExceeded) {
                event.preventDefault();
            }
        });
    });
</script>
<!-- END: main -->
