<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:39:33 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$error = [];
$array_field = array();
$array_field['name'] = $nv_Lang->getModule('name');
$array_field['phone'] = $nv_Lang->getModule('phone');
$array_field['email'] = $nv_Lang->getModule('email');
$array_field['tax'] = $nv_Lang->getModule('tax');
$array_field['company_name'] = $nv_Lang->getModule('company_name');
$array_field['sub_phone'] = $nv_Lang->getModule('sub_phone');
$array_field['sub_email'] = $nv_Lang->getModule('sub_email');
$type = $nv_Request->get_int('type', 'post,get', 1);
// L<PERSON>y danh sách các column trong leads, cơ hội và các thông tin liên quan
$array_field_merger = [];
$params_merger = [
    'type' => $type,
];
$array_field_merger = nv_local_api('ShowColumnLeadsOpp', $params_merger, $admin_info['username'], $module_name);
$array_field_merger = json_decode($array_field_merger, true);
$array_field_merger = $array_field_merger['data'] ?? [];

if (!empty($array_field_merger)) {
    $array_field_merger['comment'] = $nv_Lang->getModule('comment');
    unset($array_field_merger['log_merge']);
}
$array_field_relate = [];
$params_relate = [
    'type' => $type == 1 ? 2 : 1,
];
$array_field_relate = nv_local_api('ShowColumnLeadsOpp', $params_relate, $admin_info['username'], $module_name);
$array_field_relate = json_decode($array_field_relate, true);
$array_field_relate = $array_field_relate['data'] ?? [];
if (!empty($array_field_relate)) {
    $array_field_relate['comment'] = $nv_Lang->getModule('comment');
    unset($array_field_relate['log_merge']);
}

$array_search = $nv_Request->get_array('field', 'post,get', array());
$q_search = $nv_Request->get_title('q_search', 'post,get', '');
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&type=' . $type;
$_arr_rowcheck = [];
$per_page = 50;
$num_items = 0;

if ($nv_Request->isset_request('merger', 'post,get')) {
    $rowcheck = $nv_Request->get_array('rowcheck', 'post,get', array());
    $list_all = $list_relate = [];
    if (!empty($rowcheck)) {
        if ($type == 1) {
            // Lấy danh sách leads
            // $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_leads WHERE id IN (' . implode(',', $rowcheck) . ')';
            $where['AND'][] = [
                'IN' => [
                    'id' => '(' . implode(',', $rowcheck) . ')'
                ]
            ];
            $param_all = [
                'where' => $where,
                'use_elastic' => 1
            ];
            $list_all = nv_local_api('ListAllLeads', $param_all, $admin_info['username'], $module_name);
            $list_all = json_decode($list_all, true);
            $list_id_relate = [];
            $list_all['type'] = 'lead';
            if (!empty($list_all['data'])) {
                foreach ($list_all['data'] as $row) {
                    if ($row['opportunities_id'] > 0) {
                        $list_id_relate[] = $row['opportunities_id'];
                    }
                }
            }
            if (!empty($list_id_relate)) {
                $where_relate['AND'][] = [
                    'IN' => [
                        'id' => '(' . implode(',', $list_id_relate) . ')'
                    ]
                ];
                $param_relate = [
                    'where' => $where_relate,
                    'use_elastic' => 1
                ];
                $list_relate = nv_local_api('ListAllOpportunities', $param_relate, $admin_info['username'], $module_name);
                $list_relate = json_decode($list_relate, true);
            }
        } else if ($type == 2) {
            // Lấy danh sách cơ hội
            $where['AND'][] = [
                'IN' => [
                    'id' => '(' . implode(',', $rowcheck) . ')'
                ]
            ];
            $param_all = [
                'where' => $where,
                'use_elastic' => 1
            ];
            $list_all = nv_local_api('ListAllOpportunities', $param_all, $admin_info['username'], $module_name);
            $list_all = json_decode($list_all, true);
            $list_all['type'] = 'opportunities';
            $list_id_relate = [];
            if (!empty($list_all['data'])) {
                foreach ($list_all['data'] as $row) {
                    if ($row['leadsid'] > 0) {
                        $list_id_relate[] = $row['leadsid'];
                    }
                }
            }

            if (!empty($list_id_relate)) {
                $where_relate['AND'][] = [
                    'IN' => [
                        'id' => '(' . implode(',', $list_id_relate) . ')'
                    ]
                ];
                $param_relate = [
                    'where' => $where_relate,
                    'use_elastic' => 1
                ];
                $list_relate = nv_local_api('ListAllLeads', $param_relate, $admin_info['username'], $module_name);
                $list_relate = json_decode($list_relate, true);
            }
            // $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities WHERE id IN (' . implode(',', $rowcheck) . ')';
        }
    }
    // $result = $db->query($sql);
    // Lấy danh sách comment đi kèm
    $list_comment = [];
    foreach ($list_all['data'] as $key => $value) {
        $_arr_rowcheck[$value['id']] = $value;
        $source = 2;
        if ($list_all['type'] == "lead") {
            $source = 1;
        }
        $sql_comment = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_comment WHERE source = ' . $source . ' AND sourceid = ' . $value['id'] . ' ORDER BY timecreate DESC';
        $result_comment = $db->query($sql_comment);
        while ($row = $result_comment->fetch()) {
            $row['timecreate'] = nv_date('H:i d/m/Y', $row['timecreate']);
            $list_comment[$row['sourceid']][] = $row;
        }
        $_arr_rowcheck[$value['id']]['comment'] = $list_comment[$value['id']] ?? [];
    }
    // Lấy dữ liệu liên quan
    if (!empty($list_relate['data'])) {
        foreach ($list_relate['data'] as $key => $value) {
            $_arr_rowcheck_relate[$value['id']] = $value;
            $source_relate = ($source == 2) ? 1 : 2;
            $sql_comment_relate = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_comment WHERE source = ' . $source_relate .  ' AND sourceid = ' . $value['id'] . ' ORDER BY timecreate DESC';
            $result_comment_relate = $db->query($sql_comment_relate);
            while ($row = $result_comment->fetch()) {
                $list_comment_relate[$row['sourceid']][] = $row;
            }
            $_arr_rowcheck_relate[$value['id']]['comment'] = $list_comment_relate[$value['id']] ?? [];
        }
    }
} else {
    $page = $nv_Request->get_int('page', 'post,get', 1);
    $array_data = array();
    $field_search = 'email';
    if (!empty($array_search)) {
        $sql_select = implode(',', $array_search);
        $sql_where = [];
        $field_search = $array_search[0];
        foreach ($array_search as $value) {
            // $sql_where[] = $value . "!=''";
            $sql_where['AND'][] = [
                '!=' => [
                    $value => ''
                ]
            ];
        }
        if (!empty($q_search)) {
            $q_search = $db->dblikeescape($q_search);
            $sql_where['OR'][] = [
                'like' => [
                    'name' =>  '%' . $q_search . '%'
                ]
            ];
            $sql_where['OR'][] = [
                'like' => [
                    'phone' =>  '%' . $q_search . '%'
                ]
            ];
            $sql_where['OR'][] = [
                'like' => [
                    'email' =>  '%' . $q_search . '%'
                ]
            ];
            $sql_where['OR'][] = [
                'like' => [
                    'tax' =>  '%' . $q_search . '%'
                ]
            ];
            // $sql_where[] = "( name LIKE '%" . $q_search . "%' OR phone LIKE '%" . $q_search . "%' OR email LIKE '%" . $q_search . "%' OR tax LIKE '%" . $q_search . "%')";

        }
        // $sql_where = implode(' AND ', $sql_where);
        $sql_where['AND'][] = [
            '=' => [
                'active' => 1
            ]
        ];
        $order['caregiver_id'] = 'ASC';
        $order[$field_search] = 'ASC';
        if ($type == 1) {
            $param_all = [
                'where' => $sql_where,
                'userid' => $admin_info['userid'],
                'order' => $order,
                'use_elastic' => 1,
                'perpage' => $per_page,
                'page' => $page
            ];
            $list_all = nv_local_api('ListAllLeads', $param_all, $admin_info['username'], $module_name);
            $list_all = json_decode($list_all, true);
            $all_items = $list_all['data'] ?? [];
            $num_items = $list_all['total'] ?? 0;

            foreach ($all_items as $key => $value) {
                $array_data[$value['caregiver_id']][strtolower($value[$field_search])][] = $value;
            }
        } else if ($type == 2) {
            $param_all = [
                'where' => $sql_where,
                'userid' => $admin_info['userid'],
                'use_elastic' => 1,
                'perpage' => $per_page,
                'page' => $page
            ];
            $list_all = nv_local_api('ListAllOpportunities', $param_all, $admin_info['username'], $module_name);
            $list_all = json_decode($list_all, true);

            $all_items = $list_all['data'] ?? [];
            $num_items = $list_all['total'] ?? 0;
            foreach ($all_items as $key => $value) {
                $array_data[$value['caregiver_id']][strtolower($value[$field_search])][] = $value;
            }
        }
    }
}

// nhấn gộp dữ liệu
if ($nv_Request->isset_request('merger_submit', 'post,get')) {
    $leadsmerger = $nv_Request->get_array('leadsmerger', 'post,get', array());
    $relatemerger = $nv_Request->get_array('relatemerger', 'post,get', array());

    $table_merge = ($type == 1) ? NV_PREFIXLANG . "_" . $module_data . "_leads" : NV_PREFIXLANG . "_" . $module_data . "_opportunities";
    $source = ($type == 1) ? 1 : 2;

    // Lấy danh sách comment
    $sql_list_comment = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_comment WHERE source = " . $source .  " AND sourceid IN (" . implode(',', $leadsmerger) . ")";
    $result_comment = $db->query($sql_list_comment);
    while ($row = $result_comment->fetch()) {
        $list_comment[$row['sourceid']][] = $row;
    }

    // Lấy thông tin log trước khi merge
    $sql_list_log = "SELECT * FROM " . $table_merge . " WHERE id IN (" . implode(',', $leadsmerger) . ")";
    $result_log = $db->query($sql_list_log);
    while ($row = $result_log->fetch()) {
        if (isset($row['log_merge'])) {
            unset($row['log_merge']);
        }
        $row['comment'] = $list_comment[$row['id']] ?? [];
        $list_log[] = $row;
    }
    $list_log = json_encode($list_log, true);

    // Nếu có thông tin liên quan
    $list_log_relate = [];
    if (!empty($relatemerger)) {
        $table_merge_relate = ($type == 2) ? NV_PREFIXLANG . "_" . $module_data . "_leads" : NV_PREFIXLANG . "_" . $module_data . "_opportunities";
        $source = ($type == 2) ? 1 : 2;

        $sql_list_log_relate = "SELECT * FROM " . $table_merge_relate . " WHERE id IN (" . implode(',', $relatemerger) . ")";
        $result_log_relate = $db->query($sql_list_log_relate);

        // Lấy comment ở các bảng liên quan
        $sql_list_comment_relate = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_comment WHERE source = " . $source .  " AND sourceid IN (" . implode(',', $relatemerger) . ")";
        $result_comment_relate = $db->query($sql_list_comment_relate);

        while ($row = $result_comment->fetch()) {
            $list_comment_relate[$row['sourceid']] = $row;
        }

        // Lưu log ở các bảng liên quan
        while ($row = $result_log_relate->fetch()) {
            if (isset($row['log_merge'])) {
                unset($row['log_merge']);
            }
            $row['comment'] = $list_comment_relate[$row['id']] ?? [];
            $list_log_relate[] = $row;
        }
        $list_log_relate = json_encode($list_log_relate, true);
    }

    // Lấy các row được chọn
    $array_request = [];
    foreach ($array_field_merger as $key => $value) {
        $array_request[$key] = $nv_Request->get_title($key, 'post,get', '');
    }
    
    $array_request['keep_both_phone'] = $nv_Request->get_int('keep_both_phone', 'post,get', 0);
    $array_request['keep_both_email'] = $nv_Request->get_int('keep_both_email', 'post,get', 0);
    $array_request['keep_both_comment'] = $nv_Request->get_int('keep_both_comment', 'post,get', 0);
    
    $array_request['merged_phone'] = $nv_Request->get_title('merged_phone', 'post,get', '');
    $array_request['merged_email'] = $nv_Request->get_title('merged_email', 'post,get', '');
    $array_request['merged_comment'] = $nv_Request->get_title('merged_comment', 'post,get', '');
    
    $array_request['keep_both_relate_phone'] = $nv_Request->get_int('keep_both_relate_phone', 'post,get', 0);
    $array_request['keep_both_relate_email'] = $nv_Request->get_int('keep_both_relate_email', 'post,get', 0);
    $array_request['keep_both_relate_comment'] = $nv_Request->get_int('keep_both_relate_comment', 'post,get', 0);
    
    $array_request['merged_relate_phone'] = $nv_Request->get_title('merged_relate_phone', 'post,get', '');
    $array_request['merged_relate_email'] = $nv_Request->get_title('merged_relate_email', 'post,get', '');
    $array_request['merged_relate_comment'] = $nv_Request->get_title('merged_relate_comment', 'post,get', '');
    
    $array_request['all_orderid'] = $nv_Request->get_title('all_orderid', 'post,get', '');
    $array_request['all_orderid_dtnet'] = $nv_Request->get_title('all_orderid_dtnet', 'post,get', '');
    $array_request['all_customs_id'] = $nv_Request->get_title('all_customs_id', 'post,get', '');

    // Lấy các row liên quan được chọn
    foreach ($array_field_relate as $key => $value) {
        $array_request_relate[$key] = $nv_Request->get_title($key . '_relate', 'post,get', '');
    }

    // Nếu có row được chọn
    if ($array_request['id'] > 0) {
        if ($type == 1) {
            // $row_old = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_leads WHERE active=1 AND id=' . $array_request['id'])->fetch();
            $params_leads = [
                'leadid' => $array_request['id']
            ];
            $data_leads = nv_local_api('GetDetailLeads', $params_leads, $admin_info['username'], $module_name);
            $row_old = json_decode($data_leads, true);
            $row_old = $row_old['data'];

            $data_update_leads['source_leads'] = $array_request['source_leads'];
            $data_update_leads['log_merge'] = $list_log;
            $data_update_leads['name'] = $array_request['name'];
            $data_update_leads['phone'] = $array_request['phone'];
            $data_update_leads['sub_phone'] = $array_request['sub_phone'];
            if ($array_request['keep_both_phone']) {
                $data_update_leads['sub_phone'] && $data_update_leads['sub_phone'] .= ',';
                $data_update_leads['sub_phone'] .= $array_request['merged_phone'];
            }
            $data_update_leads['email'] = $array_request['email'];
            $data_update_leads['sub_email'] = $array_request['sub_email'];
            if ($array_request['keep_both_email']) {
                $data_update_leads['sub_email'] && $data_update_leads['sub_email'] .= ',';
                $data_update_leads['sub_email'] .= $array_request['merged_email'];
            }
            $data_update_leads['address'] = $array_request['address'];
            $data_update_leads['company_name'] = $array_request['company_name'];
            $data_update_leads['status'] = $array_request['status'];
            $data_update_leads['address_company'] = $array_request['address_company'];
            $data_update_leads['tax'] = $array_request['tax'];
            $data_update_leads['affilacate_id'] = $array_request['affilacate_id'];
            $data_update_leads['caregiver_id'] = $array_request['caregiver_id'];
            $data_update_leads['active'] = $array_request['active'];
            $data_update_leads['about'] = $array_request['about'];
            $data_update_leads['label'] = $array_request['label'];
            $data_update_leads['opportunities_id'] = !empty($array_request_relate['id']) ? $array_request_relate['id'] : $array_request['opportunities_id'];

            $params = [];
            $params = [
                'leadsid' => $array_request['id'],
                'admin_id' => $admin_info['admin_id'],
                'data' => $data_update_leads
            ];
            $result_update = nv_local_api('UpdateLeads', $params, $admin_info['username'], $module_name);
            $exc = json_decode($result_update, true);
            // cập nhật thông tin bản ghi về 1 loại
            if ($exc) {
                // Update comments liên quan về cái được chọn
                if (!empty($array_request['comment'])) {
                    try {
                        $db->beginTransaction();
                        if ($array_request['keep_both_comment']) {
                            $all_comment_ids = $array_request['comment'] . ',' . $array_request['merged_comment'];
                            $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_comment SET sourceid = " . $array_request['id'] . "  WHERE source = 1 AND sourceid IN (" .  $all_comment_ids . ")";
                        } else if ($array_request['comment'] != $array_request['id']) {
                            $sql_delete = "DELETE FROM " . NV_PREFIXLANG . "_" . $module_data . "_comment WHERE sourceid=" . $array_request['id'] . ' AND source = 1';
                            $db->query($sql_delete);
                            $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_comment SET sourceid = " . $array_request['id'] . "  WHERE source = 1 AND sourceid=" .  $array_request['comment'];
                        }
                        $db->query($sql);
                        $db->commit();
                    } catch (PDOException $e) {
                        $db->rollBack();
                        trigger_error($e->getMessage());
                    }
                }

                // foreach các thông tin khác về 0
                foreach ($leadsmerger as $leads) {
                    if ($leads != $array_request['id']) {
                        $data_leadsmerger['active'] = 0;
                        $params_leadsmerger = [
                            'leadsid' => $leads,
                            'admin_id' => $admin_info['admin_id'],
                            'data' => $data_leadsmerger
                        ];
                        $result_update = nv_local_api('UpdateLeads', $params_leadsmerger, $admin_info['username'], $module_name);
                        // $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_leads SET updatetime= ' . NV_CURRENTTIME . ', active=0 WHERE id = ' . $leads);
                    }
                }

                // Update thông tin liên quan về các row được chọn
                if (!empty($array_request_relate['comment'])) {
                    try {
                        $db->beginTransaction();
                        if ($array_request['keep_both_relate_comment']) {
                            $all_relate_comment_ids = $array_request_relate['comment'] . ',' . $array_request['merged_relate_comment'];
                            $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_comment SET sourceid = " . $array_request_relate['id'] . "  WHERE source = 2 AND sourceid IN (" .  $all_relate_comment_ids . ")";
                        } else if ($array_request_relate['comment'] != $array_request_relate['id']) {
                            $sql_delete = "DELETE FROM " . NV_PREFIXLANG . "_" . $module_data . "_comment WHERE sourceid=" . $array_request_relate['id'] . ' AND source = 2';
                            $db->query($sql_delete);
                            $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_comment SET sourceid = " . $array_request_relate['id'] . "  WHERE source = 2 AND sourceid=" .  $array_request_relate['comment'];
                        }
                        $db->query($sql);
                        $db->commit();
                    } catch (PDOException $e) {
                        $db->rollBack();
                        trigger_error($e->getMessage());
                    }
                }

                // Nếu tồn tại thông tin liên quan được chọn để merge
                if ($array_request_relate['id'] > 0) {
                    $data_update_relate['leadsid'] = $array_request['id'];
                    $data_update_relate['log_merge'] = $list_log_relate;
                    $data_update_relate['label'] = $array_request_relate['label'];
                    $data_update_relate['user_id'] = $array_request_relate['user_id'];
                    $data_update_relate['orderid'] = $array_request_relate['orderid'];
                    $data_update_relate['name'] = $array_request_relate['name'];
                    $data_update_relate['phone'] = $array_request_relate['phone'];
                    $data_update_relate['sub_phone'] = $array_request_relate['sub_phone'];
                    if ($array_request['keep_both_relate_phone']) {
                        $data_update_relate['sub_phone'] && $data_update_relate['sub_phone'] .= ',';
                        $data_update_relate['sub_phone'] .= $array_request['merged_relate_phone'];
                    }
                    $data_update_relate['email'] = $array_request_relate['email'];
                    $data_update_relate['sub_email'] = $array_request_relate['sub_email'];
                    if ($array_request['keep_both_relate_email']) {
                        $data_update_relate['sub_email'] && $data_update_relate['sub_email'] .= ',';
                        $data_update_relate['sub_email'] .= $array_request['merged_relate_email'];
                    }
                    $data_update_relate['address'] = $array_request_relate['address'];
                    $data_update_relate['tax'] = $array_request_relate['tax'];
                    $data_update_relate['company_name'] = $array_request_relate['company_name'];
                    $data_update_relate['address_company'] = $array_request_relate['address_company'];
                    $data_update_relate['status'] = $array_request_relate['status'];
                    $data_update_relate['affilacate_id'] = $array_request_relate['affilacate_id'];
                    $data_update_relate['caregiver_id'] = $array_request_relate['caregiver_id'];
                    $data_update_relate['updatetime'] = NV_CURRENTTIME;
                    $data_update_relate['active'] = $array_request_relate['active'];
                    $data_update_relate['about'] = $array_request_relate['about'];
                    $params = [];
                    $params = [
                        'opportunitiesid' => $array_request_relate['id'],
                        'admin_id' => $admin_info['admin_id'],
                        'data' => $data_update_relate
                    ];
                    $result_update = nv_local_api('UpdateOpportunities', $params, $admin_info['username'], $module_name);
                    $exc = json_decode($result_update, true);

                    foreach ($relatemerger as $relate) {
                        if ($relate != $array_request_relate['id']) {
                            $data_relatemerger['active'] = 0;
                            $data_relatemerger['leadsid'] = $array_request['id'];
                            $params_relatemerger = [
                                'opportunitiesid' => $relate,
                                'admin_id' => $admin_info['admin_id'],
                                'data' => $data_relatemerger
                            ];
                            $result_update = nv_local_api('UpdateOpportunities', $params_relatemerger, $admin_info['username'], $module_name);
                            // $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_leads SET updatetime= ' . NV_CURRENTTIME . ', active=0 WHERE id = ' . $leads);
                        }
                    }
                }

                $log_data = [
                    $nv_Lang->getModule('log_merger_leads')
                ];

                if ($row_old['name'] != $array_request['name']) {
                    $log_data[] = [
                        'Họ tên: ',
                        $row_old['name'] . ' =&gt; ' . $array_request['name']
                    ];
                }
                if ($row_old['phone'] != $array_request['phone']) {
                    $log_data[] = [
                        'Điện thoại:',
                        $row_old['phone'] . ' =&gt; ' . $array_request['phone']
                    ];
                }
                if ($row_old['sub_phone'] != $array_request['sub_phone']) {
                    $log_data[] = [
                        'Điện thoại khác:',
                        $row_old['sub_phone'] . ' =&gt; ' . $array_request['sub_phone']
                    ];
                }
                if ($row_old['email'] != $array_request['email']) {
                    $log_data[] = [
                        'Email:',
                        $row_old['email'] . ' =&gt; ' . $array_request['email']
                    ];
                }
                if ($row_old['sub_email'] != $array_request['sub_email']) {
                    $log_data[] = [
                        'Email khác:',
                        $row_old['sub_email'] . ' =&gt; ' . $array_request['sub_email']
                    ];
                }
                if ($row_old['address'] != $array_request['address']) {
                    $log_data[] = [
                        'Địa chỉ:',
                        $row_old['address'] . ' =&gt; ' . $array_request['address']
                    ];
                }
                if ($row_old['company_name'] != $array_request['company_name']) {
                    $log_data[] = [
                        'Tên công ty:',
                        $row_old['company_name'] . ' =&gt; ' . $array_request['company_name']
                    ];
                }
                if ($row_old['tax'] != $array_request['tax']) {
                    $log_data[] = [
                        'Mã số thuế',
                        $row_old['tax'] . ' =&gt; ' . $array_request['tax']
                    ];
                }

                if ($row_old['address_company'] != $array_request['address_company']) {
                    $log_data[] = [
                        'Địa chỉ công ty:',
                        $row_old['address_company'] . ' =&gt; ' . $array_request['address_company']
                    ];
                }
                if ($row_old['about'] != $array_request['about']) {
                    $log_data[] = [
                        'Thông tin khác:',
                        $row_old['about'] . ' =&gt; ' . $array_request['about']
                    ];
                }
                if ($row_old['status'] != $array_request['status']) {
                    $log_data[] = [
                        'Trạng thái:',
                        $array_status[$row_old['status']] . ' =&gt; ' . $array_status[$array_request['status']]
                    ];
                }
                if ($row_old['affilacate_id'] != $array_request['affilacate_id']) {
                    $log_data[] = [
                        'Người giới thiệu:',
                        nv_show_name_user($array_user_id_users[$row_old['affilacate_id']]['first_name'], $array_user_id_users[$row_old['affilacate_id']]['last_name'], $array_user_id_users[$row_old['affilacate_id']]['username']) . ' =&gt; ' . nv_show_name_user($array_user_id_users[$array_request['affilacate_id']]['first_name'], $array_user_id_users[$array_request['affilacate_id']]['last_name'], $array_user_id_users[$array_request['affilacate_id']]['username'])
                    ];
                }
                if ($row_old['caregiver_id'] != $array_request['caregiver_id']) {
                    $log_data[] = [
                        'Người chăm sóc:',
                        nv_show_name_user($array_user_id_users[$row_old['caregiver_id']]['first_name'], $array_user_id_users[$row_old['caregiver_id']]['last_name'], $array_user_id_users[$row_old['caregiver_id']]['username']) . ' =&gt; ' . nv_show_name_user($array_user_id_users[$array_request['caregiver_id']]['first_name'], $array_user_id_users[$array_request['caregiver_id']]['last_name'], $array_user_id_users[$array_request['caregiver_id']]['username'])
                    ];
                }
                if (sizeof($log_data) <= 1) {
                    $log_data[] = 'Không có thay đổi thông tin';
                }
                $params_log = [];
                $params_log = [
                    'userid' => $admin_info['admin_id'],
                    'log_area' => 1,
                    'log_key' => 'Merger Leads Info',
                    'log_time' => NV_CURRENTTIME,
                    'log_data' => $log_data,
                    'leads_id' => $array_request['id']
                ];

                $result_data = nv_local_api('CreateAllLogs', $params_log, $admin_info['username'], $module_name);
                $success = $nv_Lang->getModule('merge_leads_ok');
                if (!empty($array_request_relate)) {
                    $log_data_relate = [
                        $nv_Lang->getModule('log_merger_leads')
                    ];
                    foreach ($array_request_relate as $key => $value) {
                        if (isset($row_old_relate[$key])) {
                            if ($row_old_relate[$key] != $value) {
                                if ($key == 'status') {
                                    $log_data_relate[] = [
                                        'Trạng thái:',
                                        $array_status[$row_old['status']] . ' =&gt; ' . $array_status[$array_request['status']]
                                    ];
                                } else if ($key == 'affilacate_id') {
                                    $log_data_relate[] = [
                                        'Người giới thiệu:',
                                        nv_show_name_user($array_user_id_users[$row_old['affilacate_id']]['first_name'], $array_user_id_users[$row_old['affilacate_id']]['last_name'], $array_user_id_users[$row_old['affilacate_id']]['username']) . ' =&gt; ' . nv_show_name_user($array_user_id_users[$array_request['affilacate_id']]['first_name'], $array_user_id_users[$array_request['affilacate_id']]['last_name'], $array_user_id_users[$array_request['affilacate_id']]['username'])
                                    ];
                                } else if ($key == 'caregiver_id') {
                                    $log_data_relate[] = [
                                        'Người chăm sóc:',
                                        nv_show_name_user($array_user_id_users[$row_old['caregiver_id']]['first_name'], $array_user_id_users[$row_old['caregiver_id']]['last_name'], $array_user_id_users[$row_old['caregiver_id']]['username']) . ' =&gt; ' . nv_show_name_user($array_user_id_users[$array_request['caregiver_id']]['first_name'], $array_user_id_users[$array_request['caregiver_id']]['last_name'], $array_user_id_users[$array_request['caregiver_id']]['username'])
                                    ];
                                } else {
                                    $log_data_relate[] = [
                                        $key . ':',
                                        $row_old_relate[$key] . ' =&gt; ' . $value
                                    ];
                                }
                            }
                        }
                    }
                    if (sizeof($log_data_relate) <= 1) {
                        $log_data_relate[] = 'Không có thay đổi thông tin';
                    }

                    $params_log_relate = [];
                    $params_log_relate = [
                        'userid' => $admin_info['admin_id'],
                        'log_area' => 1,
                        'log_key' => 'Merger Oppotunites Info',
                        'log_time' => NV_CURRENTTIME,
                        'log_data' => $log_data_relate,
                        'leads_id' => $array_request_relate['id']
                    ];
                    $result_data = nv_local_api('CreateAllLogs', $params_log, $admin_info['username'], $module_name);
                    $success = $nv_Lang->getModule('merge_leads_ok');
                }
            }
        } else if ($type == 2) {
            $params_opp = [
                'opportunitiesid' => $array_request['id']
            ];
            $data_opp = nv_local_api('GetDetailOpportunities', $params_opp, $admin_info['username'], $module_name);
            $row_old = json_decode($data_opp, true);
            $row_old = $row_old['data'];

            if (!empty($array_request_relate['id'])) {
                $params_relate = [];
                $params_relate = [
                    'leadsid' => $array_request_relate['id']
                ];
                $data_leads = nv_local_api('GetDetailLeads', $params_relate, $admin_info['username'], $module_name);
                $row_old_relate = json_decode($data_leads, true);
                $row_old_relate = $row_old_relate['data'] ?? [];
            }
            // $row_old = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities WHERE active=1 AND id=' . $array_request['id'])->fetch();
            // $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities SET leadsid=:leadsid, label=:label, user_id=:user_id, orderid=:orderid, name=:name,phone=:phone,sub_phone=:sub_phone,email=:email,sub_email=:sub_email,address=:address,tax=:tax,company_name=:company_name,address_company=:address_company,status=:status,affilacate_id=:affilacate_id,caregiver_id=:caregiver_id,updatetime=:updatetime, active=:active, about=:about WHERE id=' . $array_request['id']);

            $data_update_opp['leadsid'] = !empty($array_request_relate['id']) ? $array_request_relate['id'] : $array_request['leadsid'];
            $data_update_opp['log_merge'] = $list_log;
            $data_update_opp['label'] = $array_request['label'];
            $data_update_opp['user_id'] = $array_request['user_id'];
            
            $data_update_opp['orderid'] = implode(",", array_unique(explode(",", $array_request['all_orderid'])));
            $data_update_opp['orderid_dtnet'] = implode(",", array_unique(explode(",", $array_request['all_orderid_dtnet'])));
            $data_update_opp['customs_id'] = implode(",", array_unique(explode(",", $array_request['all_customs_id'])));
            
            $data_update_opp['name'] = $array_request['name'];
            $data_update_opp['phone'] = $array_request['phone'];
            $data_update_opp['sub_phone'] = $array_request['sub_phone'];
            if ($array_request['keep_both_phone']) {
                $data_update_opp['sub_phone'] && $data_update_opp['sub_phone'] .= ',';
                $data_update_opp['sub_phone'] .= $array_request['merged_phone'];
            }
            $data_update_opp['email'] = $array_request['email'];
            $data_update_opp['sub_email'] = $array_request['sub_email'];
            if ($array_request['keep_both_email']) {
                $data_update_opp['sub_email'] && $data_update_opp['sub_email'] .= ',';
                $data_update_opp['sub_email'] .= $array_request['merged_email'];
            }
            $data_update_opp['address'] = $array_request['address'];
            $data_update_opp['tax'] = $array_request['tax'];
            $data_update_opp['company_name'] = $array_request['company_name'];
            $data_update_opp['address_company'] = $array_request['address_company'];
            $data_update_opp['status'] = $array_request['status'];
            $data_update_opp['affilacate_id'] = $array_request['affilacate_id'];
            $data_update_opp['caregiver_id'] = $array_request['caregiver_id'];
            $data_update_opp['updatetime'] = NV_CURRENTTIME;
            $data_update_opp['active'] = $array_request['active'];
            $data_update_opp['about'] = $array_request['about'];
            $params = [];
            $params = [
                'opportunitiesid' => $array_request['id'],
                'admin_id' => $admin_info['admin_id'],
                'data' => $data_update_opp
            ];
            $result_update = nv_local_api('UpdateOpportunities', $params, $admin_info['username'], $module_name);
            $exc = json_decode($result_update, true);

            if ($exc['code'] == 0000) {
                if (!empty($array_request['comment'])) {
                    try {
                        $db->beginTransaction();
                        if ($array_request['keep_both_comment']) {
                            $all_comment_ids = $array_request['comment'] . ',' . $array_request['merged_comment'];
                            $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_comment SET sourceid = " . $array_request['id'] . "  WHERE source = 2 AND sourceid IN (" .  $all_comment_ids . ")";
                        } else if ($array_request['comment'] != $array_request['id']) {
                            $sql_delete = "DELETE FROM " . NV_PREFIXLANG . "_" . $module_data . "_comment WHERE sourceid=" . $array_request['id'] . ' AND source = 2';
                            $db->query($sql_delete);
                            $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_comment SET sourceid = " . $array_request['id'] . "  WHERE source = 2 AND sourceid=" .  $array_request['comment'];
                        }
                        $db->query($sql);
                        $db->commit();
                    } catch (PDOException $e) {
                        $db->rollBack();
                        trigger_error($e->getMessage());
                    }
                }
                foreach ($leadsmerger as $leads) {
                    if ($leads != $array_request['id']) {
                        $data_leadsmerger['active'] = 0;
                        $params_leadsmerger = [
                            'opportunitiesid' => $leads,
                            'admin_id' => $admin_info['admin_id'],
                            'data' => $data_leadsmerger
                        ];
                        $result_update = nv_local_api('UpdateOpportunities', $params_leadsmerger, $admin_info['username'], $module_name);
                        // $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_opportunities SET updatetime= ' . NV_CURRENTTIME . ', active=0 WHERE id = ' . $leads);
                    }
                }
                
                if (!empty($array_request_relate['comment'])) {
                    try {
                        $db->beginTransaction();
                        if ($array_request['keep_both_relate_comment']) {
                            $all_relate_comment_ids = $array_request_relate['comment'] . ',' . $array_request['merged_relate_comment'];
                            $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_comment SET sourceid = " . $array_request_relate['id'] . "  WHERE source = 1 AND sourceid IN (" .  $all_relate_comment_ids . ")";
                        } else if ($array_request_relate['comment'] != $array_request_relate['id']) {
                            $sql_delete = "DELETE FROM " . NV_PREFIXLANG . "_" . $module_data . "_comment WHERE sourceid=" . $array_request_relate['id'] . ' AND source = 1';
                            $db->query($sql_delete);
                            $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_comment SET sourceid = " . $array_request_relate['id'] . "  WHERE source = 1 AND sourceid=" .  $array_request_relate['comment'];
                        }
                        $db->query($sql);
                        $db->commit();
                    } catch (PDOException $e) {
                        $db->rollBack();
                        trigger_error($e->getMessage());
                    }
                }
                if (!empty($relatemerger)) {

                    $params_leads = [
                        'leadid' => $array_request_relate['id']
                    ];
                    $data_leads = nv_local_api('GetDetailLeads', $params_leads, $admin_info['username'], $module_name);
                    $row_old = json_decode($data_leads, true);
                    $row_old = $row_old['data'];

                    $data_update_leads['source_leads'] = $array_request_relate['source_leads'];
                    $data_update_leads['log_merge'] = $list_log_relate;
                    $data_update_leads['name'] = $array_request_relate['name'];
                    $data_update_leads['phone'] = $array_request_relate['phone'];
                    $data_update_leads['sub_phone'] = $array_request_relate['sub_phone'];
                    if ($array_request['keep_both_relate_phone']) {
                        $data_update_leads['sub_phone'] && $data_update_leads['sub_phone'] .= ',';
                        $data_update_leads['sub_phone'] .= $array_request['merged_relate_phone'];
                    }
                    $data_update_leads['email'] = $array_request_relate['email'];
                    $data_update_leads['sub_email'] = $array_request_relate['sub_email'];
                    if ($array_request['keep_both_relate_email']) {
                        $data_update_leads['sub_email'] && $data_update_leads['sub_email'] .= ',';
                        $data_update_leads['sub_email'] .= $array_request['merged_relate_email'];
                    }
                    $data_update_leads['address'] = $array_request_relate['address'];
                    $data_update_leads['company_name'] = $array_request_relate['company_name'];
                    $data_update_leads['status'] = $array_request_relate['status'];
                    $data_update_leads['address_company'] = $array_request_relate['address_company'];
                    $data_update_leads['tax'] = $array_request_relate['tax'];
                    $data_update_leads['affilacate_id'] = $array_request_relate['affilacate_id'];
                    $data_update_leads['caregiver_id'] = $array_request_relate['caregiver_id'];
                    $data_update_leads['active'] = $array_request_relate['active'];
                    $data_update_leads['about'] = $array_request_relate['about'];
                    $data_update_leads['label'] = $array_request_relate['label'];
                    $data_update_leads['opportunities_id'] = $array_request['id'];

                    $params = [];
                    $params = [
                        'leadsid' => $array_request_relate['id'], // Sửa code lỗi từ trước
                        'admin_id' => $admin_info['admin_id'],
                        'data' => $data_update_leads
                    ];

                    $result_update = nv_local_api('UpdateLeads', $params, $admin_info['username'], $module_name);
                    $exc = json_decode($result_update, true);

                    foreach ($relatemerger as $relate) {
                        if ($relate != $array_request_relate['id']) {
                            $data_relatemerger['active'] = 0;
                            $params_relatemerger = [
                                'leadsid' => $relate,
                                'admin_id' => $admin_info['admin_id'],
                                'data' => $data_relatemerger
                            ];
                            $result_update = nv_local_api('UpdateLeads', $params_relatemerger, $admin_info['username'], $module_name);
                            // $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_leads SET updatetime= ' . NV_CURRENTTIME . ', active=0 WHERE id = ' . $leads);
                        }
                    }
                }

                $log_data = [
                    $nv_Lang->getModule('log_merger_leads')
                ];

                if ($row_old['name'] != $array_request['name']) {
                    $log_data[] = [
                        'Họ tên: ',
                        $row_old['name'] . ' =&gt; ' . $array_request['name']
                    ];
                }
                if ($row_old['phone'] != $array_request['phone']) {
                    $log_data[] = [
                        'Điện thoại:',
                        $row_old['phone'] . ' =&gt; ' . $array_request['phone']
                    ];
                }
                if ($row_old['sub_phone'] != $array_request['sub_phone']) {
                    $log_data[] = [
                        'Điện thoại khác:',
                        $row_old['sub_phone'] . ' =&gt; ' . $array_request['sub_phone']
                    ];
                }
                if ($row_old['email'] != $array_request['email']) {
                    $log_data[] = [
                        'Email:',
                        $row_old['email'] . ' =&gt; ' . $array_request['email']
                    ];
                }
                if ($row_old['sub_email'] != $array_request['sub_email']) {
                    $log_data[] = [
                        'Email khác:',
                        $row_old['sub_email'] . ' =&gt; ' . $array_request['sub_email']
                    ];
                }
                if ($row_old['address'] != $array_request['address']) {
                    $log_data[] = [
                        'Địa chỉ:',
                        $row_old['address'] . ' =&gt; ' . $array_request['address']
                    ];
                }
                if ($row_old['company_name'] != $array_request['company_name']) {
                    $log_data[] = [
                        'Tên công ty:',
                        $row_old['company_name'] . ' =&gt; ' . $array_request['company_name']
                    ];
                }
                if ($row_old['tax'] != $array_request['tax']) {
                    $log_data[] = [
                        'Mã số thuế',
                        $row_old['tax'] . ' =&gt; ' . $array_request['tax']
                    ];
                }

                if ($row_old['address_company'] != $array_request['address_company']) {
                    $log_data[] = [
                        'Địa chỉ công ty:',
                        $row_old['address_company'] . ' =&gt; ' . $array_request['address_company']
                    ];
                }
                if ($row_old['about'] != $array_request['about']) {
                    $log_data[] = [
                        'Thông tin khác:',
                        $row_old['about'] . ' =&gt; ' . $array_request['about']
                    ];
                }
                if ($row_old['status'] != $array_request['status']) {
                    $log_data[] = [
                        'Trạng thái:',
                        $array_status[$row_old['status']] . ' =&gt; ' . $array_status[$array_request['status']]
                    ];
                }
                if ($row_old['affilacate_id'] != $array_request['affilacate_id']) {
                    $log_data[] = [
                        'Người giới thiệu:',
                        nv_show_name_user($array_user_id_users[$row_old['affilacate_id']]['first_name'], $array_user_id_users[$row_old['affilacate_id']]['last_name'], $array_user_id_users[$row_old['affilacate_id']]['username']) . ' =&gt; ' . nv_show_name_user($array_user_id_users[$array_request['affilacate_id']]['first_name'], $array_user_id_users[$array_request['affilacate_id']]['last_name'], $array_user_id_users[$array_request['affilacate_id']]['username'])
                    ];
                }
                if ($row_old['caregiver_id'] != $array_request['caregiver_id']) {
                    $log_data[] = [
                        'Người chăm sóc:',
                        nv_show_name_user($array_user_id_users[$row_old['caregiver_id']]['first_name'], $array_user_id_users[$row_old['caregiver_id']]['last_name'], $array_user_id_users[$row_old['caregiver_id']]['username']) . ' =&gt; ' . nv_show_name_user($array_user_id_users[$array_request['caregiver_id']]['first_name'], $array_user_id_users[$array_request['caregiver_id']]['last_name'], $array_user_id_users[$array_request['caregiver_id']]['username'])
                    ];
                }
                if (sizeof($log_data) <= 1) {
                    $log_data[] = 'Không có thay đổi thông tin';
                }

                $params_log = [];
                $params_log = [
                    'userid' => $admin_info['admin_id'],
                    'log_area' => 1,
                    'log_key' => 'Merger Leads Info',
                    'log_time' => NV_CURRENTTIME,
                    'log_data' => $log_data,
                    'leads_id' => $array_request['id']
                ];
                $result_data = nv_local_api('CreateAllLogs', $params_log, $admin_info['username'], $module_name);
                $success = $nv_Lang->getModule('merge_leads_ok');

                $log_data_relate = [
                    $nv_Lang->getModule('log_merger_leads')
                ];
                foreach ($array_request_relate as $key => $value) {
                    if (!empty($row_old_relate[$key])) {
                        if ($row_old_relate[$key] != $value) {
                            if ($key == 'status') {
                                $log_data_relate[] = [
                                    'Trạng thái:',
                                    $array_status[$row_old['status']] . ' =&gt; ' . $array_status[$array_request['status']]
                                ];
                            } else if ($key == 'affilacate_id') {
                                $log_data_relate[] = [
                                    'Người giới thiệu:',
                                    nv_show_name_user($array_user_id_users[$row_old['affilacate_id']]['first_name'], $array_user_id_users[$row_old['affilacate_id']]['last_name'], $array_user_id_users[$row_old['affilacate_id']]['username']) . ' =&gt; ' . nv_show_name_user($array_user_id_users[$array_request['affilacate_id']]['first_name'], $array_user_id_users[$array_request['affilacate_id']]['last_name'], $array_user_id_users[$array_request['affilacate_id']]['username'])
                                ];
                            } else if ($key == 'caregiver_id') {
                                $log_data_relate[] = [
                                    'Người chăm sóc:',
                                    nv_show_name_user($array_user_id_users[$row_old['caregiver_id']]['first_name'], $array_user_id_users[$row_old['caregiver_id']]['last_name'], $array_user_id_users[$row_old['caregiver_id']]['username']) . ' =&gt; ' . nv_show_name_user($array_user_id_users[$array_request['caregiver_id']]['first_name'], $array_user_id_users[$array_request['caregiver_id']]['last_name'], $array_user_id_users[$array_request['caregiver_id']]['username'])
                                ];
                            } else {
                                $log_data_relate[] = [
                                    $key . ':',
                                    $row_old_relate[$key] . ' =&gt; ' . $value
                                ];
                            }
                        }
                    }
                }
                if (sizeof($log_data_relate) <= 1) {
                    $log_data_relate[] = 'Không có thay đổi thông tin';
                }

                $params_log_relate = [];
                $params_log_relate = [
                    'userid' => $admin_info['admin_id'],
                    'log_area' => 1,
                    'log_key' => 'Merger Oppotunites Info',
                    'log_time' => NV_CURRENTTIME,
                    'log_data' => $log_data_relate,
                    'oppotunities_id' => $array_request_relate['id']
                ];
                $result_data = nv_local_api('CreateAllLogs', $params_log, $admin_info['username'], $module_name);
                $success = $nv_Lang->getModule('merge_leads_ok');
            }
        }
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('Q', $q_search);
$xtpl->assign('FORM_ACTION', $base_url);
$xtpl->assign('LINK_UNDO_MERGE', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=undo_merge&type=' . $type);

if (!empty($array_search)) {
    foreach ($array_search as $value) {
        $base_url .= "&field[]=" . $value;
    }
}
if (!empty($_arr_rowcheck)) {

    // parser main
    $size = sizeof($_arr_rowcheck);
    for ($i = 1; $i <= $size; $i++) {
        $xtpl->assign('I', $i);
        $xtpl->parse('merger.main_info.loopthead');
    }
    foreach ($_arr_rowcheck as $leads) {
        $xtpl->assign('LEADS', $leads);
        $xtpl->parse('merger.lead');
    }

    foreach ($array_field_merger as $field => $field_merger) {
        $xtpl->assign('FIELD', $field);
        $xtpl->assign('FIELD_MERGER', $field_merger);
        $j = 1;

        foreach ($_arr_rowcheck as $key => $rowcheck) {
            // if (isset($rowcheck[$field])) {
                $xtpl->assign('J', $j);
                $xtpl->assign('checked', $j == 1 ? 'checked="checked"' : '');
                if ($field == "comment") {
                    $xtpl->assign('DATA_FIELD', $rowcheck['id']);
                } else {
                    $xtpl->assign('DATA_FIELD', $rowcheck[$field]);
                }
                if ($field == 'status') {
                    if ($type == 1) {
                        $xtpl->assign('TITLE_MAIN', $nv_Lang->getModule('leads_title'));
                        $xtpl->assign('TITLE_RELATE', $nv_Lang->getModule('opportunities'));
                        $rowcheck[$field] = $array_status[$rowcheck[$field]];
                    } else {
                        $xtpl->assign('TITLE_MAIN', $nv_Lang->getModule('opportunities'));
                        $xtpl->assign('TITLE_RELATE', $nv_Lang->getModule('leads_title'));
                        $rowcheck[$field] = $array_status_opportunities[$rowcheck[$field]];
                    }
                }
                if ($field == 'affilacate_id') {
                    $rowcheck[$field] = isset($array_user_id_users[$rowcheck[$field]]) ? nv_show_name_user($array_user_id_users[$rowcheck[$field]]['first_name'], $array_user_id_users[$rowcheck[$field]]['last_name'], $array_user_id_users[$rowcheck[$field]]['username']) : '';
                }
                if ($field == 'caregiver_id') {
                    $rowcheck[$field] = isset($array_user_id_users[$rowcheck[$field]]) ? nv_show_name_user($array_user_id_users[$rowcheck[$field]]['first_name'], $array_user_id_users[$rowcheck[$field]]['last_name'], $array_user_id_users[$rowcheck[$field]]['username']) : '';
                }
                if ($field == 'source_leads') {
                    $rowcheck[$field] = isset($array_groups_leads[$rowcheck[$field]]['title']) ? $array_groups_leads[$rowcheck[$field]]['title'] : '';
                }
                if ($field == 'updatetime') {
                    $rowcheck[$field] = $rowcheck[$field] != 0 ? nv_date('H:i d/m/Y', $rowcheck[$field]) : '';
                }
                if ($field == 'timecreate') {
                    $rowcheck[$field] = $rowcheck[$field] != 0 ? nv_date('H:i d/m/Y', $rowcheck[$field]) : '';
                }
                if ($field == 'comment') {
                    if (!empty($rowcheck[$field])) {
                        foreach ($rowcheck[$field] as $key => $value) {
                            $xtpl->assign('DATA_COMMENT', nv_htmlspecialchars($value));
                            $xtpl->parse('merger.main_info.loopfield.looprow.loop_comment');
                        }
                    }
                } else {
                    $xtpl->assign('DATA_FIELD_TITLE', $rowcheck[$field]);
                    $xtpl->parse('merger.main_info.loopfield.looprow.field_total');
                }
                $xtpl->parse('merger.main_info.loopfield.looprow');
                ++$j;
            // }
        }
        
        if ($field == 'phone') {
            $xtpl->parse('merger.main_info.loopfield.keep_both_phone');
        }
        if ($field == 'email') {
            $xtpl->parse('merger.main_info.loopfield.keep_both_email');
        }
        if ($field == 'comment') {
            $xtpl->parse('merger.main_info.loopfield.keep_both_comment');
        }
        $xtpl->parse('merger.main_info.loopfield');
    }
    $xtpl->parse('merger.main_info');

    // parser relate
    if (!empty($_arr_rowcheck_relate )) {
        $size_relate = sizeof($_arr_rowcheck_relate);
        for ($i = 1; $i <= $size_relate; $i++) {
            $xtpl->assign('II', $i);
            $xtpl->parse('merger.relate_info.loopthead_relate');
        }
        foreach ($_arr_rowcheck_relate as $relate) {
            $xtpl->assign('INFO_RELATE', $relate);
            $xtpl->parse('merger.relate');
        }

        foreach ($array_field_relate as $field => $field_merger) {
            $xtpl->assign('FIELD_RELATE', $field.'_relate');
            $xtpl->assign('FIELD_MERGER_RELATE', $field_merger);
            $jj = 1;
            foreach ($_arr_rowcheck_relate as $rowcheck_relate) {
                // if (isset($rowcheck[$field])) {
                    $xtpl->assign('JJ', $jj);
                    $xtpl->assign('checked_relate', $jj == 1 ? 'checked="checked"' : '');
                    if ($field == "comment") {
                        $xtpl->assign('DATA_FIELD_RELATE', $rowcheck_relate['id']); // Sửa code lỗi từ trước
                    } else {
                        $xtpl->assign('DATA_FIELD_RELATE', $rowcheck_relate[$field]);
                    }
                    if ($field == 'status') {
                        if ($type == 1) {
                            $xtpl->assign('TITLE_MAIN', $nv_Lang->getModule('leads_title'));
                            $xtpl->assign('TITLE_RELATE', $nv_Lang->getModule('opportunities') . ' ' . $nv_Lang->getModule('relate'));
                            $rowcheck_relate[$field] = $array_status[$rowcheck_relate[$field]];
                        } else {
                            $xtpl->assign('TITLE_MAIN', $nv_Lang->getModule('opportunities'));
                            $xtpl->assign('TITLE_RELATE', $nv_Lang->getModule('leads_title') . ' ' .  $nv_Lang->getModule('relate'));
                            $rowcheck_relate[$field] = $array_status_opportunities[$rowcheck_relate[$field]];
                        }
                    }
                    if ($field == 'affilacate_id') {
                        $rowcheck_relate[$field] = isset($array_user_id_users[$rowcheck_relate[$field]]) ? nv_show_name_user($array_user_id_users[$rowcheck_relate[$field]]['first_name'], $array_user_id_users[$rowcheck_relate[$field]]['last_name'], $array_user_id_users[$rowcheck_relate[$field]]['username']) : '';
                    }
                    if ($field == 'caregiver_id') {
                        $rowcheck_relate[$field] = isset($array_user_id_users[$rowcheck_relate[$field]]) ? nv_show_name_user($array_user_id_users[$rowcheck_relate[$field]]['first_name'], $array_user_id_users[$rowcheck_relate[$field]]['last_name'], $array_user_id_users[$rowcheck_relate[$field]]['username']) : '';
                    }
                    if ($field == 'source_leads') {
                        $rowcheck_relate[$field] = isset($array_groups_leads[$rowcheck_relate[$field]]['title']) ? $array_groups_leads[$rowcheck_relate[$field]]['title'] : '';
                    }
                    if ($field == 'updatetime') {
                        $rowcheck_relate[$field] = $rowcheck_relate[$field] != 0 ? nv_date('H:i d/m/Y', $rowcheck_relate[$field]) : '';
                    }
                    if ($field == 'timecreate') {
                        $rowcheck_relate[$field] = $rowcheck_relate[$field] != 0 ? nv_date('H:i d/m/Y', $rowcheck_relate[$field]) : '';
                    }
                    if ($field == 'comment') {
                        if (!empty($rowcheck[$field])) {
                            foreach ($rowcheck[$field] as $key => $value) {
                                $xtpl->assign('DATA_COMMENT', nv_htmlspecialchars($value));
                                $xtpl->parse('merger.relate_info.loopfield_relate.looprow.loop_comment');
                            }
                        }
                    } else {
                        $xtpl->assign('DATA_FIELD_TITLE_RELATE', $rowcheck_relate[$field]);
                        $xtpl->parse('merger.relate_info.loopfield_relate.looprow.field_total');
                    }
                    $xtpl->parse('merger.relate_info.loopfield_relate.looprow');
                    ++$jj;
                // }
            }
            if ($field == 'phone') {
                $xtpl->parse('merger.relate_info.loopfield_relate.keep_both_relate_phone');
            }
            if ($field == 'email') {
                $xtpl->parse('merger.relate_info.loopfield_relate.keep_both_relate_email');
            }
            if ($field == 'comment') {
                $xtpl->parse('merger.relate_info.loopfield_relate.keep_both_relate_comment');
            }
            $xtpl->parse('merger.relate_info.loopfield_relate');
        }
        $xtpl->parse('merger.relate_info');
    }
    $xtpl->parse('merger');
    $contents = $xtpl->text('merger');
} else {
    $array_user_id_users[0]['first_name'] = $nv_Lang->getModule('leads_status0');
    $array_user_id_users[0]['last_name'] = '';
    foreach ($array_field as $key => $field) {
        $data['checked'] = in_array($key, $array_search) ? 'checked="checked"' : '';
        $data['key'] = $key;
        $data['title'] = $field;
        $xtpl->assign('FIELD', $data);
        $xtpl->parse('main.field');
    }

    $number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;
    if (!empty($array_data)) {
        $num_items = 0;
        foreach ($array_data as $_data) {
            foreach ($_data as $_key => $_val) {
                if (sizeof($_val) > 1) {
                    $num_items += sizeof($_val);
                    $_data['stt'] = $number++;
                    $xtpl->assign('DATA', $_data);
                    $i = 1;
                    $xtpl->assign('rowspan', sizeof($_val));
                    foreach ($_val as $_k => $_v) {
                        $_v['caregiver_name'] = $array_user_id_users[$_v['caregiver_id']]['first_name'] . ' ' . $array_user_id_users[$_v['caregiver_id']]['last_name'];
                        if ($type == 1) {
                            $_v['source_leads'] = isset($array_groups_leads[$_v['source_leads']]['title']) ? $array_groups_leads[$_v['source_leads']]['title'] : '';
                        }
                        $xtpl->assign('DATA_ROW', $_v);
                        if ($i == 1) {
                            if ($type == 1) {
                                $xtpl->parse('main.loop.row.type1');
                            }
                            $xtpl->parse('main.loop.row');
                        } else {
                            if ($type == 1) {
                                $xtpl->parse('main.loop.row2.type1');
                            }
                            $xtpl->parse('main.loop.row2');
                        }
                        ++$i;
                    }
                    $xtpl->parse('main.loop');
                }
            }
        }
    }
    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
    }

    if ($type == 1) {
        $xtpl->parse('main.type1');
    }

    if (!empty($error)) {
        $xtpl->assign('ERROR', implode('<br />', $error));
        $xtpl->parse('main.error');
    }
    if (!empty($success)) {
        $xtpl->assign('SUCCESS', $success);
        $xtpl->parse('main.success');
    }
    $xtpl->parse('main');
    $contents = $xtpl->text('main');
}

$page_title = $nv_Lang->getModule('merger');
include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
