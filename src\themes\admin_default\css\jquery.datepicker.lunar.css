/*
 * jQuery Lunar date picker plugin for jQuery datepicker v0.1 - https://github.com/Minh0001/jquery.datepicker.lunar
 *
 * TERMS OF USE - jQuery Lunar date picker
 * 
 * Open source under the BSD License. 
 * 
 * Copyright © 2013 <PERSON>
 * All rights reserved.
 * 
 * Redistribution and use in source and binary forms, with or without modification, 
 * are permitted provided that the following conditions are met:
 * 
 * Redistributions of source code must retain the above copyright notice, this list of 
 * conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright notice, this list 
 * of conditions and the following disclaimer in the documentation and/or other materials 
 * provided with the distribution.
 * 
 * Neither the name of the author nor the names of contributors may be used to endorse 
 * or promote products derived from this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY 
 * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 *  COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 *  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 *  GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED 
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 *  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED 
 * OF THE POSSIBILITY OF SUCH DAMAGE. 
 *
*/

.ui-datepicker td span,
.ui-datepicker td a {
    display: block;
    padding: .2em;
    text-align: left;
    text-decoration: none;
}

.ui-datepicker-calendar .ui-state-default,
.ui-datepicker-calendar .ui-state-default,
.ui-datepicker-calendar .ui-state-default {
    border: none !important;
    /* background-color: #FFF; */
    font-weight: bold;
    color: #000;
    margin-bottom: 0;
    text-align: left;
}

.ui-datepicker td {
    border: 1px solid #aaa !important;
    padding: 1px;
    background-color: #f6f6f6;
}

.ui-datepicker td:hover:not(.ui-datepicker-today) {
    background-color: #ededed !important;
}

.ui-datepicker td.ui-datepicker-current-day {
    background: #007fff;
}

.ui-datepicker td.ui-datepicker-current-day:hover {
    background-color: #0076ec !important;
}

.ui-state-default,
.ui-state-active {
    background: none !important;
}

.ui-datepicker td.ui-datepicker-today {
    background: #fffa90;
}

.ui-datepicker td.ui-datepicker-today:hover {
    background: #fff400;
}

.ui-datepicker .lunar-date {
    text-align: right;
    font-size: 70%;
    line-height: 80%;
    color: #555;
    cursor: pointer;
}

.ui-datepicker .lunar-date-leap {
    text-align: right;
    font-size: 70%;
    line-height: 80%;
    color: #B91E1E;
}

.ui-datepicker .lunar-date-tet {
    text-align: right;
    font-size: 70%;
    line-height: 80%;
    color: #fff;
    background: #B91E1E;
}

table.ui-datepicker-calendar>thead>tr>th:nth-child(6)>span {
    color: rgb(0, 0, 255);
}

table.ui-datepicker-calendar>thead>tr>th:nth-child(7)>span {
    color: rgb(255, 102, 0);
}

table.ui-datepicker-calendar tr td:nth-child(6)>a {
    color: rgb(0, 0, 255) !important;
}

table.ui-datepicker-calendar tr td:nth-child(7)>a {
    color: rgb(255, 102, 0) !important;
}

.filter .ui-widget-header {
    background: #e84e12;
}