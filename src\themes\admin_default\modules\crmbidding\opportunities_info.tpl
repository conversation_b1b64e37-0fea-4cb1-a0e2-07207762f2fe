<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css">
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<!-- BEGIN: error -->
<div class="alert alert-danger">{ERROR}</div>
<!-- END: error -->
<!-- BEGIN: prompt_to_combine -->
    <div class="alert alert-warning">{LANG.prompt_to_combine}</div>
<!-- END: prompt_to_combine -->
<div class="row">
    <div class="col-md-14">
        <div class="panel panel-default">
            <div class="panel-body">
                <form class="form-horizontal" action="{FORM_ACTION}" method="post">
                    <div class="row">
                        <h2 class="text-info col-md-8">
                            {LANG.opportunities_info} <br />
                            <!-- BEGIN: label -->
                            <span style="background-color: {LABEL.color}"><i class="fa fa-flag"></i> {LABEL.title} </span> <br />
                            <!-- END: label -->
                            <!-- BEGIN: schedule -->
                            <span style="color: red;"><i class="fa fa-bell"></i> {LANG.schedule}: {SCHEDULE} </span> <br />
                            <!-- END: schedule -->
                        </h2>
                        <div class="col-md-16 text-right">
                            <!-- BEGIN: show_link_detai_customer -->
                            <a href="{LINK_VIEW_MANAGER_CUSTOMER}" class="btn btn-primary" data-toggle="tooltip" title="{LANG.manage_customer}"> <i class="fa fa-google-wallet" aria-hidden="true"></i>
                            </a>
                            <!-- END: show_link_detai_customer -->

                            <!-- BEGIN: convert_contact -->
                            <a href="{LINK_CONVERT_CONTACT}" class="btn btn-primary" data-toggle="tooltip" title="{LANG.convert_contact}"> <i class="fa fa-address-book"></i>
                            </a>
                            <!-- END: convert_contact -->
                            <!-- BEGIN: view_contact -->
                            <a href="{LINK_VIEW_CONTACT}" class="btn btn-primary" data-toggle="tooltip" title="{LANG.view_contact}"> <i class="fa fa-address-book"></i>
                            </a>
                            <!-- END: view_contact -->

                            <!-- BEGIN: convert_org -->
                            <a href="{LINK_CONVERT_ORG}" class="btn btn-primary" data-toggle="tooltip" title="{LANG.convert_org}"> <i class="fa fa-users"></i>
                            </a>
                            <!-- END: convert_org -->
                            <!-- BEGIN: view_org -->
                            <a href="{LINK_VIEW_ORG}" class="btn btn-primary" data-toggle="tooltip" title="{LANG.view_org}"> <i class="fa fa-users"></i>
                            </a>
                            <!-- END: view_org -->
                            <!-- BEGIN: create_lock_seo -->
                            <button type="button" class="btn btn-primary btn_add_lockseo" data-toggle="tooltip" title="{LANG.add_lock_seo}"{DISABLE}>
                                <i class="fa fa-cart-plus"> </i><small> {LANG.lock_seo}</small>
                            </button>
                            <!-- END: create_lock_seo -->
                            <!-- BEGIN: accept -->
                            <button type="button" class="btn btn-accept btn-primary" data-toggle="tooltip" title="Nhận đơn hàng">
                                <i class="fa fa-check-circle"></i>
                            </button>
                            <!-- END: accept -->
                            <button type="button" class="btn btn-label btn-primary" data-toggle="tooltip" title="{LANG.menu_label}">
                                <i class="fa fa-star"></i>
                            </button>
                            <!-- BEGIN: leadsid -->
                            <a href="{LINK_OPPOTUNITIES}" class="btn btn-primary" data-toggle="tooltip" title="{LANG.leads_sources}"> <i class="fa fa-cube"></i>
                            </a>
                            <!-- END: leadsid -->
                            <button type="button" class="btn btn-primary btnusers" data-id="{ROW.id}" data-title="{LANG.opportunities_info_user}" data-toggle="tooltip" title="{LANG.opportunities_info_user}">
                                <i class="fa fa-user-plus"></i>
                            </button>
                            <button type="button" class="btn btn-primary btnorder" data-toggle="tooltip" title="{LANG.opportunities_info_order}"{DISABLE_ORDER}>
                                <i class="fa fa-shopping-cart"></i>
                            </button>
                            <button type="button" class="btn btn-primary btn_order_add" data-toggle="tooltip" title="{LANG.opportunities_info_order_add}"{DISABLE}>
                                <i class="fa fa-cart-plus"></i>
                            </button>
                            <button type="button" class="btn editbtn btn-primary {hidden_edit}" data-toggle="tooltip" title="{LANG.update}">
                                <i class="fa fa-pencil" aria-hidden="true"></i>
                            </button>
                            <button type="submit" name="submit" class="btn savebtn btn-primary hidden" value="1" data-toggle="tooltip" title="{LANG.save}">
                                <i class="fa fa-floppy-o" aria-hidden="true"></i>
                            </button>
                            <button type="button" class="btn deletebtn btn-danger hidden" data-toggle="tooltip" title="{LANG.update_cancel}">
                                <i class="fa fa-times" aria-hidden="true"></i>
                            </button>
                            <!-- BEGIN: sync_user_btn -->
                            <button type="button" class="btn btn-info" data-action="syncUser" data-userid="{ROW.user_id}" data-tokend="{TOKEND}" data-toggle="tooltip" title="{LANG.sync_user}">
                                <i class="fa fa-retweet" aria-hidden="true"></i>
                            </button>
                            <!-- END: sync_user_btn -->
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-md-12 col-sm-24">
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.name}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary textinfo">{ROW.name}</strong> <input class="form-control inputinfo hidden required" type="text" name="name" value="{ROW.name}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">
                                    {LANG.phone} <i class="fa fa-info-circle red" data-original-title="Giá trị Số điện thoại hoặc email có thể nhập 1 trong 2. {LANG.info_phone}" data-toggle="tooltip"></i>
                                </div>
                                <div class="col-sm-18 col-md-16">
                                    <strong id="phone" class="text-primary textinfo">{ROW.phone}</strong> <input class="form-control inputinfo hidden required" type="text" name="phone" value="{ROW.phone}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.sub_phone}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong id="sub_phone" class="text-primary textinfo">{ROW.sub_phone}</strong> <input class="form-control inputinfo hidden" type="text" name="sub_phone" value="{ROW.sub_phone}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.address}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary textinfo">{ROW.address}</strong> <input class="form-control inputinfo hidden" type="text" name="address" value="{ROW.address}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.company_name}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary textinfo">{ROW.company_name}</strong> <input class="form-control inputinfo hidden" type="text" name="company_name" value="{ROW.company_name}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.address_company}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary textinfo">{ROW.address_company}</strong> <input class="form-control inputinfo hidden" type="text" name="address_company" value="{ROW.address_company}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.tax}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong id="tax" class="text-primary textinfo">{ROW.tax}</strong> <input class="form-control inputinfo hidden" type="text" name="tax" value="{ROW.tax}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.siteid}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong id="siteid" class="text-primary textinfo">{ROW.siteid} {ROW.prefix_lang_letter}</strong> <select class="form-control inputinfo hidden" name="siteid">
                                        <!-- BEGIN: siteid -->
                                        <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                                        <!-- END: siteid -->
                                    </select> <br /> <select class="form-control inputinfo hidden" name="prefix_lang">
                                        <!-- BEGIN: select_prefix_lang -->
                                        <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                                        <!-- END: select_prefix_lang -->
                                    </select>
                                </div>
                            </div>
                            <!-- BEGIN: ext_uinfo -->
                            <!-- BEGIN: loop -->
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{F_NAME}</div>
                                <div class="col-sm-18 col-md-16">
                                    <div class="textinfo">
                                        <!-- BEGIN: items -->
                                        <ul class="pl-4 mb-0">
                                            <!-- BEGIN: item -->
                                            <li><strong class="text-primary">{F_VALUE}</strong></li>
                                            <!-- END: item -->
                                        </ul>
                                        <!-- END: items -->
                                        <!-- BEGIN: na -->
                                        <strong class="text-primary">N/A</strong>
                                        <!-- END: na -->
                                    </div>
                                    <div class="inputinfo hidden">
                                        <!-- BEGIN: edititem -->
                                        <div class="checkbox">
                                            <label> <input type="checkbox" name="{F_KEY}[]" {F_INPUT_CHECKED} value="{F_INPUT_KEY}"> {F_VALUE}
                                            </label>
                                        </div>
                                        <!-- END: edititem -->
                                    </div>
                                </div>
                            </div>
                            <!-- END: loop -->
                            <!-- END: ext_uinfo -->
                        </div>
                        <div class="col-md-12 col-sm-24">
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.email}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong id="email" class="text-primary textinfo">{ROW.email}</strong> <input class="form-control inputinfo hidden required" type="text" name="email" value="{ROW.email}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.sub_email}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong id="sub_email" class="text-primary textinfo">{ROW.sub_email}</strong> <input class="form-control inputinfo hidden" type="text" name="sub_email" value="{ROW.sub_email}" />
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.timecreate}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary">{ROW.timecreate}</strong>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.updatetime}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary">{ROW.updatetime}</strong>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.activity_time}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary">{ROW.activity_time}</strong>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.status}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary textinfo">{ROW.status}</strong> <select class="form-control inputinfo hidden" name="status">
                                        <!-- BEGIN: status -->
                                        <option value="{STATUS.id}"{STATUS.selected}>{STATUS.title}</option>
                                        <!-- END: status -->
                                    </select>

                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.affilacate_id}</div>
                                <div class="col-sm-18 col-md-16">
                                    <!-- BEGIN: no_affilacate -->
                                    <strong class="text-primary">{ROW.affilacate_id}</strong>
                                    <!-- END: no_affilacate -->
                                    <!-- BEGIN: affilacate -->
                                    <strong class="text-primary textinfo">{ROW.affilacate_id}</strong> <select class="form-control inputinfo hidden" name="affilacate_id">
                                        <option value="0">---</option>
                                        <!-- BEGIN: affilacate_id -->
                                        <option value="{CAREGIVER_ID.userid}"{CAREGIVER_ID.selected_affilacate}>{CAREGIVER_ID.username} ({CAREGIVER_ID.fullname})</option>
                                        <!-- END: affilacate_id -->
                                    </select>
                                    <!-- END: affilacate -->
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.caregiver_id}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary textinfo">{ROW.caregiver_id}</strong> <select class="form-control inputinfo hidden" name="caregiver_id">
                                        <option value="0">---</option>
                                        <!-- BEGIN: caregiver_id -->
                                        <option value="{CAREGIVER_ID.userid}"{CAREGIVER_ID.selected}>{CAREGIVER_ID.username} ({CAREGIVER_ID.fullname})</option>
                                        <!-- END: caregiver_id -->
                                    </select>
                                </div>
                            </div>
                            <!-- BEGIN: starttime -->
                            <div class="form-group row">
                                <div class="col-sm-6 col-md-8 text-right">{LANG.starttime}</div>
                                <div class="col-sm-18 col-md-16">
                                    <strong class="text-primary">{STARTTIME}</strong>
                                </div>
                            </div>
                            <!-- END: starttime -->
                        </div>
                        <div class="col-md-24">
                            <div class="form-group">
                                <div class="col-sm-6 col-md-4 text-right">{LANG.about}</div>
                                <div class="col-sm-19 col-md-20">
                                    <strong class="text-primary textinfo">{ROW.about}</strong>
                                    <textarea class="form-control inputinfo hidden" name="about" rows="6">{ROW.about}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-24 text-right">
                            <button type="submit" name="submit" class="btn savebtn btn-primary hidden" value="1" title="{LANG.save}">
                                <i class="fa fa-floppy-o" aria-hidden="true"></i>
                            </button>
                            <button type="button" class="btn deletebtn btn-danger hidden" title="{LANG.update_cancel}">
                                <i class="fa fa-times" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="panel panel-default col-md-24">
            <div class="panel-body">
                <h2 class="text-info">{LANG.caregiver_history}</h2>
                <hr>
                <!-- BEGIN: another_hot_lead -->
                <div class="alert alert-danger">{ANOTHER_HOT_LEAD}</div>
                <!-- END: another_hot_lead -->
                <div class="form-group col-md-24">
                    <form class="form-horizontal" action="{FORM_ACTION}" method="post" class="form-inline">
                        <label class="col-md-24"><b>{LANG.note}</b></label>
                        <div class="form-group col-md-24">
                            <textarea class="form-control" name="note" rows="4">{EDIT_LOG.note}</textarea>
                        </div>
                        <div class="form-group col-md-24">
                            <label><b>{LANG.schedule}</b></label> <input class="form-control w100 uidatepicker" style="display: inline-block;" type="text" value="{EDIT_LOG.schedule}" name="schedule" maxlength="10" autocomplete="off">
                        </div>
                        <div class="form-group col-md-24" style="text-align: right;">
                            <!-- BEGIN: add_comment -->
                            <input class="btn btn-primary" name="save" type="submit" value="{LANG.comment}" />
                            <!-- END: add_comment -->
                            <!-- BEGIN: edit_comment -->
                            <input type="hidden" name="comentid" value="{EDIT_LOG.id}" /> <input class="btn btn-primary" name="save" type="submit" value="{LANG.update}" />
                            <!-- END: edit_comment -->
                        </div>
                    </form>
                </div>
                <!-- BEGIN: comment -->
                <div class="table-responsive col-md-24">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th class="text-nowrap w150">{LANG.timecreate}/{LANG.updatetime}</th>
                                <th class="text-nowrap w150">{LANG.log_user}</th>
                                <th class="text-nowrap">{LANG.log_data}</th>
                                <th class="text-nowrap"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- BEGIN: loop -->
                            <tr>
                                <td>{LOG.timecreate}<br />{LOG.update_time}
                                </td>
                                <td>{LOG.fullname}</td>
                                <td>
                                    <!-- BEGIN: schedule --> <i class="fa fa-clock"></i><b>{LANG.schedule}</b>: {LOG.schedule_title} <br /> <!-- END: schedule --> <!-- BEGIN: send_mail --> <i class="fa fa-paper-plane-o" aria-hidden="true"></i> {LANG.sendmail_to_customer}: <a data-toggle="openMail" href="{LINK_EMAIL}">{LOG.note}</a> <!-- END: send_mail --> <!-- BEGIN: plain_text -->{LOG.note}<!-- END: plain_text -->
                                </td>
                                <td width="50px">
                                    <!-- BEGIN: edit --> <a href="{LOG.action}" title="{LANG.update}"> <i class="fa fa-edit fa-lg" aria-hidden="true"></i></a> <!-- END: edit -->
                                </td>
                            </tr>
                            <!-- END: loop -->
                        </tbody>
                    </table>
                </div>
                <!-- END: comment -->
            </div>
        </div>
    </div>
    <div id="customModal" class="custom-modal">
        <div class="custom-modal-content">
            <span class="custom-modal-close">&times;</span>
            <h2 class="custom-modal-title"></h2>
            <div class="custom-modal-body">
            </div>
        </div>
    </div>

    <link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
    <script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
    <script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
    <script type="text/javascript">
    $(document).ready(function() {
        $('.uidatepicker').datepicker({
            showOn : "both",
            dateFormat : "dd/mm/yy",
            changeMonth : true,
            changeYear : true,
            showOtherMonths : true,
            buttonImage : nv_base_siteurl + "assets/images/calendar.gif",
            buttonImageOnly : true,
            maxDate : "+1m",
            minDate : "0d"
        });
        $('input[name="phone"]').on('change', function() {
            $(this).val($(this).val().replace(/[^0-9\+]/gm, ''));
        });
        $('input[name="sub_phone"]').on('change', function() {
            $(this).val($(this).val().replace(/[^0-9\+\,]/gm, ''));
        });

        <!-- BEGIN: edit -->
        $('.editbtn').click();
        <!-- END: edit -->
        var is_show_duplicate = 1;
        $('.showleadsduplicate').click(function() {
            if ($('#info .content_duplicate').hasClass('in')) {
                $('#info .content_duplicate').removeClass('in');
                $('#duplicate .fa-angle-double-down').removeClass('hidden');
                $('#duplicate .fa-angle-double-up').addClass('hidden');
                if (is_show_duplicate == 1) {
                    $('.content_duplicate').html('');
                } else {
                    $('.content_duplicate').addClass('hidden');
                }
            } else {
                $('#info .content_duplicate').addClass('in');
                $('#duplicate .fa-angle-double-down').addClass('hidden');
                $('#duplicate .fa-angle-double-up').removeClass('hidden');
                if (is_show_duplicate == 1) {
                    $('#duplicate .loading').removeClass('hidden');
                    checkduplicate(1);
                    is_show_duplicate  = 0;
                } else {
                    $('.content_duplicate').removeClass('hidden');
                }
            }
            return;
        });

        $('.showleadsunsubscribe').click(function() {
            if ($('.sunsubscribe_collapse').length == 0) { 
                $('#unsubscribe .loading').removeClass('hidden');               
                unsubscribe();
            }
            return;
        });

        function checkduplicate(next) {
            $.post(script_name + '?' + nv_name_variable + '=crmbidding&' + nv_fc_variable + '=check_duplicate&nocache=' + new Date().getTime(), 'id={ROW.id}&type=2&next=' + next, function(res) {
                $('.content_duplicate').append(res.data);
                if (res.next == 5) {
                    $('#duplicate .loading').addClass('hidden');
                } else {
                    checkduplicate(res.next);
                }
            });
        }
    });
        $('.editbtn').click(function() {
            $('.savebtn').removeClass('hidden');
            $('.deletebtn').removeClass('hidden');
            $('.editbtn').addClass('hidden');

            $('.inputinfo').removeClass('hidden');
            $('.textinfo').addClass('hidden');
             $("select[name='caregiver_id']").select2();
            $("select[name='affilacate_id']").select2();
        });
        $('.deletebtn').click(function() {
            $('.savebtn').addClass('hidden');
            $('.deletebtn').addClass('hidden');
            $('.editbtn').removeClass('hidden');

            $('.inputinfo').addClass('hidden');
            $('.textinfo').removeClass('hidden');
            $("select[name='caregiver_id']").select2('destroy');
            $("select[name='affilacate_id']").select2('destroy');
        });
        function chosse_user(id, userid) {
            $.post(script_name + '?' + nv_name_variable + '=crmbidding&'
                + nv_fc_variable + '=convert_user&nocache='
                + new Date().getTime(), 'chosse_user=1&chosse=1&id=' + id + '&userid='
                + userid, function(res) {
                    if (res == 'OK') {
                        $('#sitemodal').modal('hide');
                        window.location.href = script_name + '?' + nv_name_variable
                        + '=crmbidding&' + nv_fc_variable
                        + '=opportunities_info&id=' + id;
                    }
                });
            return;
        }

        $('.btnorder').click(function() {
            nv_open_browse(
                '{URL_DTINFO_ADMIN}index.php?'
                + nv_name_variable
                + '=bidding&'
                + nv_fc_variable
                + '=payment&userid={ROW.user_id}&showheader=0&opportunities_id={ROW.id}',
                'NVImg', 1000, 500,
                'resizable=no,scrollbars=no,toolbar=no,location=no,status=no');
            return;
        });

        $('.btn_order_add').click(function() {
            nv_open_browse(
                '{URL_DTINFO_ADMIN}index.php?'
                + nv_name_variable
                + '=bidding&'
                + nv_fc_variable
                + '=payment-content&userid={ROW.user_id}&showheader=0&admin_id={ROW.admin_id}&affilacate_id={ROW.affilacate}&opportunities_id={ROW.id}',
                'NVImg', 1000, 500,
                'resizable=no,scrollbars=no,toolbar=no,location=no,status=no');
            return;
        });

        $('.btn_add_lockseo').click(function() {
            nv_open_browse(
                '{URL_DTINFO_ADMIN}index.php?'
                + nv_name_variable
                + '=bidding&'
                + nv_fc_variable
                + '=payment-content&userid={ROW.user_id}&{BUSINESS_ID}&showheader=0&admin_id={ROW.admin_id}&affilacate_id={ROW.affilacate}&opportunities_id={ROW.id}&type=lockseo',
                'NVImg', 1000, 500,
                'resizable=no,scrollbars=no,toolbar=no,location=no,status=no');
            return;
        });
        $('.btn-label').click(function() {
            $.post(script_name + '?' + nv_name_variable + '='
                + nv_module_name + '&' + nv_fc_variable
                + '=label&nocache=' + new Date().getTime(),
                'opportunitiesid={ROW.id}&showleads=1', function(res) {
                   // modalShow('{LANG.menu_label}', res);
                    modalShow('{LANG.menu_label}', res, () => {}, () => {
                        location.reload();
                    });
                });
            return;
        });
        $('.btn-accept').click(function() {
            $.post(
                script_name + '?' + nv_name_variable + '='
                + nv_module_name + '&' + nv_fc_variable
                + '=opportunities_info&nocache='
                + new Date().getTime(), 'id={ROW.id}&accept=1',
                function(res) {
                    if (res == 'OK') {
                        window.location.href = script_name + '?'
                        + nv_name_variable + '=crmbidding&'
                        + nv_fc_variable
                        + '=opportunities_info&id={ROW.id}';
                    } else {
                        alert(res);
                    }
                });
            return;
        });
        function unsubscribe() {
            $.post(script_name + '?' + nv_name_variable + '=crmbidding&' + nv_fc_variable + '=unsubscribe&nocache=' + new Date().getTime(), 'id={ROW.id}&type=2', function(res) {
                $('#unsubscribe').html(res);
                $('#unsubscribe .fa-angle-double-down').addClass('hidden');
                $('#unsubscribe .fa-angle-double-up').removeClass('hidden');
                $('#unsubscribe .loading').addClass('hidden');
            });
        }
    </script>
    <script>
    function extractBodyOnly(response) {
        var tempDiv = document.createElement('html');
        tempDiv.innerHTML = response;

        var body = tempDiv.querySelector('body');
        if (body) {
            return body.innerHTML.trim();
        }
        return response.trim();
    }

    $(document).on('click', '.btnusers', function(e) {
        e.preventDefault();
        var opportunities_info_user_lang = "{LANG.opportunities_info_user}";
        var id = $(this).data('id');

        $.post(
            script_name + '?' + nv_name_variable + '=crmbidding&' + nv_fc_variable + '=convert_user&nocache=' + new Date().getTime(),
            { id: id },
            function(responseConvertUser) {
                var cleanContent = extractBodyOnly(responseConvertUser);
                $('#customModal .custom-modal-title').text(opportunities_info_user_lang);
                $('#customModal .custom-modal-body').html(cleanContent);
                $('#customModal').fadeIn();
            }
        );
    });

    $(document).on('click', '.custom-modal-close', function() {
        $('#customModal').fadeOut();
    });
    </script>
    <div class="col-md-10">
        <div class="panel panel-default">
            <div class="panel-body bidding-form" id="info">
                <div class="col-md-24 col-sm-24" id="duplicate">
                    <h2 class="text-info showleadsduplicate">
                        <a href="#">{LANG.leads_duplicate} ({LANG.click_view}) <i class="fa fa-angle-double-down"></i> <i class="fa fa-angle-double-up hidden"></i></a>
                    </h2>
                    <hr>
                    <div class="form-group text-center loading hidden">
                        <i class="fa fa-spinner fa-spin" aria-hidden="true"></i>
                    </div>
                    <div class="form-group content_duplicate"></div>
                </div>
                <div class="col-md-24 col-sm-24" id="unsubscribe">
                    <h2 class="text-info showleadsunsubscribe">
                        <a href="#">{LANG.leads_unsubscribe} <i class="fa fa-angle-double-down"></i> <i class="fa fa-angle-double-up hidden"></i></a>
                    </h2>
                    <hr>
                    <div class="form-group text-center loading hidden">
                        <i class="fa fa-spinner fa-spin" aria-hidden="true"></i>
                    </div>
                </div>
                <div class="col-md-24 col-sm-24">
                    <h2 class="text-info">{LANG.update_leads_history}</h2>
                    <hr>
                    <div class="table-responsive log_history">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th class="text-nowrap w100">{LANG.log_time}</th>
                                    <th class="text-nowrap">{LANG.log_user}</th>
                                    <th class="text-nowrap">{LANG.log_data}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- BEGIN: logall -->
                                <tr>
                                    <td>{LOG.log_time}</td>
                                    <td>{LOG.user}</td>
                                    <td>
                                        <!-- BEGIN: data -->
                                        <div>
                                            <!-- BEGIN: sarray -->
                                            {LOG_DATA_SHOW.0} <strong>{LOG_DATA_SHOW.1}</strong>
                                            <!-- END: sarray -->
                                            <!-- BEGIN: sstring -->
                                            {LOG_DATA_SHOW}
                                            <!-- END: sstring -->
                                            <!-- BEGIN: other1 -->
                                            &nbsp; <a data-toggle="collapse" href="#logOther{LOG.id}" aria-expanded="false" aria-controls="logOther{LOG.id}"><i class="fa fa-ellipsis-h" aria-hidden="true"></i></a>
                                            <!-- END: other1 -->
                                        </div> <!-- BEGIN: other -->
                                        <div class="collapse" id="logOther{LOG.id}">
                                            <ul class="logotherlists">
                                                <!-- BEGIN: loop -->
                                                <li>
                                                    <!-- BEGIN: sarray --> {LOG_DATA_OTHER.0} <strong>{LOG_DATA_OTHER.1}</strong> <!-- END: sarray --> <!-- BEGIN: sstring --> {LOG_DATA_OTHER} <!-- END: sstring -->
                                                </li>
                                                <!-- END: loop -->
                                            </ul>
                                        </div> <!-- END: other --> <!-- END: data -->
                                    </td>
                                </tr>
                                <!-- END: logall -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <hr>
                <div class="col-md-24 col-sm-24">
                    <p></p>
                    <p class="text-danger">
                        <b>Lưu ý:</b>
                    </p>
                    <ul>
                        <li>Các cơ hội mới tạo từ đơn hàng sẽ được tự động chia cho các sales</li>
                        <li>Để nhận đơn hàng sale cần thực hiện 1 trong các thao tác: <b>ấn nút nhận đơn</b>/ <b>comment</b>/ <b>thay đổi trạng thái đơn hàng</b></li>
                        <li>30 phút đầu giao cơ hội mới cho sale (theo lượt), sau 30 phút sale đó sẽ mất lượt và tất cả các sale đều có quyền tiếp cận khách.</li>
                        <li>Sale nào nhanh tay nhất thì sẽ được nhận oppotunities, sale chậm tay hơn thì hệ thống sẽ thông báo đơn hàng đã có sale khác nhận. Sale đó không có quyền tiếp cận nữa</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: main -->
