<!-- BEGIN: main -->
<link rel="stylesheet" href="{NV_STATIC_URL}{NV_ASSETS_DIR}/js/select2/select2.min.css">
<link type="text/css" href="{NV_BASE_SITEURL}{NV_ASSETS_DIR}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{NV_BASE_SITEURL}{NV_ASSETS_DIR}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{NV_BASE_SITEURL}{NV_ASSETS_DIR}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<div id="users">
    <div class="well">
        <form action="{FORM_ACTION}" method="get">
            <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" /> 
            <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" /> 
            <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
            <div class="row">
                <div class="col-xs-24 col-md-24"><label>{LANG.stat_time_login}</label> <em>{LANG.stat_time_comment}</em>
                    <div class="form-group reg-date">
                        <label class="control-label col-sm-3 col-md-1" for="stat_from">{LANG.stat_from}:</label>
                        <div class="col-sm-9 col-md-5">
                            <div class="form-pickdate">
                                <input name="from" id="stat-from" class="form-control" value="{time_from}" maxlength="10" type="text" autocomplete="off">
                            </div>
                        </div>
                        <label class="control-label col-sm-3 col-md-1" for="stat_to">{LANG.stat_to}:</label>
                        <div class="col-sm-9 col-md-5">
                            <div class="form-pickdate">
                                <input name="to" id="stat-to" class="form-control" value="{time_to}" maxlength="10" type="text" autocomplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xs-24 col-md-24">
                    <br>
                    <input class="btn btn-primary" type="submit" value="{LANG.stat_submit}" />
                </div>
            </div>
        </form>
    </div>
    <div class="stat-sum">
        <label>{LANG.stat_sum}</label>
        <div class="users-sum"><em>{LANG.stat_users_sum} : {RESULT.users_sum}</em></div>
        <div class="business-sum"><em>{LANG.stat_business_sum} : {RESULT.business_number}</em></div>
    </div>
</div>
<script type="text/javascript">
var export_note = '{LANG.export_note}';
var export_complete = '{LANG.export_complete}';

$(document).ready(function() {
    const _current_time = new Date();

    $("#stat-to").datepicker({
        showOn : "both",
        dateFormat : "dd/mm/yy",
        changeMonth : true,
        changeYear : true,
        showOtherMonths : true,
        buttonImage : nv_base_siteurl + "assets/images/calendar.gif",
        buttonImageOnly : true,
        yearRange: "-99:+0",
        maxDate: _current_time,
        beforeShow: function() {
            setTimeout(function(){
                $('.ui-datepicker').css('z-index', 99);
            }, 0);
        }
    });

    $("#stat-from").datepicker({
        showOn : "both",
        dateFormat : "dd/mm/yy",
        changeMonth : true,
        changeYear : true,
        showOtherMonths : true,
        buttonImage : nv_base_siteurl + "assets/images/calendar.gif",
        buttonImageOnly : true,
        yearRange: "-99:+0",
        maxDate: _current_time,
        beforeShow: function() {
            setTimeout(function(){
                $('.ui-datepicker').css('z-index', 99);
            }, 0);
        }
    });
});
</script>
<!-- END: main -->
