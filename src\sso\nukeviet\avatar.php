<?php

/**
 * @Project SSO
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @Createdate 18/7/2020, 9:40
 */

if (!defined('NV_IS_MOD_USER')) {
    die('Stop!!!');
}

if (!defined('NV_IS_USER') or !$global_config['allowuserlogin']) {
    Header('Location: ' . SSO_REGISTER_DOMAIN . '/users/');
} elseif ((int) $user_info['safemode'] > 0) {
    Header('Location: ' . SSO_REGISTER_DOMAIN . '/users/editinfo/');
} else {
    /*
     * Chỗ này xem như site quản lý thông tin thành viên có rewrite
     * Truyền thêm biến client để biết site con kết nối đên
     */
    Header('Location: ' . SSO_REGISTER_DOMAIN . '/users/avatar/upd/?client=' . urlencode(NV_MY_DOMAIN));
}
die();
