<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\supportticket\Api;

use LogKey;
use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use TicketStatus;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdateTicket implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'ticket';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $array_ticket_status, $array_ticket_labels, $array_ticket_cats, $array_admin_users, $array_active_cats, $array_active_labels, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $error = '';
        $error_code = '0000';
        $ticket_id = $nv_Request->get_int('ticket_id', 'post', '0');
        $is_comment = $nv_Request->get_int('is_comment', 'post', 0);
        if ($ticket_id > 0) {
            $row_old = $db->query('SELECT * FROM ' . TB_TICKET_ROW . ' WHERE id=' . $ticket_id)->fetch();
            if (empty($row_old)) {
                return $this->result->setError()
                    ->setCode('2004')
                    ->setMessage('Ticket not exit')
                    ->getResult();
            }

            $admin_id = $nv_Request->get_int('admin_id', 'post', '0');
            $data = $nv_Request->get_array('data', 'post');
            if (!is_array($data)) {
                return $this->result->setError()
                    ->setCode('2000')
                    ->setMessage('Param data invalid to json')
                    ->getResult();
            }
            $row = $data;
            $keys_check = [
                'title',
                'cat_id',
                'content',
                'customer_id',
                'vip_id',
                'order_id',
                'label_ids',
                'assignee_to',
                'file_attach',
                'status',
                'status_client',
                'notify',
                'add_userid',
                'last_comment_userid',
                'last_comment_time',
                'is_paid',
                'ask_expert'
            ];
            if (!empty($data)) {
                $array_sql = [];
                foreach ($data as $key => $value) {
                    if (!in_array($key, $keys_check)) {
                        return $this->result->setError()
                            ->setCode('2002')
                            ->setMessage('Missing field ' . $key . ' in otherdata')
                            ->getResult();
                    }
                    $array_sql[$key] = $key . '=:' . $key;
                }

                if (isset($row['title']) and empty($row['title'])) {
                    $error_code = '2003';
                    $error = $nv_Lang->getModule('error_required_title');
                } elseif (isset($row['cat_id']) and empty($row['cat_id'])) {
                    $error_code = '2004';
                    $error = $nv_Lang->getModule('error_required_cat_id');
                } elseif (isset($row['content']) and empty($row['content'])) {
                    $error_code = '2005';
                    $error = $nv_Lang->getModule('error_required_content');
                }
                // Comment để không bắt buộc assignee và gắn nhãn
                // elseif ($row['is_paid'] == 0 and isset($row['assignee_to']) and empty($row['assignee_to'])) {
                //     $error_code = '2006';
                //     $error = $nv_Lang->getModule('error_required_assignee');
                // } elseif (isset($row['label_ids']) and empty($row['label_ids'])) {
                //     $error_code = '2007';
                //     $error = $nv_Lang->getModule('error_required_label_ids');
                // }
                else {
                    if (!empty($row['customer_id'])) {
                        $list_vips = get_user_vips($row['customer_id']);
                        $list_orders = get_user_orders($row['customer_id']);
                        $customer_info = get_user_info($row['customer_id']);

                        if (empty($customer_info)) {
                            //Kiểm tra KH có trong csdl ko
                            $error = $nv_Lang->getModule('error_cant_save') . ' 001';
                        } elseif (!isset($array_active_cats[$row['cat_id']])) {
                            //Kiểm tra bộ phận có khả dụng ko
                            $error_code = '2008';
                            $error = $nv_Lang->getModule('error_cant_save') . ' 002';
                        } elseif ($row['vip_id'] > 0 && !isset($list_vips[$row['vip_id']])) {
                            //Kiểm tra vip có thuộc KH hiện tại ko
                            $error_code = '2009';
                            $error = $nv_Lang->getModule('error_cant_save') . ' 003';
                        } elseif ($row['order_id'] > 0 && !isset($list_orders[$row['order_id']])) {
                            //Kiểm tra đơn hàng có thuộc KH hiện tại ko
                            $error_code = '2010';
                            $error = $nv_Lang->getModule('error_cant_save') . ' 004';
                        }
                    }
                    //Kiểm tra nhãn có hợp lệ ko
                    foreach ($row['label_ids'] as $label_id) {
                        if (!isset($array_active_labels[$label_id])) {
                            $error_code = '2011';
                            $error = $nv_Lang->getModule('error_cant_save') . ' 005';
                        }
                    }

                    //Kiểm tra có assignee đúng người ko
                    if (!empty($row['assignee_to']) and $row['is_paid'] == 0) {
                        foreach ($row['assignee_to'] as $admin_id) {
                            if (!isset($array_admin_users[$admin_id])) {
                                $error_code = '2012';
                                $error = $nv_Lang->getModule('error_cant_save') . ' 006';
                            }
                        }
                    }

                    //Kiểm tra nếu tick không phải trạng thái mới tạo thì không được sửa bộ phận
                    if (!empty($row['cat_id']) && $row_old['cat_id'] != $row['cat_id']) {
                        $ticket_answer = check_ticket_answer($row_old);
                        if ($row['status'] != TicketStatus::Open->value) {
                            $error_code = '2013';
                            $error = $nv_Lang->getModule('error_cant_change_cat');
                        } elseif ($ticket_answer) {
                            $error_code = '2014';
                            $error = $nv_Lang->getModule('error_cant_change_cat');
                        }
                    }
                }

                if (empty($error)) {
                    try {
                        $sql = 'UPDATE ' . TB_TICKET_ROW . ' SET ' . implode(',', $array_sql) . ', edit_time=' . NV_CURRENTTIME . ', activity_time=' . NV_CURRENTTIME;
                        if ($is_comment == 1) {
                            $sql .= ' last_comment_userid=' . $admin_id . ', last_comment_time=' . NV_CURRENTTIME;
                        }
                        $sql .= ' WHERE id=' . $ticket_id;
                        $stmt = $db->prepare($sql);

                        foreach ($row as $key => $value) {
                            $stmt->bindValue(':' . $key, $value, PDO::PARAM_STR);
                        }
                        $exc = $stmt->execute();
                        if ($exc) {
                            // Ghi log
                            $log_data = [
                                LogKey::AdminUpdate->getLabel()
                            ];

                            foreach ($row as $key => $value) {
                                if ($key == 'status') {
                                    if ($row_old['status'] != $row['status']) {
                                        $log_data[] = [
                                            $nv_Lang->getModule('status') . ':',
                                            TicketStatus::tryFrom($row_old['status'])?->getLabel() ?? '' . ' =&gt; ' . TicketStatus::tryFrom($row['status'])?->getLabel() ?? ''
                                        ];
                                    }
                                }
                                else if ($key == 'notify') {
                                    if ($row_old['notify'] != $row['notify']) {
                                        $log_data[] = [
                                            $nv_Lang->getModule('receive_email_feedback') . ':',
                                            $row_old['notify'] . ' =&gt; ' . $row['notify']
                                        ];
                                    }
                                }
                                else if ($key == 'title') {
                                    if ($row_old['title'] != $row['title']) {
                                        $log_data[] = [
                                            $nv_Lang->getModule('ticket_title') . ':',
                                            $row_old['title'] . ' =&gt; ' . $row['title']
                                        ];
                                    }
                                }
                                else if ($key == 'content') {
                                    if ($row_old['content'] != $row['content']) {
                                        $log_data[] = [
                                            $nv_Lang->getModule('ticket_content') . ':',
                                            $row_old['content'] . ' =&gt; ' . $row['content']
                                        ];
                                    }
                                }
                                else if ($key == 'cat_id') {
                                    if ($row_old['cat_id'] != $row['cat_id']) {
                                        $log_data[] = [
                                            $nv_Lang->getModule('cat_id') . ':',
                                            $array_ticket_cats[$row_old['cat_id']]['title_vi'] . ' =&gt; ' . $array_ticket_cats[$row['cat_id']]['title_vi']
                                        ];
                                    }
                                }
                                else if ($key == 'vip_id') {
                                    if ($row_old['vip_id'] != $row['vip_id']) {
                                        $array_ticket_vips = get_user_vips($row['customer_id']);
                                        $log_data[] = [
                                            $nv_Lang->getModule('vip_id') . ':',
                                            $array_ticket_vips[$row_old['vip_id']]['title'] . ' =&gt; ' . $array_ticket_vips[$row['vip_id']]['title']
                                        ];
                                    }
                                }
                                else if ($key == 'label_ids') {
                                    if ($row_old['label_ids'] != $row['label_ids']) {
                                        $old_label_ids = explode(',', $row_old['label_ids']);
                                        $old_label_name = [];
                                        foreach ($old_label_ids as $old_label) {
                                            $old_label_name[] = $array_ticket_labels[$old_label]['title_vi'];
                                        }
                                        $label_ids = explode(',', $row['label_ids']);
                                        $label_name = [];
                                        foreach ($label_ids as $label) {
                                            $label_name[] = $array_ticket_labels[$label]['title_vi'];
                                        }
                                        $log_data[] = [
                                            $nv_Lang->getModule('label_ids') . ':',
                                            implode(', ', $old_label_name) . ' =&gt; ' . implode(', ', $label_name)
                                        ];
                                    }
                                }
                                else if ($key == 'assignee_to') {
                                    if ($row_old['assignee_to'] != $row['assignee_to']) {
                                        $old_assignee_to = explode(',', $row_old['assignee_to']);
                                        $old_caregiver = [];
                                        foreach ($old_assignee_to as $userid) {
                                            $old_caregiver[] = nv_show_name_user($array_admin_users[$userid]['first_name'], $array_admin_users[$userid]['last_name'], $array_admin_users[$userid]['username']);
                                        }
                                        $assignee_to = explode(',', $row['assignee_to']);
                                        $caregiver = [];
                                        foreach ($assignee_to as $userid) {
                                            $caregiver[] = nv_show_name_user($array_admin_users[$userid]['first_name'], $array_admin_users[$userid]['last_name'], $array_admin_users[$userid]['username']);
                                        }
                                        $log_data[] = [
                                            $nv_Lang->getModule('assignee_to') . ':',
                                            implode(', ', $old_caregiver) . ' =&gt; ' . implode(', ', $caregiver)
                                        ];
                                    }
                                }
                                else {
                                    if ($row_old[$key] != $row[$key]) {
                                        if (isset($row_old[$key]) && isset($row[$key])) {
                                            $log_data[] = [
                                                $nv_Lang->getModule($key) . ':',
                                                $row_old[$key] . ' =&gt; ' . $row[$key]
                                            ];
                                        }
                                    }
                                }
                            }
                            if (sizeof($log_data) > 1) {
                                add_ticket_logs($admin_id, LogKey::AdminUpdate->value, $log_data, $ticket_id, 1);
                            }

                            $nv_Cache->delMod($module_name);
                            if (!empty($row['assignee_to'])) {
                                $row['id'] = $ticket_id;
                                $row['customer_id'] = $row_old['customer_id'];
                                ticket_notification_assignee_to_caregiver($row, $row_old);
                            }
                            $this->result->setSuccess()->setMessage($nv_Lang->getModule('update_success'));
                            $this->result->set('ticket_id', $ticket_id);
                        }
                    } catch (PDOException $e) {
                        $this->result->setError()
                            ->setCode('3000')
                            ->setMessage(print_r($e, true));
                    }
                } else {
                    return $this->result->setError()
                        ->setCode($error_code)
                        ->setMessage($error)
                        ->getResult();
                }
            } else {
                return $this->result->setError()
                    ->setCode('2001')
                    ->setMessage('Param data empty')
                    ->getResult();
            }
        } else {
            return $this->result->setError()
                ->setCode('2003')
                ->setMessage('ticket_id is integer, more than 0' . $ticket_id)
                ->getResult();
        }

        return $this->result->getResult();
    }
}
