<?php
/*
 * thống kê sử dụng điểm của người dùng theo từng ngày
 * Chạy 1 ngày 1 lần vào 23h00
 */
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);

define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __FILE__), PATHINFO_DIRNAME))));
require NV_CONSOLE_DIR . '/server.php';
require NV_ROOTDIR . '/includes/mainfile.php';

if (ob_get_level()) {
    ob_end_clean();
}

define('NV_ADMIN', true);
if ($sys_info['ini_set_support']) {
    set_time_limit(0);

    if ((int) ini_get('memory_limit') < 512) {
        ini_set('memory_limit', '512M');
    }
}

$last_id = $db->query("SELECT config_value FROM " . NV_CONFIG_GLOBALTABLE . " WHERE lang = 'vi' AND module = 'points' AND config_name = 'last_static_pointlog'")->fetchColumn();

try {
    // Cần loại trừ toàn bộ dữ liệu của tài khoản test, tài khoản quản trị, nói chung tài khoản của nhân viên công ty
    $sql = 'SELECT admin_id FROM ' . NV_AUTHORS_GLOBALTABLE . ' WHERE is_suspend = 0';
    $result = $db->query($sql);
    $array_user_id = [];
    while ($_user_info = $result->fetch()) {
        $array_user_id[$_user_info['admin_id']] = $_user_info['admin_id'];
    }

    // các tài khoản test mà k muốn thống kê đưa vào đây, lấy theo nhóm test https://id.dauthau.net/qtdtvnds/index.php?language=vi&nv=users&op=groups&userlist=19
    $sql = 'SELECT userid FROM ' . NV_GROUPS_GLOBALTABLE . '_users WHERE group_id = 19';
    $result = $db->query($sql);
    while ($_gr_user_info = $result->fetch()) {
        $array_user_id[$_gr_user_info['userid']] = $_gr_user_info['userid'];
    }

    // lấy các giao dịch điểm của ngày
    // tb mỗi ngày ~20.000 giao dịch log với khoảng 3000 users
    $last_id_max = $last_id + 1000;
    $sql = "SELECT * FROM nv4_points_log WHERE id > " . $last_id . " AND id <= " . $last_id_max . " AND time_static = 0 AND userid NOT IN (" . implode(',', $array_user_id) . ") LIMIT 1000";
    $result = $db->query($sql);
    $arr_tmp = [];
    while ($row = $result->fetch()) {
        $date = mktime(0, 0, 0, date('m', $row['created_time']), date('d', $row['created_time']), date('Y', $row['created_time']));
        $point_in = 0;
        $point_out = 0;
        if ($row['status'] == 1) {
            // chỉ tính các giao dịch nạp điểm: đổi từ ví tiền
            // khách đổi hoặc sale đổi
            if (($row['customerid'] > 0 and $row['is_reward'] == 0) or ($row['admin_id'] > 0 and $row['sub_status'] == 0)) {
                $point_in = $row['point_total'];
            }

            // hệ thống tặng gán chung vào type_transaction = 1000
            if ($row['is_reward'] == 1) {
                $exe = $db->exec("INSERT INTO nv4_points_static (date, type, count_point, total_point) VALUES (" . $date . ", 1000, 1, " . $row['point_total'] . ")
                    ON DUPLICATE KEY UPDATE count_point=count_point + 1, total_point=total_point + " . $row['point_total'] . "");
                if ($exe) {
                    echo "Cập nhật nv4_points_static thành công!\n";
                }
            }
        } else {
            // chỉ tính các giao dịch trừ điểm do khách hàng thực hiện, bao gồm cả tặng điểm
            if ($row['customerid'] > 0 and $row['type_transaction'] > 0) {
                $point_out = $row['point_total'];
            }

            // thống kê theo tính năng sử dụng
            $exe = $db->exec("INSERT INTO nv4_points_static (date, type, count_point, total_point) VALUES (" . $date . ", " . $row['type_transaction'] . ", 1, " . $row['point_total'] . ")
                    ON DUPLICATE KEY UPDATE count_point=count_point + 1, total_point=total_point + " . $row['point_total'] . "");
            if ($exe) {
                echo "Cập nhật nv4_points_static thành công!\n";
            }
        }

        if ($point_in > 0 or $point_out > 0) {
            $exe = $db->exec("INSERT INTO nv4_points_customs_static (date, userid, point_in, point_out) VALUES (" . $date . ", " . $row['userid'] . ", " . $point_in . ", " . $point_out . ")
                ON DUPLICATE KEY UPDATE point_in=point_in + " . $point_in . ", point_out=point_out + " . $point_out . "");
            if ($exe) {
                echo "cập nhật nv4_points_customs_static thành công!\n";
            }

            if ($point_out > 0) {
                // đánh dấu đã sử dụng điểm
                $db->query("UPDATE nv4_users_info SET used_point = 1 WHERE userid = " . $row['userid'] . " AND used_point = 0");
            }
            if ($point_in > 0) {
                // số điểm đã nạp, k tính điểm tặng
                $db->query("UPDATE nv4_users_info SET point_in=point_in + " . $point_in . " WHERE userid = " . $row['userid']);
            }

            // số điểm còn lại
            $point_total = $db->query("SELECT point_total FROM `nv4_points_customs` WHERE userid = " . $row['userid'])->fetchColumn();
            if ($point_total > 0) {
                $db->query("UPDATE nv4_users_info SET point_total=" . $point_total . " WHERE userid = " . $row['userid']);
            }
        }

        $arr_tmp[$row['id']] = $row['id'];
        $last_id = $row['id'];
    }

    if (!empty($arr_tmp)) {
        $db->query("UPDATE nv4_points_log SET time_static = " . NV_CURRENTTIME . " WHERE id IN (" . implode(',', $arr_tmp) . ")");
        $db->query("UPDATE " . NV_CONFIG_GLOBALTABLE . " SET config_value = " . $last_id . " WHERE lang = 'vi' AND module = 'points' AND config_name = 'last_static_pointlog'");
    } else {
        $max_id = $db->query("SELECT MAX(id) FROM nv4_points_log")->fetchColumn();
        if ($last_id_max < $max_id) {
            $last_id = $last_id_max;
            $db->query("UPDATE " . NV_CONFIG_GLOBALTABLE . " SET config_value = " . $last_id . " WHERE lang = 'vi' AND module = 'points' AND config_name = 'last_static_pointlog'");
        } else {
            echo "Đã chạy hết!!";
            exit(1);
        }
        echo "chạy đến " . $last_id . "\n";
    }
} catch (PDOException $e) {
    print_r($e);
    die($e->getMessage());
}

echo "\n<br><br>Cập nhật trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
die("Thống kê xong\n");
