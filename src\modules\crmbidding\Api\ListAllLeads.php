<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use NukeViet\Dauthau\Condition;
use NukeViet\Dauthau\Group;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

define('NV_IS_API_LEADS', true);

class ListAllLeads implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'leads';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $array_user_id_users, $module_config, $module_name, $array_label, $array_groups_leads, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        // $lang_module = !empty($lang_module) ? $lang_module : Api::getLangModule(); //Phương thức getLangModule ko tồn tại => lỗi.
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';
        $page = $nv_Request->get_int('page', 'post', '1');
        $perpage = $nv_Request->get_int('perpage', 'post', '50');
        $use_elastic = $nv_Request->get_int('use_elastic', 'post', 1);
        if ($page <= 0 and $page > 9999) {
            return $this->result->setError()
                ->setCode('2005')
                ->setMessage('Page is integer, more than 0')
                ->getResult();
        }
        if ($perpage > 50 or $perpage <= 0) {
            return $this->result->setError()
                ->setCode('2002')
                ->setMessage('Perpage to larger')
                ->getResult();
        }

        /*
         * $where = [];
         * $where['AND'][] = [
         * '=' => [
         * 'user_id' => 8223,
         * ],
         * ];
         * $where['AND'][] = [
         * '=' => [
         * 'source_leads' => 1
         * ],
         * ];
         */

        $array_where = $nv_Request->get_array('where', 'post');
        if (!is_array($array_where)) {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Param where invalid to array')
                ->getResult();
        }
        /*
         * $order = [];
         * $order['timecreate'] = 'DESC';
         * $order['id'] = 'ASC';
         */
        $array_order = $nv_Request->get_array('order', 'post');
        if (!is_array($array_order)) {
            return $this->result->setError()
                ->setCode('2003')
                ->setMessage('Param order invalid to array')
                ->getResult();
        }

        $array_groups = $nv_Request->get_array('group', 'post');
        if (!is_array($array_groups)) {
            return $this->result->setError()
                ->setCode('2007')
                ->setMessage('Param group by invalid to json')
                ->getResult();
        }

        // key cho phép trong where
        $keys_check = [
            'id',
            'user_id',
            'source_leads',
            'label',
            'name',
            'phone',
            'sub_phone',
            'email',
            'sub_email',
            'address',
            'tax',
            'status',
            'affilacate_id',
            'caregiver_id',
            'timecreate',
            'updatetime',
            'active',
            'last_comment',
            'schedule',
            'siteid',
            'phone_search',
            'sub_phone_search',
            'elasticsearch',
            'log_merge',
            'prefix_lang',
            'opportunities_id',
        ];

        // key cho phép hiển thị, cũng là key order
        $keys_view = [
            'id',
            'user_id',
            'source_leads',
            'label',
            'name',
            'phone',
            'sub_phone',
            'email',
            'sub_email',
            'address',
            'tax',
            'status',
            'affilacate_id',
            'caregiver_id',
            'timecreate',
            'updatetime',
            'active',
            'last_comment',
            'schedule',
            'siteid',
            'phone_search',
            'sub_phone_search',
            'businessid',
            'company_name',
            'address_company',
            'opportunities_id',
            'about',
            'is_check',
            'first_time',
            'elasticsearch',
            'teleproid',
            'convert_contact',
            'convert_organization',
            'log_merge',
            'activity_time',
            'prefix_lang'
        ];

        $where = array();
        $where[] = 'active=1';

        if (!empty($array_where)) {
            // check $field
            foreach ($array_where as $keys) {
                foreach ($keys as $key) {
                    $operator = array_key_first($key);
                    $field = array_key_first($key[$operator]);
                    if (!in_array($field, $keys_check)) {
                        return $this->result->setError()
                            ->setCode('2001')
                            ->setMessage('Missing field ' . $field . ' in data')
                            ->getResult();
                    }
                }
            }

            $condition = new Condition();
            $where[] = $condition->toSqlString($array_where);
        }

        $order_string = 'id DESC';
        if (!empty($array_order)) {
            // check $field

            foreach ($array_order as $field => $type) {
                if (!in_array($field, $keys_view)) {
                    return $this->result->setError()
                        ->setCode('2004')
                        ->setMessage('Missing field ' . $field . ' in data order')
                        ->getResult();
                }
            }
            // $order = new Order();
            // $order_string = $order->toSqlString($array_order);
            $text = array();
            foreach ($array_order as $key => $val) {
                $text[] = $key . ' ' . $val;
            }
            $order_string = implode(', ', $text);
            // print_r($order_string); die;
        }

        $group_string = [];
        if (!empty($array_groups)) {
            $group = new Group();
            $group_string = $group->toSqlString($array_groups);
        }

        try {
            $is_elas = ($module_config[$module_name]['elas_use']) ? true : false;

            // kiểm tra sử dụng elastic search hay mysql
            if ($is_elas and $use_elastic) {
                require NV_ROOTDIR . '/modules/' . $module_name . '/search/leadselastic.php';
            } else {
                require NV_ROOTDIR . '/modules/' . $module_name . '/search/leadsmysql.php';
            }
            if (!empty($arr_data)) {
                $this->result->setSuccess();
                $this->result->set('total', $total);
                $this->result->set('perpage', $perpage);
                $this->result->set('page', $page);
                $this->result->set('data', $arr_data);
            } else {
                $this->result->setSuccess()
                    ->setCode('4000')
                    ->setMessage('Empty data');
            }
        } catch (PDOException $e) {
            $this->result->setError()
                ->setCode('3000')
                ->setMessage(print_r($e, true));
        }

        return $this->result->getResult();
    }
}
