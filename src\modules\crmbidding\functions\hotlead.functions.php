<?php

use NukeViet\Api\DoApi;

/**
 * Hàm check comment theo quy trình 3 7 10
 *
 */
function check_comment($source, $id, $first_time, $type)
{
    if (empty($source) or empty($id) or empty($first_time) or empty($type)) {
        return false;
    }

    $check_3710 = check_3710($source, $id, $first_time, $type);
    if ($type == 30) {
        if ($check_3710 == true) {
            $type = 10;
            return check_comment($source, $id, $first_time, $type);
        }
    } else if ($type == 10) {
        if ($check_3710 == true) {
            $type = 7;
            return check_comment($source, $id, $first_time, $type);
        }
    } else if ($type == 7) {
        if ($check_3710 == true) {
            $type = 3;
            return check_comment($source, $id, $first_time, $type);
        }
    } else if ($type == 3) {
        return $check_3710;
    }

    return false;
}
/**
 * Hàm check quy trình 3 7 10
 *
 */
function check_3710($source, $id, $first_time, $type)
{
    global $db;

    $first_time_endday = mktime(23, 59, 59, date('m', $first_time), date('d', $first_time), date('Y', $first_time));

    if ($type == 3) {
        $fromtime = $first_time;
        $to_time = $first_time_endday + 86400 * 3;
        $comments = 1;
        $tcomments = 1;
    } else if ($type == 7) {
        $fromtime = $first_time_endday + 86400 * 3;
        $to_time = $first_time_endday + 86400 * 7;
        $comments = 1;
        $tcomments = 2;
    } else if ($type == 10) {
        $fromtime = $first_time_endday + 86400 * 7;
        $to_time = $first_time_endday + 86400 * 10;
        $comments = 1;
        $tcomments = 3;
    } else if ($type == 30) {
        $fromtime = $first_time_endday + 86400 * 10;
        $to_time = $first_time_endday + 86400 * 40;
        $comments = 1;
        $tcomments = 4;
    }

    $_sql = 'SELECT COUNT(id) FROM `nv4_vi_crmbidding_comment` WHERE source =' . $source . ' AND sourceid =' . $id . ' AND timecreate >=' . $fromtime . ' AND timecreate <= ' . $to_time;
    $_tsql = 'SELECT COUNT(id) FROM `nv4_vi_crmbidding_comment` WHERE source =' . $source . ' AND sourceid =' . $id . ' AND timecreate >=' . $first_time . ' AND timecreate <= ' . $to_time;

    $_query = $db->query($_sql);
    $_tquery = $db->query($_tsql);

    if ($comments <= $_query->fetchColumn() and $tcomments <= $_tquery->fetchColumn()) {
        return true;
    }

    return false;
}

/**
 * Kiểm tra các thông tin nhập vào có trùng với lead nóng không
 *
 */
function check_hot_leads($phone, $email, $sub_phone, $sub_email, $current_id = 0, $type = 1)
{
    global $admin_info, $module_name;

    $hot_status = 0; //0 là không có lead ứng với thông tin, 1 là nóng, 2 là nguội

    $array_lead_phone = $array_oppotunities = $array_customs = [];
    // chuẩn hóa số điện thoại
    $_tmp_phone = $phone;
    if (preg_match('/(\d{9})$/', $phone, $m)) {
        $_tmp_phone = $m[0];
    }
    $_tmp_phone = preg_replace('/[!@#$%^&*()_+\-=\[\]{};:"\\|,.<>\s]/', '', $_tmp_phone);
    $_tmp_phone = (int) $_tmp_phone;

    $_tmp_sub_phone = is_null($sub_phone) ? '' : $sub_phone;
    $_tmp_sub_phone = $root_sub_phone = explode(',', $_tmp_sub_phone);
    foreach ($_tmp_sub_phone as $key => $value) {
        if (preg_match('/(\d{9})$/', $value, $m)) {
            $_tmp_sub_phone[$key] = $m[0];
        }
        $_tmp_sub_phone[$key] = preg_replace('/[!@#$%^&*()_+\-=\[\]{};:"\\|,.<>\s]/', '', $_tmp_sub_phone[$key]);
        $_tmp_sub_phone[$key] = (int) $_tmp_sub_phone[$key];
    }

    if (!empty($phone) or !empty($email)) {
        $where = [];
        $where_customs = [];
        if (!empty($email)) {
            $where['OR'][] = $where_customs['OR'][] = [
                '=' => [
                    'email' => $email
                ]
            ];

            $where['OR'][] = $where_customs['OR'][] = [
                'FIND_IN_SET' => [
                    'sub_email' => $email
                ]
            ];
        }
        if (!empty($sub_email)) {
            $_sub_email = explode(',', $sub_email);
            foreach ($_sub_email as $key => $value) {
                $where['OR'][] = $where_customs['OR'][] = [
                    '=' => [
                        'email' => $value
                    ]
                ];

                $where['OR'][] = $where_customs['OR'][] = [
                    'FIND_IN_SET' => [
                        'sub_email' => $value
                    ]
                ];
            }
        }

        if (!empty($phone)) {
            $where['OR'][] = [
                '=' => [
                    'phone_search' => $_tmp_phone
                ]
            ];

            $where['OR'][] = [
                'FIND_IN_SET' => [
                    'sub_phone_search' => $_tmp_phone
                ]
            ];

            $where_customs['OR'][] = [
                '=' => [
                    'phone' => $phone
                ]
            ];

            $where_customs['OR'][] = [
                '=' => [
                    'contact_phone' => $phone
                ]
            ];
        }
        if (!empty($sub_phone)) {
            foreach ($_tmp_sub_phone as $key => $value) {
                $where['OR'][] = [
                    '=' => [
                        'phone_search' => $value
                    ]
                ];

                $where['OR'][] = [
                    'FIND_IN_SET' => [
                        'sub_phone_search' => $value
                    ]
                ];
            }

            foreach ($root_sub_phone as $key => $value) {
                $where_customs['OR'][] = [
                    '=' => [
                        'phone' => $value
                    ]
                ];

                $where_customs['OR'][] = [
                    '=' => [
                        'contact_phone' => $value
                    ]
                ];
            }
        }

        //Nếu thông tin hiện tại có nguồn là lead thì loại trừ nó
        if ($type == 1 && $current_id > 0) {
            $where['AND'][] = [
                '!=' => [
                    'id' => $current_id
                ]
            ];
        }

        //Nếu thông tin hiện tại là cơ hội thì loại trừ lead của nó
        if ($type == 2 && $current_id > 0) {
            $where['AND'][] = [
                '!=' => [
                    'opportunities_id' => $current_id
                ]
            ];
        }

        $params_leads = [
            'userid' => $admin_info['userid'],
            'use_elastic' => 1,
            'where' => $where
        ];
        $List = nv_local_api('ListAllLeads', $params_leads, $admin_info['username'], $module_name);
        $ListAllLeads = json_decode($List, true);
        if (isset($ListAllLeads['data'])) {
            foreach ($ListAllLeads['data'] as $key => $value) {
                $array_lead_phone[$value['id']] = $value;
            }
        }

        // _opportunities
        $where = [];
        if (!empty($email)) {
            $where['OR'][] = [
                '=' => [
                    'email' => $email
                ]
            ];

            $where['OR'][] = [
                'FIND_IN_SET' => [
                    'sub_email' => $email
                ]
            ];
        }
        if (!empty($sub_email)) {
            $_sub_email = explode(',', $sub_email);
            foreach ($_sub_email as $key => $value) {
                $where['OR'][] = [
                    '=' => [
                        'email' => $value
                    ]
                ];

                $where['OR'][] = [
                    'FIND_IN_SET' => [
                        'sub_email' => $value
                    ]
                ];
            }
        }

        if (!empty($phone)) {
            $where['OR'][] = [
                '=' => [
                    'phone_search' => $_tmp_phone
                ]
            ];

            $where['OR'][] = [
                'FIND_IN_SET' => [
                    'sub_phone_search' => $_tmp_phone
                ]
            ];
        }
        if (!empty($sub_phone)) {
            foreach ($_tmp_sub_phone as $key => $value) {
                $where['OR'][] = [
                    '=' => [
                        'phone_search' => $value
                    ]
                ];

                $where['OR'][] = [
                    'FIND_IN_SET' => [
                        'sub_phone_search' => $value
                    ]
                ];
            }
        }

        //Nếu thông tin hiện tại có nguồn là cơ hội thì loại trừ nó
        if ($type == 2 && $current_id > 0) {
            $where['AND'][] = [
                '!=' => [
                    'id' => $current_id
                ]
            ];
        }

        //Nếu thông tin hiện tại có nguồn là lead thì loại trừ cơ hội của nó
        if ($type == 1 && $current_id > 0) {
            $where['AND'][] = [
                '!=' => [
                    'leadsid' => $current_id
                ]
            ];
        }

        $params = [
            'userid' => $admin_info['userid'],
            'use_elastic' => 1,
            'where' => $where
        ];

        // GỌI API
        $ListAllOpportunities = nv_local_api('ListAllOpportunities', $params, $admin_info['username'], $module_name);
        $ListAllOpportunities = json_decode($ListAllOpportunities, true);
        if (isset($ListAllOpportunities['data'])) {
            foreach ($ListAllOpportunities['data'] as $key => $value) {
                $array_oppotunities[$value['id']] = $value;
            }
        }

        //Danh sách gói vip còn hạn, chỉ check khi thêm mới lead
        if ($current_id == 0) {
            $params = [];
            $where_customs['AND'][] = [
                '>=' => [
                    'end_time' => NV_CURRENTTIME
                ]
            ];
            $params['where'] = $where_customs;
            $api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
            $api->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListBiddingCustoms')
                ->setData($params);
            $result = $api->execute();
            $error = $api->getError();
            if (empty($error) and $result['status'] == 'success') {
                $array_customs = $result['data'];
            }
        }
    }

    //Có gói vip còn hạn sử dụng thì xem là thông tin nóng
    if ($current_id == 0 && !empty($array_customs)) {
        foreach ($array_customs as $custom) {
            // Gói vip của chính mình thì bỏ qua
            if ($custom['admin_id'] == $admin_info['userid']) continue;

            if (!empty($phone) and $phone == $custom['phone']) {
                $custom['duplicate'] = $phone;
                $custom['error'] = 'phone';
            }
            if (!empty($phone) and $phone == $custom['contact_phone']) {
                $custom['duplicate'] = $phone;
                $custom['error'] = 'contact_phone';
            }
            if (!empty($email) and $email == $custom['email']) {
                $custom['duplicate'] = $email;
                $custom['error'] = 'email';
            }
            if (!empty($sub_phone)) {
                foreach ($root_sub_phone as $key => $value) {
                    if ($value == $custom['phone']) {
                        $custom['duplicate'] = $phone;
                        $custom['error'] = 'phone';
                    }
                    if ($value == $custom['contact_phone']) {
                        $custom['duplicate'] = $value;
                        $custom['error'] = 'contact_phone';
                    }
                }
            }
            if (!empty($sub_email)) {
                $_sub_email = explode(',', $sub_email);
                foreach ($_sub_email as $key => $value) {
                    if ($value == $custom['email']) {
                        $custom['duplicate'] = $value;
                        $custom['error'] = 'sub_email';
                    }
                    if (!empty($custom['sub_email'])) {
                        $__sub_email = explode(',', $custom['sub_email']);
                        foreach ($__sub_email as $_key => $_value) {
                            if ($value == $_value) {
                                $custom['duplicate'] = $value;
                                $custom['error'] = 'sub_email';
                            }
                        }
                    }
                }
            }
            $custom['type'] = 3;
            $custom['hot_remaining_to'] = nv_date('H:i:s d/m/Y', $custom['end_time']);
            return [
                'status' => 1,
                'data' => $custom,
            ];
        }
    }

    // oppotunities
    $array_oppotunity_ids = [];
    if (sizeof($array_oppotunities) > 0) {
        foreach ($array_oppotunities as $oppotunities) {
            $array_oppotunity_ids[] = $oppotunities['id'];
            if ($oppotunities['status'] == 2 && $oppotunities['customs_id'] > 0) {
                $get_bidding_customs = get_bidding_customs($oppotunities['customs_id']);
                if ($get_bidding_customs['status'] == 1) {
                    if (!empty($phone) and $_tmp_phone == $oppotunities['phone_search']) {
                        $oppotunities['duplicate'] = $phone;
                        $oppotunities['error'] = 'phone';
                    }
                    if (!empty($email) and $email == $oppotunities['email']) {
                        $oppotunities['duplicate'] = $email;
                        $oppotunities['error'] = 'email';
                    }
                    if (!empty($sub_phone)) {
                        foreach ($_tmp_sub_phone as $key => $value) {
                            if ($value == $oppotunities['phone_search']) {
                                $oppotunities['duplicate'] = $value;
                                $oppotunities['error'] = 'sub_phone';
                            }
                            if (!empty($oppotunities['sub_phone_search'])) {
                                $_sub_phone_search = explode(',', $oppotunities['sub_phone_search']);
                                foreach ($_sub_phone_search as $_key => $_value) {
                                    if ($value == $_value) {
                                        $oppotunities['duplicate'] = $value;
                                        $oppotunities['error'] = 'sub_phone';
                                    }
                                }
                            }
                        }
                    }
                    if (!empty($sub_email)) {
                        $_sub_email = explode(',', $sub_email);
                        foreach ($_sub_email as $key => $value) {
                            if ($value == $oppotunities['email']) {
                                $oppotunities['duplicate'] = $value;
                                $oppotunities['error'] = 'sub_email';
                            }
                            if (!empty($oppotunities['sub_email'])) {
                                $__sub_email = explode(',', $oppotunities['sub_email']);
                                foreach ($__sub_email as $_key => $_value) {
                                    if ($value == $_value) {
                                        $oppotunities['duplicate'] = $value;
                                        $oppotunities['error'] = 'sub_email';
                                    }
                                }
                            }
                        }
                    }
                    $oppotunities['type'] = 2;
                    $oppotunities['hot_remaining_to'] = nv_date('H:i:s d/m/Y', $get_bidding_customs['end_time']);
                    return [
                        'status' => 1,
                        'data' => $oppotunities,
                    ];
                } else {
                    $hot_status = 2;
                }
            } else {
                $check_current_oppotunities = check_current_oppotunities($oppotunities);
                $hot_lead = $check_current_oppotunities['hotting'];
                if ($hot_lead) {
                    if (!empty($phone) and $_tmp_phone == $oppotunities['phone_search']) {
                        $oppotunities['duplicate'] = $phone;
                        $oppotunities['error'] = 'phone';
                    }
                    if (!empty($email) and $email == $oppotunities['email']) {
                        $oppotunities['duplicate'] = $email;
                        $oppotunities['error'] = 'email';
                    }
                    if (!empty($sub_phone)) {
                        foreach ($_tmp_sub_phone as $key => $value) {
                            if ($value == $oppotunities['phone_search']) {
                                $oppotunities['duplicate'] = $value;
                                $oppotunities['error'] = 'sub_phone';
                            }
                            if (!empty($oppotunities['sub_phone_search'])) {
                                $_sub_phone_search = explode(',', $oppotunities['sub_phone_search']);
                                foreach ($_sub_phone_search as $_key => $_value) {
                                    if ($value == $_value) {
                                        $oppotunities['duplicate'] = $value;
                                        $oppotunities['error'] = 'sub_phone';
                                    }
                                }
                            }
                        }
                    }
                    if (!empty($sub_email)) {
                        $_sub_email = explode(',', $sub_email);
                        foreach ($_sub_email as $key => $value) {
                            if ($value == $oppotunities['email']) {
                                $oppotunities['duplicate'] = $value;
                                $oppotunities['error'] = 'sub_email';
                            }
                            if (!empty($oppotunities['sub_email'])) {
                                $__sub_email = explode(',', $oppotunities['sub_email']);
                                foreach ($__sub_email as $_key => $_value) {
                                    if ($value == $_value) {
                                        $oppotunities['duplicate'] = $value;
                                        $oppotunities['error'] = 'sub_email';
                                    }
                                }
                            }
                        }
                    }
                    $oppotunities['type'] = 2;
                    $oppotunities['hot_remaining_to'] = $check_current_oppotunities['remaining'];
                    return [
                        'status' => 1,
                        'data' => $oppotunities,
                    ];
                } else {
                    $hot_status = 2;
                }
            }
        }
    }

    if (sizeof($array_lead_phone) > 0) {
        foreach ($array_lead_phone as $lead_phone) {
            if ($lead_phone['status'] == 2) {
                if (!in_array($lead_phone['opportunities_id'], $array_oppotunity_ids)) {
                    $params = [
                        'userid' => $admin_info['userid'],
                        'opportunitiesid' => $lead_phone['opportunities_id']
                    ];

                    // GỌI API
                    $GetDetailOpportunities = nv_local_api('GetDetailOpportunities', $params, $admin_info['username'], $module_name);
                    $GetDetailOpportunities = json_decode($GetDetailOpportunities, true);
                    $oppotunities = isset($GetDetailOpportunities['data']) ? $GetDetailOpportunities['data'] : [];
                    $check_current_oppotunities = check_current_oppotunities($oppotunities);
                    $hot_lead = $check_current_oppotunities['hotting'];
                    if ($hot_lead) {
                        if (!empty($phone) and $_tmp_phone == $oppotunities['phone_search']) {
                            $oppotunities['duplicate'] = $phone;
                            $oppotunities['error'] = 'phone';
                        }
                        if (!empty($email) and $email == $oppotunities['email']) {
                            $oppotunities['duplicate'] = $email;
                            $oppotunities['error'] = 'email';
                        }
                        if (!empty($sub_phone)) {
                            foreach ($_tmp_sub_phone as $key => $value) {
                                if ($value == $oppotunities['phone_search']) {
                                    $oppotunities['duplicate'] = $value;
                                    $oppotunities['error'] = 'sub_phone';
                                }
                                if (!empty($oppotunities['sub_phone_search'])) {
                                    $_sub_phone_search = explode(',', $oppotunities['sub_phone_search']);
                                    foreach ($_sub_phone_search as $_key => $_value) {
                                        if ($value == $_value) {
                                            $oppotunities['duplicate'] = $value;
                                            $oppotunities['error'] = 'sub_phone';
                                        }
                                    }
                                }
                            }
                        }
                        if (!empty($sub_email)) {
                            $_sub_email = explode(',', $sub_email);
                            foreach ($_sub_email as $key => $value) {
                                if ($value == $oppotunities['email']) {
                                    $oppotunities['duplicate'] = $value;
                                    $oppotunities['error'] = 'sub_email';
                                }
                                if (!empty($oppotunities['sub_email'])) {
                                    $__sub_email = explode(',', $oppotunities['sub_email']);
                                    foreach ($__sub_email as $_key => $_value) {
                                        if ($value == $_value) {
                                            $oppotunities['duplicate'] = $value;
                                            $oppotunities['error'] = 'sub_email';
                                        }
                                    }
                                }
                            }
                        }
                        $oppotunities['type'] = 2;
                        $oppotunities['hot_remaining_to'] = $check_current_oppotunities['remaining'];
                        return [
                            'status' => 1,
                            'data' => $oppotunities,
                        ];
                    } else {
                        $hot_status = 2;
                    }
                }
            } else {
                $check_current_lead = check_current_lead($lead_phone);
                $hot_lead = $check_current_lead['hotting'];
                if ($hot_lead) {
                    if (!empty($phone) and $_tmp_phone == $lead_phone['phone_search']) {
                        $lead_phone['duplicate'] = $phone;
                        $lead_phone['error'] = 'phone';
                    }
                    if (!empty($email) and $email == $lead_phone['email']) {
                        $lead_phone['duplicate'] = $email;
                        $lead_phone['error'] = 'email';
                    }
                    if (!empty($sub_phone)) {
                        foreach ($_tmp_sub_phone as $key => $value) {
                            if ($value == $lead_phone['phone_search']) {
                                $lead_phone['duplicate'] = $value;
                                $lead_phone['error'] = 'sub_phone';
                            }
                            if (!empty($lead_phone['sub_phone_search'])) {
                                $_sub_phone_search = explode(',', $lead_phone['sub_phone_search']);
                                foreach ($_sub_phone_search as $_key => $_value) {
                                    if ($value == $_value) {
                                        $lead_phone['duplicate'] = $value;
                                        $lead_phone['error'] = 'sub_phone';
                                    }
                                }
                            }
                        }
                    }
                    if (!empty($sub_email)) {
                        $_sub_email = explode(',', $sub_email);
                        foreach ($_sub_email as $key => $value) {
                            if ($value == $lead_phone['email']) {
                                $lead_phone['duplicate'] = $value;
                                $lead_phone['error'] = 'sub_email';
                            }
                            if (!empty($lead_phone['sub_email'])) {
                                $__sub_email = explode(',', $lead_phone['sub_email']);
                                foreach ($__sub_email as $_key => $_value) {
                                    if ($value == $_value) {
                                        $lead_phone['duplicate'] = $value;
                                        $lead_phone['error'] = 'sub_email';
                                    }
                                }
                            }
                        }
                    }
                    $lead_phone['type'] = 1;
                    $lead_phone['hot_remaining_to'] = $check_current_lead['remaining'];
                    return [
                        'status' => 1,
                        'data' => $lead_phone,
                    ];
                } else {
                    $hot_status = 2;
                }
            }
        }
    }

    return [
        'status' => $hot_status,
    ];
}

// Thông báo thông tin lead trùng
function hot_lead_error_message($hot_leads)
{
    global $array_groups_leads, $array_status_opportunities, $array_status, $global_arr_vip, $array_user_id_users, $admin_info;

    if ($hot_leads['type'] == 1) {
        $hot_leads['type_text'] = '<b>Trùng Leads: </b>';
        $hot_leads['status'] = $array_status[$hot_leads['status']];
        $hot_leads['source_leads'] = isset($array_groups_leads[$hot_leads['source_leads']]['title']) ? '; Nguồn Leads: <b>' . $array_groups_leads[$hot_leads['source_leads']]['title'] . '</b>' : '';
    } elseif ($hot_leads['type'] == 2) {
        $hot_leads['type_text'] = '<b>Trùng Cơ hội: </b>';
        $hot_leads['status'] = $array_status_opportunities[$hot_leads['status']];
        $hot_leads['source_leads'] = '';
    } else {
        $hot_leads['type_text'] = '<b>Trùng gói VIP: </b>';
        $hot_leads['source_leads'] = '';
    }

    if ($hot_leads['type'] == 3) {
        $admin_fullname = nv_show_name_user($array_user_id_users[$hot_leads['admin_id']]['first_name'], $array_user_id_users[$hot_leads['admin_id']]['last_name'], $array_user_id_users[$hot_leads['admin_id']]['username']);
        $hot_leads['name'] = 'Khách hàng: <b>' . $hot_leads['name'] . '</b>';
        $hot_leads['vip'] = '; Gói: <b>' . $global_arr_vip[$hot_leads['vip']] . '</b>';
        $hot_leads['admin'] = '; Khách hàng của: <b>' . $admin_fullname . '</b>';
        if (!empty($hot_leads['duplicate'])) {
            $hot_leads['duplicate'] = '; Trùng trường dữ liệu: <b><span class="red">' . $hot_leads['duplicate'] . '</span></b>';
        }

        $hot_leads['hot_remaining_to'] = '; Còn hạn đến: <b>' . $hot_leads['hot_remaining_to'] . '</b>';

        $error = $hot_leads['type_text'] . $hot_leads['name'] . $hot_leads['vip'] . $hot_leads['admin'] . $hot_leads['duplicate'] . $hot_leads['hot_remaining_to'];
    } else {
        $hot_leads['name'] = 'Họ tên: <b>' . $hot_leads['name'] . '</b>';
        $hot_leads['status'] = '; Trạng thái: <b>' . $hot_leads['status'] . '</b>';
        if (!empty($hot_leads['duplicate'])) {
            $hot_leads['duplicate'] = '; Trùng trường dữ liệu: <b><span class="red">' . $hot_leads['duplicate'] . '</span></b>';
        }
        $hot_leads['caregiver_id_fullname'] = '; Người chăm sóc: <b>' . $hot_leads['caregiver_id_fullname'] . '</b>';
        $hot_leads['updatetime_display'] = '; Cập nhật lần cuối: <b>' . $hot_leads['updatetime_display'] . '</b>';
        $hot_leads['schedule_display'] = empty($hot_leads['schedule_display']) ? '' : '; Lịch hẹn: <b>' . $hot_leads['schedule_display'] . '</b>';

        if ($hot_leads['status'] != 2 && $hot_leads['first_time'] > 0) {
            if ($hot_leads['schedule'] > 0 && NV_CURRENTTIME > $hot_leads['schedule']) {
                $hot_leads['starttime'] = nv_date('H:i:s d/m/Y', $hot_leads['schedule']);
            } else {
                $hot_leads['starttime'] = nv_date('H:i:s d/m/Y', $hot_leads['first_time']);
            }
            $hot_leads['starttime'] = !empty($hot_leads['starttime']) ? '; Chăm sóc 3-7-10 từ: <b>' . $hot_leads['starttime'] . '</b>' : '';
        }

        $hot_leads['hot_remaining_to'] = '; Còn nóng đến: <b>' . $hot_leads['hot_remaining_to'] . '</b>';

        $error = $hot_leads['type_text'] . $hot_leads['name'] . $hot_leads['duplicate'] . $hot_leads['status'] . $hot_leads['source_leads'] . $hot_leads['caregiver_id_fullname'] . $hot_leads['starttime'] . $hot_leads['updatetime_display'] . $hot_leads['schedule_display'] . $hot_leads['hot_remaining_to'];
    }

    return $error;
}

//Check lead nóng theo quy trình 3 7 10
function check_current_lead($lead_phone)
{
    if (empty($lead_phone)) {
        return [
            'hotting' => false,
            'remaining' => NV_CURRENTTIME,
        ];
    }
    $lead_phone['updatetime'] = intval($lead_phone['updatetime']);
    $hot_lead = true;
    $starttime = $lead_phone['first_time'];
    $updatetime_endday = mktime(23, 59, 59, date('m', $lead_phone['updatetime']), date('d', $lead_phone['updatetime']), date('Y', $lead_phone['updatetime']));
    $hot_remaining_to = NV_CURRENTTIME;
    if ($lead_phone['schedule'] > 0) {
        // kiểm tra thời hạn của lịch hẹn với thời gian hiện tại
        if (NV_CURRENTTIME > $lead_phone['schedule']) {
            $starttime = $lead_phone['schedule'];
            $numday = ceil((NV_CURRENTTIME - $lead_phone['schedule']) / 86400);
            //Giai đoạn 3 ngày đầu lead mặc định luôn nóng, các giai đoạn sau sẽ kiểm tra xem giai đoạn trước đã chăm đủ lượt hay chưa
            if ($numday <= 3) {
                $hot_lead = true;
                //3 ngày đầu mặc định là lead nóng, nhưng nếu có cmt thì mốc xét nóng tiếp theo sẽ là ngày thứ 7
                if (check_comment(1, $lead_phone['id'], $lead_phone['schedule'], 3) == true) {
                    $hot_remaining_to = $lead_phone['schedule'] + (7 * 86400);
                } else {
                    $hot_remaining_to = $lead_phone['schedule'] + (3 * 86400);
                }
            } elseif ($numday > 3 and $numday <= 7) {
                //Nếu mốc 3->7 ngày có cmt thì mốc xét nóng tiếp theo là ngày thứ 10
                if (check_comment(1, $lead_phone['id'], $lead_phone['schedule'], 7) == true) {
                    $hot_lead = true;
                    $hot_remaining_to = $lead_phone['schedule'] + (10 * 86400);
                } elseif (check_comment(1, $lead_phone['id'], $lead_phone['schedule'], 3) == true) {
                    $hot_lead = true;
                    $hot_remaining_to = $lead_phone['schedule'] + (7 * 86400);
                } else {
                    $hot_lead = false;
                }
            } elseif ($numday > 7 and $numday <= 10) {
                //Nếu mốc 7->10 ngày có cmt thì mốc xét nóng tiếp theo là ngày thứ 40
                if (check_comment(1, $lead_phone['id'], $lead_phone['schedule'], 10) == true) {
                    $hot_lead = true;
                    $hot_remaining_to = $lead_phone['schedule'] + (40 * 86400);
                } elseif (check_comment(1, $lead_phone['id'], $lead_phone['schedule'], 7) == true) {
                    $hot_lead = true;
                    $hot_remaining_to = $lead_phone['schedule'] + (10 * 86400);
                } else {
                    $hot_lead = false;
                }
            } elseif ($numday > 10 and $numday <= 40) {
                //Nếu mốc 10->30 ngày có cmt thì mốc xét nóng tiếp theo là sau 30 ngày kể từ lần chăm cuối
                if (check_comment(1, $lead_phone['id'], $lead_phone['schedule'], 30) == true) {
                    $hot_lead = true;
                    $hot_remaining_to = $updatetime_endday + (30 * 86400);
                } elseif (check_comment(1, $lead_phone['id'], $lead_phone['schedule'], 10) == true) {
                    $hot_lead = true;
                    $hot_remaining_to = $lead_phone['schedule'] + (40 * 86400);
                } else {
                    $hot_lead = false;
                }
            } else {
                //Sau quy trình 3 7 10 sẽ mở ra cho người khác nhận nếu không có update gì trong 30 ngày gần nhất
                if ($updatetime_endday + (30 * 86400) > NV_CURRENTTIME && check_comment(1, $lead_phone['id'], $lead_phone['schedule'], 30) == true) {
                    $hot_lead = true;
                    $hot_remaining_to = $updatetime_endday + (30 * 86400);
                } else {
                    $hot_lead = false;
                }
            }
        } elseif ($lead_phone['first_time'] >= 1732665600) {
            // Nếu lịch hẹn chưa đến ngày hẹn thì cần kiểm tra quy trình 3 7 10 theo updatetime như thường, tính từ ngày 27/11/2024
            $check_careof_leads = check_careof_leads($lead_phone);
            $hot_lead = $check_careof_leads['hotting'];
            $hot_remaining_to = $check_careof_leads['remaining'];
        } else {
            $hot_lead = true;
            $hot_remaining_to = $lead_phone['schedule'] + (3 * 86400);
        }
    } else {
        $check_careof_leads = check_careof_leads($lead_phone);
        $hot_lead = $check_careof_leads['hotting'];
        $hot_remaining_to = $check_careof_leads['remaining'];
    }

    return [
        'hotting' => $hot_lead,
        'starttime' => nv_date('H:i:s d/m/Y', $starttime),
        'remaining' => nv_date('H:i:s d/m/Y', $hot_remaining_to),
    ];
}

// Kiểm tra quá trình chăm sóc lead theo quy trình 3 7 10
function check_careof_leads($lead_phone) {
    $hot_lead = true;
    $hot_remaining_to = NV_CURRENTTIME;
    $first_time_endday = mktime(23, 59, 59, date('m', $lead_phone['first_time']), date('d', $lead_phone['first_time']), date('Y', $lead_phone['first_time']));
    $updatetime_endday = mktime(23, 59, 59, date('m', $lead_phone['updatetime']), date('d', $lead_phone['updatetime']), date('Y', $lead_phone['updatetime']));
    /*
    * Trong 3 ngày làm việc đầu tiên kể từ khi sale nhận lead về chăm sóc
    * nếu lead không có thông tin cập nhật mới thì lead đó sẽ được mở trên phần Check trùng, nếu tool Nhả lead chưa xử lý đến.
    */
    if ($lead_phone['first_time'] == $lead_phone['updatetime']) {
        if ($first_time_endday + (3 * 86400) <= NV_CURRENTTIME) {
            $hot_lead = false;
        } else {
            $hot_lead = true;
            //3 ngày đầu mặc định là lead nóng, nhưng nếu có cmt thì mốc xét nóng tiếp theo sẽ là ngày thứ 7
            if (check_comment(1, $lead_phone['id'], $lead_phone['first_time'], 3) == true) {
                $hot_remaining_to = $first_time_endday + (7 * 86400);
            } else {
                $hot_remaining_to = $first_time_endday + (3 * 86400);
            }
        }
    } else {
        if (empty($lead_phone['caregiver_id'])) {
            return [
                'hotting' => false,
                'remaining' => NV_CURRENTTIME,
            ];
        }
        /*
        * Kiểm tra quy trình 3 7 10
        */
        $numday = ceil((NV_CURRENTTIME - $first_time_endday) / 86400);
        //Giai đoạn 3 ngày đầu lead mặc định luôn nóng, các giai đoạn sau sẽ kiểm tra xem giai đoạn trước đã chăm đủ lượt hay chưa
        if ($numday <= 3) {
            $hot_lead = true;
            //3 ngày đầu mặc định là lead nóng, nhưng nếu có cmt thì mốc xét nóng tiếp theo sẽ là ngày thứ 7
            if (check_comment(1, $lead_phone['id'], $lead_phone['first_time'], 3) == true) {
                $hot_remaining_to = $first_time_endday + (7 * 86400);
            } else {
                $hot_remaining_to = $first_time_endday + (3 * 86400);
            }
        } elseif ($numday > 3 and $numday <= 7) {
            //Nếu mốc 3->7 ngày có cmt thì mốc xét nóng tiếp theo là ngày thứ 10
            if (check_comment(1, $lead_phone['id'], $lead_phone['first_time'], 7) == true) {
                $hot_lead = true;
                $hot_remaining_to = $first_time_endday + (10 * 86400);
            } elseif (check_comment(1, $lead_phone['id'], $lead_phone['first_time'], 3) == true) {
                $hot_lead = true;
                $hot_remaining_to = $first_time_endday + (7 * 86400);
            } else {
                $hot_lead = false;
            }
        } elseif ($numday > 7 and $numday <= 10) {
            //Nếu mốc 7->10 ngày có cmt thì mốc xét nóng tiếp theo là ngày thứ 40
            if (check_comment(1, $lead_phone['id'], $lead_phone['first_time'], 10) == true) {
                $hot_lead = true;
                $hot_remaining_to = $first_time_endday + (40 * 86400);
            } elseif (check_comment(1, $lead_phone['id'], $lead_phone['first_time'], 7) == true) {
                $hot_lead = true;
                $hot_remaining_to = $first_time_endday + (10 * 86400);
            } else {
                $hot_lead = false;
            }
        } elseif ($numday > 10 and $numday <= 40) {
            //Nếu mốc 10->30 ngày có cmt thì mốc xét nóng tiếp theo là sau 30 ngày kể từ lần chăm cuối
            if (check_comment(1, $lead_phone['id'], $lead_phone['first_time'], 30) == true) {
                $hot_lead = true;
                $hot_remaining_to = $updatetime_endday + (30 * 86400);
            } elseif (check_comment(1, $lead_phone['id'], $lead_phone['first_time'], 10) == true) {
                $hot_lead = true;
                $hot_remaining_to = $first_time_endday + (40 * 86400);
            } else {
                $hot_lead = false;
            }
        } else {
            //Sau quy trình 3 7 10 sẽ mở ra cho người khác nhận nếu không có update gì trong 30 ngày gần nhất
            if ($updatetime_endday + (30 * 86400) > NV_CURRENTTIME && check_comment(1, $lead_phone['id'], $lead_phone['first_time'], 30) == true) {
                $hot_lead = true;
                $hot_remaining_to = $updatetime_endday + (30 * 86400);
            } else {
                $hot_lead = false;
            }
        }
    }

    return [
        'hotting' => $hot_lead,
        'remaining' => $hot_remaining_to,
    ];;
}

// Kiểm tra cơ hội có nóng hay không, ưu tiên kiểm tra lịch hẹn trước, không có lịch hẹn sẽ kiểm tra theo tg nhận lead
function check_current_oppotunities($oppotunities)
{
    if (empty($oppotunities)) {
        return [
            'hotting' => false,
            'remaining' => NV_CURRENTTIME,
        ];
    }

    $hot_lead = true;
    $starttime = $oppotunities['first_time'];
    $updatetime_endday = mktime(23, 59, 59, date('m', $oppotunities['updatetime']), date('d', $oppotunities['updatetime']), date('Y', $oppotunities['updatetime']));
    $hot_remaining_to = NV_CURRENTTIME;
    if ($oppotunities['schedule'] > 0) {
        // kiểm tra thời hạn của lịch hẹn với thời gian hiện tại
        if (NV_CURRENTTIME > $oppotunities['schedule']) {
            $starttime = $oppotunities['schedule'];
            $numday = ceil((NV_CURRENTTIME - $oppotunities['schedule']) / 86400);
            //Giai đoạn 3 ngày đầu lead mặc định luôn nóng, các giai đoạn sau sẽ kiểm tra xem giai đoạn trước đã chăm đủ lượt hay chưa
            if ($numday <= 3) {
                $hot_lead = true;
                //3 ngày đầu mặc định là lead nóng, nhưng nếu có cmt thì mốc xét nóng tiếp theo sẽ là ngày thứ 7
                if (check_comment(2, $oppotunities['id'], $oppotunities['schedule'], 3) == true) {
                    $hot_remaining_to = $oppotunities['schedule'] + (7 * 86400);
                } else {
                    $hot_remaining_to = $oppotunities['schedule'] + (3 * 86400);
                }
            } elseif ($numday > 3 and $numday <= 7) {
                //Nếu mốc 3->7 ngày có cmt thì mốc xét nóng tiếp theo là ngày thứ 10
                if (check_comment(2, $oppotunities['id'], $oppotunities['schedule'], 7) == true) {
                    $hot_lead = true;
                    $hot_remaining_to = $oppotunities['schedule'] + (10 * 86400);
                } elseif (check_comment(2, $oppotunities['id'], $oppotunities['schedule'], 3) == true) {
                    $hot_lead = true;
                    $hot_remaining_to = $oppotunities['schedule'] + (7 * 86400);
                } else {
                    $hot_lead = false;
                }
            } elseif ($numday > 7 and $numday <= 10) {
                //Nếu mốc 7->10 ngày có cmt thì mốc xét nóng tiếp theo là ngày thứ 40
                if (check_comment(2, $oppotunities['id'], $oppotunities['schedule'], 10) == true) {
                    $hot_lead = true;
                    $hot_remaining_to = $oppotunities['schedule'] + (40 * 86400);
                } elseif (check_comment(2, $oppotunities['id'], $oppotunities['schedule'], 7) == true) {
                    $hot_lead = true;
                    $hot_remaining_to = $oppotunities['schedule'] + (10 * 86400);
                } else {
                    $hot_lead = false;
                }
            } elseif ($numday > 10 and $numday <= 40) {
                //Nếu mốc 10->30 ngày có cmt thì mốc xét nóng tiếp theo là sau 30 ngày kể từ lần chăm cuối
                if (check_comment(2, $oppotunities['id'], $oppotunities['schedule'], 30) == true) {
                    $hot_lead = true;
                    $hot_remaining_to = $updatetime_endday + (30 * 86400);
                } elseif (check_comment(2, $oppotunities['id'], $oppotunities['schedule'], 10) == true) {
                    $hot_lead = true;
                    $hot_remaining_to = $oppotunities['schedule'] + (40 * 86400);
                } else {
                    $hot_lead = false;
                }
            } else {
                //Sau quy trình 3 7 10 sẽ mở ra cho người khác nhận nếu không có update gì trong 30 ngày gần nhất
                if ($updatetime_endday + (30 * 86400) > NV_CURRENTTIME && check_comment(1, $oppotunities['id'], $oppotunities['schedule'], 30) == true) {
                    $hot_lead = true;
                    $hot_remaining_to = $updatetime_endday + (30 * 86400);
                } else {
                    $hot_lead = false;
                }
            }
        } elseif ($oppotunities['first_time'] >= 1732665600) {
            // Nếu lịch hẹn chưa đến ngày hẹn thì cần kiểm tra quy trình 3 7 10 theo updatetime như thường, tính từ ngày 27/11/2024
            $check_careof_oppotunities = check_careof_oppotunities($oppotunities);
            $hot_lead = $check_careof_oppotunities['hotting'];
            $hot_remaining_to = $check_careof_oppotunities['remaining'];
        } else {
            $hot_lead = true;
            $hot_remaining_to = $oppotunities['schedule'] + (3 * 86400);
        }
    } else {
        $check_careof_oppotunities = check_careof_oppotunities($oppotunities);
        $hot_lead = $check_careof_oppotunities['hotting'];
        $hot_remaining_to = $check_careof_oppotunities['remaining'];
    }

    return [
        'hotting' => $hot_lead,
        'starttime' => nv_date('H:i:s d/m/Y', $starttime),
        'remaining' => nv_date('H:i:s d/m/Y', $hot_remaining_to),
    ];
}

// Kiểm tra quá trình chăm sóc cơ hội theo quy trình 3 7 10
function check_careof_oppotunities($oppotunities) {
    $hot_lead = true;
    $hot_remaining_to = NV_CURRENTTIME;
    $first_time_endday = mktime(23, 59, 59, date('m', $oppotunities['first_time']), date('d', $oppotunities['first_time']), date('Y', $oppotunities['first_time']));
    $updatetime_endday = mktime(23, 59, 59, date('m', $oppotunities['updatetime']), date('d', $oppotunities['updatetime']), date('Y', $oppotunities['updatetime']));
    /*
    * Trong 3 ngày làm việc đầu tiên kể từ khi sale nhận lead về chăm sóc
    * nếu lead không có thông tin cập nhật mới thì lead đó sẽ được mở trên phần Check trùng, nếu tool Nhả lead chưa xử lý đến.
    */
    if ($oppotunities['first_time'] == $oppotunities['updatetime']) {
        if ($first_time_endday + (3 * 86400) <= NV_CURRENTTIME) {
            $hot_lead = false;
        } else {
            $hot_lead = true;
            //3 ngày đầu mặc định là lead nóng, nhưng nếu có cmt thì mốc xét nóng tiếp theo sẽ là ngày thứ 7
            if (check_comment(2, $oppotunities['id'], $oppotunities['first_time'], 3) == true) {
                $hot_remaining_to = $first_time_endday + (7 * 86400);
            } else {
                $hot_remaining_to = $first_time_endday + (3 * 86400);
            }
        }
    } else {
        if (empty($oppotunities['caregiver_id'])) {
            return [
                'hotting' => false,
                'remaining' => NV_CURRENTTIME,
            ];
        }
        /*
        * Kiểm tra quy trình 3 7 10
        */
        $numday = ceil((NV_CURRENTTIME - $first_time_endday) / 86400);
        //Giai đoạn 3 ngày đầu lead mặc định luôn nóng, các giai đoạn sau sẽ kiểm tra xem giai đoạn trước đã chăm đủ lượt hay chưa
        if ($numday <= 3) {
            $hot_lead = true;
            //3 ngày đầu mặc định là lead nóng, nhưng nếu có cmt thì mốc xét nóng tiếp theo sẽ là ngày thứ 7
            if (check_comment(2, $oppotunities['id'], $oppotunities['first_time'], 3) == true) {
                $hot_remaining_to = $first_time_endday + (7 * 86400);
            } else {
                $hot_remaining_to = $first_time_endday + (3 * 86400);
            }
        } elseif ($numday > 3 and $numday <= 7) {
            //Nếu mốc 3->7 ngày có cmt thì mốc xét nóng tiếp theo là ngày thứ 10
            if (check_comment(2, $oppotunities['id'], $oppotunities['first_time'], 7) == true) {
                $hot_lead = true;
                $hot_remaining_to = $first_time_endday + (10 * 86400);
            } elseif (check_comment(2, $oppotunities['id'], $oppotunities['first_time'], 3) == true) {
                $hot_lead = true;
                $hot_remaining_to = $first_time_endday + (7 * 86400);
            } else {
                $hot_lead = false;
            }
        } elseif ($numday > 7 and $numday <= 10) {
            //Nếu mốc 7->10 ngày có cmt thì mốc xét nóng tiếp theo là ngày thứ 40
            if (check_comment(2, $oppotunities['id'], $oppotunities['first_time'], 10) == true) {
                $hot_lead = true;
                $hot_remaining_to = $first_time_endday + (40 * 86400);
            } elseif (check_comment(2, $oppotunities['id'], $oppotunities['first_time'], 7) == true) {
                $hot_lead = true;
                $hot_remaining_to = $first_time_endday + (10 * 86400);
            } else {
                $hot_lead = false;
            }
        } elseif ($numday > 10 and $numday <= 40) {
            //Nếu mốc 10->30 ngày có cmt thì mốc xét nóng tiếp theo là sau 30 ngày kể từ lần chăm cuối
            if (check_comment(2, $oppotunities['id'], $oppotunities['first_time'], 30) == true) {
                $hot_lead = true;
                $hot_remaining_to = $updatetime_endday + (30 * 86400);
            } elseif (check_comment(2, $oppotunities['id'], $oppotunities['first_time'], 10) == true) {
                $hot_lead = true;
                $hot_remaining_to = $first_time_endday + (40 * 86400);
            } else {
                $hot_lead = false;
            }
        } else {
            //Sau quy trình 3 7 10 sẽ mở ra cho người khác nhận nếu không có update gì trong 30 ngày gần nhất
            if ($updatetime_endday + (30 * 86400) > NV_CURRENTTIME && check_comment(1, $oppotunities['id'], $oppotunities['first_time'], 30) == true) {
                $hot_lead = true;
                $hot_remaining_to = $updatetime_endday + (30 * 86400);
            } else {
                $hot_lead = false;
            }
        }
    }

    return [
        'hotting' => $hot_lead,
        'remaining' => $hot_remaining_to,
    ];;
}

/**
 * Ghi log thời gian và lý do bắt đầu chăm sóc 3-7-10
 */
function add_log_3710($type, $id, $content)
{
    global $db, $module_name, $admin_info, $id;

    if ($type == 1) {
        $leads_id = $id;
        $oppotunities_id = 0;
    } else {
        $oppotunities_id = $id;
        $leads_id = 0;
    }

    $log_data[] = [
        'Bắt đầu quy trình 3-7-10'
    ];
    $log_data[] = [
        'Nội dung:',
        $content,
    ];

    $params = [
        'userid' => $admin_info['admin_id'],
        'log_area' => 1,
        'log_key' => 'LOG_ADMIN_PROCESS_3710',
        'log_time' => NV_CURRENTTIME,
        'log_data' => $log_data,
        'leads_id' => $leads_id,
        'oppotunities_id' => $oppotunities_id,
    ];

    nv_local_api('CreateAllLogs', $params, $admin_info['username'], $module_name);
}
