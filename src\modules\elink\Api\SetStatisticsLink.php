<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\elink\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class SetStatisticsLink implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'stat';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $global_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        // Các site thanh toán
        $sql = "SELECT * FROM " . $db_config['prefix'] . "_wallet_sites ORDER BY weight ASC";
        $global_array_sites = $nv_Cache->db($sql, 'id', 'wallet');

        $array = [];
        $array['site_id'] = $nv_Request->get_absint('site_id', 'post', 0);
        $array['userid'] = $nv_Request->get_absint('userid', 'post', 0);
        $array['referer'] = $nv_Request->get_title('referer', 'post', '');
        $array['selfurl'] = $nv_Request->get_title('selfurl', 'post', '');
        $array['accesstime'] = $nv_Request->get_absint('accesstime', 'post', 0);
        $array['ip'] = $nv_Request->get_title('ip', 'post', '');
        $array['user_agent'] = $nv_Request->get_title('user_agent', 'post', '');

        if (!empty($array['site_id']) and !isset($global_array_sites[$array['site_id']])) {
            return $this->result->setCode('1002')->setMessage($nv_Lang->getModule('apierror_site_exists'))->getResult();
        }

        $sql = "INSERT INTO " . $db_config['prefix'] . "_elink_statistics_link (
            site_id, userid, link_referer, link_target, access_time, access_ip, access_agent
        ) VALUES (
            " . $array['site_id'] . ", " . $array['userid'] . ", " . $db->quote($array['referer']) . ",
            " . $db->quote($array['selfurl']) . ", " . $array['accesstime'] . ", " . $db->quote($array['ip']) . ",
            " . $db->quote($array['user_agent']) . "
        )";
        $stat_id = $db->insert_id($sql, 'id');

        if (empty($stat_id)) {
            return $this->result->setCode('1002')->setMessage('Save DB error!!!')->getResult();
        }

        $this->result->set('data', $stat_id);
        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
