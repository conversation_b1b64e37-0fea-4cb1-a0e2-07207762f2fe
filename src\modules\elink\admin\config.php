<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 2-9-2010 14:43
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('config');
$array_config = [];
$array_config_group = [];
$groups_list = nv_groups_list();
unset($groups_list[5], $groups_list[6]);

if ($nv_Request->isset_request('submit', 'post')) {
    $array_config['promotion_code_length'] = $nv_Request->get_int('promotion_code_length', 'post', 0);
    $array_config['affiliate_code_groups'] = implode(',', nv_groups_post($nv_Request->get_typed_array('affiliate_code_groups', 'post', 'int', [])));
    $array_config['promotion_use_limityear'] = $nv_Request->get_title('promotion_use_limityear', 'post', '');
    $promotion_use_limityear = [];
    $array_config['promotion_use_limityear'] = explode(',', $array_config['promotion_use_limityear']);
    foreach ($array_config['promotion_use_limityear'] as $value) {
        $value = intval($value);
        if ($value > 0) {
            $promotion_use_limityear[] = $value;
        }
    }
    asort($promotion_use_limityear);
    $array_config['promotion_use_limityear'] = implode(',', array_unique($promotion_use_limityear));

    if ($array_config['promotion_code_length'] < 1 or $array_config['promotion_code_length'] > 100) {
        $array_config['promotion_code_length'] = 8;
    }

    $array_config['discount_freelance'] = abs($nv_Request->get_float('discount_freelance', 'post', 0));
    $array_config['discount_official'] = abs($nv_Request->get_float('discount_official', 'post', 0));
    $array_config['group_id_collaborator'] = $nv_Request->get_absint('group_id_collaborator', 'post', 0);
    $array_config['max_freelance_promo_value'] = $nv_Request->get_absint('max_freelance_promo_value', 'post', 0);
    $array_config['max_official_promo_value'] = $nv_Request->get_absint('max_official_promo_value', 'post', 0);

    $sth = $db->prepare('UPDATE ' . $db_config['prefix'] . '_' . $module_data . '_config SET config_value = :config_value WHERE config_name = :config_name');
    foreach ($array_config as $config_name => $config_value) {
        $sth->bindParam(':config_name', $config_name, PDO::PARAM_STR);
        $sth->bindParam(':config_value', $config_value, PDO::PARAM_STR);
        $sth->execute();
    }

    // Xóa hết thiết lập nhóm
    $db->query("TRUNCATE " . $db_config['prefix'] . "_" . $module_data . "_config_group");

    // Ghi lại thiết lập nhóm
    $promo_enable = $nv_Request->get_typed_array('promo_enable', 'post', 'int', []);
    $promo_number = $nv_Request->get_typed_array('promo_number', 'post', 'int', []);
    $promo_coupon = $nv_Request->get_typed_array('promo_coupon', 'post', 'int', []);
    $promo_voucher = $nv_Request->get_typed_array('promo_voucher', 'post', 'int', []);
    $promo_couponmax = $nv_Request->get_typed_array('promo_couponmax', 'post', 'float', []);
    $promo_vouchermax = $nv_Request->get_typed_array('promo_vouchermax', 'post', 'float', []);
    foreach ($groups_list as $group_id => $group_title) {
        if (!empty($promo_enable[$group_id])) {
            $_promo_enable = 1;
            $_promo_number = (isset($promo_number[$group_id]) and $promo_number[$group_id] > 0) ? $promo_number[$group_id] : 1;
            $_promo_coupon = (isset($promo_coupon[$group_id]) and $promo_coupon[$group_id] > 0) ? 1 : 0;
            $_promo_voucher = (isset($promo_voucher[$group_id]) and $promo_voucher[$group_id] > 0) ? 1 : 0;
            if (!$_promo_coupon and !$_promo_voucher) {
                $_promo_enable = 0;
            }
            $_promo_couponmax = (isset($promo_couponmax[$group_id]) and $promo_couponmax[$group_id] > 0) ? $promo_couponmax[$group_id] : 10;
            $_promo_vouchermax = (isset($promo_vouchermax[$group_id]) and $promo_vouchermax[$group_id] > 0) ? $promo_vouchermax[$group_id] : 500000;

            $sql = "INSERT INTO " . $db_config['prefix'] . "_" . $module_data . "_config_group (
                group_id, promo_enable, promo_number, promo_coupon, promo_voucher, promo_couponmax, promo_vouchermax
            ) VALUES (
                " . $group_id . ", " . $_promo_enable . ", " . $_promo_number . ",
                " . $_promo_coupon . ", " . $_promo_voucher . ", " . $_promo_couponmax . ", " . $_promo_vouchermax . "
            )";
            $db->query($sql);
        }
    }

    $nv_Cache->delMod('settings');
    $nv_Cache->delMod($module_name);
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
}

$array_config['affiliate_code_groups'] = '4';
$array_config['promotion_code_length'] = 8;
$array_config['promotion_use_limityear'] = '';

$sql = 'SELECT config_name, config_value FROM ' . $db_config['prefix'] . '_' . $module_data . '_config';
$result = $db->query($sql);
while (list ($c_config_name, $c_config_value) = $result->fetch(3)) {
    $array_config[$c_config_name] = $c_config_value;
}

$array_config['affiliate_code_groups'] = explode(',', $array_config['affiliate_code_groups']);

$sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_config_group";
$result = $db->query($sql);
while ($row = $result->fetch()) {
    $array_config_group[$row['group_id']] = $row;
}

$xtpl = new XTemplate('config.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('FORM_ACTION', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('DATA', $array_config);
$xtpl->assign('COLSPAN', sizeof($groups_list) + 1);

foreach ($groups_list as $group_id => $group_title) {
    $xtpl->assign('GROUP_ID', $group_id);
    $xtpl->assign('GROUP_TITLE', $group_title);
    $xtpl->assign('PROMO_ENABLE', !empty($array_config_group[$group_id]['promo_enable']) ? ' checked="checked"' : '');
    $xtpl->assign('PROMO_NUMBER', !empty($array_config_group[$group_id]['promo_number']) ? $array_config_group[$group_id]['promo_number'] : '');
    $xtpl->assign('PROMO_COUPON', !empty($array_config_group[$group_id]['promo_coupon']) ? ' checked="checked"' : '');
    $xtpl->assign('PROMO_VOUCHER', !empty($array_config_group[$group_id]['promo_voucher']) ? ' checked="checked"' : '');
    $xtpl->assign('PROMO_COUPONMAX', !empty($array_config_group[$group_id]['promo_couponmax']) ? $array_config_group[$group_id]['promo_couponmax'] : '');
    $xtpl->assign('PROMO_VOUCHERMAX', !empty($array_config_group[$group_id]['promo_vouchermax']) ? $array_config_group[$group_id]['promo_vouchermax'] : '');
    $xtpl->parse('main.group');
    $xtpl->parse('main.group1');
    $xtpl->parse('main.group2');
    $xtpl->parse('main.group3');
    $xtpl->parse('main.group4');
    $xtpl->parse('main.group5');
    $xtpl->parse('main.group6');

    $xtpl->assign('GROUP_AFFILIATE', in_array($group_id, $array_config['affiliate_code_groups']) ? ' checked="checked"' : '');
    $xtpl->parse('main.group_affiliate');

    if ($group_id > 10) {
        $xtpl->assign('COLLABORATOR_CHECKED', $group_id == $array_config['group_id_collaborator'] ? ' checked="checked"' : '');
        $xtpl->parse('main.group_id_collaborator');
    }
}
$xtpl->assign('NO_COLLABORATOR_CHECKED', 0 == $array_config['group_id_collaborator'] ? ' checked="checked"' : '');

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
