<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<!-- BEGIN: error -->
<div class="alert alert-warning">{ERROR}</div>
<!-- END: error -->

<!-- BEGIN: success -->
<div class="alert alert-success">{SUCCESS}</div>
<script type="text/javascript">
   $(document).ready(function($) {
        $("input").val("");
        setTimeout(function() {
            location.href = location.href;
        }, 5000);
   });
</script>
<!-- END: success -->

<div class="addLeads">
    <div class="detail__orgainzation_heading">
        <div class="box__title">
            <h2 class="title__page title__page_organization">{LANG.leads_info}</h2>
        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
                <input type="hidden" name="id" value="{ROW.id}"/>
                <input type="hidden" name="contactid" value="{ROW.convert_contact}"/>
                <input type="hidden" name="organizationid" value="{ROW.convert_organization}"/>
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.name}</strong> <span class="red">(*)</span></label>
                        <div class="col-sm-19 col-md-18">
                            <input class="form-control" type="text" name="name" value="{ROW.name}" required="required" oninvalid="setCustomValidity(nv_required)" oninput="setCustomValidity('')" />
                        </div>
                    </div>
                    <div class="form-group <!-- BEGIN: error_phone -->has-error<!-- END: error_phone -->">
                        <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.phone}</strong> <span class="red">(*)</span><a class="order-edit-ection" href="#" data-toggle="tooltip" title="" data-original-title="Giá trị Số điện thoại hoặc email có thể nhập 1 trong 2. {LANG.info_phone}"><i class="fa fa-info-circle" aria-hidden="true"></i></a></label>
                        <div class="col-sm-19 col-md-18">
                            <input class="form-control" type="text" name="phone" value="{ROW.phone}" />
                        </div>
                    </div>
                    <div class="form-group <!-- BEGIN: error_sub_phone -->has-error<!-- END: error_sub_phone -->">
                        <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.sub_phone}</strong></label>
                        <div class="col-sm-19 col-md-18">
                            <input class="form-control" type="text" name="sub_phone" value="{ROW.sub_phone}" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.tax}</strong></label>
                        <div class="col-sm-19 col-md-18">
                            <input class="form-control" type="text" name="tax" value="{ROW.tax}" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.company_name}</strong></label>
                        <div class="col-sm-19 col-md-18">
                            <input class="form-control" type="text" name="company_name" value="{ROW.company_name}" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.affilacate_id}</strong></label>
                        <div class="col-sm-19 col-md-18">
                            <select class="form-control" name="affilacate_id">
                                <option value="0">---</option>
                                <!-- BEGIN: select_affilacate_id -->
                                <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                                <!-- END: select_affilacate_id -->
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.about}</strong>
                        </label>
                        <div class="col-sm-19 col-md-18">
                            <textarea class="form-control" name="about" rows="6">{ROW.about}</textarea>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.source_leads}</strong> <span class="red">(*)</span></label>
                        <div class="col-sm-19 col-md-18">
                            <select class="form-control" name="source_leads">
                                <option value="0" >{LANG.select_source_leads}</option>
                                <!-- BEGIN: select_source_leads -->
                                <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                                <!-- END: select_source_leads -->
                            </select>
                            <div class="input-group gr_user">
                                <input type="text" class="form-control required" value="{DATA.userid}" onchange="getData();" name="userid" id="od_userid">
                                <div class="input-group-btn">
                                    <button type="button" class="btn btn-default" data-toggle="seluser" data-area="od_userid">
                                        <i class="fa fa-search-plus" aria-hidden="true"></i> {LANG.choose}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="gr_business_id">
                            <div class="row">
                                <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.select_business}</strong> <span class="red">(*)</span></label>
                                <div class="col-sm-19 col-md-18">
                                    <select class="form-control" name="business_id" id="business_id">
                                    <option value="">---</option>
                                    <!-- BEGIN: select_business_id -->
                                    <option value="{OPTION.key}"{OPTION.selected_business_id}>{OPTION.title}</option>
                                    <!-- END: select_business_id -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="gr_solicitor_id">
                            <div class="row">
                                <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.select_solicitor}</strong> <span class="red">(*)</span></label>
                                <div class="col-sm-19 col-md-18">
                                    <select class="form-control" name="solicitor_id" id="solicitor_id">
                                    <option value="">---</option>
                                    <!-- BEGIN: select_solicitor_id -->
                                    <option value="{OPTION.key}"{OPTION.selected_solicitor_id}>{OPTION.title}</option>
                                    <!-- END: select_solicitor_id -->
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group <!-- BEGIN: error_email -->has-error<!-- END: error_email -->">
                        <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.email}</strong> <span class="red">(*)</span><a class="order-edit-ection" href="#" data-toggle="tooltip" title="" data-original-title="Giá trị Số điện thoại hoặc email có thể nhập 1 trong 2"><i class="fa fa-info-circle" aria-hidden="true"></i></a></label>
                        <div class="col-sm-19 col-md-18">
                            <input class="form-control" type="text" name="email" value="{ROW.email}" />
                        </div>
                    </div>
                    <div class="form-group <!-- BEGIN: error_sub_email -->has-error<!-- END: error_sub_email -->">
                        <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.sub_email}</strong></label>
                        <div class="col-sm-19 col-md-18">
                            <input class="form-control" type="text" name="sub_email" value="{ROW.sub_email}" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.address}</strong></label>
                        <div class="col-sm-19 col-md-18">
                            <input class="form-control" type="text" name="address" value="{ROW.address}" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.address_company}</strong></label>
                        <div class="col-sm-19 col-md-18">
                            <input class="form-control" type="text" name="address_company" value="{ROW.address_company}" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.caregiver_id}</strong></label>
                        <div class="col-sm-19 col-md-18">
                            <select class="form-control" name="caregiver_id">
                                <option value="">---</option>
                                <!-- BEGIN: select_caregiver_id -->
                                <option value="{OPTION.key}"{OPTION.selectedcaregiver_id}>{OPTION.title}</option>
                                <!-- END: select_caregiver_id -->
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-5 col-md-6 control-label" style="margin-top: 10px;"><strong>{LANG.siteid}</strong></label>
                        <div class="col-sm-19 col-md-18">
                            <select class="form-control" name="siteid" style="margin-top: 10px;">
                                <!-- BEGIN: select_siteid -->
                                <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                                <!-- END: select_siteid -->
                            </select>
                            <select class="form-control" name="prefix_lang">
                                <!-- BEGIN: select_prefix_lang -->
                                <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                                <!-- END: select_prefix_lang -->
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-group col-md-24" style="text-align: center; margin-top: 10px;">
                    <button class="btn btn-primary" name="submit" type="submit" value="{LANG.save}"><i class="fa fa-floppy-o" aria-hidden="true"></i> {LANG.save}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script type="text/javascript">
    affilacate_id = '{ROW.affilacate_id}';
    user_id = '{ROW.user_id}';
    // business_id = '{ROW.businessid}';
    // solicitor_id = '{ROW.solicitorid}';
    source_lead = {ROW.source_leads};
    var old_userId = $('[id="od_userid"]').val();
    var myVar = setInterval(function() {
        getData();
    }, 2000);
    var i = 1;
    function getData() {
        var userId = $('[id="od_userid"]').val();
        $("[name='userid']").val(userId);

        $("input[name='admin_id']").val($("select[name='admin_id']").val());
        if (userId > 0 && userId != old_userId) {
            old_userId = userId;
            i++;
            $.ajax({
                type: "POST",
                url: window.location.href,
                data: "&getuser=1&userid=" + userId,
                dataType: "json",
                success: function(data) {
                    $("[name='name']").val(data['name']);
                    $("[name='tax']").val(data['tax']);
                    $("[name='email']").val(data['email']);
                    $("[name='phone']").val(data['phone']);
                }
            });
        }
    }

    $(document).ready(function() {
        $("#business_id").select2({
            language: "vi",
            width: '100%',
            allowClear: true,
            placeholder: '{LANG.select_business}',
            ajax: {
            url: script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=add&get_bussiness=1',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q_select: params.term, // search term
                        type: 1
                    };
                },
                processResults: function (data, params) {
                    return {
                        results: data,
                        pagination: {
                            more: (params.page * 30) < data.total_count
                        }
                    };
                },
                cache: true
            },
            escapeMarkup: function (markup) { return markup; },
            minimumInputLength: 0,
            templateResult: formatRepo, // omitted for brevity, see the source of this page
            templateSelection: formatRepoSelection // omitted for brevity, see the source of this page
        });

        $("#solicitor_id").select2({
            language: "vi",
            width: '100%',
            allowClear: true,
            placeholder: '{LANG.select_solicitor}',
            ajax: {
            url: script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=add&get_bussiness=1',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q_select: params.term, // search term
                        type: 2
                    };
                },
                processResults: function (data, params) {
                    return {
                        results: data,
                        pagination: {
                            more: (params.page * 30) < data.total_count
                        }
                    };
                },
                cache: true
            },
            escapeMarkup: function (markup) { return markup; },
            minimumInputLength: 0,
            templateResult: formatRepo, // omitted for brevity, see the source of this page
            templateSelection: formatRepoSelectionS // omitted for brevity, see the source of this page
        });
        
        function formatRepo (repo) {
            if (repo.loading) {
                return "Loading....";
            }
            return repo.title;
        }

        function formatRepoSelection (repo) {
            return repo.title || "{LANG.select_business}";
        }

        function formatRepoSelectionS (repo) {
            return repo.title || "{LANG.select_solicitor}";
        }

        $("select[name='affilacate_id']").select2();
        $("select[name='caregiver_id']").select2();
        getData();
        $('.gr_user').hide();
        $('.gr_business_id').hide();
        $('.gr_solicitor_id').hide();

        if (affilacate_id == '0') {
            $('select[name="affilacate_id"]').val("0").trigger("change");
            $('select[name="affilacate_id"]').prop("disabled", true);
        }

        if (user_id != 0) {
            $('.gr_user').show();
            $("#od_userid").val(user_id);
        }
        
        if (source_lead == 2) {
            $('.gr_business_id').show();
        }

        if (source_lead == 15) {
            $('.gr_solicitor_id').show();
        }

        $('select[name="source_leads"]').on('change', function() {
            if($('select[name="source_leads"]').val() == 1) {
                $('.gr_user').show();
            } else {
                $('.gr_user').hide();
            }
            
            if($('select[name="source_leads"]').val() == 2) {
                $('.gr_business_id').show();
            } else {
                $('.gr_business_id').hide();
            }

            if($('select[name="source_leads"]').val() == 15) {
                $('.gr_solicitor_id').show();
            } else {
                $('.gr_solicitor_id').hide();
            }

            if($('select[name="source_leads"]').val() == 2 || $('select[name="source_leads"]').val() == 15 || $('select[name="source_leads"]').val() == 7 || $('select[name="source_leads"]').val() == 4) {
                $('select[name="affilacate_id"]').val("{ROW.affilacate_id}").trigger("change");
                $('select[name="affilacate_id"]').prop("disabled", false);
            } else {
                $('select[name="affilacate_id"]').val("0").trigger("change");
                $('select[name="affilacate_id"]').prop("disabled", true);
            }
        });
        // Chọn thành viên
        $('[data-toggle="seluser"]').on('click', function(e) {
            e.preventDefault();
            var url = script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=users&' + nv_fc_variable;
            url += '=getuserid&area=' + $(this).data('area');
            nv_open_browse(url, "NVImg", 850, 420, "resizable=no,scrollbars=no,toolbar=no,location=no,status=no");
        });
        $('input[name="phone"]').on('change', function() {
            $(this).val($(this).val().replace(/[^0-9\+]/gm, ''));
        });
        $('input[name="sub_phone"]').on('change', function() {
            $(this).val($(this).val().replace(/[^0-9\+\,]/gm, ''));
        });
    });
</script>
<!-- END: main -->
