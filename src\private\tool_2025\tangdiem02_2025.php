<?php

/*  https://vinades.org/dauthau/dauthau.info/-/issues/3124
 Chương trình lì xì đầu năm cho khách hàng:
Tặng 100 điểm cho toàn bộ tài khoản người dùng trên DauThau.info.
Tặng 200 điểm cho khách hàng sử dụng các gói phần mềm của DauThau.info nhưng hiện tại đã hết hạn
Tặng 300 điểm cho khách hàng đang sử dụng gói phần mềm bất kỳ của DauThau.info.

Thời gian sử dụng điểm: Trong vòng 30 ngày, từ ngày 10/02/2025 đến ngày 10/03/2025.
tặng theo tài khoản chứ ko phải theo gói phần mềm
Mỗi khách (tính theo tài kho<PERSON>n) chỉ áp dụng 1 trong 3 chư<PERSON><PERSON> trình, cho ph<PERSON><PERSON> họ nhận số điểm cao nhất có thể được nhận
*/

define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __FILE__), PATHINFO_DIRNAME) . '/..')));

require NV_CONSOLE_DIR . '/server.php';
require NV_ROOTDIR . '/includes/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

$giveexpired = mktime(23, 59, 59, 03, 10, 2025); // 10/03/2025

$last_userid = 0;
$filelog = NV_CONSOLE_DIR . '/tool_2025/tangdiem02_2025.txt';
if (file_exists($filelog)) {
    $last_userid = intval(file_get_contents($filelog));
}

$sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . " WHERE userid > " . $last_userid . " AND active = 1 ORDER BY userid ASC LIMIT 100";
$result = $db->query($sql);
$arr_tmp = [];
while ($row = $result->fetch()) {
    $arr_tmp[$row['userid']] = $row['userid'];
    $last_userid = $row['userid'];
}

if (empty($arr_tmp)) {
    echo "Đã chạy hết!!";
    exit(1);
} else {
    // loại trừ các tài khoản đã dc tặng điểm do có gói vip
    $query = $db->query("SELECT userid FROM nv4_points_users_tmp WHERE type=113 AND userid IN(" . implode(',', $arr_tmp) . ") ");
    while ($row = $query->fetch()) {
        if (isset($arr_tmp[$row['userid']])) {
            unset($arr_tmp[$row['userid']]);
        }
    }

    if (!empty($arr_tmp)) {
        foreach ($arr_tmp as $userid => $row) {
            $point = 100;
            $message_log = $message_reward_point = [];
            $message_log['vi'] = 'Hệ sinh thái Đấu thầu lì xì đầu năm';
            $message_log['en'] = 'Ecosystem Bidding lucky money at the beginning of the year';
            $message_log = json_encode($message_log);

            $message_reward_point['vi'] = '+ ' . $point . ' điểm nhân dịp đầu năm mới,';
            $message_reward_point['en'] = '+ ' . $point . ' points on the occasion of the New Year';
            $message_reward_point = json_encode($message_reward_point);

            // insert trực tiếp vào bảng nv4_points_users_tmp
            $sql = "INSERT INTO nv4_points_users_tmp (site_id, userid, pre_uid, type, status, addtime, givepoints, givemessage, givelog, giveexpired, key_crawl) VALUES (1, " . $userid . ", 0, 113, 0, " . NV_CURRENTTIME . ", " . $point . ", " . $db->quote($message_reward_point) . ", " . $db->quote($message_log) . ", " . $giveexpired . ", '')";
            $exec = $db->exec($sql);
            if ($exec) {
                echo "Userid: " . $userid . ": " . $point . " điểm \n";
                $last_userid = $userid;
            }
        }
    }
    file_put_contents($filelog, $last_userid);
    echo "Xong, last userid " . $userid . "\n";
}
