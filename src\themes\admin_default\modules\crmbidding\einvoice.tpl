<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<div class="econtract-page">
    <!-- BEGIN: allow_add -->
    <a href="#" class="btn btn-success" data-toggle="modal" data-target="#modalUploadAttachment">
        <em class="fa fa-upload"></em> {LANG.upload_new_einvoice}
    </a>
    <!-- END: allow_add -->
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-group well" style="margin-top: 15px;">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" />
        <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" />
        <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
        <div class="row">
            <div class="col-md-20">
                <div class="form-group col-md-{COL_SEARCH}">
                    <input class="form-control" type="text" value="{Q}" name="q" maxlength="255" placeholder="{LANG.einvoice_search_placeholder_key}" />
                </div>
                <div class="form-group col-md-6">
                    <select class="form-control" multiple="multiple" name="vip_id[]">
                        <!-- BEGIN: search_vip -->
                        <option value="{VIP.id}" {VIP.selected}>{VIP.title}</option>
                        <!-- END: search_vip -->
                    </select>
                </div>
                <!-- BEGIN: search_uploader -->
                <div class="form-group col-md-6">
                    <select class="form-control" multiple="multiple" name="uploader_id[]">
                        <!-- BEGIN: search_uploader_id -->
                        <option value="{UPLOADER_ID.userid}" {UPLOADER_ID.selected}>{UPLOADER_ID.title}</option>
                        <!-- END: search_uploader_id -->
                    </select>
                </div>
                <!-- END: search_uploader -->
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <input class="btn btn-primary" type="submit" value="{LANG.search_submit}" />
                    <a href="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&{NV_NAME_VARIABLE}={MODULE_NAME}&{NV_OP_VARIABLE}={OP}" class="btn btn-default">{LANG.clear_search}</a>
                </div>
            </div>
        </div>
    </form>

    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th class="w50 text-center">{LANG.stt}</th>
                    <th class="mw100 text-center">{LANG.customer_info}</th>
                    <th class="w200 text-center">{LANG.orders} <small>({LANG.order_user})</small></th>
                    <th class="w150 text-center">{LANG.vip}</th>
                    <th class="w150 text-center">{LANG.einvoice_no}</th>
                    <th class="w150 text-center">{LANG.uploader_fullname}/<br>{LANG.updated_time}</th>
                    <th class="w100 text-center text-nowrap">{LANG.action}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop_row -->
                <tr>
                    <td class="text-center">{ROW.stt}</td>
                    <td>
                        <strong>{ROW.c_name}</strong>
                        <br>
                        <small>
                            <!-- BEGIN: tax_code -->
                            <strong>{LANG.taxcode}:</strong> {ROW.tax_code}
                            <br>
                            <!-- END: tax_code -->
                            <!-- BEGIN: phone_email -->
                            <strong>{LANG.phone}:</strong> {ROW.phone} - <strong>{LANG.email}:</strong> {ROW.email}
                            <br>
                            <!-- END: phone_email -->
                            <strong>{LANG.address}:</strong> {ROW.c_address}
                        </small>
                    </td>
                    <td class="text-center">{ROW.orders}</td>
                    <td class="text-center">{ROW.vips}</td>
                    <td class="text-center">
                        <strong>{ROW.einvoice_no}</strong>
                        <br>---<br>
                        <!-- BEGIN: only_file -->
                        <a href="{ROW.url_download_einvoice}" data-toggle="tooltip" title="{LANG.click_download_attachment}" download="{ROW.einvoice_attachment}">
                            <em class="fa fa-paperclip"></em>
                            <span>{LANG.einvoice_attachment}</span>
                        </a>
                        <!-- END: only_file -->
                        <!-- BEGIN: muti_file -->
                        <!-- BEGIN: loop -->
                        <a href="{ROW.url_download_einvoice}" data-toggle="tooltip" title="{LANG.click_download_attachment}" download="{ROW.einvoice_attachment}">
                            <em class="fa fa-paperclip"></em>
                            <span>{LANG.einvoice_attachment}</span>
                        </a>
                        <!-- END: loop -->
                        <!-- END: muti_file -->
                    </td>
                    <td class="text-center">
                        {ROW.uploader}
                        <br>---<br>
                        <small><em>{ROW.updated_at}</em></small>
                    </td>
                    <td class="text-center list-actions">
                        <a href="javascript:;" onclick="showModalView('{ROW.id}');" class="btn btn-xs btn-info"><em class="fa fa-eye"></em> {LANG.view_detail}</a>
                        <!-- BEGIN: allow_edit_einvoice -->
                        <a href="javascript:;" onclick="showModalEdit('{ROW.id}');" class="btn btn-xs btn-warning"><em class="fa fa-edit"></em> {LANG.edit_einvoice_btn}</a>
                        <!-- END: allow_edit_einvoice -->
                        <!-- BEGIN: allow_del_einvoice -->
                        <a href="{ROW.del_url}" onclick="return confirm(nv_is_del_confirm[0]);" class="btn btn-xs btn-danger"><em class="fa fa-trash"></em> {LANG.del_einvoice}</a>
                        <!-- END: allow_del_einvoice -->
                    </td>
                </tr>
                <!-- END: loop_row -->
            </tbody>

            <tfoot>
                <tr>
                    <td colspan="2"><em>{LANG.total_num_einvoice}: <strong>{NUM_ITEMS}</strong></em></td>
                    <td class="text-right" colspan="5">
                        <!-- BEGIN: generate_page -->
                        {NV_GENERATE_PAGE}
                        <!-- END: generate_page -->
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>

    <!-- BEGIN: allow_add_modal -->
    <div class="modal fade" id="modalUploadAttachment" tabindex="-1" role="dialog" aria-labelledby="modalUploadAttachmentLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <form id="frmUploadAttachment" enctype="multipart/form-data">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h3 class="modal-title" id="modalUploadAttachmentLabel"><em class="fa fa-upload"></em> <strong>{LANG.upload_new_einvoice}</strong></h3>
                    </div>
                    <div class="modal-body">
                        <div class="text-warning text-center mb-2"><em>{LANG.ht_option_input_econtract_einvoice}</em></div>
                        <div class="form-group row">
                            <label class="col-md-6 text-right">{LANG.einvoice_taxcode} <span class="text-danger">**</span></label>
                            <div class="col-md-18 input-group" style="padding-left: 5px;padding-right: 5px;">
                                <input type="text" name="tax_code" id="inpTaxCode" class="form-control">
                                <span class="input-group-btn">
                                    <button class="btn btn-info btn-get-data-company" type="button" id="btnGetDataCompany_TaxCode" data-type="taxcode" data-toggle="tooltip" data-placement="top" title="{LANG.get_company_info}">
                                        <em class="fa fa-undo fa-fix">&nbsp;</em>
                                    </button>
                                </span>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-6 text-right">{LANG.customer_email} <span class="text-danger">**</span></label>
                            <div class="col-md-18 input-group" style="padding-left: 5px;padding-right: 5px;">
                                <input type="text" name="c_email" id="inpCEmail" class="form-control">
                                <span class="input-group-btn">
                                    <button class="btn btn-info btn-get-data-company" type="button" id="btnGetDataCompany_Email" data-type="email" data-toggle="tooltip" data-placement="top" title="{LANG.get_company_info}">
                                        <em class="fa fa-undo fa-fix">&nbsp;</em>
                                    </button>
                                </span>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-6 text-right">{LANG.customer_phone} <span class="text-danger">**</span></label>
                            <div class="col-md-18 input-group" style="padding-left: 5px;padding-right: 5px;">
                                <input type="text" name="c_phone" id="inpCPhone" class="form-control">
                                <span class="input-group-btn">
                                    <button class="btn btn-info btn-get-data-company" type="button" id="btnGetDataCompany_Phone" data-type="phone" data-toggle="tooltip" data-placement="top" title="{LANG.get_company_info}">
                                        <em class="fa fa-undo fa-fix">&nbsp;</em>
                                    </button>
                                </span>
                            </div>
                        </div>
                        <hr>
                        <div class="form-group row">
                            <label class="col-md-6 text-right">{LANG.customer_name} <span class="text-danger">*</span></label>
                            <div class="col-md-18">
                                <input type="text" name="c_name" id="inpCName" class="form-control">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-6 text-right">{LANG.company_address} <span class="text-danger">*</span></label>
                            <div class="col-md-18">
                                <input type="text" name="c_address" id="inpCAddress" class="form-control">
                            </div>
                        </div>
                        <h4 class="text-center"><strong>{LANG.choose_order}</strong> <span class="text-danger">*</span></h4>
                        <div class="form-group row">
                            <div class="col-md-24">
                                <div id="tblOrders" class="hide" style="max-height: 300px;overflow-y: scroll;">
                                    <table class="table table-striped table-bordered">
                                        <thead>
                                            <tr>
                                                <th class="w50"></th>
                                                <th class="text-center w100">{LANG.order_id}</th>
                                                <th class="text-center w100">{LANG.vip}</th>
                                                <th>{LANG.fee_info}</th>
                                                <th class="text-center">{LANG.edit_time}</th>
                                                <th class="text-center w180">{LANG.invoice_no_if}</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                                <p id="txtOrders" class="text-center" style="margin-bottom: 0;"><small class="text-warning"><em>{LANG.choose_order_helptext}</em></small></p>
                            </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <div class="row d-flex align-items-center">
                                <label class="col-md-6 text-right">{LANG.einvoice_no} <span class="text-danger">*</span></label>
                                <div class="col-md-18">
                                    <input type="text" name="einvoice_no" class="form-control">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-18 col-md-offset-6">
                                    <small class="text-muted">
                                        <em>{LANG.einvoice_format_guide}
                                        <br>{LANG.einvoice_short_guide}</em>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-6 text-right">{LANG.einvoice_choose_file} <span class="text-danger">*</span></label>
                            <div class="col-md-18">
                                <input type="file" name="einvoice_path" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer" style="text-align: center;">
                        <button type="button" class="btn btn-default" data-toggle="reset">{LANG.btn_reset}</button>
                        <button type="submit" class="btn btn-primary"><em class="fa fa-upload"></em> {LANG.do_upload}</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- END: allow_add_modal -->
    <div class="modal fade" id="modalEditEinvoice" tabindex="-1" role="dialog" aria-labelledby="modalEditEinvoiceLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h3 class="modal-title" id="modalEditEinvoiceLabel"><em class="fa fa-edit"></em> <strong>{LANG.edit_einvoice}: <span id="txtEditId">N/A</span></strong></h3>
                </div>
                <div class="modal-body"></div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modalViewEinvoice" tabindex="-1" role="dialog" aria-labelledby="modalViewEinvoiceLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h3 class="modal-title" id="modalViewEinvoiceLabel"><em class="fa fa-file-o"></em> <strong>{LANG.view_einvoice}: <span id="txtViewId">N/A</span></strong></h3>
                </div>
                <div class="modal-body"></div>
            </div>
        </div>
    </div>
</div>
<script>
    function getParamValue(param) {
        let url = new URL(window.location.href);
        return url.searchParams.get(param);
    }

    function addOrUpdateParam(param, value) {
        let url = new URL(window.location.href);
        url.searchParams.set(param, value); // Thêm hoặc cập nhật tham số
        window.history.replaceState({}, '', url.toString());
    }

    function removeParam(param) {
        let url = new URL(window.location.href);
        url.searchParams.delete(param); // Xóa tham số
        window.history.replaceState({}, '', url.toString());
    }

    function showModalView(id) {
        $('#modalEditEinvoice').modal('hide');
        // Gọi ajax lấy thông tin theo id để show ra view
        $.ajax({
            url: '{OP_BASE_URL}&view_detail=1&einvoice_id=' + id,
            method: 'GET',
            dataType: 'JSON',
            success: (res) => {
                removeParam('edit_id');
                addOrUpdateParam('view_id', id);
                $('#txtViewId').text('#' + id);
                if (res.status == 'success') {
                    $('#modalViewEinvoice .modal-body').html(res.html);
                } else {
                    $('#modalViewEinvoice .modal-body').html('<div class="text-center alert alert-danger">' + res.message + '</div>');
                }
                $('#modalViewEinvoice').modal('show');
            },
            error: (err) => {
                alert('{LANG.error_unknown}');
            }
        });
        return false;
    }

    function showModalEdit(id) {
        $('#modalViewEinvoice').modal('hide');
        // Gọi ajax lấy thông tin theo id để fill lên form edit
        $.ajax({
            url: '{OP_BASE_URL}&edit_form=1&einvoice_id=' + id,
            method: 'GET',
            dataType: 'JSON',
            success: (res) => {
                removeParam('view_id');
                addOrUpdateParam('edit_id', id);
                $('#txtEditId').text('#' + id);
                if (res.status == 'success') {
                    $('#modalEditEinvoice .modal-body').html(res.html);
                } else {
                    $('#modalEditEinvoice .modal-body').html('<div class="text-center alert alert-danger">' + res.message + '</div>');
                }
                $('#modalEditEinvoice').modal('show');
            },
            error: (err) => {
                alert('{LANG.error_unknown}');
            }
        });
        return false;
    }

    $(() => {
        $('select[name="vip_id[]"]').select2({
            placeholder: '{LANG.select_vip_package}',
        });
        $('select[name="uploader_id[]"]').select2({
            placeholder: '{LANG.select_uploader}',
        });

        if (getParamValue('edit_id')) {
            showModalEdit(getParamValue('edit_id'));
        }

        if (getParamValue('view_id')) {
            showModalView(getParamValue('view_id'));
        }

        $('#modalEditEinvoice').on('hidden.bs.modal', function () {
            removeParam('edit_id');
            $('#txtEditId').text('N/A');
            $('body').removeAttr("style");
        });

        $('#modalViewEinvoice').on('hidden.bs.modal', function () {
            removeParam('view_id');
            $('#txtViewId').text('N/A');
            $('body').removeAttr("style");
        });

        $('#modalUploadAttachment button[data-toggle="reset"]').on('click', (e) => {
            e.preventDefault();
            $('#frmUploadAttachment')[0].reset();
            $('#tblOrders').addClass('hide');
            $('#txtOrders').removeClass('hide');
        });

        var inProcessing = false;

        // Lấy thông tin công ty
        $('.btn-get-data-company').on('click', (e) => {
            if (!inProcessing) {
                let elm = $(e.currentTarget);
                let typeCheck = elm.data('type');
                let val2check = elm.closest('.input-group').find('input[type="text"]').val();
                if (val2check != '') {
                    inProcessing = true;
                    $('.btn-get-data-company, #inpCEmail, #inpTaxCode, #inpCPhone').attr('disabled', 'disabled');
                    $('.btn-get-data-company > em').addClass('wait');

                    $.ajax({
                        url: '{AJAX_GETCOMINFO}&val2check=' + val2check + '&typecheck=' + typeCheck,
                        method: 'GET',
                        dataType: 'JSON',
                        success: (res) => {
                            inProcessing = false;
                            $('.btn-get-data-company, #inpCEmail, #inpTaxCode, #inpCPhone').removeAttr('disabled');
                            $('.btn-get-data-company > em').removeClass('wait');
                            if (res.status == 'success') {
                                $('#inpCName').val(res.data.c_name);
                                $('#inpCAddress').val(res.data.c_address);
                                $('#inpCEmail').val(res.data.email);
                                $('#inpCPhone').val(res.data.phone);

                                // Hiện danh sách đơn hàng
                                let html_table_orders = '';
                                if (res.data.orders) {
                                    res.data.orders.forEach((order, i) => {
                                        html_table_orders += `<tr>
                                            <td class="text-center">
                                                <input type="checkbox" name="order_ids[]" value="` + order.id + `" class="order-checkbox" data-order-id="` + order.id + `" />
                                            </td>
                                            <td class="text-center">` + order.order_code + `</td>
                                            <td class="text-center vip-titles-container" data-order-id="` + order.id + `">
                                                <div class="vip-titles-list" style="display: none;">
                                                    <div class="vip-titles-container">
                                                        ` + Object.entries(order.vip_titles).map(([key, value]) => {
                                                            return `<div class="vip-title-item">
                                                                <input type="checkbox" name="vip_titles[` + order.id + `][` + order.lang + `][]" value="` + key + `" class="vip-checkbox" data-order-id="` + order.id + `" disabled />
                                                                <label>` + value + `</label>
                                                            </div>`;
                                                        }).join('') + `
                                                    </div>
                                                </div>
                                                <span class="vip-titles-display">` + Object.values(order.vip_titles).join(', ') + `</span>
                                            </td>
                                            <td>
                                                <ul>
                                                    <li>{LANG.total_price}: <strong>` + order.money + `</strong></li>
                                                    <li>{LANG.duoc_giam}: <strong>` + order.discount + `</strong></li>
                                                    <li>{LANG.total_payment}: <strong>` + order.total + `</strong></li>
                                                    <li>{LANG.chiet_khau_rieng}: <strong>` + order.price_reduce + `</strong></li>
                                                    <li>{LANG.total_real_receive}: <strong>` + order.total_end + `</strong></li>
                                                </ul>
                                            </td>
                                            <td class="text-center">` + order.edit_time + `</td>
                                            <td class="text-center">` + (order.invoice_number != '' ? order.invoice_number : ``) + `</td>
                                        </tr>`;
                                    });
                                    $('#tblOrders tbody').html(html_table_orders);
                                    $('#tblOrders').removeClass('hide');
                                    $('#txtOrders').addClass('hide');
                                }
                            } else {
                                alert(res.message);
                                $('#inpCName').val('');
                                $('#inpCAddress').val('');
                                $('#inpCEmail').val('');
                                $('#inpCPhone').val('');
                                $('#tblOrders tbody').html('');
                                $('#tblOrders').addClass('hide');
                                $('#txtOrders').removeClass('hide');
                            }
                        },
                        error: (err) => {
                            inProcessing = false;
                            $('.btn-get-data-company, #inpCEmail, #inpTaxCode, #inpCPhone').removeAttr('disabled');
                            $('.btn-get-data-company > em').removeClass('wait');
                            alert('{LANG.error_unknown}');
                        }
                    });
                } else {
                    alert('{LANG.error_empty_taxcode}');
                }
            }
            return false;
        });

        // Bắt đầu tải form thông tin hợp đồng lên
        $('#frmUploadAttachment').on('submit', () => {
            if (!inProcessing) {
                // Kiểm tra form trước khi submit
                let form = $('#frmUploadAttachment')[0];
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return false;
                }

                inProcessing = true;
                let frmData = new FormData(form);
                $.ajax({
                    url: '{AJAX_SUBMIT_UPLOAD}',
                    method: 'POST',
                    data: frmData,
                    processData: false,
                    contentType: false,
                    success: (res) => {
                        inProcessing = false;
                        if (res.status == 'success') {
                            $('#frmUploadAttachment')[0].reset();
                            $('#tblOrders tbody').html('');
                            $('#tblOrders').addClass('hide');
                            $('#txtOrders').removeClass('hide');
                            alert(res.message);
                            window.location.reload();
                        } else if (res.status == 'error') {
                            alert(res.message);
                        }
                    },
                    error: (err) => {
                        inProcessing = false;
                        console.error('AJAX Error:', err);
                        console.error('Status:', err.status);
                        console.error('Status Text:', err.statusText);
                        console.error('Response Text:', err.responseText);
                        alert('{LANG.error_unknown_zip}');
                    }
                });
            }
            return false;
        });

        // Bắt đầu tải form bổ sung hóa đơn điện tử
        $('#frmUploadEinvoice').on('submit', () => {
            if (!inProcessing) {
                inProcessing = true;
                let frmData = new FormData($('#frmUploadEinvoice')[0]);
                $.ajax({
                    url: '{AJAX_SUBMIT_UPLOAD_EINVOICE}',
                    method: 'POST',
                    data: frmData,
                    processData: false,
                    contentType: false,
                    success: (res) => {
                        inProcessing = false;
                        if (res.status == 'success') {
                            alert(res.message);
                            window.location.reload();

                        } else if (res.status == 'error') {
                            alert(res.message);
                        }
                    },
                    error: (err) => {
                        inProcessing = false;
                        alert('{LANG.error_unknown_zip}');
                    }
                });
            }
            return false;
        });

        // Xử lý hiển thị/ẩn checkbox VIP titles khi order được chọn
        $(document).on('change', '.order-checkbox', function() {
            const orderId = $(this).data('order-id');
            const container = $('.vip-titles-container[data-order-id="' + orderId + '"]');
            const vipTitlesList = container.find('.vip-titles-list');
            const vipTitlesDisplay = container.find('.vip-titles-display');

            if ($(this).is(':checked')) {
                // Hiện VIP titles của order được chọn
                vipTitlesList.show();
                vipTitlesDisplay.hide();
                container.find('.vip-checkbox').prop('disabled', false);
            } else {
                // Ẩn VIP titles của order bị bỏ chọn
                vipTitlesList.hide();
                vipTitlesDisplay.show();
                // Uncheck tất cả các checkbox VIP titles của order này
                vipTitlesList.find('input[type="checkbox"]').prop('checked', false);
                container.find('.vip-checkbox').prop('disabled', true);
            }
        });
    });
</script>
<!-- END: main -->

<!-- BEGIN: view_detail -->
<div class="row">
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <colgroup>
                <col class="w150">
                <col>
            </colgroup>
            <!-- BEGIN: tax_code -->
            <tr>
                <td><strong>{LANG.einvoice_taxcode}</strong></td>
                <td>{VIEW_EINVOICE.tax_code}</td>
            </tr>
            <!-- END: tax_code -->
            <!-- BEGIN: email -->
            <tr>
                <td><strong>{LANG.customer_email}</strong></td>
                <td>{VIEW_EINVOICE.email}</td>
            </tr>
            <!-- END: email -->
            <!-- BEGIN: phone -->
            <tr>
                <td><strong>{LANG.customer_phone}</strong></td>
                <td>{VIEW_EINVOICE.phone}</td>
            </tr>
            <!-- END: phone -->
            <tr>
                <td><strong>{LANG.customer_name}</strong></td>
                <td>{VIEW_EINVOICE.c_name}</td>
            </tr>
            <tr>
                <td><strong>{LANG.company_address}</strong></td>
                <td>{VIEW_EINVOICE.c_address}</td>
            </tr>
            <tr>
                <td><strong>{LANG.einvoice_no}</strong></td>
                <td>{VIEW_EINVOICE.einvoice_no} &nbsp;&nbsp;<a href="{VIEW_EINVOICE.einvoice_link}" class="label label-xs label-info" target="_blank">{LANG.view} <em class="fa fa-external-link"></em></a></td>
            </tr>
            <tr>
                <td><strong>{LANG.uploader_fullname}</strong></td>
                <td>
                    <a href="{VIEW_EINVOICE.uploader_link}">{VIEW_EINVOICE.uploader}</a>
                </td>
            </tr>
            <tr>
                <td><strong>{LANG.updated_time}</strong></td>
                <td>{VIEW_EINVOICE.updated_at}</td>
            </tr>
        </table>
    </div>
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th class="text-center" colspan="4"><strong>{LANG.list_orders}</strong></th>
                </tr>
                <tr>
                    <th class="text-center w150">{LANG.order_id}</th>
                    <th class="text-center">{LANG.vip}</th>
                    <th class="text-center w150">{LANG.order_user}</th>
                    <th class="text-center w100"></th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop_order -->
                <tr>
                    <td class="text-center">{ORDER_ROW.order_code}</td>
                    <td class="text-center">{ORDER_ROW.vips}</td>
                    <td class="text-center">{ORDER_ROW.username}</td>
                    <td class="text-center">
                        <a href="{ORDER_ROW.link}" class="btn btn-xs btn-info" target="_blank">{LANG.view} <em class="fa fa-external-link"></em></a>
                    </td>
                </tr>
                <!-- END: loop_order -->
            </tbody>
        </table>
    </div>
</div>
<!-- BEGIN: show_btn_edit -->
<hr>
<div class="text-center">
    <a href="javascript:;" onclick="showModalEdit('{VIEW_EINVOICE.id}');" class="btn btn-warning"><em class="fa fa-edit"></em> {LANG.edit}</a>
</div>
<!-- END: show_btn_edit -->
<!-- END: view_detail -->

<!-- BEGIN: edit_detail -->
<form id="frmEditEinvoice" enctype="multipart/form-data">
    <input type="hidden" name="einvoice_id" value="{EDIT_EINVOICE.id}">
    <input type="hidden" value="{EDIT_EINVOICE.tax_code}" name="tax_code">
    <input type="hidden" value="{EDIT_EINVOICE.phone}" name="phone">
    <input type="hidden" value="{EDIT_EINVOICE.email}" name="email">
    <div class="form-group row">
        <label class="col-md-6 text-right">{LANG.company_name} <span class="text-danger">*</span></label>
        <div class="col-md-18">
            <input type="text" value="{EDIT_EINVOICE.c_name}" name="c_name" id="inpCName" class="form-control">
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-6 text-right">{LANG.company_address} <span class="text-danger">*</span></label>
        <div class="col-md-18">
            <input type="text" value="{EDIT_EINVOICE.c_address}" name="c_address" id="inpCAddress" class="form-control">
        </div>
    </div>
    <h4 class="text-center"><strong>{LANG.choose_order}</strong> <span class="text-danger">*</span></h4>
    <div class="form-group row">
        <div class="col-md-24">
            <div id="tblOrders" style="max-height: 300px;overflow-y: scroll;">
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th class="w50"></th>
                            <th class="text-center w100">{LANG.order_id}</th>
                            <th class="text-center w100">{LANG.vip}</th>
                            <th>{LANG.fee_info}</th>
                            <th class="text-center">{LANG.edit_time}</th>
                            <th class="text-center">{LANG.invoice_no_if}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- BEGIN: order_loop -->
                        <tr id="order-{CUSTOMER_ORDER.id}">
                            <td class="text-center">
                                <input type="checkbox" name="order_ids[]" value="{CUSTOMER_ORDER.id}" {CUSTOMER_ORDER.check} class="order-checkbox" data-order-id="{CUSTOMER_ORDER.id}" />
                            </td>
                            <td class="text-center">{CUSTOMER_ORDER.order_code}</td>
                            <td class="text-center">
                                <!-- BEGIN: loop_vip -->
                                <div class="vip-title-item">
                                    <input type="checkbox" name="vip_titles[{CUSTOMER_ORDER_VIP.order_id}][{CUSTOMER_ORDER.lang}][]" value="{CUSTOMER_ORDER_VIP.key}" {CUSTOMER_ORDER_VIP.is_checked} class="vip-checkbox" data-order-id="{CUSTOMER_ORDER_VIP.order_id}" {CUSTOMER_ORDER_VIP.is_disabled} />
                                    <label>{CUSTOMER_ORDER_VIP.title}</label>
                                </div>
                                <!-- END: loop_vip -->
                            </td>
                            <td>
                                <ul>
                                    <li>{LANG.total_price}: <strong>{CUSTOMER_ORDER.money}</strong></li>
                                    <li>{LANG.duoc_giam}: <strong>{CUSTOMER_ORDER.discount}</strong></li>
                                    <li>{LANG.total_payment}: <strong>{CUSTOMER_ORDER.total}</strong></li>
                                    <li>{LANG.chiet_khau_rieng}: <strong>{CUSTOMER_ORDER.price_reduce}</strong></li>
                                    <li>{LANG.total_real_receive}: <strong>{CUSTOMER_ORDER.total_end}</strong></li>
                                </ul>
                            </td>
                            <td class="text-center">{CUSTOMER_ORDER.edit_time}</td>
                            <td class="text-center">{CUSTOMER_ORDER.invoice_number}</td>
                        </tr>
                        <!-- END: order_loop -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <hr>
    <div class="form-group">
        <div class="row d-flex align-items-center">
            <label class="col-md-6 text-right">{LANG.einvoice_no} <span class="text-danger">*</span></label>
            <div class="col-md-18">
                <input type="text" value="{EDIT_EINVOICE.einvoice_no}" name="einvoice_no" class="form-control">
            </div>
        </div>
        <div class="row">
            <div class="col-md-18 col-md-offset-6">
                <small class="text-muted">
                    <em>{LANG.einvoice_format_guide}
                    <br>{LANG.einvoice_short_guide}</em>
                </small>
            </div>
        </div>
    </div>
    <div class="form-group row">
        <label class="col-md-6 text-right">
            {LANG.einvoice_choose_file}
            <br><small class="text-info"><em>({LANG.if_not_update})</em></small>
        </label>
        <div class="col-md-18">
            <a class="btn btn-sm btn-info" href="{EDIT_EINVOICE.url_download_einvoice}" data-toggle="tooltip" title="{LANG.click_download_attachment}" download="{EDIT_EINVOICE.einvoice_attachment}">
                <em class="fa fa-file-zip-o"></em>
                <span><em>{LANG.uploaded_name}:</em> <strong>{EDIT_EINVOICE.einvoice_attachment}</strong></span>
            </a>
            <input type="file" name="einvoice_path" class="form-control">
        </div>
    </div>
    <div class="modal-footer" style="text-align: center;">
        <button type="submit" class="btn btn-primary"><em class="fa fa-save"></em> {LANG.save}</button>
    </div>
</form>

<script>
    $(() => {
        var inProcessing = false;

        $('[data-toggle="tooltip"]').tooltip();
        // Bắt đầu tải form thông tin hợp đồng lên
        $('#frmEditEinvoice').on('submit', (e) => {
            e.preventDefault();
            if (!inProcessing) {
                $('#frmEditEinvoice button[type="submit"]').text('{LANG.in_progress}').attr('disabled', 'disabled');
                inProcessing = true;
                let frmData = new FormData($('#frmEditEinvoice')[0]);
                $.ajax({
                    url: '{AJAX_SUBMIT_EDIT}',
                    method: 'POST',
                    data: frmData,
                    processData: false,
                    contentType: false,
                    success: (res) => {
                        $('#frmEditEinvoice button[type="submit"]').html('<em class="fa fa-save"></em> {LANG.save}').removeAttr('disabled');
                        inProcessing = false;
                        alert(res.message);
                        if (res.status == 'success') {
                            window.location.href = '{OP_BASE_URL}';
                        }
                    },
                    error: (err) => {
                        inProcessing = false;
                        console.error('AJAX Error:', err);
                        console.error('Status:', err.status);
                        console.error('Status Text:', err.statusText);
                        console.error('Response Text:', err.responseText);
                        $('#frmEditEinvoice button[type="submit"]').html('<em class="fa fa-save"></em> {LANG.save}').removeAttr('disabled');
                        alert('{LANG.error_unknown_zip}');
                    }
                });
            }
            return false;
        });

        // Xử lý khi checkbox đơn hàng thay đổi
        $('.order-checkbox').on('change', function() {
            const orderId = $(this).data('order-id');
            const vipCheckboxes = $('.vip-checkbox[data-order-id="' + orderId + '"]');
            if ($(this).is(':checked')) {
                vipCheckboxes.prop('disabled', false);
            } else {
                vipCheckboxes.prop('disabled', true);
                vipCheckboxes.prop('checked', false);
            }
        });

        // Thiết lập trạng thái ban đầu
        $('.order-checkbox').each(function() {
            const orderId = $(this).data('order-id');
            const vipCheckboxes = $('.vip-checkbox[data-order-id="' + orderId + '"]');
            if ($(this).is(':checked') && !$(this).is(':disabled')) {
                vipCheckboxes.prop('disabled', false);
            } else {
                vipCheckboxes.prop('disabled', true);
            }
        });
    });
</script>
<!-- END: edit_detail -->
