#bieudo, .transaction_sales {
    box-shadow: 0px 0px 10px 0px #ccc;
    padding: 16px;
    margin-bottom: 10px;
}

.title_static {
	padding: 21px;
    background: #2196f3;
    color: #fff;
    border-radius: 2px;
}

.title_static + .title_static {
	margin-left: 15px;
}

/* Chart layout */
.chart-layout {
    margin: 36px auto 0 auto;
    padding: 32px 0;
    height: 320px;
    background-color: #fff;
    display: flex;
    justify-content: space-around;
    /* flex-wrap: wrap-reverse; */
    align-items: flex-end;
    margin-top: 0;
}

.chart-layout__item {
    position: relative;
    width: 75px;
    color: #fff;
    text-align: center;
    height: var(--percent);
    background-color: var(--bg);
    box-shadow: 9px 7px 5px -3px #d4d4d4e0;
    animation: growth_char ease-in 0.5s;
    display: flex;
    flex-direction: column;
}

.chart-layout__point {
	height: var(--percent_point);
}

@keyframes growth_char {
    from {
        opacity: 0;
        height: calc(var(--percent) - 50%);
    }
    to {
        opacity: 1;
        height: var(--percent);
    }
}

.chart-layout__item .point {
	position: absolute;
    top: -20px;
    left: 0;
    width: 100%;
    color: #3f51b5;
    font-weight: 600;
    font-size: 17px;
}

.customer_point {
    position: absolute;
    display: block;
    top: 50%;
    width: 100%;
    transform: translateY(-50%);
}

.lb_point {
	font-size: 12px;
}

.title_point_top  {
	color: #486d49;
    font-size: 22px;
    border-left: 2px solid;
    padding-left: 10px;
}

.point__link {
    cursor: pointer;
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes growth {
    from {
        transform: scale(var(--growth-from));
    }

    to {
        transform: scale(var(--growth-to));
    }
}

#myModal {
    animation: fadeIn linear 0.2s !important; 
}

.modal-dialog {
    --growth-from:  0.5;
    --growth-to:  1;
    animation: growth linear 0.2s !important; 
    /* top: 20%; */
    /* transform: translateY(-20%) !important;  */
}

.modal-content {
    box-shadow: none !important;
    border-radius: 2px !important;
    border: none !important;
    background: #fff;
    position: relative;
}

#myModal .modal-header {
    background: linear-gradient(
    45deg
    , #2e60ab, #f5f5f500);
        color: #fff;
}

#myModal .myModal-title{
    font-size: 16px;
    font-weight: bold;
}

#myModal .modal-body {
    position: absolute;
    background: #fff;
    width: 100%;
    overflow: auto;
    max-height: 500px;
}

#myModal .modal-body::-webkit-scrollbar-track {
  -webkit-box-shadow:inset 0 0 6px rgba(0,0,0,.3);
  background-color:#f5f5f5;
}

#myModal .modal-body::-webkit-scrollbar {
  width:6px;
  background-color:#f5f5f5;
}

#myModal .modal-body::-webkit-scrollbar-thumb {
  background-color:#f79633;
}

.search {
    margin-bottom: 16px;
}

.btn__search {
    margin-top: 22px;
}

/* fomat time css */
._jw-tpk-container {
    height: auto !important;
}

._jw-tpk-header {
    background: #0a78d0 !important;
    padding: 4px;
    font-size: 12px;
}

._jw-tpk-container ol>li>a {
    font-size: 13px !important;
    padding: 10px 0 !important;
}

.text-red {
    font-style: italic;
    color: #d83737;
}

input[type="file"]:focus, input[type="radio"]:focus, input[type="checkbox"]:focus {
    outline: none;
}

.nodata {
    color: #ff5722;
    font-size: 19px;
}

.convert__point, .convert__money {
    margin: auto;
    display: block;
    height: 30px;
    line-height: 30px;
}

.convert__point span {
    font-weight: bold;
}

.note_point {
    color: rebeccapurple;
    display: block;
    line-height: 40px;
}

.note__red {
    color: #d40909;
}

.stt_point {
    padding: 6px 0px;
    background: orange;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 15px;
    color: #fff;
}

span.point {
    font-size: 16px;
    display: inline-table;
}

.main__boxPoint {
    position: relative;
    overflow-x: hidden;
    max-height: 400px;
}


.main__boxPoint::-webkit-scrollbar-track {
  -webkit-box-shadow:inset 0 0 6px rgba(0,0,0,.3);
  background-color:#f5f5f5;
}

.main__boxPoint::-webkit-scrollbar {
  width:3px;
  background-color:#f5f5f5;
}

.main__boxPoint::-webkit-scrollbar-thumb {
  background-color:#e91e63;
}

.row__box_logPoint {
    display: flex;
    border-bottom: 1px dotted #ccc;
    padding: 16px 10px;
    border-left: 2px solid #82589f91;
    margin-bottom: 20px;
}

.row__box-left {
    display: flex;
    align-items: center;
}

.row__box-left .stt {
    padding: 10px;
    background: #00b894;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
}

.row__box-right {
    flex: 1;
    padding: 0 16px;
    display: flex;
    justify-content: space-evenly;
}

.row__box_point, .row__box_money {
    flex: 1;
    flex-basis: 46%;
}

.font-weight {
    font-weight: bold;
}

.point_in {
    background: #0984e3;
    padding: 5px;
    border-radius: 2px;
    color: #fff;
    outline-offset: 0px;
    box-shadow: 0 2px 6px rgb(0 0 0 / 20%);
    border: 1px solid;
}

.row_total {
    position: relative;
    display: flex;
    justify-content: space-evenly;
    margin-bottom: 20px;
}

.row_total_full .box_total {
    flex: 1;
}

.box_total {
    display: inline-block;
    background: #05c46b;
    padding: 10px;
    text-align: center;
    color: #fff;
    font-size: 14px;
    box-shadow: 0px 3px 5px rgb(0 0 0 / 20%);
    border: 1px solid;
    flex-basis: 48%;
}

.box_total .box_total-title {
    border-bottom: 1px solid #fff;
    padding-bottom: 5px;
    min-height: 54px;
    text-align: center;
    margin-bottom: 16px;
    padding: 8px 0px;
}

.box_total span {
    font-weight: bold; 
    font-size: 24px;
}

.tt_sale {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.bg--all {
    background: #feca57;
}

.box--blue {
    background: #73a7dc;
}

.row__box-serach {
    margin-bottom: 8px;
}

.always-wrap {
    white-space: normal!important;
}

.txt_total {
    font-size: 14px;
}

.mt-22 {
    margin-top: 22px;
}
