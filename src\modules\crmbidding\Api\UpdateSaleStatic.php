<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdateSaleStatic implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'salestatic';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $error = '';
        $error_code = '0000';
        $userid = $nv_Request->get_int('userid', 'post', 0);
        $date = $nv_Request->get_int('date', 'post', 0);
        $admin_id = $nv_Request->get_int('admin_id', 'post', '0');

        if ($date <= 0) {
            return $this->result->setError()
                ->setCode('2012')
                ->setMessage('date invalid')
                ->getResult();
        }

        if ($userid > 0) {
            $row_old = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_sale_static WHERE userid=' . $userid . ' AND date=' . $date . ' LIMIT 1')->fetch();
            if (empty($row_old)) {
                return $this->result->setError()
                    ->setCode('2004')
                    ->setMessage('sale static not exit')
                    ->getResult();
            }

            $data = $nv_Request->get_array('data', 'post');
            if (!is_array($data)) {
                return $this->result->setError()
                    ->setCode('2000')
                    ->setMessage('Param data invalid to json')
                    ->getResult();
            }
            $row = $data;

            $keys_check = [
                'num_leads',
                'num_opportunities',
                'num_order',
                'num_vip',
                'money',
                'discount',
                'total',
                'price_reduce',
                'total_end',
                'recharge_day',
                'total_day',
                'bonus',
                'money_point_num',
                'money_point',
                'money_point_bonus'
            ];
            if (!empty($data)) {
                // check $field
                $array_sql = [];
                foreach ($data as $key => $value) {
                    if (!in_array($key, $keys_check)) {
                        return $this->result->setError()
                            ->setCode('2002')
                            ->setMessage('Missing field ' . $key . ' in otherdata')
                            ->getResult();
                    }
                    $array_sql[$key] = $key . '=:' . $key;
                }

                if (empty($error)) {
                    try {
                        $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_sale_static SET ' . implode(',', $array_sql) . ' WHERE userid=' . $userid . ' AND date=' . $date);

                        foreach ($row as $key => $value) {
                            $stmt->bindValue(':' . $key, $value, PDO::PARAM_STR);
                        }
                        $exc = $stmt->execute();
                        if ($exc) {
                            // ghi log
                            $log_data = [
                                $nv_Lang->getModule('log_update_sale_static')
                            ];

                            foreach ($row as $key => $value) {
                                if ($row_old[$key] != $row[$key]) {
                                    $log_data[] = [
                                        $nv_Lang->getModule($key),
                                        $row_old[$key] . ' =&gt; ' . $row[$key]
                                    ];
                                }
                            }

                            if (sizeof($log_data) > 1) { // không có gì thay đổi thì k ghi log
                                $sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (userid, log_area, log_key, log_time, log_data) VALUES (" . $admin_id . ", 1, 'LOG_ADMIN_UPDATE_SALE_STACTIC', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ")";
                                $db->query($sql);
                            }
                            $nv_Cache->delMod($module_name);
                            nv_insert_logs(NV_LANG_DATA, $module_name, 'Edit sale static', 'UserID: ' . $userid . ' date: ' . $date, $admin_id);
                            $this->result->setSuccess();
                            $this->result->set('user_update', [
                                'userid' => $userid,
                                'date' => $date
                            ]);
                        }
                    } catch (PDOException $e) {
                        $this->result->setError()
                            ->setCode('3000')
                            ->setMessage(print_r($e, true));
                    }
                } else {
                    return $this->result->setError()
                        ->setCode($error_code)
                        ->setMessage($error)
                        ->getResult();
                }
            } else {
                return $this->result->setError()
                    ->setCode('2001')
                    ->setMessage('Param data empty')
                    ->getResult();
            }
        } else {
            return $this->result->setError()
                ->setCode('2003')
                ->setMessage('userid is integer, more than 0 (' . $userid . ')')
                ->getResult();
        }
        return $this->result->getResult();
    }
}
