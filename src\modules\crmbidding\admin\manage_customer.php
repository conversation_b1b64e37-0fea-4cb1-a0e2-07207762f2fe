<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
use NukeViet\Api\DoApi;
$page_title = $nv_Lang->getModule('manage_customer');

$_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE userid = ' . $admin_info['userid'] . ' ORDER BY userid ASC';
$_query = $db->query($_sql);
$admin_crm_config = $caregiver_id_leads = [];
$is_leader = 0;
while ($_row = $_query->fetch()) {
    $admin_crm_config = json_decode($_row['config'], true);
    if ($_row['is_leader'] == 1) {
        $is_leader = 1;
        $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $_row['group_id'] . ' AND userid != ' . $admin_info['userid'];
        $_result = $db->query($_sql);
        while ($_row_groups_users = $_result->fetch()) {
            $caregiver_id_leads[$_row_groups_users['userid']] = $_row_groups_users['userid'];
        }
    }
}
$caregiver_id_leads[$admin_info['userid']] = $admin_info['userid'];

$array_search = [];
$array_search['q'] = $nv_Request->get_title('q', 'post,get');
$array_search['s_vip'] = $nv_Request->get_int('s_vip', 'get', 0);
$array_search['id_sale'] = $nv_Request->get_int('s_admin', 'get', 0);
$array_search['s_point'] = $nv_Request->get_int('s_point', 'get', 0);
$array_search['s_wallet'] = $nv_Request->get_int('s_wallet', 'get', 0);
$array_search['s_money_wallet'] = $nv_Request->get_int('s_money_wallet', 'get', 0);

$per_page = 20;
$page = $nv_Request->get_int('page', 'post,get', 1);
$arr_where = [];
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;

if (defined('NV_IS_SPADMIN') or (isset($admin_crm_config['show_chart']) and $admin_crm_config['show_chart'] == 1)) {
    // điều hành chung, mkt/kế toán được cấp quyền xem thống kê dc xem toàn bộ, hoặc xem theo bộ lọc người chăm sóc,
    if ($array_search['id_sale'] > 0) {
        $arr_where[] = 't2.saleid =' . $array_search['id_sale'];
    }
} else if ($is_leader and !empty($caregiver_id_leads)) {
    // tk sale trưởng nhóm
    if ($array_search['id_sale'] > 0) {
        // xem theo tìm kiếm thành viên nhóm
        $arr_where[] = 't2.saleid =' . $array_search['id_sale'];
    } else { // xem toàn bộ nhóm mình
        $arr_where[] = 't2.saleid IN (' . implode(',', $caregiver_id_leads) . ')';
    }
} else { // sale thường chỉ xem lead của mình
    $arr_where[] = 't2.saleid =' . $admin_info['userid'];
}

if ($array_search['id_sale'] > 0) {
    $base_url .= '&s_admin=' . $array_search['id_sale'];
}

if ($array_search['s_vip'] == 200) {
    $arr_where[] = "NOT FIND_IN_SET(99, t2.vip)";
} else if ($array_search['s_vip'] > 0) {
    $arr_where[] = 'FIND_IN_SET(' . $array_search['s_vip'] . ', t2.vip)';
} else if ($array_search['s_vip'] == -1) {
    $arr_where[] = "t2.vip = ''";
} else if ($array_search['s_vip'] == -2) {
    $arr_where[] = "t2.vip != ''";
}

if ($array_search['s_vip'] != 0) {
    $base_url .= '&s_vip=' . $array_search['s_vip'];
}

if (!empty($array_search['q'])) {
    $_q = $db->dblikeescape($array_search['q']);
    $arr_where[] = 't1.email LIKE ' . $db->quote('%' . $_q . '%') . ' OR t1.first_name LIKE ' . $db->quote('%' . $_q . '%') . ' OR t1.last_name LIKE ' . $db->quote('%' . $_q . '%') . ' OR t1.username LIKE ' . $db->quote('%' . $_q . '%') . ' OR t2.phone LIKE ' . $db->quote('%' . $_q . '%') . ' OR t2.mst LIKE ' . $db->quote('%' . $_q . '%');
    $base_url .= '&q=' . $array_search['q'];
}

if ($array_search['s_point'] > 0) {
    $arr_where[] = 't2.used_point=' . $array_search['s_point'];
    $base_url .= '&amp;s_point=' . $array_search['s_point'];
}

if ($array_search['s_wallet'] > 0) {
    if ($array_search['s_wallet'] == 1) {
        $arr_where[] = 't2.charge_wallet > 0';
    } else {
        $arr_where[] = 't2.charge_wallet = 0';
    }
    $base_url .= '&amp;s_wallet=' . $array_search['s_wallet'];
}
if ($array_search['s_money_wallet'] > 0) {
    if ($array_search['s_money_wallet'] == 1) {
        $arr_where[] = 't2.money_wallet = 0';
    } else {
        $arr_where[] = 't2.money_wallet > 0';
    }
    $base_url .= '&amp;s_money_wallet=' . $array_search['s_money_wallet'];
}

$db->sqlreset()
    ->select('COUNT(*)')
    ->from(NV_USERS_GLOBALTABLE . ' as t1')
    ->join('INNER JOIN ' . NV_USERS_GLOBALTABLE . '_info as t2 ON t1.userid=t2.userid');

if (!empty($arr_where)) {
    $db->where(implode(' AND ', $arr_where));
}

$sth = $db->prepare($db->sql());
$sth->execute();
$num_items = $sth->fetchColumn();

$db->select('*')
    ->order('t1.userid DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$sth = $db->prepare($db->sql());
$sth->execute();

$tpl = new \NukeViet\Template\NVSmarty();
$tpl->setTemplateDir(get_module_tpl_dir('manage_customer.tpl'));
$tpl->assign('LANG', $nv_Lang);
$tpl->assign('MODULE_NAME', $module_name);
$tpl->assign('MODULE_DATA', $module_data);
$tpl->assign('OP', $op);
$tpl->assign('ARRAY_SEARCH', $array_search);

$global_arr_vip[-1] = $nv_Lang->getModule('vip_none_1');
$global_arr_vip[-2] = $nv_Lang->getModule('vip_none_2');
$tpl->assign('GLOBAL_ARR_VIP', $global_arr_vip);

$loop_admin = [];
foreach ($array_user_id_users as $key => $value) {
    $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
    if (defined('NV_IS_SPADMIN') or (isset($admin_crm_config['show_chart']) and $admin_crm_config['show_chart'] == 1) or isset($caregiver_id_leads[$value['userid']])) {
        $loop_admin[] = '<option value="' . $value['userid'] . '" ' . ($id_sale == $value['userid'] ? ' selected="selected"' : '') . '>' . $fullname . '</option>';
    }
}
if (!empty($loop_admin)) {
    $tpl->assign('LOOP_ADMIN', implode('', $loop_admin));
}

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
$tpl->assign('GENERATE_PAGE', $generate_page);

$arr_data = [];
$number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;
while ($view = $sth->fetch()) {
    $view['number'] = $number++;
    $view['fullname'] = nv_show_name_user($view['first_name'], $view['last_name'], $view['username']);
    $view['last_login'] = nv_date('d/m/Y', $view['last_login']);
    $view['regdate'] = nv_date('d/m/Y', $view['regdate']);
    $view['money_total'] = number_format($view['money_wallet']);
    $view['money_total_nap'] = number_format($view['charge_wallet']);
    $view['point_customer'] = number_format($view['point_total']);
    $view['point_in'] = number_format($view['point_in']);

    $view['num_vip'] = $view['vip'] != '' ? sizeof(explode(',', $view['vip'])) : 0;
    $view['revenue'] = number_format($view['revenue']);

    $view['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=detail_customer_new&user_id=' . $view['userid'];
    $view['linkorder'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding' . '&amp;' . NV_OP_VARIABLE . '=payment&amp;' . 'userid=' . $view['userid'];
    $value['linkvip'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding' . '&amp;' . NV_OP_VARIABLE . '=customs&amp;q=' . $view['username'] . '&s_vip=0&s_admin=-1&s_status=-1&e_status=-1';
    $value['editUser'] = URL_DTINFO_ADMIN . 'index.php?' . NV_NAME_VARIABLE . '=users&amp;value=' . $view['username'] . '&amp;method=username';

    $arr_data[] = $view;
}

$tpl->assign('ARR_DATA', $arr_data);

$contents = $tpl->fetch('manage_customer.tpl');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
