<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/includes/../modules" isTestSource="false" packagePrefix="NukeViet\Module\" />
      <sourceFolder url="file://$MODULE_DIR$/src/includes/api" isTestSource="false" packagePrefix="NukeViet\Api\" />
      <sourceFolder url="file://$MODULE_DIR$/src/includes/uapi" isTestSource="false" packagePrefix="NukeViet\Uapi\" />
      <sourceFolder url="file://$MODULE_DIR$/src/includes/vendor/vinades/nukeviet" isTestSource="false" packagePrefix="NukeViet\" />
      <sourceFolder url="file://$MODULE_DIR$/src/vendor/paypal" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/tests" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/league/oauth2-client" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/paragonie/random_compat" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/phpmailer/phpmailer" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/smarty/smarty" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/symfony/polyfill-php72" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/tecnickcom/tc-lib-barcode" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/tecnickcom/tc-lib-color" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/vinades/nukeviet" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/vinades/pclzip" />
      <excludeFolder url="file://$MODULE_DIR$/src/includes/vendor/zaloplatform/zalo-php-sdk" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/aws/aws-crt-php" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/aws/aws-sdk-php" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/aws/aws-sdk-php-resources" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/desarrolla2/cache" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/elastic/transport" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/elasticsearch/elasticsearch" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/ezyang/htmlpurifier" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/jasny/sso" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/jasny/validation-result" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/johngrogg/ics-parser" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/maennchen/zipstream-php" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/markbaker/complex" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/markbaker/matrix" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/mtdowling/jmespath.php" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/paypal/PayPal" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/php-http/discovery" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/php-http/httplug" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/php-http/promise" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/phpoffice/phpspreadsheet" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/src/vendor/symfony/polyfill-mbstring" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>