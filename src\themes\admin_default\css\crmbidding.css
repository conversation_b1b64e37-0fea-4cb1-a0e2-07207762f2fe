/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tu<PERSON>. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */

:root {
    --color__label: #62899a;
    --color__success: #40bb6b;
    --color__total_money: #e91e98c7;
}

.pl-4 {
    padding-left: 20px !important;
}

.crm-label {
    display: inline-block;
    padding: 4px !important;
}

.text-break {
    overflow-wrap: break-word;
    word-wrap: break-word;
    -ms-word-break: break-word;
    word-break: break-word;
    -ms-hyphens: auto;
    -moz-hyphens: auto;
    -webkit-hyphens: auto;
    hyphens: auto;
    max-width: 100%;
}

.minw200 {
    min-width: 200px !important;
}

.text-nowrap {
    white-space: nowrap !important;
}

.d-inline-block {
    display: inline-block !important;
}

.mb-4 {
    margin-bottom: 20px !important;
}

.mb-2 {
    margin-bottom: 10px !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mr-0 {
    margin-right: 0 !important;
}

.mr-2 {
    margin-right: 10px !important;
}

.mt-0 {
    margin-top: 0 !important;
}

.mx-1 {
    margin-right: 5px !important;
    margin-left: 5px !important;
}

.p-4 {
    padding: 20px !important;
}

.dropdown-menu-item {
    width: 200px !important;
}

.dropdown-menu-item li a {
    white-space: normal !important;
}

.cursor-default {
    cursor: default !important;
}

.btn-xs-fixed {
    width: 24px;
    height: 24px;
    line-height: 20px;
    text-align: center;
}

#sitemodal .modal-dialog {
    position: relative;
    width: auto !important;
    max-width: 1000px !important;
    text-align: center;
    font-size: 0;
}

#sitemodal .modal-content {
    width: 100% !important;
    display: inline-block;
    font-size: 14px;
    text-align: left;
}

.log_history {
    max-height: 300px;
    overflow: auto;
}

.list_leads {
    list-style-type: none;
}

.list_leads li {
    margin-bottom: 10px;
}

.panel-main .panel-body {
    padding: 5px;
}

audio {
    width: 150px !important;
}

.icon-lead {
    margin-left: 5px;
}

.inlineblock {
    display: inline-block;
}

.margintop5 {
    margin-top: 5px;
}

.margintop10 {
    margin-top: 10px;
}

#footer {
    position: relative;
}

/* Table flex */
.flex-table .flex-table-head {
    background-color: #428bca;
    display: flex;
    flex-wrap: nowrap;
    align-items: stretch;
}

.flex-table .flex-table-head a {
    color: #ffc48c;
}

.flex-table .flex-table-head>div {
    position: relative;
    flex-grow: 0;
    min-height: 1px;
    flex-shrink: 1;
    padding: 10px 8px;
    text-transform: uppercase;
    color: #fff;
}

.flex-table .flex-table-body .item {
    display: flex;
    flex-wrap: nowrap;
    align-items: stretch;
    border-bottom: 1px #ddd solid;
}

.flex-table .flex-table-body .item:hover {
    background-color: #f5f5f5
}

.flex-table .flex-table-body .item>div {
    position: relative;
    flex-grow: 0;
    min-height: 1px;
    flex-shrink: 1;
    padding: 8px;
    order: 3;
}

.flex-table .flex-table-head>div.name,
.flex-table .flex-table-body .item>div.name {
    flex-grow: 1;
}

.flex-table .flex-table-body .item>div.name a.menu-icon-tools {
    float: right;
    margin-left: 10px;
    display: none;
}

.flex-table .flex-table-head>div.status,
.flex-table .flex-table-body .item>div.status {
    flex: 0 0 180px;
    max-width: 180px;
}

.flex-table .flex-table-head>div.stat,
.flex-table .flex-table-body .item>div.stat {
    flex: 0 0 90px;
    max-width: 90px;
}

.flex-table .flex-table-head>div.cw100,
.flex-table .flex-table-body .item>div.cw100 {
    flex: 0 0 100px;
    max-width: 100px;
}

.flex-table .flex-table-head>div.cw250,
.flex-table .flex-table-body .item>div.cw250 {
    flex: 0 0 250px;
    max-width: 250px;
}

.flex-table .flex-table-head>div.stt,
.flex-table .flex-table-body .item>div.stt {
    flex: 0 0 50px;
    max-width: 50px;
}

.flex-table .flex-table-head>div.tools,
.flex-table .flex-table-body .item>div.tools {
    flex: 0 0 70px;
    max-width: 70px;
}

.flex-table .flex-table-body .item>div.name h3 {
    font-weight: 400;
}

.flex-table .flex-table-body .item .label-name {
    display: none;
    font-weight: 700;
}

.flex-table .hide-desktop {
    display: none;
}

.d-table-flex-block-lg {
    display: block;
}

.flex-search-form .simple-form {
    display: inline-flex;
    align-items: center;
    flex-wrap: nowrap;
}

.flex-search-form .simple-form .item {
    margin-bottom: 15px;
    padding-right: 10px;
}

.flex-search-form .simple-form .item.item-btns {
    margin-bottom: 10px;
}

.flex-search-form .simple-form .item.item-btns .btn {
    margin-bottom: 5px;
    margin-right: 5px;
}

.flex-search-form .inline-form {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    margin-right: -10px;
}

.flex-search-form .ui-datepicker-trigger {
    display: none;
}

.flex-search-form .inline-form .item {
    margin-bottom: 15px;
    padding-right: 10px;
}

.flex-tools {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
}

.flex-tools .left-tool {
    padding-right: 20px;
}

.flex-input-inline {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

@media (min-width: 992px) {
    .flex-search-form .simple-form .item.item-q {
        min-width: 300px;
    }
}

@media (min-width: 768px) {
    .form-group-label {
        padding-top: 23px;
    }
}

@media (max-width: 676.98px) {
    .flex-search-form .simple-form {
        display: flex;
        flex-wrap: wrap;
    }

    .flex-search-form .simple-form .item.item-q {
        flex-grow: 1;
        flex-shrink: 1;
    }

    .flex-search-form .simple-form .item.item-submit {
        padding-right: 0;
    }

    .flex-search-form .simple-form .item.item-btns {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .flex-search-form .inline-form .item-50 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .flex-tools {
        flex-wrap: wrap;
    }

    .flex-tools .left-tool,
    .flex-tools .right-tool {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .flex-tools .left-tool {
        order: 2;
    }

    .flex-tools .right-tool {
        order: 1;
        margin-bottom: 15px;
    }
}

/* Dạng danh sách khi vùng hiển thị nhỏ */
@media (max-width: 676.98px),
(min-width: 768px) and (max-width: 991.98px) {
    .flex-table .flex-table-head {
        display: none;
    }

    .flex-table .flex-table-body .item {
        border: 1px #ddd solid;
        border-radius: 4px;
        margin-bottom: 15px;
        flex-direction: column;
        padding-bottom: 8px;
    }

    .flex-table .flex-table-body .item>div.name h3 {
        font-weight: 500;
        font-size: 18px;
    }

    .flex-table .flex-table-body .item .label-name {
        display: inline-block;
        margin-right: 5px;
    }

    .flex-table .flex-table-body .item>div {
        padding-bottom: 0;
    }

    .flex-table .flex-table-body .item>div.stt,
    .flex-table .flex-table-body .item>div.name,
    .flex-table .flex-table-body .item>div.stat,
    .flex-table .flex-table-body .item>div.cw100,
    .flex-table .flex-table-body .item>div.cw250,
    .flex-table .flex-table-body .item>div.status,
    .flex-table .flex-table-body .item>div.tools {
        flex-basis: inherit;
        width: 100%;
        max-width: 100%;
    }

    .flex-table .flex-table-body .item>div.name a.menu-icon-tools {
        display: block;
    }

    .flex-table .flex-table-head>div.tools,
    .flex-table .flex-table-body .item>div.tools {
        display: none;
    }

    .flex-table .hide-desktop {
        display: block;
    }

    .d-table-flex-block-lg {
        display: inline-block;
    }
}

#detail_customer {
    display: block;
    height: 100vh;
    transition: transform 0.6s;
    animation: fadeIn linear 0.3s;
    width: auto;
    height: auto;
    margin-bottom: 16px;
    /* background: #3e89ab0d; */
}

#detail_customer .info_order {
    padding: 16px 8px;
}

#detail_customer fieldset {
    padding: .35em .625em .75em;
    margin: 0 2px;
    border: 1px solid #ffa8a8;
    min-height: 150px;
    position: relative;
}

.isMeName {
    line-height: 23px;
}

.fieldset__order {
    position: relative;
}

#detail_customer .fieldset__order::after {
    display: block;
    position: absolute;
    left: -9px;
    top: -26px;
    height: 22px;
    width: 22px;
    background: #f35151;
    content: attr(data-stt);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    box-shadow: 0 2px 10px rgb(0 0 0 / 25%);
}

#detail_customer fieldset legend {
    border-bottom: none;
    margin-bottom: 0;
    padding: 0 8px;
}

.info_order__name {
    font-size: 16px;
    line-height: 16px;
    color: #23984c;
}

tbody tr .info_order__vip {
    display: block;
}

.info_order__vip {
    background: #32bd63;
    padding: 4px;
    border-radius: 2px;
    color: #fff;
    box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
    text-align: center;
}

.info_order__vip:hover {
    color: #fff;
}

.info_order__deal-price {
    background: var(--color__total_money);
    padding: 3px 8px;
    border-radius: 5px;
    color: #fff;
    box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
    font-size: 13px;
}

.info_order__tax {
    color: #3f51b5;
}

.info_order__address-org {
    font-style: italic;
}

.text--weight {
    font-weight: bold;
}

.info_order__status_order {
    background: antiquewhite;
    padding: 2px 11px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
}

.promo--code {
    background: #2196f3;
    color: #fff;
    padding: 2px 9px;
    border-radius: 3px;
    box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
}

.label__gerenal {
    background: var(--color__label);
    color: #fff;
    padding: 2px 9px;
    border-radius: 3px;
    box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
    font-size: 12px;
}

.price--status {
    color: #fff;
    padding: 2px 9px;
    border-radius: 3px;
    box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
}

.is_expired {
    display: block;
    background: #3f51b5;
    color: #fff;
    padding: 2px 9px;
    border-radius: 3px;
    box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
}

.not_is_expired {
    display: block;
    background: #ff3a3a;
    color: #fff;
    padding: 2px 9px;
    border-radius: 3px;
    box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
}

.panel--success .panel-heading {
    background: #05c46b;
    color: #fff;
}

.panel--info .panel-heading {
    background: #0383bd;
    color: #fff;
}

.panel--orange .panel-heading {
    background: #ff5722;
    color: #fff;
}

.panel--yellow .panel-heading {
    background: #ff9900;
    color: #fff;
}

.money--total {
    font-size: 12px;
    background: #05c46b;
    color: #fff;
    padding: 2px 9px;
    border-radius: 3px;
    box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
}

.point--total {
    background: #0383bd;
    color: #fff;
    padding: 2px 9px;
    border-radius: 3px;
    box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
}

.color--red {
    color: red;
}

.btn--social {
    position: relative;
    text-decoration: none;
    text-transform: uppercase;
    font-family: sans-serif;
    overflow: hidden;
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
    border: none;
    margin-bottom: 5px;
    display: inline-flex;
    align-items: center;
    outline: none !important;
}

.btn--social i {
    margin-right: 4px;
}

.btn--social:before {
    content: '';
    position: absolute;
    top: 0;
    left: -15px;
    width: 50%;
    height: 100%;
    transform: skew(46deg);
    background: rgba(255, 255, 255, .2);
}

.btn--social:after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: 0.5s;
}

.btn--social:hover:after {
    left: 100%;
    border: none;
}

.label--success {
    background: #20bf6b;
    padding: 4px 14px;
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
    position: relative;
    display: flex;
    align-items: center;
}

.label--success:before {
    content: '';
    position: absolute;
    top: 0;
    left: -15px;
    width: 50%;
    height: 100%;
    transform: skew(46deg);
    background: rgba(255, 255, 255, .2);
}

.label--stt {
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
    position: relative;
    background: #f47c36;
    color: #fff;
    font-weight: bold;
    padding: 7px 11px;
}

.word-wrap {
    word-wrap: break-word;
    max-width: 250px;
    display: block;
}

.list_customer {
    overflow-x: hidden;
}

table tr td {
    vertical-align: middle !important;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

.support {
    max-height: 500px;
    overflow-x: hidden;
    padding: 17px 20px;
}

.support::-webkit-scrollbar-track,
.info_product::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
    background-color: #f5f5f5;
}

.support::-webkit-scrollbar,
.info_product::-webkit-scrollbar {
    width: 3px;
    background-color: #f5f5f5;
}

.support::-webkit-scrollbar-thumb,
.info_product::-webkit-scrollbar-thumb {
    background-color: #f79633;
}

.row__support {
    padding: 16px 10px;
    border: 1px solid #cccccc73;
    margin-bottom: 20px;
    position: relative;
    transition: transform 0.6s;
    animation: fadeIn linear 1s;
}

.support__stt {
    position: absolute;
    top: -6px;
    left: -6px;
    height: 20px;
    width: 20px;
    background: #fdf500;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border-radius: 2px;
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
}

.support__action_edit {
    position: absolute;
    top: -11px;
    right: -3PX;
    box-shadow: 0 5px 5px rgb(0 0 0 / 20%);
    padding: 4px 0px 4px 5px;
}

.icon_new {
    background: url("../images/icons/new.gif") no-repeat;
    display: inline-block;
}

.support__new {
    position: absolute;
    top: -8px;
    left: 27px;
    width: 100%;
}

.support__title {
    font-weight: bold;
    font-size: 17px;
    color: #737373;
}

.main_support {
    border: 1px solid #ffa8a8;
    padding: 10px;
    border-radius: 2px;
    background: #fbfbfb;
    margin-bottom: 20px;
}

.row__support {
    background: #fff;
}

.fieldset__order--danger:before {
    content: '';
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    background: #ffe4e46b;
    height: 100%;
    width: 100%;
}

/* // Nếu sau này đơn hàng dài chỉ việc sử dụng đoạn này */
.info_product {
    padding: 8px 16px 0 14px;
    position: relative;
    max-height: 1175px;
    overflow-x: hidden;
}

.fieldset__order--success {}

.price--status--danger {
    background: #fd5145;
    display: flex;
    align-items: center;
}

.span__data {
    font-size: 12px;
}

.price--status--success {
    background: var(--color__success);
    display: flex;
    align-items: center;
}

/* CSS Notification */
#toast {
    position: fixed;
    top: 16px;
    right: 12px;
    z-index: 9999;
}

.toast {
    display: flex;
    align-items: center;
    background: #fff;
    padding: 20px 0;
    border-radius: 2px;
    border-left: 4px solid;
    min-width: 400px;
    max-width: 450px;
    transition: all 0.5s;
    z-index: 2;
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.2);
    animation: slideInLeft ease 0.6s, fadeOut linear 1s 3000s forwards;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(calc(100% + 32px));
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeOut {
    to {
        opacity: 1;
    }
}

.toast--success {
    border-color: #47d684;
    background: #f7fffb;
}

.toast--success .toast__icon {
    color: #47d684;
}

.toast--info {
    border-color: #2f86ed;
}

.toast--info .toast__icon {
    color: #2f86ed;
}

.toast--warning {
    border-color: #ffc021;
}

.toast--warning .toast__icon {
    color: #ffc021;
}

.toast--error {
    border-color: #ff623d;
    background: #f9f2f2;
}

.toast--error .toast__icon {
    color: #ff623d;
}


.toast+.toast {
    margin-top: 24px;
}

.toast__icon {
    font-size: 24px;
}

.toast__icon,
.toast__close {
    padding: 0 16px;
    cursor: pointer;
}

.toast__body {
    flex-grow: 1;
}

.toast__title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.toast__msg {
    font-size: 14px;
    color: #888;
    margin-top: 5px;
    line-height: 1.6;
}

.toast__close {
    font-size: 20px;
}

.imap-stat-form {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.imap-stat-form-inner {
    flex-basis: 480px;
    flex-shrink: 1;
}

.imap-stat-form-inner .input-daterange {
    max-width: 480px;
}

.countSearch {
    background: #f44336;
    padding: 7px;
    color: #fff;
    border: 1px solid #fff;
    box-shadow: 0px 5px 5px rgb(0 0 0 / 20%);
}

.a_link {
    color: #fff;
    text-decoration: underline;
}

.a_link:hover {
    color: #fff;
}

.dataTables_filter {
    text-align: right;
}

input[type="search"] {
    outline: none !important;
    padding: 5px !important;
    border-radius: 0 !important;
    border: 1px solid #ece6e6;
}

.dataTables_paginate {
    text-align: right;
}

.dataTables_paginate a {
    padding: 5px;
    cursor: pointer;
    outline: none;
}

.title_list,
.title_list_profile,
.title_list_ticket {
    color: #0e5e9e;
    font-size: 18px;
    text-transform: uppercase;
    padding-bottom: 8px;
    position: relative;
    cursor: pointer;
}

.title_list:before,
.title_list_profile:before,
.title_list_ticket:before {
    position: absolute;
    content: '';
    /* top: 6px; */
    left: -20px;
    /* background: red; */
    border-width: 15px;
    border-color: transparent transparent transparent #f44336;
    border-style: solid;
}

.listVIP,
.listOrder,
.btn__downleft,
.ttcomment {
    cursor: pointer;
    user-select: none;
}

.listVIP i,
.btn__downleft i,
.listOrder i,
.btn__downleft i,
.ttcomment i,
.listProfile i {
    transition: all 0.3s;
}


.listOrder .iconDown {
    transform: rotate(90deg) !important;
}

.fa-angle-double-right {
    transform: rotate(90deg);
}

.iconDown {
    transform: rotate(0);
}

.showtt__vip {
    color: #095ca5;
    cursor: pointer;
    user-select: none;
    line-height: 40px;
}

.tt__order {
    line-height: 20px;
    margin-bottom: 12px;
    color: #095ca5;
    cursor: pointer;
    user-select: none;
}

.showtt__order {
    margin-bottom: 16px;
}


/*Responsive table*/
@media screen and (max-width: 600px) {
    .label_responsive {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .responsivetb table {
        border: none;
    }

    .info_product {
        padding: 0;
    }

    .responsivetb table .td_content {
        max-width: 200px;
        text-align: right;
    }

    .responsivetb table thead {
        border: none;
        clip: rect(0 0 0 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }

    .responsivetb table tr {
        border-bottom: 3px solid #ddd;
        display: block;
        margin-bottom: 36px;
    }

    .responsivetb table td {
        display: flex;
        font-size: 13px;
        justify-content: space-between;
        text-align: right;
    }

    .responsivetb table td:last-child {
        display: block !important;
    }

    .responsivetb table td::before {
        content: attr(data-label);
        font-weight: bold;
        text-transform: uppercase;
        text-align: left;
        word-break: break-word;
        width: 80px;
    }

    .responsivetb table td:last-child {
        border-bottom: 0;
    }
}

.view_ordervip {
    margin-top: 10px;
    border: 1px dotted #ccc;
    padding: 14px;
    position: relative;
}

.view_ordervip:before {
    position: absolute;
    content: '';
    top: -20px;
    left: 50%;
    border-style: solid;
    border-width: 10px 9px;
    border-color: transparent transparent #d2d4da transparent;
    transform: translateX(-50%);
}

.show_detail,
.show_detail_order,
.show_detail_vip,
.show_detail_order_dtnet,
#ttdhkh,
#ttdhkh_profile,
#support_ticket {
    cursor: pointer;
    user-select: none;
}

.show_detail i,
.show_detail_order i,
.show_detail_vip i,
.show_detail_order_dtnet i {
    transform: rotate(0);
}

.show_detail .iconUp,
.show_detail_order .iconUp,
.show_detail_vip .iconUp,
.show_detail_order_dtnet .iconUp {
    transform: rotate(90deg);
}

.listOrder .fa-angle-double-right {
    transform: rotate(0deg);
}

.show_detail i,
.show_detail_order i,
.show_detail_vip i,
.show_detail_order_dtnet i,
#ttdhkh .fa-angle-double-right,
#ttdhkh_profile .fa-angle-double-right {
    transition: all 0.5s;
}

.btn_order__vip {
    background: #ee5a24;
    padding: 3px;
    color: #fff;
    border-radius: 2px;
    box-shadow: 0px 0px 5px rgb(0 0 0 / 20%);
}

.btn_order__vip:hover {
    color: #fff;
}

.ttnddh a:hover {
    text-decoration: none !important;
}

#ttdhkh {
    user-select: none;
    cursor: pointer;
}

.ttnddh {
    display: block;
    line-height: 24px;
}

.note_source {
    background: #0070bf;
    color: #fff;
    padding: 2px;
    font-size: 13px;
}

.link_manager {
    margin-bottom: 2px;
}

.panel__success_left {
    position: relative;
}

.panel__success_left .panel-heading {
    background-color: #f1f1f1;
    color: #3c3c3c !important;
}

.panel__custom {
    position: relative;
}

.panel__custom .panel-heading {
    position: relative;
}

.panel__custom .panel-heading:before {
    content: '';
    position: absolute;
    top: 0;
    right: 20px;
    width: 9%;
    height: 100%;
    transform: skew(38deg);
    background: rgb(181 181 181 / 15%);
}

.panel__custom {
    border: none;
    box-shadow: 0px 4px 10px 3px #ccc;
    border-radius: 0;
}

.box__title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.title__page_organization {
    font-family: "Roboto";
    text-transform: uppercase;
    font-size: 19px;
    border-left: 7px solid #729eff;
    padding: 10px;
    box-shadow: -9px 10px 28px rgb(168 166 166 / 20%);
    flex: 1;
    margin-bottom: 0;
}

.result__data {
    margin: 0;
    padding: 7px;
    box-shadow: 0px 0px 0px 5px #eaeaea;
    margin-right: 20px;
}

.number__result {
    font-weight: bold;
    font-size: 18px;
    font-family: "Roboto";
    color: #ff7f00;
}

.box__action {
    display: flex;
    justify-content: space-between;
}

.box__action a {
    margin-right: 5px;
}

.tr__search div {
    display: flex;
    align-items: center;
    user-select: none;
    cursor: pointer;
    justify-content: center;
}

.wrap__text,
.body__detail>.form-group strong {
    word-break: break-word;
}

.icon__custom {
    font-size: 12px;
    margin-right: 4px;
}

.content__detail {
    padding: 3px;
    border-radius: 2px;
}

.content__organizationname {
    color: #3071a9;
}

.content__shortname {
    color: #ff3900;
}

.content__employees {
    background: #6d9fda;
    color: #FFf;
    box-shadow: 0px 5px 17px rgb(0 0 0 / 20%);
}

.body__detail {
    background: #FFF;
    box-shadow: 0px 5px 16px rgb(178 174 174 / 20%);
    animation: fadeOut .4s linear;
    transition: all 0.3s;
}

.list_tochuc {
    transition: all 0.2s;
    /* animation: fadeOut .2s linear; */
}

.form__fadeOut {
    transition: all 0.3s;
    animation: fadeOut .3s linear;
}

@keyframes fadeOut {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

.checkbox__active {
    display: flex;
    justify-content: center;
    user-select: none;
    align-items: center;
}

.checkbox__active input {
    margin: 0;
    outline: none !important;
}

.edit__org {
    margin-right: 10px;
}

.addLeads {
    transition: all 0.3s;
    animation: fadeOut .3s linear;
}

.addLeads .form-group input,
.addLeads .form-group select,
.addLeads .form-group .input-group {
    margin-bottom: 10px;
}

.addLeads .form-group textarea {
    margin-top: 10px;
}

.addLeads .form-group .input-group input {
    margin-bottom: 0;
}

.textinfo {
    overflow-wrap: break-word;
}

.timeline {
    list-style: none;
    padding: 20px 0 20px;
    position: relative;
}

.timeline:before {
    top: 0;
    bottom: 0;
    position: absolute;
    content: " ";
    width: 3px;
    background-color: #eeeeee;
    left: 50%;
    margin-left: -1.5px;
}

.timeline>li {
    /* margin-bottom: 20px; */
    position: relative;
    transition: 0.5s;
}

.timeline>li:before,
.timeline>li:after {
    content: " ";
    display: table;
}

.timeline>li:after {
    clear: both;
}

.timeline>li:before,
.timeline>li:after {
    content: " ";
    display: table;
}

.timeline>li:after {
    clear: both;
}

.timeline>li>.timeline-panel {
    width: 50%;
    float: left;
    border: 1px solid #d4d4d421;
    border-radius: 2px;
    padding: 20px;
    position: relative;
    /* -webkit-box-shadow: 0 1px 6px rgb(0 0 0 / 18%); */
    /* box-shadow: 0 1px 6px rgb(0 0 0 / 18%); */
    background: #e6e9f147;
    border-right: none;
    border-left: none;
}

.timeline>li.timeline-inverted+li:not(.timeline-inverted),
.timeline>li:not(.timeline-inverted)+li.timeline-inverted {
    margin-top: -16px;
}

.timeline>li.not_display {
    /* margin-top: 10px !important; */
}

.timeline>li:not(.timeline-inverted) {
    padding-right: 90px;
}

.timeline>li.timeline-inverted {
    padding-left: 90px;
}

.timeline>li>.timeline-panel:before {
    position: absolute;
    top: 26px;
    right: -15px;
    display: inline-block;
    border-top: 15px solid transparent;
    border-left: 15px solid #d4d4d421;
    border-right: 0 solid #d4d4d421;
    border-bottom: 15px solid transparent;
    content: " ";
}

.timeline>li>.timeline-panel:after {
    position: absolute;
    top: 27px;
    right: -14px;
    display: inline-block;
    border-top: 14px solid transparent;
    border-left: 14px solid #d4d4d421;
    border-right: 0 solid #d4d4d421;
    border-bottom: 14px solid transparent;
    content: " ";
}

.timeline>li>.timeline-badge {
    color: #fff;
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 1.4em;
    text-align: center;
    position: absolute;
    top: 16px;
    left: 50%;
    margin-left: -25px;
    background-color: #999999;
    z-index: 100;
    border-top-right-radius: 50%;
    border-top-left-radius: 50%;
    border-bottom-right-radius: 50%;
    border-bottom-left-radius: 50%;
}

.timeline>li.timeline-inverted>.timeline-panel {
    float: right;
}

.timeline>li.timeline-inverted>.timeline-panel:before {
    border-left-width: 0;
    border-right-width: 15px;
    left: -15px;
    right: auto;
}

.timeline>li.timeline-inverted>.timeline-panel:after {
    border-left-width: 0;
    border-right-width: 14px;
    left: -14px;
    right: auto;
}

.timeline-badge.primary {
    background-color: #2e6da4 !important;
}

.timeline-badge.success {
    background-color: #3f903f !important;
}

.timeline-badge.warning {
    background-color: #f0ad4e !important;
}

.timeline-badge.danger {
    background-color: #d9534f !important;
}

.timeline-badge.info {
    background-color: #5bc0de !important;
}

.timeline-title {
    margin-top: 0;
    color: inherit;
}

.timeline-body>p,
.timeline-body>ul {
    margin-bottom: 0;
}

.timeline-body>p+p {
    margin-top: 5px;
}

.panel__htkh {
    /* box-shadow: 0px 4px 10px 3px #ccc; */
    border: 1px solid #d9d9d9;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.panel__htkh .panel-heading {
    color: #424242;
    background-color: #dde4f5;
    border-color: #dde4f5;
    cursor: pointer;
}

#info {
    /* padding: 9px; */
    /* border: 1px solid #ffa8a8; */

    /* max-height: 700px; */
    /* overflow-y: scroll; */
}

.timeline-title {
    user-select: none;
}

#info::-webkit-scrollbar-track,
.info_product::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
    background-color: #f5f5f5;
}

#info::-webkit-scrollbar,
.info_product::-webkit-scrollbar {
    width: 3px;
    background-color: #f5f5f5;
}

#info::-webkit-scrollbar-thumb,
.info_product::-webkit-scrollbar-thumb {
    background-color: #f79633;
}

#info .header {
    overflow: hidden;
    padding: 20px 0;
}

#info .show_log {
    margin-bottom: 0;
    display: inline-block;
    user-select: none;
}

#info .show_log a span {
    padding: 8px;
    /* background: whitesmoke; */
}

.hidentime {
    /* top: -16px !important; */
    cursor: pointer;
    user-select: none;
    margin-top: 10px;
    font-size: 15px;
    text-align: center;
    width: 100%;
    display: flex;
    justify-content: center;
}

.li__time_custom {
    z-index: 9999;
    padding-right: 0 !important;
    width: 100%;
}

.li__time_custom:nth-child(1) i {
    margin-bottom: 5px;
}

.li__time_custom i {
    padding: 10px;
    font-size: 18px;
    background: #ebebeb;
    border-radius: 50%;
}

.not_display1 {
    display: none;
}

.timeline .showtimeline {
    display: block;
    animation: 0.6s fadeIn linear;
}

.timeline .showtimeline .timeline-panel {
    background: #fdf3f37d;
}

.timeline-title .fa-ellipsis-h {
    font-size: 19px;
    margin-left: 8px;
    margin-top: 6px;
}

.navigation_menu {
    position: relative;
    width: 40px;
    height: 40px;
    background: #fff;
    transition: 0.5s;
    /* transition-delay: 0s; */
    border-radius: 4px;
    z-index: 10000000;
    overflow: hidden;
}

.navigation_menu.active {
    width: 250px;
    height: 360px;
    transition: 0.5s;
    /* transition-delay:0.75s; */
    box-shadow: 0px 2px 6px rgb(0 0 0 / 40%);
}

.navigation_menu .toggle_menu {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background: #ff4081;
    cursor: pointer;
    transition: 0.5s;
}

.navigation_menu .toggle_menu.active {
    background: #2196f3;
}

.navigation_menu .toggle_menu::before {
    content: '+';
    position: absolute;
    font-size: 2em;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    font-weight: 400;
    transition: 0.5s;
    color: #fff;
}

.navigation_menu .toggle_menu.active::before {
    transform: rotate(315deg);
}

.navigation_menu ul {
    position: absolute;
    left: 0;
    width: 100%;
    padding: 0;
}

.navigation_menu ul li {
    position: relative;
    list-style: none;
    width: 100%;
    /* transition: all 0.5s; */
}

.navigation_menu ul li:hover {
    background: #03a9f4;
}

.navigation_menu ul li:hover a {
    color: #fff;
}

.navigation_menu ul li a {
    position: relative;
    display: block;
    width: 100%;
    display: flex;
    text-decoration: none;
    color: #154367;
}

.navigation_menu ul li a .icon {
    position: relative;
    display: block;
    min-width: 40px;
    height: 40px;
    text-align: center;
    line-height: 50px;
}

.navigation_menu ul li a .icon .fa-solid {
    font-size: 24px;
    color: #154367;
}

.navigation_menu ul li a .title {
    position: relative;
    display: block;
    height: 40px;
    line-height: 40px;
    text-align: start;
}

.navigation_menu ul li:hover a .icon .fa-solid,
.navigation_menu ul li:hover a .icon .title {
    color: #fff;
}

#menu__customer {
    position: absolute;
    right: 26%;
    top: 16px;
}

#menu__customer.affix {
    position: fixed;
    z-index: 999999;
    top: 1px;
    right: 22%;
}

#menu__customer.affix-top {
    position: absolute;
    right: 26%;
    top: 16px;
}

#menu__customer.affix-bottom {
    /* position:absolute; */
}

/* #menu__customer.active.affix {
    right: 14px;
}

#menu__customer.active.affix .navigation_menu {
    width: 274px;
} */

/*Responsive table*/
@media screen and (max-width: 768px) {

    #menu__customer.affix-top,
    #menu__customer,
    #menu__customer.affix {
        right: 16px;
    }


    .navigation_menu .toggle_menu {
        right: 0;
    }
}


.timeline_mobile::before {
    top: 0;
    bottom: 0;
    position: absolute;
    content: " ";
    width: 3px;
    background-color: #eeeeee;
    left: 6%;
    margin-left: -1.5px;
}

.timeline_mobile>li {
    margin-bottom: 12px;
}

.timeline_mobile>li>.timeline-badge {
    left: 6%;
}

.timeline_mobile>li>.timeline-panel {
    width: 100%;
}

.timeline_mobile li>.hidentime {
    display: block;
    justify-content: left;
    text-align: left;
    margin-top: 0;
}

.timeline_mobile li.li__time_custom {
    padding-left: 0;
}

.timeline_mobile>li>.timeline-badge {
    width: 40px;
    height: 40px;
    line-height: 43px;
    font-size: 1.3em;
}

.timeline_mobile .showtimeline {
    display: block;
    animation: 0.6s fadeIn linear;
}

.timeline_mobile .showtimeline .timeline-panel {
    background: #fdf3f37d;
}

/*Responsive table*/
@media screen and (min-width: 500px) {
    .timeline_mobile li.li__time_custom {
        margin-left: 2%;
    }
}

@media screen and (min-width: 410px) and (max-width: 470px) {
    .timeline_mobile li.li__time_custom {
        margin-left: 1%;
    }
}

@media screen and (max-width: 470px) {
    .timeline_mobile>li.timeline-inverted:not(.li__time_custom) {
        padding-left: 65px;
    }
}

@media screen and (min-width: 470px) and (max-width: 700px) {
    .timeline_mobile>li.timeline-inverted:not(.li__time_custom) {
        padding-left: 75px;
    }

    .timeline_mobile::before {
        left: 5.5%;
    }

}

.flicker__title {
    animation: flicker 1.5s infinite;
    transition: all 0.3s;
}

@keyframes flicker {
    0% {
        opacity: 0;
    }

    10% {
        opacity: 0.1;
    }

    30% {
        opacity: 0.3;
    }


    50% {
        opacity: 0.5;
    }

    70% {
        opacity: 0.7;
    }

    100% {
        opacity: 1;
    }

}

.span_ave {
    padding: 10px;
    background: #ffe700;
    border-radius: 2px;
    box-shadow: 0px 15px 14px rgb(222 206 206 / 60%);
    font-weight: bold;
}

#stat-from,
#login_from {
    display: inline-block;
    width: 85%;
}

#stat-to,
#login_to {
    display: inline-block;
    width: 85%;
}

#filter-log-time {
    margin-top: 10px;
}

#input-logtime:not(.checked) {
    display: none;
}

.form-pickdate img {
    padding-left: 5px;
}

.reg-date .control-label {
    padding: 6px 0 0 20px;
}

#stat-note {
    padding-top: 5px;
}

.stat-sum {
    padding-bottom: 5px;
}

#chartcolumn>.apexcharts-canvas>svg {
    overflow: visible;
}

.econtract-page .well .form-group {
    margin-bottom: 0;
}

.econtract-page .form-group.row {
    display: flex;
    align-items: center;
}

.econtract-page .form-group.row>label {
    margin-bottom: 0;
}

.econtract-page .wait {
    transition: all 2s;
    -webkit-animation: spin 2s linear infinite;
    -moz-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
}

input[type="file"]:focus, input[type="radio"]:focus, input[type="checkbox"]:focus {
    outline: none;
}

.econtract-page table a.btn {
    display: inline-block;
    margin-top: 5px;
    margin-bottom: 5px;
}
.econtract-page #tblOrders td ul {
    padding-left: 20px;
    margin-bottom: 0;
}

.econtract-page table td a {
    text-decoration: none;
}

.mt-1 {
    margin-top: 10px;
}

@-moz-keyframes spin {
    100% {
        -moz-transform: rotate(-360deg);
    }
}

@-webkit-keyframes spin {
    100% {
        -webkit-transform: rotate(-360deg);
    }
}

@keyframes spin {
    100% {
        -webkit-transform: rotate(-360deg);
        transform: rotate(-360deg);
    }
}

.bg_leade_new {
    transition: all 1s;
    animation: fadeBG 50s linear;
}

#time_update {
    margin-bottom: 20px;
}

@keyframes fadeBG {
    from {
        background: #e8fffd;
    }
    to {
        background: #fff;
    }
}

.social-icon {
    width: 24px;
    height: 24px;
    margin-right: 10px;
    vertical-align: middle;
}
#social_network_form {
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 5px;
}
.processing {
    position: relative;
}
.processing:before {
    content: "";
    margin: 0 auto;
    font-size: 10px;
    text-indent: -9999em;
    border-top: 3px solid rgba(0,0,0,.1)!important;
    border-right: 3px solid rgba(0,0,0,.1)!important;
    border-bottom: 3px solid rgba(0,0,0,.1)!important;
    opacity: .8;
    border-left: 3px solid #fff;
    -webkit-animation: spin .5s infinite linear;
    animation: spin .5s infinite linear;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    pointer-events: none;
}
.processing:before {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -15px;
    margin-top: -15px;
    z-index: 99;
}
.logotherlists a {
    word-break: break-all;
    overflow-wrap: break-word;
}
@keyframes spin {
    from {
        transform:rotate(0deg);
    }
    to {
        transform:rotate(360deg);
    }
}
.cr-md-dialog {
    max-width:980px;
    width:auto;
}
.custom-modal {
    display: none; /* Ẩn mặc định */
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4); /* Mờ nền */
}

.custom-modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    border-radius: 8px;
    width: 80%;
    max-width: 600px;
    position: relative;
}

.custom-modal-close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    cursor: pointer;
}

.custom-modal-close:hover {
    color: red;
}
