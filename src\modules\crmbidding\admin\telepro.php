<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Module\crmbidding\Telepro\Api as ApiConact;

// Lấy danh sách công việc
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_jobs ORDER BY title ASC';
$array_telepro_jobs = $nv_Cache->db($sql, 'id', $module_name);

// Xem danh sách cuộc gọi
if ($nv_Request->isset_request('ajaxmode', 'post')) {
    if (!defined('NV_IS_SPADMIN') and !$crmbidding_admin_info['allowed_telepro']) {
        nv_htmlOutput('Error access!!!');
    }
    $ajaxmode = $nv_Request->get_title('ajaxmode', 'post', '');
    $id = $nv_Request->get_absint('id', 'post', 0);

    // Lấy liên hệ
    $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro WHERE id=' . $id;
    $contact = $db->query($sql)->fetch();
    if (empty($contact)) {
        nv_htmlOutput('Conact not exists!!!');
    }
    if (!isset($array_telepro_jobs[$contact['job_id']])) {
        nv_htmlOutput('Job not exists!!!');
    }
    $job = $array_telepro_jobs[$contact['job_id']];

    $queries = [
        'call_converted' => '',
        'conversation_status' => '',
        'error' => '',
        'keywords' => '',
        'limit' => 500,
        'package' => '',
        'page' => 1,
        'period' => '',
        'search' => '',
        'status' => '',
        'phone' => $contact['phone']
    ];
    $url = 'https://api.telepro.me/api/v1/jobs/' . $job['id'] . '/calls?' . http_build_query($queries, null, '&');
    $ch = curl_init($url);
    $authorization = "Authorization: Bearer " . $job['api_key'];
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        $authorization
    ]);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    $response = curl_exec($ch);
    $response = json_decode($response, true);
    if (!is_array($response) or empty($response)) {
        nv_htmlOutput('Api response error json!!!');
    }
    if (empty($response['success'])) {
        nv_htmlOutput('Api not success!!!');
    }
    if (empty($response['calls']['data'])) {
        nv_htmlOutput($nv_Lang->getModule('telepro_call_empty'));
    }
    $response = $response['calls']['data'];

    if ($ajaxmode == 'synccontact') {
        // Cập nhật trạng thái call
        $call = array_shift($response);

        $update_data = [];
        $update_data['note'] = $call['notes'] ?? '';
        $update_data['timecall'] = $call['call_at'] ?? 0;
        $update_data['recording'] = $call['recording'] ?? '';
        $update_data['status'] = $call['conversation_status'] ?? '';
        $update_data['contact_id'] = $call['contact_id'] ?? 0;
        $update_data['call_id'] = $call['results'][0]['call_id'] ?? 0;
        $update_data['updated_at'] = $call['results'][0]['updated_at'] ?? '';
        $update_data['updated_at'] = empty($update_data['updated_at']) ? 0 : strtotime($update_data['updated_at']);
        $update_data['duration'] = $call['duration'] ?? '';
        $update_data['agent_id'] = $call['uid'] ?? 0;
        $update_data['agent_fullname'] = $call['fullname'] ?? '';
        $update_data['is_converted'] = $call['call_converted'] ?? 0;
        $update_data['recording_isset'] = 1;

        $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_telepro SET
            note=" . $db->quote($update_data['note']) . ",
            timecall=" . $update_data['timecall'] . ",
            recording=" . $db->quote($update_data['recording']) . ",
            recording_isset=" . $update_data['recording_isset'] . ",
            status=" . $db->quote($update_data['status']) . ",
            contact_id=" . $update_data['contact_id'] . ",
            call_id=" . $update_data['call_id'] . ",
            updated_at=" . $update_data['updated_at'] . ",
            duration=" . $update_data['duration'] . ",
            agent_id=" . $update_data['agent_id'] . ",
            agent_fullname=" . $db->quote($update_data['agent_fullname']) . ",
            is_converted=" . $update_data['is_converted'] . "
        WHERE id=" . $id;
        $db->query($sql);

        nv_htmlOutput($nv_Lang->getModule('telepro_sync_success'));
    }

    $xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    krsort($response);
    $stt = 0;
    foreach ($response as $call) {
        $row = [];
        $row['note'] = $call['notes'] ?? '';
        $row['timecall'] = $call['call_at'] ?? 0;
        $row['recording'] = $call['recording'] ?? '';
        $row['status'] = $call['conversation_status'] ?? '';
        $row['contact_id'] = $call['contact_id'] ?? 0;
        $row['call_id'] = $call['results'][0]['call_id'] ?? 0;
        $row['updated_at'] = $call['results'][0]['updated_at'] ?? '';
        $row['updated_at'] = empty($row['updated_at']) ? 0 : strtotime($row['updated_at']);
        $row['duration'] = $call['duration'] ?? '';
        $row['agent_id'] = $call['uid'] ?? 0;
        $row['agent_fullname'] = $call['fullname'] ?? '';
        $row['is_converted'] = $call['call_converted'] ?? 0;
        $row['recording_isset'] = 1;

        $row['timecall'] = $row['timecall'] ? nv_date('d/m/Y H:i', $row['timecall']) : '';

        $xtpl->assign('STT', ++$stt);
        $xtpl->assign('CALL', $row);
        $xtpl->parse('calls.loop');
    }

    $xtpl->parse('calls');
    $contents = $xtpl->text('calls');

    include NV_ROOTDIR . '/includes/header.php';
    echo $contents;
    include NV_ROOTDIR . '/includes/footer.php';
}

$row = [];
$error = [];
$set_active_op = 'import';

$showheader = $nv_Request->get_int('showheader', 'post,get', 1);

$array_sapi_status = [
    1 => [
        '=0',
        $nv_Lang->getModule('telepro_api_status0')
    ],
    2 => [
        '=1',
        $nv_Lang->getModule('telepro_api_status1')
    ],
    3 => [
        '<0',
        $nv_Lang->getModule('telepro_api_status2')
    ]
];

$array_search = [];
$array_search['q'] = $nv_Request->get_title('q', 'post,get');
$array_search['id'] = $nv_Request->get_int('id', 'post,get', 0);
$array_search['phone'] = $nv_Request->get_title('phone', 'post,get', '');
$array_search['email'] = $nv_Request->get_title('email', 'post,get', '');
$array_search['job_id'] = $nv_Request->get_int('job_id', 'post,get', -1);
$array_search['api_status'] = $nv_Request->get_int('api_status', 'post,get', 0);

if (!isset($array_telepro_jobs[$array_search['job_id']]) and $array_search['job_id'] != 0) {
    $array_search['job_id'] = -1;
}
if (!isset($array_sapi_status[$array_search['api_status']])) {
    $array_search['api_status'] = 0;
}

// Fetch Limit
$show_view = true;
$per_page = 20;
$page = $nv_Request->get_int('page', 'post,get', 1);
$db->sqlreset()
    ->select('COUNT(*)')
    ->from('' . NV_PREFIXLANG . '_' . $module_data . '_telepro');

$where = [];
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;

if (!empty($array_search['q'])) {
    $where[] = 'name LIKE :q_name OR phone LIKE :q_phone OR email LIKE :q_email';
    $base_url .= '&amp;q=' . urlencode($array_search['q']);
}
if ($array_search['id'] > 0) {
    $where[] = 'id= ' . $array_search['id'];
    $base_url .= '&amp;id=' . $array_search['id'];
} else if (!empty($array_search['phone'])) {
    $where[] = "phone LIKE '%" . $array_search['phone'] . "%'";
    $base_url .= '&amp;phone=' . urlencode($array_search['phone']);
} else if (!empty($array_search['email'])) {
    $where[] = "email LIKE '%" . $array_search['email'] . "%'";
    $base_url .= '&amp;email=' . urlencode($array_search['email']);
}
if ($array_search['job_id'] > -1) {
    $where[] = 'job_id= ' . $array_search['job_id'];
    $base_url .= '&amp;job_id=' . $array_search['job_id'];
}
if (!empty($array_search['api_status'])) {
    $where[] = 'job_id>0';
    $where[] = 'api_status' . $array_sapi_status[$array_search['api_status']][0];
    $base_url .= '&amp;api_status=' . $array_search['api_status'];
}

if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}
$sth = $db->prepare($db->sql());

if (!empty($array_search['q'])) {
    $sth->bindValue(':q_name', '%' . $array_search['q'] . '%');
    $sth->bindValue(':q_phone', '%' . $array_search['q'] . '%');
    $sth->bindValue(':q_email', '%' . $array_search['q'] . '%');
}
$sth->execute();
$num_items = $sth->fetchColumn();

$db->select('*')
    ->order('id DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$sth = $db->prepare($db->sql());

if (!empty($array_search['q'])) {
    $sth->bindValue(':q_name', '%' . $array_search['q'] . '%');
    $sth->bindValue(':q_phone', '%' . $array_search['q'] . '%');
    $sth->bindValue(':q_email', '%' . $array_search['q'] . '%');
}
$sth->execute();

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('ROW', $row);
$xtpl->assign('Q', $array_search['q']);
$xtpl->assign('LINK_JOBS', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=telepro-jobs');
$xtpl->assign('LINK_IMPORT', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=telepro-import');
$xtpl->assign('LINK_LOGS', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=telepro-logs');

if ($show_view) {
    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
    }
    $number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;

    // $src = NV_BASE_SITEURL . 'uploads/' . $module_upload . '/telepro/';
    // $realdir = NV_UPLOADS_REAL_DIR . '/' . $module_upload . '/telepro/';

    $src = 'https://s3.ap-southeast-1.amazonaws.com/dauthau.asia/uploads/telepro/';

    while ($view = $sth->fetch()) {
        $view['number'] = $number++;
        $view['link_view'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $view['id'];
        $view['timecall'] = $view['timecall'] ? nv_date('H:i d/m/Y', $view['timecall']) : '';

            // recording_isset: 1; đường dẫn còn tồn tại, 0. đường dẫn k tồn tại
        $view['file_telepro'] = '';
        if ($view['s3'] == 1) {
            $view['file_telepro'] = $src . $view['file'];
        } elseif ($view['recording'] != '' and $view['recording_isset'] == 1) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $view['recording']);
            curl_setopt($ch, CURLOPT_HEADER, 0);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_exec($ch);
            $http_code = curl_getinfo($ch);
            curl_close($ch);
            if ($http_code['http_code'] == 200) {
                $view['file_telepro'] = $view['recording'];
            } else {
                $db->query("UPDATE `nv4_vi_crmbidding_telepro` SET `recording_isset` = '0' WHERE id = " . $view['id']);

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $src . $view['file']);
                curl_setopt($ch, CURLOPT_HEADER, 0);
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_exec($ch);
                $http_code = curl_getinfo($ch);
                curl_close($ch);
                if ($http_code['http_code'] == 200) {
                    $view['file_telepro'] = $src . $view['file'];
                }
            }
        } else {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $src . $view['file']);
            curl_setopt($ch, CURLOPT_HEADER, 0);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_exec($ch);
            $http_code = curl_getinfo($ch);
            curl_close($ch);
            if ($http_code['http_code'] == 200) {
                $view['file_telepro'] = $src . $view['file'];
            }
        }

        if ($view['file_telepro'] != '') {
            $xtpl->assign('AUDIO', $view['file_telepro']);
            $xtpl->parse('main.loop.recording');
        }

        $view['api_time'] = nv_date('H:i:s d/m/Y', $view['api_time']);
        $view['api_text'] = nv_htmlspecialchars($view['api_text']);

        $view['job_title'] = '';
        if (!empty($view['job_id']) && in_array($view['job_id'], array_keys($_array_telepro_jobs))) {
            $view['job_title'] = $_array_telepro_jobs[$view['job_id']]['title'];
        }

        $xtpl->assign('VIEW', $view);

        if ($view['job_id'] > 0 and $showheader) {
            if ($view['api_status'] == ApiConact::STATUS_PENDING) {
                $xtpl->parse('main.loop.status0');
            } elseif ($view['api_status'] == ApiConact::STATUS_PUSHED) {
                $xtpl->parse('main.loop.status1');
            } else {
                $xtpl->parse('main.loop.statuserror');
            }

            // Công cụ đối với liên hệ đã đồng bộ
            $tools = 0;
            if ($view['api_status'] == ApiConact::STATUS_PUSHED) {
                $tools++;
            }

            if ($tools > 0 and (defined('NV_IS_SPADMIN') or $crmbidding_admin_info['allowed_telepro'])) {
                $xtpl->parse('main.loop.tools');
            }
        }

        $xtpl->parse('main.loop');
    }
}

if ($showheader) {
    $xtpl->assign('NUMITEMS', number_format($num_items, 0, ',', '.'));

    // Xuất công việc
    $xtpl->assign('SELECTED_JOB_NONE', $array_search['job_id'] == 0 ? ' selected="selected"' : '');
    foreach ($array_telepro_jobs as $job) {
        $job['selected'] = $job['id'] == $array_search['job_id'] ? ' selected="selected"' : '';
        $xtpl->assign('JOB', $job);
        $xtpl->parse('main.form.job');
    }

    // Xuất trạng thái đồng bộ
    foreach ($array_sapi_status as $api_status => $api_status_lang) {
        $xtpl->assign('API_STATUS', [
            'key' => $api_status,
            'title' => $api_status_lang[1],
            'selected' => $api_status == $array_search['api_status'] ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.form.api_status');
    }

    // Các công cụ của điều hành chung trở lên
    if (defined('NV_IS_SPADMIN')) {
        $xtpl->parse('main.form.admin_links');
    }

    $xtpl->parse('main.form');
    $xtpl->parse('main.js_data');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('telepro');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';
