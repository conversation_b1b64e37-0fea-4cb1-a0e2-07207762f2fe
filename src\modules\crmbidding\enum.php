<?php

/**
 * <PERSON><PERSON>ch sử dụng enum
 * $array_data['status'] = EContractStatus::tryFrom($array_data['status'])?->getLabel() ?? ''; => Lấy ra label
 * $array_data['status'] = EContractStatus::Deleted->value; // So sánh giá trị
 */

/**
 * Trạng thái của hợp đồng
 */
enum EContractStatus: int
{
    case Deleted = -1;           // Đã xóa
    case Incomplete = 0;         // Chưa hoàn thiện
    case ContentIncomplete = 1;  // Nội dung chưa hoàn thiện
    case HSTDTSigned = 2;        // HSTDT đã ký
    case CustomerSigned = 3;     // Khách đã ký
    case TermsChanged = 4;       // Điều khoản đã được thay đổi
    case Done = 5;               // Hoàn thành ký kết
    case Cancel = 6;            // Hủy hợp đồng

    public function getLabel()
    {
        global $nv_Lang;
        return match ($this) {
            self::Deleted => $nv_Lang->getModule('contract_status_deleted'),
            self::Incomplete => $nv_Lang->getModule('contract_status_incomplete'),
            self::ContentIncomplete => $nv_Lang->getModule('contract_status_content_incomplete'),
            self::HSTDTSigned => $nv_Lang->getModule('contract_status_hstdt_signed'),
            self::CustomerSigned => $nv_Lang->getModule('contract_status_customer_signed'),
            self::TermsChanged => $nv_Lang->getModule('contract_status_terms_changed'),
            self::Done => $nv_Lang->getModule('contract_status_done'),
            self::Cancel => $nv_Lang->getModule('contract_status_cancel')
        };
    }
}

/**
 * Các giai đoạn của hợp đồng
 */
enum EContractStage: int
{
    case Negotiating = 0;            // Đang thương thảo nội dung
    case SupplementingInfo = 1;      // Bổ sung thông tin trong hợp đồng
    case CustomerSignatureRequired = 2;  // Cần chữ ký của KH
    case HSTDTSignatureRequired = 3;     // Cần chữ ký của HSTDT
    case CustomerContractReview = 4;     // Kiểm tra hợp đồng khách hàng
    case Done = 5;     // Hoàn thành ký kết

    public function getLabel()
    {
        global $nv_Lang;
        return match ($this) {
            self::Negotiating => $nv_Lang->getModule('contract_stage_negotiating'),
            self::SupplementingInfo => $nv_Lang->getModule('contract_stage_supplementing_info'),
            self::CustomerSignatureRequired => $nv_Lang->getModule('contract_stage_customer_signature_required'),
            self::HSTDTSignatureRequired => $nv_Lang->getModule('contract_stage_hstdt_signature_required'),
            self::CustomerContractReview => $nv_Lang->getModule('contract_stage_customer_contract_review'),
            self::Done => $nv_Lang->getModule('contract_stage_done'),
        };
    }
}
