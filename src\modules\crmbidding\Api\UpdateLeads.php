<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use NukeViet\Dauthau\Condition;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdateLeads implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'leads';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $array_site, $array_user_id_users, $array_groups_leads, $array_status, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $error = '';
        $error_code = '0000';
        $leadid = $nv_Request->get_int('leadsid', 'post', '0');
        if ($leadid > 0) {
            $row_old = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_leads WHERE id=' . $leadid)->fetch();
            if (empty($row_old)) {
                return $this->result->setError()
                    ->setCode('2004')
                    ->setMessage('Leads not exit')
                    ->getResult();
            }

            $admin_id = $nv_Request->get_int('admin_id', 'post', '0');

            $array_where = $nv_Request->get_array('where', 'post');
            if (!is_array($array_where)) {
                return $this->result->setError()
                    ->setCode('2000')
                    ->setMessage('Param where invalid to array')
                    ->getResult();
            }
            $keys_check = [
                'id',
                'user_id',
                'source_leads',
                'label',
                'name',
                'phone',
                'sub_phone',
                'email',
                'sub_email',
                'address',
                'tax',
                'status',
                'affilacate_id',
                'caregiver_id',
                'timecreate',
                'updatetime',
                'active',
                'last_comment',
                'schedule',
                'siteid',
                'activity_time'
            ];

            $where = array();
            if (!empty($array_where)) {
                foreach ($array_where as $keys) {
                    if (!empty($keys)) {
                        foreach ($keys as $key) {
                            $operator = array_key_first($key);
                            $field = array_key_first($key[$operator]);
                            if (!in_array($field, $keys_check)) {
                                return $this->result->setError()
                                    ->setCode('2001')
                                    ->setMessage('Missing field ' . $field . ' in data')
                                    ->getResult();
                            }
                        }
                    }
                }

                $condition = new Condition();
                $where[] = $condition->toSqlString($array_where);
            }

            $data = $nv_Request->get_array('data', 'post');
            if (!is_array($data)) {
                return $this->result->setError()
                    ->setCode('2000')
                    ->setMessage('Param data invalid to json')
                    ->getResult();
            }
            $row = $data;
            if (!empty($row['email'])) {
                $row['email'] = nv_strtolower($row['email']);
            }
            $keys_check = [
                'source_leads',
                'label',
                'user_id',
                'businessid',
                'teleproid',
                'name',
                'phone',
                'sub_phone',
                'email',
                'sub_email',
                'address',
                'tax',
                'company_name',
                'address_company',
                'status',
                'opportunities_id',
                'affilacate_id',
                'caregiver_id',
                'active',
                'about',
                'last_comment',
                'schedule',
                'is_check',
                'first_time',
                'siteid',
                'convert_contact',
                'convert_organization',
                'timecreate',
                'updatetime',
                'log_merge',
                'activity_time',
                'prefix_lang'
            ];
            if (!empty($data)) {
                // check $field
                $array_sql = [];
                foreach ($data as $key => $value) {
                    if (!in_array($key, $keys_check)) {
                        return $this->result->setError()
                            ->setCode('2002')
                            ->setMessage('Missing field ' . $key . ' in otherdata')
                            ->getResult();
                    }
                    $array_sql[$key] = $key . '=:' . $key;
                }

                if (isset($row['source_leads']) and empty($row['source_leads'])) {
                    $error_code = '2005';
                    $error = $nv_Lang->getModule('error_required_source_leads');
                } else if (isset($row['source_leads']) and !in_array($row['source_leads'], array_keys($array_groups_leads))) {
                    $error_code = '2006';
                    $error = $nv_Lang->getModule('source_leads_not_defined');
                } else if (isset($row['name']) and $row['name'] == '') {
                    $error_code = '2007';
                    $error = $nv_Lang->getModule('error_required_name');
                } else if (isset($row['phone']) and isset($row['email']) and $row['phone'] == '' and $row['email'] == '') {
                    $error_code = '2008';
                    $error = $nv_Lang->getModule('error_required_phone');
                } elseif (isset($row['phone']) and $row['phone'] != '' && !phonecheck($row['phone'], $row['prefix_lang'])) {
                    $error_code = '2009';
                    $error = $nv_Lang->getModule('error_phone_number');
                } elseif (isset($row['email']) and $row['email'] != '' && nv_check_valid_email($row['email']) != '') {
                    $error_code = '2010';
                    $error = $nv_Lang->getModule('error_email');
                } else if (isset($row['siteid']) && !in_array($row['siteid'], array_keys($array_site))) {
                    $error_code = '2013';
                    $error = $nv_Lang->getModule('siteid_not_defined');
                }

                if (!empty($row['source_leads'])) {
                    if ($row['source_leads'] != 2 and $row['source_leads'] != 7 and $row['source_leads'] != 4 and $row['source_leads'] != 11) {
                        $row['affilacate_id'] = 0;
                        $array_sql['affilacate_id'] = 'affilacate_id=:affilacate_id';
                    }
                }

                if (isset($row['sub_email']) and $row['sub_email'] != '') {
                    $row['sub_email'] = nv_strtolower($row['sub_email']);
                    $row['sub_email'] = str_replace(" ", "", $row['sub_email']);
                    $row['sub_email'] = str_replace(';', ',', $row['sub_email']);
                    $row['sub_email'] = str_replace("\n", ',', $row['sub_email']);
                    $_arr_email = array();
                    $list_mail = explode(',', $row['sub_email']);
                    foreach ($list_mail as $_mail) {
                        $_mail = trim($_mail);
                        if ((nv_check_valid_email($_mail)) != '') {
                            $error_code = '2011';
                            $error = sprintf($nv_Lang->getModule('error_subemail'), $_mail);
                        } elseif (!in_array($_mail, $_arr_email)) {
                            $_arr_email[] = $_mail;
                        }
                    }
                    $row['sub_email'] = implode(',', $_arr_email);
                }

                if (isset($row['phone']) and $row['phone'] != '') {
                    if (!isset($row['phone_search'])) {
                        $row['phone_search'] = 0;
                        if (isset($row['phone']) && preg_match('/(\d{9})$/', $row['phone'], $m)) {
                            $row['phone_search'] = $m[0];
                        }
                    }
                    $array_sql['phone_search'] = 'phone_search=:phone_search';
                } elseif (isset($row['phone']) and $row['phone'] == '' and $row_old['phone_search'] != '') {
                    $row['phone_search'] = 0;
                    $array_sql['phone_search'] = 'phone_search=:phone_search';
                }

                if (isset($row['sub_phone']) && $row['sub_phone'] != '') {
                    $row['sub_phone'] = str_replace(' ', '', $row['sub_phone']);
                    $row['sub_phone'] = str_replace(';', ',', $row['sub_phone']);
                    $row['sub_phone'] = str_replace("\n", ',', $row['sub_phone']);
                    $_arr_phone = $sub_phone_search = [];
                    $list_phone = explode(',', $row['sub_phone']);
                    foreach ($list_phone as $key => $_phone) {
                        $_phone = trim($_phone);
                        $_arr_phone[] = $_phone;
                        if (!phonecheck($_phone)) {
                            $error_code = '2007';
                            $error = sprintf($nv_Lang->getModule('error_sub_phone'), $_phone);
                        } else {
                            if (preg_match('/(\d{9})$/', $_phone, $m)) {
                                $sub_phone_search[$key] = $m[0];
                            }
                        }
                    }
                    $row['sub_phone'] = implode(',', $_arr_phone);
                    if (!isset($row['sub_phone_search'])) {
                        $row['sub_phone_search'] = implode(',', $sub_phone_search);
                        $array_sql['sub_phone_search'] = 'sub_phone_search=:sub_phone_search';
                    }
                } elseif (isset($row['sub_phone']) and $row['sub_phone'] == '' and $row_old['sub_phone_search'] != '') {
                    $row['sub_phone_search'] = '';
                    $array_sql['sub_phone_search'] = 'sub_phone_search=:sub_phone_search';
                }

                if (isset($row['tax']) && !empty($row['tax']) and taxcodecheck2($row['tax']) == false) {
                    $error = $nv_Lang->getModule('error_tax');
                    $error_code = '2014';
                }

                if (empty($error)) {
                    try {
                        $current_time = NV_CURRENTTIME;
                        $updatetime = isset($data['updatetime']) ? $data['updatetime'] : $current_time;
                        $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_leads SET ' . implode(',', $array_sql) . ', updatetime=' . $updatetime . ', elasticsearch = 0 WHERE id=' . $leadid;
                        if (!empty($where)) {
                            $sql .= ' AND ' . implode(' AND ', $where);
                        }
                        $stmt = $db->prepare($sql);
                        $field_changed = 0;
                        foreach ($row as $key => $value) {
                            $stmt->bindValue(':' . $key, $value, PDO::PARAM_STR);
                            if ($row_old[$key] != $value) {
                                ++$field_changed;
                            }
                        }
                        $exc = $stmt->execute();

                        // Cập nhật lại activetime nếu updatetime > activetime
                        if ($current_time > $row_old['activity_time'] && $field_changed > 0) {
                            $sql = $db->query('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_leads SET activity_time = ' . $current_time . ' WHERE id = ' . $leadid);
                        }

                        if ($exc) {
                            // ghi log
                            $log_data = [
                                $nv_Lang->getModule('log_update_leads_info')
                            ];

                            foreach ($row as $key => $value) {
                                if ($key == 'status') {
                                    if ($row_old['status'] != $row['status']) {
                                        $log_data[] = [
                                            'Trạng thái:',
                                            $array_status[$row_old['status']] . ' =&gt; ' . $array_status[$row['status']]
                                        ];
                                    }
                                } else if ($key == 'affilacate_id') {
                                    if ($row_old['affilacate_id'] != $row['affilacate_id']) {
                                        $log_data[] = [
                                            'Người giới thiệu:',
                                            nv_show_name_user($array_user_id_users[$row_old['affilacate_id']]['first_name'], $array_user_id_users[$row_old['affilacate_id']]['last_name'], $array_user_id_users[$row_old['affilacate_id']]['username']) . ' =&gt; ' . nv_show_name_user($array_user_id_users[$row['affilacate_id']]['first_name'], $array_user_id_users[$row['affilacate_id']]['last_name'], $array_user_id_users[$row['affilacate_id']]['username'])
                                        ];
                                    }
                                } else if ($key == 'caregiver_id') {
                                    if ($row['caregiver_id'] != 0 && $row_old['caregiver_id'] != 0 && $row_old['caregiver_id'] != $row['caregiver_id']) {
                                        $log_data[] = [
                                            'Người chăm sóc:',
                                            nv_show_name_user($array_user_id_users[$row_old['caregiver_id']]['first_name'], $array_user_id_users[$row_old['caregiver_id']]['last_name'], $array_user_id_users[$row_old['caregiver_id']]['username']) . ' =&gt; ' . nv_show_name_user($array_user_id_users[$row['caregiver_id']]['first_name'], $array_user_id_users[$row['caregiver_id']]['last_name'], $array_user_id_users[$row['caregiver_id']]['username'])
                                        ];
                                    }
                                } else if ($key == 'source_leads') {
                                    if ($row_old['source_leads'] != $row['source_leads']) {
                                        $log_data[] = [
                                            'Nguồn leads: ',
                                            $array_groups_leads[$row_old['source_leads']]['title'] . ' =&gt; ' . $array_groups_leads[$row['source_leads']]['title']
                                        ];
                                    }
                                } else if (in_array($key, ['first_time', 'timecreate', 'updatetime', 'activity_time', 'last_comment', 'schedule'])) {
                                    if ($row_old['first_time'] != $row['first_time'] || $row_old['timecreate'] != $row['timecreate'] || $row_old['updatetime'] != $row['updatetime'] || $row_old['activity_time'] != $row['activity_time'] || $row_old['last_comment'] != $row['last_comment'] || $row_old['schedule'] != $row['schedule']) {
                                        $log_data[] = [
                                            $nv_Lang->getModule($key),
                                            nv_date('H:i:s d/m/Y', $row_old[$key]) . ' =&gt; ' . nv_date('H:i:s d/m/Y', $row[$key])
                                        ];
                                    }
                                } else {
                                    if ($row_old[$key] != $row[$key]) {
                                        if (isset($row_old[$key]) && isset($row[$key])) {
                                            $log_data[] = [
                                                $nv_Lang->getModule($key),
                                                $row_old[$key] . ' =&gt; ' . $row[$key]
                                            ];
                                        }
                                    }
                                }
                            }

                            if (sizeof($log_data) > 1) { // không có gì thay đổi thì k ghi log
                                $sql = "INSERT INTO " . NV_PREFIXLANG . '_' . $module_data . "_alllogs (userid, log_area, log_key, log_time, log_data, leads_id) VALUES (" . $admin_id . ", 1, 'LOG_ADMIN_UPDATE_LEADS_INFO', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $leadid . ")";
                                $db->query($sql);
                            }
                            $nv_Cache->delMod($module_name);
                            nv_insert_logs(NV_LANG_DATA, $module_name, 'Edit Leads Info', 'ID: ' . $leadid, $admin_id);

                            // gọi code đẩy dữ liệu lên ES luôn
                            update_es();

                            $this->result->setSuccess();
                            $this->result->set('leadsid', $leadid);
                        } else {
                            $this->result->setError()
                                ->setCode('3001')
                                ->setMessage('Update error');
                        }
                    } catch (PDOException $e) {
                        $this->result->setError()
                            ->setCode('3000')
                            ->setMessage(print_r($e, true));
                    }
                } else {
                    return $this->result->setError()
                        ->setCode($error_code)
                        ->setMessage($error)
                        ->getResult();
                }
            } else {
                return $this->result->setError()
                    ->setCode('2001')
                    ->setMessage('Param data empty')
                    ->getResult();
            }
        } else {
            return $this->result->setError()
                ->setCode('2003')
                ->setMessage('Leadid is integer, more than 0' . $leadid)
                ->getResult();
        }

        return $this->result->getResult();
    }
}
