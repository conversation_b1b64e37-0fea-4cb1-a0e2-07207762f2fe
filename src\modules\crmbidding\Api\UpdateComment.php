<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use NukeViet\Dauthau\Condition;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdateComment implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'comment';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $error = '';
        $error_code = '0000';
        $comentid = $nv_Request->get_int('comentid', 'post', '0');
        if ($comentid > 0) {
            $row_old = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_comment WHERE id=' . $comentid)->fetch();
            if (empty($row_old)) {
                return $this->result->setError()
                    ->setCode('2004')
                    ->setMessage('Comment not exit')
                    ->getResult();
            }

            $array_where = $nv_Request->get_array('where', 'post');
            if (!is_array($array_where)) {
                return $this->result->setError()
                    ->setCode('2000')
                    ->setMessage('Param where invalid to array')
                    ->getResult();
            }
            $keys_check = [
                'source',
                'sourceid',
                'post_id',
                'timecreate',
                'update_time',
                'note',
                'schedule',
                'vip',
                'type_export',
                'id_mail'
            ];

            $where = array();
            if (!empty($array_where)) {
                foreach ($array_where as $keys) {
                    foreach ($keys as $key) {
                        $operator = array_key_first($key);
                        $field = array_key_first($key[$operator]);
                        if (!in_array($field, $keys_check)) {
                            return $this->result->setError()
                                ->setCode('2001')
                                ->setMessage('Missing field ' . $field . ' in data')
                                ->getResult();
                        }
                    }
                }

                $condition = new Condition();
                $where[] = $condition->toSqlString($array_where);
            }

            $data = $nv_Request->get_array('data', 'post');
            if (!is_array($data)) {
                return $this->result->setError()
                    ->setCode('2000')
                    ->setMessage('Param data invalid to json')
                    ->getResult();
            }
            $row = $data;
            $keys_check = [
                'source',
                'sourceid',
                'post_id',
                'note',
                'schedule',
                'vip',
                'type_export',
                'id_mail'
            ];
            if (!empty($data)) {
                // check $field
                $array_sql = [];
                foreach ($data as $key => $value) {
                    if (!in_array($key, $keys_check)) {
                        return $this->result->setError()
                            ->setCode('2002')
                            ->setMessage('Missing field ' . $key . ' in otherdata')
                            ->getResult();
                    }
                    $array_sql[$key] = $key . '=:' . $key;
                }

                if (empty($error)) {
                    try {
                        $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_comment SET ' . implode(',', $array_sql) . ', update_time=' . NV_CURRENTTIME . ' WHERE id=' . $comentid;
                        if (!empty($where)) {
                            $sql .= ' AND ' . implode(' AND ', $where);
                        }
                        $stmt = $db->prepare($sql);

                        foreach ($row as $key => $value) {
                            $stmt->bindValue(':' . $key, $value, PDO::PARAM_STR);
                        }
                        $exc = $stmt->execute();
                        if ($exc) {
                            $this->result->setSuccess();
                            $this->result->set('comentid', $comentid);
                        } else {
                            $this->result->setError()
                                ->setCode('3001')
                                ->setMessage('Update error');
                        }
                    } catch (PDOException $e) {
                        $this->result->setError()
                            ->setCode('3000')
                            ->setMessage(print_r($e, true));
                    }
                } else {
                    return $this->result->setError()
                        ->setCode($error_code)
                        ->setMessage($error)
                        ->getResult();
                }
            } else {
                return $this->result->setError()
                    ->setCode('2001')
                    ->setMessage('Param data empty')
                    ->getResult();
            }
        } else {
            return $this->result->setError()
                ->setCode('2003')
                ->setMessage('Comentid is integer, more than 0' . $comentid)
                ->getResult();
        }

        return $this->result->getResult();
    }
}
