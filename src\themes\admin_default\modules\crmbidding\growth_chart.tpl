<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<link type="text/css" href="{NV_BASE_SITEURL}themes/{TEMPLATE}/images/{MODULE_FILE}/plugins/apexcharts/apexcharts.css" rel="stylesheet">
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/{TEMPLATE}/images/{MODULE_FILE}/plugins/apexcharts/apexcharts.min.js"></script>
<style>
.ui-datepicker-calendar {
    display: none;
}
</style>
<div class="panel">
	<form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline">
		<input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}"> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}"> <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}">
		<div class="form-group">
			<select class="form-control" name="type_chart" id="type_chart">
				<option value="1" {TYPE_CHART1}>{LANG.type_chart_1}</option>
				<option value="2" {TYPE_CHART2}>{LANG.type_chart_2}</option>
			</select>
			<div id="from_month" class="form-group">
				<label>{LANG.from}:</label> <input class="form-control w200 uidatepicker" type="text" value="{ARRAY_SEARCH.time_from}" name="time_from" maxlength="10" autocomplete="off">
			</div>
			<div id="to_month" class="form-group">
				<label>{LANG.to}:</label> <input class="form-control w200 uidatepicker" type="text" value="{ARRAY_SEARCH.time_to}" name="time_to" maxlength="10" autocomplete="off">
			</div>
			<div id="single_month" class="form-group hidden">
				<label>{LANG.month}:</label> <input class="form-control w200 uidatepicker" type="text" value="{ARRAY_SEARCH.single_month}" name="single_month" maxlength="10" autocomplete="off">
			</div>
		</div>
		<div class="form-group">
			<input class="btn btn-primary" type="submit" value="{LANG.search_submit}">
		</div>
	</form>
</div>

<!-- BEGIN: chartline -->
<h1 class="text-center">{LANG.type_chart_1}</h1>
<div id="chartline"></div>
<script type="text/javascript">
$(document).ready(function() {
    var options = {
        series: {DATA_LINE},
        chart: {
          height: 350,
          type: 'line',
          zoom: {
            enabled: false
          },
        },
        stroke: {
          width: 2
        },
        colors: ['#2E93fA', '#66DA26', '#546E7A', '#E91E63', '#FF9800', '#f54e42', '#f5f542', '#035419', '#42f5ad', '#42cef5','#4251f5','#d442f5','#f542a4'],
        dataLabels: {
          enabled: false
        },
        legend: {
          tooltipHoverFormatter: function(val, opts) {
            return val + ' - ' + opts.w.globals.series[opts.seriesIndex][opts.dataPointIndex] + ''
          }
        },
        markers: {
          size: 2,
          hover: {
            sizeOffset: 5
          }
        },
        xaxis: {
          categories: {LIST_MONTH},
        },
        tooltip: {
          y: [
            {
              title: {
                formatter: function (val) {
                  return val;
                }
              }
            },
            {
              title: {
                formatter: function (val) {
                  return val;
                }
              }
            },
            {
              title: {
                formatter: function (val) {
                  return val;
                }
              }
            }
          ]
        },
        grid: {
          borderColor: '#f1f1f1',
        }
    };
    var chart = new ApexCharts(document.querySelector("#chartline"), options);
    chart.render();

});
</script>
<!-- END: chartline -->
<!-- BEGIN: chartcolumn -->
<h1 class="text-center">{LANG.type_chart_2}</h1>
<div id="chartcolumn"></div>
<script type="text/javascript">
    console.log({DATA_COLUMN});
    var options = {
        series: [{
            name: '{LANG.doanh_so}',
            data: {DATA_COLUMN}
        }],
        chart: {
            height: 350,
            type: 'bar',
        },
        plotOptions: {
            bar: {
                borderRadius: 4, // bằng với border radius của các UI nukeviet
                columnWidth: '50%',
                dataLabels: {
                    position: 'top'
                }
            }
        },
        dataLabels: {
            enabled: true,
            style: {
              colors: ['#111', '#555', '#999']
            },
            offsetY: -20,
            formatter: function (val, opts) {
                return  nv_separateComma(val);
            },
        },
        stroke: {
            width: 2
        },
        grid: {
            row: {
              colors: ['#fff', '#f2f2f2']
            }
        },
        yaxis: {
            labels: {
                formatter: function (value) {
                    return nv_separateComma(value);
                }
            }
        },
        xaxis: {
            labels: {
              rotate: -45
            },
            categories: {CATEGORIES},
            tickPlacement: 'on'
        },
        fill: {
            type: 'gradient',
            gradient: {
              shade: 'light',
              type: "horizontal",
              shadeIntensity: 0.25,
              gradientToColors: undefined,
              inverseColors: true,
              opacityFrom: 0.85,
              opacityTo: 0.85,
              stops: [50, 0, 100]
            },
        }
    };
    var chart = new ApexCharts(document.querySelector("#chartcolumn"), options);
    chart.render();
</script>
<!-- END: chartcolumn -->
<script type="text/javascript">
	$(document).ready(function() {
    load_option_type_chart();
    $( "#type_chart" ).change(function() {
        load_option_type_chart();
    });
    function load_option_type_chart() {
        var type_chart = parseInt($('#type_chart').val());
        console.log(type_chart);
        if (type_chart == 1) {
            $('#from_month').removeClass('hidden');
            $('#to_month').removeClass('hidden');
            $('#single_month').addClass('hidden');
        }
        if (type_chart == 2) {
            $('#from_month').addClass('hidden');
            $('#to_month').addClass('hidden');
            $('#single_month').removeClass('hidden');
        }
    }
		$('.uidatepicker').datepicker({
			showOn : "both",
      dateFormat: 'mm/yy',
			language: "{NV_LANG_INTERFACE}",
			changeMonth : true,
			changeYear : true,
			showOtherMonths : true,
			buttonImage : nv_base_siteurl + "assets/images/calendar.gif",
			buttonImageOnly : true,
			onClose: function(dateText, inst) {
				$(this).datepicker('setDate', new Date(inst.selectedYear, inst.selectedMonth, 1));
			}
		});
	});
</script>
<!-- END: main -->