<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\wallet\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetOrderPaidDetails implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'order';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        require_once NV_ROOTDIR . '/modules/' . $module_file . '/init.php';

        $site_id = $nv_Request->get_absint('site_id', 'post', 0);
        $modname = $nv_Request->get_title('modname', 'post', '');
        $ids = $nv_Request->get_title('ids', 'post', '');

        $ids = array_map('trim', explode(',', $ids));
        $sql_in = [];
        foreach ($ids as $id) {
            if (!empty($id)) {
                $sql_in[] = $db->quote($id);
            }
        }
        if (empty($sql_in)) {
            return $this->result->setCode('1001')->setMessage($nv_Lang->getModule('apierr_no_orderid'))->getResult();
        }

        $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_orders
        WHERE site_id=" . $site_id . " AND order_mod=" . $db->quote($modname) . " AND order_id IN(" . implode(',', $sql_in) . ")";
        $result = $db->query($sql);

        $array = [];
        $transaction_ids = [];
        while ($row = $result->fetch()) {
            $transaction_id = 0;
            if (!empty($row['paid_id'])) {
                $transaction_id = intval(substr($row['paid_id'], 2));
                if (!empty($transaction_id)) {
                    $transaction_ids[$transaction_id] = $transaction_id;
                }
            }

            $array[$row['order_id']] = [
                'id' => $row['id'],
                'money_amount' => $row['money_amount'],
                'money_unit' => $row['money_unit'],
                'paid_status' => $row['paid_status'],
                'paid_id' => $row['paid_id'],
                'paid_time' => $row['paid_time'],
                'transaction_id' => $transaction_id,
                'transaction' => []
            ];
        }

        $array_transactions = [];
        if (!empty($transaction_ids)) {
            $sql = "SELECT id, created_time, transaction_id, transaction_type, transaction_status, transaction_time, transaction_data,
            payment, is_expired
            FROM " . $db_config['prefix'] . "_" . $module_data . "_transaction
            WHERE id IN(" . implode(',', $transaction_ids) . ")";
            $result = $db->query($sql);

            while ($row = $result->fetch()) {
                $array_transactions[$row['id']] = $row;
            }
        }

        foreach ($array as $order_id => $order) {
            if (isset($array_transactions[$order['transaction_id']])) {
                $array[$order_id]['transaction'] = $array_transactions[$order['transaction_id']];
            }
        }

        $this->result->setSuccess();
        $this->result->set('data', $array);
        return $this->result->getResult();
    }
}
