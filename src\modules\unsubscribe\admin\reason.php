<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    exit('Stop!!!');
}

$page_title = $nv_Lang->getModule('reason');

// Sua cau hoi
if ($nv_Request->isset_request('edit', 'post')) {
    if (!defined('NV_IS_AJAX')) {
        exit('Wrong URL');
    }

    $qid = $nv_Request->get_int('qid', 'post', 0);
    $reason = $nv_Request->get_title('title', 'post', '', 1);

    if (empty($reason)) {
        exit('NO');
    }
    $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_name . '_reason SET
		reason= :reason, time_edit=' . NV_CURRENTTIME . '
		WHERE id=' . $qid);

    $stmt->bindParam(':reason', $reason, PDO::PARAM_STR, strlen($reason));
    if ($stmt->execute()) {
        nv_insert_logs(NV_LANG_DATA, $module_name, $nv_Lang->getModule('savereason'), 'id: ' . $qid . '; ' . $reason);
        exit('OK');
    }
    exit('NO');
}

// Them cau hoi
if ($nv_Request->isset_request('add', 'post')) {
    if (!defined('NV_IS_AJAX')) {
        exit('Wrong URL');
    }

    $title = $nv_Request->get_title('title', 'post', '', 1);
    if (empty($title)) {
        exit('NO');
    }

    $sql = 'SELECT MAX(weight) FROM ' . NV_PREFIXLANG . '_' . $module_name . "_reason";
    $weight = $db->query($sql)->fetchColumn();
    $weight = (int) $weight + 1;
    $_sql = 'INSERT INTO ' . NV_PREFIXLANG . '_' . $module_name . "_reason
		(reason, weight, time_add, time_edit) VALUES
		( :reason, " . $weight . ', ' . NV_CURRENTTIME . ', ' . NV_CURRENTTIME . ')';

    $data_insert = [];
    $data_insert['reason'] = $title;
    if ($db->insert_id($_sql, 'id', $data_insert)) {
        nv_insert_logs(NV_LANG_DATA, $module_name, $nv_Lang->getModule('addreason'), $title);
        exit('OK');
    }
    exit('NO' . $_sql);
}

// Chinh thu tu
if ($nv_Request->isset_request('changeweight', 'post')) {
    if (!defined('NV_IS_AJAX')) {
        exit('Wrong URL');
    }

    $qid = $nv_Request->get_int('qid', 'post', 0);
    $new_vid = $nv_Request->get_int('new_vid', 'post', 0);

    $query = 'SELECT COUNT(*) FROM ' . NV_PREFIXLANG . '_' . $module_name . '_reason WHERE id=' . $qid;
    $numrows = $db->query($query)->fetchColumn();
    if ($numrows != 1) {
        exit('NO');
    }

    $query = 'SELECT id FROM ' . NV_PREFIXLANG . '_' . $module_name . '_reason WHERE id!=' . $qid . " ORDER BY weight ASC";
    $result = $db->query($query);
    $weight = 0;
    while ($row = $result->fetch()) {
        ++$weight;
        if ($weight == $new_vid) {
            ++$weight;
        }
        $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_name . '_reason SET weight=' . $weight . ' WHERE id=' . $row['id'];
        $db->query($sql);
    }
    $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_name . '_reason SET weight=' . $new_vid . ' WHERE id=' . $qid;
    $db->query($sql);
    exit('OK');
}

// Xoa cau hoi
if ($nv_Request->isset_request('del', 'post')) {
    if (!defined('NV_IS_AJAX')) {
        exit('Wrong URL');
    }

    $qid = $nv_Request->get_int('qid', 'post', 0);

    list($qid, $title) = $db->query('SELECT id, reason FROM ' . NV_PREFIXLANG . '_' . $module_name . '_reason WHERE id=' . $qid)->fetch(3);

    if ($qid) {
        $sql = 'DELETE FROM ' . NV_PREFIXLANG . '_' . $module_name . '_reason WHERE id=' . $qid;
        if ($db->exec($sql)) {
            nv_insert_logs(NV_LANG_DATA, $module_name, $nv_Lang->getModule('deletereason'), 'id: ' . $qid . '; ' . $title);

            // fix weight question
            $sql = 'SELECT id FROM ' . NV_PREFIXLANG . '_' . $module_name . "_reason ORDER BY weight ASC";
            $result = $db->query($sql);
            $weight = 0;
            while ($row = $result->fetch()) {
                ++$weight;
                $sql = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_name . '_reason SET weight=' . $weight . ' WHERE id=' . $row['id'];
                $db->query($sql);
            }
            $result->closeCursor();
            exit('OK');
        }
    }
    exit('NO');
}

$xtpl = new XTemplate('reason.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

// Danh sach cau hoi
if ($nv_Request->isset_request('qlist', 'post')) {
    if (!defined('NV_IS_AJAX')) {
        exit('Wrong URL');
    }

    $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_name . "_reason ORDER BY weight ASC";
    $_rows = $db->query($sql)->fetchAll();
    $num = sizeof($_rows);
    if ($num) {
        foreach ($_rows as $row) {
            $xtpl->assign('ROW', [
                'qid' => $row['id'],
                'title' => $row['reason']
            ]);

            for ($i = 1; $i <= $num; ++$i) {
                $xtpl->assign('WEIGHT', [
                    'key' => $i,
                    'title' => $i,
                    'selected' => $i == $row['weight'] ? ' selected="selected"' : ''
                ]);
                $xtpl->parse('main.data.loop.weight');
            }

            $xtpl->parse('main.data.loop');
        }

        $xtpl->parse('main.data');
    }

    $xtpl->parse('main');
    $contents = $xtpl->text('main');

    include NV_ROOTDIR . '/includes/header.php';
    echo $contents;
    include NV_ROOTDIR . '/includes/footer.php';
}

$xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
$xtpl->parse('load');
$contents = $xtpl->text('load');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
