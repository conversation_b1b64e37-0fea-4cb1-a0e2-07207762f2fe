<!-- BEGIN: main -->
<div class="row">
    <div class="panel panel-default panel-main">
        <div class="panel-heading">
            <b>{LANG.list_user_set_order}</b>
        </div>
        <div class="panel-body">
            <table class="table table-striped table-bordered table-hover" style="margin-bottom: 0px">
                <tbody>
                    <tr>
                        <td><b>{LANG.list_user_set_order_now}</b></td>
                        <td>
                            <!-- BEGIN: loop_users --> {USERS.username}, <!-- END: loop_users -->
                        </td>
                    </tr>
                    <tr>
                        <td><b><a href="{LINK_LEADS_LOG_VIP}">{LANG.sale_order_to}</a></b></td>
                        <td>
                            <!-- BEGIN: sale_order_to --> {SALE_ORDER_TO.username}, <!-- END: sale_order_to -->
                        </td>
                    </tr>
                    <tr>
                        <td><b><a href="{LINK_LEADS_LOG_VIEWEB}">{LANG.sale_order_to_vieweb}</a></b></td>
                        <td>
                            <!-- BEGIN: sale_order_to_vieweb --> {SALE_ORDER_TO_VIEWEB.username}, <!-- END: sale_order_to_vieweb -->
                        </td>
                    </tr>
                    <tr>
                        <td><b><a href="{LINK_LEADS_LOG_APIPRO}">{LANG.api_pro_to_sale}</a></b></td>
                        <td>
                            <!-- BEGIN: api_pro_to_sale --> {API_PRO_TO_SALE.username}, <!-- END: api_pro_to_sale -->
                        </td>
                    </tr>
                    <tr>
                        <td><b><a href="{LINK_LEADS_LOG_ORDER_DTNET}">{LANG.sale_order_dtnet_to}</a></b></td>
                        <td>
                            <!-- BEGIN: sale_order_dtnet_to --> {SALE_ORDER_DTNET_TO.username}, <!-- END: sale_order_dtnet_to -->
                        </td>
                    </tr>
                    <tr>
                        <td><b><a href="{LINK_LEADS_LOG_TRANS_WALLET}">{LANG.share_transaction_wallet_to}</a></b></td>
                        <td>
                            <!-- BEGIN: share_transaction_wallet_to --> {SHARE_TRANSACTION_WALLET_TO.username}, <!-- END: share_transaction_wallet_to -->
                        </td>
                    </tr>
                    
                    <tr>
                        <td><b>{LANG.list_user_share_mess_zalo_now}</b></td>
                        <td>
                            <!-- BEGIN: loop_users_mess_zalo --> {USERS_MESS_ZALO.username}, <!-- END: loop_users_mess_zalo -->
                        </td>
                    </tr>
                    <tr>
                        <td><b><a href="{LINK_LEADS_LOG_SHARE_MESSAGE}">{LANG.leads_messenger_to_sale}</a></b></td>
                        <td>
                            <!-- BEGIN: leads_messenger_to_sale --> {LEADS_MESSENGER_TO_SALE.username}, <!-- END: leads_messenger_to_sale -->
                        </td>
                    </tr>
                    <tr>
                        <td><b><a href="{LINK_LEADS_LOG_SHARE_ZALO}">{LANG.leads_zalo_to_sale}</a></b></td>
                        <td>
                            <!-- BEGIN: leads_zalo_to_sale --> {LEADS_ZALO_TO_SALE.username}, <!-- END: leads_zalo_to_sale -->
                        </td>
                    </tr>
                    <tr>
                        <td><b>{LANG.list_sale_view_zalo}</a></b></td>
                        <td>
                            <!-- BEGIN: list_sale_view_zalo --> {LIST_SALE_VIEW_ZALO}, <!-- END: list_sale_view_zalo -->
                        </td>
                    </tr>
                    
                    <tr>
                        <td><b>{CURRENT_LANG}</b></td>
                        <td>
                            <!-- BEGIN: sale_view_zalo_ca1 --> {CURRENTSHIFTSALES}, <!-- END: sale_view_zalo_ca1 -->
                        </td>
                    </tr>
                    <tr>
                        <td><b>{NEXT_LANG}</b></td>
                        <td>
                            <!-- BEGIN: sale_view_zalo_ca2 --> {NEXTSHIFTSALES}, <!-- END: sale_view_zalo_ca2 -->
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="row">
    <div class="panel panel-default col-md-7 panel-main">
        <div class="panel-heading">
            <b>{LANG.leads_top10}</b>
        </div>
        <div class="panel-body">
            <!-- BEGIN: leads -->
            <table class="table table-striped table-bordered table-hover" style="margin-bottom: 0px">
                <thead>
                    <tr>
                        <th class="w150">{LANG.name}</th>
                        <th class="w100">{LANG.source_leads}</th>
                        <th class="w100">{LANG.last_comment}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop -->
                    <tr>
                        <td><a href="{VIEW.link_view}"><i class="fa fa-address-book"></i> {VIEW.name} </a></td>
                        <td>{VIEW.source_leads}</td>
                        <td>{VIEW.last_comment}</td>
                    </tr>
                    <!-- END: loop -->
                </tbody>
                <tfoot>
                    <tr>
                        <td class="text-right" colspan="3"><a href="{link_view_all}">{LANG.view_all}</a></td>
                    </tr>
                </tfoot>
            </table>
            <!-- END: leads -->
            <!-- BEGIN: empty_leads -->
            <p class="col-md-24">{LANG.empty_leads}</p>
            <!-- END: empty_leads -->
        </div>
    </div>

    <div class="panel panel-default col-md-offset-1 col-md-7 panel-main">
        <div class="panel-heading">
            <b>{LANG.opportunities_top10}</b>
        </div>
        <div class="panel-body">
            <!-- BEGIN: opportunities -->
            <table class="table table-striped table-bordered table-hover" style="margin-bottom: 0px">
                <thead>
                    <tr>
                        <th class="w150">{LANG.name}</th>
                        <th class="w100">{LANG.source_leads}</th>
                        <th class="w100">{LANG.last_comment}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop -->
                    <tr>
                        <td><a href="{VIEW_OP.link_view}"><i class="fa fa-address-book"></i> {VIEW_OP.name} </a></td>
                        <td>{VIEW_OP.source_leads}</td>
                        <td>{VIEW_OP.last_comment}</td>
                    </tr>
                    <!-- END: loop -->
                </tbody>
                <tfoot>
                    <tr>
                        <td class="text-right" colspan="3"><a href="{link_view_all}">{LANG.view_all}</a></td>
                    </tr>
                </tfoot>
            </table>
            <!-- END: opportunities -->
            <!-- BEGIN: empty_opportunities -->
            <p class="col-md-24">{LANG.no_change}</p>
            <!-- END: empty_opportunities -->
        </div>
    </div>

    <div class="panel panel-default col-md-offset-1 col-md-7 panel-main">
        <div class="panel-heading row">
            <div class="col-md-12">
                <b>{LANG.history}</b>
            </div>
            <div class="col-md-12">
                <select class="form-control" name="typeviewhistory" id="typeviewhistory" onchange="get_history_body();">
                    <option value="0">{LANG.tat_ca}</option>
                    <option value="1">{LANG.leads}</option>
                    <option value="2">{LANG.change}</option>
                    <option value="3">{LANG.payment}</option>
                    <option value="4">{LANG.customs}</option>
                </select>
            </div>
        </div>
        <div class="panel-body" id="history_body" style="height: 450px; overflow: scroll;"></div>
    </div>
</div>

<div class="row">
    <script type="text/javascript" src="{ASSETS_STATIC_URL}/js/chart/chart.js"></script>
    <div class="panel panel-default col-md-7 panel-main">
        <div class="panel-heading">
            <b>{LANG.leads_status}</b>
        </div>
        <div class="panel-body">
            <div class="text-left row">
                <div id="canvas-holder" class="col-md-24">
                    <canvas id="chart-area" width="300" height="400"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="panel panel-default col-md-offset-1 col-md-7 panel-main">
        <div class="panel-heading">
            <b>{LANG.oppotunities_status}</b>
        </div>
        <div class="panel-body">
            <div class="text-left row">
                <div id="canvas-holder" class="col-md-24">
                    <canvas id="chart-area1" width="300" height="400"></canvas>
                </div>
            </div>
        </div>
    </div>
    <!-- BEGIN: static_number -->
    <div class="panel panel-default col-md-offset-1 col-md-7 panel-main">
        <div class="panel-heading">
            <b>{LANG.static_number}</b>
        </div>
        <div class="panel-body">
            <table class="table table-striped table-bordered table-hover text-center" style="margin-bottom: 0px">
                <thead>
                    <tr>
                        <th></th>
                        <th>{LANG.doanh_so}</th>
                        <th>{LANG.thuc_nhan}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <th><a href="{ARR_STATIC.all.link}">{LANG.total_income}</a></th>
                        <td>{ARR_STATIC.all.total_money}</td>
                        <td>{ARR_STATIC.all.total_end}</td>
                    </tr>
                    <tr>
                        <th><a href="{ARR_STATIC.year.link}">{LANG.this_year}</a></th>
                        <td>{ARR_STATIC.year.total_money}</td>
                        <td>{ARR_STATIC.year.total_end}</td>
                    </tr>
                    <tr>
                        <th><a href="{ARR_STATIC.quy.link}">{LANG.this_quater}</a></th>
                        <td>{ARR_STATIC.quy.total_money}</td>
                        <td>{ARR_STATIC.quy.total_end}</td>
                    </tr>
                    <tr>
                        <th><a href="{ARR_STATIC.lastmonth.link}">{LANG.last_month}</a></th>
                        <td>{ARR_STATIC.lastmonth.total_money}</td>
                        <td>{ARR_STATIC.lastmonth.total_end}</td>
                    </tr>
                    <tr>
                        <th><a href="{ARR_STATIC.month.link}">{LANG.this_month}</a></th>
                        <td>{ARR_STATIC.month.total_money}</td>
                        <td>{ARR_STATIC.month.total_end}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <!-- END: static_number -->

    <!-- BEGIN: email_dead -->
    <div class="panel panel-default col-md-offset-1 col-md-7 panel-main">
        <div class="panel-heading">
            <b>{LANG.10email_die}</b> <a href="{URL_BIDDING}" class="btn btn-link">{LANG.view_all}</a>
        </div>
        <div class="panel-body">
            <table class="table table-striped table-bordered table-hover text-center" style="margin-bottom: 0px">
                <thead>
                    <tr>
                        <th>{LANG.email}</th>
                        <th>{LANG.time_die}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop_email_dead -->
                    <tr>
                        <th><span data-toggle="tooltip" data-placement="left" title="{DATA.comments}">{DATA.email}</span></th>
                        <th><span>{DATA.date_added}</span></th>
                    </tr>
                    <!-- END: loop_email_dead -->
                </tbody>
            </table>
        </div>
    </div>
    <!-- END: email_dead -->
</div>

<script type="text/javascript">
    function get_history_body() {
        var typeviewhistory = $('#typeviewhistory').children("option:selected").val();
        $.post(script_name + '?' + nv_name_variable + '=crmbidding&' + nv_fc_variable + '=main&nocache=' + new Date().getTime(), 'typeviewhistory=' + typeviewhistory + '&viewhistory=1', function(res) {
            $('#history_body').empty();
            $('#history_body').html(res);
        });
        return;
    }

    window.chartColors = {
        red: 'rgb(255, 99, 132)',
        orange: 'rgb(255, 159, 64)',
        yellow: 'rgb(255, 205, 86)',
        green: 'rgb(75, 192, 192)',
        blue: 'rgb(54, 162, 235)',
        purple: 'rgb(153, 102, 255)',
        grey: 'rgb(201, 203, 207)'
    };

    var config = {
        type: 'pie',
        data: {
            datasets: [{
                data: [
                    {DATA_VALUE}
                ],
                backgroundColor: [
                    window.chartColors.red,
                    window.chartColors.green,
                    window.chartColors.yellow
                ],
                label: '{LANG.leads_status}'
            }],
            labels: [
                {DATA_LABEL}
            ]
        },
        options: {
            responsive: true,
            legend: {
                onClick: function(e, legendItem) {
                    window.location.href = script_name + '?' + nv_name_variable + '=crmbidding&' + nv_fc_variable + '=leads&status=' + (legendItem.index + 1);
                }
            }
        }
    };
    var config1 = {
        type: 'pie',
        data: {
            datasets: [{
                data: [
                    {DATA_VALUE_OPPOTUNITIES}
                ],
                backgroundColor: [
                    window.chartColors.red,
                    window.chartColors.green,
                    window.chartColors.yellow
                ],
                label: '{LANG.oppotunities_status}'
            }],
            labels: [
                {DATA_LABEL_OPPOTUNITIES}
            ]
        },
        options: {
            responsive: true,
            legend: {
                onClick: function(e, legendItem) {
                    window.location.href = script_name + '?' + nv_name_variable + '=crmbidding&' + nv_fc_variable + '=opportunities&status=' + (legendItem.index + 1);
                }
            }
        }
    };

    window.onload = function() {
        var ctx = document.getElementById('chart-area').getContext('2d');
        window.myPie = new Chart(ctx, config);

        var ctx1 = document.getElementById('chart-area1').getContext('2d');
        window.myPie = new Chart(ctx1, config1);

        get_history_body();
    };

    Chart.plugins.register({
        afterDatasetsDraw: function(chartInstance, easing) {
            // To only draw at the end of animation, check for easing === 1
            var ctx = chartInstance.chart.ctx;
            chartInstance.data.datasets.forEach(function(dataset, i) {
                var meta = chartInstance.getDatasetMeta(i);
                if (!meta.hidden) {
                    meta.data.forEach(function(element, index) {
                        // Draw the text in black, with the specified font
                        ctx.fillStyle = 'grey';
                        var fontSize = 16;
                        var fontStyle = 'normal';
                        var fontFamily = 'Helvetica Neue';
                        ctx.font = Chart.helpers.fontString(fontSize, fontStyle, fontFamily);
                        // Just naively convert to string for now
                        var dataString = dataset.data[index].toString();
                        // Make sure alignment settings are correct
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'middle';
                        var padding = 5;
                        var position = element.tooltipPosition();
                        ctx.fillText(dataString, position.x, position.y - (fontSize / 2) - padding);
                    });
                }
            });
        }
    });
</script>
<!-- END: main -->

<!-- BEGIN: viewhistory -->
<table class="table table-striped table-hover" style="margin-bottom: 0px">
    <tbody>
        <!-- BEGIN: loop -->
        <tr>
            <td>{LOG.icon}</td>
            <td>{LOG.content}</td>
            <td>{LOG.log_time}</td>
        </tr>
        <!-- END: loop -->
    </tbody>
</table>
<!-- END: viewhistory -->
