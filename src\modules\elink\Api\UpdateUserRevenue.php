<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\elink\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdateUserRevenue implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'promotion';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;
        $data_update = html_entity_decode($nv_Request->get_title('data_update', 'post', '{}', true));

        $data_update = json_decode($data_update, true);

        if (empty($data_update)) {
            $this->result->setError();
            return $this->result->setCode('1001')->setMessage('No data')->getResult();
        }

        foreach ($data_update as $key => $a) {
            $a['userid'] = (int) $a['userid'];
            $a['mode'] = (int) $a['mode'];
            if ($a['userid'] <= 0 || $a['mode'] < 0 || $a['amount'] <= 0 || sizeof(array_intersect(array_keys($a), ['userid', 'mode', 'amount'])) != 3) {
                unset($data_update[$key]);
            }
        }
        if (empty($data_update)) {
            $this->result->setError();
            return $this->result->setCode('1001')->setMessage('No data')->getResult();
        }
        try{
            foreach ($data_update as $data) {
                $db->exec('UPDATE ' . NV_USERS_GLOBALTABLE . ' SET total_revenue = total_revenue ' . ($data['mode'] == 0 ? '+' : '-') . $data['amount'] . ' WHERE userid = ' . $db->quote($data['userid']));   
            }
        } catch (\Exception $e) {
            trigger_error($e);
            $this->result->setError();
            return $this->result->setCode('1004')->setMessage($e->getMessage())->getResult();
        }

        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
