<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2023 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 27 Feb 2023 09:02:35 GMT
 */

use NukeViet\Dauthau\LangMulti;
use NukeViet\InForm\InForm;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE') or !defined('NV_IS_MODADMIN')) {
    die('Stop!!!');
}
require NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

define('NV_IS_FILE_ADMIN', true);

//Danh sách bộ phận theo user
$_sql = 'SELECT * from ' . $db_config['prefix'] . '_' . $module_data . '_cat_admins WHERE userid = ' . $admin_info['userid'];
$array_cat_admins = $nv_Cache->db($_sql, 'cat_admins', $module_name);

// Lấy đường dẫn ảnh
function get_image_src($image)
{
    global $module_upload, $global_config;
    if (empty($image)) {
        $image_src = NV_BASE_SITEURL . 'themes/' . $global_config['site_theme'] . '/images/no_image.gif';
    } else {
        $image_src = NV_BASE_SITEURL . NV_UPLOADS_DIR . '/' . $module_upload . '/' . $image;
    }

    return $image_src;
}

// Lấy ticket thông qua API
function get_ticket_by_id($ticket_id)
{
    global $admin_info, $module_name;

    $data = [];

    $params = [
        'id'        =>  $ticket_id,
        'userid'    =>  $admin_info['userid']
    ];
    // GỌI API
    $ticket = nv_local_api('GetTicket', $params, $admin_info['username'], $module_name);
    $ticket = json_decode($ticket, true);
    if ($ticket['status'] == 'success' and $ticket['canView']) {
        $data = $ticket;
    }

    return $data;
}

//Danh sách bộ phận của user
function get_cat_by_userid()
{
    global $array_cat_admins;
    $lis_cat_id = [];
    foreach ($array_cat_admins as $cat_id) {
        $lis_cat_id[] = $cat_id['cat_id'];
    }
    return $lis_cat_id;
}

//Render đánh giá đã được gửi
function get_customer_review_component($comment, $customer_id, $can_mark_ai)
{
    global $module_file, $nv_Lang, $module_config, $module_name, $global_config;
    $review_rating = [
        'display_rating' => 'block',
    ];
    $xtpl = new XTemplate('review.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file . '/detail');
    $comment['reply_time'] = nv_date('H:i:s d/m/Y', empty($comment['reply_edit_time']) ? $comment['reply_add_time'] : $comment['reply_edit_time']);
    $comment['customer_review'] = $nv_Lang->getModule('customer_review', get_user_info($customer_id)['fullname']);;
    $comment['message_refund'] = RefundStatus::tryFrom($comment['refund_status'])?->getLabel() ?? '';
    $comment['class_refund'] = RefundStatus::tryFrom($comment['refund_status'])?->getClass() ?? '';
    $comment['rating_time'] = nv_date('H:i:s d/m/Y', empty($comment['rating_edit_time']) ? $comment['rating_add_time'] : $comment['rating_edit_time']);

    $xtpl->assign('COMMENT', $comment);
    $xtpl->assign('RATING', $review_rating);
    $xtpl->assign('TOKEN', NV_CHECK_SESSION);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    for ($i = 1; $i <= 5; $i++) {
        $star = [
            'value' => $i,
            'checked' => (isset($comment['rating_number']) && $comment['rating_number'] == $i) ? 'checked="checked"' : '',
        ];
        $xtpl->assign('STAR', $star);
        $xtpl->parse('main.star');
    }
    if ($comment['rating_number'] > 0) {
        // Yêu cầu hoàn điểm
        if ($comment['refund_status'] == RefundStatus::Open->value && $comment['status'] == CommentStatus::Done->value) {
            $xtpl->parse('main.review.refund');
        }
        if (!empty($comment['rating_reply'])) {
            $xtpl->parse('main.review.admin_reply');
        }
        $xtpl->parse('main.review');
    }
    if ($comment['comment_type'] == CommentType::AI->value && $can_mark_ai) {
        $xtpl->assign('CHECK', $comment['invalid'] == 1 ? 'checked' : '');
        $xtpl->parse('main.mark_invalid');
    }
    $xtpl->parse('main');

    return $xtpl->text('main');
}

// Hàm xây dựng cây phân cấp từ mảng comments
function build_comment_tree($allComments, $parentId = 0)
{
    $branch = [];
    if (isset($allComments[$parentId])) {
        foreach ($allComments[$parentId] as $comment) {
            $children = build_comment_tree($allComments, $comment['log_id']);
            $comment['children'] = $children;
            $branch[] = $comment;
        }
        // Đưa câu TL bổ sung lên đầu tiên
        usort($branch, function ($a, $b) {
            if ($a['comment_type'] == CommentType::Expert->value && $b['comment_type'] != CommentType::Expert->value) {
                return -1;
            } elseif ($a['comment_type'] != CommentType::Expert->value && $b['comment_type'] == CommentType::Expert->value) {
                return 1;
            } else {
                return 0;
            }
        });
    }
    return $branch;
}

// Gắn cây phân cấp comment con vào comment cấp đầu tiên
function attach_comment_children($comments, $allComments)
{
    foreach ($comments as &$comment) {
        // Kiểm tra xem comment hiện tại có comment con không
        if (isset($allComments[$comment['log_id']])) {
            // Nếu có, gắn comment con và tiếp tục đệ quy
            $comment['children'] = build_comment_tree($allComments, $comment['log_id']);
        } else {
            // Nếu không có comment con, đặt children là một mảng trống
            $comment['children'] = [];
        }
    }
    return $comments;
}

//Render form review cho comment
function get_refund_form($comment, $rating)
{
    global $module_file, $global_config;
    $xtpl = new XTemplate('form-refund.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file . '/detail');
    $xtpl->assign('RATING', $rating);
    $xtpl->assign('COMMENT', $comment);
    $xtpl->assign('TOKEN', NV_CHECK_SESSION);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

    $refunds_status = [RefundStatus::Accept->value, RefundStatus::Refuse->value];
    foreach ($refunds_status as $status) {
        $xtpl->assign('OPTION', [
            'key' => $status,
            'title' => RefundStatus::tryFrom($status)->getLabel()
        ]);
        $xtpl->parse('main.loop');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

// Render form comment
function get_form_comment_additional($parent_id, $comment_parent_type, $min_point_offer)
{
    global $module_file, $nv_Lang, $global_config;

    if ($comment_parent_type == CommentType::Customer->value) {
        $is_comment_customer = CommentType::Expert->value;
        $point_offer = $min_point_offer;
    } else if ($comment_parent_type == CommentType::AI->value) {
        $is_comment_customer = CommentType::ExpertAdditionalForAI->value;
        $point_offer = 0;
    } else {
        $is_comment_customer = CommentType::ExpertAdditional->value;
        $point_offer = 0;
    }

    $xtpl = new XTemplate('form-comment.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file . '/detail');
    $xtpl->assign('TOKEN', NV_CHECK_SESSION);
    $xtpl->assign('COMMENT_TYPE', $is_comment_customer);
    $xtpl->assign('COMMENT_PARENT_ID', $parent_id);
    $xtpl->assign('MIN_POINT_EXPERT', $point_offer);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('TITLE_REPLY', $nv_Lang->getModule($is_comment_customer ? 'comment_content' : 'send_additional_comment'));

    // Nếu trả lời bổ sung cho yêu cầu của khách hàng thì có thêm điểm đề xuất
    if ($is_comment_customer == CommentType::Expert->value) {
        $xtpl->parse('main.expert_offer_point');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}
