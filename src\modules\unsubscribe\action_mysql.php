<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_MODULES')) {
    exit('Stop!!!');
}

$sql_drop_module = [];

// $sql_drop_module[] = 'DROP TABLE IF EXISTS ' . $db_config['prefix'] . '_' . $module_data . ';';
// $sql_drop_module[] = 'DROP TABLE IF EXISTS ' . $db_config['prefix'] . '_' . $lang . '_' . $module_data . '_reason;';

$sql_create_module = $sql_drop_module;

$sql_create_module[] = 'CREATE TABLE IF NOT EXISTS ' . $db_config['prefix'] . '_' . $module_data . " (
 id mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
 userid int NOT NULL,
 email varchar(255) NOT NULL DEFAULT '',
 type varchar(250) NOT NULL COMMENT 'Loại email khách unsubscribe',
 reason varchar(255) DEFAULT '' COMMENT 'Lý do khách unsubcribe',
 time_add int NOT NULL COMMENT 'Thời gian unsubscribe',
 PRIMARY KEY (id),
 INDEX(userid),
 INDEX(type),
 INDEX(time_add)
) ENGINE=InnoDB";

$sql_create_module[] = 'CREATE TABLE IF NOT EXISTS ' . $db_config['prefix'] . '_' . $lang . '_' . $module_data . '_reason (
 id mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
 reason varchar(1000) NOT NULL COMMENT "Lý do hiện sẵn cho khách chọn",
 weight tinyint unsigned NOT NULL,
 time_add int NOT NULL COMMENT "Thời gian thêm lý do",
 time_edit int NOT NULL COMMENT "Thời gian chỉnh sửa",
 PRIMARY KEY (id),
 INDEX(weight)
) ENGINE=InnoDB';
