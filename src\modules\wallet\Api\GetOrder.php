<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\wallet\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetOrder implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'order';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        require_once NV_ROOTDIR . '/modules/' . $module_file . '/init.php';

        $array = [];

        // Lấy các biến để xác định được đơn hàng
        $array['id'] = $nv_Request->get_absint('id', 'post', 0);
        $array['site_id'] = $nv_Request->get_absint('site_id', 'post', 0);
        $array['order_mod'] = $nv_Request->get_title('order_mod', 'post', '');
        $array['order_id'] = $nv_Request->get_title('order_id', 'post', '');

        // Tìm đơn hàng
        if (!empty($array['id'])) {
            $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_orders WHERE id=" . $array['id'];
        } else {
            $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_orders
            WHERE site_id=" . $array['site_id'] . " AND order_mod=" . $db->quote($array['order_mod']) . " AND
            order_id=" . $db->quote($array['order_id']);
        }
        $order = $db->query($sql)->fetch();
        if (empty($order)) {
            return $this->result->setCode('1001')->setMessage($nv_Lang->getModule('apierr_exists_order'))->getResult();
        }

        $this->result->set('data', $order);
        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
