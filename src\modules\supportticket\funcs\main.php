<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2023 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 27 Feb 2023 09:02:35 GMT
 */

if (!defined('NV_IS_MOD_SUPPORTTICKET')) {
    die('Stop!!!');
}

if (!defined('NV_IS_USER')) {
    $redirect = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name, true);
    Header("Location: " . NV_BASE_SITEURL . "index.php?" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($redirect));
    die();
}

// Lưu ý: Nếu nhận bất cứ array_op nào hoặc giá trị tìm kiếm hãy thiết đặt $home = 0
$home = defined('IS_DEV_LOCAL') ? 0 : 1;

$page_title = $module_config[$module_name]['sitename'];
$key_words = $module_info['keywords'];
$array_mod_title[] = array(
    'title' => $nv_Lang->getModule('tickets'),
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op, true)
);

$userid = $user_info['userid'];
$array_search = [];
$where = 'customer_id=' . $userid . ' AND delete_time=0';
$array_search['q'] = $nv_Request->get_title('q', 'get', '');
if (!empty($array_search['q'])) {
    $num = 0;
    $where .= ' AND (';
    $arr_key = explode(',', $array_search['q']);
    foreach ($arr_key as $key) {
        $key = trim($key);
        if ($num == 0) {
            $where .= "title LIKE " . $db->quote('%' . $key . '%');
        } else {
            $where .= " OR title LIKE " . $db->quote('%' . $key . '%');
        }
        $num++;
    }
    $where .= ')';
    $home = 0;
}
$array_search['cat'] = $nv_Request->get_int('cat', 'get', 0);
if (isset($array_ticket_cats[$array_search['cat']]) && !empty($array_search['cat'])) {
    $where .= ' AND cat_id=' . $array_search['cat'];
    $home = 0;
}
$array_search['status'] = $nv_Request->get_int('status', 'get', 0);
// Issue 2168: Cập nhật lại status ngoài site cho phù hợp
if (in_array($array_search['status'], $array_ticket_status) && !empty($array_search['status'])) {
    $where .= ' AND status=' . $array_search['status'];
    $home = 0;
}

$per_page = 20;
$page = $nv_Request->get_int('page', 'post,get', 1);
$db->sqlreset()
    ->select('COUNT(id)')
    ->from(TB_TICKET_ROW)
    ->where($where);
$sth = $db->prepare($db->sql());
$sth->execute();
$total = $sth->fetchColumn();

$db->select('*')
    ->order('activity_time DESC') // issue 2156: Sắp xếp hiển thị ticket ngoài site
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$sth = $db->prepare($db->sql());
$sth->execute();
$i = ($page - 1) * $per_page;
$array_data = [];

while ($view = $sth->fetch()) {
    $view['stt'] = $i + 1;
    $array_data[$view['id']] = $view;
    ++$i;
}

if ($page > 1 and empty($array_data)) {
    if (empty($array_data)) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
    }
    $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
}
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name;

$base_url .= !empty($array_search['q']) ? "&amp;q=" . urlencode($array_search['q']) : '';
$base_url .= !empty($array_search['cat']) ? "&amp;cat=" . $array_search['cat'] : '';
$base_url .= !empty($array_search['status']) ? "&amp;status=" . $array_search['status'] : '';
$base_url .= !empty($array_search['payment_status']) ? "&amp;payment_status=" . $array_search['payment_status'] : '';

$generate_page = nv_generate_page($base_url, $total, $per_page, $page);

// lấy page_url
$page_url = $base_url;
if ($page > 1) {
    $home = 0;
    $page_url .= '&amp;page=' . $page;
}
$canonicalUrl = getCanonicalUrl($page_url);
$urlappend = '&amp;page=';
// Kiểm tra đánh số trang
betweenURLs($page, ceil($total / $per_page), $base_url, $urlappend, $prevPage, $nextPage);

$contents = nv_theme_supportticket_main($array_data, $generate_page, $page, $per_page, $array_search);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
