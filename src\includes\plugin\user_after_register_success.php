<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

use NukeViet\Api\DoApi;

//$nv_hook_module = 'users';
//$nv_receive_module = 'users'; // Event của hệ thống và module nhận là users

$callback = function($vars, $from_data, $receive_data) {
    $module_name = 'users';

    /*
    $module_name = $receive_data['module_name'];
    $module_info = $receive_data['module_info'];
    $module_data = $module_info['module_data'];
     */

    $userid = $vars[0];
    $user_info = $vars[1];

    global $db, $db_config;

    // Lấy lại full thông tin thành viên
    $sql = 'SELECT * FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid=' . $userid;
    $user = $db->query($sql)->fetch();

    if (empty($user) or empty($user['lead_tn'])) {
        return;
    }
    $user['full_name'] = nv_show_name_user($user['first_name'], $user['last_name'], $user['username']);

    // Lấy tùy biến
    $sql = 'SELECT * FROM ' . NV_USERS_GLOBALTABLE . '_info WHERE userid=' . $userid;
    $user_customs = $db->query($sql)->fetch();
    if (empty($user_customs)) {
        $user_customs = [];
    }

    // Lấy thông tin người giới thiệu
    $sql = 'SELECT pri_uid FROM ' . $db_config['prefix'] . '_elink_affiliate_set WHERE pre_uid=' . $userid;
    $affilacate_id = $db->query($sql)->fetchColumn();
    if (!$affilacate_id) {
        $affilacate_id = 0;
    }

    $otherdata = [];
    $otherdata['user_id'] = $user['userid'];
    if (!empty($user_customs['mst'])) {
        $otherdata['tax'] = $user_customs['mst'];
    }
    $otherdata['affilacate_id'] = $affilacate_id;
    $otherdata['caregiver_id'] = $affilacate_id;

    // Gọi API tạo lead
    try {
        $api = new DoApi(API_CRM_URL, API_CRM_KEY, API_CRM_SECRET);
        $api->setModule('crmbidding')
        ->setLang('vi')
        ->setAction('CreateLeads')
        ->setData([
            'source_leads' => 13, // Nguồn thi trắc nghiệm
            'name' => $user['full_name'],
            'phone' => empty($user_customs['phone']) ? '' : $user_customs['phone'],
            'email' => $user['email'],
            'siteid' => 3, // tracnghiem.dauthau.asia
            'admin_id' => $affilacate_id,
            'otherdata' => $otherdata
        ]);
        $result = $api->execute();
        $error = $api->getError();
        if (!empty($error)) {
            trigger_error(print_r($error, true));
            nv_insert_logs(NV_LANG_DATA, $module_name, 'API_CREAT_LEAD_TN_ERROR', json_encode([$userid, $error]));
        } elseif ($result['status'] != 'success') {
            trigger_error($result['message']);
            nv_insert_logs(NV_LANG_DATA, $module_name, 'API_CREAT_LEAD_TN_ERROR', json_encode([$userid, $result]));
        } else {
            // Thành công, cập nhật lại thời gian đồng bộ
            $sql = 'UPDATE ' . NV_USERS_GLOBALTABLE . ' SET lead_tnsync=' . NV_CURRENTTIME . ' WHERE userid=' . $userid;
            $db->query($sql);
        }

        unset($api, $result, $error);
    } catch (Exception $e) {
        trigger_error(print_r($e, true));
        nv_insert_logs(NV_LANG_DATA, $module_name, 'API_CREAT_LEAD_TN_ERROR', $e->getMessage());
    }
};
nv_add_hook($module_name, 'user_after_register_success', $priority, $callback, $hook_module, $pid);
