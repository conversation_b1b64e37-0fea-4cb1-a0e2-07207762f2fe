<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class DeleteLeads implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'leads';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];

        $leadsid = $nv_Request->get_int('leadsid', 'post', '0');
        $admin_id = $nv_Request->get_int('admin_id', 'post', '0');

        if ($leadsid > 0) {
            try {
                $row = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_leads WHERE id=' . $leadsid)->fetch();
                if (empty($row)) {
                    $this->result->setSuccess()
                        ->setCode('4000')
                        ->setMessage($nv_Lang->getModule('api_error_400'));
                } else {
                    $query = 'DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_leads WHERE id=' . $leadsid;
                    $exec = $db->exec($query);
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Delete Leads', 'ID: ' . $leadsid, $admin_id);
                    if ($exec) {
                        $nv_Cache->delMod($module_name);
                        $this->result->setSuccess();
                        $this->result->set('leadid', $leadsid);
                    }
                }
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Leadid is integer, more than 0')
                ->getResult();
        }
        return $this->result->getResult();
    }
}
