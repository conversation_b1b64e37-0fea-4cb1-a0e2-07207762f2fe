<?xml version="1.0" encoding="utf-8"?>
<payment>
    <name><PERSON>h toán qua ATM</name>
    <domain>http://nukeviet.vn</domain>
    <images_button>[NV_BASE_SITEURL]themes/default/images/wallet/pay-amt.jpg</images_button>
    <config>
        <completemessage lang="en" lang_en="Notice after payment">Your information has been recorded. We will check this transaction as soon as possible. Thank you!</completemessage>
        <completemessage lang="vi" lang_vi="Thông báo sau khi thanh toán">Thông tin của bạn đã được ghi nhận. Chúng tôi sẽ kiểm tra giao dịch này trong thời gian sớm nhất. Xin cảm ơn!</completemessage>
        <acq_id lang_vi="Danh sách ID ngân hàng thanh toán" lang_en="List of paying bank IDs" note_vi="Phân cách nhau bởi dấu phảy. <PERSON><PERSON>y là mã BIN của ngân hàng, c<PERSON> thể tìm thấy ở https://api.vietqr.io/v1/banks. Nhập đầy đủ thông số này và hai thông số ở dưới, hệ thống sẽ hiển thị mã VietQR để thanh toán nhanh, nếu không cần bạn có thể bỏ qua" note_en="Separated by commas. This is the bank's BIN, which can be found at https://api.vietqr.io/v1/banks. Fill in this parameter and the two parameters below, the system will display VietQR code for quick payment, if you don't need it, you can skip it."></acq_id>
        <account_no lang_vi="Danh sách số tài khoản thụ hưởng" lang_en="List of beneficiary account numbers" note_vi="Phân cách nhau bởi dấu phảy. Đây là số tài khoản tương ứng với các ngân hàng bên trên. Mỗi số tài khoản chỉ nhập số, tối thiểu 6 ký tự, tối đa 19 kí tự" note_en="Separated by commas. This is the account number corresponding to the above banks. Each account number only enter numbers, minimum 6 characters, maximum 19 characters"></account_no>
        <account_name lang_vi="Danh sách tên tài khoản ngân hàng" lang_en="List of bank account names" note_vi="Phân cách nhau bởi dấu phảy. Đây là tên chủ tài khoản tương ứng với các số tài khoản bên trên. Mỗi tên chỉ nhập tiếng Việt không dấu, viết hoa, tối thiểu 5 ký tự, tối đa 50 kí tự, không chứa các ký tự đặc biệt" note_en="Separated by commas. This is the name of the account holder corresponding to the above account numbers. Each name can only enter Vietnamese without accents, capital letters, minimum 5 characters, maximum 50 characters, no special characters"></account_name>
    </config>
    <currency>VND</currency>
    <optional>1</optional>
</payment>
