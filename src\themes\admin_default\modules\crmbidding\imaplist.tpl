<!-- BEGIN: main -->
<form action="{NV_BASE_ADMINURL}index.php" method="get">
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}">
    <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
    <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}">
    <input type="hidden" name="viewlist" value="1">
    <div class="flex-search-form">
        <div class="simple-form">
            <div class="item item-q">
                <input type="text" class="form-control" name="q" value="{DATA_SEARCH.q}">
            </div>
            <div class="item item-account">
                <select class="form-control" name="a">
                    <option value="0">{LANG.imapstat_all_account}</option>
                    <!-- BEGIN: account -->
                    <option value="{ACCOUNT.key}"{ACCOUNT.selected}>{ACCOUNT.title}</option>
                    <!-- END: account -->
                </select>
            </div>
            <div class="item item-submit">
                <button type="submit" class="btn btn-primary"><i class="fa fa-search" aria-hidden="true"></i> {GLANG.search}</button>
            </div>
            <div class="item item-btns">
                <a href="{LINK_WORK}" class="btn btn-success"><i class="fa fa-code-fork" aria-hidden="true"></i> {LANG.imaplist_work}</a>
                <!-- BEGIN: link_stat -->
                <a href="{LINK_STAT}" class="btn btn-info"><i class="fa fa-pie-chart" aria-hidden="true"></i> {LANG.imapstat}</a>
                <!-- END: link_stat -->
            </div>
        </div>
    </div>
</form>
<div class="flex-table mb-4">
    <div class="flex-table-head">
        <div class="stt">{LANG.id}</div>
        <div class="name">{GLANG.email}</div>
        <div class="cw250">{LANG.imaplist_to}</div>
        <div class="status">{LANG.status}</div>
        <div class="tools"></div>
    </div>
    <div class="flex-table-body">
        <!-- BEGIN: loop -->
        <div class="dropdown dropdown-parent-item" id="dropdown-parent-item-{ROW.email_id}">
            <div class="item">
                <div class="stt"><span class="label-name mr-0">#</span>{ROW.email_id}</div>
                <div class="name">
                    <!-- BEGIN: menu1 -->
                    <a title="{LANG.select_action}" href="#menutool-{ROW.email_id}" data-parent="#dropdown-parent-item-{ROW.email_id}" class="menu-icon-tools btn btn-default btn-xs btn-xs-fixed"><i class="fa fa-caret-down" aria-hidden="true"></i></a>
                    <!-- END: menu1 -->
                    <div><strong class="text-primary">{ROW.subject}</strong></div>
                    <div><small class="text-muted"><i class="fa fa-server" aria-hidden="true"></i> {ROW.from_host}</small></div>
                    <div>
                        <!-- BEGIN: sender_apart --><small><i class="fa fa-user-circle" aria-hidden="true"></i> {ROW.from_address}</small><!-- END: sender_apart -->
                        <!-- BEGIN: sender_full --><small><i class="fa fa-user-circle" aria-hidden="true"></i> {ROW.from_name} &lt;{ROW.from_address}&gt;</small><!-- END: sender_full -->
                    </div>
                </div>
                <div class="cw250">
                    <span class="label-name">{LANG.imaplist_to}: </span>{ROW.send_to}
                    <!-- BEGIN: send_cc --><div class="text-muted text-break"><small><span class="label label-info crm-label">CC</span> {ROW.send_cc}</small></div><!-- END: send_cc -->
                    <!-- BEGIN: send_bcc --><div class="text-muted text-break"><small><span class="label label-info crm-label">BCC</span> {ROW.send_bcc}</small></div><!-- END: send_bcc -->
                </div>
                <div class="status">
                    <span class="label-name">{LANG.status}:</span>
                    <!-- BEGIN: status_text -->{ROW.status_text}<!-- END: status_text -->
                    <!-- BEGIN: status_viewing -->
                    <div>{ROW.status_text} {LANG.by} {VIEW_USER}</div>
                    <div><small class="text-muted">{LANG.start_at} {ROW.status_time}</small></div>
                    <!-- END: status_viewing -->
                    <!-- BEGIN: view_assign -->
                    <div>
                        {LANG.imaplist_assign_to} <span class="text-danger">{VIEW_ASSIGN}</span>
                    </div>
                    <!-- END: view_assign -->
                </div>
                <div class="tools text-right">
                    <!-- BEGIN: delete -->
                    <a href="#" data-toggle="ajActionReload" data-op="{OP}" data-action="emaildelete" data-id="{ROW.email_id}" data-busy="false" data-tokend="{TOKEND}" data-mbusy="{LANG.waitforbusy}" data-msg="{LANG.imaplist_delete_confirm}" class="btn btn-danger btn-xs btn-xs-fixed" title="{GLANG.delete}"><i class="fa fa-trash" aria-hidden="true"></i></a>
                    <!-- END: delete -->
                    <!-- BEGIN: continue -->
                    <a href="{ROW.url_continue}" class="btn btn-success btn-xs btn-xs-fixed" title="{LANG.imaplist_continue}"><i class="fa fa-sign-in" aria-hidden="true"></i></a>
                    <!-- END: continue -->
                    <!-- BEGIN: menu -->
                    <a title="{LANG.select_action}" href="#menutool-{ROW.email_id}" data-parent="#dropdown-parent-item-{ROW.email_id}" class="menu-icon-tools btn btn-default btn-xs btn-xs-fixed{SHOW_DESKTOP_TOOLS}"><i class="fa fa-caret-down" aria-hidden="true"></i></a>
                    <!-- END: menu -->
                </div>
            </div>
            <ul class="dropdown-menu dropdown-menu-item" id="menutool-{ROW.email_id}">
                <!-- BEGIN: take_over -->
                <li><a href="#" data-toggle="ajActionReload" data-op="{OP}" data-action="emailtakeover" data-id="{ROW.email_id}" data-busy="false" data-tokend="{TOKEND}" data-mbusy="{LANG.waitforbusy}" data-msg="{LANG.imaplist_take_over_confirm}"><i class="fa fa-chain-broken" aria-hidden="true"></i> {LANG.take_over}</a></li>
                <!-- END: take_over -->
                <!-- BEGIN: continue1 -->
                <li class="hide-desktop"><a href="{ROW.url_continue}"><i class="fa fa-sign-in" aria-hidden="true"></i> {LANG.imaplist_continue}</a></li>
                <!-- END: continue1 -->
                <!-- BEGIN: direct_access -->
                <li><a href="{ROW.url_continue}"><i class="fa fa-sign-in" aria-hidden="true"></i> {LANG.imaplist_direct_access}</a></li>
                <!-- END: direct_access -->
                <!-- BEGIN: delete1 -->
                <li class="hide-desktop"><a href="#" data-toggle="deleteImapMail" data-id="{ROW.email_id}" data-busy="false" data-tokend="{TOKEND}" data-mbusy="{LANG.waitforbusy}"><i class="fa fa-trash" aria-hidden="true"></i> {GLANG.delete}</a></li>
                <!-- END: delete1 -->
            </ul>
        </div>
        <!-- END: loop -->
    </div>
</div>
<!-- BEGIN: generate_page -->
<div class="text-center">
    {GENERATE_PAGE}
</div>
<!-- END: generate_page -->
<!-- END: main -->
