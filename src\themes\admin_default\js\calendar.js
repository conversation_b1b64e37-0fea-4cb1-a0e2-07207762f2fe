/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

function get_author_alias(id, checksess) {
    var name_calendar = strip_tags(document.getElementById('element_name_calendar').value);
    if (name_calendar != '') {
        $.post(
            script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=calendar&nocache=' + new Date().getTime(),
            'changealias=' + checksess + '&name_calendar=' + encodeURIComponent(name_calendar) + '&id=' + id, function(res) {
                if (res != "") {
                    document.getElementById('element_alias').value = res;
                } else {
                    document.getElementById('element_alias').value = '';
                }
            });
    }
}

function nv_change_calendar_status(id, checksess) {
    $('#change_status' + id).prop('disabled', true);
    $.post(
        script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=calendar&nocache=' + new Date().getTime(),
        'changestatus=' + checksess + '&id=' + id, function(res) {
            $('#change_status' + id).prop('disabled', false);
            if (res != 'OK') {
                alert(nv_is_change_act_confirm[2]);
                location.reload();
            }
        });
}

function nv_delele_calendar(id, checksess) {
    if (confirm(nv_is_del_confirm[0])) {
        $.post(
            script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=calendar&nocache=' + new Date().getTime(),
            'delete=' + checksess + '&id=' + id, function(res) {
                var r_split = res.split("_");
                if (r_split[0] == 'OK') {
                    location.reload();
                } else {
                    alert(nv_is_del_confirm[2]);
                }
            });
    }
}

function get_event_alias(id, checksess) {
    var title = strip_tags(document.getElementById('element_title').value);
    if (title != '') {
        $.post(
            script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=event&nocache=' + new Date().getTime(),
            'changealias=' + checksess + '&title=' + encodeURIComponent(title) + '&id=' + id, function(res) {
                if (res != "") {
                    document.getElementById('element_alias').value = res;
                } else {
                    document.getElementById('element_alias').value = '';
                }
            });
    }
}

function nv_change_event_status(id, checksess) {
    $('#change_status' + id).prop('disabled', true);
    $.post(
        script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=main&nocache=' + new Date().getTime(),
        'changestatus=' + checksess + '&id=' + id, function(res) {
            $('#change_status' + id).prop('disabled', false);
            if (res != 'OK') {
                alert(nv_is_change_act_confirm[2]);
                location.reload();
            }
        });
}

function nv_delele_event(id, checksess) {
    if (confirm(nv_is_del_confirm[0])) {
        $.post(
            script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=main&nocache=' + new Date().getTime(),
            'delete=' + checksess + '&id=' + id, function(res) {
                var r_split = res.split("_");
                if (r_split[0] == 'OK') {
                    location.reload();
                } else {
                    alert(nv_is_del_confirm[2]);
                }
            });
    }
}

function nv_sort_calendar(id, w) {
    $("#order_weight").dialog("open");
    $("#order_weight_id").val(id, w);
    $("#order_weight_number").val(w);
    $("#order_weight_new").val(w);
    return false;
}

function nv_delete_all_event(id, checksess) {
    if (confirm(nv_is_del_confirm[0])) {
        var $form = $('#form_event');
        $.ajax({
            type: 'POST',
            url:  script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=main&nocache=' + new Date().getTime(),
            data: $form.serialize(),
        }).done(function (response) {
            if (response['res'] == 'success') {
                location.reload();
            } else {
                alert(nv_is_del_confirm[2]);
            }
        });
    }
}

function nv_change_weight_calendar(id, checksess) {
    var $form = $('#form-weight');
    $.ajax({
        type: 'POST',
        url: script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '=calendar&nocache=' + new Date().getTime(),
        data: $form.serialize(),
    }).done(function (response) {
        if (response['res'] == 'success') {
            $('#order_weight').dialog('close');
            location.reload();
        } else {
            alert(response['mess']);
        }
    });
}
