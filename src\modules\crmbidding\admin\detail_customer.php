<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
use NukeViet\Api\DoApi;
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

session_write_close();// Đến đây Session vẫn có sẵn để đọc, nhưng không thể ghi thêm được

$page_title = $nv_Lang->getModule('detail_customer');
$id_user = intval($nv_Request->get_int('user_id', 'get', 0));
$admin_id = intval($nv_Request->get_int('admin_id', 'get', 0));

// L<PERSON>y thông tin khách hàng
$get_id_user = $db->query('SELECT * FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $id_user)->fetch();
$get_admin_id = $db->query('SELECT * FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $admin_id)->fetch();

// Khai báo các bảng table
$customs_points = $db_config['prefix'] . "_points_customs";
$wallet_money = $db_config['prefix'] . "_wallet_money";
$wallet_transaction = $db_config['prefix'] . "_wallet_transaction";
$support_customer = NV_PREFIXLANG . '_' . $module_data . "_comment";

// danh sách Sale của mình
$_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE userid = ' . $admin_info['userid'] . ' ORDER BY userid ASC';
$_query = $db->query($_sql);
$admin_crm_config = $caregiver_id_leads = [];
$is_leader = 0;
while ($_row = $_query->fetch()) {
    $admin_crm_config = json_decode($_row['config'], true);
    if ($_row['is_leader'] == 1) {
        $is_leader = 1;
        $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $_row['group_id'] . ' AND userid != ' . $admin_info['userid'];
        $_result = $db->query($_sql);
        while ($_row_groups_users = $_result->fetch()) {
            $caregiver_id_leads[$_row_groups_users['userid']] = $_row_groups_users['userid'];
        }
    }
}
$caregiver_id_leads[$admin_info['userid']] = $admin_info['userid'];
// kiểm tra quyền hạn truy cập của admin
if (!defined('NV_IS_SPADMIN') and $admin_crm_config['show_chart'] == 0 and ($is_leader and !isset($caregiver_id_leads[$admin_id]) or ($is_leader == 0 and $admin_info['userid'] != $admin_id))) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=manage_customer');
}

// lấy khách vip theo user
$arr_where = [];
$arr_where['AND'][] = [
    '=' => [
        'user_id' => $id_user
    ]
];
$infoAPI = array(
    'where' => $arr_where
);
$result = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'ListBiddingCustoms', $infoAPI, 'bidding');

$get_order_vip = [];
if (isset($result['data'])) {
    $get_order_vip = $result['data'];
}

// k có user và gói dịch vụ nào thì chuyển hướng
if (empty($get_id_user) || empty($get_admin_id) || empty($get_order_vip)) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=manage_customer');
}

$infoAPI = array(
    'user_id' => $id_user,
    'group_by' => 'email'
);
$ListBiddingCusomsEmailDie = CallAPI($infoAPI, 'ListBiddingCustomsEmailDie');
$ListBiddingCusomsEmailDie['data'] = $ListBiddingCusomsEmailDie['data'] ?? [];

if (isset($get_id_user['userid'])) {
    $get_id_user['fullname'] = nv_show_name_user($get_id_user['first_name'], $get_id_user['last_name'], $get_id_user['userid']);
    $get_id_user['birthday'] = ($get_id_user['birthday'] != 0) ? nv_date('d/m/Y', $get_id_user['birthday']) : 'N/A';
    $ttUser = $db->query('SELECT phone, mst FROM ' . NV_USERS_GLOBALTABLE . '_info WHERE userid = ' . $id_user)->fetch();
    if (!empty($ttUser['phone'])) {
        $get_id_user['phone'] = $ttUser['phone'];
    } else {
        $get_id_user['phone'] = 'N/A';
    }

    if (!empty($ttUser['mst'])) {
        $get_id_user['mst'] = $ttUser['mst'];
    } else {
        $get_id_user['mst'] = 'N/A';
    }
}

// Các gói VIP
$array_vip = [
    1, // VIP 1
    2, // VIP 2
    3, // VIP 3
    11, // VIP 1 Quốc tế
    21, // VIP 2 Quốc tế
    5, // VIP 5
    4, // VIP 4
    77, // Tải file T0
    99, // VIEWEB
    88, // X1
    69, // SIEUVIP
    6, // SIEUVIP
    7, // VIP 7
    19, // PRO 1
    55
];

$array_vip_dtnet = [];
$array_vip_dtnet[] = 'bvieweb';
$array_vip_dtnet[] = 'bvip1';
$array_vip_dtnet[] = 'bvip2';
$array_vip_dtnet[] = 'bpro';
$array_vip_dtnet[] = 'abasic';
$array_vip_dtnet[] = 'avip1';
$array_vip_dtnet[] = 'apro1';
$array_vip_dtnet[] = 'apro2';
$array_vip_dtnet[] = 'x3';

$array_vip = array_merge($array_vip, $array_vip_dtnet);

// Tổng số tiền đã nạp
$sum_total = [];
$sum_total['loaded'] = $db->query('SELECT sum(money_total) as total FROM ' . $wallet_transaction . ' WHERE userid = ' . $id_user . ' AND status = 1 AND transaction_status = 4')->fetchColumn();

// Tổng số tiền đã thanh toán
$sum_total['amount_paid'] = $db->query('SELECT sum(money_total) as total FROM ' . $wallet_transaction . ' WHERE userid = ' . $id_user . ' AND status = -1 AND transaction_status = 4')->fetchColumn();

// Số dư trong ví khách
$get_wallet_money = $db->query("SELECT * FROM " . $wallet_money . " WHERE userid = " . $id_user . ' AND status = 1')->fetch();

/* ---Xử lý API liên quan đến bảng order------- */
// Lấy đơn hàng của khách hàng
// Ghi log bằng API

/* 2. API Gọi hàm xuất dữ liệu qua API */
$arr_where = [];
$array_order = [];
$arr_where['AND'][] = [
    '=' => [
        'userid' => $id_user
    ]
];

$array_order = [
    'add_time' => 'DESC'
];

$infoAPI = array(
    'where' => $arr_where,
    'order' => $array_order
);

/* 2. Dữ liệu trả về thông qua API */
$get_order = [];
$result = CallAPI($infoAPI, 'ListBiddingOrder');
if (isset($result['data'])) {
    $get_order = $result['data'];
}

/* 3. API Gọi hàm xuất dữ liệu qua API */
$arr_where = [];
$arr_where['AND'][] = [
    '=' => [
        'userid' => $id_user
    ]
];

$arr_where['AND'][] = [
    '=' => [
        'status' => 4
    ]
];

$infoAPI = array(
    'where' => $arr_where,
    'sum' => 1
);

/* 3. Dữ liệu trả về thông qua API */
$result = CallAPI($infoAPI, 'ListBiddingOrder');

$sum_total['money_pay'] = $sum_total['price_reduce'] = $sum_total['discount'] = $sum_total['total_end'] = $sum_total['money'] = $sum_total['taxes_fees'] = 0;

if (isset($result['data'])) {
    foreach ($result['data'] as $key => $value) {
        // Tổng số tiền đơn hàng của khách
        $sum_total['money_pay'] = $value['sum_total'];

        // Tổng tiền chiết khẩu
        $sum_total['price_reduce'] = $value['sum_price_reduce'];

        // Tổng tiền giảm giá
        $sum_total['discount'] = $value['sum_discount'];

        // Tổng tiền thực nhận
        $sum_total['total_end'] = $value['sum_total_end'];

        // Tổng giá dịch vụ
        $sum_total['money'] = $value['sum_money'];

        // Tổng thuế và phí giao dịch
        $sum_total['taxes_fees'] = $value['sum_taxes_fees'];
    }
}

// Số điểm của khách hàng
$get_point = $db->query("SELECT * FROM " . $customs_points . " WHERE userid = " . $id_user . " AND status = 1 ORDER BY point_in DESC")->fetch();

// Lấy tất cả gói vip của khách để lây comment khi gắn thêm gói vip

/* 4. API Gọi hàm xuất dữ liệu qua API */
$arr_where = [];
$arr_where['AND'][] = [
    '=' => [
        'user_id' => $id_user
    ]
];

$infoAPI = array(
    'where' => $arr_where,
    'array_select' => [
        'vip',
        'type_export'
    ],
    'group' => 'vip'
);

/* 4. Dữ liệu trả về thông qua API */
$result = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'ListBiddingCustoms', $infoAPI, 'bidding');
$getvip = [];

if (isset($result['data'])) {
    foreach ($result['data'] as $k => $row) {
        $result['data'][$k]['source'] = 'dauthau.info';
    }
    $getvip = $result['data'];
}

// Lấy dữ liệu các gói vip tại dauthaunet
$fields = [
    'id',
    'profile_id',
    'vip',
    'status',
    'name',
    'admin_id'
];
$api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
$api->setModule('')
    ->setLang(NV_LANG_DATA)
    ->setAction('ListVip')
    ->setData([
    'page' => 1,
    'per_page' => 19,
    'fields' => $fields,
    'where' => [],
    'userid' => $id_user
]);
$result = $api->execute();
if (isset($result['data'])) {
    foreach ($result['data'] as $k => $row) {
        $result['data'][$k]['source'] = 'dauthau.net';
    }
    $getvip = array_merge($getvip, $result['data']);
}

// Lấy danh sách các comment tại dauthaunet tương ứng
$dauthaunet_comment = [];
$api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
$api->setModule('dn')
    ->setLang(NV_LANG_DATA)
    ->setAction('ListBidsProfileComment')
    ->setData([
    'userid' => $id_user
]);
$result = $api->execute();
if (isset($result['data'])) {
    $dauthaunet_comment = $result['data'];
}

$get_comment = $db->query("SELECT * FROM " . $support_customer . " WHERE sourceid = " . $id_user . " AND source = 3 ORDER BY timecreate DESC")->fetchAll();
// 5.API Gọi hàm xuất dữ liệu qua API - Lấy id đơn hàng dịch vụ
$arr_where = [];
$arr_where['AND'][] = [
    '=' => [
        'user_id' => $id_user
    ]
];

$infoAPI = array(
    'where' => $arr_where,
    'array_select' => [
        'id'
    ]
);

/* 5. Dữ liệu trả về thông qua API */
$result = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'ListBiddingCustoms', $infoAPI, 'bidding');

$id_dh = [];
if (isset($result['data'])) {
    $id_dh = $result['data'];
}

// lấy id đơn hàng
// 6.API Gọi hàm xuất dữ liệu qua API - lấy id đơn hàng
$arr_where = [];
$arr_where['AND'][] = [
    '=' => [
        'userid' => $id_user
    ]
];

$infoAPI = array(
    'where' => $arr_where
);

/* 6. Dữ liệu trả về thông qua API */
$result = CallAPI($infoAPI, 'ListBiddingOrder');

$id_order = [];
if (isset($result['data'])) {
    $id_order = $result['data'];
}

$id = [];

foreach ($id_dh as $key => $value) {
    $id[] = $value['id'];
}

foreach ($id_order as $key => $value) {
    $id[] = $value['id'];
}

if (!empty($id)) {
    $id = implode(',', $id);
} else {
    $id = "";
}

// Thêm/sửa liên kết MXH
if ($nv_Request->isset_request('profileUrl', 'post') && $nv_Request->isset_request('platform', 'post')) {
    $editId = $nv_Request->get_int('editId', 'post', 0);
    $profileName = $nv_Request->get_title('profileName', 'post', '');
    $profileUrl = $nv_Request->get_title('profileUrl', 'post', '');
    $platform = $nv_Request->get_title('platform', 'post', '');

    if (empty(SocialNetwork::tryFrom($platform)?->value)) {
        nv_jsonOutput([
            'status' => 'error',
            'code' => 1001,
            'mess' => $nv_Lang->getModule('social_error_platform'),
        ]);
    }

    $profileName = trim(strip_tags($profileName));
    if (empty($profileName)) {
        nv_jsonOutput([
            'status' => 'error',
            'code' => 1002,
            'mess' => $nv_Lang->getModule('social_error_name'),
        ]);
    }

    if (!nv_is_url($profileUrl)) {
        nv_jsonOutput([
            'status' => 'error',
            'code' => 1003,
            'mess' => $nv_Lang->getModule('social_error_url'),
        ]);
    }

    if ($editId > 0) {
        $profile = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_user_socials WHERE userid=' . $id_user . ' AND id=' . $editId)->fetch();
        if (empty($profile)) {
            nv_jsonOutput([
                'status' => 'error',
                'code' => 1004,
                'mess' => 'Error!',
            ]);
        } else {
            $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_user_socials SET platform=:platform, profile_name=:profile_name, profile_url=:profile_url, edit_time=:edit_time WHERE id=:id');
            $stmt->bindParam(':platform', $platform, PDO::PARAM_STR);
            $stmt->bindParam(':profile_name', $profileName, PDO::PARAM_STR);
            $stmt->bindParam(':profile_url', $profileUrl, PDO::PARAM_STR);
            $stmt->bindValue(':edit_time', NV_CURRENTTIME, PDO::PARAM_INT);
            $stmt->bindParam(':id', $editId, PDO::PARAM_INT);
            $exc = $stmt->execute();
            if ($exc) {
                nv_insert_logs(NV_LANG_DATA, $module_name, 'Update social profile', json_encode(['userid' => $id_user, 'profile_name' => $profileName, 'profile_url' => $profileUrl]), $admin_info['userid']);
                nv_jsonOutput([
                    'mess' => $nv_Lang->getModule('social_success_update'),
                    'data' => [
                        'platform' => SocialNetwork::tryFrom($platform)?->getLabel(),
                    ],
                    'status' => 'success'
                ]);
            }
        }
    } else {
        $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_user_socials (userid, platform, profile_name, profile_url , add_by, add_time) VALUES (:userid, :platform, :profile_name, :profile_url, :add_by, :add_time)');
        $stmt->bindParam(':userid', $id_user, PDO::PARAM_INT);
        $stmt->bindParam(':platform', $platform, PDO::PARAM_STR);
        $stmt->bindParam(':profile_name', $profileName, PDO::PARAM_STR);
        $stmt->bindParam(':profile_url', $profileUrl, PDO::PARAM_STR);
        $stmt->bindParam(':add_by', $admin_info['userid'], PDO::PARAM_INT);
        $stmt->bindValue(':add_time', NV_CURRENTTIME, PDO::PARAM_INT);
        $exc = $stmt->execute();
        if ($exc) {
            $newId = $db->lastInsertId();
            nv_insert_logs(NV_LANG_DATA, $module_name, 'Insert social profile', json_encode(['userid' => $id_user, 'profile_name' => $profileName, 'profile_url' => $profileUrl]), $admin_info['userid']);
            nv_jsonOutput([
                'mess' => $nv_Lang->getModule('social_success_insert'),
                'data' => [
                    'id' => $newId,
                    'platform' => SocialNetwork::tryFrom($platform)?->getLabel(),
                ],
                'status' => 'success'
            ]);
        }
    }

    nv_jsonOutput([
        'status' => 'error',
        'code' => 1005,
        'mess' => 'Error!',
    ]);
}

// Xóa social profile
if ($nv_Request->isset_request('profileIdSelected', 'post')) {
    $profileId = $nv_Request->get_int('profileIdSelected', 'post', 0);
    $profile = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_user_socials WHERE userid=' . $id_user . ' AND id=' . $profileId)->fetch();
    if (empty($profile)) {
        nv_jsonOutput([
            'status' => 'error',
            'code' => 2001,
            'mess' => 'Error!',
        ]);
    } else {
        $stmt = $db->prepare('DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_user_socials WHERE userid=:userid AND id=:id');
        $stmt->bindParam(':userid', $id_user, PDO::PARAM_INT);
        $stmt->bindParam(':id', $profileId, PDO::PARAM_INT);
        $exc = $stmt->execute();
        if ($exc) {
            nv_insert_logs(NV_LANG_DATA, $module_name, 'Delete social profile', json_encode(['userid' => $id_user, 'profile_id' => $profileId]), $admin_info['userid']);
            nv_jsonOutput([
                'mess' => $nv_Lang->getModule('social_success_delete'),
                'status' => 'success'
            ]);
        }
    }
}

// API ListProfile
if (defined('API_DAUTHAUNET_URL') and $nv_Request->isset_request('apiName', 'post') && $nv_Request->get_title('apiName', 'post', '') == 'ListProfile') {
    $where_api = [];
    $per_page = 20;
    $page = $nv_Request->get_int('page', 'post', 1);
    $fields = [
        'id',
        'profile_type',
        'prof_name',
        'prof_code',
        'prof_alias',
        'info_phone',
        'info_email',
        'status',
        'fee_status',
        'fee_expired',
        'active_id',
        'add_time'
    ];
    $where_api['AND'][] = [
        '=' => [
            'userid' => $id_user
        ]
    ];

    $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
    $api->setModule('')
        ->setLang('vi')
        ->setAction('ListProfile')
        ->setData([
        'page' => $page,
        'per_page' => $per_page,
        'fields' => $fields,
        'where' => $where_api
    ]);
    $result = $api->execute();
    $error = $api->getError();

    if (empty($error) and $result['status'] == 'success') {
        if (!empty($result['data'])) {
            $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;user_id=' . $id_user . '&amp;admin_id=' . $admin_id;
            $show_api_list_profile = show_api_list_profile($result['data'], $id_user, $base_url, $result['total'], $result['per_page'], $result['page']);
            // trả về dữ liệu
            nv_jsonOutput([
                'status' => 'ok',
                'mess' => 'Success',
                'data' => $show_api_list_profile,
                'number_profile' => count($result['data'])
            ]);
        }
        nv_jsonOutput([
            'status' => 'ok',
            'mess' => 'Empty data'
        ]);
    } else {
        nv_jsonOutput([
            'status' => 'fail',
            'mess' => $nv_Lang->getModule('err_list_profile_dtnet'),
            'error' => $error,
            'result' => $result
        ]);
    }
}

// API ListTicket
if ($nv_Request->isset_request('apiName', 'post') && $nv_Request->get_title('apiName', 'post', '') == 'ListTicket') {
    $where_api = [];
    $where_api['AND'][] = [
        '=' => [
            'customer_id' => $id_user
        ]
    ];

    $perpage = 20;
    $page = $nv_Request->get_int('page', 'post', 1);

    $params = [
        'page' => $page,
        'perpage' => $perpage,
        'where' => $where_api
    ];

    // GỌI API
    $ListAllTicket = nv_local_api('ListAllTicket', $params, $admin_info['username'], 'supportticket');
    $result = json_decode($ListAllTicket, true);
    if ($result['status'] == 'success') {
        if (!empty($result['data'])) {
            $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;user_id=' . $id_user . '&amp;admin_id=' . $admin_id;
            $show_api_list_ticket = show_api_list_ticket($result['data'], $id_user, $base_url, $result['total'], $result['perpage'], $result['page']);
            // trả về dữ liệu
            nv_jsonOutput([
                'status' => 'ok',
                'mess' => 'Success',
                'data' => $show_api_list_ticket,
                'number_profile' => count($result['data'])
            ]);
        }
        nv_jsonOutput([
            'status' => 'ok',
            'mess' => 'Empty data'
        ]);
    } else {
        nv_jsonOutput([
            'status' => 'fail',
            'mess' => $nv_Lang->getModule('err_list_ticket'),
            'error' => $error,
            'result' => $result
        ]);
    }
}

// API ListVip
if (defined('API_DAUTHAUNET_URL') and $nv_Request->isset_request('apiName', 'post') && $nv_Request->get_title('apiName', 'post', '') == 'ListVip') {
    $where_api = [];
    $per_page = 20;
    $page = $nv_Request->get_int('page', 'post', 1);
    $fields = [
        'id',
        'profile_id',
        'vip',
        'from_time',
        'end_time',
        'sum_viptime',
        'status',
        'name',
        'tax',
        'email_bill',
        'contact_phone',
        'contact_to',
        'address_org',
        'admin_id'
    ];

    $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
    $api->setModule('')
        ->setLang('vi')
        ->setAction('ListVip')
        ->setData([
        'page' => $page,
        'per_page' => $per_page,
        'fields' => $fields,
        'where' => $where_api,
        'userid' => $id_user
    ]);
    $result = $api->execute();
    $error = $api->getError();

    if (empty($error) and $result['status'] == 'success') {
        if (!empty($result['data'])) {
            $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;user_id=' . $id_user . '&amp;admin_id=' . $admin_id;
            $show_api_list_vip = show_api_list_vip($result['data'], $id_user, $base_url, $result['total'], $result['per_page'], $result['page']);
            // trả về dữ liệu
            nv_jsonOutput([
                'status' => 'ok',
                'mess' => 'Success',
                'data' => $show_api_list_vip,
                'number_profile' => count($result['data'])
            ]);
        }
        nv_jsonOutput([
            'status' => 'ok',
            'mess' => 'Empty data'
        ]);
    } else {
        nv_jsonOutput([
            'status' => 'fail',
            'mess' => $nv_Lang->getModule('err_list_profile_dtnet'),
            'error' => $result['message'],
            'result' => $result
        ]);
    }
}

// API ListPayment
if ($nv_Request->isset_request('apiName', 'post') && $nv_Request->get_title('apiName', 'post', '') == 'ListPayment') {
    $where_api = [];
    $per_page = 20;
    $page = $nv_Request->get_int('page', 'post', 1);
    $fields = [
        'id',
        'profile_id',
        'userid',
        'admin_id',
        'caregiver_id',
        'affiliate_userid',
        'promo_userid',
        'promo_code',
        'promo_type',
        'promo_value',
        'promo_id',
        'money',
        'discount',
        'price_reduce',
        'total_end',
        'total',
        'discount_update',
        'total_update',
        'note',
        'status',
        'is_expired',
        'add_time',
        'static_time',
        'source_money',
        'add_adminid'
    ];

    $where_api['AND'][] = [
        '=' => [
            'userid' => $id_user
        ]
    ];
    $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
    $api->setModule('')
        ->setLang('vi')
        ->setAction('ListPayment')
        ->setData([
        'page' => $page,
        'per_page' => $per_page,
        'fields' => $fields,
        'where' => $where_api
    ]);
    $result = $api->execute();
    $error = $api->getError();

    if (empty($error) and $result['status'] == 'success') {
        if (!empty($result['data'])) {
            $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;user_id=' . $id_user . '&amp;admin_id=' . $admin_id;
            $show_api_list_vip = show_api_list_payment($result['data'], $base_url, $result['total'], $result['per_page'], $result['page']);
            // trả về dữ liệu
            nv_jsonOutput([
                'status' => 'ok',
                'mess' => 'Success',
                'data' => $show_api_list_vip,
                'number_profile' => count($result['data'])
            ]);
        }
        nv_jsonOutput([
            'status' => 'ok',
            'mess' => 'Empty data'
        ]);
    } else {
        nv_jsonOutput([
            'status' => 'fail',
            'mess' => $nv_Lang->getModule('err_list_profile_dtnet'),
            'error' => $result['message'],
            'result' => $result
        ]);
    }
}

// Kiểm tra id đơn hàng hoặc id gói dịch vụ đã được comment chưa sau đó gán vào biến get_commnet_dv
$get_comment_dv = [];
$arridVip = [];
$arridNameVip = [];
if ($id != "") {
    /* 1. API Gọi hàm xuất dữ liệu qua API */
    $arr_where = [];
    $arr_where['AND'][] = [
        'IN' => [
            'sourceid' => '(' . $id . ')'
        ]
    ];

    $infoAPI = array(
        'where' => $arr_where
    );

    /* 1. Dữ liệu trả kq thông qua API */
    $getBiddingComment = CallAPI($infoAPI, 'ListBiddingComment');
    if (isset($getBiddingComment['data'])) {
        foreach ($getBiddingComment['data'] as $key => $value) {
            if ($value['source'] == 2) {
                $arridVip[] = $value['sourceid'];
            }
        }
    }

    // Lấy tên gói vip trong gói dịch vụ để hiển thị ở trang chi tiết
    if (!empty($arridVip)) {
        $arr_where = [];

        $arr_where['AND'][] = [
            'IN' => [
                'id' => '(' . implode(',', $arridVip) . ')'
            ]
        ];

        $infoAPI = array(
            'where' => $arr_where,
            'array_select' => [
                'vip',
                'type_export',
                'id'
            ]
        );

        /* 6. Dữ liệu trả về thông qua API */
        $result = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'ListBiddingCustoms', $infoAPI, 'bidding');

        $get_nameVip = [];
        if (isset($result['data'])) {
            $get_nameVip = $result['data'];
        }
    } else {
        $get_nameVip = [];
    }
    unset($arridVip);

    if (!empty($get_nameVip)) {
        foreach ($get_nameVip as $key => $value) {
            if (in_array($value['vip'], $array_vip)) {
                $arridNameVip[$value['id']] = $value['vip'] == 55 ? ($value['type_export'] == 1 ? $nv_Lang->getModule('vip55_year') : $nv_Lang->getModule('vip55_month')) : $nv_Lang->getModule('vip' . $value['vip']);
                $arridVip[$value['id']] = $value['vip'];
            }
        }
    }
}

$array_comment = [];

// Thực hiện gộp comment của trang CRM và gói dịch vụ, đơn hàng vào mảng array_comment rồi sắp xếp lại
if (!empty($get_comment_dv)) {
    foreach ($get_comment_dv as $key => $value) {
        $value['name_vip'] = '';
        if (isset($arridNameVip[$value['sourceid']])) {
            $value['name_vip'] = $arridNameVip[$value['sourceid']];
            $value['vip'] = $arridVip[$value['sourceid']];
        }
        $array_comment[] = $value;
    }
}

// Gán comment của trang chi tiết khách hàng vào mảng array_comment
if (!empty($get_comment)) {
    foreach ($get_comment as $key => $value) {
        $array_comment[] = $value;
    }
}

foreach ($dauthaunet_comment as $value) {
    $value['name_vip'] = $nv_Lang->getModule($value['vip']);
    /**
     * sourceid ở dauthau.net nó chứa id của nv4_bids_profile_vips
     * sourceid ở dauthau.info nó chứa id của nv4_bidding_customs
     * sourceid ở id.dauthau.net nó chứa userid
     * Code hiện tại sử dụng kiểu của id.dauthau.net để xử lý, nên sẽ điều chỉnh lại sourceid
     */
    $value['vips_sourceid'] = $value['sourceid'];
    $value['sourceid'] = $id_user;
    /**
     * Phía bên dauthau.net thì source mang 2 giá trị là 1: Đơn hàng, 2: Vip
     * Để không nhầm với 2: vip bên dauthau.info nên ở đây chuyển sang 4 để hiện thị phù hợp
     */
    $value['source'] = $value['source'] == 2 ? 4 : $value['source'];
    // Giúp phân biệt comment đến từ đâu hỗ trợ trong quá trình xử lý ở sau
    $value['origin'] = 'dauthau.net';
    $array_comment[] = $value;
}

if (!empty($array_comment)) {
    // Sắp xếp comment theo thời gian
    usort($array_comment, function ($a, $b) {
        return $a['timecreate'] < $b['timecreate'] ? 1 : -1;
    });
}

// Hiển thị gói VIP mà khách hàng đang sử dụng theo dạng KEY -> VALUE
foreach ($getvip as $key => $value) {
    if ($value['source'] == 'dauthau.info') {
        $key = $value['vip'] . '_' . $value['type_export'] . '_' . $value['source'];
        $vip[$key] = $value['vip'] == 55 ? ($value['type_export'] == 1 ? $nv_Lang->getModule('vip55_year') : $nv_Lang->getModule('vip55_month')) : $nv_Lang->getModule('vip' . $value['vip']);
    } elseif ($value['source'] == 'dauthau.net') {
        $key = $value['vip'] . '_' . $value['id'] . '_' . $value['source'];
        $vip[$key] = $nv_Lang->getModule($value['vip']);
    }
}

// ghi chú comment
$arr_note = array(
    1 => $nv_Lang->getModule('payment'),
    2 => $nv_Lang->getModule('customs'),
    3 => $nv_Lang->getModule('chitietkh'),
    4 => $nv_Lang->getModule('customs_net')
);

// Lưu comment Sale
if ($nv_Request->isset_request('action', 'post')) {
    $id = intval($nv_Request->get_int('id', 'post', 0));
    $vip = explode('_', $nv_Request->get_title('vip', 'post', 0));
    $userid = intval($nv_Request->get_int('userid', 'post', 0));
    $admin_id_note = intval($nv_Request->get_int('admin_id', 'post', 0));
    $status = $nv_Request->get_title('status', 'post', '');
    $content = $nv_Request->get_textarea('content', '', NV_ALLOWED_HTML_TAGS);
    $note = $nv_Request->get_textarea('note', '', NV_ALLOWED_HTML_TAGS);
    $source = $nv_Request->get_title('source', 'post', '');

    $vip[0] = isset($vip[0]) ? intval($vip[0]) : 0;
    $vip[1] = isset($vip[1]) ? intval($vip[1]) : 0;
    $vip[2] = isset($vip[2]) ? $vip[2] : '';

    $admin_id = $admin_info['userid'];
    if ($vip[2] == 'dauthau.net' || $source == 4) {
        $data_post_api = [];
        $data_post_api['id'] = $id;
        $data_post_api['source'] = '2';
        $data_post_api['sourceid'] = $vip[1];
        $data_post_api['post_id'] = $admin_id;
        $data_post_api['note'] = $content ?: $note;
        // Kiểm tra quyền thao tác
        $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
        $api->setModule('dn')
            ->setLang(NV_LANG_DATA)
            ->setAction('GetBidsProfileCommentPermission')
            ->setData($data_post_api);
        $result = $api->execute();
        if ($result['status'] == 'error') {
            nv_jsonOutput([
                'res' => $nv_Lang->getModule('tb'),
                'mess' => $nv_Lang->getModule($result['message']),
                'status' => 'requied'
            ]);
        }
        $api->setAction('BidsProfileComment');
        $result = $api->execute();
        if ($result['status'] == 'error') {
            nv_jsonOutput([
                'res' => $nv_Lang->getModule('tb'),
                'mess' => $nv_Lang->getModule($result['message']),
                'status' => 'requied'
            ]);
        } else {
            nv_jsonOutput([
                'res' => $nv_Lang->getModule('tb'),
                'mess' => $nv_Lang->getModule('update_comment_success'),
                'status' => 'success'
            ]);
        }
    } else {
        $where = '';
        $arr_where = [];
        // comment lại đoạn này để k xác định theo người chăm sóc
        /* if (!defined('NV_IS_SPADMIN') and $admin_crm_config['show_chart'] == 0 and $admin_crm_config['is_leader'] == 0) {
            $arr_where['AND'][] = [
                '=' => [
                    'admin_id' => $admin_info['userid']
                ]
            ];
        } else if (!defined(constant_name: 'NV_IS_SPADMIN') and !empty($caregiver_id_leads) and $admin_crm_config['show_chart'] == 0) {
            if (!empty($caregiver_id_leads)) {
                $arr_where['AND'][] = [
                    'IN' => [
                        'admin_id' => '(' . implode(',', $caregiver_id_leads) . ')'
                    ]
                ];
            }
        } */

        $arr_where['AND'][] = [
            '=' => [
                'user_id' => $id_user
            ]
        ];
        if ($vip[0] > 0) {
            $arr_where['AND'][] = [
                '=' => [
                    'vip' => $vip[0]
                ]
            ];
        }

        if ($id_user != $userid) {
            nv_jsonOutput([
                'res' => $nv_Lang->getModule('tb'),
                'mess' => $nv_Lang->getModule('error L949'),
                'status' => $nv_Lang->getModule('error')
            ]);
        }

        // Kiểm tra xem có tồn tại id comment đối với source = 1 và 2
        if ($source == 1 || $source == 2) {
            $check = [];
            /* 1. API Gọi hàm xuất dữ liệu qua API */
            $arr_where = [];
            $arr_where['AND'][] = [
                '=' => [
                    'source' => $source
                ]
            ];

            $arr_where['AND'][] = [
                '=' => [
                    'id' => $id
                ]
            ];

            $arr_where['AND'][] = [
                '=' => [
                    'post_id' => $admin_id
                ]
            ];

            $infoAPI = array(
                'where' => $arr_where
            );

            /* 1. Dữ liệu trả kq thông qua API */
            $getBiddingComment = CallAPI($infoAPI, 'ListBiddingComment');
            if (isset($getBiddingComment['data'])) {
                $check = $getBiddingComment['data'][0];
            }
        } else {
            $infoAPI = array(
                'where' => $arr_where
            );


            /* 6. Dữ liệu trả về thông qua API */
            $result = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'ListBiddingCustoms', $infoAPI, 'bidding');
            if (isset($result['data'])) {
                $check = $result['data'];
            } else {
                $check = [];
            }
        }

        if (empty($check)) {
            nv_jsonOutput([
                'res' => $nv_Lang->getModule('tb'),
                'mess' => $nv_Lang->getModule('error L1003'),
                'status' => $nv_Lang->getModule('error')
            ]);
        }

        if ($source == 1 || $source == 2) {
            if (empty($note)) {
                nv_jsonOutput([
                    'res' => $nv_Lang->getModule('tb'),
                    'mess' => $nv_Lang->getModule('error_comment L1012'),
                    'status' => 'requied'
                ]);
            }
        } else {
            if ($vip[0] == 0 && $content == '') {
                nv_jsonOutput([
                    'res' => $nv_Lang->getModule('tb'),
                    'mess' => $nv_Lang->getModule('error_comment L1020'),
                    'status' => 'requied'
                ]);
            }
        }

        $time = NV_CURRENTTIME;
        $content = nv_nl2br($content);
        $note = nv_nl2br($note);
        try {
            if ($source != 3 && !empty($note)) {
                $params_update = [];
                $data_update = [
                    'note' => $note,
                    'update_time' => $time
                ];

                $params_update = [
                    'id' => $id,
                    'post_id' => $admin_id,
                    'data' => $data_update
                ];

                $api = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                $api->setModule('bidding')
                    ->setLang('vi')
                    ->setAction('UpdateBiddingComment')
                    ->setData($params_update);
                $exc = $api->execute();

                if ($exc) {
                    $nv_Cache->delMod($module_name);
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Update Comment Sale: ', ' ', $admin_info['userid']);
                    nv_jsonOutput([
                        'res' => $nv_Lang->getModule('tb'),
                        'mess' => $nv_Lang->getModule('update_comment_success'),
                        'status' => 'success'
                    ]);
                }
            } else {
                $type_export = intval($vip[1]);
                if ($status == 'add' && $id == 0) {
                    // source = 3 tức là Customer VIP
                    $stmt = $db->prepare('INSERT INTO ' . $support_customer . '(id, source, sourceid, vip, post_id , timecreate, note, type_export) VALUES (NULL, 3, :sourceid, :vip, :post_id, :timecreate, :note, :type_export)');
                    $stmt->bindParam(':vip', $vip[0], PDO::PARAM_INT);
                    $stmt->bindParam(':sourceid', $userid, PDO::PARAM_INT);
                    $stmt->bindParam(':post_id', $admin_id, PDO::PARAM_INT);
                    $stmt->bindParam(':timecreate', $time, PDO::PARAM_INT);
                    $stmt->bindParam(':note', $content, PDO::PARAM_STR);
                    $stmt->bindParam(':type_export', $type_export, PDO::PARAM_INT);
                    $exc = $stmt->execute();
                    if ($exc) {
                        $nv_Cache->delMod($module_name);
                        nv_insert_logs(NV_LANG_DATA, $module_name, 'Insert Comment Sale: ', ' ', $admin_info['userid']);
                        nv_jsonOutput([
                            'res' => $nv_Lang->getModule('tb'),
                            'mess' => $nv_Lang->getModule('success_comment'),
                            'status' => 'success'
                        ]);
                    }
                }

                if ($status == 'edit' && $id != 0) {
                    $stmt = $db->prepare('UPDATE ' . $support_customer . ' SET note = :note, vip=:vip, update_time=:update_time, type_export=:type_export WHERE id = ' . $id . ' AND post_id = ' . $admin_id . ' AND sourceid = ' . $userid);
                    $stmt->bindParam(':vip', $vip[0], PDO::PARAM_INT);
                    $stmt->bindParam(':note', $content, PDO::PARAM_STR);
                    $stmt->bindParam(':update_time', $time, PDO::PARAM_INT);
                    $stmt->bindParam(':type_export', $type_export, PDO::PARAM_INT);
                    $exc = $stmt->execute();
                    if ($exc) {
                        $nv_Cache->delMod($module_name);
                        nv_insert_logs(NV_LANG_DATA, $module_name, 'Update Comment Sale: ', ' ', $admin_info['userid']);
                        nv_jsonOutput([
                            'res' => $nv_Lang->getModule('tb'),
                            'mess' => $nv_Lang->getModule('update_comment_success'),
                            'status' => 'success'
                        ]);
                    }
                }
            }
        } catch (PDOException $e) {
            nv_jsonOutput([
                'res' => $nv_Lang->getModule('tb'),
                'mess' => $nv_Lang->getModule('error L1103') . $e->getMessage(),
                'status' => $nv_Lang->getModule('error')
            ]);
        }
    }
}

$row_link = [];
$row_link['order'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding' . '&amp;' . NV_OP_VARIABLE . '=payment&amp;' . 'userid=' . $get_id_user['userid'];
$row_link['editUser'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=api-client-site&amp;' . NV_OP_VARIABLE . '=edit&amp;userid=' . $get_id_user['userid'] . '&check_admin=' . md5(NV_CHECK_SESSION . $get_id_user['userid']);
$row_link['link_point'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=points&amp;' . NV_OP_VARIABLE . '=transaction&amp;userid=' . $get_id_user['userid'];
$row_link['link_money'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=wallet&amp;' . NV_OP_VARIABLE . '=transaction&amp;userid=' . $get_id_user['userid'];
$row_link['link_transaction_point'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=points&amp;' . NV_OP_VARIABLE . '=add_transaction&amp;username=' . $get_id_user['username'];
// Log của đơn hàng
$array_logs = [];

// timeline mua hàng
// API CUSTOMS_LOG
$arr_where = [];
$arr_where['AND'][] = [
    '=' => [
        'user_id' => $id_user
    ]
];

$infoAPI = array(
    'where' => $arr_where
);

/* 6. Dữ liệu trả về thông qua API */
$resultCustomsLog = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'ListBiddingCustomsLog', $infoAPI, 'bidding');

$arr_customer_info = $customer_info = [];
$arrayOrderVip = [];
if (isset($resultCustomsLog['data'])) {
    foreach ($resultCustomsLog['data'] as $key => $row) {
        $arr_customer_info[$row['id']] = $row;
        $customer_info = $row;
        $arrayOrderVip[$row['vip']] = $row['type_export'];
    }

    $_tmp_phone = $customer_info['phone'] != '' ? $customer_info['phone'] : $customer_info['contact_phone'];
    if (preg_match('/(\d{9})$/', $customer_info['phone'], $m)) {
        $_tmp_phone = $m[0];
    }
    if (!empty($customer_info['contact_phone'])) {
        $_tmp_phone2 = $customer_info['contact_phone'];
        if (preg_match('/(\d{9})$/', $customer_info['contact_phone'], $m)) {
            $_tmp_phone2 = $m[0];
        }
    }

    $array_timline = [];
    // telepro
    $sql = "SELECT id,name,phone,email,timecall, status, note, thoigian_nghetuvan FROM " . NV_PREFIXLANG . "_crmbidding_telepro";
    $where = [];
    if (!empty($customer_info['email'])) {
        $where[] = "email = " . $db->quote($customer_info['email']) . "";
    }
    if (!empty($customer_info['sub_email'])) {
        $where[] = "email IN (" . $db->quote($customer_info['sub_email']) . ") ";
    }
    if (!empty($customer_info['phone'])) {
        $where[] = "phone LIKE " . $db->quote('%' . $_tmp_phone . '');
    }
    if (!empty($customer_info['contact_phone'])) {
        $where[] = "phone LIKE " . $db->quote('%' . $_tmp_phone2 . '');
    }
    if (!empty($where)) {
        $sql .= " WHERE " . implode(' OR ', $where) . " ORDER BY id DESC";
        $result = $db->query($sql);
        while ($_row = $result->fetch()) {
            $_row_tmp = [];
            $_row_tmp['type'] = 'call';
            $_row_tmp['time'] = $_row['timecall'];
            $_row_tmp['title'] = nv_date('H:i d/m/Y', $_row['timecall']) . " - Telepro - " . $_row['phone'];
            // $_row_tmp['body'] = $_row['note'] . ' ' . $_row['status'];
            $_row_tmp['body'] = '<p>' . $_row['note'] . ' ' . $_row['status'] . '</p>';
            $_row_tmp['body'] .= '<p><strong>' . $nv_Lang->getModule('thoigian_nghetuvan') . ': </strong>' . $_row['thoigian_nghetuvan'] . '</p>';
            $href = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=telepro&amp;id=' . $_row['id'];
            $_row_tmp['body'] .= '<a href="' . $href . '" target="_blank">' . $nv_Lang->getModule('view_detai') . '</a>';
            insertTimeline($_row['timecall'], $_row_tmp, $array_timline);
        }
    }

    // chatgpt
    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_crmbidding_chatgpt_users WHERE userid = " . $customer_info['user_id'];
    $result = $db->query($sql);
    $chatGptUsers = [];
    while ($_row = $result->fetch()) {
        $chatGptUsers[$_row['id']] = $_row['last_activity'];
    }
    $chatGptIds = array_keys($chatGptUsers);
    if (!empty($chatGptIds)) {
        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_crmbidding_chatgpt WHERE crmbidding_chatgpt_id IN (" . implode(',', $chatGptIds) . ")";
        $result = $db->query($sql);
        while ($_row = $result->fetch()) {
            $lastActivity = $chatGptUsers[$_row['crmbidding_chatgpt_id']];
            $_row_tmp = [];
            $_row_tmp['type'] = 'chat';
            $_row_tmp['time'] = $lastActivity;
            $_row_tmp['title'] = nv_date('H:i d/m/Y', $lastActivity) . " - " . $nv_Lang->getModule('use_chatgpt');
            $_row_tmp['not_display'] = 1;

            $_row_tmp['body'] = '<p><strong>' . $nv_Lang->getModule('user_question') . '</strong>: ' . $_row['message_user'] . '</p>';
            $_row_tmp['body'] .= '<p><strong>' . $nv_Lang->getModule('ai_answer') . '</strong>: ' . $_row['message_ai'] . '</p>';
            $href = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=chatgpt_detail&amp;id=' . $_row['crmbidding_chatgpt_id'];
            $_row_tmp['body'] .= '<a href="' . $href . '" target="_blank">' . $nv_Lang->getModule('view_detai') . '</a>';
            insertTimeline($lastActivity, $_row_tmp, $array_timline);
        }
    }

    // mobifone
    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_crmbidding_mobiphone";
    if (!empty($customer_info['phone'])) {
        $sql .= " WHERE sdt = " . $db->quote($customer_info['phone']);
        $result = $db->query($sql);
        while ($_row = $result->fetch()) {
            $_row_tmp = [];
            $_row_tmp['type'] = 'call';
            $_row_tmp['time'] = $_row['thoi_gian_bat_dau'];
            $_row_tmp['title'] = nv_date('H:i d/m/Y', $_row['thoi_gian_bat_dau']) . " - Tổng đài Mobifone - " . $_row['sdt'];
            $_row_tmp['body'] = '<p>' . ($_row['loai_cuoc_goi'] == 1 ? 'Khách gọi đến' : 'Sale gọi đi') . ':';
            $_row_tmp['body'] .= "từ " . nv_date('H:i d/m/Y', $_row['thoi_gian_bat_dau']) . ' đến ' . nv_date('H:i d/m/Y', $_row['thoi_gian_ket_thuc']) . '.</p>';
            $_row_tmp['body'] .= '<p>' . 'Trạng thái cuộc gọi: ' . ($_row['trang_thai_cuoc_goi'] == 1 ? 'Cuộc gọi gặp' : 'Cuộc gọi nhỡ') . '</p>';
            $_row_tmp['body'] .= '<p>' . $nv_Lang->getModule('kq_khao_sat') . ':' . $_row['kq_khao_sat'] . '</p>';
            $href = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=mobiphone&amp;id=' . $_row['call_id'];
            $_row_tmp['body'] .= '<a href="' . $href . '" target="_blank">' . $nv_Lang->getModule('view_detai') . '</a>';
            insertTimeline($_row['thoi_gian_bat_dau'], $_row_tmp, $array_timline);
        }
    }

    if ($customer_info['user_id'] > 0) {
        // đăng ký thành viên
        $sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . " WHERE userid = " . $customer_info['user_id'];
        $result = $db->query($sql);
        while ($_row = $result->fetch()) {
            $_row_tmp = [];
            $_row_tmp['type'] = 'user';
            $_row_tmp['time'] = $_row['regdate'];
            $_row_tmp['title'] = nv_date('H:i d/m/Y', $_row['regdate']) . " - Đăng ký thành viên";

            if ($_row['active_obj'] == 'SYSTEM') {
                $_row_tmp['body'] = 'Hệ thống tự kích hoạt';
            } elseif ($_row['active_obj'] == 'EMAIL') {
                $_row_tmp['body'] = 'Kích hoạt qua email';
            } elseif (preg_match('/^OAUTH\:(.*?)$/', $_row['active_obj'], $m)) {
                $_row_tmp['body'] = sprintf('Kích hoạt qua Oauth %s', $m[1]);
            } else {
                $_row_tmp['body'] = 'Admin kích hoạt';
            }
            if ($_row['email_verification_time'] > 0) {
                $_row_tmp['body'] .= sprintf(' - Xác minh email lúc %s', nv_date('H:i d/m/Y', $_row['email_verification_time']));
            }
            // kiểm tra xem có qua affiliate k
            $affilicate = $db->query('SELECT tb2.username FROM ' . $db_config['prefix'] . '_elink_affiliate_set as tb1 INNER JOIN ' . NV_USERS_GLOBALTABLE . ' as tb2 ON tb1.pri_uid = tb2.userid WHERE tb1.pre_uid =' . $_row['userid'])->fetch();
            if (!empty($affilicate)) {
                $_row_tmp['body'] = '<br/> Đăng ký tài khoản qua affiliate của tài khoản: ' . $affilicate['username'];
            }
            insertTimeline($_row['regdate'], $_row_tmp, $array_timline);
        }

        // các đơn hàng khác
        $arr_where = [];
        $arr_where['AND'][] = [
            '=' => [
                'userid' => $id_user
            ]
        ];

        $infoAPI = array(
            'where' => $arr_where
        );

        /* 6. Dữ liệu trả về thông qua API */
        $result = CallAPI($infoAPI, 'ListBiddingOrder');

        $result_other = [];
        if (isset($result['data'])) {
            $result_other = $result['data'];
            foreach ($result_other as $key => $array_order_other) {
                $_row_tmp = [];
                $_row_tmp['type'] = 'order';
                $_row_tmp['time'] = $array_order_other['add_time'];
                $_row_tmp['title'] = nv_date('H:i d/m/Y', $array_order_other['add_time']) . " - Đăng ký đơn hàng" . ($array_order_other['status'] == 4 ? '-Đã thanh toán' : '-Chưa thanh toán');
                $_row_tmp['body'] = '';

                $arr_where = [];
                $arr_where['AND'][] = [
                    '=' => [
                        'order_id' => $array_order_other['id']
                    ]
                ];

                $infoAPI = array(
                    'where' => $arr_where
                );

                /* 6. Dữ liệu trả về thông qua API */
                $resultCustomsLog = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'ListBiddingCustomsLog', $infoAPI, 'bidding');
                if (isset($resultCustomsLog['data'])) {
                    foreach ($resultCustomsLog['data'] as $key => $row) {
                        $tmp_body = '<p>' . empty($row['is_renewal']) ? '- Đăng ký mới ' : '- Gia hạn ';
                        $tmp_body .= $row['numbers_year'] . ' năm ';
                        if ($row['vip'] == 11) {
                            $tmp_body .= 'VIP 1 Quốc tế';
                        } else if ($row['vip'] == 21) {
                            $tmp_body .= 'VIP 2 Quốc tế';
                        } else if ($row['vip'] == 99) {
                            $tmp_body .= 'VIEWEB';
                        } else if ($row['vip'] == 88) {
                            $tmp_body .= $row['type_export'] == 1 ? 'Gói X1 quá khứ' : 'Gói X1 hiện tại';
                        } else if ($row['vip'] == 77) {
                            $tmp_body .= 'Gói T0';
                        } else {
                            $tmp_body .= 'VIP' . $row['vip'];
                        }
                        $_row_tmp['body'] .= $tmp_body . ' </p>';
                    }
                }

                if (true) {
                    $ck = false;
                    if ($array_order_other['promo_code'] != '' and $array_order_other['add_adminid'] == 0) {
                        $_row_tmp['body'] .= '<p>- Đăng ký qua mã giảm giá: ' . $array_order_other['promo_code'] . '</p>';
                    } else {
                        $ck = true;
                        $_row_tmp['body'] .= '<p>- Đăng ký qua link affiliate';
                    }
                    $affilicate = $db->query('SELECT username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid =' . $array_order_other['affiliate_userid'])->fetch();
                    if (!empty($affilicate)) {
                        $_row_tmp['body'] .= ' của tài khoản: ' . $affilicate['username'];
                    }
                    if ($ck) {
                        $_row_tmp['body'] .= '</p>';
                    }
                }

                $href = $value['link_order'] = $view['order'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding' . '&amp;' . NV_OP_VARIABLE . '=payment&amp;vieworderid=' . $array_order_other['id'] . '&amp;userid=' . $array_order_other['userid'] . '&showheader=1';
                $_row_tmp['body'] .= '<a href="' . $href . '" target="_blank">' . $nv_Lang->getModule('view_detai') . '</a>';
                insertTimeline($array_order_other['add_time'], $_row_tmp, $array_timline);
            }
        }

        // Hiển thị email chết, search theo từng marketing
        $array_email = [];
        $array_sub_email = [];
        $array_email[] = trim($customer_info['email']);
        if ($customer_info['sub_email'] != '') {
            $array_sub_email = explode(',', $customer_info['sub_email']);
            $array_sub_email = array_map('trim', $array_sub_email);
        }
        $array_email = array_merge($array_sub_email, $array_email);

        $list_dead_mail_marketing = get_dead_email_from_marketing($array_email);
        foreach ($list_dead_mail_marketing as $dead_mail_marketing) {
            $_row_tmp = [
                'title' => nv_date('H:i d/m/Y', $dead_mail_marketing['date_added']) . ' - ' . $nv_Lang->getModule('emaildie') . ' (Marketing)',
                'type' => 'emaildie',
                'time' => $dead_mail_marketing['date_added'],
                'body' => '<strong>' . $dead_mail_marketing['email'] . '</strong>' . ':</strong> ' . nv_htmlspecialchars($dead_mail_marketing['comments'])
            ];
            insertTimeline($dead_mail_marketing['date_added'], $_row_tmp, $array_timline);
        }

        foreach ($ListBiddingCusomsEmailDie['data'] as $dead_email_dauthauinfo) {
            $_row_tmp = [
                'title' => nv_date('H:i d/m/Y', $dead_email_dauthauinfo['time_die']) . ' - ' . $nv_Lang->getModule('emaildie') . ' (ElasticSearch)',
                'type' => 'emaildie',
                'time' => $dead_email_dauthauinfo['time_die'],
                'body' => '<strong>' . $dead_email_dauthauinfo['email'] . '</strong>' . ':</strong> ' . nv_htmlspecialchars($dead_email_dauthauinfo['comments'])
            ];
            insertTimeline($dead_email_dauthauinfo['time_die'], $_row_tmp, $array_timline);
        }
    }
}

if ($customer_info['user_id'] > 0) {
    $bnclk_where['AND'][] = [
        '=' => [
            'c.userid' => $customer_info['user_id']
        ]
    ];
    $bnclk_params = [
        'where' => $bnclk_where
    ];

    $api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
    $api->setModule('bannersdt')
        ->setLang('vi')
        ->setAction('ListUsersClickBanners')
        ->setData($bnclk_params);
    $result = $api->execute();

    if ($result['status'] == 'success') {
        $history = $result['data'][$customer_info['user_id']]['click_history'];

        foreach ($history as $click_time => $row) {
            $_row_tmp = [
                'title' => nv_date('H:i d/m/Y', $click_time) . ' - ' . 'Click vào banner - ' . $row['title'],
                'type' => 'bannersdt_click',
                'time' => $click_time,
                'body' => '<p>Click vào banner ' . $row['title'] . ' trên trang ' . $row['click_ref'] . '</p><img style="max-width: 100%" src="' . URL_DTINFO . 'uploads/bannersdt/' . $row['file_name'] . '" />'
            ];
            insertTimeline($click_time, $_row_tmp, $array_timline);
        }
    }
}

// mail marketing
if (defined('MARKETING_API_CRM_KEY')) {
    $send_mail = [];
    if ($customer_info['sub_email'] != '') {
        $send_mail = explode(',', $customer_info['sub_email']);
    }
    $num = 0;
    array_push($send_mail, $customer_info['email']);
    foreach ($send_mail as $email) {
        $num++;
        if ($email != '') {
            $page = 1;
            $run = 1;
            while ($run == 1 and (microtime(true) - NV_START_TIME < 10)) {
                $run = 0;
                // Lấy data email được gửi tới KH
                $request = [
                    // Tham số bắt buộc
                    'apikey' => MARKETING_API_CRM_KEY,
                    'apisecret' => MARKETING_API_CRM_SECRET,
                    'action' => 'GetEmailSentOfEmail',
                    'module' => 'marketing',

                    // Tham số tùy chọn
                    'page' => $page,
                    'per_page' => 100,
                    'email' => $email,
                    'language' => 'vi'
                ];
                $NV_Http = new NukeViet\Http\Http($global_config, NV_TEMP_DIR);
                $NV_Http->reset();
                $args = [
                    'headers' => [
                        'Referer' => NV_MY_DOMAIN
                    ],
                    'body' => $request,
                    'timeout' => 10,
                    'sslverify' => false,
                    'decompress' => false
                ];
                $responsive = $NV_Http->post(MARKETING_API_URL, $args);
                if (is_array($responsive) and empty(NukeViet\Http\Http::$error)) {
                    $email_marketing = !empty($responsive['body']) ? json_decode($responsive['body'], true) : [];
                    if (!empty($email_marketing)) {
                        if ($email_marketing['status'] == 'success') {
                            foreach ($email_marketing['rows'] as $item) {
                                if ($item['is_read'] == 1) {
                                    $_row_tmp = [];
                                    $_row_tmp['type'] = 'mail';
                                    $_row_tmp['time'] = $item['date_read'];
                                    $_row_tmp['title'] = nv_date('H:i d/m/Y', $item['date_read']) . " - Đọc emailmarketing qua: " . $email;
                                    $_row_tmp['body'] = '';
                                    $_row_tmp['body'] .= 'Sự kiện: ' . $item['event'];
                                    $_row_tmp['body'] .= '<br/> Chiến dịch: ' . $item['campaign'];
                                    $_row_tmp['body'] .= '<br/> Tiêu đề mail: ' . $item['subject'];

                                    if (!empty($item['tracking_urls'])) {
                                        $item['urls'] = '';
                                        foreach ($item['tracking_urls'] as $link) {
                                            if ($link['hits'] > 0) {
                                                $item['urls'] .= '<a href="' . $link['url'] . '">' . $link['url'] . '</a> - ' . $link['hits'] . ' click </br>';
                                            } else {
                                                $_row_tmp['not_display'] = 1;
                                            }
                                        }
                                    }
                                    if ($item['urls'] != '') {
                                        $_row_tmp['body'] .= '<br/> Link đã click:<br/> ' . $item['urls'];
                                    }
                                    insertTimeline($item['date_read'], $_row_tmp, $array_timline);
                                }
                            }
                        }

                        if (intval($email_marketing['pages']) > $page) {
                            $page++;
                            $run = 1;
                        }
                    }
                } else {
                    trigger_error(print_r($NV_Http, true));
                }
            }
        }
    }
}

// leads liên quan
// $sql = "SELECT id, source_leads, name, caregiver_id, timecreate, updatetime, status FROM " . NV_PREFIXLANG . "_crmbidding_leads WHERE active = 1";
$where = [];
if (!empty($customer_info['email'])) {
    $where['OR'][] = [
        '=' => [
            'email' => $customer_info['email']
        ]
    ];

    $where['OR'][] = [
        'FIND_IN_SET' => [
            'sub_email' => $customer_info['email']
        ]
    ];
}
if (!empty($customer_info['address_bill'])) {
    $where['OR'][] = [
        '=' => [
            'email' => $customer_info['address_bill']
        ]
    ];

    $where['OR'][] = [
        'FIND_IN_SET' => [
            'sub_email' => $customer_info['address_bill']
        ]
    ];
}

if (!empty($customer_info['sub_email'])) {
    $_sub_email = explode(',', $customer_info['sub_email']);
    foreach ($_sub_email as $key => $value) {
        $where['OR'][] = [
            '=' => [
                'email' => $value
            ]
        ];

        $where['OR'][] = [
            'FIND_IN_SET' => [
                'sub_email' => $value
            ]
        ];
    }
}
if (!empty($customer_info['phone'])) {
    $where['OR'][] = [
        '=' => [
            'phone_search' => $_tmp_phone
        ]
    ];

    $where['OR'][] = [
        'FIND_IN_SET' => [
            'sub_phone_search' => $_tmp_phone
        ]
    ];
}

if (!empty($customer_info['contact_phone'])) {
    $where['OR'][] = [
        '=' => [
            'phone_search' => $_tmp_phone2
        ]
    ];

    $where['OR'][] = [
        'FIND_IN_SET' => [
            'sub_phone_search' => $_tmp_phone2
        ]
    ];
}

if (!empty($where)) {
    // $sql .= " AND (" . implode(' OR ', $where) . ") ORDER BY id DESC";
    // pr($sql);
    // $result = $db->query($sql);
    $params = [
        'page' => 1,
        'perpage' => 50
    ];

    // Nếu có điều kiện where thì gán
    if (!empty($where)) {
        $params['where'] = $where;
    }
    // GỌI API
    $List = nv_local_api('ListAllLeads', $params, $admin_info['username'], 'crmbidding');
    $ListAllLeads = json_decode($List, true);
    if (isset($ListAllLeads['data'])) {
        foreach ($ListAllLeads['data'] as $_row) {
            /**
             * Không rõ CSDL vì sao $_row['timecreate'] = 0.
             *
             * @since 09/09/2021
             * <AUTHOR>
             * @link https://vinades.org/dauthau/dauthau.info/-/issues/468
             *       Code lại nếu $_row['timecreate'] = 0 thì lấy từ updatetime
             */
            if (empty($_row['timecreate'])) {
                $_row['timecreate'] = $_row['updatetime'];
            }
            $href = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $_row['id'] . '&amp;showheader=1';
            $_row_tmp = [];
            $_row_tmp['type'] = 'leads';
            $_row_tmp['time'] = $_row['timecreate'];
            $_row_tmp['title'] = nv_date('H:i d/m/Y', $_row['timecreate']) . " - Leads " . $array_groups_leads[$_row['source_leads']]['title'] . " - " . '<a href="' . $href . '" target="_blank">' . $_row['name'] . '</a>';
            $_row_tmp['body'] = '<p>- Trạng thái: ' . $nv_Lang->getModule('status' . $_row['status']) . '</p>';
            $_row_tmp['body'] .= '<p>- Cập nhật lần cuối: ' . nv_date('H:i d/m/Y', $_row['updatetime']) . '</p>';
            if (!empty($array_admin_id[$_row['caregiver_id']])) {
                $_row_tmp['body'] .= '<p>- Người chăm sóc: ' . $array_admin_id[$_row['caregiver_id']]['username'] . '</p>';
            }

            if ($_row['status'] == 2) {
                $_row_tmp['not_display'] = 0;
            } else {
                $_row_tmp['not_display'] = 1;
            }

            insertTimeline($_row_tmp['time'], $_row_tmp, $array_timline);
        }
    }
}

// cơ hội liên quan
$sql = "SELECT id,name, caregiver_id, timecreate, updatetime, status, orderid FROM " . NV_PREFIXLANG . "_crmbidding_opportunities WHERE active=1";
$where = [];
if (!empty($customer_info['email'])) {
    $where[] = "email = " . $db->quote($customer_info['email']) . "";
    $where[] = "FIND_IN_SET(" . $db->quote($customer_info['email']) . ", sub_email) ";
}
if (!empty($customer_info['address_bill'])) {
    $where[] = "email = " . $db->quote($customer_info['address_bill']) . "";
    $where[] = "FIND_IN_SET(" . $db->quote($customer_info['address_bill']) . ", sub_email) ";
}
if (!empty($customer_info['sub_email'])) {

    $_sub_email = explode(',', $customer_info['sub_email']);
    foreach ($_sub_email as $key => $value) {
        $where[] = "email = " . $db->quote($value);
        $where[] = "FIND_IN_SET(" . $db->quote($value) . ", sub_email) ";
    }
}
if (!empty($customer_info['phone'])) {
    $where[] = "phone_search = " . $db->quote($_tmp_phone);
    $where[] = "FIND_IN_SET(" . $db->quote($_tmp_phone) . ", sub_phone_search) ";
}
if (!empty($customer_info['contact_phone'])) {
    $where[] = "phone_search = " . $db->quote($_tmp_phone2);
    $where[] = "FIND_IN_SET(" . $db->quote($_tmp_phone2) . ", sub_phone_search) ";
}

if (!empty($where)) {
    $sql .= " AND (" . implode(' OR ', $where) . ") ORDER BY id DESC";

    $result = $db->query($sql);
    while ($_row = $result->fetch()) {
        $_row_tmp = [];
        $href = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=opportunities_info&amp;id=' . $_row['id'] . '&amp;showheader=1';
        $_row_tmp['type'] = 'opportunities';
        $_row_tmp['time'] = $_row['timecreate'];
        $_row_tmp['title'] = nv_date('H:i d/m/Y', $_row['timecreate']) . " - Opportunities - " . '<a href="' . $href . '" target="_blank">' . $_row['name'] . '</a>';
        $_row_tmp['body'] = '<p>- Trạng thái: ' . $nv_Lang->getModule('status_opportunities' . $_row['status']) . '</p>';
        $_row_tmp['body'] .= '<p>- Cập nhật lần cuối: ' . nv_date('H:i d/m/Y', $_row['updatetime']) . '</p>';
        if (!empty($array_admin_id[$_row['caregiver_id']])) {
            $_row_tmp['body'] .= '<p>- Người chăm sóc: ' . $array_admin_id[$_row['caregiver_id']]['username'] . '</p>';
        }

        $_row_tmp['not_display'] = 1;
        if ($_row['orderid'] != '') {
            // API hóa ListBiddingCustomsLog
            $infoAPI = [];
            $where = [];
            $where['AND'][] = [
                'IN' => [
                    'order_id' => '(' . $_row['orderid'] . ')'
                ]
            ];

            $infoAPI = [
                'where' => $where
            ];

            $APIListBiddingCustomsLog = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'ListBiddingCustomsLog', $infoAPI, 'bidding');
            if (isset($APIListBiddingCustomsLog['data'])) {
                foreach ($APIListBiddingCustomsLog['data'] as $_order) {
                    $_row_tmp['body'] .= '<br/>- Đã tạo đơn hàng: VIP ' . $_order['vip'] . ' lúc:' . nv_date('H:i d/m/Y', $_order['addtime']);
                    $_row_tmp['not_display'] = 0;
                }
            }
        }

        insertTimeline($_row_tmp['time'], $_row_tmp, $array_timline);
    }
}

// Timeline nhận mail sale gửi đến <EMAIL>
$where = [];
if (!empty($customer_info['email'])) {
    $where[] = "FIND_IN_SET(" . $db->quote($customer_info['email']) . ", send_to)";
}
if (!empty($customer_info['address_bill'])) {
    $where[] = "FIND_IN_SET(" . $db->quote($customer_info['address_bill']) . ", send_to)";
}
if (!empty($customer_info['sub_email'])) {
    $sub_email = array_unique(array_filter(array_map('trim', explode(',', $customer_info['sub_email']))));
    if (!empty($customer_info['email'])) {
        $sub_email = array_diff($sub_email, [
            $customer_info['email']
        ]);
    }
    if (empty($sub_email)) {
        foreach ($sub_email as $_sub_email) {
            $where[] = "FIND_IN_SET(" . $db->quote($_sub_email) . ", send_to)";
        }
    }
}

if (!empty($where)) {
    $sql = "SELECT email_id, mail_date, subject, sort_content, from_host, from_name, from_address
    FROM " . NV_PREFIXLANG . "_crmbidding_emails WHERE (" . implode(' OR ', $where) . ")";
    $result = $db->query($sql);
    while ($_row = $result->fetch()) {
        $_row_tmp = [];
        $_row_tmp['type'] = 'mail';
        $_row_tmp['time'] = $_row['mail_date'];
        $_row_tmp['title'] = nv_date('H:i d/m/Y', $_row['mail_date']) . " - " . $nv_Lang->getModule('timeline_recieve_email') . ' ';
        if (isset($array_admin_mails[$_row['from_address']])) {
            $_row_tmp['title'] .= $array_admin_mails[$_row['from_address']]['full_name'];
        } else {
            $_row_tmp['title'] .= $_row['from_address'];
            if (!empty($_row['from_name'])) {
                $_row_tmp['title'] .= ' (' . $_row['from_name'] . ')';
            }
        }
        $href = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=imapdetail&amp;id=' . $_row['email_id'];

        $_body = [];
        $_body[] = '<b>' . $nv_Lang->getModule('timeline_mailsubject') . '</b>: ' . (empty($_row['subject']) ? 'No subject' : $_row['subject']);
        if (!empty($_row['sort_content'])) {
            $_body[] = '<b>' . $nv_Lang->getModule('timeline_mailscontent') . '</b>: ' . $_row['sort_content'];
        }
        if (!empty($_row['from_host'])) {
            $_body[] = '<b>' . $nv_Lang->getModule('timeline_mailfhost') . '</b>: ' . $_row['from_host'];
        }
        if (!empty($_row['from_name'])) {
            $_body[] = '<b>' . $nv_Lang->getModule('timeline_mailfname') . '</b>: ' . $_row['from_name'];
        }
        if (!empty($_row['from_address'])) {
            $_body[] = '<b>' . $nv_Lang->getModule('timeline_mailfaddress') . '</b>: ' . $_row['from_address'];
        }
        $_body[] = '<a href="' . $href . '" target="_blank">' . $nv_Lang->getModule('view_detai') . '</a>';
        $_row_tmp['body'] = implode('<br />', $_body);
        $_row_tmp['not_display'] = 1;

        insertTimeline($_row['mail_date'], $_row_tmp, $array_timline);
    }
}

// Hiển thị cảnh báo lỗi liên quan đến Bộ lọc
$check_vip = $db->query('SELECT * FROM ' . NV_CONFIG_GLOBALTABLE . ' WHERE config_name LIKE ' . $db->quote('%_filters'))
    ->fetchAll();

$params = [
    'name' => 'bidding',
    'site' => 'dauthau.info'
];
$api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
$api->setLang('vi')
    ->setAction('ListModuleConfig')
    ->setData($params);
$result_api = $api->execute();

if ($result_api['status'] == 'success') {
    foreach ($result_api['data'] as $k => $v) {
        $right = substr($k, -7);
        ;
        if ($right == 'filters') {
            $check_vip[] = [
                'config_name' => $k,
                'config_value' => $v
            ];
        }
    }
}

foreach ($check_vip as $k => $v) {
    $vip_filters[] = str_replace('vip', '', explode('_', $v['config_name'])[0]);
}

unset($check_vip);
// lấy những thông tin gói VIP đang sử dụng filter
$check_vip_filters = [];

if (!empty($vip_filters)) {
    foreach ($get_order_vip as $v) {
        if (in_array(needle: $v['vip'], $vip_filters) and $v['end_time'] >= NV_CURRENTTIME) {
            $check_vip_filters[] = $v;
        }
    }
}

// Kiểm tra filter

/* 1. API Gọi hàm xuất dữ liệu qua API */
$infoAPI = [];
$infoAPI = array(
    'user_id' => $id_user
);

/* 1. Dữ liệu trả kq thông qua API */
$ListBiddingFilter = CallAPI($infoAPI, 'ListBiddingFilter');

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('USER', $get_id_user);
$xtpl->assign('ADMIN_USER', $get_admin_id);
$xtpl->assign('COMMENT', $get_comment);
$xtpl->assign('LINK', $row_link);
$xtpl->assign('LINK_FILE', '/themes/' . $global_config['module_theme'] . '/');

// Kiểm tra Khách VIP chưa tạo bộ lọc sau 1 tháng từ khi kích hoạt => Hiện cảnh báo cho Sale
// 2592000 => 1 tháng
$arr_warning_filter = [];
foreach ($check_vip_filters as $k => $v) {
    if ($v['end_time'] >= NV_CURRENTTIME) {
        // Sau 1 tháng khách hàng chưa có bộ lọc hiện cảnh báo cho sale sau
        if (($v['from_time'] + 2592000) < NV_CURRENTTIME and $v['filter_titles'] == '') {
            $arr_warning_filter[] = $nv_Lang->getModule('vip' . $v['vip']);
        }
    }
}

// Đã tạo bộ lọc nhưng k tìm kiếm được bộ lọc đó
$arr_warning_filter_mai = [];
if (isset($ListBiddingFilter['data'])) {
    foreach ($ListBiddingFilter['data'] as $k => $v) {
        if ($v['stat_totalresult'] == 0 and $v['addtime'] + 2592000 < NV_CURRENTTIME) {
            $link_edit_filter = rtrim(URL_DTINFO, '/') . nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=filters&amp;userid=' . $id_user . '&id=' . $v['id'], true);

            $arr_warning_filter_mail[] = "<a href=" . $link_edit_filter . ">" . $v['title'] . '</a> (từ ngày ' . date('d/m/Y', $v['addtime']) . ' đến nay)';
        }
    }
}

sort($arr_warning_filter, SORT_NATURAL | SORT_FLAG_CASE);

$fullname_admin = nv_show_name_user($get_admin_id['first_name'], $get_admin_id['last_name'], $get_admin_id['userid']);

// $fullname_customer = nv_show_name_user($get_id_user['first_name'], $get_id_user['last_name'], $get_id_user['userid']);
if (!empty($arr_warning_filter)) {
    if (defined('NV_IS_MODADMIN')) {
        $checss = md5($admin_info['username'] . '_' . $admin_info['level']);
    } else {
        $checss = md5($id_user);
    }

    $link_filter = rtrim(URL_DTINFO, '/') . nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=filters&amp;userid=' . $id_user . '&amp;check_crm=1&amp;checkss=' . $checss, true);

    $xtpl->assign('WARING_FILTER', sprintf($nv_Lang->getModule('waring_filter'), implode(', ', $arr_warning_filter), $fullname_admin, $link_filter));
    $xtpl->parse('main.show_waring_filter');
}

if (!empty($arr_warning_filter_mail)) {
    $xtpl->assign('WARING_FILTER_EMAIL', sprintf($nv_Lang->getModule('waring_filter_email'), implode(', ', $arr_warning_filter_mail), $fullname_admin));
    $xtpl->parse('main.show_waring_filter_email');
}

if (!empty($ListBiddingCusomsEmailDie['data'])) {
    foreach ($ListBiddingCusomsEmailDie['data'] as $v) {
        $xtpl->assign('VALUE', $v);
        $xtpl->parse('main.show_email_die.loop');
    }
    $xtpl->parse('main.show_email_die');
}

$i = 1;
$sole = 0;
$arr_check = [];
$dem = 1;

if (!empty($array_timline)) {
    // show timeline
    krsort($array_timline, 1); // sắp xếp theo thứ tự giảm dần của thời gian

    foreach ($array_timline as $timline_i) {
        foreach ($timline_i as $timline) {
            $sole += empty($timline['not_display']) ? 1 : 0;
            $timline['class_li'] = '';
            $timline['id'] = $i;
            if ($sole % 2 == 0) {
                $timline['class_li'] = 'timeline-inverted';
            }
            $timline['class'] = 'primary';
            if ($timline['type'] == 'call') {
                $timline['icon'] = '<i class="fa fa-phone"></i>';
            } else if ($timline['type'] == 'user') {
                $timline['class'] = 'info';
                $timline['icon'] = '<i class="fa fa-user"></i>';
            } else if ($timline['type'] == 'order') {
                $timline['icon'] = '<i class="fa fa-shopping-cart"></i>';
                $timline['class'] = 'success';
            } else if ($timline['type'] == 'mail') {
                $timline['icon'] = '<i class="fa fa-envelope-open"></i>';
                $timline['class'] = 'warning';
            } else if ($timline['type'] == 'leads') {
                $timline['icon'] = '<i class="fa fa-cube"></i>';
                $timline['class'] = 'primary';
            } else if ($timline['type'] == 'opportunities') {
                $timline['icon'] = '<i class="fa fa-cubes"></i>';
                $timline['class'] = 'primary';
            } else if ($timline['type'] == 'emaildie') {
                $timline['icon'] = '<i class="fa fa-envelope"></i>';
                $timline['class'] = 'danger';
            } else if ($timline['type'] == 'chat') {
                $timline['icon'] = '<i class="fa fa-comments-o"></i>';
                $timline['class'] = 'warning';
            } else if ($timline['type'] == 'bannersdt_click') {
                $timline['icon'] = '<i class="fa fa-picture-o"></i>';
                $timline['class'] = 'warning';
            }

            $timline['display'] = (!empty($timline['not_display']) and $timline['not_display'] == 1) ? 'display: none;' : '';
            if (isset($timline['not_display']) && $timline['not_display'] == 1) {
                $x = $timline['id'];
                $arr_check[$timline['id']] = $timline;
                if (count($arr_check) == 1) {
                    $xtpl->assign('CLASS', 'hidentime');
                    $xtpl->assign('ID', 'hide_not_important' . $dem);
                    $xtpl->parse('main.timline.show_line');
                }
                $timline['class_hide'] = 'hide_not_important' . $dem;
            }

            if (isset($x) && ($x + 1 == $i)) {
                $x = 1;
                $arr_check = [];
                $dem++;
            }

            $xtpl->assign('TIMELINE', $timline);
            $xtpl->parse('main.timline');
            $i++;
        }
    }
}

//Danh sách mạng xã hội
$socials = SocialNetwork::cases();
foreach ($socials as $social) {
    $xtpl->assign('SOCIAL', [
        'key' => $social->value,
        'value' => $social->getLabel(),
    ]);
    $xtpl->parse('main.social_platform');
}

//Danh sách profile
$user_social_query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_user_socials WHERE userid = ' . $id_user);
$count_profile = $user_social_query->rowCount();
$xtpl->assign('DISPLAY_PROFILE', $count_profile > 0 ? 'table' : 'none');
while ($profile = $user_social_query->fetch()) {
    $profile['platform_label'] = SocialNetwork::from($profile['platform'])?->getLabel();
    $xtpl->assign('PROFILE', $profile);
    $xtpl->parse('main.social_profile');
}

// Parse giá tiền của khách
if (!empty($sum_total)) {
    foreach ($sum_total as $key => $value) {
        if (!empty($sum_total[$key])) {
            $sum_total[$key] = number_format($value, 0, ',', '.') . CURRENCY_UNIT;
        } else {
            $sum_total[$key] = 0;
        }
    }
    $xtpl->assign('SUM_TOTAL', $sum_total);
}

// Tổng tiền hiện tại của khách
if (!empty($get_wallet_money)) {
    $get_wallet_money['money_total'] = number_format($get_wallet_money['money_total'], 0, ',', '.') . ' ' . $get_wallet_money['money_unit'];
    $get_wallet_money['money_in'] = number_format($get_wallet_money['money_in'], 0, ',', '.') . ' ' . $get_wallet_money['money_unit'];
    $get_wallet_money['money_out'] = number_format($get_wallet_money['money_out'], 0, ',', '.') . ' ' . $get_wallet_money['money_unit'];
} else {
    $get_wallet_money = [];
    $get_wallet_money['money_total'] = 0;
    $get_wallet_money['money_in'] = 0;
    $get_wallet_money['money_out'] = 0;
}

$xtpl->assign('MONEY', $get_wallet_money);

// Số điểm của khách
if (!empty($get_point)) {
    $get_point['point_in'] = number_format($get_point['point_in'], 0, ',', '.');
    $get_point['point_out'] = number_format($get_point['point_out'], 0, ',', '.');
    $get_point['point_total'] = number_format($get_point['point_total'], 0, ',', '.');
} else {
    $get_point = [];
    $get_point['point_in'] = 0;
    $get_point['point_out'] = 0;
    $get_point['point_total'] = 0;
}
$xtpl->assign('POINT', $get_point);

// Hiển thị gói vip khi chọn
if (!empty($vip)) {
    foreach ($vip as $key => $value) {
        $xtpl->assign('OPTION', [
            'key' => $key,
            'title' => $value
            //'selected' => ($key == $row['enter_status']) ? ' selected="selected"' : ''
        ]);
        $xtpl->parse('main.view_vip.loop');
    }
    $xtpl->parse('main.view_vip');
}

$arr_status = [
    0 => $nv_Lang->getModule('history_payment_no'),
    1 => $nv_Lang->getModule('history_payment_send'),
    2 => $nv_Lang->getModule('history_payment_check'),
    3 => $nv_Lang->getModule('history_payment_cancel'),
    4 => $nv_Lang->getModule('history_payment_yes'),
    5 => $nv_Lang->getModule('history_payment_cancel_by_user'),
    6 => $nv_Lang->getModule('history_payment_cancel_by_admin')
];

if (!empty($get_order)) {
    $arrUser = [];
    $arrIdOrder = [];
    foreach ($get_order as $key => $value) {
        $arrIdOrder[] = $value['id'];
        $value['name_userid'] = 'N/A';
        $value['name_admin_id'] = 'N/A';
        $value['name_caregiver_id'] = 'N/A';
        $value['name_promo_userid'] = 'N/A';
        $value['name_add_adminid'] = 'N/A';
        $value['name_affiliate_userid'] = 'N/A';
        // Khách hàng
        $arrUser[$value['userid']] = $value['userid'];
        // Người khởi tạo
        $arrUser[$value['admin_id']] = $value['admin_id'];
        // Người chăm sóc
        $arrUser[$value['caregiver_id']] = $value['caregiver_id'];
        $arrUser[$value['promo_userid']] = $value['promo_userid'];
        $arrUser[$value['add_adminid']] = $value['add_adminid'];
        $arrUser[$value['affiliate_userid']] = $value['affiliate_userid'];
    }

    // Log của đơn hàng
    $infoAPI = [];
    $arr_where = [];
    $arr_where['AND'][] = [
        'IN' => [
            'order_id' => "(" . implode(',', $arrIdOrder) . ")"
        ]
    ];
    $infoAPI = array(
        'where' => $arr_where,
        'array_select' => 'order_id, vip, type_export, numbers_year'
    );
    $result_log = nv_call_api(API_API_URL, API_API_KEY, API_API_SECRET, 'ListBiddingCustomsLog', $infoAPI, 'bidding');
    if (!empty($result_log['data'])) {
        foreach ($result_log['data'] as $key => $value) {
            $array_logs[$value['order_id']][] = $value;
        }
    }

    if (!empty($arrUser)) {
        $user = $db->query('SELECT userid, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arrUser) . ')')->fetchAll();
        unset($arrUser);
        foreach ($user as $key => $value) {
            $arrUser[$value['userid']] = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
        }
        unset($user);
    }
    $stt = 1;
    foreach ($get_order as $key => $value) {
        $value['stt'] = $stt++;

        if (isset($arr_status[$value['status']])) {
            $value['name_status'] = $arr_status[$value['status']];
        }

        if ($value['status'] == 4) {
            $value['class_status'] = 'fieldset__order--success';
            $value['status_bg'] = 'price--status--success';
        } else {
            $value['class_status'] = 'fieldset__order--danger';
            $value['status_bg'] = 'price--status--danger';
            $xtpl->parse('main.view_order.loop.show_bell');
        }

        if ($value['is_expired'] == 1) {
            $value['is_expired'] = $nv_Lang->getModule('not_expired');
            $value['class_expired'] = 'not_is_expired';
        } else {
            $value['is_expired'] = $nv_Lang->getModule('yes_expired');
            $value['class_expired'] = 'is_expired';
        }
        if (isset($arr_source_money[$value['source_money']])) {
            $value['name_source_money'] = $arr_source_money[$value['source_money']];
        }

        $value['money'] = number_format($value['money'], 0, ',', '.') . CURRENCY_UNIT;
        $value['discount'] = number_format($value['discount'], 0, ',', '.') . CURRENCY_UNIT;
        $value['price_reduce'] = number_format($value['price_reduce'], 0, ',', '.') . CURRENCY_UNIT;
        $value['source_money'] = number_format($value['source_money'], 0, ',', '.') . CURRENCY_UNIT;
        $value['total_end'] = number_format($value['total_end'], 0, ',', '.') . CURRENCY_UNIT;
        $value['total'] = number_format($value['total'], 0, ',', '.') . CURRENCY_UNIT;
        $value['taxes_fees'] = number_format($value['taxes_fees'], 0, ',', '.') . CURRENCY_UNIT;
        $value['add_time'] = nv_date('d/m/Y H:i:s', $value['add_time']);
        $value['edit_time'] = nv_date('d/m/Y H:i:s', $value['edit_time']);
        $value['static_time'] = nv_date('d/m/Y H:i:s', $value['static_time']);
        $value['link_order'] = $view['order'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding' . '&amp;' . NV_OP_VARIABLE . '=payment&amp;vieworderid=' . $value['id'] . '&amp;userid=' . $value['userid'] . '&showheader=1';
        if (isset($arrUser[$value['userid']])) {
            $value['name_userid'] = $arrUser[$value['userid']];
        }

        if (isset($arrUser[$value['admin_id']])) {
            $value['name_admin_id'] = $arrUser[$value['admin_id']];
        }

        if (isset($arrUser[$value['caregiver_id']])) {
            $value['name_caregiver_id'] = $arrUser[$value['caregiver_id']];
        }

        if (isset($arrUser[$value['promo_userid']])) {
            $value['name_promo_userid'] = $arrUser[$value['promo_userid']];
        }

        if (isset($arrUser[$value['add_adminid']])) {
            $value['name_add_adminid'] = $arrUser[$value['add_adminid']];
        }

        if (isset($arrUser[$value['affiliate_userid']])) {
            $value['name_affiliate_userid'] = $arrUser[$value['affiliate_userid']];
        }
        $value['title'] = [];
        if (isset($array_logs[$value['id']])) {
            foreach ($array_logs[$value['id']] as $log) {
                if ($log['vip'] == 88) {
                    // VIP X1 không tính theo năm
                    if ($log['type_export'] == 1) {
                        $value['title'][] = 'gói <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . '</span>: ' . $nv_Lang->getModule('vip88_export_old');
                    } elseif ($log['type_export'] == 2) {
                        $value['title'][] = 'gói <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . '</span>: ' . $nv_Lang->getModule('vip88_export_now_month');
                    } else {
                        $value['title'][] = 'gói <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . '</span>: ' . $nv_Lang->getModule('vip88_export_now_year');
                    }
                } else if ($log['vip'] == 55) {
                    if (!empty($log['type_export'])) {
                        $value['title'][] = 'gói <span class="btn_order__vip">' . ($log['type_export'] == 1 ? $nv_Lang->getModule('vip55_year_lang') : $nv_Lang->getModule('vip55_month_lang')) . '</span>';
                    }
                } else if ($log['vip'] == 33 || $log['vip'] == 44) {
                    $value['title'][] = $log['numbers_year'] . ' tháng gói <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . '</span>';
                } else {
                    if ($log['numbers_year'] < 1) {
                        switch ($log['numbers_year']) {
                            case '0.08':
                                $value['title'][] = 'gói: <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . ' </span> với thời hạn ' . $nv_Lang->getModule('key_numer_year_' . $log['numbers_year']);
                                break;

                            case '0.25':
                                $value['title'][] = 'gói: <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . ' </span> với thời hạn ' . $nv_Lang->getModule('key_numer_year_' . $log['numbers_year']);
                                break;

                            case '0.5':
                                $value['title'][] = 'gói: <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . ' </span> với thời hạn ' . $nv_Lang->getModule('key_numer_year_' . $log['numbers_year']);
                                break;

                            case '0.75':
                                $value['title'][] = 'gói: <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . ' </span> với thời hạn ' . $nv_Lang->getModule('key_numer_year_' . $log['numbers_year']);
                                break;
                        }
                    } else {
                        // Các gói còn lại tính theo năm
                        $value['title'][] = $log['numbers_year'] . ' năm gói <span class="btn_order__vip">' . $nv_Lang->getModule('vip' . $log['vip']) . '</span>';
                    }
                }
            }
        }

        if (empty($value['title'])) {
            $value['title'] = 'Giao dịch chưa rõ nội dung';
        } else {
            $value['title'] = '<a href="' . $value['link_order'] . '" data-toggle="tooltip" title="Chi tiết gói ' . $nv_Lang->getModule('vip' . $log['vip']) . '"> Thanh toán ' . implode(', ', $value['title']) . '</a>';
        }
        $xtpl->assign('ORDER', $value);
        $xtpl->parse('main.view_order.loop');
    }

    $xtpl->parse('main.view_order');
}

$number = 1;
if (!empty($get_order_vip)) {
    foreach ($get_order_vip as $key => $value) {
        $value['name_admin_id'] = 'N/A';
        $arrId[] = $value['admin_id'];
    }

    if (!empty($arrId)) {
        $user = $db->query('SELECT userid, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arrId) . ')')->fetchAll();
        unset($arrId);
        foreach ($user as $key => $value) {
            $arrUser[$value['userid']] = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
        }
        unset($user);
    }

    foreach ($get_order_vip as $key => $value) {
        $value['stt'] = $number++;
        $value['id_vip'] = $value['vip'];
        if (in_array($value['vip'], $array_vip)) {
            $value['vip'] = $value['vip'] == 55 ? ($value['type_export'] == 1 ? $nv_Lang->getModule('vip55_year') : $nv_Lang->getModule('vip55_month')) : $nv_Lang->getModule('vip' . $value['vip']);
        }
        $value['class_status'] = ($value['status'] == 1) ? 'label label-primary' : 'label label-danger';
        $value['status'] = ($value['status'] == 1) ? $nv_Lang->getModule('hieuluc') : $nv_Lang->getModule('hethan');
        $value['fullname'] = $get_id_user['fullname'];
        $value['from_time'] = nv_date('d/m/Y H:i:s', $value['from_time']);
        $value['end_time'] = nv_date('d/m/Y H:i:s', $value['end_time']);
        $value['deal_time'] = nv_date('d/m/Y H:i:s', $value['deal_time']);
        $value['deal_price'] = number_format($value['deal_price'], 0, ',', '.') . CURRENCY_UNIT;
        $value['link_vip'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding' . '&amp;' . NV_OP_VARIABLE . '=cus_info&amp;id=' . $value['user_id'] . '-' . $value['id_vip'];
        $value['sum_viptime'] = nv_convertfromSec($value['sum_viptime']);
        if (isset($arrUser[$value['admin_id']])) {
            $value['name_admin_id'] = $arrUser[$value['admin_id']];
        }
        $xtpl->assign('ORDER', $value);
        $xtpl->parse('main.view_ordervip.loop');
    }

    $xtpl->parse('main.view_ordervip');
}

$history = show_history_support($array_comment, $array_vip, $id_user, $arr_note);
if (!empty(trim($history))) {
    $xtpl->assign('HISTORY_SUPPORT', $history);
    $xtpl->parse('main.show_support');
    $xtpl->parse('main.show_support1');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';

function show_history_support($get_comment, $array_vip, $id_user, $arr_note)
{
    global $module_info, $op, $global_config, $module_file, $admin_info, $db, $array_logs, $nv_Lang;

    $xtpl = new XTemplate('history_support.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    if (!empty($get_comment)) {
        $arrUserID = [];
        foreach ($get_comment as $key => $value) {
            $value['name_admin_id'] = 'N/A';
            $value['fullname'] = 'N/A';
            $value['name_vip'] = 0;
            // Người comment
            $arrUserID[$value['post_id']] = $value['post_id'];

            if ($value['source'] == 1 || $value['source'] == 2) {
                $arrUserID[$id_user] = $id_user;
            } else {
                $arrUserID[$value['sourceid']] = $value['sourceid'];
            }

            if (isset($arr_note[$value['source']])) {
                $get_comment[$key]['source_note'] = $arr_note[$value['source']];
            }

            if ($value['source'] == 1 && isset($array_logs[$value['sourceid']])) {
                foreach ($array_logs[$value['sourceid']] as $log) {
                    if ($log['vip'] == 88) {
                        // VIP X1 không tính theo năm
                        if ($log['type_export'] == 1) {
                            $value['title'][] = 'gói <span>' . $nv_Lang->getModule('vip' . $log['vip']) . '</span>: ' . $nv_Lang->getModule('vip88_export_old');
                        } elseif ($log['type_export'] == 2) {
                            $value['title'][] = 'gói <span>' . $nv_Lang->getModule('vip' . $log['vip']) . '</span>: ' . $nv_Lang->getModule('vip88_export_now_month');
                        } else {
                            $value['title'][] = 'gói <span>' . $nv_Lang->getModule('vip' . $log['vip']) . '</span>: ' . $nv_Lang->getModule('vip88_export_now_year');
                        }
                    } else if ($log['vip'] == 55) {
                        if (!empty($log['type_export'])) {
                            $value['title'][] = 'gói <span>' . ($log['type_export'] == 1 ? $nv_Lang->getModule('vip55_year') : $nv_Lang->getModule('vip55_month')) . '</span>';
                        }
                    } else {
                        // Các gói còn lại tính theo năm
                        $value['title'][] = $log['numbers_year'] . ' năm gói <span>' . $nv_Lang->getModule('vip' . $log['vip']) . '</span>';
                    }
                }
            }
            $get_comment[$key]['link_order'] = '';
            if ($value['source'] == 1) {
                $get_comment[$key]['link_order'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding' . '&amp;' . NV_OP_VARIABLE . '=payment&amp;vieworderid=' . $value['sourceid'] . '&amp;userid=' . $id_user . '&showheader=1';
            }

            if (empty($value['title'])) {
                $get_comment[$key]['title'] = 'Giao dịch chưa rõ nội dung';
            } else {
                $get_comment[$key]['title'] = '<a href="' . $get_comment[$key]['link_order'] . '" data-toggle="tooltip" title="Chi tiết gói ' . $nv_Lang->getModule('vip' . $log['vip']) . '"> Thanh toán ' . implode(', ', $value['title']) . '</a>';
            }

            if (isset($value['vip']) && in_array($value['vip'], $array_vip)) {
                if (!empty($value['origin']) && $value['origin'] == 'dauthau.net') {
                    $get_comment[$key]['link_vip'] = URL_DTNET_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=dn' . '&amp;' . NV_OP_VARIABLE . '=cus_info&amp;id=' . $value['vips_sourceid'];
                    $get_comment[$key]['name_vip'] = $nv_Lang->getModule($value['vip']);
                } else {
                    $get_comment[$key]['link_vip'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding' . '&amp;' . NV_OP_VARIABLE . '=cus_info&amp;id=' . $id_user . '-' . $value['vip'];
                    $get_comment[$key]['name_vip'] = $nv_Lang->getModule('vip' . $value['vip']);
                }
            }
        }

        if (!empty($arrUserID)) {
            $user = $db->query('SELECT userid, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arrUserID) . ')')->fetchAll();
            unset($arrUserID);
            foreach ($user as $key => $value) {
                $arrUserID[$value['userid']] = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
            }
            unset($user);
        }

        foreach ($get_comment as $key => $value) {
            $value['note'] = nv_br2nl($value['note']);
            if (isset($arrUserID[$value['post_id']])) {
                $value['name_admin_id'] = $arrUserID[$value['post_id']];
            }

            if ($value['source'] == 3 && isset($arrUserID[$value['sourceid']])) {
                $value['fullname'] = $arrUserID[$value['sourceid']];
            }

            if (($value['source'] == 1 || $value['source'] == 2 || $value['source'] == 4) && isset($arrUserID[$id_user])) {
                $value['fullname'] = $arrUserID[$id_user];
            }

            // Hiển thị icon new <= 24h
            if ((NV_CURRENTTIME - $value['timecreate']) <= 86400) {
                $xtpl->parse('main.view_comment.loop.show_new');
            }
            $value['stt'] = $key + 1;
            $value['timecreate'] = nv_date('d/m/Y H:i:s', $value['timecreate']);
            if ($value['update_time'] != 0) {
                $value['update_time'] = nv_date('d/m/Y H:i:s', $value['update_time']);
            }
            $value['siteid'] = 'dauthau.info'; // fix tạm vì dauthau.net k thấy lưu lại giá trị
            $xtpl->assign('COMMENT', $value);

            if (isset($value['vip']) && in_array($value['vip'], $array_vip)) {
                $xtpl->parse('main.view_comment.loop.show_linkvip');
                $xtpl->parse('main.view_comment.loop.show_linkvip1');
            }

            if ($admin_info['userid'] == $value['post_id'] && $value['sourceid'] == $id_user) {
                $xtpl->parse('main.view_comment.loop.show_action');
            }

            if ($value['source'] == 1) {
                $xtpl->parse('main.view_comment.loop.show_linkorder');
                $xtpl->parse('main.view_comment.loop.show_linkorder1');
            }

            $xtpl->parse('main.view_comment.loop');
        }
        $xtpl->parse('main.view_comment');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

// hiển thị api ListProfile
function show_api_list_profile($array_profile_dtnet, $id_user, $base_url, $num_items, $per_page, $page)
{
    global $module_info, $op, $global_config, $module_file, $admin_info, $db, $nv_Lang;

    $xtpl = new XTemplate('detail_customer_list_profile.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    $stt = $per_page * ($page - 1);
    foreach ($array_profile_dtnet as $key => $value) {
        $stt++;
        $value['stt'] = $stt;
        $value['add_time'] = nv_date('d/m/Y', $value['add_time']);
        if ($value['status'] == 3 or $value['status'] == 4 or $value['status'] == 5) {

            $value['status_txt'] = '<a href="' . URL_DTNET_ADMIN . 'index.php?language=vi&nv=dn&op=detail&active_id=' . $value['active_id'] . '">' . $nv_Lang->getModule('active_' . $value['status']) . '</a>';
        } else if ($value['status'] == 10 || $value['status'] == 11 || $value['status'] == 12) {
            $value['status_txt'] = $nv_Lang->getModule('venture_' . $value['status']);
        } else {
            // đã xác thực hoặc cần xác thực lại
            $value['status_txt'] = $nv_Lang->getModule('active_' . $value['status']);
        }

        $value['prof_code_view'] = $value['info_phone_view'] = $value['info_email_view'] = '';
        if (!empty($value['prof_code'])) {
            $value['prof_code_view'] = '<br/> MST: ' . $value['prof_code'];
        }
        if (!empty($value['info_phone'])) {
            $value['info_phone_view'] = '<br/> SĐT: ' . $value['info_phone'];
        }
        if (!empty($value['info_email'])) {
            $value['info_email_view'] = '<br/> Email: ' . $value['info_email'];
        }

        $value['fee_status_parse'] = $value['status_fee'] > 0 ? $nv_Lang->getModule('fee_status1') : $nv_Lang->getModule('fee_status0');
        $value['fee_expired_parse'] = nv_date('d/m/Y', $value['fee_expired']);
        $value['link_view'] = 'https://dauthau.net/vi/dn/' . $value['prof_alias'];

        $xtpl->assign('PROFILE', $value);
        if ($value['status'] == 1 or $value['status'] == 2 or $value['status'] == 4) {
            $xtpl->parse('main.loop.confirmed');
        } else {
            $xtpl->parse('main.loop.unconfirmed');
        }
        $xtpl->parse('main.loop');
    }
    // phân trang
    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
        $xtpl->parse('main.generate_page_js');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

// hiển thị api ListTicket
function show_api_list_ticket($array_ticket, $id_user, $base_url, $num_items, $per_page, $page)
{
    global $module_info, $op, $global_config, $module_file, $admin_info, $db, $nv_Lang;

    $xtpl = new XTemplate('detail_customer_list_ticket.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    $stt = $per_page * ($page - 1);
    foreach ($array_ticket as $key => $value) {
        $stt++;
        $value['stt'] = $stt;
        $value['add_time'] = nv_date('H:i d/m/Y', $value['add_time']);
        $value['edit_time'] = $value['edit_time'] > 0 ? nv_date('H:i d/m/Y', $value['edit_time']) : '';
        if (!empty($value['assignee_to'])) {
            $assignee_fullname = [];
            foreach ($value['assignee'] as $assignee) {
                $assignee_fullname[] = nv_show_name_user($assignee['first_name'], $assignee['last_name'], $assignee['userid']);
            }
            $value['assignee'] = implode(', ', $assignee_fullname);
        } else {
            $value['assignee'] = '';
        }
        if (!empty($value['label_ids'])) {
            $label_title = [];
            foreach ($value['label'] as $label) {
                $label_title[] = $label['title_' . NV_LANG_DATA];
            }
            $value['label'] = implode(', ', $label_title);
        } else {
            $value['label'] = '';
        }

        $xtpl->assign('TICKET', $value);
        $xtpl->parse('main.loop');
    }
    // phân trang
    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
        $xtpl->parse('main.generate_page_js');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

// hiển thị api ListVip
function show_api_list_vip($array_vip_dtnet, $id_user, $base_url, $num_items, $per_page, $page)
{
    global $module_info, $op, $global_config, $module_file, $admin_info, $db, $nv_Lang;

    $admin_ids = array_map(fn($value): int => $value['admin_id'], $array_vip_dtnet);
    $users = $db->query('SELECT userid, username, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $admin_ids) . ')')->fetchAll();
    $usersMap = [];
    foreach ($users as $key => $value) {
        $usersMap[$value['userid']] = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']) . ' (' . $value['username'] . ')';
    }

    $xtpl = new XTemplate('detail_customer_list_vip.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    $stt = $per_page * ($page - 1);
    foreach ($array_vip_dtnet as $key => $value) {
        $stt++;
        $value['stt'] = $stt;
        $value['from_time'] = nv_date('d/m/Y', $value['from_time']);
        $value['end_time'] = nv_date('d/m/Y', $value['end_time']);
        $value['sum_viptime'] = nv_convertfromSec($value['sum_viptime']);
        $value['class_status'] = ($value['status'] == 1) ? 'label label-primary' : 'label label-danger';
        $value['status'] = ($value['status'] == 1) ? $nv_Lang->getModule('hieuluc') : $nv_Lang->getModule('hethan');
        $value['email'] = $value['email_bill'];
        $value['link_vip'] = URL_DTNET_ADMIN . 'index.php?language=vi&nv=dn&op=cus_info&showheader=1&id=' . $value['id'];
        $value['vip_title'] = $nv_Lang->getModule($value['vip']);
        $value['name_admin_id'] = isset($usersMap[$value['admin_id']]) ? $usersMap[$value['admin_id']] : 'N/A';
        $xtpl->assign('VIP', $value);
        $xtpl->parse('main.loop');
    }
    // phân trang
    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
        $xtpl->parse('main.generate_page_js');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

// hiển thị api ListPayment
function show_api_list_payment($array_payment_dtnet, $base_url, $num_items, $per_page, $page)
{
    global $module_info, $op, $global_config, $module_file, $admin_info, $db, $arr_source_money, $nv_Lang;

    $xtpl = new XTemplate('detail_customer_list_payment.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    foreach ($array_payment_dtnet as $key => $value) {
        $arrIdOrder[] = $value['id'];
        $value['name_userid'] = 'N/A';
        $value['name_admin_id'] = 'N/A';
        $value['name_caregiver_id'] = 'N/A';
        $value['name_promo_userid'] = 'N/A';
        $value['name_add_adminid'] = 'N/A';
        $value['name_affiliate_userid'] = 'N/A';
        // Khách hàng
        $arrUser[$value['userid']] = $value['userid'];
        // Người khởi tạo
        $arrUser[$value['admin_id']] = $value['admin_id'];
        // Người chăm sóc
        $arrUser[$value['caregiver_id']] = $value['caregiver_id'];
        $arrUser[$value['promo_userid']] = $value['promo_userid'];
        $arrUser[$value['add_adminid']] = $value['add_adminid'];
        $arrUser[$value['affiliate_userid']] = $value['affiliate_userid'];
    }

    $where_api = [];
    $fields = [
        'pay_id',
        'vip',
        'vip_title',
        'numbers_year'
    ];
    foreach ($arrIdOrder as $key => $value) {
        $where_api['OR'][] = [
            '=' => [
                'pay_id' => $value
            ]
        ];
    }

    $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
    $api->setModule('')
        ->setLang('vi')
        ->setAction('ListPayLog')
        ->setData([
        'page' => 1,
        'per_page' => $per_page,
        'fields' => $fields,
        'where' => $where_api
    ]);
    $result = $api->execute();
    $error = $api->getError();

    if (empty($error) and $result['status'] == 'success') {
        if ($result['total'] > 0) {
            foreach ($result['data'] as $key => $value) {
                $array_logs[$value['pay_id']] = $value;
            }
        }
    }

    if (!empty($arrUser)) {
        $user = $db->query('SELECT userid, first_name, last_name FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arrUser) . ')')->fetchAll();
        unset($arrUser);
        foreach ($user as $key => $value) {
            $arrUser[$value['userid']] = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
        }
        unset($user);
    }

    // Trạng thái
    $arr_status = [
        $nv_Lang->getModule('history_payment_no'),
        $nv_Lang->getModule('history_payment_send'),
        $nv_Lang->getModule('history_payment_check'),
        $nv_Lang->getModule('history_payment_cancel'),
        $nv_Lang->getModule('history_payment_yes')
    ];

    $stt = $per_page * ($page - 1);
    foreach ($array_payment_dtnet as $key => $value) {
        $stt++;
        $value['stt'] = $stt;
        if (isset($arr_status[$value['status']])) {
            $value['name_status'] = $arr_status[$value['status']];
        }

        if ($value['status'] == 4) {
            $value['class_status'] = 'fieldset__order--success';
            $value['status_bg'] = 'price--status--success';
        } else {
            $value['class_status'] = 'fieldset__order--danger';
            $value['status_bg'] = 'price--status--danger';
            $xtpl->parse('main.view_order.loop.show_bell');
        }

        if ($value['is_expired'] == 1) {
            $value['is_expired'] = $nv_Lang->getModule('not_expired');
            $value['class_expired'] = 'not_is_expired';
        } else {
            $value['is_expired'] = $nv_Lang->getModule('yes_expired');
            $value['class_expired'] = 'is_expired';
        }
        if (isset($arr_source_money[$value['source_money']])) {
            $value['name_source_money'] = $arr_source_money[$value['source_money']];
        }

        $value['money'] = number_format($value['money'], 0, ',', '.') . CURRENCY_UNIT;
        $value['discount'] = number_format($value['discount'], 0, ',', '.') . CURRENCY_UNIT;
        $value['price_reduce'] = number_format($value['price_reduce'], 0, ',', '.') . CURRENCY_UNIT;
        $value['source_money'] = number_format($value['source_money'], 0, ',', '.') . CURRENCY_UNIT;
        $value['total_end'] = number_format($value['total_end'], 0, ',', '.') . CURRENCY_UNIT;
        $value['total'] = number_format($value['total'], 0, ',', '.') . CURRENCY_UNIT;
        $value['taxes_fees'] = number_format($value['taxes_fees'], 0, ',', '.') . CURRENCY_UNIT;
        $value['add_time'] = nv_date('d/m/Y H:i:s', $value['add_time']);
        $value['edit_time'] = nv_date('d/m/Y H:i:s', $value['edit_time']);
        $value['static_time'] = nv_date('d/m/Y H:i:s', $value['static_time']);
        $value['link_order'] = $view['order'] = URL_DTNET_ADMIN . 'index.php?language=vi&nv=dn&op=payment&vieworderid=' . $value['id'];
        if (isset($arrUser[$value['userid']])) {
            $value['name_userid'] = $arrUser[$value['userid']];
        }

        if (isset($arrUser[$value['admin_id']])) {
            $value['name_admin_id'] = $arrUser[$value['admin_id']];
        }

        if (isset($arrUser[$value['caregiver_id']])) {
            $value['name_caregiver_id'] = $arrUser[$value['caregiver_id']];
        }

        if (isset($arrUser[$value['promo_userid']])) {
            $value['name_promo_userid'] = $arrUser[$value['promo_userid']];
        }

        if (isset($arrUser[$value['add_adminid']])) {
            $value['name_add_adminid'] = $arrUser[$value['add_adminid']];
        }

        if (isset($arrUser[$value['affiliate_userid']])) {
            $value['name_affiliate_userid'] = $arrUser[$value['affiliate_userid']];
        }
        $value['title'] = '';
        if (isset($array_logs[$value['id']])) {
            if (!empty($array_logs[$value['id']]['vip'])) {
                $value['title'] = $array_logs[$value['id']]['numbers_year'] . ' năm gói <span class="btn_order__vip">' . $array_logs[$value['id']]['vip_title'] . '</span>';
            } else {
                $value['title'] = $array_logs[$value['id']]['numbers_year'] . ' năm gia hạn hồ sơ';
            }
        }

        if ($value['title'] == '') {
            $value['title'] = 'Giao dịch chưa rõ nội dung';
        } else {
            $value['title'] = '<a href="' . $value['link_order'] . '" data-toggle="tooltip" title="Chi tiết gói ' . $array_logs[$value['id']]['vip_title'] . '"> Thanh toán ' . $value['title'] . '</a>';
        }
        $xtpl->assign('ORDER', $value);

        $xtpl->parse('main.loop');
    }
    // phân trang
    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
        $xtpl->parse('main.generate_page_js');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function CallAPI($infoAPI, $API)
{
    if (isset($infoAPI['page'])) {
        $infoAPI['page'] = intval($infoAPI['page']);
    }

    if (isset($infoAPI['perpage'])) {
        $infoAPI['perpage'] = intval($infoAPI['perpage']);
    }

    foreach ($infoAPI as $key => $value) {
        $params_customs[$key] = $value;
    }

    $api = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
    $api->setModule('bidding')
        ->setLang('vi')
        ->setAction($API)
        ->setData($params_customs);
    $result = $api->execute();
    $error = $api->getError();
    $data = [];
    if (empty($error) and $result['status'] == 'success') {
        $data = $result;
    }
    return $data;
}
