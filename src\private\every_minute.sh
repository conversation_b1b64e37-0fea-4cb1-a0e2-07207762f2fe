#!/bin/sh

DIR_PATH=$(dirname ${BASH_SOURCE[0]})
echo "$DIR_PATH"

if [ -f "$DIR_PATH/logs/every_minute.txt" ]; then
    if [ -n "$(find $DIR_PATH/logs/ -type f -name every_minute.txt -mmin +10)" ]; then
        # 10 phút mà còn file này thì xóa để chạy đè tiến trình
        rm -f $DIR_PATH/logs/every_minute.txt
    else
        echo "Đang bận";
        exit
    fi
fi

echo "$(date +%Y-%m-%d-%T)" > "$DIR_PATH/logs/every_minute.txt"

START=$(date +%s)
crawls_logs="$DIR_PATH/logs/every_minute_$(date +%Y-%m-%d).txt"
echo "$(date +%Y-%m-%d-%T)" >> $crawls_logs

# Crontab này không đặt ở private vì gọi đến cả module point để lấy code và có cả xóa cache của module
echo "---- <PERSON>ử lý điểm  ----";
basetime=$(date +%s%N)
php $DIR_PATH/point-handling.php
echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds point-handling" >> $crawls_logs

basetime=$(date +%s%N)
php $DIR_PATH/static_point.php
echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds static_point" >> $crawls_logs

echo "---- Đẩy liên hệ lên site chính  ----";
basetime=$(date +%s%N)
php $DIR_PATH/marketing-satellite.php segments:push >/dev/null 2>&1
echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds marketing-satellite" >> $crawls_logs

# kiểm tra file static_sale.txt có tồn tại k thì chạy static_sales.php
# kiểm tra lại đường dẫn thực tế bên crm NV_ROOTDIR . '/data/logs/static_sale.txt
if [ -f "/home/<USER>/public_html/data/logs/static_sale.txt" ]; then
    php /home/<USER>/private/static_sales.php
fi

echo "Kết thúc"
echo "Tổng cộng: $(($(date +%s) - $START)) giây"

rm -f "$DIR_PATH/logs/every_minute.txt"

echo "Total time : $(($(date +%s) - $START))" >> $crawls_logs
echo "--------------------END---------------------------" >> $crawls_logs

