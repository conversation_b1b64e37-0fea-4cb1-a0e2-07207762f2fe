<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\supportticket\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use NukeViet\Dauthau\Condition;
// use NukeViet\Dauthau\Group;
// use NukeViet\Dauthau\Order;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class ListAllComment implements IApi
{

    private $result;
    private $page = 0;
    private $perpage = 0;
    private $total = 0;
    private $where = [];
    private $order_string = 'log_id DESC';

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'ticket';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $nv_Request;

        $this->page = $nv_Request->get_int('page', 'post', 1);
        $this->perpage = $nv_Request->get_int('perpage', 'post', 50);
        $ticket_id = $nv_Request->get_int('ticket_id', 'post', 0);

        if ($this->page <= 0 and $this->page > 9999) {
            return $this->result->setError()
                ->setCode('2005')
                ->setMessage('Page is integer, more than 0')
                ->getResult();
        }
        if ($this->perpage > 50) {
            return $this->result->setError()
                ->setCode('2002')
                ->setMessage('Perpage to larger')
                ->getResult();
        }

        $array_where = $nv_Request->get_array('where', 'post');
        if (!is_array($array_where)) {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Param where invalid to array')
                ->getResult();
        }

        // key cho phép trong where
        $keys_check = [
            'reply_userid',
            'add_time',
            'edit_time',
            'refund_status',
            'rating_add_time',
            'delete_time',
            'status',
            'invalid',
            'comment_type',
            'ticket_id',
            'rating_number',
            'is_paid',
            'ticket_add_time',
        ];

        if ($ticket_id > 0) {
            $this->where[] = 'ticket_id=' . $ticket_id;
        } else {
            $this->where[] = 'ticket_id>0';
        }

        if (!empty($array_where)) {
            foreach ($array_where as $keys) {
                foreach ($keys as $key) {
                    $operator = array_key_first($key);
                    $field = array_key_first($key[$operator]);
                    if (!in_array($field, $keys_check)) {
                        return $this->result->setError()
                            ->setCode('2001')
                            ->setMessage('Missing field ' . $field . ' in data')
                            ->getResult();
                    }
                }
            }

            $condition = new Condition();
            $this->where[] = $condition->toSqlString($array_where);
        }

        try {
            $arr_data = $this->mysql();
            if (!empty($arr_data)) {
                $this->result->setSuccess();
                $this->result->set('total', $this->total);
                $this->result->set('perpage', $this->perpage);
                $this->result->set('page', $this->page);
                $this->result->set('data', $arr_data);
            } else {
                $this->result->setError()
                    ->setCode('4000')
                    ->setMessage('Empty data');
            }
        } catch (PDOException $e) {
            $this->result->setError()
                ->setCode('3000')
                ->setMessage(print_r($e, true));
        }

        return $this->result->getResult();
    }

    private function mysql()
    {
        global $db, $db_config, $admin_info, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];

        //key select
        $keys_view = [
            'log_id', 'ticket_id', 'reply_userid', 'comment_type', 'parent',
            'status', 'status_view', 'point_offer', 'point_quote', 'point_final', 'point_refund',
            'payment_status','rating_number', 'rating_content', 'rating_reply', 'refund_status',
            'area','display_userid','content', 'file_attach', 'invalid',
            'add_time', 'edit_time', 'delete_time', 'rating_add_time', 'rating_edit_time', 'reply_add_time',
            'reply_edit_time', 'is_paid', 'ticket_add_time',
        ];

        $db->sqlreset()
            ->select('COUNT(log_id) as num')
            ->from(TB_TICKET_LOG);

        if (!empty($this->where)) {
            $db->where(implode(' AND ', $this->where));
        }
        if (!empty($this->group_string)) {
            $db->group($this->group_string);
            $keys_view[] = 'count(log_id) as num';
        }

        $sth = $db->prepare($db->sql());
        $sth->execute();
        $this->total = $sth->fetchColumn();
        $db->select(implode(",", $keys_view));

        $db->order($this->order_string);
        if ($this->perpage > 0) {
            $db->limit($this->perpage)->offset(($this->page - 1) * $this->perpage);
        }

        $sth = $db->prepare($db->sql());
        $sth->execute();
        $arr_data = [];

        while ($view = $sth->fetch()) {
            $arr_data[] = $view;
        }
        return $arr_data;
    }

    private function elastic()
    {
        return [];
    }
}
