<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Module\crmbidding\Log;

$showheader = $nv_Request->get_int('showheader', 'post,get', 1);
$error = [];

$q = $nv_Request->get_title('q', 'post,get');
$orderby_hoten = $nv_Request->get_int('orderby_hoten', 'post,get', 0);
$orderby_time = $nv_Request->get_int('orderby_time', 'post,get', 0);

$sfrom = nv_d2u_get($nv_Request->get_title('time_from', 'post,get', '01-' . nv_date('m-Y', NV_CURRENTTIME)), 0, 0, 0);
$sto = nv_d2u_get($nv_Request->get_title('time_to', 'post,get', nv_date('d-m-Y', NV_CURRENTTIME)), 23, 59, 59);

$per_page = 100;
$page = $nv_Request->get_page('page', 'post,get', 1);
$db->sqlreset()
    ->select('COUNT(*)')
    ->from('' . NV_PREFIXLANG . '_' . $module_data . '_zalo');
$where = [];

$where[] = 'thoi_gian_gui >= ' . $sfrom;
$where[] = 'thoi_gian_gui <= ' . $sto;

if (!empty($q)) {
    $where[] = '(hoten LIKE :q_hoten OR sdt LIKE :q_sdt OR email LIKE :q_email)';
}

if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}

$sth = $db->prepare($db->sql());

if (!empty($q)) {
    $sth->bindValue(':q_hoten', '%' . $q . '%');
    $sth->bindValue(':q_sdt', '%' . $q . '%');
    $sth->bindValue(':q_email', '%' . $q . '%');
}
$sth->execute();
$num_items = $sth->fetchColumn();
$order = 'thoi_gian_gui DESC';
if ($orderby_hoten > 0) {
    $order = $orderby_hoten == 1 ? 'hoten ASC' : 'hoten DESC';
}
if ($orderby_time > 0) {
    $order = $orderby_time == 1 ? 'thoi_gian_gui ASC' : 'thoi_gian_gui DESC';
}
$db->select('*')
    ->order($order)
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$sth = $db->prepare($db->sql());

if (!empty($q)) {
    $sth->bindValue(':q_hoten', '%' . $q . '%');
    $sth->bindValue(':q_sdt', '%' . $q . '%');
    $sth->bindValue(':q_email', '%' . $q . '%');
}

$sth->execute();

$array_zalo = [];
$number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;
while ($view = $sth->fetch()) {
    $view['number'] = $number++;
    $view['thoi_gian_gui'] = nv_date("H:m d/m/y", $view['thoi_gian_gui']);
    $array_zalo[] = $view;
}

$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;showheader=' . $showheader;
if (!empty($q)) {
    $base_url .= '&amp;q=' . $q;
}
$base_url .= '&amp;time_from=' . urlencode(nv_u2d_get($sfrom));
$base_url .= '&amp;time_to=' . urlencode(nv_u2d_get($sto));

// URL sắp xếp theo họ tên
$link_orderby_hoten = $base_url . '&amp;orderby_hoten=1';
if ($orderby_hoten > 0) {
    $base_url .= '&amp;orderby_hoten=' . $orderby_hoten;
}

// URL sắp xếp theo thời gian
$link_orderby_time = $base_url . '&amp;orderby_time=1';
if ($orderby_time > 0) {
    $base_url .= '&amp;orderby_time=' . $orderby_time;
}

$sort_urls = [
    'hoten' => [
        'link' => $link_orderby_hoten . '&amp;page=' . $page,
        'asc' => $base_url . '&amp;orderby_hoten=1' . '&amp;page=' . $page,
        'desc' => $base_url . '&amp;orderby_hoten=2' . '&amp;page=' . $page,
        'active' => $orderby_hoten
    ],
    'time' => [
        'link' => $link_orderby_time . '&amp;page=' . $page,
        'asc' => $base_url . '&amp;orderby_time=1' . '&amp;page=' . $page,
        'desc' => $base_url . '&amp;orderby_time=2' . '&amp;page=' . $page,
        'active' => $orderby_time
    ]
];

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);

$tpl = new \NukeViet\Template\NVSmarty();
$tpl->setTemplateDir(NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$tpl->assign('LANG', $nv_Lang);
$tpl->assign('MODULE_NAME', $module_name);
$tpl->assign('OP', $op);
$tpl->assign('Q', $q);
$tpl->assign('ARRAY_ZALO', $array_zalo);
$tpl->assign('PAGINATION', $generate_page);
$tpl->assign('SEARCH', [
    'q' => $q,
    'time_from' => nv_u2d_get($sfrom),
    'time_to' => nv_u2d_get($sto)
]);
$tpl->assign('SORT_URLS', $sort_urls);
$tpl->assign('SHOWHEADER', $showheader);
$tpl->assign('ERROR', $error);

$contents = $tpl->fetch('list-zalo.tpl');
$page_title = $nv_Lang->getModule('ds_khach_zalo');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';
