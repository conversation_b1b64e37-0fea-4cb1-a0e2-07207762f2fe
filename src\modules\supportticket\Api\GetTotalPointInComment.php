<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\supportticket\Api;

use CommentStatus;
use CommentType;
use PaymentStatus;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetTotalPointInComment implements IApi
{

    private $result;
    private $reply_userid;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'ticket';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $db_config, $nv_Lang, $admin_info, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_file = $module_info['module_file'];

        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $ticket_id = $nv_Request->get_int('ticket_id', 'post', 0);
        $customer_id = $nv_Request->get_int('customer_id', 'post', 0);
        if ($ticket_id > 0) {
            // Lấy thông tin điểm khách hàng đã chi trả ở bảng nv4_supportticket_point_logs
            $sql_customer_log_point = 'SELECT id, comment_id, point, status FROM ' . TB_TICKET_POINT_LOG . ' WHERE ticket_id=' . $ticket_id . ' AND userid=' . $customer_id;
            $row_points = $db->query($sql_customer_log_point)->fetchAll();
            $points_customer = [
                'positive' => 0,
                'negative' => 0
            ];
            if (!empty($row_points)) {
                foreach ($row_points as $point) {
                    if ($point['status'] == 1) {
                        $points_customer['positive'] += $point['point'];
                    } else {
                        $points_customer['negative'] += $point['point'];
                    }
                }
            }

            // Thống kê số câu hỏi, câu trả lời và điểm nhận được
            $ticket_comments = [];

            $sql_comment = 'SELECT log_id, reply_userid, comment_type, point_final, status, payment_status FROM ' . TB_TICKET_LOG . ' WHERE ticket_id=' . $ticket_id;
            $row_comments = $db->query($sql_comment)->fetchAll();

            if (!empty($row_comments)) {
                foreach ($row_comments as $comment) {
                    $comment_type = $comment['comment_type'];
                    $reply_userid = $comment['reply_userid'];
                    $point_final = $comment['point_final'];
                    $payment_status = $comment['payment_status'];

                    // Đối với CommentType::Customer
                    if ($comment_type == CommentType::Customer->value) {
                        if (!isset($ticket_comments[CommentType::Customer->getLabel()])) {
                            $ticket_comments[CommentType::Customer->getLabel()] = [
                                'count' => 0,
                                'points' => [
                                    'positive' => $points_customer['positive'],
                                    'negative' => $points_customer['negative']
                                ]
                            ];
                        }
                        $ticket_comments[CommentType::Customer->getLabel()]['count']++;
                    }

                    // Đối với CommentType::AI
                    elseif ($comment_type == CommentType::AI->value) {
                        if (!isset($ticket_comments[CommentType::AI->getLabel()])) {
                            $ticket_comments[CommentType::AI->getLabel()] = [
                                'count' => 0,
                                'points' => 0
                            ];
                        }
                        $ticket_comments[CommentType::AI->getLabel()]['count']++;
                        if ($comment['status'] == CommentStatus::Done->value && $payment_status == PaymentStatus::Done->value) {
                            $ticket_comments[CommentType::AI->getLabel()]['points'] += $point_final;
                        }
                    }

                    // Đối với CommentType::Expert và CommentType::ExpertAdditional
                    elseif ($comment_type == CommentType::Expert->value || $comment_type == CommentType::ExpertAdditional->value) {
                        if (!isset($ticket_comments[CommentType::Expert->getLabel()][$reply_userid])) {
                            $ticket_comments[CommentType::Expert->getLabel()][$reply_userid] = [
                                CommentType::Expert->getLabel() => 0,
                                CommentType::ExpertAdditional->getLabel() => 0,
                                'points' => 0
                            ];
                        }
                        if ($comment_type == CommentType::Expert->value) {
                            $ticket_comments[CommentType::Expert->getLabel()][$reply_userid][CommentType::Expert->getLabel()]++;
                        } else {
                            $ticket_comments[CommentType::Expert->getLabel()][$reply_userid][CommentType::ExpertAdditional->getLabel()]++;
                        }
                        if ($payment_status == PaymentStatus::Done->value) {
                            $ticket_comments[CommentType::Expert->getLabel()][$reply_userid]['points'] += $point_final;
                        }
                    }
                }
            }
            return $this->result->setSuccess()
                ->setCode('0000')
                ->set('data', $ticket_comments)
                ->getResult();
        } else {
            return $this->result->setError()
                ->setCode('2000')
                ->setMessage('Ticket is not exist')
                ->getResult();
        }
    }
}
