<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('chart');

$array_search = array();
$array_search['type'] = $nv_Request->get_int('type', 'post,get', 0);
// Type 0: Xem theo ngày 1: Xem theo tháng, 2 xem theo năm

$array_search['type_chart'] = $nv_Request->get_int('type_chart', 'post,get', 1);
// Type_chart: 1: Biểu đồ tăng trưởng, 2 biểu đồ số lượng

$curent_from = '01/' . nv_date('m/Y', NV_CURRENTTIME);
$curent = nv_date('d/m/Y', NV_CURRENTTIME);
$array_search['time_from'] = $nv_Request->get_title('time_from', 'post,get', $curent_from);
$array_search['time_to'] = $nv_Request->get_title('time_to', 'post,get', $curent);

$is_compare_mode = $nv_Request->isset_request('compare_mode', 'get');
$array_search['time2_from'] = $nv_Request->get_title('time2_from', 'post,get', $array_search['time_from']);
$array_search['time2_to'] = $nv_Request->get_title('time2_to', 'post,get', $array_search['time_to']);

if ($array_search['type'] == 1) {
    // Xem theo tháng
    if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_from'], $m)) {
        $sfrom = mktime(0, 0, 0, $m[2], 01, $m[3]);
    } else {
        $sfrom = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
    }

    if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_to'], $m)) {
        $sto = mktime(0, 0, 0, $m[2], get_last_day($m[2], $m[3]), $m[3]);
    } else {
        $sto = mktime(23, 59, 59, nv_date('m', NV_CURRENTTIME), get_last_day(nv_date('m', NV_CURRENTTIME), nv_date('Y', NV_CURRENTTIME)), nv_date('Y', NV_CURRENTTIME));
    }
} else if ($array_search['type'] == 2) {
    // Xem theo năm
    if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_from'], $m)) {
        $sfrom = mktime(0, 0, 0, 01, 01, $m[3]);
    } else {
        $sfrom = mktime(0, 0, 0, 01, 01, nv_date('Y', NV_CURRENTTIME));
    }

    if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_to'], $m)) {
        $sto = mktime(0, 0, 0, 12, 31, $m[3]);
    } else {
        $sto = mktime(23, 59, 59, 12, 31, nv_date('Y', NV_CURRENTTIME));
    }
} else {
    // Xem theo ngày
    if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_from'], $m)) {
        $sfrom = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
    } else {
        $sfrom = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
    }
    if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_to'], $m)) {
        $sto = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
    } else {
        $sto = mktime(23, 59, 59, nv_date('m', NV_CURRENTTIME), nv_date('d', NV_CURRENTTIME), nv_date('Y', NV_CURRENTTIME));
    }
}

$array_users = $array_vips = $array_leads = $array_oppotunities = array();
$vip = $user = $leads = $oppotunities = 0;
if ($array_search['type_chart'] == 1) {
    // Biểu đồ tăng trưởng
    if ($array_search['type'] == 1) {
        // Tăng trưởng theo tháng
        $month_from = nv_date('n', $sfrom);
        $month_to = nv_date('n', $sto);
        $_sfrom = mktime(0, 0, 0, $month_from, 01, nv_date('Y', $sfrom));
        $_sto = mktime(0, 0, 0, $month_to, get_last_day($month_to, nv_date('Y', $sto)), nv_date('Y', $sto));
        $sql = 'SELECT date, total_user, total_vip, total_vieweb, total_leads, total_opportunities, total_sales FROM ' . NV_PREFIXLANG . '_crmbidding_daily_statistic WHERE date >= ' . $_sfrom . ' AND date <=' . $_sto . ' GROUP BY date ORDER BY date ASC';
        $result = $db->query($sql);
        while ($row = $result->fetch()) {
            $array_leads[$row['date']] = $row['total_leads'];
            $array_oppotunities[$row['date']] = $row['total_opportunities'];
            $array_vips[$row['date']] = $row['total_vip'];
            $array_vieweb[$row['date']] = $row['total_vieweb'];
            $array_users[$row['date']] = $row['total_user'];
            $array_sales[$row['date']] = $row['total_sales'];
        }
    } else if ($array_search['type'] == 2) {
        // Tăng trưởng theo năm
        $year_from = nv_date('Y', $sfrom);
        $year_to = nv_date('Y', $sto);
        $_sfrom = mktime(0, 0, 0, 1, 01, $year_from);
        $_sto = mktime(0, 0, 0, 12, 31, $year_to);
        $sql = 'SELECT date, total_user, total_vip, total_vieweb, total_leads, total_opportunities, total_sales FROM ' . NV_PREFIXLANG . '_crmbidding_daily_statistic WHERE date >= ' . $_sfrom . ' AND date <=' . $_sto . ' GROUP BY date ORDER BY date ASC';
        $result = $db->query($sql);
        $lead = $oppotunities = $vip = $users = 0;
        while ($row = $result->fetch()) {
            $array_leads[$row['date']] = $row['total_leads'];
            $array_oppotunities[$row['date']] = $row['total_opportunities'];
            $array_vips[$row['date']] = $row['total_vip'];
            $array_vieweb[$row['date']] = $row['total_vieweb'];
            $array_users[$row['date']] = $row['total_user'];
            $array_sales[$row['date']] = $row['total_sales'];
        }
    } else {
        $sql = 'SELECT date, total_user, total_vip, total_vieweb, total_leads, total_opportunities, total_sales FROM ' . NV_PREFIXLANG . '_crmbidding_daily_statistic WHERE date >= ' . $sfrom . ' AND date <=' . $sto . ' GROUP BY date ORDER BY date ASC';
        $result = $db->query($sql);
        while ($row = $result->fetch()) {
            $array_leads[$row['date']] = $row['total_leads'];
            $array_oppotunities[$row['date']] = $row['total_opportunities'];
            $array_vips[$row['date']] = $row['total_vip'];
            $array_vieweb[$row['date']] = $row['total_vieweb'];
            $array_users[$row['date']] = $row['total_user'];
            $array_sales[$row['date']] = $row['total_sales'];
        }
    }
} else {
    // Biểu đồ số lượng
    $sql = 'SELECT date, SUM(num_leads) as num_leads, SUM(num_opportunities) as num_opportunities, SUM(num_vips) as num_vips, SUM(num_vieweb) as num_vieweb, SUM(num_users) as num_users, SUM(num_sales) as num_sales FROM ' . NV_PREFIXLANG . '_crmbidding_daily_statistic WHERE date >= ' . $sfrom . ' AND date <=' . $sto . '  GROUP BY date ORDER BY date ASC';
    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        if ($array_search['type'] == 1) {
            // số lượng theo tháng
            $date_val = mktime(0, 0, 0, nv_date('m', $row['date']), 01, nv_date('Y', $row['date']));
            if (isset($array_leads[$date_val])) {
                $array_leads[$date_val] += $row['num_leads'];
                $array_oppotunities[$date_val] += $row['num_opportunities'];
                $array_users[$date_val] += $row['num_users'];
                $array_vips[$date_val] += $row['num_vips'];
                $array_vieweb[$date_val] += $row['num_vieweb'];
                $array_sales[$date_val] += $row['num_sales'];
                // pr($array_sales);
            } else {
                $array_leads[$date_val] = $row['num_leads'];
                $array_oppotunities[$date_val] = $row['num_opportunities'];
                $array_users[$date_val] = $row['num_users'];
                $array_vips[$date_val] = $row['num_vips'];
                $array_vieweb[$date_val] = $row['num_vieweb'];
                $array_sales[$date_val] = $row['num_sales'];
            }
        } else if ($array_search['type'] == 2) {
            $date_val = mktime(0, 0, 0, 01, 01, nv_date('Y', $row['date']));
            if (isset($array_leads[$date_val])) {
                $array_leads[$date_val] += $row['num_leads'];
                $array_oppotunities[$date_val] += $row['num_opportunities'];
                $array_users[$date_val] += $row['num_users'];
                $array_vips[$date_val] += $row['num_vips'];
                $array_vieweb[$date_val] += $row['num_vieweb'];
                $array_sales[$date_val] += $row['num_sales'];
            } else {
                $array_leads[$date_val] = $row['num_leads'];
                $array_oppotunities[$date_val] = $row['num_opportunities'];
                $array_users[$date_val] = $row['num_users'];
                $array_vips[$date_val] = $row['num_vips'];
                $array_vieweb[$date_val] = $row['num_vieweb'];
                $array_sales[$date_val] = $row['num_sales'];
            }
        } else {
            $array_leads[$row['date']] = $row['num_leads'];
            $array_oppotunities[$row['date']] = $row['num_opportunities'];
            $array_users[$row['date']] = $row['num_users'];
            $array_vips[$row['date']] = $row['num_vips'];
            $array_vieweb[$row['date']] = $row['num_vieweb'];
            $array_sales[$row['date']] = $row['num_sales'];
        }
    }
}

if ($is_compare_mode) {
    if ($array_search['type'] == 1) {
        // Xem theo tháng
        if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time2_from'], $m)) {
            $s2from = mktime(0, 0, 0, $m[2], 01, $m[3]);
        } else {
            $s2from = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
        }

        if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time2_to'], $m)) {
            $s2to = mktime(0, 0, 0, $m[2], get_last_day($m[2], $m[3]), $m[3]);
        } else {
            $s2to = mktime(23, 59, 59, nv_date('m', NV_CURRENTTIME), get_last_day(nv_date('m', NV_CURRENTTIME), nv_date('Y', NV_CURRENTTIME)), nv_date('Y', NV_CURRENTTIME));
        }
    } else if ($array_search['type'] == 2) {
        // Xem theo năm
        if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time2_from'], $m)) {
            $s2from = mktime(0, 0, 0, 01, 01, $m[3]);
        } else {
            $s2from = mktime(0, 0, 0, 01, 01, nv_date('Y', NV_CURRENTTIME));
        }

        if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time2_to'], $m)) {
            $s2to = mktime(0, 0, 0, 12, 31, $m[3]);
        } else {
            $s2to = mktime(23, 59, 59, 12, 31, nv_date('Y', NV_CURRENTTIME));
        }
    } else {
        // Xem theo ngày
        if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time2_from'], $m)) {
            $s2from = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
        } else {
            $s2from = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
        }
        if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time2_to'], $m)) {
            $s2to = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
        } else {
            $s2to = mktime(23, 59, 59, nv_date('m', NV_CURRENTTIME), nv_date('d', NV_CURRENTTIME), nv_date('Y', NV_CURRENTTIME));
        }
    }

    $array_users_2 = $array_vips_2 = $array_leads_2 = $array_oppotunities_2 = array();
    // $vip = $user = $leads = $oppotunities = 0;
    if ($array_search['type_chart'] == 1) {
        // Biểu đồ tăng trưởng
        if ($array_search['type'] == 1) {
            // Tăng trưởng theo tháng
            $month_from = nv_date('n', $s2from);
            $month_to = nv_date('n', $s2to);
            $_sfrom = mktime(0, 0, 0, $month_from, 01, nv_date('Y', $s2from));
            $_sto = mktime(0, 0, 0, $month_to, get_last_day($month_to, nv_date('Y', $s2to)), nv_date('Y', $s2to));
            $sql = 'SELECT date, total_user, total_vip, total_vieweb, total_leads, total_opportunities, total_sales FROM ' . NV_PREFIXLANG . '_crmbidding_daily_statistic WHERE date >= ' . $_sfrom . ' AND date <=' . $_sto . ' GROUP BY date ORDER BY date ASC';
            $result = $db->query($sql);
            while ($row = $result->fetch()) {
                $array_leads_2[$row['date']] = $row['total_leads'];
                $array_oppotunities_2[$row['date']] = $row['total_opportunities'];
                $array_vips_2[$row['date']] = $row['total_vip'];
                $array_vieweb_2[$row['date']] = $row['total_vieweb'];
                $array_users_2[$row['date']] = $row['total_user'];
                $array_sales_2[$row['date']] = $row['total_sales'];
            }
        } else if ($array_search['type'] == 2) {
            // Tăng trưởng theo năm
            $year_from = nv_date('Y', $s2from);
            $year_to = nv_date('Y', $s2to);
            $_sfrom = mktime(0, 0, 0, 1, 01, $year_from);
            $_sto = mktime(0, 0, 0, 12, 31, $year_to);
            $sql = 'SELECT date, total_user, total_vip, total_vieweb, total_leads, total_opportunities, total_sales FROM ' . NV_PREFIXLANG . '_crmbidding_daily_statistic WHERE date >= ' . $_sfrom . ' AND date <=' . $_sto . ' GROUP BY date ORDER BY date ASC';
            $result = $db->query($sql);
            // $lead = $oppotunities = $vip = $users = 0;
            while ($row = $result->fetch()) {
                $array_leads_2[$row['date']] = $row['total_leads'];
                $array_oppotunities_2[$row['date']] = $row['total_opportunities'];
                $array_vips_2[$row['date']] = $row['total_vip'];
                $array_vieweb_2[$row['date']] = $row['total_vieweb'];
                $array_users_2[$row['date']] = $row['total_user'];
                $array_sales_2[$row['date']] = $row['total_sales'];
            }
        } else {
            $sql = 'SELECT date, total_user, total_vip, total_vieweb, total_leads, total_opportunities, total_sales FROM ' . NV_PREFIXLANG . '_crmbidding_daily_statistic WHERE date >= ' . $s2from . ' AND date <=' . $s2to . ' GROUP BY date ORDER BY date ASC';
            $result = $db->query($sql);
            while ($row = $result->fetch()) {
                $array_leads_2[$row['date']] = $row['total_leads'];
                $array_oppotunities_2[$row['date']] = $row['total_opportunities'];
                $array_vips_2[$row['date']] = $row['total_vip'];
                $array_vieweb_2[$row['date']] = $row['total_vieweb'];
                $array_users_2[$row['date']] = $row['total_user'];
                $array_sales_2[$row['date']] = $row['total_sales'];
            }
        }
    } else {
        // Biểu đồ số lượng
        $sql = 'SELECT date, SUM(num_leads) as num_leads, SUM(num_opportunities) as num_opportunities, SUM(num_vips) as num_vips, SUM(num_vieweb) as num_vieweb, SUM(num_users) as num_users, SUM(num_sales) as num_sales FROM ' . NV_PREFIXLANG . '_crmbidding_daily_statistic WHERE date >= ' . $s2from . ' AND date <=' . $s2to . '  GROUP BY date ORDER BY date ASC';
        $result = $db->query($sql);
        while ($row = $result->fetch()) {
            if ($array_search['type'] == 1) {
                // số lượng theo tháng
                $date_val = mktime(0, 0, 0, nv_date('m', $row['date']), 01, nv_date('Y', $row['date']));
                if (isset($array_leads[$date_val])) {
                    $array_leads_2[$date_val] += $row['num_leads'];
                    $array_oppotunities_2[$date_val] += $row['num_opportunities'];
                    $array_users_2[$date_val] += $row['num_users'];
                    $array_vips_2[$date_val] += $row['num_vips'];
                    $array_vieweb_2[$date_val] += $row['num_vieweb'];
                    $array_sales_2[$date_val] += $row['num_sales'];
                } else {
                    $array_leads_2[$date_val] = $row['num_leads'];
                    $array_oppotunities_2[$date_val] = $row['num_opportunities'];
                    $array_users_2[$date_val] = $row['num_users'];
                    $array_vips_2[$date_val] = $row['num_vips'];
                    $array_vieweb_2[$date_val] = $row['num_vieweb'];
                    $array_sales_2[$date_val] = $row['num_sales'];
                }
            } else if ($array_search['type'] == 2) {
                $date_val = mktime(0, 0, 0, 01, 01, nv_date('Y', $row['date']));
                if (isset($array_leads[$date_val])) {
                    $array_leads_2[$date_val] += $row['num_leads'];
                    $array_oppotunities_2[$date_val] += $row['num_opportunities'];
                    $array_users_2[$date_val] += $row['num_users'];
                    $array_vips_2[$date_val] += $row['num_vips'];
                    $array_vieweb_2[$date_val] += $row['num_vieweb'];
                    $array_sales_2[$date_val] += $row['num_sales'];
                } else {
                    $array_leads_2[$date_val] = $row['num_leads'];
                    $array_oppotunities_2[$date_val] = $row['num_opportunities'];
                    $array_users_2[$date_val] = $row['num_users'];
                    $array_vips_2[$date_val] = $row['num_vips'];
                    $array_vieweb_2[$date_val] = $row['num_vieweb'];
                    $array_sales_2[$date_val] = $row['num_sales'];
                }
            } else {
                $array_leads_2[$row['date']] = $row['num_leads'];
                $array_oppotunities_2[$row['date']] = $row['num_opportunities'];
                $array_users_2[$row['date']] = $row['num_users'];
                $array_vips_2[$row['date']] = $row['num_vips'];
                $array_vieweb_2[$row['date']] = $row['num_vieweb'];
                $array_sales_2[$row['date']] = $row['num_sales'];
            }
        }
    }
}
$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);

for ($i = 0; $i < 3; $i++) {
    $xtpl->assign('OPTION', array(
        'key' => $i,
        'title' => $nv_Lang->getModule('type' . $i),
        'selected' => $array_search['type'] == $i ? ' selected="selected"' : ''
    ));
    $xtpl->parse('main.loop_type');
}

$xtpl->assign('TYPE_CHART' . $array_search['type_chart'], ' selected="selected"');

$array_users_tmp = $array_vips_tmp = $array_leads_tmp = $array_oppotunities_tmp =  $array_vieweb_tmp = $array_sales_tmp = [];
$array_users_tmp_2 = $array_vips_tmp_2 = $array_leads_tmp_2 = $array_oppotunities_tmp_2 =  $array_vieweb_tmp_2 = $array_sales_tmp_2 = [];

foreach ($array_leads as $key => $value) {
    if ($array_search['type'] == 1) {
        $date_val = nv_date('m/Y', $key);
    } else if ($array_search['type'] == 2) {
        $date_val = nv_date('Y', $key);
    } else {
        $date_val = nv_date('d/m/Y', $key);
    }
    $array_leads_tmp[$date_val] = $array_leads[$key];
    $array_oppotunities_tmp[$date_val] = $array_oppotunities[$key];
    $array_users_tmp[$date_val] = $array_users[$key];
    $array_vips_tmp[$date_val] = $array_vips[$key];
    $array_vieweb_tmp[$date_val] = $array_vieweb[$key];
    $array_sales_tmp[$date_val] = $array_sales[$key];
}

if ($is_compare_mode) {
    foreach ($array_leads_2 as $key => $value) {
        if ($array_search['type'] == 1) {
            $date_val = nv_date('m/Y', $key);
        } else if ($array_search['type'] == 2) {
            $date_val = nv_date('Y', $key);
        } else {
            $date_val = nv_date('d/m/Y', $key);
        }
        $array_leads_tmp_2[$date_val] = $array_leads_2[$key];
        $array_oppotunities_tmp_2[$date_val] = $array_oppotunities_2[$key];
        $array_users_tmp_2[$date_val] = $array_users_2[$key];
        $array_vips_tmp_2[$date_val] = $array_vips_2[$key];
        $array_vieweb_tmp_2[$date_val] = $array_vieweb_2[$key];
        $array_sales_tmp_2[$date_val] = $array_sales_2[$key];
    }

    // pr($array_oppotunities_tmp_2);
}

if ($is_compare_mode) {
    if ($array_search['type'] == 1) {
        $prefix = 'Tháng ';
    } else if ($array_search['type'] == 2) {
        $prefix = 'Năm ';
    } else {
        $prefix = 'Ngày ';
    }

    $count = 1;
    foreach ($array_leads_tmp as $key => $value) {
        $array_leads_tmp[$prefix . $count] = $value;
        unset($array_leads_tmp[$key]);
        $count++;
    }

    $count = 1;
    foreach ($array_oppotunities_tmp as $key => $value) {
        $array_oppotunities_tmp[$prefix . $count] = $value;
        unset($array_oppotunities_tmp[$key]);
        $count++;
    }

    $count = 1;
    foreach ($array_users_tmp as $key => $value) {
        $array_users_tmp[$prefix . $count] = $value;
        unset($array_users_tmp[$key]);
        $count++;
    }

    $count = 1;
    foreach ($array_vips_tmp as $key => $value) {
        $array_vips_tmp[$prefix . $count] = $value;
        unset($array_vips_tmp[$key]);
        $count++;
    }

    $count = 1;
    foreach ($array_vieweb_tmp as $key => $value) {
        $array_vieweb_tmp[$prefix . $count] = $value;
        unset($array_vieweb_tmp[$key]);
        $count++;
    }

    $count = 1;
    foreach ($array_sales_tmp as $key => $value) {
        $array_sales_tmp[$prefix . $count] = $value;
        unset($array_sales_tmp[$key]);
        $count++;
    }

    // range 2
    $count = 1;
    foreach ($array_leads_tmp_2 as $key => $value) {
        $array_leads_tmp_2[$prefix . $count] = $value;
        unset($array_leads_tmp_2[$key]);
        $count++;
    }

    $count = 1;
    foreach ($array_oppotunities_tmp_2 as $key => $value) {
        $array_oppotunities_tmp_2[$prefix . $count] = $value;
        unset($array_oppotunities_tmp_2[$key]);
        $count++;
    }

    $count = 1;
    foreach ($array_users_tmp_2 as $key => $value) {
        $array_users_tmp_2[$prefix . $count] = $value;
        unset($array_users_tmp_2[$key]);
        $count++;
    }

    $count = 1;
    foreach ($array_vips_tmp_2 as $key => $value) {
        $array_vips_tmp_2[$prefix . $count] = $value;
        unset($array_vips_tmp_2[$key]);
        $count++;
    }

    $count = 1;
    foreach ($array_vieweb_tmp_2 as $key => $value) {
        $array_vieweb_tmp_2[$prefix . $count] = $value;
        unset($array_vieweb_tmp_2[$key]);
        $count++;
    }

    $count = 1;
    foreach ($array_sales_tmp_2 as $key => $value) {
        $array_sales_tmp_2[$prefix . $count] = $value;
        unset($array_sales_tmp_2[$key]);
        $count++;
    }
}

// users
$xtpl->assign('LABEL', "'" . implode("','", array_keys($array_users_tmp)) . "'");
$xtpl->assign('DATA', implode(",", $array_users_tmp));
$xtpl->assign('DATA_VIPS', implode(",", $array_vips_tmp));
$xtpl->assign('DATA_VIEWEB', implode(",", $array_vieweb_tmp));
$xtpl->assign('DATA_LEADS', implode(",", $array_leads_tmp));
$xtpl->assign('DATA_OPPOTUNITIESs', implode(",", $array_oppotunities_tmp));
$xtpl->assign('DATA_SALES', implode(",", $array_sales_tmp));

if ($is_compare_mode) {
    // Lấy array labels theo khoảng thời gian nào dài hơn
    $xtpl->assign('LABEL', "'" . implode("','", array_keys(count($array_users_tmp) > count($array_users_tmp_2) ? $array_users_tmp : $array_users_tmp_2)) . "'");
    $xtpl->assign('DATA_2', implode(",", $array_users_tmp_2));
    $xtpl->assign('DATA_VIPS_2', implode(",", $array_vips_tmp_2));
    $xtpl->assign('DATA_VIEWEB_2', implode(",", $array_vieweb_tmp_2));
    $xtpl->assign('DATA_LEADS_2', implode(",", $array_leads_tmp_2));
    $xtpl->assign('DATA_OPPOTUNITIESs_2', implode(",", $array_oppotunities_tmp_2));
    $xtpl->assign('DATA_SALES_2', implode(",", $array_sales_tmp_2));
}

$xtpl->assign('ARRAY_SEARCH', $array_search);

if ($is_compare_mode) {
    $xtpl->parse('main.compare_checked');
    $xtpl->parse('main.compared');
    $xtpl->parse('main.compare_mode');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
