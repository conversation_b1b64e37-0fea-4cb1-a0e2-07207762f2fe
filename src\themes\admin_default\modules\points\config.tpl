<!-- BEGIN: main -->
<!-- BEGIN: error -->
<div class="alert alert-warning">{ERROR}</div>
<!-- END: error -->
<form class="form-inline" action="" method="post">

    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <caption>
                <em class="fa fa-file-text-o">&nbsp;</em>{LANG.purchased_points_conf}
            </caption>
            <tbody>
                <tr>
                    <th>{LANG.expired_admin}</th>
                    <td style="width: 40%"><input class="form-control" type="text" value="{DATA.expired}" name="expired" /></td>
                </tr>
            </tbody>
        </table>
        <table class="table table-striped table-bordered table-hover">
            <caption>
                <em class="fa fa-file-text-o">&nbsp;</em>{LANG.given_points_conf}
            </caption>
            <tbody>
                <tr>
                    <th>{LANG.new_user}</th>
                    <td style="width: 40%"><input class="form-control" type="text" value="{DATA.new_user}" name="new_user" /></td>
                </tr>
                <tr>
                    <th>{LANG.new_phone}</th>
                    <td><input class="form-control" type="text" value="{DATA.new_phone}" name="new_phone" /></td>
                </tr>
                <tr>
                    <th>{LANG.new_tax}</th>
                    <td><input class="form-control" type="text" value="{DATA.new_tax}" name="new_tax" /></td>
                </tr>
                <tr>
                    <td colspan="2" class="always-wrap">{LANG.note_reward}</td>
                </tr>
                <tr>
                    <th>{LANG.new_login}</th>
                    <td><input class="form-control" type="text" value="{DATA.new_login}" name="new_login" /></td>
                </tr>
                <tr>
                    <th>{LANG.login_10m}</th>
                    <td><input class="form-control" type="text" value="{DATA.login_10m}" name="login_10m" /></td>
                </tr>
                <tr>
                    <th>{LANG.expired_time}</th>
                    <td><input class="form-control" type="text" value="{DATA.expired_time}" name="expired_time" /></td>
                </tr>
            </tbody>
        </table>
        <table class="table table-striped table-bordered table-hover">
            <caption>
                <em class="fa fa-file-text-o">&nbsp;</em>{LANG.mem_given_points_conf}
            </caption>
            <tbody>
                <tr>
                    <th>{LANG.use_give_points}</th>
                    <td style="width: 40%"><input type="checkbox" value="1" name="use_give_points" {USE_GIVE_POINTS}/></td>
                </tr>
                <tr>
                    <th>{LANG.limit_give_points}</th>
                    <td><input class="form-control" type="number" min="1" value="{DATA.limit_give_points}" name="limit_give_points" /></td>
                </tr>
                <tr>
                    <th>{LANG.ratio_give_points}</th>
                    <td><input class="form-control" min="1" max="100" type="number" value="{DATA.ratio_give_points}" name="ratio_give_points" /></td>
                </tr>
            </tbody>
        </table>
        <table class="table table-striped table-bordered table-hover">
            <caption>
                <em class="fa fa-file-text-o">&nbsp;</em>{LANG.bonus_points_conf}
            </caption>
            <tbody>
                <tr>
                    <th class="always-wrap">{LANG.new_user_aff}</th>
                    <td style="width: 40%"><input class="form-control" type="text" value="{DATA.new_user_aff}" name="new_user_aff" /></td>
                </tr>
                <tr>
                    <th class="always-wrap">{LANG.new_phone_aff}</th>
                    <td><input class="form-control" type="text" value="{DATA.new_phone_aff}" name="new_phone_aff" /></td>
                </tr>
                <tr>
                    <th class="always-wrap">{LANG.new_tax_aff}</th>
                    <td><input class="form-control" type="text" value="{DATA.new_tax_aff}" name="new_tax_aff" /></td>
                </tr>
                <tr>
                    <th class="always-wrap">{LANG.expired_aff}</th>
                    <td><input class="form-control" type="text" value="{DATA.expired_aff}" name="expired_aff" /></td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <caption>
                <em class="fa fa-file-text-o">&nbsp;</em>{LANG.title_crawl}
            </caption>
            <tbody>
                <tr>
                    <th>
                        <p>{LANG.point_crawl_plus}</p>
                    </th>
                    <td style="width: 40%"><input class="form-control" type="number" value="{DATA.point_crawl_plus}" name="point_crawl_plus" /></td>
                </tr>

                <tr>
                    <th>{LANG.point_crawl_mintus}</th>
                    <td style="width: 40%"><input class="form-control" type="number" value="{DATA.point_crawl_mintus}" name="point_crawl_mintus" /></td>
                </tr>

                <tr>
                    <th>{LANG.title_hethanpoint}</th>
                    <td style="width: 40%"><input class="form-control" type="number" value="{DATA.expired_crawl}" name="expired_crawl" /></td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="table-responsive">
        <table id="table" class="table table-responsive table-bordered table-striped">
            <caption>{LANG.config_exchange}</caption>
            <thead>
                <tr>
                    <td>
                        <strong>{LANG.exchange_money}</strong><span class="red">(*)</span>
                    </td>
                    <td>
                        <strong>{LANG.exchange_point_admin}</strong><span class="red">(*)</span>
                    </td>
                    <td>&nbsp;</td>
                </tr>
            </thead>
            <tfoot>
                <tr>
                    <td colspan="3">
                        <a href="javascript:void(0)" class="btn btn-success" onclick="nv_add_items()">{LANG.cfg_exchange_add}</a>
                    </td>
                </tr>
            </tfoot>
            <tbody id="id-area">
                <!-- BEGIN: loop -->
                <tr id="weight_{KEY}">
                    <td>
                        <input class="form-control" type="number" name="exchange_money_{KEY}" value="{CONFIG.exchange_money}" placeholder="100.000 vnđ" />
                    </td>
                    <td>
                        <input class="form-control" type="number" name="exchange_point_{KEY}" value="{CONFIG.exchange_point}" placeholder="100 điểm" />
                    </td>
                    <td class="text-center">
                        <em class="fa fa-trash-o fa-lg">&nbsp;</em><a href="javascript:void(0);" onclick="nv_remove_item({KEY});">{LANG.cfg_exchange_remove}</a>
                        <input type="hidden" name="ids[]" value="{KEY}" />
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
    <div class="text-center">
        <input class="btn btn-primary" type="submit" value="{LANG.save}" name="submit" />
        <input type="hidden" value="1" name="savesetting">
    </div>
</form>
<script type="text/javascript">
var num = {CONFIG_WEIGHT_COUNT}
function nv_add_items() {
    num += 1;
    var html = '';
    html += '<tr id="weight_' + num + '">';
    html += '<td><input class="form-control" type="text" name="exchange_money_' + num + '" value="{exchange_money}" placeholder="100.000 vnđ" /></td>';
    html += '<td><input class="form-control" type="text" name="exchange_point_' + num + '" value="{exchange_point}" placeholder="100 điểm" /></td>';
    html += '<td class="text-center"><em class="fa fa-trash-o fa-lg">&nbsp;</em><a href="javascript:void(0);" onclick="nv_remove_item(' + num + ');">{LANG.cfg_exchange_remove}</a><input type="hidden" name="ids[]" value="' + num + '" /></td>';
    html += '</tr>';
    $('#id-area').append(html);
}
function nv_remove_item(num) {
    $('#weight_' + num).remove();
}
</script>
<!-- BEGIN: main -->
