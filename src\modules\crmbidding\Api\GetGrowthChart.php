<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use PDOException;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetGrowthChart implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'GetGrowthChart';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        $type_chart = $nv_Request->get_int('type_chart', 'post', 1);
        $from_date = $nv_Request->get_int('from_date', 'post', '0');
        $to_date = $nv_Request->get_int('to_date', 'post', '0');
        if ($from_date == 0 || $to_date == 0) {
            return $this->result->setError()
            ->setCode('2000')
            ->setMessage($nv_Lang->getModule('miss_date'));
        }
        try {
            require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';
            $data_return = [];
            $sql = "SELECT id, title FROM nv4_vi_crmbidding_groups_leads WHERE active = 1 ORDER BY weight ASC";
            $array_groups_leads = $nv_Cache->db($sql, 'id', $module_data);
            $array_groups_leads[0]['id'] = 0;
            $array_groups_leads[0]['title'] = $nv_Lang->getModule('groups_lead0');

            $series_line = $channel = $total_month_column = $list_month = $data_series_month = [];
            foreach ($array_groups_leads as $key => $value) {
                $series_line[$key] = ["name" => $value['title']];
                $series_line[$key]["data"] = [];
                $channel[$key] = $value['title'];
                $total_month_column[$key] = 0;
            }
            ksort($total_month_column);
            ksort($series_line);
            ksort($channel);
            $range_date = [];
            $data_return['channel'] = $channel;

            // Biểu đồ dạng đường
            if ($type_chart == 1) {
                $sql = "SELECT source_leads, CONCAT(FROM_UNIXTIME(static_time, '%m'),'/',FROM_UNIXTIME(static_time, '%Y')) as time_stat, CONCAT(FROM_UNIXTIME(static_time, '%Y'),FROM_UNIXTIME(static_time, '%m')) as key_time_stat,SUM(total_end) as total FROM `nv4_vi_bidding_orders_general` WHERE status = 4 AND static_time > " . $from_date . " AND static_time < " . $to_date . " GROUP BY FROM_UNIXTIME(static_time, '%Y') DESC,FROM_UNIXTIME(static_time, '%m') DESC, source_leads";
                $result = $db->query($sql);
                while ($row = $result->fetch()) {
                    $list_month[$row['key_time_stat']] = $row['time_stat'];
                    $data_series_month[$row['source_leads']][$row['key_time_stat']] = $row['total'];
                    $series_line[$row['source_leads']]['data'] = $row['total'];
                    $range_date[] = $row['key_time_stat'];
                }
                ksort($list_month);
                $list_month = array_values(array_unique($list_month));
                foreach ($data_series_month as $key => $value) {
                    foreach ($range_date as $k => $v) {
                        if (!isset($value[$v])) {
                            $data_series_month[$key][$v] = 0;
                        }
                    }
                }
                foreach ($data_series_month as $k => $val) {
                    ksort($val);
                    $series_line[$k]['data'] = array_values($this->sum_element($val));
                }
                $data_return['series_line'] = $series_line;
                $data_return['list_month'] = $list_month;
            } else {
                // Biểu đồ dạng cột
                $sql = "SELECT source_leads, SUM(total_end) as tong FROM nv4_vi_bidding_orders_general WHERE status = 4 AND FROM_UNIXTIME(static_time, '%m') = " . $from_date . " AND FROM_UNIXTIME(static_time, '%Y') = " . $to_date . " GROUP BY source_leads";
                $result = $db->query($sql);
                while ($row = $result->fetch()) {
                    if (!isset($total_month_column[$row['source_leads']])) {
                        $total_month_column[$row['source_leads']] = $row['tong'];
                    } else {
                        $total_month_column[$row['source_leads']] = $total_month_column[$row['source_leads']] + $row['tong'];
                    }
                }
                $data_return['m'] = $single_month;
                $data_return['y'] = $single_year;
                $data_return['total_month_column'] = $total_month_column;
            }
            if (empty($data_return)) {
                $this->result->setSuccess()
                    ->setCode('4000')
                    ->setMessage($nv_Lang->getModule('api_error_400'));
            } else {
                $this->result->setSuccess();
                $this->result->set('data', $data_return);
            }

        } catch (PDOException $e) {
            $this->result->setError()
                ->setCode('3000')
                ->setMessage(print_r($e, true));
        }
        return $this->result->getResult();
    }

    public function sum_element($array)
    {
        $keys = array_keys($array);
        $array = array_values($array);
        $newArr = [];
        foreach ($array as $key => $val) {
            $newArr[] = array_sum(array_slice($array, 0, $key + 1));
        }
        $newArr = array_combine($keys, $newArr);
        return $newArr;
    }
}
