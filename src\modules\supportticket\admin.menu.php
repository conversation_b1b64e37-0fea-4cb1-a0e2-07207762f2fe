<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2023 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 27 Feb 2023 09:02:35 GMT
 */

if (!defined('NV_ADMIN')) {
    die('Stop!!!');
}
//Danh sách admin được quyền ưu tiên quản trị ticket
$special_admin_array = [
    99390, //Trung
    35704, //Giang
];

$allow_func = ['main', 'tickets', 'ticket_detail', 'ticket_detail_paid', 'ticket_add', 'user_extend', 'refunds', 'statistics', 'answers', 'invalid_answers'];

$submenu['tickets'] = $nv_Lang->getModule('tickets');
$submenu['refunds'] = $nv_Lang->getModule('refund_request');
$submenu['ticket_add'] = $nv_Lang->getModule('ticket_add');
$menu_config = [];
$menu_static = [];

if (defined('NV_IS_SPADMIN') || in_array($admin_info['userid'], $special_admin_array)) {
    $spamin_func = ['config', 'cat', 'label', 'cat_admins', 'config_ai'];
    $allow_func = array_merge($allow_func, $spamin_func);
    $submenu['cat'] = $nv_Lang->getModule('cat');
    $submenu['label'] = $nv_Lang->getModule('label');
    $menu_config['config'] = $nv_Lang->getModule('module_config');
    $menu_config['config_ai'] = $nv_Lang->getModule('config_ai');
}

$menu_static['statistics'] = $nv_Lang->getModule('general_statistics');
$menu_static['answers'] = $nv_Lang->getModule('answers');
$menu_static['invalid_answers'] = $nv_Lang->getModule('invalid_answers');
$submenu['statistics'] = [
    'title' => $nv_Lang->getModule('statistics'),
    'submenu' => $menu_static
];

$menu_config['user_extend'] = $nv_Lang->getModule('user_extend');
$submenu['user_extend'] = [
    'title' => $nv_Lang->getModule('config'),
    'submenu' => $menu_config
];

