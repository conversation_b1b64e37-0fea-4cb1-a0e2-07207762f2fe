<!-- BEGIN: main -->
	<!-- BEGIN: success -->
        <div class="alert alert-success">{SUCCESS}</div>
        <script type="text/javascript">
           $(document).ready(function($) {
                setTimeout(function() {
                    // location.href = location.href;
                }, 2000);
           });

        </script>
    <!-- END: success -->

    <!-- BEGIN: error -->
        <div class="alert alert-warning">{ERROR}</div>
    <!-- END: error --> 

	<div class="manage__orgainzation">
		<div class="detail__orgainzation_heading">
			<div class="box__title"> 
                <h2 class="title__page title__page_organization">{LANG.title_info_orag}</h2>
                <a href="{URL}" class="btn btn-success btn--social">{LANG.title_list_orag}</a>
            </div>
		</div>

		<div class="panel panel-default">
	        <div class="panel-body">
	            <form method="post" class="form__fadeOut">
	                <input type="hidden" name="convert_leads" value="{ROW.convert_leads}" />
	                <div class="row">
	                    <div class="col-md-12">
	                        <div class="form-group">
	                            <label><strong>{LANG.tax}</strong></label>
	                            <input class="form-control" type="text" name="tax" value="{ROW.tax}" />
	                        </div>

	                        <div class="form-group">
	                            <label><strong>{LANG.shortname}</strong></label>
	                            <input class="form-control" type="text" name="shortname" value="{ROW.shortname}"  />
	                        </div>

	                        <div class="form-group">
	                            <label class="control-label"><strong>{LANG.employees}</strong></label>
	                            <input class="form-control" type="text" name="employees" value="{ROW.employees}" pattern="^[0-9]*$"  oninvalid="setCustomValidity(nv_digits)" oninput="setCustomValidity('')" />
	                        </div>

	                        <div class="form-group">
	                            <label class="control-label"><strong>{LANG.website}</strong></label>
	                            <input class="form-control" type="text" name="website" value="{ROW.website}" />
	                        </div>
	                        

	                        <div class="form-group">
	                            <label class="control-label"><strong>{LANG.secondaryphone}</strong></label>
	                            <input class="form-control" type="text" name="secondaryphone" value="{ROW.secondaryphone}" />
	                        </div>

	                        <div class="form-group">
	                            <label class="control-label"><strong>{LANG.secondaryemail}</strong></label>
	                            <input class="form-control" type="text" name="secondaryemail" value="{ROW.secondaryemail}" />
	                        </div>   

	                        <div class="form-group">
	                            <label class="control-label"><strong>{LANG.represent_address}</strong></label>
	                                <input class="form-control" type="text" name="represent_address" value="{ROW.represent_address}" />
	                        </div>
	                        <div class="form-group">
	                            <label class="control-label"><strong>{LANG.description}</strong></label>
	                            <input class="form-control" type="text" name="description" value="{ROW.description}" />
	                        </div>
	                    </div>

	                    <div class="col-md-12">
	                        <div class="form-group">
	                            <label><strong>{LANG.organizationname} <span class="red">(*)</span></strong></label>
	                            <input class="form-control" type="text" name="organizationname" value="{ROW.organizationname}"  required />
	                        </div>

	                        <div class="form-group">
	                            <label class="control-label"><strong>{LANG.primaryphone} </span><span class="red">(*)</span></strong></label>
	                                <input class="form-control" type="text" name="primaryphone" value="{ROW.primaryphone}" required />
	                        </div>

	                        <div class="form-group">
	                            <label class="control-label"><strong>{LANG.primaryemail} <span class="red">(*)</span></strong></label>
	                            <input class="form-control" type="email" name="primaryemail" value="{ROW.primaryemail}" required/>
	                        </div>

	                        <div class="form-group">
	                            <label class="control-label"><strong>{LANG.address}</strong></label>
	                                <input class="form-control" type="text" name="address" value="{ROW.address}" />
	                        </div>
	                        <div class="form-group">
	                            <label class="control-label"><strong>{LANG.trading_address}</strong></label>
	                            <input class="form-control" type="text" name="trading_address" value="{ROW.trading_address}" />
	                        </div>
	                        <div class="form-group">
	                            <label class="control-label"><strong>{LANG.represent_name}</strong></label>
	                            <input class="form-control" type="text" name="represent_name" value="{ROW.represent_name}" />
	                        </div>
	                        <div class="form-group">
	                            <label class="control-label"><strong>{LANG.represent_position}</strong></label>
	                            <input class="form-control" type="text" name="represent_position" value="{ROW.represent_position}" />
	                        </div>

	                        <div class="form-group">
	                            <div class="<!-- BEGIN: show_class --> col-md-24 <!-- END: show_class --> <!-- BEGIN: hide_class --> col-md-18 <!-- END: hide_class -->">
	                                <label class="control-label"><strong>{LANG.fax}</strong></label>
	                                <input class="form-control" type="text" name="fax" value="{ROW.fax}" />
	                            </div>

	                            <!-- BEGIN: select -->
	                            <div class="col-md-6">
	                                <label class="control-label"><strong>{LANG.trangthai}</strong></label>
	                                <select class="form-control" name="active">
	                                    <!-- BEGIN: loop -->
	                                        <option value="{S_SELECT.key}" {S_SELECT.selected}> {S_SELECT.title}</option>
	                                    <!-- END: loop -->
	                                </select>
	                            </div>
	                            <!-- END: select -->
	                        </div> 
	                    </div>
	                </div>
	                <div class="form-group" style="text-align: center">
	                    <a href="{URL}" class="btn btn-default btn-sm">Hủy</a>
	                    <button class="btn btn-primary btn-sm" name="add" type="submit" value="{LANG.save}"><i class="fa fa-floppy-o" aria-hidden="true"></i> {LANG.save}</button>
	            </form>
	        </div>
	    </div>
	</div>
<!-- END: main -->