<?php

if (!defined('NV_IS_MOD_CRMBIDDING')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

if (!defined('NV_IS_USER')) {
    $redirect = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name, true);
    Header("Location: " . NV_BASE_SITEURL . "index.php?" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($redirect));
    die();
}

if (!isset($array_op[1]) || !is_numeric($array_op[1])) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}
// Kiểm tra hợp đồng
$status_check = implode(',', [EContractStatus::Done->value, EContractStatus::Cancel->value]);
$row = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $array_op[1] . ' AND customer_id=' . $user_info['userid'] . ' AND status NOT IN (' . $status_check . ')')->fetch();
if (empty($row)) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}

// Lấy thông tin chi tiết của hợp đồng
$row_econtract_order = $db->query('SELECT order_id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_orders WHERE econtract_id=' . $row['id'])->fetchAll();
if (empty($row_econtract_order)) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}

$page_title = $nv_Lang->getModule('page_update_contract');
$key_words = $module_info['keywords'];
$array_mod_title[] = array(
    'title' => $nv_Lang->getModule('page_update_contract'),
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op, true)
);

// Lấy thông tin đơn hàng
$order_user_ids = $data_vip = $where = $orders = [];
$data_total = [
    'total_service' => 0,
    'promotion' => 0,
    'total_payment' => 0,
];
$error = $success_message = '';
$order_ids = array_column($row_econtract_order, 'order_id');
$where['AND'] = [
    ['IN' => ['id' => '(' . implode(',', $order_ids) . ')']]
];
$api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
$api_dtinfo->setModule('bidding')
    ->setLang('vi')
    ->setAction('ListBiddingOrder')
    ->setData([
        'where' => $where,
        'show_customs_log' => 1
    ]);
$result = $api_dtinfo->execute();
if ($result['status'] == 'success' && !empty($result['data'])) {
    foreach ($result['data'] as $order_row) {
        foreach ($order_row['customs_log'] as $key => $detail_order) {
            $order_data = [];
            $order_data['vip'] = $detail_order['vip'];
            $order_data['is_combo'] = $detail_order['is_combo'];
            $order_data['type_export'] = $detail_order['type_export'];
            $order_data['vip_name'] = $detail_order['vip_name'];
            $order_data['vip_price'] = $detail_order['vip_price'];
            $order_data['order_id'] = $detail_order['order_id'];
            $order_data['lang'] =  $detail_order['prefix_lang'] == 0 ? $nv_Lang->getModule('lang_vi') : $nv_Lang->getModule('lang_en');
            $order_data['money'] = nv_number_format(($detail_order['vip_price'] * $detail_order['numbers_year'])) . ' VND';
            $order_user_ids[] = $order_data;
            // Lưu danh sách các gói vip trong đơn hàng
            $data_vip[] = [$detail_order['vip_name'], $detail_order['numbers_year']];
        }
        // Xử lý thông tin đơn hàng
        $code = sprintf('BDH%010s', $order_row['id']);
        $link = URL_DTINFO . 'orders/pay/?worderid=' . $order_row['id'];
        $label_order = $order_row['status'] == 5 ? 'label-warning' : 'label-info'; // 5: Đã hủy đơn hàng
        $url_order = '<a href="' . $link . '" target="_blank"><span class="label ' . $label_order . '"> ' . $code . ' <em class="fa fa-external-link"></em></span></a>';
        $orders[] = $url_order;
        // Tổng hóa đơn
        $data_total['total_service'] += $order_row['money'];
        $data_total['promotion'] += $order_row['discount'];
        $data_total['total_payment'] += $order_row['total'];
    }
}
$data_vip = convert_data_vip($data_vip);

// Thông tin cache file
$cache_file = 'econtract_' . $row['id'] . '_' . NV_LANG_DATA . '.cache';

// Xem trước thay đôi
if ($nv_Request->isset_request('previewEcontract', 'post')) {
    $row_new['contract_no'] = $row['contract_no'];
    $row_new['id'] = $nv_Request->get_int('econtract_id', 'post', 0);
    $row_new['c_name'] = $nv_Request->get_title('c_name', 'post', '');
    $row_new['tax_code'] = $nv_Request->get_title('tax_code', 'post', '');
    $row_new['representative'] = $nv_Request->get_title('representative', 'post', '');
    $row_new['jobtitle'] = $nv_Request->get_title('jobtitle', 'post', '');
    $row_new['phone'] = $nv_Request->get_title('phone', 'post', '');
    $row_new['c_address'] = $nv_Request->get_title('c_address', 'post', '');
    $row_new['receiver'] = $nv_Request->get_title('receiver', 'post', '');
    $row_new['receiver_phone'] = $nv_Request->get_title('receiver_phone', 'post', '');
    $row_new['receiver_address'] = $nv_Request->get_title('receiver_address', 'post', '');
    $row_new['customer_type'] = $nv_Request->get_int('customer_type', 'post', 1);
    $row_new['email'] = $nv_Request->get_title('email', 'post', '');
    $row_new['authorization_letter'] = $nv_Request->get_title('authorization_letter', 'post', '');
    $row_new['cccd'] = $nv_Request->get_title('cccd', 'post', 1);
    $row_new['bank_account'] = $nv_Request->get_title('bank_account', 'post', '');

    // Kiểm thông tin
    $error = validate_econtract_row($row_new);

    if (empty($error)) {
        if (!is_dir($filedir)) {
            mkdir($filedir);
        }
        if (empty($row['contract_data'])) {
            $row['contract_data'] = json_encode([
                'data_vip' => $data_vip,
                'content' => '',
                'total_service' => $data_total['total_service'],
                'promotion' => $data_total['promotion'],
                'total_payment' => $data_total['total_payment'],
            ], JSON_UNESCAPED_UNICODE);
        }
        $new_version_data = [
            'id' => $row['id'],
            'current_version' => $row['current_version'],
            'tax_code' => $row_new['tax_code'],
            'c_name' => $row_new['c_name'],
            'representative' => $row_new['representative'],
            'jobtitle' => $row_new['jobtitle'],
            'c_address' => $row_new['c_address'],
            'phone' => $row_new['phone'],
            'email' => $row_new['email'],
            'authorization_letter' => $row_new['authorization_letter'],
            'receiver' => $row_new['receiver'],
            'receiver_phone' => $row_new['receiver_phone'],
            'receiver_address' => $row_new['receiver_address'],
            'cccd' => $row_new['cccd'],
            'customer_type' => $row_new['customer_type'],
            'contract_no' => $row['contract_no'],
            'payment_proposal_no' => $row['payment_proposal_no'],
            'contract_path' => '',
            'status' => $row['status'],
            'term_changed' => $row['term_changed'],
            'term_changed_notes' => $row['term_changed_notes'],
            'stage' => $row['stage'],
            'stage_next' => $row['stage_next'],
            'uploader_id' => $row['uploader_id'],
            'customer_id' => $row['customer_id'],
            'created_at' => $row['created_at'],
            'updated_at' => NV_CURRENTTIME,
            'contract_data' => $row['contract_data'],
            'bank_account' => $row['bank_account'] ?? ''
        ];
        $nv_Cache->setItem($module_name, $cache_file, json_encode($new_version_data, JSON_UNESCAPED_UNICODE), 3600);
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['doc-econtract'] . '/' . $row['id'] . '/preview');
    }
}

// Cập nhật hợp đồng
if ($nv_Request->isset_request('updateEcontract', 'post') and !in_array($row['status'], [EContractStatus::Done])) {
    $changed_data = $setParts = [];
    $row_new['contract_no'] = $row['contract_no'];
    $row_new['id'] = $nv_Request->get_int('econtract_id', 'post', 0);
    $row_new['c_name'] = $nv_Request->get_title('c_name', 'post', '');
    $row_new['tax_code'] = $nv_Request->get_title('tax_code', 'post', '');
    $row_new['representative'] = $nv_Request->get_title('representative', 'post', '');
    $row_new['jobtitle'] = $nv_Request->get_title('jobtitle', 'post', '');
    $row_new['phone'] = $nv_Request->get_title('phone', 'post', '');
    $row_new['c_address'] = $nv_Request->get_title('c_address', 'post', '');
    $row_new['receiver'] = $nv_Request->get_title('receiver', 'post', '');
    $row_new['receiver_phone'] = $nv_Request->get_title('receiver_phone', 'post', '');
    $row_new['receiver_address'] = $nv_Request->get_title('receiver_address', 'post', '');
    $row_new['customer_type'] = $nv_Request->get_int('customer_type', 'post', 1);
    $row_new['email'] = $nv_Request->get_title('email', 'post', '');
    $row_new['authorization_letter'] = $nv_Request->get_title('authorization_letter', 'post', '');
    $row_new['cccd'] = $nv_Request->get_title('cccd', 'post', 1);
    $row_new['bank_account'] = $nv_Request->get_title('bank_account', 'post', '');

    // Kiểm thông tin
    $error = validate_econtract_row($row_new);

    /**
     * Cập nhật lại các giá trị
     * status, state và stage_next
     * customer_signed và hstdt_signed
     */
    if (empty($error)) {
        $row_new['status'] = EContractStatus::Incomplete->value;
        $row_new['stage'] = EContractStage::Negotiating->value;
        $row_new['stage_next'] = EContractStage::HSTDTSignatureRequired->value;
        $row_new['customer_signed'] = 0; // Khách chưa ký
        $row_new['hstdt_signed'] = 0; // HSTDT chưa ký
    }

    $row = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts WHERE id=' . $row_new['id'] . ' AND customer_id=' . $user_info['userid'])->fetch();
    if (empty($row)) {
        $error = $nv_lang->getModule('error_request');
    }

    if (empty($error)) {
        $query = 'UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_econtracts SET ';

        $fieldsToUpdate = ['c_name', 'tax_code', 'representative', 'jobtitle', 'phone', 'email', 'c_address', 'receiver', 'receiver_phone', 'receiver_address', 'customer_type', 'authorization_letter', 'receiver', 'receiver_phone', 'receiver_address', 'cccd', 'bank_account'];
        $filedsSetDefault = ['status', 'stage', 'stage_next', 'customer_signed', 'hstdt_signed'];
        $fieldsToUpdate = array_merge($fieldsToUpdate, $filedsSetDefault);

        foreach ($fieldsToUpdate as $field) {
            if ($field == 'customer_type' || in_array($field, $filedsSetDefault)) {
                $newValue = $row_new[$field];
            } else {
                $newValue = $nv_Request->get_title($field, 'post', '');
            }
            if ($newValue != $row[$field]) {
                $setParts[] = $field . '=' . ($field == 'customer_type' ? $newValue : $db->quote($newValue));
                $changed_data[$field] = [
                    'old' => $row[$field],
                    'new' => $newValue
                ];
            }
        }

        if (!empty($setParts)) {
            $query .= implode(',', $setParts) . ' WHERE id=' . $row_new['id'];

            $exc = $db->prepare($query)->execute();
            if ($exc) {
                // Xử lý thay đổi version
                $max_code_ver = $db->query('SELECT MAX(version) FROM ' . NV_PREFIXLANG . '_' . $module_data . '_econtract_versions WHERE econtract_id=' . $row['id'])->fetchColumn() ?: 0;
                $new_version = $max_code_ver + 1;
                // Kiểm tra contract_data
                if (empty($row['contract_data'])) {
                    $row['contract_data'] = json_encode([
                        'data_vip' => $data_vip,
                        'content' => '',
                        'total_service' => $data_total['total_service'],
                        'promotion' => $data_total['promotion'],
                        'total_payment' => $data_total['total_payment'],
                    ], JSON_UNESCAPED_UNICODE);
                }
                $new_version_data = [
                    'id' => $row['id'],
                    'current_version' => $row['current_version'],
                    'tax_code' => $row_new['tax_code'],
                    'c_name' => $row_new['c_name'],
                    'representative' => $row_new['representative'],
                    'jobtitle' => $row_new['jobtitle'],
                    'c_address' => $row_new['c_address'],
                    'phone' => $row_new['phone'],
                    'email' => $row_new['email'],
                    'authorization_letter' => $row_new['authorization_letter'],
                    'receiver' => $row_new['receiver'],
                    'receiver_phone' => $row_new['receiver_phone'],
                    'receiver_address' => $row_new['receiver_address'],
                    'cccd' => $row_new['cccd'],
                    'customer_type' => $row_new['customer_type'],
                    'contract_no' => $row['contract_no'],
                    'contract_path' => '',
                    'status' => $row['status'],
                    'term_changed' => $row['term_changed'],
                    'term_changed_notes' => $row['term_changed_notes'],
                    'stage' => $row['stage'],
                    'stage_next' => $row['stage_next'],
                    'uploader_id' => $row['uploader_id'],
                    'customer_id' => $row['customer_id'],
                    'created_at' => $row['created_at'],
                    'updated_at' => NV_CURRENTTIME,
                    'contract_data' => $row['contract_data'],
                    'bank_account' => $row['bank_account'] ?? ''
                ];
                // Cập nhật version
                create_version_econtract([
                    'econtract_id' => $row_new['id'],
                    'user_id' => $user_info['userid'],
                    'version' => $new_version,
                    'pdf_path' => $row['contract_path'],
                    'contract_data' => json_encode($new_version_data),
                ]);
                // Lưu log thay đổi
                create_log_econtract([
                    'econtract_id' => $row_new['id'],
                    'version_id' => $new_version,
                    'action' => 1,
                    'user_id' => $user_info['userid'],
                    'action_desc_vi' => '<strong>Khách hàng</strong> cập nhật thông tin hợp đồng',
                    'action_desc_en' => '<strong>Customers</strong> update contract information',
                    'changed_data' => json_encode($changed_data, JSON_UNESCAPED_UNICODE),
                    'log_visible' => 1,
                    'created_at' => NV_CURRENTTIME
                ]);
                $success_message = htmlspecialchars($nv_Lang->getModule('title_success'), ENT_QUOTES, 'UTF-8');
            }
        }
    }
    // Sau khi lưu thông tin thì sẽ xóa file cache-preview
    $nv_Cache->delItem($module_name, $cache_file);
    $row = $row_new;
}

// Nếu đã lưu dữ liệu xem trước rồi thì sẽ đọc dữ liệu từ cache
if (($cache_data = $nv_Cache->getItem($module_name, $cache_file)) != false) {
    $data_array = json_decode($cache_data, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        $row = $data_array;
    }
}

$contents = nv_theme_update_econtract($row, $order_user_ids, $data_total, $error, $orders, $success_message);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
