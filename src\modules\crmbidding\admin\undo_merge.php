<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:39:33 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$error = $list_all = $list_rollback = [];
$type = $nv_Request->get_int('type', 'post, get', 1);
$q_search = $nv_Request->get_title('q_search', 'post, get', '');
$id_rollback = $nv_Request->get_int('id_rollback', 'post,get', 0);
$is_submit_rollback = $nv_Request->get_int('is_rollback', 'post,get', 0);
$arr_option_type = [
    1 => $nv_Lang->getModule('leads'),
    2 => $nv_Lang->getModule('opportunities'),
];
if ($is_submit_rollback) {
    $result_update = 0;
    // hiện các leads liên quan ra để cho merge
    $list_rollback = get_list_log($type, $is_submit_rollback);
    if (!empty($list_rollback)) {
        if ($type == 1) {
            foreach ($list_rollback as $key => $value) {
                $data_update = $params_update = [];
                $data_update['source_leads'] = $value['source_leads'];
                $data_update['label'] = $value['label'];
                $data_update['user_id'] = $value['user_id'];
                $data_update['businessid'] = $value['businessid'];
                $data_update['teleproid'] = $value['teleproid'];
                $data_update['name'] = $value['name'];
                $data_update['phone'] = $value['phone'];
                $data_update['sub_phone'] = $value['sub_phone'];
                $data_update['email'] = $value['email'];
                $data_update['sub_email'] = $value['sub_email'];
                $data_update['address'] = $value['address'];
                $data_update['tax'] = $value['tax'];
                $data_update['company_name'] = $value['company_name'];
                $data_update['address_company'] = $value['address_company'];
                $data_update['status'] = $value['status'];
                $data_update['opportunities_id'] = $value['opportunities_id'];
                $data_update['affilacate_id'] = $value['affilacate_id'];
                $data_update['caregiver_id'] = $value['caregiver_id'];
                $data_update['timecreate'] = $value['timecreate'];
                $data_update['updatetime'] = $value['updatetime'];
                $data_update['active'] = $value['active'];
                $data_update['about'] = $value['about'];
                $data_update['last_comment'] = $value['last_comment'];
                $data_update['schedule'] = $value['schedule'];
                $data_update['is_check'] = $value['is_check'];
                $data_update['first_time'] = $value['first_time'];
                $data_update['siteid'] = $value['siteid'];
                $data_update['convert_contact'] = $value['convert_contact'];
                $data_update['convert_organization'] = $value['convert_organization'];
                $data_update['log_merge'] = "";
                $params_update = [
                    'leadsid' => $value['id'],
                    'admin_id' => $admin_info['admin_id'],
                    'data' => $data_update
                ];
                $result_update = nv_local_api('UpdateLeads', $params_update, $admin_info['username'], $module_name);
                if (!empty($value['comment'])) {
                    try {
                        $db->beginTransaction();
                        $sql_delete = "DELETE FROM nv4_vi_crmbidding_comment WHERE source = 1 AND sourceid = " . $value['id'];
                        $db->query($sql_delete);
                        foreach ($value['comment'] as $k => $val) {
                            $stmt = $db->prepare('INSERT INTO nv4_vi_crmbidding_comment(source, sourceid, post_id, timecreate, update_time, note, schedule, vip, type_export, id_mail) 
                                                VALUES (:source, :sourceid, :post_id, :timecreate, :update_time, :note, :schedule, :vip, :type_export, :id_mail)');
                            $stmt->bindParam(':source', $val['source'], PDO::PARAM_INT);
                            $stmt->bindParam(':sourceid', $val['sourceid'], PDO::PARAM_INT);
                            $stmt->bindParam(':post_id', $val['post_id'], PDO::PARAM_INT);
                            $stmt->bindParam(':timecreate', $val['timecreate'], PDO::PARAM_INT);
                            $stmt->bindParam(':update_time', $val['update_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':note', $val['note'], PDO::PARAM_STR);
                            $stmt->bindParam(':schedule', $val['schedule'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $val['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':type_export', $val['type_export'], PDO::PARAM_INT);
                            $stmt->bindParam(':id_mail', $val['id_mail'], PDO::PARAM_INT);
                            $stmt->execute();
                        }
                        $db->commit();
                    } catch (PDOException $e) {
                        $db->rollBack();
                        trigger_error($e->getMessage());                    
                    }
                }
            }  
        } else {
            foreach ($list_rollback as $key => $value) {
                $data_update = $params_update = [];
                $data_update['leadsid'] = $value['leadsid'];
                $data_update['label'] = $value['label'];
                $data_update['user_id'] = $value['user_id'];
                $data_update['orderid'] = $value['orderid'];
                $data_update['customs_id'] = $value['customs_id'];
                $data_update['name'] = $value['name'];
                $data_update['phone'] = $value['phone'];
                $data_update['sub_phone'] = $value['sub_phone'];
                $data_update['email'] = $value['email'];
                $data_update['sub_email'] = $value['sub_email'];
                $data_update['address'] = $value['address'];
                $data_update['tax'] = $value['tax'];
                $data_update['company_name'] = $value['company_name'];
                $data_update['address_company'] = $value['address_company'];
                $data_update['status'] = $value['status'];
                $data_update['affilacate_id'] = $value['affilacate_id'];
                $data_update['caregiver_id'] = $value['caregiver_id'];
                $data_update['timecreate'] = $value['timecreate'];
                $data_update['updatetime'] = $value['updatetime'];
                $data_update['active'] = $value['active'];
                $data_update['about'] = $value['about'];
                $data_update['last_comment'] = $value['last_comment'];
                $data_update['is_system'] = $value['is_system'];
                $data_update['schedule'] = $value['schedule'];
                $data_update['source_leads'] = $value['source_leads'];
                $data_update['siteid'] = $value['siteid'];
                $data_update['convert_contact'] = $value['convert_contact'];
                $data_update['convert_organization'] = $value['convert_organization'];
                $data_update['log_merge'] = "";
                $params_update = [
                    'opportunitiesid' => $value['id'],
                    'admin_id' => $admin_info['admin_id'],
                    'data' => $data_update
                ];
                $result_update = nv_local_api('UpdateOpportunities', $params_update, $admin_info['username'], $module_name);
                if (!empty($value['comment'])) {
                    try {
                        $db->beginTransaction();
                        $sql_delete = "DELETE FROM nv4_vi_crmbidding_comment WHERE source = 2 AND sourceid = " . $value['id'];
                        $db->query($sql_delete);
                        foreach ($value['comment'] as $k => $val) {
                            $stmt = $db->prepare('INSERT INTO nv4_vi_crmbidding_comment(source, sourceid, post_id, timecreate, update_time, note, schedule, vip, type_export, id_mail) 
                                                VALUES (:source, :sourceid, :post_id, :timecreate, :update_time, :note, :schedule, :vip, :type_export, :id_mail)');
                            $stmt->bindParam(':source', $val['source'], PDO::PARAM_INT);
                            $stmt->bindParam(':sourceid', $val['sourceid'], PDO::PARAM_INT);
                            $stmt->bindParam(':post_id', $val['post_id'], PDO::PARAM_INT);
                            $stmt->bindParam(':timecreate', $val['timecreate'], PDO::PARAM_INT);
                            $stmt->bindParam(':update_time', $val['update_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':note', $val['note'], PDO::PARAM_STR);
                            $stmt->bindParam(':schedule', $val['schedule'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $val['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':type_export', $val['type_export'], PDO::PARAM_INT);
                            $stmt->bindParam(':id_mail', $val['id_mail'], PDO::PARAM_INT);
                            $stmt->execute();
                        }
                        $db->commit();
                    } catch (PDOException $e) {
                        $db->rollBack();
                        trigger_error($e->getMessage());                    
                    }
                }
            }  
        }
    }
    if ($result_update) {
        $success = $nv_Lang->getModule('undo_success');
    }
}

// Nếu có id rollback => chuyển đến trang cho phép rollback
$array_field_merger = [];
if (!empty($id_rollback)) {
    $params_merger = [
        'type' => $type,
    ];
    $array_field_merger = nv_local_api('ShowColumnLeadsOpp', $params_merger, $admin_info['username'], $module_name);
    $array_field_merger = json_decode($array_field_merger, true);
    $array_field_merger = $array_field_merger['data'] ?? [];
    if (!empty($array_field_merger)) {
        unset($array_field_merger['log_merge']);
        $array_field_merger['comment'] = "Ghi chú";
    }
    // 1: leads 2: opportunities
    $list_rollback = get_list_log($type, $id_rollback);
    // Nếu không tồn tại leads hoặc cơ hội thì chuyển về trang chính
    if (empty($list_rollback)) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=undo_merge');
    }
    // Roll back cả những leads, cơ hội liên quan
} else {
    $per_page = 50;
    $page = $nv_Request->get_int('page', 'post,get', 1);
    $num_items = 0;
    // Nếu không có thì hiển thị danh sách cơ hội phân trang 50
    if (!empty($q_search)) {
        $_q = $db->dblikeescape($q_search);
        $where['OR'][] = [
            'LIKE' => [
                'name' => '%' . $_q . '%'
            ]
        ];
        $where['OR'][] = [
            '=' => [
                'phone' => $_q
            ]
        ];
        $where['OR'][] = [
            '=' => [
                'sub_phone' => $_q
            ]
        ];
        $where['OR'][] = [
            '=' => [
                'sub_email' => $_q
            ]
        ];
        $where['OR'][] = [
            '=' => [
                'email' => $_q
            ]
        ];
        $where['OR'][] = [
            '=' => [
                'tax' => $_q
            ]
        ];
    }

    $order['updatetime'] = 'DESC';
    if ($type == 1) {
        $where['AND'][] = [
            '!=' => [ 
                'log_merge' => "" 
            ]
        ];
        $where['AND'][] = [
            '=' => [ 
                'active' => 1 
            ]
        ];
        $param_all = [
            'where' => $where,
            'perpage' => $per_page,
            'use_elastic' => 0,
            'order' => $order,
            'page' => $page
        ];
        $list_all = nv_local_api('ListAllLeads', $param_all, $admin_info['username'], $module_name);
        $list_all = json_decode($list_all, true);
        $num_items = $list_all['total'] ?? 0;
        $list_all = $list_all['data'] ?? [];
    } else {
        $where['AND'][] = [
            '!=' => [ 
                'log_merge' => "" 
            ]
        ];
        $where['AND'][] = [
            '=' => [ 
                'active' => 1 
            ]
        ];
        $param_all = [
            'where' => $where,
            'perpage' => $per_page,
            'order' => $order,
            'use_elastic' => 0,
            'page' => $page
        ];
        $list_all = nv_local_api('ListAllOpportunities', $param_all, $admin_info['username'], $module_name);
        $list_all = json_decode($list_all, true);
        $num_items = $list_all['total'] ?? 0;
        $list_all = $list_all['data'] ?? [];
    }
}
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&type=' . $type;
$undo_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&type=' . $type;

if (!empty($q_search)) {
    $base_url .= '&q_search=' . urlencode($q_search);
}
$link_merge = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=duplicate&type=' . $type;

// Build Path
if (empty($id_rollback)) {
    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
}
$page_title = $nv_Lang->getModule('rollback_title');
$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('Q', $q_search);
$xtpl->assign('FORM_ACTION', $base_url);
$xtpl->assign('LINK_MERGE', $link_merge);
$xtpl->assign('LINK_UNDO', $undo_url);
if (!empty($data_rollback) and empty($success)) {
    $xtpl->assign('DATA_ROLLBACK', $data_rollback);
    $xtpl->parse('main.rollback');
}

if (!empty($generate_page)) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.info_merge.generate_page');
}

if (!empty($list_all)) {
    $stt = 1;
    foreach ($list_all as $key => $value) {
        $xtpl->assign('ROW', $value);
        $xtpl->assign('LINK_ROLLBACK', $base_url . '&id_rollback=' . $value['id']);
        $xtpl->assign('STT', $stt);
        $xtpl->parse('main.info_merge.loop.row');
        $stt++;
    }
    $xtpl->parse('main.info_merge.loop');
}

foreach ($arr_option_type as $k => $val) {
    $xtpl->assign('OPTION_TYPE', [
        'key' => $k,
        'title' => $val,
        'selected' => $k == $type ? 'selected' : ''
    ]);
    $xtpl->parse('main.info_merge.option_type');
}

if (empty($id_rollback)) {
    $xtpl->parse('main.info_merge');
} else {
    $xtpl->assign('ID_ROLLBACK', $id_rollback);
}

if (!empty($list_rollback) and empty($success)) {
    foreach ($list_rollback as $key => $value) {
        $xtpl->assign('ROW', $value['id']);
        $xtpl->parse('main.rollback.main_info.loopthead');
    }

    foreach ($array_field_merger as $k => $v) {
        for ($i = 0; $i < count($list_rollback); $i++) {
            if ($k == 'comment') {
                if (!empty($list_rollback[$i][$k])) {
                    foreach ($list_rollback[$i][$k] as $key => $value) {
                        $xtpl->assign('ROW_COMMENT', $value);
                        $xtpl->parse('main.rollback.main_info.loopfield.looprow.loopcomment');
                    }
                }
            } else {
                $xtpl->assign('ROW', $list_rollback[$i][$k]);
                $xtpl->parse('main.rollback.main_info.loopfield.looprow.loopdata');
            }
            $xtpl->parse('main.rollback.main_info.loopfield.looprow');
        }
        $xtpl->assign('FIELD_MERGER', $v);
        $xtpl->parse('main.rollback.main_info.loopfield');
    }
    $xtpl->parse('main.rollback.main_info');
    
    if ($type == 1) {
        $xtpl->assign('TITLE_ROLLBACK', $nv_Lang->getModule('title_rollback_leads'));
    } else {
        $xtpl->assign('TITLE_ROLLBACK', $nv_Lang->getModule('title_rollback_opp'));
    }
    $xtpl->parse('main.rollback');
}

if (!empty($success)) {
    $xtpl->assign('SUCCESS', $success);
    $xtpl->parse('main.success');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');
include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';

function get_list_log($type, $id_rollback) {
    global $admin_info, $module_name;
    $api_rollback = ($type == 1) ? 'GetDetailLeads' : 'GetDetailOpportunities';
    $where['AND'][] = [
        '!=' => [ 
            'log_merge' => "" 
        ]
    ];
    $param_rollback = [
        'where' => $where,
    ];
    if ($type == 1) {
        $param_rollback['leadid'] = $id_rollback;
    } else {
        $param_rollback['opportunitiesid'] = $id_rollback;
    }

    $list_rollback = nv_local_api($api_rollback, $param_rollback, $admin_info['username'], $module_name);
    $list_rollback = json_decode($list_rollback, true);
    $list_rollback = $list_rollback['data'] ?? [];
    if (!empty($list_rollback)) {
        $list_rollback = html_entity_decode($list_rollback['log_merge']);
        $list_rollback = json_decode($list_rollback, true);
    }
    return $list_rollback;
}
