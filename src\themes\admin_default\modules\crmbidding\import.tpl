<!-- BEGIN: main -->
<p>{LANG.import_g1}:</p>
<ol>
    <li>{LANG.import_g2} <strong><a href="{DOWNLOAD_TEMPLATE}"><i class="fa fa-download fa-fw"></i>{LANG.import_g3}</a></strong></li>
    <li>{LANG.import_g4}</li>
    <li>{LANG.import_g6}</li>
    <li>{LANG.import_g7} <strong><a href="{DOWNLOAD_TEMPLATE2}"><i class="fa fa-download fa-fw"></i>{LANG.import_g3}</a></strong></li>
    <li>{LANG.import_g8}</li>
    <li>{LANG.import_g5}</li>
</ol>
<!-- BEGIN: error -->
<div class="alert alert-danger">{ERROR}</div>
<!-- END: error -->
<!-- BEGIN: result -->
<div class="alert alert-success">
    <p>{LANG.import_result_1}:</p>
    <ul>
        <li>{LANG.import_result_2}: <strong>{NUM_READ}</strong></li>
        <li>{LANG.import_result_3}: <strong>{NUM_INSTALL}</strong></li>
        <li>{LANG.import_result_4}: <strong>{NUM_UPDATE}</strong></li>
    </ul>
</div>
<!-- END: result -->
<form method="post" action="" enctype="multipart/form-data" class="form-horizontal">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-6"><strong>{LANG.import_sel_datatype}:</strong></label>
                <div class="col-sm-14 col-md-18">
                    <select class="form-control" name="datatype">
                        <!-- BEGIN: datatype -->
                        <option value="{DATATYPE.key}"{DATATYPE.selected}>{DATATYPE.title}</option>
                        <!-- END: datatype -->
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-6"><strong>{LANG.import_sel_existstype}:</strong></label>
                <div class="col-sm-14 col-md-18">
                    <select class="form-control" name="existstype">
                        <!-- BEGIN: existstype -->
                        <option value="{EXISTSTYPE.key}"{EXISTSTYPE.selected}>{EXISTSTYPE.title}</option>
                        <!-- END: existstype -->
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-sm-10 col-md-6"><strong>{LANG.import_sel_excelfile}:</strong></label>
                <div class="col-sm-14 col-md-18">
                    <div class="input-group">
                        <input type="text" readonly="readonly" class="form-control" id="valExcelFile" />
                        <div class="input-group-btn">
                            <div class="btn btn-default" style="padding: 5px 10px;">
                                <div class="filetrigger">
                                    <span>{LANG.select}</span>
                                    <input type="file" name="fileexcel" class="" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-14 col-md-18 col-sm-offset-10 col-md-offset-6">
                    <input type="submit" name="submit" value="{LANG.import_submit}" class="btn btn-primary" />
                </div>
            </div>
        </div>
    </div>
</form>
<script type="text/javascript">
    $(function() {
        $('[name="fileexcel"]').change(function() {
            $('#valExcelFile').val($(this).val());
        });
    });
</script>
<!-- END: main -->
