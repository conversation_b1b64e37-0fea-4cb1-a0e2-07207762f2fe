<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */

if (!defined('NV_IS_FILE_ADMIN'))
    die('Stop!!!');

$page_title = $nv_Lang->getModule('statistics_2');

$xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $global_config['module_theme'] . "/modules/" . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('DATA_LINK', NV_BASE_SITEURL . 'themes/default/images/' . $module_file . '/js/');
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);



//Lấy tổng số ví đã kích hoạt
$sql = 'SELECT COUNT(*) num_activate FROM ' . $db_config['prefix'] . '_' . $module_data . '_money WHERE status=1';
$result = $db->query($sql);
$row = $result->fetch();
$xtpl->assign('num_activate', $row['num_activate']);

//Lấy tổng số ví còn tiền
$sql = 'SELECT COUNT(*) num_left, sum(money_total) money_left FROM ' . $db_config['prefix'] . '_' . $module_data . '_money WHERE status=1 AND money_total>0';
$result = $db->query($sql);
$row = $result->fetch();
$xtpl->assign('num_left', $row['num_left']);
$xtpl->assign('money_left', number_format(floatval($row['money_left']), 0, ',', '.') . ' VND');


$_from = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
$from = nv_date('d/m/Y', $_from); // Mặc định ngày 01 của tháng
$to = nv_date('d/m/Y', NV_CURRENTTIME);
$sfrom = nv_substr($nv_Request->get_title('sfrom', 'get', $from), 0, 10);
$sto = nv_substr($nv_Request->get_title('sto', 'get', $to), 0, 10);

$xtpl->assign('FROM', $sfrom);
$xtpl->assign('FROM_DEFAULT', $from);
$xtpl->assign('TO', $sto);
$xtpl->assign('TO_DEFAULT', $to);

$min_date = $db->query('SELECT created_time FROM ' . $db_config['prefix'] . '_' . $module_data . '_transaction ORDER BY created_time ASC LIMIT 1')->fetchColumn();
$xtpl->assign('MINDATE', nv_date('d/m/Y', $min_date));
$xtpl->assign('lang_static_renewal', sprintf($nv_Lang->getModule('lang_static_renewal'), $sfrom, $sto));

//Tách ngày tháng thành mảng để xử lý
$_arr_sfrom = explode('/', $sfrom);
$_arr_sto = explode('/', $sto);

//Lấy tổng số số tiền NẠP VÀO VÍ từ ngày $sfrom đến ngày $sto
$sql = 'SELECT SUM(money_total) money_in FROM ' . $db_config['prefix'] . '_' . $module_data . '_transaction WHERE status=1 AND money_total>0 AND created_time >= '  . mktime(0, 0, 0, $_arr_sfrom[1], $_arr_sfrom[0], $_arr_sfrom[2]) . ' AND created_time <= ' . mktime(23, 59, 59, $_arr_sto[1], $_arr_sto[0], $_arr_sto[2]);
$result = $db->query($sql);
$row = $result->fetch();
$xtpl->assign('money_in', number_format(floatval($row['money_in']), 0, ',', '.') . ' VND');

//Lấy tổng số số tiền ĐÃ CHI TIÊU từ ngày $sfrom đến ngày $sto
$sql = 'SELECT SUM(money_total) money_out FROM ' . $db_config['prefix'] . '_' . $module_data . '_transaction WHERE status=-1 AND money_total>0 AND transaction_status = 4 AND created_time >= '  . mktime(0, 0, 0, $_arr_sfrom[1], $_arr_sfrom[0], $_arr_sfrom[2]) . ' AND created_time <= ' . mktime(23, 59, 59, $_arr_sto[1], $_arr_sto[0], $_arr_sto[2]);
$result = $db->query($sql);
$row = $result->fetch();
$xtpl->assign('money_out', number_format(floatval($row['money_out']), 0, ',', '.') . ' VND');


if (!empty($error)) {
    $xtpl->assign('ERROR', $error);
    $xtpl->parse('main.error');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
