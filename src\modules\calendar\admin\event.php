<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    exit('Stop!!!');
}

require_once NV_ROOTDIR . '/includes/core/amlich.php';

$db->sqlreset()
    ->select('*')
    ->from(NV_PREFIXLANG . '_' . $module_data);
$result = $db->query($db->sql());
while ($row = $result->fetch()) {
    $array_calendar[$row['id']] = $row;
}

$id = $nv_Request->get_int('id', 'get', 0);

$array = $error = [];
if ($id) {
    $array = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_event WHERE id=' . $id)->fetch();
    if (empty($array)) {
        nv_info_die($nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_content'));
    }
    $array['date_event'] = nv_date('d/m/Y', $array['date_event']);
    $array['time_event_start'] = gmdate('H:i', $array['time_event_start']);
    $array['time_event_end'] = gmdate('H:i', $array['time_event_end']);
    $array['repeat_until_date'] = $array['repeat_until_date'] ? nv_date('d/m/Y', $array['repeat_until_date']) : '';
    
    if ($array['is_lunar'] == 1) {
        $date_parts = explode('/', $array['date_event']);
        if (count($date_parts) == 3) {
            $day_solar = intval($date_parts[0]);
            $month_solar = intval($date_parts[1]);
            $year_solar = intval($date_parts[2]);
            $lunar_info = convertSolar2Lunar($day_solar, $month_solar, $year_solar, 7);
            $array['date_event'] = str_pad($lunar_info[0], 2, '0', STR_PAD_LEFT) . '/' . str_pad($lunar_info[1], 2, '0', STR_PAD_LEFT) . '/' . str_pad($lunar_info[2], 4, '0', STR_PAD_LEFT);
            
            if (!empty($array['repeat_until_date'])) {
                $repeat_parts = explode('/', $array['repeat_until_date']);
                if (count($repeat_parts) == 3) {
                    $day_solar = intval($repeat_parts[0]);
                    $month_solar = intval($repeat_parts[1]);
                    $year_solar = intval($repeat_parts[2]);
                    $lunar_info = convertSolar2Lunar($day_solar, $month_solar, $year_solar, 7);
                    $array['repeat_until_date'] = str_pad($lunar_info[0], 2, '0', STR_PAD_LEFT) . '/' . str_pad($lunar_info[1], 2, '0', STR_PAD_LEFT) . '/' . str_pad($lunar_info[2], 4, '0', STR_PAD_LEFT);
                }
            }
        }
    }
    $page_title = $nv_Lang->getModule('event_edit');
    $form_action = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $id;
} else {
    $array = [
        'id' => 0,
        'calendar_id' => 0,
        'date_event' => nv_date('d/m/Y', NV_CURRENTTIME),
        'time_event_start' => '00:00',
        'time_event_end' => '23:59',
        'title' => '',
        'description' => '',
        'repeat_type' => 0,
        'repeat_until_date' => '',
        'is_lunar' => 0
    ];

    $page_title = $nv_Lang->getModule('event_add');
    $form_action = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
}

if ($nv_Request->isset_request('action', 'post')) {
    $action = $nv_Request->get_title('action', 'post', '');
    $json = [];
    
    if ($action == 'solar2lunar' || $action == 'lunar2solar') {
        $day = $nv_Request->get_int('day', 'post', 0);
        $month = $nv_Request->get_int('month', 'post', 0);
        $year = $nv_Request->get_int('year', 'post', 0);
        $lunarLeap = $nv_Request->get_int('lunarLeap', 'post', 0);

        if ($day > 0 && $month > 0 && $year > 0) {
            if ($action == 'solar2lunar') {
                $lunar = convertSolar2Lunar($day, $month, $year, 7);
                // Xác định tháng nhuận
                $a11 = getLunarMonth11($lunar[2], 7);
                $leapOff = getLeapMonthOffset($a11, 7);
                $leapMonth = $leapOff - 2;
                if ($leapMonth < 0) {
                    $leapMonth += 12;
                }
                
                $json['data'] = str_pad($lunar[0], 2, '0', STR_PAD_LEFT) . '/' . str_pad($lunar[1], 2, '0', STR_PAD_LEFT) . '/' . str_pad($lunar[2], 4, '0', STR_PAD_LEFT);
                $json['is_leap'] = ($lunar[1] == $leapMonth && $leapOff > 0);
            } else {
                $solar = convertLunar2Solar($day, $month, $year, $lunarLeap, 7);
                $json['data'] = str_pad($solar[0], 2, '0', STR_PAD_LEFT) . '/' . str_pad($solar[1], 2, '0', STR_PAD_LEFT) . '/' .  str_pad($solar[2], 4, '0', STR_PAD_LEFT);
            }
            $json['status'] = 'success';
        } else {
            $json['status'] = 'error';
            $json['message'] = $nv_Lang->getModule('invalid_date');
        }
        nv_jsonOutput($json);
    }
}

if ($nv_Request->get_title('save', 'post', '') === NV_CHECK_SESSION) {
    $array['calendar_id'] = $nv_Request->get_int('calendar_id', 'post', 0);
    $array['date_event'] = $nv_Request->get_title('date_event', 'post', '');
    $array['is_lunar'] = $nv_Request->get_int('is_lunar', 'post', 0);
    $array['is_leap_month'] = $nv_Request->get_int('is_leap_month', 'post', 0);
    
    if ($id) {
        $event_old = $db->query('SELECT is_lunar FROM ' . NV_PREFIXLANG . '_' . $module_data . '_event WHERE id=' . $id)->fetch();
        if ($event_old['is_lunar'] != $array['is_lunar']) {
            $error[] = $nv_Lang->getModule('error_not_allowed_convert_calendar');
        }
    }
    
    $array['time_event_start'] = $nv_Request->get_title('time_event_start', 'post', '');
    $array['time_event_end'] = $nv_Request->get_title('time_event_end', 'post', '');
    $array['title'] = nv_substr($nv_Request->get_title('title', 'post', ''), 0, 190);
    $array['description'] = $nv_Request->get_textarea('description', '', NV_ALLOWED_HTML_TAGS);
    $array['description'] = nv_editor_nl2br($array['description']);
    $array['repeat_type'] = $nv_Request->get_int('repeat_type', 'post', 0);
    $array['repeat_until_date'] = $nv_Request->get_title('repeat_until_date', 'post', '');
    $date_parts = explode('/', $array['date_event']);
    $date_event = NV_CURRENTTIME;
    if (count($date_parts) == 3) {
        if ($array['is_lunar']) {
            $lunar_day = intval($date_parts[0]);
            $lunar_month = intval($date_parts[1]);
            $lunar_year = intval($date_parts[2]);
            
            $solar_info = convertLunar2Solar($lunar_day, $lunar_month, $lunar_year, $array['is_leap_month'] ? 1 : 0, 7);
            if (count($solar_info) >= 3) {
                $date_event = mktime(0, 0, 0, $solar_info[1], $solar_info[0], $solar_info[2]);
            }
        } else {
            $date_event = mktime(0, 0, 0, intval($date_parts[1]), intval($date_parts[0]), intval($date_parts[2]));
        }
    }
    
    $repeat_until_date = 0;
    if (!empty($array['repeat_until_date'])) {
        if ($array['is_lunar']) {
            $repeat_parts = explode('/', $array['repeat_until_date']);
            if (count($repeat_parts) == 3) {
                $day = intval($repeat_parts[0]);
                $month = intval($repeat_parts[1]);
                $year = intval($repeat_parts[2]);
                
                $is_repeat_leap = $nv_Request->get_int('is_repeat_leap_month', 'post', 0);
                
                $solar_info = convertLunar2Solar($day, $month, $year, $is_repeat_leap ? 1 : 0, 7);
                if (count($solar_info) >= 3) {
                    $repeat_until_date = mktime(0, 0, 0, $solar_info[1], $solar_info[0], $solar_info[2]);
                }
            }
        } else {
            $repeat_until_date = nv_d2u_post($array['repeat_until_date']);
        }
    } else {
        if ($array['repeat_type'] == 0) {
            $repeat_until_date = mktime(0, 0, 0, nv_date('m', $date_event), nv_date('d', $date_event), nv_date('Y', $date_event));
        } elseif ($array['repeat_type'] == 1) {
            $repeat_until_date = mktime(0, 0, 0, nv_date('m', $date_event), nv_date('d', $date_event) + 30, nv_date('Y', $date_event));
        } elseif ($array['repeat_type'] == 2) {
            $repeat_until_date = mktime(0, 0, 0, nv_date('m', $date_event), nv_date('d', $date_event) + (20 * 7), nv_date('Y', $date_event));
        } elseif ($array['repeat_type'] == 3) {
            $repeat_until_date = mktime(0, 0, 0, nv_date('m', $date_event) + 15, nv_date('d', $date_event), nv_date('Y', $date_event));
        } elseif ($array['repeat_type'] == 4) {
            $repeat_until_date = mktime(0, 0, 0, nv_date('m', $date_event), nv_date('d', $date_event), nv_date('Y', $date_event) + 10);
        }
    }

    $time_event_start = timetoSecond($array['time_event_start']);
    $time_event_end = timetoSecond($array['time_event_end']);

    if ($array['calendar_id'] <= 0) {
        $error[] = $nv_Lang->getModule('calendar_select');
    }

    if (empty($array['title'])) {
        $error[] = $nv_Lang->getModule('name_event');
    }

    if ($repeat_until_date < $date_event && $array['repeat_type'] != 0) {
        $error[] = $nv_Lang->getModule('repeat_until_date_error');
    }

    // Chỉ kiểm tra trùng khi thêm mới
    if (!$id) {
        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_event WHERE title = :title AND date_event = " . $date_event;
        $sth = $db->prepare($sql);
        $sth->bindParam(':title', $array['title'], PDO::PARAM_STR);
        $sth->execute();
        if ($sth->rowCount()) {
            $error[] = $nv_Lang->getModule('name_event_error_dulipcate');
        }
    }

    if (empty($error)) {
        try {
            if ($array['is_lunar']) {
                $current_date = $date_event;
                $dates_to_add = 1;
                
                if ($array['repeat_type'] > 0) {
                    $repeat_limits = [
                        1 => 30,
                        2 => 20,
                        3 => 15,
                        4 => 10
                    ];
                    $max_repeats = isset($repeat_limits[$array['repeat_type']]) ? $repeat_limits[$array['repeat_type']] : 30;
                    $days_diff = ceil(($repeat_until_date - $date_event) / 86400);
                    
                    if ($array['repeat_type'] == 1) {
                        $dates_to_add = min($days_diff, $max_repeats);
                    } elseif ($array['repeat_type'] == 2) {
                        $dates_to_add = min(ceil($days_diff / 7), $max_repeats);
                    } elseif ($array['repeat_type'] == 3) {
                        $dates_to_add = min(ceil($days_diff / 30), $max_repeats);
                    } elseif ($array['repeat_type'] == 4) {
                        $dates_to_add = min(ceil($days_diff / 365), $max_repeats);
                    }
                }
                
                $dates = [];
                
                $lunar_info = convertSolar2Lunar(nv_date('j', $date_event), nv_date('n', $date_event), nv_date('Y', $date_event), 7);
                $lunar_day = $lunar_info[0];
                $lunar_month = $lunar_info[1];
                $lunar_year = $lunar_info[2];
                $lunar_leap = $lunar_info[3];
                
                $dates[] = [
                    'date' => $date_event,
                    'lunar_date' => str_pad($lunar_day, 2, '0', STR_PAD_LEFT) . '/' . str_pad($lunar_month, 2, '0', STR_PAD_LEFT) . '/' . str_pad($lunar_year, 4, '0', STR_PAD_LEFT)
                ];
                
                if ($dates_to_add > 1) {
                    $next_date = $date_event;
                    
                    for ($i = 1; $i < $dates_to_add; $i++) {
                        if ($array['repeat_type'] == 1) {
                            $jd = jdFromDate(nv_date('j', $next_date), nv_date('n', $next_date), nv_date('Y', $next_date));
                            $jd_next = $jd + 1;
                            $date_next = jdToDate($jd_next);
                            $next_date = mktime(0, 0, 0, $date_next[1], $date_next[0], $date_next[2]);
                            
                        } elseif ($array['repeat_type'] == 2) {
                            $jd = jdFromDate(nv_date('j', $next_date), nv_date('n', $next_date), nv_date('Y', $next_date));
                            $jd_next = $jd + 7;
                            $date_next = jdToDate($jd_next);
                            $next_date = mktime(0, 0, 0, $date_next[1], $date_next[0], $date_next[2]);
                            
                        } elseif ($array['repeat_type'] == 3) {
                            if ($lunar_month == 12) {
                                $lunar_month = 1;
                                $lunar_year++;
                            } else {
                                $lunar_month++;
                            }
                            
                            $a11 = getLunarMonth11($lunar_year, 7);
                            $b11 = getLunarMonth11($lunar_year + 1, 7);
                            $k = floor(0.5 + ($a11 - 2415021.076998695) / 29.530588853);
                            $off = $lunar_month - 11;
                            if ($off < 0) {
                                $off = 12;
                            }
                            
                            $nm1 = getNewMoonDay($k + $off, 7);
                            $nm2 = getNewMoonDay($k + $off + 1, 7);
                            $monthDays = $nm2 - $nm1;
                            
                            if ($lunar_day > $monthDays) {
                                $lunar_day = $monthDays;
                            }
                            
                            $solar_info = convertLunar2Solar($lunar_day, $lunar_month, $lunar_year, 0, 7);
                            $next_date = mktime(0, 0, 0, $solar_info[1], $solar_info[0], $solar_info[2]);
                            
                        } elseif ($array['repeat_type'] == 4) {
                            $lunar_year++;
                            
                            $a11 = getLunarMonth11($lunar_year, 7);
                            $b11 = getLunarMonth11($lunar_year + 1, 7);
                            $k = floor(0.5 + ($a11 - 2415021.076998695) / 29.530588853);
                            $off = $lunar_month - 11;
                            if ($off < 0) {
                                $off = 12;
                            }
                            
                            $nm1 = getNewMoonDay($k + $off, 7);
                            $nm2 = getNewMoonDay($k + $off + 1, 7);
                            $monthDays = $nm2 - $nm1;
                            
                            if ($lunar_day > $monthDays) {
                                $lunar_day = $monthDays;
                            }
                            
                            $solar_info = convertLunar2Solar($lunar_day, $lunar_month, $lunar_year, 0, 7);
                            $next_date = mktime(0, 0, 0, $solar_info[1], $solar_info[0], $solar_info[2]);
                        }
                        
                        if ($next_date > $repeat_until_date) {
                            break;
                        }
                        
                        $lunar_info = convertSolar2Lunar(nv_date('j', $next_date), nv_date('n', $next_date), nv_date('Y', $next_date), 7);
                        
                        $dates[] = [
                            'date' => $next_date,
                            'lunar_date' => str_pad($lunar_info[0], 2, '0', STR_PAD_LEFT) . '/' . str_pad($lunar_info[1], 2, '0', STR_PAD_LEFT) . '/' . str_pad($lunar_info[2], 4, '0', STR_PAD_LEFT)
                        ];
                    }
                }
                
                foreach ($dates as $date_item) {
                    $event_date = $date_item['date'];
                    $lunar_date_note = $date_item['lunar_date'];
                    
                    $sql = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_event (calendar_id, date_event, time_event_start, time_event_end, title, description, add_time, repeat_type, repeat_until_date, is_lunar)
                           VALUES (:calendar_id, :date_event, :time_event_start, :time_event_end, :title, :description, " . NV_CURRENTTIME . ", 0, 0, :is_lunar)";
                    $sth = $db->prepare($sql);
                    $sth->bindParam(':calendar_id', $array['calendar_id'], PDO::PARAM_INT);
                    $sth->bindParam(':date_event', $event_date, PDO::PARAM_INT);
                    $sth->bindParam(':time_event_start', $time_event_start, PDO::PARAM_INT);
                    $sth->bindParam(':time_event_end', $time_event_end, PDO::PARAM_INT);
                    $sth->bindParam(':title', $array['title'], PDO::PARAM_STR);
                    $sth->bindParam(':description', $array['description'], PDO::PARAM_STR);
                    $sth->bindParam(':is_lunar', $array['is_lunar'], PDO::PARAM_INT);
                    $sth->execute();
                    
                    $event_id = $db->lastInsertId();
                    
                    $date_pattern = nv_date('d/m/Y', $event_date);
                    $db->query("INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_repeats (event_id, date_pattern) VALUES (" . $event_id . ", '" . $date_pattern . "')");
                }
                
                nv_insert_logs(NV_LANG_DATA, $module_name, 'Add Lunar Events', 'Count: ' . count($dates), $admin_info['userid']);
            } else {
                $update_repeats = false;
                $event_id = 0;
                
                if (!$id) {
                    $sql = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_event (calendar_id, date_event, time_event_start, time_event_end, title, description, add_time, repeat_type, repeat_until_date, is_lunar)
                            VALUES (:calendar_id, :date_event, :time_event_start, :time_event_end, :title, :description, " . NV_CURRENTTIME . ", :repeat_type, :repeat_until_date, :is_lunar)";
                    $sth = $db->prepare($sql);
                    $sth->bindParam(':calendar_id', $array['calendar_id'], PDO::PARAM_INT);
                    $sth->bindParam(':date_event', $date_event, PDO::PARAM_INT);
                    $sth->bindParam(':time_event_start', $time_event_start, PDO::PARAM_INT);
                    $sth->bindParam(':time_event_end', $time_event_end, PDO::PARAM_INT);
                    $sth->bindParam(':title', $array['title'], PDO::PARAM_STR);
                    $sth->bindParam(':description', $array['description'], PDO::PARAM_STR);
                    $sth->bindParam(':repeat_type', $array['repeat_type'], PDO::PARAM_INT);
                    $sth->bindParam(':repeat_until_date', $repeat_until_date, PDO::PARAM_INT);
                    $sth->bindParam(':is_lunar', $array['is_lunar'], PDO::PARAM_INT);
                    $sth->execute();
                    $event_id = $db->lastInsertId();
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Add Event', 'ID: ' . $event_id, $admin_info['userid']);
                    $update_repeats = true;
                } else {
                    $event_old = $db->query('SELECT date_event, repeat_type, repeat_until_date FROM ' . NV_PREFIXLANG . '_' . $module_data . '_event WHERE id=' . $id)->fetch();
                    $need_update_repeats = ($event_old['date_event'] != $date_event || $event_old['repeat_type'] != $array['repeat_type'] || $event_old['repeat_until_date'] != $repeat_until_date);
                    $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_event SET
                             calendar_id = :calendar_id,
                             date_event = :date_event,
                             time_event_start = :time_event_start,
                             time_event_end = :time_event_end,
                             title = :title,
                             description = :description,
                             repeat_type = :repeat_type,
                             repeat_until_date = :repeat_until_date,
                             is_lunar = :is_lunar,
                             edit_time = " . NV_CURRENTTIME . "
                             WHERE id=" . $id;
                    $sth = $db->prepare($sql);
                    $sth->bindParam(':calendar_id', $array['calendar_id'], PDO::PARAM_INT);
                    $sth->bindParam(':date_event', $date_event, PDO::PARAM_INT);
                    $sth->bindParam(':time_event_start', $time_event_start, PDO::PARAM_INT);
                    $sth->bindParam(':time_event_end', $time_event_end, PDO::PARAM_INT);
                    $sth->bindParam(':title', $array['title'], PDO::PARAM_STR);
                    $sth->bindParam(':description', $array['description'], PDO::PARAM_STR);
                    $sth->bindParam(':repeat_type', $array['repeat_type'], PDO::PARAM_INT);
                    $sth->bindParam(':repeat_until_date', $repeat_until_date, PDO::PARAM_INT);
                    $sth->bindParam(':is_lunar', $array['is_lunar'], PDO::PARAM_INT);
                    $sth->execute();
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Edit Event', 'ID: ' . $id, $admin_info['userid']);
                    
                    if ($need_update_repeats) {
                        $event_id = $id;
                        $update_repeats = true;
                    }
                }
                if ($update_repeats) {
                    if ($id) {
                        $db->query("DELETE FROM " . NV_PREFIXLANG . "_" . $module_data . "_repeats WHERE event_id = " . $event_id);
                    }
                    
                    $repeat_limits = [
                        1 => 30,
                        2 => 20,
                        3 => 15,
                        4 => 10
                    ];
                    $max_repeats = isset($repeat_limits[$array['repeat_type']]) ? $repeat_limits[$array['repeat_type']] : 500;
                    $repeat_count = 0;
                    $current_date = $date_event;
                    
                    if ($array['repeat_type'] > 0) {
                        $estimated_repeats = 0;
                        $days_diff = ceil(($repeat_until_date - $date_event) / 86400);
                        
                        if ($array['repeat_type'] == 1) {
                            $estimated_repeats = $days_diff;
                        } elseif ($array['repeat_type'] == 2) {
                            $estimated_repeats = ceil($days_diff / 7);
                        } elseif ($array['repeat_type'] == 3) {
                            $estimated_repeats = ceil($days_diff / 30);
                        } elseif ($array['repeat_type'] == 4) {
                            $estimated_repeats = ceil($days_diff / 365);
                        }
                        
                        if ($estimated_repeats > $max_repeats) {
                            if ($array['repeat_type'] == 1) {
                                $repeat_until_date = mktime(0, 0, 0, nv_date('m', $date_event), nv_date('d', $date_event) + $max_repeats, nv_date('Y', $date_event));
                            } elseif ($array['repeat_type'] == 2) {
                                $repeat_until_date = mktime(0, 0, 0, nv_date('m', $date_event), nv_date('d', $date_event) + ($max_repeats * 7), nv_date('Y', $date_event));
                            } elseif ($array['repeat_type'] == 3) {
                                $repeat_until_date = mktime(0, 0, 0, nv_date('m', $date_event) + $max_repeats, nv_date('d', $date_event), nv_date('Y', $date_event));
                            } elseif ($array['repeat_type'] == 4) {
                                $repeat_until_date = mktime(0, 0, 0, nv_date('m', $date_event), nv_date('d', $date_event), nv_date('Y', $date_event) + $max_repeats);
                            }
                            $db->query("UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_event SET repeat_until_date = " . $repeat_until_date . " WHERE id = " . $event_id);
                        }
                    }
                    if ($array['repeat_type'] == 0) {
                        $date_pattern = nv_date('d/m/Y', $current_date);
                        $db->query("INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_repeats (event_id, date_pattern) VALUES (" . $event_id . ", '" . $date_pattern . "')");
                    } else {
                        $dates_to_add = $max_repeats;
                        $count_added = 0;
                        $date_pattern = nv_date('d/m/Y', $current_date);
                        $db->query("INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_repeats (event_id, date_pattern) VALUES (" . $event_id . ", '" . $date_pattern . "')");
                        $count_added++;
                        $next_date = $current_date;
                        
                        for ($i = 1; $i < $dates_to_add; $i++) {
                            $previous_date = $next_date;
                            
                            if ($array['repeat_type'] == 1) {
                                $next_date = mktime(0, 0, 0, nv_date('m', $next_date), nv_date('d', $next_date) + 1, nv_date('Y', $next_date));
                            } elseif ($array['repeat_type'] == 2) {
                                $next_date = mktime(0, 0, 0, nv_date('m', $next_date), nv_date('d', $next_date) + 7, nv_date('Y', $next_date));
                            } elseif ($array['repeat_type'] == 3) {
                                $day = nv_date('d', $next_date);
                                $month = nv_date('m', $next_date);
                                $year = nv_date('Y', $next_date);
                                
                                if ($month == 12) {
                                    $month = 1;
                                    $year++;
                                } else {
                                    $month++;
                                }
                                
                                $days_in_month = cal_days_in_month(CAL_GREGORIAN, $month, $year);
                                if ($day > $days_in_month) {
                                    $day = $days_in_month;
                                }
                                
                                $next_date = mktime(0, 0, 0, $month, $day, $year);
                            } elseif ($array['repeat_type'] == 4) {
                                $next_date = mktime(0, 0, 0, nv_date('m', $next_date), nv_date('d', $next_date), nv_date('Y', $next_date) + 1);
                            }
                            
                            if ($next_date > $repeat_until_date) {
                                break;
                            }
                            
                            $date_pattern = nv_date('d/m/Y', $next_date);
                            $sql = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . "_repeats (event_id, date_pattern) VALUES (" . $event_id . ", '" . $date_pattern . "')";
                            $result = $db->query($sql);
                            $count_added++;
                            
                            if ($count_added >= $dates_to_add) {
                                break;
                            }
                        }
                    }
                }
            }

            $nv_Cache->delMod($module_name);
            nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op . '&success=' . ($id ? 2 : 1));
        } catch (PDOException $e) {
            trigger_error($e->getMessage());
        }
    }
}

if (defined('NV_EDITOR')) {
    require_once NV_ROOTDIR . '/' . NV_EDITORSDIR . '/' . NV_EDITOR . '/nv.php';
}
$array['description'] = nv_htmlspecialchars(nv_editor_br2nl($array['description']));
if (defined('NV_EDITOR') and nv_function_exists('nv_aleditor')) {
    $array['description'] = nv_aleditor('description', '100%', '300px', $array['description']);
} else {
    $array['description'] = '<textarea class="form-control" rows="10" name="description">' . $array['description'] . '</textarea>';
}

$success = $nv_Request->get_int('success', 'get', 0); // Lấy trạng thái thành công để hiện thông báo

$xtpl = new XTemplate('event.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('DATA', $array);
$xtpl->assign('IS_LUNAR', ($array['is_lunar'] == 1 ? ' checked="checked"' : '') . ($id ? ' disabled' : ''));
$xtpl->assign('MODULE_FILE', $module_file);

if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

if ($success) {
    $xtpl->assign('SUCCESS_MSG', $nv_Lang->getModule('alert_action_event_success_' . $success));
    $xtpl->parse('main.success');
}

$repeat = [
    0 => $nv_Lang->getModule('no_repeat'),
    1 => $nv_Lang->getModule('every_day'),
    2 => $nv_Lang->getModule('every_week'),
    3 => $nv_Lang->getModule('every_month'),
    4 => $nv_Lang->getModule('every_year')
];

foreach ($repeat as $key => $value) {
    $xtpl->assign('REPEAT', [
        'key' => $key,
        'value' => $value,
        'selected' => $key == $array['repeat_type'] ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.repeat');
}

// Xuất danh sách bộ lịch
if (!empty($array_calendar)) {
    foreach ($array_calendar as $value) {
        $value['selected'] = $value['id'] == $array['calendar_id'] ? ' selected="selected"' : '';
        $xtpl->assign('CALENDAR', $value);
        $xtpl->parse('main.calendar');
    }
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
