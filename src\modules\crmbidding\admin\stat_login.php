<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    exit('Stop!!!');
}

$page_title = $table_caption = $nv_Lang->getModule('stat_login');

$ts_to = $nv_Request->get_title('to', 'post,get', '');
unset($m);
$time_to = (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $ts_to, $m)) ? mktime(23, 59, 59, $m[2], $m[1], $m[3]) : time();
$ts_from = $nv_Request->get_title('from', 'post,get', '');
unset($m);
$time_from = (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $ts_from, $m)) ? mktime(0, 0, 0, $m[2], $m[1], $m[3]) : $time_to - (30 * 86400);

// Query DB
$result = [];
$where = 'tb3.logtime BETWEEN ' . $time_from . ' AND ' . $time_to;

// Số lượng user
$db->sqlreset()
    ->select('COUNT(DISTINCT tb1.userid)')
    ->from(NV_USERS_GLOBALTABLE . ' tb1 JOIN ' . NV_USERS_GLOBALTABLE . '_info tb2 ON tb1.userid = tb2.userid' . ' JOIN ' . NV_USERS_GLOBALTABLE . '_login_log tb3 ON tb1.userid = tb3.userid')
    ->where($where);

$result['users_sum'] = $db->query($db->sql())->fetchColumn();

// Số lượng doanh nghiệp : các user có mã số thuế không trùng nhau
$where .= ' AND tb2.mst != ""';
$db->select('COUNT(DISTINCT tb2.mst) as num_of_business')
    ->where($where);
$res = $db->query($db->sql());
$result['business_number'] = $res->fetch()['num_of_business'];

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('NV_BASE_SITEURL', NV_BASE_SITEURL);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('NV_LANG_INTERFACE', NV_LANG_INTERFACE);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('FORM_ACTION', NV_BASE_ADMINURL . 'index.php');
$xtpl->assign('OP', $op);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);

$xtpl->assign('time_from', date('d/m/Y', $time_from));
$xtpl->assign('time_to', date('d/m/Y', $time_to));

$xtpl->assign('RESULT', $result);

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
