<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jul 06, 2011, 06:31:13 AM
 */

if (!defined('NV_MAINFILE'))
    die('Stop!!!');

if (!function_exists('nv_block_elink_affiliate_info')) {
    /**
     * @param array $block_config
     * @return string
     */
    function nv_block_elink_affiliate_info($block_config)
    {
        global $db, $site_mods, $global_config, $nv_Cache, $db_slave, $nv_Request, $db_config, $user_info, $module_config, $nv_Lang;

        $module = $block_config['module'];
        $module_data = $site_mods[$module]['module_data'];
        $module_file = $site_mods[$module]['module_file'];
        $module_info = $site_mods[$module];

        $nv_Lang->loadModule($module_file, false, true);

        if (file_exists(NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file . '/block.affiliate_info.tpl')) {
            $block_theme = $global_config['module_theme'];
        } else {
            $block_theme = 'default';
        }

        $xtpl = new XTemplate('block.affiliate_info.tpl', NV_ROOTDIR . '/themes/' . $block_theme . '/modules/' . $module_file);
        $xtpl->assign('LANG', \NukeViet\Core\Language::$tmplang_module);
        $nv_Lang->changeLang();

        /*
         * Tìm tra người giới thiệu
         * Ưu tiên mã khuyến mãi
         * Sau đó là mã giới thiệu
         */
        $affiliate_user = [];
        if (defined('NV_PROMOTION_CODE')) {
            $promotion_code = NV_PROMOTION_CODE;
        } else {
            $promotion_code = $nv_Request->get_title('promotion_code', 'cookie', '');
        }
        if (!empty($promotion_code)) {
            $sql = "SELECT tb2.userid, tb2.email, tb2.first_name, tb2.last_name, tb2.photo
            FROM " . $db_config['prefix'] . "_elink_promotion_code tb1, " . NV_USERS_GLOBALTABLE . " tb2
            WHERE tb1.userid=tb2.userid AND tb2.active=1 AND tb1.promo_code=" . $db->quote($promotion_code);
            $affiliate_user = $db->query($sql)->fetch();
        }
        if (empty($affiliate_user)) {
            if (defined('NV_AFFILIATE_CODE')) {
                $affiliate_code = NV_AFFILIATE_CODE;
            } else {
                $affiliate_code = $nv_Request->get_title('affiliate_code', 'cookie', '');
            }
            if (!empty($affiliate_code)) {
                $sql = "SELECT tb2.userid, tb2.email, tb2.first_name, tb2.last_name, tb2.photo
                FROM " . NV_USERS_GLOBALTABLE . " tb2
                WHERE tb2.active=1 AND tb2.username=" . $db->quote($affiliate_code);
                $affiliate_user = $db->query($sql)->fetch();
            }
        }

        if (empty($affiliate_user)) {
            $xtpl->assign('DEFAULT_NOTICE', $module_config[$module]['default_notice']);
            $xtpl->parse('main.default_info');
        } else {
            // Xác định số điện thoại của người giới thiệu
            $sql = "SELECT * FROM " . NV_USERS_GLOBALTABLE . "_info WHERE userid=" . $affiliate_user['userid'];
            $uinfo = $db->query($sql)->fetch();
            if (!empty($uinfo['phone'])) {
                $affiliate_user['phone'] = $uinfo['phone'];
            } else {
                $affiliate_user['phone'] = '';
            }
            $affiliate_user['full_name'] = nv_show_name_user($affiliate_user['first_name'], $affiliate_user['last_name']);
            if (file_exists(NV_ROOTDIR . '/' . $affiliate_user['photo']) and !empty($affiliate_user['photo'])) {
                $avata = NV_BASE_SITEURL . $affiliate_user['photo'];
            } else {
                $avata = NV_BASE_SITEURL . 'themes/' . $block_theme . '/images/users/no_avatar.png';
            }
            $affiliate_user['photo'] = $avata;
            $affiliate_user['email'] = nv_EncodeEmail($affiliate_user['email']);

            $xtpl->assign('USER', $affiliate_user);
            $xtpl->parse('main.userinfo');
        }

        $xtpl->parse('main');
        return $xtpl->text('main');
    }
}

if (defined('NV_SYSTEM')) {
    global $site_mods;
    $module = $block_config['module'];
    if (isset($site_mods[$module])) {
        $content = nv_block_elink_affiliate_info($block_config);
    }
}
