<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 *
 * Thank you <PERSON> for the algorithm to calculate the lunar calendar <https://www.informatik.uni-leipzig.de/~duc/amlich/calrules.html>
 */

namespace NukeViet\Calendar;

class Lunar
{
    private int $day;
    private int $month;
    private int $year;
    private int $timezone;

    private $CAN = ['Giáp', 'Ất', 'B<PERSON>h', 'Đinh', 'Mậu', 'Kỷ', 'Canh', 'Tân', 'Nhâm', 'Quý'];
    private $CHI = ['Tý', 'Sửu', '<PERSON>ần', '<PERSON><PERSON>', 'Thìn', 'Tỵ', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ợ<PERSON>'];
    private $GIO_HD = ['110100101100', '001101001011', '110011010010', '101100110100', '001011001101', '010010110011'];
    private $TIETKHI = [
        'Xuân phân', 'Thanh minh', 'Cốc vũ', 'Lập hạ', 'Tiểu mãn', 'Mang chủng',
        'Hạ chí', 'Tiểu thử', 'Đại thử', 'Lập thu', 'Xử thử', 'Bạch lộ',
        'Thu phân', 'Hàn lộ', 'Sương giáng', 'Lập đông', 'Tiểu tuyết', 'Đại tuyết',
        'Đông chí', 'Tiểu hàn', 'Đại hàn', 'Lập xuân', 'Vũ Thủy', 'Kinh trập'
    ];

    private int $lunarDay;
    private int $lunarMonth;
    private int $lunarYear;
    private int $lunarLeap;
    private int $juliusDay;

    /**
     * @param int $day
     * @param int $month
     * @param int $year
     * @param int $timezone
     * @param bool $auto tự động tính âm lịch
     */
    public function __construct(?int $day = null, ?int $month = null, ?int $year = null, ?int $timezone = null, bool $auto = true)
    {
        !$day && $day = date('j');
        !$month && $month = date('n');
        !$year && $year = date('Y');
        if (is_null($timezone)) {
            $timezone = 7;
        }

        $this->day = $day;
        $this->month = $month;
        $this->year = $year;
        $this->timezone = $timezone;

        $this->calculateLunar($auto);
    }

    /**
     * @param int $day
     * @param int $month
     * @param int $year
     * @return \NukeViet\Calendar\Lunar
     */
    public function setGregoriusDay(?int $day, ?int $month, ?int $year)
    {
        $this->day = $day;
        $this->month = $month;
        $this->year = $year;
        return $this->calculateLunar();
    }

    /**
     * @param int $timezone
     * @return \NukeViet\Calendar\Lunar
     */
    public function setTimezone(int $timezone)
    {
        $this->timezone = $timezone;
        return $this->calculateLunar();
    }

    /**
     * Trong tính toán thiên văn người ta lấy ngày 1/1/4713 trước công nguyên của lịch Julius
     * (tức ngày 24/11/4714 trước CN theo lịch Gregorius) làm điểm gốc.
     * Số ngày tính từ điểm gốc này gọi là số ngày Julius (Julian day number) của một thời điểm.
     * Ví dụ, số ngày Julius của 1/1/2000 là 2451545.
     */
    private function _getJuliusDay()
    {
        $a = floor((14 - $this->month) / 12);
        $y = $this->year + 4800 - $a;
        $m = $this->month + 12 * $a - 3;
        $jd = $this->day + floor((153 * $m + 2) / 5) + (365 * $y) + floor($y / 4) - floor($y / 100) + floor($y / 400) - 32045;

        if ($jd < 2299161) {
            $jd = $this->day + floor((153 * $m + 2) / 5) + 365 * $y + floor($y / 4) - 32083;
        }

        return $jd;
    }

    /**
     * Tính ngày Sóc: Ngày đầu tiên của tháng âm lịch là ngày chứa điểm Sóc
     * Sóc là thời điểm hội diện, đó là khi trái đất, mặt trăng và mặt trời nằm trên một đường thẳng
     * và mặt trăng nằm giữa trái đất và mặt trời. (Như thế góc giữa mặt trăng và mặt trời bằng 0 độ).
     * Gọi là 'hội diện' vì mặt trăng và mặt trời ở cùng một hướng đối với trái đất. Chu kỳ của điểm Sóc là khoảng 29,5 ngày.
     * Ngày chứa điểm Sóc được gọi là ngày Sóc, và đó là ngày bắt đầu tháng âm lịch.
     *
     * Thuật toán sau tính ngày Sóc thứ k kể từ điểm Sóc ngày 1/1/1900. Kết quả trả về là số ngày Julius của ngày Sóc cần tìm.
     */
    private function getNewMoonDay($k)
    {
        $dr = pi() / 180;
        $T = $k / 1236.85; // Time in Julian centuries from 1900 January 0.5
        $T2 = $T * $T;
        $T3 = $T2 * $T;
        $Jd1 = 2415020.75933 + 29.53058868 * $k + 0.0001178 * $T2 - 0.000000155 * $T3;
        $Jd1 = $Jd1 + 0.00033 * sin((166.56 + 132.87 * $T - 0.009173 * $T2) * $dr); // Mean new moon
        $M = 359.2242 + 29.10535608 * $k - 0.0000333 * $T2 - 0.00000347 * $T3; // Sun's mean anomaly
        $Mpr = 306.0253 + 385.81691806 * $k + 0.0107306 * $T2 + 0.00001236 * $T3; // Moon's mean anomaly
        $F = 21.2964 + 390.67050646 * $k - 0.0016528 * $T2 - 0.00000239 * $T3; // Moon's argument of latitude
        $C1 = (0.1734 - 0.000393 * $T) * sin($M * $dr) + 0.0021 * sin(2 * $dr * $M);
        $C1 = $C1 - 0.4068 * sin($Mpr * $dr) + 0.0161 * sin(2 * $dr * $Mpr);
        $C1 = $C1 - 0.0004 * sin(3 * $dr * $Mpr);
        $C1 = $C1 + 0.0104 * sin(2 * $dr * $F) - 0.0051 * sin($dr * ($M + $Mpr));
        $C1 = $C1 - 0.0074 * sin($dr * ($M - $Mpr)) + 0.0004 * sin($dr * (2 * $F + $M));
        $C1 = $C1 - 0.0004 * sin($dr * (2 * $F - $M)) - 0.0006 * sin($dr * (2 * $F + $Mpr));
        $C1 = $C1 + 0.0010 * sin($dr * (2 * $F - $Mpr)) + 0.0005 * sin($dr * (2 * $Mpr + $M));
        if ($T < -11) {
            $deltat = 0.001 + 0.000839 * $T + 0.0002261 * $T2 - 0.00000845 * $T3 - 0.000000081 * $T * $T3;
        } else {
            $deltat = -0.000278 + 0.000265 * $T + 0.000262 * $T2;
        }
        $JdNew = $Jd1 + $C1 - $deltat;
        return floor($JdNew + 0.5 + ($this->timezone / 24));
    }

    /**
     * Tính tọa độ mặt trời
     *
     * @return number
     */
    private function getSunLongitude($jdn = null)
    {
        $jdn == null && $jdn = $this->juliusDay;
        $PI = pi();
        $T = ($jdn - 2451545.5 - $this->timezone / 24) / 36525; // Time in Julian centuries from 2000-01-01 12:00:00 GMT
        $T2 = $T * $T;
        $dr = $PI / 180; // degree to radian
        $M = 357.52910 + 35999.05030 * $T - 0.0001559 * $T2 - 0.00000048 * $T * $T2; // mean anomaly, degree
        $L0 = 280.46645 + 36000.76983 * $T + 0.0003032 * $T2; // mean longitude, degree
        $DL = (1.914600 - 0.004817 * $T - 0.000014 * $T2) * sin($dr * $M);
        $DL = $DL + (0.019993 - 0.000101 * $T) * sin($dr * 2 * $M) + 0.000290 * sin($dr * 3 * $M);
        $L = $L0 + $DL; // true longitude, degree
        $L = $L * $dr;
        $L = $L - $PI * 2 * floor($L / ($PI * 2)); // Normalize to (0, 2*PI)
        return floor($L / $PI * 6);
    }

    /**
     * @return number
     */
    private function getLunarMonth11($year = null)
    {
        $year == null && $year = $this->year;
        $off = (new Lunar(31, 12, $year, $this->timezone, false))->getJuliusDay() - 2415021;
        $k = floor($off / 29.530588853);
        $nm = $this->getNewMoonDay($k);
        $sunLong = $this->getSunLongitude($nm); // sun longitude at local midnight
        if ($sunLong >= 9) {
            $nm = $this->getNewMoonDay($k - 1);
        }
        return $nm;
    }

    /**
     * Xác định tháng nhuận
     * Nếu giữa hai tháng 11 âm lịch (tức tháng có chứa Đông chí) có 13 tháng âm lịch thì năm âm lịch đó có tháng nhuận.
     *
     *
     * @param int $a11
     * @return number
     */
    private function getLeapMonthOffset($a11)
    {
        $k = floor(($a11 - 2415021.076998695) / 29.530588853 + 0.5);
        $last = 0;
        $i = 1; // We start with the month following lunar month 11
        $arc = $this->getSunLongitude($this->getNewMoonDay($k + $i));
        do {
            $last = $arc;
            $i++;
            $arc = $this->getSunLongitude($this->getNewMoonDay($k + $i));
        } while ($arc != $last && $i < 14);
        return $i - 1;
    }

    /**
     * Trước hết ta xem ngày monthStart bắt đầu tháng âm lịch chứa ngày này là ngày nào
     * Sau đó, ta tìm các ngày $a11 và $b11 là ngày bắt đầu các tháng 11 âm lịch trước và sau ngày đang xem xét
     *
     * @param bool $auto
     * @return \NukeViet\Calendar\Lunar
     */
    private function calculateLunar(bool $auto = true)
    {
        $dayNumber = $this->_getJuliusDay();
        $this->juliusDay = $dayNumber;
        if (!$auto) {
            return $this;
        }

        $k = floor(($dayNumber - 2415021.076998695) / 29.530588853);
        $monthStart = $this->getNewMoonDay($k + 1);

        if ($monthStart > $dayNumber) {
            $monthStart = $this->getNewMoonDay($k);
        }

        $a11 = $this->getLunarMonth11();
        $b11 = $a11;

        if ($a11 >= $monthStart) {
            $lunarYear = $this->year;
            $a11 = $this->getLunarMonth11($this->year - 1);
        } else {
            $lunarYear = $this->year + 1;
            $b11 = $this->getLunarMonth11($this->year + 1);
        }

        $lunarDay = $dayNumber - $monthStart + 1;
        $diff = floor(($monthStart - $a11) / 29);
        $lunarLeap = 0;
        $lunarMonth = $diff + 11;

        if ($b11 - $a11 > 365) {
            $leapMonthDiff = $this->getLeapMonthOffset($a11);
            if ($diff >= $leapMonthDiff) {
                $lunarMonth = $diff + 10;
                if ($diff == $leapMonthDiff) {
                    $lunarLeap = 1;
                }
            }
        }
        if ($lunarMonth > 12) {
            $lunarMonth = $lunarMonth - 12;
        }
        if ($lunarMonth >= 11 and $diff < 4) {
            $lunarYear -= 1;
        }

        $this->lunarDay = $lunarDay;
        $this->lunarMonth = $lunarMonth;
        $this->lunarYear = $lunarYear;
        $this->lunarLeap = $lunarLeap;

        return $this;
    }

    /**
     * Lấy ngày Julius: Số ngày so với điểm gốc 24/11/4714 trước CN theo lịch Gregorius
     *
     * @return number
     */
    public function getJuliusDay()
    {
        return $this->juliusDay;
    }

    /**
     * Lấy ngày âm lịch
     *
     * @return number
     */
    public function getDay()
    {
        return $this->lunarDay;
    }

    /**
     * Lấy tháng âm lịch
     *
     * @return number
     */
    public function getMonth()
    {
        return $this->lunarMonth;
    }

    /**
     * Lấy năm âm lịch
     *
     * @return number
     */
    public function getYear()
    {
        return $this->lunarYear;
    }

    /**
     * Có phải là tháng nhuận hay không. 0 là không phải >0 là nhuận
     *
     * @return number
     */
    public function getLeap()
    {
        return $this->lunarLeap;
    }

    /**
     * Lấy thứ. 0 tương ứng chủ nhật 6 tương ứng thứ 7
     * @return number
     */
    public function getDayOfWeek()
    {
        return ($this->juliusDay + 1) % 7;
    }

    /**
     * Can chi của năm âm lịch
     *
     * @return string[]
     */
    public function getCanChiYear()
    {
        return [
            $this->CAN[($this->lunarYear + 6) % 10],
            $this->CHI[($this->lunarYear + 8) % 12]
        ];
    }

    /**
     * Can chi của tháng âm lịch
     *
     * @return string[]
     */
    public function getCanChiMonth()
    {
        return [
            $this->CAN[($this->lunarYear * 12 + $this->lunarMonth + 3) % 10],
            $this->CHI[($this->lunarMonth + 1) % 12],
            $this->lunarLeap
        ];
    }

    /**
     * Can chi của ngày âm lịch
     *
     * @return string[]
     */
    public function getCanChiDay()
    {
        return [
            $this->CAN[($this->juliusDay + 9) % 10],
            $this->CHI[($this->juliusDay + 1) % 12]
        ];
    }

    /**
     * Can chi của giờ đầu ngày âm lịch
     *
     * @return string[]
     */
    public function getCanChiHour()
    {
        return [
            $this->CAN[($this->juliusDay - 1) * 2 % 10],
            $this->CHI[0]
        ];
    }

    /**
     * Ngày tháng năm theo can chi
     *
     * @return string[][]
     */
    public function getCanChi()
    {
        return [
            $this->getCanChiDay(),
            $this->getCanChiMonth(),
            $this->getCanChiYear(),
            $this->getCanChiHour()
        ];
    }

    /**
     * Lấy tiết khí của ngày
     *
     * @return string
     */
    public function getTietKhi()
    {
        return $this->TIETKHI[intval(floor(($this->getSunLongitude($this->juliusDay + 1) * (180 / pi()) / 15)) % 24)];
    }

    /**
     * Giờ hoàng đạo trong ngày
     *
     * @return number[][]|string[][]
     */
    public function getHoangDao()
    {
        $chiOfDay = ($this->juliusDay + 1) % 12;
        $gioHD = $this->GIO_HD[$chiOfDay % 6];
        $ret = [];
        for ($i = 0; $i < 12; $i++) {
            if ($gioHD[$i] == '1') {
                $ret[] = [
                    $this->CHI[$i],
                    (($i * 2 + 23) % 24),
                    (($i * 2 + 1) % 24)
                ];
            }
        }
        return $ret;
    }
}
