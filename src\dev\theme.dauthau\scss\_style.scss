:root {
    --nv-primary: #{$brand-primary};
    --nv-success: #{$brand-success};
    --nv-white: #fff;

    //--cr-md-header-font-size: #{$h5-font-size};
    //--cr-cap-header-font-size: #{$h5-font-size};
    --cr-danger: #{$brand-danger};
    //--cr-border-radius: #{$border-radius};
    //--cr-input-border-radius: #{$border-radius};
    //--cr-btn-border-radius: #{$border-radius};
    --cr-alert-zindex: #{$zindex-alert};
    --cr-alert-zindex-backdrop: #{$zindex-alert-backdrop};

    // Toast core
    @each $key, $value in $toast-levels {
        --cr-toast-#{$key}-bg: #{$value};
        --cr-toast-#{$key}-color: #{nv-color-contrast($value)};
    }

    // Button core
    @each $key, $value in $core-btn-colors {
        --cr-btn-#{$key}-color: #{nv-color-contrast($value)};
        --cr-btn-#{$key}-bg: #{$value};
        --cr-btn-#{$key}-border-color: #{map-get($core-btn-border-colors, $key)};
        --cr-btn-#{$key}-hover-color: #{nv-color-contrast(nv-shade-color($value, 15%))};
        --cr-btn-#{$key}-hover-bg: #{nv-shade-color($value, 15%)};
        --cr-btn-#{$key}-hover-border-color: #{nv-shade-color(map-get($core-btn-border-colors, $key), 20%)};
        --cr-btn-#{$key}-active-color: #{nv-color-contrast(nv-shade-color($value, 20%))};
        --cr-btn-#{$key}-active-bg: #{nv-shade-color($value, 20%)};
        --cr-btn-#{$key}-active-border-color: #{nv-shade-color(map-get($core-btn-border-colors, $key), 25%)};
        --cr-btn-#{$key}-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
        --cr-btn-#{$key}-disabled-color: #{nv-color-contrast($value)};
        --cr-btn-#{$key}-disabled-bg: #{$value};
        --cr-btn-#{$key}-disabled-border-color: #{map-get($core-btn-border-colors, $key)};
    }
}

/* Global */
html {
    font-size: 16px;
}

html,
body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}
a {
    cursor: pointer;
}
ul,
ol {
    list-style: none;
}

iframe {
    border: 0;
}
figure {
    position: relative;
    margin: 5px 0 10px 0;
    text-align: center
}

figure.image.image-left,
img.image-left {
    margin-right: 15px;
    float: left!important;
}

figure.image.image-right,
img.image-right {
    margin-left: 15px;
    float: right!important;
}

.image-center figure.image,
.image-center {
    text-align: center;
    float: none!important;
}

div.image-center,
p.image-center {
    margin-bottom: 10px;
}

figure.article {
    background-color: #939393;
}
figure.left {
    float: left;
    margin: 5px 10px 10px 0;
}
figure.right{
    float: right;
    margin: 5px 0 10px 10px;
}
figure.center{
    float: none;
    background: transparent;
    margin: 0 auto 10px;
    padding-top: 0;
    text-align:center
}
figure.noncaption{
    background: transparent;
    padding: 0;
}
figcaption{
    text-align: center;
    margin-top: 5px;
    font-weight: 700;
}
figure figcaption {
    font-size: 12px;
    font-weight: normal;
}
figure.article figcaption {
    color: #fff;
}
figure.article.center figcaption{
    color: inherit
}
figure.avatar{
    margin-top:0 !important
}
figure.avatar figcaption{
    position: absolute;
    bottom: 12px;
    left: 3px;
    width:calc(100% - 6px);
    background-color: #357ebd;
    color:#fff;
    font-size:11px
}

.has-error {
    .cke {
        border-color: #a94442 !important;
    }
}

.nv-docviewer {
    margin-bottom: 8px;
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
    margin: 0;
    padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: $font-weight-bold;
}

img[src=""] {
    display: none;
}

.affix {
    top: $padding-base-horizontal;
    max-height: 90%;
    overflow-y: auto;
    z-index: $zindex-navbar-fixed !important;
    animation: affixFadeIn 0.44s  ease-in;
    -webkit-animation: affixFadeIn 0.44s;
}

@keyframes affixFadeIn {
  from {opacity: 0;}
  to {opacity: 1;}
}

@-webkit-keyframes affixFadeIn {
  from {opacity: 0;}
  to {opacity: 1;}
}

.wraper {
    position: relative;
    margin: 0 auto;
    width: $container-non-sponsive;
}

.display-flex {
    display: flex;
    align-items: center;
}

.flex-justify-center {
    display: flex;
    justify-content: center;
}

.flex-align-items-end {
    display: flex;
    align-items: end;
}

.flex-column-stretch {
    display: flex;
    flex-direction: column;
    align-content: stretch;

    .last {
        margin-top: auto;
    }
}

.pos-relative {
    position: relative;
}

.body-bg {
    position: relative;
    min-width: 100%;
    min-height: 100%;
    background-color: $component-active-color;
}

.center-search-bl {
    position: relative;
    min-width: 100%;
    background-color: rgba(245, 245, 245, 0.5);
    overflow: initial;
    background-blend-mode: overlay;
    background-size: 480px;
    background-image: url(../images/bg-page.webp);

    h2 {
        font-size: ceil(($font-size-base * 1.6));
        margin: 0;
    }
}

.data-pc {
    display: block;
}

.data-mobile {
    display: none;
}

.dauthau-navbar {
    background-color: $body-bg;

    .navbar {
        border: 0;
    }

    .navbar-header {
        float: none;
        display: flex;
        align-items: center;
        height: $navbar-header-height;
        background-color: $body-bg;

        &:before,
        &:after {
            display: none;
        }

        .navbar-header-right-wrap {
            //flex-grow: 1;

            .navbar-header-right {
                display: flex;
                align-items: center;
                justify-content: flex-end;
            }
        }
    }

    .navbar-brand {
        padding: 0;
        height: auto;

        &.navbar-brand-other:not(.active) {
            filter: grayscale(100%);
            opacity: 0.5;
        }

        &.navbar-brand-self.gray-filter {
            filter: grayscale(100%);
            opacity: 0.5;
        }
    }

    .navbar-toggle {
        border-color: $navbar-default-toggle-icon-bar-bg !important;
        transition: $transition;

        .icon-bar {
            background-color: $navbar-default-toggle-icon-bar-bg !important;
        }
    }
    .navbar-toggle:hover,
    .navbar-toggle:focus {
        border-color: $navbar-default-toggle-border-color !important;
        background-color: $navbar-default-link-hover-bg !important;

        .icon-bar {
            background-color: $navbar-default-toggle-icon-bar-bg !important;
        }
    }

    .logo {
        display: block;
        height: $navbar-header-height;
        width: $logo-width-base + 19px;
        overflow: hidden;
        background-repeat: no-repeat;
        background-size: 85%;
        background-position: 0 center;
        background-image: $svg-logo;
        &.logo-en {
            background-image: $svg-logo-en;
        }
    }

    .navbar-brand-other {
        .logo {
            background-image: $svg-logo-dauthaunet;
            width: $logo-width-base;
            &.logo-en {
                background-image: $svg-logo-dauthaunet-en;
                width: $logo-width-base;
            }
        }
    }
    .right-content {
        display: flex;
        align-items: center;
        position: relative;
    }
}

@font-face {
  font-family: '#{$icomoon-font-family}';
  src:
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.woff2') format('woff2'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.woff') format('woff'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.ttf') format('truetype'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.svg') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
    font-family: 'Open Sans';
    src: url('#{$icomoon-font-path}/OpenSans-Regular.woff2') format('woff2'),
        url('#{$icomoon-font-path}/OpenSans-Regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Open Sans';
    src: url('#{$icomoon-font-path}/OpenSans-Bold.woff2') format('woff2'),
        url('#{$icomoon-font-path}/OpenSans-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

[class^="icon-"],
[class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: "#{$icomoon-font-family}" !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Enable Ligatures ================ */
    letter-spacing: 0;
    -webkit-font-feature-settings: "liga";
    -moz-font-feature-settings: "liga=1";
    -moz-font-feature-settings: "liga";
    -ms-font-feature-settings: "liga" 1;
    font-feature-settings: "liga";
    -webkit-font-variant-ligatures: discretionary-ligatures;
    font-variant-ligatures: discretionary-ligatures;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

@mixin icon-variant($icon, $code) {
    .icon-#{$icon}:before {
        content: unquote('"\\#{$code}"');
    }
}

@each $icon, $code in $icons {
    @include icon-variant($icon, $code);
}

@include icon-variant(random, map-get($icons, responsive));
@include icon-variant(mobile, map-get($icons, smartphone));

@font-face {
    font-family: '#{$icomoon-hstdt-font-family}';
    src:  url('#{$icomoon-font-path}/#{$icomoon-hstdt-font-family}.eot?9rq6a8');
    src:  url('#{$icomoon-font-path}/#{$icomoon-hstdt-font-family}.eot?9rq6a8#iefix') format('embedded-opentype'),
      url('#{$icomoon-font-path}/#{$icomoon-hstdt-font-family}.ttf?9rq6a8') format('truetype'),
      url('#{$icomoon-font-path}/#{$icomoon-hstdt-font-family}.woff?9rq6a8') format('woff'),
      url('#{$icomoon-font-path}/#{$icomoon-hstdt-font-family}.svg?9rq6a8##{$icomoon-hstdt-font-family}') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

[class^="hstdt-icon-"], [class*=" hstdt-icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: '#{$icomoon-hstdt-font-family}' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Enable Ligatures ================ */
    letter-spacing: 0;
    -webkit-font-feature-settings: "liga";
    -moz-font-feature-settings: "liga=1";
    -moz-font-feature-settings: "liga";
    -ms-font-feature-settings: "liga" 1;
    font-feature-settings: "liga";
    -webkit-font-variant-ligatures: discretionary-ligatures;
    font-variant-ligatures: discretionary-ligatures;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

@mixin hstdt-icon-variant($icon, $code) {
    .hstdt-icon-#{$icon}:before {
        content: unquote('"\\#{$code}"');
    }
}

@each $icon, $code in $icons {
    @include hstdt-icon-variant($icon, $code);
}

.icon-lg {
    font-size: 1.33333333em;
    line-height: 0.75em;
    vertical-align: -25%;
}

.icon-xl {
    font-size: $font-size-base * 2;
}

.icon-horizon {
    width: 14px;
}

.icon-lg.icon-horizon {
    width: 22px;
}

.icon_new {
    background: url("../images/icons/new.gif") no-repeat;
    display: inline-block !important;
    width: 33px;
    height: 15px;
}

.icon_new_small {
    background: url("../images/squared-blue.png") no-repeat 0 8px;
}

.icon_list {
    background: url("../images/arrow_left_orange.png") no-repeat 0 8px;
}

[class^="ic-"],
[class*=" ic-"] {
    position: relative;
    padding-left: 20px !important;

    &:before {
        position: absolute;
        margin-left: -20px;
    }
}
.ic-phone:before {
    content: loadcontent(old-phone);
    font-family: $icomoon-font-family;
}
.ic-envelope:before {
    content: loadcontent(envelope);
    font-family: $icomoon-font-family;
}
.ic-yahoo {
    content: loadcontent(yahoo);
    font-family: $icomoon-font-family;
}
.ic-skype {
    content: " \f17e ";
    font-family: FontAwesome;
}
.ic-viber {
    content: loadcontent(viber);
    font-family: $icomoon-font-family;
}
.ic-icq {
    content: loadcontent(icq);
    font-family: $icomoon-font-family;
}
.ic-whatsapp {
    content: " \f232 ";
    font-family: FontAwesome;
}

/* Account Level */
[class^="lev-"],
[class*=" lev-"] {
    display: inline-block;
}

[class^="lev-"]:before,
[class*=" lev-"]:before {
    font-family: $icomoon-font-family;
    font-size: inherit;
    margin-right: 5px;
}

.lev-1:before {
    content: loadcontent(medal-first);
}

.lev-2:before {
    content: loadcontent(medal-second);
}

.lev-3:before {
    content: loadcontent(medal-third);
}

.lev-user:before {
    content: loadcontent(user);
}

blockquote.list ul {
    list-style: none;
    margin: 0;
    margin-bottom: $margin-md;
    padding: 0;

    li:before {
        content: loadcontent(check);
        font-family: $icomoon-font-family;
        margin-right: 5px;
    }
}

/* Fonts Color */
.text-black {
    color: $gray-base !important;
}

.text-white {
    color: $component-active-color !important;
}

/* Fonts Weight */
.text-normal {
    font-weight: $font-weight-normal;
}

.text-bold {
    font-weight: $font-weight-bold;
}

.visible-xs-block,
.visible-xs-inline,
.visible-xs-inline-block,
.visible-ss-block,
.visible-ss-inline,
.visible-ss-inline-block {
    display: none;
}

.hidden-ss-block {
    display: block !important;
}

.hidden-ss-inline {
    display: inline !important;
}

.hidden-ss-inline-block {
    display: inline-block !important;
}

.margin {
    margin: $margin-base;
}

.margin-right {
    margin-right: $margin-base;
}

.margin-left {
    margin-left: $margin-base;
}

.margin-top {
    margin-top: $margin-base;
}

.margin-bottom {
    margin-bottom: $margin-base;
}

.margin-sm {
    margin: $margin-sm;
}

.margin-right-sm {
    margin-right: $margin-sm;
}

.margin-left-sm {
    margin-left: $margin-sm;
}

.margin-top-sm {
    margin-top: $margin-sm;
}

.margin-bottom-sm {
    margin-bottom: $margin-sm;
}

.margin-lg {
    margin: $margin-lg;
}

.margin-right-lg {
    margin-right: $margin-lg;
}

.margin-left-lg {
    margin-left: $margin-lg;
}

.margin-top-lg {
    margin-top: $margin-lg;
}

.margin-bottom-lg {
    margin-bottom: $margin-lg;
}

.padding-top {
    padding-top: $padding-base;
}

.padding-left {
    padding-left: $padding-base;
}

.padding-right {
    padding-right: $padding-base;
}

.padding-bottom {
    padding-bottom: $padding-base;
}

.padding-lr {
    padding-left: ($grid-gutter-width / 2);
    padding-right: ($grid-gutter-width / 2);
}

.margin-lr {
    margin-left: ($grid-gutter-width / 2);
    margin-right: ($grid-gutter-width / 2);
}

.pt-0 {
    padding-top: 0 !important;
}

.mt-1 {
    margin-top: $margin-sm !important;
}

.mb-1 {
    margin-bottom: $margin-sm !important;
}

.ml-1 {
    margin-left: $margin-sm !important;
}

.mr-1 {
    margin-right: $margin-sm !important;
}

.boder-top {
    border-top-width: 1px;
    border-top-style: solid;
    border-top-color: $gray-300;
}

.boder-bottom {
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: $gray-300;
}

.border-left {
    border-left-width: 1px;
    border-left-style: solid;
    border-left-color: $gray-300;
}

.border-right {
    border-right-width: 1px;
    border-right-style: solid;
    border-right-color: $gray-300;
}

.column-margin-left {
    margin-left: $margin-base !important;
}

.bg-gainsboro {
    background-color: $gray-300;
}

.bg-lavender {
    background-color: $gray-400;
}

.bg-loading {
    display: inline-block;
    min-width: 20px;
    min-height: 20px;
    background-image: $svg-loading;
    background-position: center center;
    background-repeat: no-repeat;
    vertical-align: middle;
}

.overflow-hidden {
    position: relative;
    overflow: hidden;
}

/*Load_Bar*/
.load-bar {
    display: inline-block;
    vertical-align: middle;
    width: 33px;
    height: 8px;
    background: transparent url(../../../assets/images/load_bar.gif);
    margin: auto 10px;
}

/*Center DIV*/
.centered {
    text-align: center;
    font-size: 0;
}

.centered > div {
    float: none;
    display: inline-block;
    text-align: left;
    font-size: $font-size-base;
}

.nv-info {
    border-width: 1px;
    border-style: solid;
    border-color: $gray-300;
    border-radius: $border-radius;
    -webkit-border-radius: $border-radius;
    background-color: $gray-100;
    color: $gray-700;
    padding: $padding-base;
}

.nv-info.error {
    background-color: #efd7d7 !important;
    border-color: #dca8a6 !important;
}

.nv-info.success {
    background-color: #edf4fa !important;
    border-color: #82b2dc !important;
}

.nv-info.info {
    background-color: #d9edf7;
    border-color: #bce8f1;
    color: #31708f;
}

.nv-info:before {
    display: inline-block;
    font-family: FontAwesome;
    font-size: 20px;
    line-height: 1;
    vertical-align: middle;
    margin-right: 5px;
}

.nv-info.error:before {
    content: " \f057 ";
    color: #de495b;
}

.nv-info.success:before {
    content: " \f13a ";
    color: #68d044;
}

.nv-info.info:before {
    content: "\f05a";
    color: #31708f;
}

.clear {
    clear: both;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.pointer {
    cursor: pointer;
}

.middle {
    vertical-align: middle;
}

.align-bottom {
    vertical-align: bottom;
}

.align-top {
    vertical-align: top;
}

.radius-top-left {
    -webkit-border-top-left-radius: 5px;
    border-top-left-radius: 5px;
}

.radius-top-right {
    -webkit-border-top-right-radius: 5px;
    border-top-right-radius: 5px;
}

.radius-bottom-left {
    -webkit-border-bottom-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

.radius-bottom-right {
    -webkit-border-bottom-right-radius: 5px;
    border-bottom-right-radius: 5px;
}

.radius {
    -webkit-border-radius: 5px;
    border-radius: 5px;
}

.bg-gradient {
    background-image: $svg-bg-gradient;
    background-image: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.8) 0%,
        rgba(255, 255, 255, 0.3) 100%
    );
}

.box-shadow {
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);
    -webkit-box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);
}

.fix-box {
    position: inherit !important;
    margin-left: 0 !important;
}

input[type="text"].required,
input[type="password"].required,
input[type="email"].required,
input[type="number"].required,
input[type="search"].required,
input[type="tel"].required,
input[type="time"].required,
input[type="url"].required,
input[type="url"].required,
textarea.required,
select.required,
label.required {
    background-image: url(../images/icons/required.png);
    background-position: right center;
    background-repeat: no-repeat;
}

textarea.required {
    background-position: right 10px;
}

select.required {
    background-position: calc(100% - 15px) 10px;
}

label.required {
    padding-right: 20px;
}

label.radio-box,
label.check-box {
    font-weight: normal;
    margin-right: 20px;
    cursor: pointer;
}

div.radio-box,
div.check-box {
    background-color: #fff;
    border-width: 1px;
    border-style: solid;
    border-color: #ccc;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    padding: 10px;
}

.has-error div.radio-box,
.has-error div.check-box {
    border-color: #de495b;
}

div.radio-box label,
div.check-box label {
    margin-bottom: 0;
}

div.radio-box label [type="radio"],
div.check-box label [type="radio"] {
    margin-top: -2px;
}

.display-inline-block {
    display: inline-block;
}

.display-table {
    display: table;
}

.display-table > * {
    display: table-row;
}

.display-table > * > * {
    display: table-cell;
    padding: 5px;
}

/*tooltip*/
.tooltip-inner {
    height: auto;
    overflow: auto;
}

/* Fixed Jquery UI Style */
.ui-widget {
    font-size: 12px !important;
    font-weight: 400 !important;
    line-height: 18px !important;
}

.ui-datepicker {
    width: 18em !important;
}

.ui-datepicker select.ui-datepicker-month {
    width: 59% !important;
    font-weight: normal !important;
}

.ui-datepicker select.ui-datepicker-year {
    width: 39% !important;
    font-weight: normal !important;
}

a.dimgray {
    color: #707070;
}

a.dimgray:hover {
    color: #0e2132;
}

a.black {
    color: #333;
}

a.black:hover {
    color: #0e2132;
}

.title {
    font-size: $font-size-title;
}

.hometext {
    font-weight: $font-weight-bold;
}

.bodytext {
    word-break: keep-all;
    max-width: 100%;
    overflow-x: hidden;
    overflow-y: visible;

    p {
        margin-bottom: 15px !important;
    }

    ol {
        list-style-type: decimal !important;
    }

    ul {
        list-style-type: disc !important;
        padding-left: 40px !important;
        margin-bottom: 10px !important;
    }

    li {
        margin-bottom: 0 !important;
    }

    div.alert {
        margin-bottom: 7px;
        margin-top: 4px;
    }

    a,
    a:link,
    a:active,
    a:visited {
        color: $link-color !important;
    }
}

.inline-related-articles {
    li {
        margin-bottom: 0 !important;
    }
}

.m-bottom {
    margin-bottom: 10px !important;
}

.rel {
    position: relative;
}

.fa-pointer {
    cursor: pointer;
}

.fa-horizon {
    width: 14px;
}

.fa-lg.fa-horizon {
    width: 22px;
}

h3.sm {
    font-size: 16px;
}

span.keyword {
    background-color: yellow;
}

.nv-fullbg {
    min-height: 100%;
    background-color: #fff;
}

/* Head-Nav */
.header-nav {
    background-color: transparent;
    margin-left: $margin-lg;

    a {
        color: $text-color;

        &.button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: $button-user-width;
            height: $button-user-width;
            color: $navbar-default-toggle-icon-bar-bg;
            line-height: 1;
            background-color: #fff;
            border: 1px $navbar-default-toggle-icon-bar-bg solid;
            border-radius: $border-radius-base;
            transition: $transition;
        }
    }

    a.button:hover,
    a.active {
        border-color: $navbar-default-toggle-border-color;
        background-color: $navbar-default-link-hover-bg !important;
        color: $navbar-default-toggle-icon-bar-bg !important;
    }

    a.button.user {
        border-width: 0;
        border-radius: ($button-user-width / 2) !important;
        background-color: $navbar-default-toggle-icon-bar-bg !important;
        background-size: contain;

        &:hover,
        &.active {
            background-color: $component-active-bg !important;
        }
    }
}

/* Blocks social-icons,contactDefault and personalArea */

.social-icons {
    line-height: 51px;
    float: right;
    margin: 0 10px;
}

.contactDefault {
    position: absolute;
    top: 0;
    left: 0;
    line-height: 51px;
    margin: 0 10px;
}

.social-icons {
    float: left;
    margin: 0 31px;
    z-index: 10000;
    position: relative;
}

/* TIP POPUP and FTIP POPUP */
#tip,
#ftip {
    position: absolute;
    color: $gray-dark !important;
    background-color: $gray-lighter;
    max-width: $tip-max-width;
    box-shadow: $box-shadow-sm;
    z-index: $zindex-navbar;
    display: none;

    h3 {
        font-size: $font-size-base;
        font-weight: $headings-font-weight;
        text-transform: uppercase;
        margin-bottom: $margin-bottom-lg;
    }

    .bg {
        padding: $padding-large-vertical $padding-large-horizontal;
        border-color: darken($gray-lighter, 7%);
        border-width: 1px;
        border-style: solid;
    }

    .tip-footer,
    .tip-footer {
        border-top: 1px darken($gray-lighter, 7%) solid;
        padding-top: $padding-base-horizontal;
    }
}

#tip {
    top: $button-user-width + $padding-base-vertical;
    right: 0;
    min-width: $tip-min-width;
    min-height: $tip-min-height;
    margin-right: -$padding-base-horizontal;
    overflow-x: hidden;
    overflow-y: hidden;
    border-bottom-left-radius: $border-radius-base;
    border-bottom-right-radius: $border-radius-base;

    ul {
        margin: 0;
        padding: 0;
    }

    a {
        color: $gray-dark;
    }

    .bg {
        position: relative;
        border-bottom-left-radius: $border-radius-base;
        border-bottom-right-radius: $border-radius-base;
    }

    .tip-footer {
        margin-top: $margin-base;

        > * {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    }
}

#ftip {
    bottom: 45px;
    left: 0;
    min-height: 51px;
    margin-left: 10px;
    border-top-left-radius: $border-radius-base;
    border-top-right-radius: $border-radius-base;
    z-index: 2000000100;

    .bg {
        border-top-left-radius: $border-radius-base;
        border-top-right-radius: $border-radius-base;
    }

    .ftip-footer {
        margin: 10px -15px -15px;
    }
}

.contactList {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
}

.socialList {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: nowrap;

    li {
        display: inline-block;
    }
    li ~ li {
        margin-left: $margin-base;
    }

    li a {
        display: block;
        width: $button-user-sm-width;
        height: $button-user-sm-width;
        line-height: $button-user-sm-width;
        background-color: $component-active-bg;
        color: $component-active-color;
        text-align: center;
        font-size: $font-size-large;
        border-radius: $border-radius-base;
        transition: $transition;
        &[href*="feeds"] {
            background-color: $social-icon-feeds-color;

            &:hover {
                background-color: darken($social-icon-feeds-color, 15%);
            }
        }

        &[href*="google"] {
            background-color: $social-icon-google-color;
        }

        &[href*="facebook"] {
            background-color: $social-icon-facebook-color;

            &:hover {
                background-color: darken($social-icon-facebook-color, 15%);
            }
        }

        &[href*="youtube"] {
            background-color: $social-icon-youtube-color;

            &:hover {
                background-color: darken($social-icon-youtube-color, 15%);
            }
        }

        &[href*="twitter"] {
            background-color: $social-icon-twitter-color;

            &:hover {
                background-color: darken($social-icon-twitter-color, 15%);
            }
        }
    }

    li .fa {
        margin-right: -4px;
        color: $component-active-color !important;
    }
}

.headerSearch {
    // margin-top: $margin-top-lg;
    margin: 9px 0px 7px 0px;
    clear: both;
}

.input-group__search {
    display: flex !important;
    justify-content: flex-end !important;
    padding-right: 30px;
}

.input-group__search input {
    max-width: 350px;
}

.caret-right {
    border-left: $caret-width-base solid;
    border-bottom: $caret-width-base solid transparent;
    border-top: $caret-width-base solid transparent;
}

/* Bootstrap Menu */
#menusite-wrap {
    li > a {
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: darken($component-active-color, 15%);
    }

    .navbar {
        background-color: $body-bg !important;

        .navbar-nav {
            & > li {
                position: relative;
            }
            & > li > a {
                color: $component-active-color !important;
                padding: 15px 25px 15px 25px;
            }

            & > li > a:hover,
            & > li > a:focus,
            & > li.active > a,
            & > li.active > a:hover,
            & > li.open > a {
                background-color: darken($component-active-bg, 5%) !important;
            }

            & > li > a > span > span > .caret {
                border-bottom-color: $component-active-color !important;
            }
            & > li > .icon_caret, & > li > ul > li .icon_caret  {
                position: absolute;
                width: 30px;
                height: 30px;
                top: 10px;
                right: 0px;
                color: #fff;
                padding-left: 10px;
            }
            & > li > ul > li .icon_caret  {
                top: 5px;
                color: #333;
            }
        }
    }

    .collapse {
        background-color: $component-active-bg !important;
    }

    .menu-item {
        display: flex;
        align-items: center;
        justify-content: space-between;

        & > *:last-child:not(:first-child) {
            margin-left: auto;
            padding-left: $padding-xs-horizontal;
        }
    }

    .navbar-nav > li > a > span > span {
        white-space: nowrap;
        text-transform: uppercase;
    }

    .dropdown-menu {
        margin: 0;
        padding: 0;
        min-width: $dropdown-menu-min-width;
        width: auto !important;

        & > li {
            & > a {
                padding: $padding-large-vertical $padding-small-horizontal;
                white-space: normal;
                line-height: 1.42857;
            }
        }
    }

    .dropdown-menu > li:last-child > a:hover,
    .dropdown-menu > li:last-child > a:focus,
    .dropdown-menu > li:last-child.active > a,
    .dropdown-menu > li:last-child.active > a:hover,
    .dropdown-menu > li:last-child.active > a:focus,
    .navbar .navbar-nav .open .dropdown-menu > li:last-child > a:hover,
    .navbar .navbar-nav .open .dropdown-menu > li:last-child > a:focus,
    .navbar .navbar-nav .open .dropdown-menu > li:last-child.active > a,
    .navbar .navbar-nav .open .dropdown-menu > li:last-child.active > a:hover,
    .navbar .navbar-nav .open .dropdown-menu > li:last-child.active > a:focus {
        /*background-color: $dropdown-link-hover-bg;*/
        border-radius: 0 0 $border-radius-base $border-radius-base;
    }

    .dropdown-submenu {
        position: relative;
        transition: $transition;

        & > .dropdown-menu {
            top: 0;
            left: 100%;
            margin-top: -1px;
            margin-left: -1px;
            border-radius: 0 $border-radius-base $border-radius-base $border-radius-base;
        }

        &.pull-left {
            float: none;

            & > .dropdown-menu {
                left: -100%;
                margin-left: 10px;
                border-radius: $border-radius-base 0 $border-radius-base $border-radius-base;
            }
        }
    }

    .dropdown.open {
        transition: height 0.5s ease;
    }

    .dropdown.open > a > span > span > .caret:not(.caret-right) {
        border-top: none;
        border-bottom-width: $caret-width-base;
        border-bottom-style: solid;
    }

    .dropdown.open > a > span > span > .caret:not(.caret-right) {
        border-bottom-color: $dropdown-link-color;
    }

    .switch-logos {
        display: none;
        padding: 4px;

        .header__cl_btn {
            display: flex;
            align-items: center;
            position: relative;
            top: -45px;
            .box__btn {
                display: flex;
                width: 100%;
                flex-direction: column;
                height: 100vh;
            }

            .btn__info, .btn__daugia, .btn__dauthaunet {
                width: 150px;
                height: 160px;
                transform: rotate(-90deg);
                transform-origin: 98px 98px;
                position: relative;
                right: -2px;
            }

            .btn__info {
                background: $svg-logo;
                background-repeat: no-repeat;
                background-size: contain;
            }

            .btn__daugia {
                background: $svg-logo-daugia;
                background-repeat: no-repeat;
                background-size: contain;
            }

            .btn__dauthaunet {
                background: $svg-logo-dauthaunet;
                background-repeat: no-repeat;
                background-size: contain;
            }
        }


        .switch-logo {
            background-color: #fff;
            border-radius: 2px;
            display: block;
            width: 196px;
            height: 44px;
            transform: rotate(-90deg);
            transform-origin: 98px 98px;
            padding: 4px 8px;

            .switch-logo-inner {
                display: block;
                width: 180px;
                height: 36px;
                background-size: 100%;
                background-position: center center;
                background-image: $svg-logo-sm;
                background-repeat: no-repeat;
            }

            &.switch-logo-other {
                transform-origin: 176px 176px;
                background-color: #eee;

                .switch-logo-inner {
                    background-image: $svg-logo-dauthaunet-sm;
                }
            }
        }
    }
}

/*jssor slider loading skin spin css*/
.jssorl-009-spin img {
    animation-name: jssorl-009-spin;
    animation-duration: 1.6s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

@keyframes jssorl-009-spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/*jssor slider bullet skin 061 css*/
.jssorb061 {
    position: absolute;

    .i {
        position: absolute;
        cursor: pointer;

        .b {
            fill: $gray-lighter;
        }

        &:hover .b {
            fill-opacity: 0.5;
        }

        &.idn {
            opacity: 0.3;
        }
    }

    .iav .b {
        fill: #fff;
        fill-opacity: 1;
        stroke: $component-active-bg;
        stroke-width: 2000;
    }
}

/*jssor slider arrow skin 073 css*/
.jssora073 {
    display: block;
    position: absolute;
    cursor: pointer;

    .a {
        fill: $gray-lighter;
        fill-opacity: 0.5;
        stroke: #000;
        stroke-width: 160;
        stroke-miterlimit: 10;
        stroke-opacity: 0.3;
    }

    &:hover {
        opacity: 0.8;
    }

    &.jssora073dn {
        opacity: 0.4;
    }

    &.jssora073ds {
        opacity: 0.3;
        pointer-events: none;
    }
}

/* breadcrumb */
.breadcrumbs-wrap {
    position: relative;
    border-top: 1px $component-active-color solid;
    border-bottom: 1px $breadcrumbs-bg solid;
    padding-top: $breadcrumbs-margin;
    background-color: $breadcrumbs-wrap-bg;

    .display {
        display: block;
        height: $breadcrumbs-height;
        overflow: hidden;
        position: relative;

        &:before {
            content: "";
            position: absolute;
            top: $breadcrumbs-height / 2;
            margin-top: $breadcrumbs-height / -2;
            border-top-width: $breadcrumbs-height / 2;
            border-top-style: solid;
            border-top-color: transparent;
            border-bottom-width: $breadcrumbs-height / 2;
            border-bottom-style: solid;
            border-bottom-color: transparent;
            border-left-width: $breadcrumbs-height / 2;
            border-left-style: solid;
            border-left-color: $brand-danger;
            left: 0;
            height: $breadcrumbs-height;
            width: $breadcrumbs-height / 2;
        }
    }

    .breadcrumbs {
        position: relative;
        list-style: none;
        margin: 0 0 0 $breadcrumbs-margin * 2;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: $breadcrumbs-height;

        li {
            float: left;
            margin: 0 (($breadcrumbs-height / 4) + $breadcrumbs-margin);
        }

        a {
            background-color: $breadcrumbs-bg;
            padding-left: $padding-small-horizontal;
            padding-right: $padding-small-horizontal / 2;
            height: $breadcrumbs-height;
            line-height: $breadcrumbs-height;
            float: left;
            text-decoration: none;
            color: $breadcrumbs-color;
            position: relative;
            font-size: $breadcrumbs-font-size;

            &:hover {
                color: $breadcrumbs-hover-color;
                background-color: $breadcrumbs-hover-bg;

                &:before {
                    border-color: $breadcrumbs-hover-bg $breadcrumbs-hover-bg
                        $breadcrumbs-hover-bg transparent;
                }

                &:after {
                    border-left-color: $breadcrumbs-hover-bg;
                }
            }

            &:before {
                content: "";
                position: absolute;
                top: 50%;
                margin-top: $breadcrumbs-height / -2;
                border-width: ($breadcrumbs-height / 2) 0 ($breadcrumbs-height / 2)
                    ($breadcrumbs-height / 2);
                border-style: solid;
                border-color: $breadcrumbs-bg $breadcrumbs-bg $breadcrumbs-bg transparent;
                left: $breadcrumbs-height / -2;
                height: $breadcrumbs-height;
                width: $breadcrumbs-height / 2;
            }

            &:after {
                content: "";
                position: absolute;
                top: 50%;
                margin-top: $breadcrumbs-height / -2;
                border-top-width: $breadcrumbs-height / 2;
                border-top-style: solid;
                border-top-color: transparent;
                border-bottom-width: $breadcrumbs-height / 2;
                border-bottom-style: solid;
                border-bottom-color: transparent;
                border-left-width: $breadcrumbs-height / 2;
                border-left-style: solid;
                border-left-color: $breadcrumbs-bg;
                right: $breadcrumbs-height / -2;
                height: $breadcrumbs-height;
                width: $breadcrumbs-height / 2;
            }

            &.show-subs-breadcrumbs {
                padding-left: $padding-small-horizontal / 2;
                padding-right: 0;
            }

            > span {
                display: inline-block;
                overflow: hidden;
            }

            .dotsign {
                padding-right: $padding-base-horizontal;

                &:after {
                    content: loadcontent(ellipsis);
                    font-family: "#{$icomoon-font-family}";
                    position: absolute;
                    top: 0;
                    right: 0;
                }
            }
        }

        li:first-child {
            margin-left: $breadcrumbs-height / 2;
        }

        li:last-child {
            a {
                margin-right: $padding-base-horizontal;
            }
        }
    }

    .subs-breadcrumbs {
        margin: 0;
        padding: 0;
        background-color: $dropdown-bg;
        position: absolute;
        left: 0;
        top: $breadcrumbs-height + $breadcrumbs-margin;
        border-bottom-left-radius: $border-radius-base;
        border-bottom-right-radius: $border-radius-base;
        box-shadow: $box-shadow;
        display: none;
        z-index: $zindex-dropdown;

        &.open {
            display: block;
            background: #fff;
        }

        li,
        a {
            display: block;
            float: none;
        }

        li:not(:last-child) a {
            border-bottom-width: 1px;
            border-bottom-style: solid;
            border-bottom-color: $dropdown-border;
        }

        li:last-child,
        li:last-child a {
            border-bottom-left-radius: $border-radius-base;
            border-bottom-right-radius: $border-radius-base;
        }

        a {
            overflow: hidden;
            padding: $padding-small-horizontal;
            color: $dropdown-link-color;

            &:hover {
                color: $dropdown-link-hover-color;
                background-color: $dropdown-link-hover-bg;
            }

            em {
                display: inline-block;
                margin-right: $margin-sm;
            }
        }
    }
}

/* Multi Step bar */
%remain-steps {
    &:before {
        content: counter(stepNum);
        font-family: inherit;
        font-weight: $font-weight-bold;
    }
    &:after {
        background-color: $gray-300;
    }
}

.multi-steps {
    display: table;
    table-layout: fixed;
    width: 100%;
    margin: $margin-xl auto;

    > .item {
        counter-increment: stepNum;
        text-align: center;
        display: table-cell;
        position: relative;
        color: $text-color;
        vertical-align: middle;
        z-index: 1;

        &:before {
            content: loadcontent(tick);
            font-family: $icomoon-font-family;
            display: block;
            margin: 0 auto;
            background-color: $brand-danger;
            color: setcolor($brand-danger);
            width: 40px;
            height: 40px;
            line-height: 36px;
            text-align: center;
            font-weight: bold;
            transition: $transition;
            border: {
                width: 2px;
                style: solid;
                color: $brand-danger;
                radius: 50%;
            }
        }
        &:after {
            content: "";
            height: 2px;
            width: 100%;
            background-color: $brand-danger;
            position: absolute;
            top: 19px;
            left: 50%;
            z-index: -1;
        }
        &:last-child {
            &:after {
                display: none;
            }
        }

        &.is-active {
            @extend %remain-steps;
            &:before {
                background-color: $body-bg;
                color: setcolor($body-bg);
                border-color: $brand-danger;
            }

            ~ .item {
                color: $gray;
                @extend %remain-steps;
                &:before {
                    background-color: $gray-300;
                    color: setcolor($gray-300);
                    border-color: $gray-300;
                }
            }
        }
    }

    > a.item:hover:before {
        content: counter(stepNum);
        font-family: inherit;
        background-color: $body-bg;
        color: setcolor($body-bg);
    }
}

/*Cac cot cua site */
.col-main,
.col-left,
.col-right {
    padding-top: $margin-lg;
}

.col-main-inner {
    position: relative;
}

#siteContent {
    > .col-main::before {
        content: '';
        position: absolute;
        left: 0;
        top: 200px;
        bottom: 0;
        right: 0;
        opacity: 0.08;
        background-repeat: repeat-y;
        background-position: center top;
        background-image: url('../images/watermark-desktop.png');
        background-size: 100%;
    }
}

@if $enable-responsive {
@media (max-width: $screen-xs) {
    #siteContent {
        > .col-main::before {
            background-image: url('../images/watermark-mobile.svg');
            background-size: 40%;
        }
    }
}
}

.op-detail.mf-news {
    .news_column.panel {
        background-color: transparent;
    }
}

.op-main.mf-page {
    .page.panel {
        background-color: transparent;
    }
}

/*container cua cac block*/
.block-bidding {
    position: relative;
    margin-bottom: $margin-bottom-block * 1.5;
    padding-bottom: $margin-bottom-block;

    &:after {
        background-image: linear-gradient(
                to right,
                $gray-light 0px,
                $gray-light 75px,
                transparent 75px,
                transparent 125px,
                $gray-light 125px,
                $gray-light 100%
            ),
            $svg-block-bidding;
        background-position: center left, center center;
        background-repeat: no-repeat, no-repeat;
        background-size: 100% 1px, 30px 30px;
        content: "";
        display: block;
        height: 30px;
        left: 50%;
        position: absolute;
        bottom: -15.5px;
        width: 200px;
        transform: translateX(-50%);
        transition: all 0.3s ease;
    }
}

.bidding-simple {
    clear: both;

    & > .panel-bidding.well {
        padding-left: 0;
        padding-right: 0;
    }
}

.border-bidding {
    border-bottom: 1px $component-active-bg solid;
    padding-bottom: 0;
    margin-bottom: $margin-lg;

    & > span {
        border-bottom: 4px $component-active-bg solid;
        text-transform: uppercase;
        font-size: $font-size-large;
        color: $component-active-bg;
        font-weight: $headings-font-weight;
        padding-bottom: 3px;
        display: inline-block;

        &:before {
            display: inline-block;
            font-family: FontAwesome;
            font-size: $font-size-small;
            line-height: 1;
            vertical-align: middle;
            margin-right: $margin-sm;
            content: " \f0da ";
            color: $brand-danger;
        }
    }

    & > .nav {
        & > li {
            & + li {
                margin-left: $margin-lg;
            }

            & > a {
                text-transform: uppercase;
                font-size: $font-size-large;
                font-weight: $font-weight-normal;
                border-radius: 0 !important;
                padding: 0 0 3px;

                &:before {
                    display: inline-block;
                    font-family: FontAwesome;
                    font-size: $font-size-small;
                    line-height: 1;
                    vertical-align: middle;
                    margin-right: $margin-sm;
                    content: " \f0da ";
                    color: lighten($text-color, 30%);
                }

                &:hover {
                    background-color: inherit;
                    color: $component-active-bg;
                    border-bottom: 4px $component-active-bg solid;
                    transition: all 0.3s linear;

                    &:before {
                        color: $brand-danger;
                    }
                }
            }
        }

        &.nav-pills > li.active {
            & > a,
            & > a:hover,
            & > a:focus {
                background-color: inherit;
                border-bottom: 4px $component-active-bg solid;
                color: $component-active-bg;
                font-weight: $headings-font-weight;
                transition: all 0.3s linear;

                &:before {
                    color: $brand-danger;
                }
            }
        }
    }
}

.ltablesearch-cont {
    padding: $padding-large-vertical * 3 $padding-base-horizontal * 2;
}

.ltablesearch {
    &.block-bidding {
        margin-bottom: $margin-lg !important;
    }

    .block {
        display: flex;
        align-items: stretch;

        > div + div {
            > .row:last-child {
                margin-top: auto;
            }
        }
    }

    .panel {
        box-shadow: none;
        margin-top: 25px;
         margin-bottom: 5px;
    }

    .panel-heading {
        border-bottom: none;
        display: flex;
        align-items: center;
    }

    .panel-body {
        border-top: 1px $panel-default-border solid;
    }

    .input-group-addon > span {
        width: 30px;
        display: inline-block;
    }

    .form-control[readonly] {
        background-color: $body-bg;
        cursor: pointer;
    }

    .statistics {
        padding: $padding-base-horizontal * 2;
        margin: 0;
        background-image: linear-gradient(
                to left,
                rgba($component-active-bg, 0.9),
                rgba($component-active-bg, 0.7)
            ),
            /*url(../images/block_bg4.webp);*/ /*url(../images/auction-bg.webp);*/
                url(../images/dollar.webp);
        background-repeat: no-repeat, no-repeat;
        background-size: cover, 100% 100%;
        background-position: left top, left top;
        color: $component-active-color;
        border: 1px solid shade($component-active-bg, 10%);
        border-radius: $border-radius-base;
        box-shadow: $box-shadow-sm;

        h3 {
            font-size: $font-size-base * 1.25;
            font-weight: $font-weight-bold;
            margin-bottom: $margin-base;
        }

        p {
            display: block;
            position: relative;
            padding-left: $font-size-base + $padding-base-horizontal;
            margin: 0;

            &:before {
                content: loadcontent(check);
                font-family: $icomoon-font-family;
                position: absolute;
                display: flex;
                align-items: center;
                justify-content: center;
                left: 0;
                font-size: $font-size-base;
            }
        }

        p + p {
            margin-top: $margin-base;
        }

        .pull-right {
            margin-top: -10px;
            margin-right: -10px;
        }
    }

    .help {
        display: flex;
        align-items: center;

        a {
            display: flex;
            align-items: center;

            em {
                line-height: inherit;
            }

            &:hover em {
                color: $brand-danger;
            }
        }

        a + a {
            margin-left: $margin-bottom-xl;
        }
    }

    .control-label {
        display:block;
    }
    .type_org {
        .custom-radio{
            display:inline-block;
            margin-right:15px;
        }
    }
}

.money-format {
    padding-right: 25px;
    background-image: $svg-vnd;
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 12px 12px;
    text-align: right;
}

table.bidding-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0 auto $margin-lg;

    tr:nth-of-type(odd) {
        //background-color: tint($gray, 97.5%);
    }

    td,
    th {
        padding: $padding-large-vertical $padding-base-horizontal;
        border: 1px solid $table-border-color;
        font-size: $font-size-base;
    }

    th {
        background-color: $component-active-bg;
        text-align: center;
        text-transform: uppercase;
        color: $component-active-color;
        font-weight: normal;
    }

    td {
        vertical-align: top;
    }

    .txt-center {
        text-align: center;
    }
}

.bidding-list {
    margin-bottom: 15px;

    .bidding-list-header {
        background-color: $component-active-bg;
        display: flex;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: stretch;

        & > div {
            position: relative;
            flex-grow: 0;
            min-height: 1px;
            flex-shrink: 1;
            padding: $padding-large-vertical $padding-base-horizontal;
            text-align: center;
            text-transform: uppercase;
            color: $component-active-color;
            border-right: 1px $table-border-color solid;
            display: flex;
            align-items: center;
            justify-content: center;

            &:last-child {
                border-right: 0;
            }
        }
    }

    .bidding-list-body .item {
        display: flex;
        flex-wrap: nowrap;
        align-items: stretch;
        border-bottom: 1px $table-border-color solid;

        &:hover {
            background-color: darken($body-bg, 1%);
        }

        & > div {
            position: relative;
            flex-grow: 0;
            min-height: 1px;
            flex-shrink: 1;
            padding: $padding-large-vertical $padding-base-horizontal;
            border-right: 1px $table-border-color solid;
            order: 3;

            &:first-child {
                border-left: 1px $table-border-color solid;
            }

            &.c-open h3,
            &.c-name h3 {
                font-weight: $font-weight-normal;
            }
        }

        .label-name {
            display: none;
            font-weight: $font-weight-bold;
        }
    }

    .bidding-list-header,
    .bidding-list-body .item {
        & > div.c-adm {
            flex: 0 0 80px;
            max-width: 80px;
        }

        & > div.c-name {
            flex-grow: 1;
        }

        & > div.c-author {
            flex: 0 0 calc((100% - 240px) * 0.4);
            max-width: calc((100% - 240px) * 0.4);
        }

        & > div.c-tyle {
            flex: 0 0 60px;
            max-width: 60px;
            text-align: center;
        }

        & > div.c-author1 {
            flex: 0 0 140px;
            max-width: 140px;
        }

        & > div.c-author2 {
            flex: 0 0 calc((100% - 125px) / 3);
            max-width: calc((100% - 125px) / 3);
        }

        & > div.c-author3 {
            flex: 0 0 calc((100% - 125px) * 0.3);
            max-width: calc((100% - 125px) * 0.3);
        }

        & > div.c-res {
            flex: 0 0 calc((100% - 125px) * 0.4);
            max-width: calc((100% - 125px) * 0.4);
        }
    }

    .bidding-list-pagenav {
        text-align: center;
    }

    &:not(.bidding-list-detail) {
        .bidding-list-header > div.c-close,
        .bidding-list-body .item > div.c-close {
            flex: 0 0 198px;
            max-width: 198px;
        }

        .bidding-list-header > div.c-pub,
        .bidding-list-body .item > div.c-pub {
            flex: 0 0 150px;
            max-width: 150px;
        }
    }

    &.bidding-list-detail {
        .bidding-list-header > div.c-number,
        .bidding-list-body .item > div.c-number {
            flex: 0 0 130px;
            max-width: 130px;
        }

        .bidding-list-header > div.c-number.c-number-lg,
        .bidding-list-body .item > div.c-number.c-number-lg {
            flex: 0 0 140px;
            max-width: 140px;
        }

        .bidding-list-header > div.c-close,
        .bidding-list-body .item > div.c-close,
        .bidding-list-header > div.c-pub,
        .bidding-list-body .item > div.c-pub {
            flex: 0 0 120px;
            max-width: 120px;
            text-align: center;
        }

        .bidding-list-header > div.c-pub.c-pub-lg,
        .bidding-list-body .item > div.c-pub.c-pub-lg {
            flex: 0 0 160px;
            max-width: 160px;
        }

        .bidding-list-header > div.c-open,
        .bidding-list-body .item > div.c-open {
            flex: 0 0 90px;
            max-width: 90px;
            text-align: center;
        }

        .bidding-list-header > div.c-business,
        .bidding-list-body .item > div.c-business {
            flex: 0 0 100px;
            max-width: 100px;
            text-align: center;
        }

        .bidding-list-header > div.c-stt,
        .bidding-list-body .item > div.c-stt {
            flex: 0 0 40px;
            max-width: 40px;
            text-align: center;
        }
        .bidding-list-header > div.c-gia,
        .bidding-list-body .item > div.c-gia {
            flex: 0 0 100px;
            max-width: 100px;
            text-align: center;
        }
        .bidding-list-header > div.c-gia1,
        .bidding-list-body .item > div.c-gia1 {
            flex: 0 0 90px;
            max-width: 90px;
            text-align: center;
        }
    }
}

.bidding-code,
.bd-code,
.plan-code,
.solicitor-code,
.lic-code,
.promocode {
    font-size: 85%;
    padding: 3px 5px;
    border-width: 1px;
    border-style: solid;
    border-color: $gray-500 !important;
    border-radius: 4px;
    margin-right: 5px;
    color: $gray-800 !important;
    background-color: $body-bg;
    background-image: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.8) 0%,
        rgba(255, 255, 255, 0.3) 100%
    );
    box-shadow: 0 0px 4px rgba(0, 0, 0, 0.15);
    white-space: nowrap;

    &:before {
        display: inline-block;
        font: normal normal normal 14px/1 $icomoon-font-family;
        font-size: inherit;
        margin-right: 4px;
    }
}

.bidding-code:before,
.bd-code:before {
    content: loadcontent(gavel);
}

.plan-code:before {
    content: loadcontent(plan);
}

.solicitor-code:before {
    content: loadcontent(businessman);
}

.lic-code:before {
    content: loadcontent(licence);
}

.promocode:before {
    content: loadcontent(gift);
}

.fix_banner_left {
    width: 120px;
    height: 480px;
    position: fixed;
    top: 2px;
    left: 2px;
    display: none;
}

.fix_banner_right {
    width: 120px;
    height: 480px;
    position: fixed;
    top: 2px;
    right: 2px;
    display: none;
}

/* SECOND NAV */
.second-nav {
    z-index: 3;
    background-color: #dddddd;
}

.second-nav .bg {
    background: #cccccc;
    background-image: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.05) 0%,
        rgba(0, 0, 0, 0) 100%
    ) !important;
    -webkit-border-top-left-radius: 5px;
    -webkit-border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

/* THIRD NAV */
.third-nav {
    position: relative;
    margin-bottom: 6px;

    .row {
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: $gray-400;
    }

    .bg {
        background-color: $gray-200;
        padding: 5px 5px 3px 0;
    }

    .current-time {
        display: inline-block;
        vertical-align: middle;
        margin-right: 10px;
        line-height: 37px;
    }

    ul + .current-time {
        float: right;
        margin-top: -35px;
    }
}

.qlist {
    position: absolute;
    left: 0;
    bottom: 35px;
    width: 100%;
    background-color: #d9e8f4;
    border-width: 1px;
    border-style: solid;
    border-color: #a6c8e6;
    padding: 10px;
    z-index: 1000;
    display: none;
}

/* block counter */
.counter.display-table {
    width: 100%;
}

.counter span + span {
    text-align: right;
}

/* guestBlock */
.guestBlock {
    width: 350px;
}

.guestBlock > h3 {
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: #cccccc;
}

.guestBlock > h3 > a {
    display: inline-block;
    line-height: 34px;
    padding: 0 17px;
    background-color: #e5e5e5;
    -webkit-border-top-right-radius: 5px;
    border-top-right-radius: 5px;
    -webkit-border-top-left-radius: 5px;
    border-top-left-radius: 5px;
}

.guestBlock > h3 > a:hover,
.guestBlock > h3 > a.current {
    background-color: #cccccc;
}

/* Body */
#body {
    background-color: #fff;
}

.text-carousel {
    position: relative;
    background-color: $gray-lighter;
    border-top: 1px $component-active-color solid;
    border-bottom: 1px $component-active-color solid;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    width: 100%;

    > * {
        white-space: nowrap;
        font-size: $headlines-nav-font-size;
        line-height: $headlines-nav-height;
        /* height: $headlines-nav-height; */
        position: relative;
        display: inline-block;
    }

    .before {
        background-image: $svg-carousel-left;
        background-position: right center;
        background-repeat: no-repeat;
        background-size: $headlines-nav-height / 2 + 6px;

        & > span {
            display: inline-block;
            background-color: $brand-danger;
            background-clip: content-box;
            padding-right: $headlines-nav-height / 2 + 4px;

            & > span {
                padding-left: $padding-base-horizontal;
                color: $component-active-color;
                font-weight: $font-weight-bold;
            }
        }
    }

    .after {
        margin-left: auto;
        background-color: $brand-danger;
        width: $headlines-nav-height / 2 + 4px;
        background-image: $svg-carousel-right;
        background-position: right 4px center;
        background-size: $headlines-nav-height / 2 + 4px;
        background-repeat: no-repeat;

        img {
            width: $headlines-nav-height / 2 + 4px;
        }
    }

    .cont {
        padding-left: $padding-xs-horizontal;
        padding-right: 0;
        overflow: hidden;

        a {
            color: $gray-dark;

            &:hover {
                color: $brand-danger;
            }
        }
    }
}

.footer-nav {
    position: relative;
    background-color: rgba($gray-lighter, 0.5);
    overflow: initial;
    background-blend-mode: overlay;
    background-size: 480px;
    background-image: url(../images/bg-page.webp);
    margin-top: $margin-top-lg * 2;

    .content {
        padding: $grid-gutter-width / 2;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: $font-size-title;
        font-weight: $font-weight-bold;
        text-align: center;
    }
}

/* Footer */
#footer {
    position: relative;
    min-height: 160px;
    width: 100%;
    border-top-width: 1px;
    border-top-style: solid;
    border-top-color: lighten($footer-bg, 10%);
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: lighten($footer-bg, 10%);
    background-color: $footer-bg;
    background-image: linear-gradient(
            to bottom,
            rgba(darken($footer-bg, 100%), 0.3),
            rgba(darken($footer-bg, 100%), 0)
        ),
        url(../images/bg-footer-noise.webp);
    background-repeat: repeat-x, repeat;
    background-size: 100px 15px, 140px 140px;

    /*background-color: $footer-bg;
    background-image: $svg-footer-bg1;
*/
    /*background-color: $footer-bg;
    background-image: $svg-footer-bg2;
    */
    /*background-color: $footer-bg;
    background-image: $svg-footer-bg3;
    */
    padding-top: $padding-large-vertical * 3;
    padding-bottom: $padding-large-vertical;
    font-size: $font-size-small;
    color: $footer-color;

    a {
        color: $footer-color;

        &:hover {
            color: tint($footer-color, 60%);
        }
    }

    h3,
    h4 {
        font-weight: $font-weight-bold;
        font-size: $font-size-base;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        margin-bottom: $margin-base;

        a {
            color: inherit;
        }
    }

    .dropdown-menu {
        li:not(.active) > a {
            color: $dropdown-link-color !important;
        }

        li.active {
            a,
            span {
                color: $dropdown-link-active-color !important;
            }
        }
    }

    .btn-primary {
        color: shade($footer-color, 10%) !important;
        background-color: shade($footer-bg, 8%) !important;
        border-color: tint($footer-bg, 15%) !important;
    }

    p {
        margin: 0;
        padding: 0;
    }

    hr {
        border-top-color: lighten($footer-bg, 6.5%);
        margin-top: $margin-lg;
        margin-bottom: $margin-lg;
        box-shadow: shade($footer-bg, 100%) 0 -1px 0;
    }

    .icon-dauthau-logo {
        font-size: $font-size-base * 2;
    }

    .site-info {
        display: flex;
        flex-direction: row;
        margin-bottom: $margin-lg * 2;

        > div:last-child {
            margin-left: $margin-lg;
        }
    }

    .site-info-outer {
        display: flex;

        .site-info-wraper {
            flex-grow: 1;
            flex-shrink: 1;
        }

        .network-trust {
            flex: 0 0 170px;
            max-width: 170px;
            padding-left: 20px;

            img {
                filter: grayscale(1);
                opacity: 0.85;
                margin-top: 8px;
                margin-bottom: 10px;
            }
        }
    }

    .social,
    .social li,
    .site-terms,
    .site-terms li {
        position: relative;
        margin: 0;
        padding: 0;
    }

    .site-terms {
        display: block;
        margin-bottom: $margin-base;

        li {
            display: inline-block;
        }

        li:not(:last-child) {
            margin-right: $margin-sm;

            &:after {
                content: "|";
                display: inline-block;
                margin-left: $margin-sm;
            }
        }
    }

    .social {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: center;
        align-items: center;
        margin-bottom: $margin-lg + $margin-base;

        a {
            display: inline-block;
            background-color: lighten($footer-bg, 10%);
            border-radius: 50%;
            width: 36px;
            height: 36px;
            line-height: 36px;
            font-size: $font-size-base * 1.1;
            text-align: center;
        }

        li + li {
            margin-left: $margin-base;
        }
    }
}

.footer-menu {
    ul {
        position: relative;
        margin: 0;
        padding: 0;
    }

    li {
        position: relative;
        margin: 0;
        padding: 0;

        a {
            display: inline-block;
            line-height: $font-size-base*1.8;
            height: $font-size-base*1.8;

            &:hover, &:focus {
                em {
                    color: $brand-danger;
                }
            }
        }
    }

    .main-item + .main-item, .sub-menu + .sub-menu {
        margin-left: $margin-xl;
    }
    .main-menu {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: stretch;
    }

    .main-title {
      display: flex !important;
      flex-direction: row;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: space-between;
      padding-top: $margin-base;
      padding-bottom: $margin-base;

      & > span:last-child {
        margin-left: $margin-lg;
      }
    }

    .sub {
        display: flex;
        flex-direction: row;
        align-items: stretch;
        margin-bottom: $margin-lg;
    }
    .h3 {
        font-weight: $font-weight-bold;
        font-size: $font-size-base;
        letter-spacing: 0.5px;
        text-transform: uppercase;
    }
}

/* Block copyright*/
.copyright {
    color: #dcdcdc;
}

.copyright span a {
    color: #dcdcdc !important;
}

.copyright span a:hover {
    color: #ffffff !important;
}

/* Block Company info */
.company_info li {
    display: table;
    clear: both;
}

.company_info .company_name {
    text-transform: uppercase;
}

.company_info li>em, .company_info li>span {
    display: table-cell;
    vertical-align: middle;
}

.company_info li.company_name>span {
    display: inline;
}

.company_info li em {
    font-size: 1.4em;
    width: 26px;
    text-align: center;
    padding-right: 10px;
}

.company-map {
    width: 100%;
    height: 300px;
}

/* Page break line */
.nv-hr {
    clear: both;
    height: 1px;
    border-bottom-width: 1px;
    border-bottom-color: #dcdcdc;
    border-bottom-style: solid;
    margin: 10px 0;
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    -box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.nv-hr.nv-hr-lg {
    margin: 20px 0;
}

.nv-hr.nv-hr-sm {
    margin: 5px 0;
}

/* Social share,social button */
.nv-social-share {
    list-style: none;
    margin: 0;
    padding: 4px 5px 0 5px !important;
}

.nv-social-share li {
    display: inline-block;
    margin-bottom: 0 !important;
    height: 20px;
}

.nv-social-share li.facebook {
    position: relative;
    top: -4px;
    padding-right: 0px;
}

/* Form control */
.nv-captcha {
    vertical-align: middle;
}

/* List item */
.nv-list-item {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nv-list-item li {
    padding: 4px;
    margin: 0 !important;
}

.nv-list-item.sm li {
    padding: 2px;
}

.nv-list-item.xsm li {
    padding: 1px;
}

.nv-list-item.lg li {
    padding: 10px;
}

.page .nv-list-item li {
    display: flex;
}

.page .nv-list-item li em {
    position: relative;
    top: 2px;
}

ul.list-default {
    list-style: disc!important;
    padding-left: 24px!important;
}

/* sub-list-icon */
.sub-list-icon > li:first-child > *:before,
.cat-icon:before {
    content: " \f114 ";
    font-family: FontAwesome;
    font-size: inherit;
    font-weight: 400;
    text-decoration: none;
    display: inline-block;
    vertical-align: middle;
    margin-right: 4px;
    margin-top: -3px;
}

.sub-list-icon > li + li > *:before {
    content: " \f10c ";
    font-family: FontAwesome;
    font-size: 8px;
    font-weight: 400;
    text-decoration: none;
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
}

.sub-list-icon > li:first-child + li > *:before {
    content: " \f101 ";
    font-size: 12px;
    margin-top: -2px;
}

/* List none */
.list-none {
    list-style: none;
    margin: 0;
    padding: 0;
}

/* Block global */
.nv-block-banners {
    position: relative;
    margin-bottom: 10px;
    text-align: center;
}

.nv-block-banners .link {
    position: absolute;
    top: 0;
    left: 0;
}

.nv-block-banners img {
    max-width: 100% !important;
    height: auto;
}

.nv-block-rss li {
    border-bottom-width: 1px;
    border-bottom-color: #dcdcdc;
    border-bottom-style: solid;
}

.nv-block-rss li:last-child {
    border: none;
    padding-bottom: 0;
}

.nv-block-rss li:first-child {
    padding-top: 0;
}

.nv-block-rss img {
    padding: 4px;
    line-height: 1.42857143;
    background-color: #fff;
    border-width: 1px;
    border-style: solid;
    border-color: #dcdcdc;
    border-radius: 4px;
    -webkit-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    display: inline-block;
    max-width: 100%;
    height: auto;
    position: relative;
    top: 4px;
    margin-right: 10px;
    margin-bottom: 5px;
}

/* Info die */
.nv-infodie {
    margin: 0 auto;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);
    -webkit-box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);
    border-width: 1px;
    border-color: #dcdcdc;
    border-style: solid;
    background-color: #fff;
    position: absolute;
    min-height: 300px !important;
    min-width: 300px !important;
    display: table;
}

.nv-infodie .panel-body {
    display: table-cell;
    vertical-align: middle;
}

/* panel-body */
.panel-body p {
    margin: 0;
}

.panel-body ul {
    margin: 0;
    padding: 0;
}

.panel-body ul li {
    margin-bottom: 10px;
}

.panel-body img {
    margin-right: 5px;
}

.panel-body ul li:last-child {
    margin-bottom: 0;
}

.nv-infodie .logo {
    max-width: 90% !important;
    margin-bottom: 10px;
}

/* custom checkbox and radio button*/
.custom-checkbox,
.custom-radio {
    display: block;
}
.custom-checkbox-inline,
.custom-radio-inline {
    display: inline-block;
}
.custom-checkbox + .custom-checkbox,
.custom-radio + .custom-radio {
    margin-top: $margin-base;
}
.custom-checkbox-inline + .custom-checkbox-inline,
.custom-radio-inline + .custom-radio-inline {
    margin-left: $margin-sm;
}
.custom-checkbox,
.custom-radio,
.custom-checkbox-inline,
.custom-radio-inline {
    position: relative;
    cursor: pointer;
    color: $text-color;
    font-weight: normal;
    margin-bottom: 0;

    input[type="checkbox"],
    input[type="radio"] {
        position: absolute;
        right: 9000px;

        &:checked + .txt:before {
            color: $component-active-bg;
            animation: effect 250ms ease-in;
        }

        &:disabled + .txt {
            color: $btn-link-disabled-color;
            cursor: default;

            &:before {
                color: $btn-link-disabled-color;
            }
        }
    }

    .txt {
        display: flex;
        align-items: flex-start;

        &:before {
            font-family: $icomoon-font-family;
            font-style: normal;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            font-size: $font-size-large;
            line-height: $line-height-base;
            display: inline-block;
            margin-top: -(($font-size-large - $font-size-base) / 2);
            margin-right: $margin-sm;
            color: $gray-light;
        }
    }
}

.custom-radio,
.custom-radio-inline {
    input[type="radio"] {
        & + .txt:before {
            content: loadcontent(radio);
        }

        &:checked + .txt:before {
            content: loadcontent(check-radio);
        }

        &:disabled + .txt:before {
            content: loadcontent(disabled-radio);
        }
    }
}

.custom-checkbox,
.custom-checkbox-inline {
    input[type="checkbox"] {
        & + .txt:before {
            content: loadcontent(checkbox);
        }

        &:checked + .txt:before {
            content: loadcontent(check-checkbox);
        }

        &:disabled + .txt:before {
            content: loadcontent(disabled-checkbox);
        }
    }
}

.custom-radio.toggle input[type="radio"],
.custom-checkbox.toggle input[type="checkbox"],
.custom-radio-inline.toggle input[type="radio"],
.custom-checkbox-inline.toggle input[type="checkbox"] {
    & + .txt:before {
        content: loadcontent(toggle-off);
    }

    &:checked + .txt:before {
        content: loadcontent(toggle-on);
    }

    &:disabled + .txt:before {
        content: loadcontent(toggle-off);
    }
}

@keyframes effect {
    0% {
        transform: scale(0);
    }
    25% {
        transform: scale(1.3);
    }
    75% {
        transform: scale(1.4);
    }
    100% {
        transform: scale(1);
    }
}

.img-thumbnail {
    background-color: #cccccc;
    border: none;
    border-radius: 3px;
    max-width: 100%;
    padding: 1px;
}

.fb-like {
    margin-right: 4px;
}

.panel-heading {
    background-image: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.05) 0%,
        rgba(0, 0, 0, 0) 100%
    ) !important;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: rgba(0, 0, 0, 0);
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    padding: 10px;
}

.panel-primary > .panel-heading > a {
    color: #fff;
}

.panel-primary > .panel-heading > a:hover {
    color: #dcdcdc;
}

table caption {
    color: #357ebd;
    text-align: left;
    font-size: 13px;
    font-weight: 700;
    line-height: 22px;
    padding: 0 0 5px 2px;
}

.modal.auto-width .modal-dialog {
    text-align: left;
    max-width: 100%;
    width: auto !important;
    display: inline-block;
    vertical-align: middle;
}

.modal.auto-height .modal-dialog {
    text-align: left;
    max-height: 100%;
    display: inline-block;
    vertical-align: middle;
}

.modal.auto-width,
.modal.auto-height {
    text-align: center;
}

.modal-header .close {
    margin-top: -2px;
}

button.close {
    padding: 0;
    cursor: pointer;
    background: 0 0;
    border: 0;
    -webkit-appearance: none;
}

.close {
    float: right;
    font-size: 21px;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: 0.2;
}

.modal-body {
    padding: 15px;
}

#sb-container {
    z-index: 99999999 !important;
}

.chromeframe {
    position: fixed !important;
    top: 0 !important;
    right: 0;
    left: 0;
    width: 100% !important;
    z-index: 99999999999999 !important;
    background-color: #ffff00 !important;
    color: #000 !important;
    height: 25px;
    line-height: 25px;
    padding: 0.2em 0;
    text-align: center !important;
}

#timeoutsess {
    display: none;
}

#timeoutsess a {
    color: #2f70a7 !important;
}

/* Block featured-products */
.featured-products > .row {
    margin-bottom: 35px;
}

.featured-products > .row:last-child {
    margin-bottom: 0;
}

.featured-products .img-thumbnail {
    margin-top: 3px;
    margin-bottom: 10px;
    background-color: #fff !important;
    border-width: 1px !important;
    padding: 0 !important;
}

.featured-products ul {
    margin: 0;
    padding: 0;
}

.featured-products ul > li {
    margin-bottom: 5px;
}

.featured-products ul > li:before {
    font-family: "FontAwesome";
    content: "\f105";
    margin-right: 5px;
}

/* module Contact*/
.contact-result {
    position: absolute;
    top: 40px;
    left: 5%;
    width: 90%;
    float: left;
    z-index: 2;
    padding: 30px 20px;
    display: none;
}

/* Block module-menu */
.module-menu {
    display: block;
    float: left;
    width: 100%;
    background-color: #333;
}

.module-menu ul {
    margin: 0;
    padding: 0;
}

.module-menu a {
    display: block;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: rgba(0, 0, 0, 0.1);
    background-color: #428bca;
    background-image: linear-gradient(to right, #428bca 4px, #428bca 4px);
    background-repeat: repeat-x;
    color: #fff;
    text-decoration: none;
    margin: 0;
    padding: 9px 10px 9px 20px;
    cursor: pointer;
}

.module-menu a:before {
    font-family: "FontAwesome";
    content: " \f105 ";
    margin-right: 5px;
}

.module-menu a.home:before {
    content: " \f07c ";
}

.module-menu a.active {
    background-color: #a6c8e6;
}

.module-menu a:hover,
.module-menu a.active,
.module-menu a:focus {
    color: #333;
    text-decoration: none;
    background-image: linear-gradient(to right, #42cac5 4px, rgba(0, 0, 0, 0) 4px);
}

.calendar-icon {
    background-image: url(../../../assets/images/calendar.jpg);
    background-position: right 6px center;
    background-repeat: no-repeat;
    padding-right: 26px;
}

/*alert*/
.nv-alert {
    position: fixed !important;
    top: 25% !important;
    right: 0;
    left: 0;
    width: 350px !important;
    margin: auto;
    z-index: 99999999999999 !important;
    text-align: center !important;
    padding-top: 30px !important;
    padding-bottom: 30px !important;
}

.select2-container--default .select2-selection--single,
.select2-container--bootstrap .select2-selection--single {
    height: 32px !important;
    padding-top: 2px;
}

.voting-col-2 {
    border-left: 1px solid #ccc;
    padding: 0px 10px;
}

.padding-voting ul li {
    margin: 0px 10px;
    padding: 10px 0px;
    list-style: disc;
    text-align: justify;
}

.sum-voting {
    margin-top: 5px;
}

.bx-wrapper .bx-pager {
    display: none;
}

.bx-wrapper {
    margin-bottom: 20px !important;
}

.well {
    border-radius: 0px;
    background-color: #fff;
    border: none;
    padding: 10px;
}

.home-news {
    display: flex;
    align-items: stretch;

    .panel-left {
        border-right: 1px $table-border-color solid;
    }

    .news_other {
        position: relative;
        height: 100%;
        max-height: 100%;
        margin: 0;
        padding: 0;
        overflow-y: auto;
    }

    .cont {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        height: 100%;
        max-height: 100%;
        width: 100%;
        max-width: 100%;
        padding-right: $grid-gutter-width / 2;
        padding-left: $grid-gutter-width / 2;
    }

    .hot-row {
        .pull-left {
            min-height: 90px;
            position: relative;
            display: inline-block;
            background-repeat: no-repeat;
            background-size: 50%;
            background-position: center center;
            background-image: $svg-logo-sm;
            border: 1px $table-border-color solid;
            border-radius: $border-radius-base;
        }

        .imghome {
            width: 100%;
            min-height: 50px;
            font-size: 0;
            line-height: 0;
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center center;
        }

        .home-text {
            text-align: justify;
        }
    }

    .hot-row + .hot-row {
        margin-top: $margin-lg;
        padding-top: $margin-lg;
        border-top: 1px $table-border-color dashed;
    }

    .other-home {
        padding-bottom: 9px;

        a {
            color: $text-color;

            .fa {
                color: $link-color;
                font-size: 0.9rem;
                vertical-align: middle;
                margin-top: -2px;
            }

            &:hover {
                color: $link-color;
                .fa {
                    color: $brand-danger;
                }
            }
        }
    }
}

/*news-list*/
.news-list {
    padding: 0;
    margin: 0;

    li {
        display: block;
    }

    li + li {
        margin-top: $margin-base;
        padding-top: $margin-base;
        border-top: 1px $table-border-color dashed;
    }

    .pull-left {
        margin-top: $font-size-base * ($line-height-base - 1) / 2;
        min-height: 40px;
        position: relative;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: 50%;
        background-position: center center;
        background-image: $svg-logo-sm;
        border: 1px $table-border-color solid;
        border-radius: $border-radius-small;
    }

    img {
        font-size: 0;
        line-height: 0;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center center;
    }
}

.list-bidder {
    margin: 0;
    padding: 0;
    .fa {
        font-size: 12px;
        line-height: 1;
    }

    a:hover .fa {
        color: $brand-danger;
    }

    li + li {
        margin-top: $margin-base;
    }
}

.btn-more {
    border-radius: 25px;
    padding-right: $padding-small-horizontal;
}

.site-name {
    margin-left: 15px;
    display: block !important;
    margin-bottom: 10px;
}

ul.slimmenu li ul li a:hover,
ul.slimmenu li ul li a:focus {
    color: #0685d6 !important;
}

ul.slimmenu li > ul {
    width: auto !important;
}

.w300 {
    width: 300px;
}

.w250 {
    width: 250px;
}

.w200 {
    width: 200px;
}

.w150 {
    width: 150px;
}

.w100 {
    width: 100px;
}

.bidding_link {
    color: $link-color !important;
}

.bidding_link:hover {
    color: $link-hover-color !important;
}

.bidding_table thead th {
    color: #fff;
    font-weight: 300;
    background: #0685d6;
    padding-bottom: 20px !important;
    padding-top: 20px !important;
    text-align: center;
    text-transform: uppercase;
}

.btn-home {
    padding: 12px 25px;
    text-transform: uppercase;
}

.bidding-label {
    text-transform: uppercase;
}

.bidding-control {
    border: none;
}

.icon-datepicker {
    margin-right: 15px;
}

.icon-datepicker a {
    font-size: 18px;
    color: #0685d6;
}

#slide {
    height: 35px;
    background-color: #f6f5f5;
    margin-bottom: 15px;
}

#slide p {
    margin-top: 5px;
}

.bx-wrapper {
    border: 0px solid #fff !important;
}

.bx-wrapper .bx-next,
.bx-wrapper .bx-prev {
    display: none !important;
}

.bx-wrapper:hover .bx-next,
.bx-wrapper:hover .bx-prev {
    display: block !important;
}

.pull-left {
    margin-right: 10px;
}

.search_icon {
    margin: 10px 0 !important;
}
.type_org .radio {
    margin-left: 20px;
}

ul.related h4 {
    display: inline !important;
}

/* Chi tiet dau thau */
.bidding-detail {
    margin-bottom: 20px;
    background-color: #eaf1f7;
}

.bidding-detail .bidding-detail-item {
    display: flex;
    flex-wrap: nowrap;
    align-items: stretch;
}

.bidding-detail .bidding-detail-item .c-tit {
    background-color: #ccdef6;
    font-weight: 700;
    flex: 0 0 150px;
    max-width: 150px;
}

.bidding-detail .bidding-detail-item .c-val {
    flex-grow: 1;
    flex-shrink: 1;
}

.bidding-detail .bidding-detail-item.col-four > div {
    flex: 0 0 50%;
    max-width: 50%;
    display: flex;
    flex-wrap: nowrap;
    align-items: stretch;
}

.bidding-detail .bidding-detail-item:not(.col-four) > div,
.bidding-detail .bidding-detail-item.col-four > div > div {
    border-bottom: 1px #fff solid;
    border-right: 1px #fff solid;
    padding: 8px;
}

.bidding-detail
    .bidding-detail-item.col-four
    > div:last-child
    > div:first-child {
    border-left: 1px #fff solid;
}

.bidding-detail .bidding-detail-item:not(.col-four):last-child > div,
.bidding-detail .bidding-detail-item.col-four > div:last-child > div {
    border-bottom: 0;
}

.bidding-detail .bidding-detail-item:not(.col-four) > div:last-child,
.bidding-detail .bidding-detail-item.col-four > div > div:last-child {
    border-right: 0;
}

.bidding-detail-wrapper-result h1.bidding-name {
    margin-bottom: 10px;
    margin-top: 5px;
}

/* Block ki?u m?i */
.bidding-block-item {
    border: 1px #ddd solid;
    border-radius: 4px;
    padding: 7px 7px 0 7px;
    margin-bottom: 12px;
}

.bidding-block-item h3 {
    padding: 0;
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 5px 0;
}

.bidding-block-item .de {
    margin-bottom: 7px;
}

.bidding-block-item .de:last-child {
    margin-bottom: 12px;
}

.bidding-block-item .de .lbl {
    font-weight: 700;
}

.style_num {
    margin-top: 25px;
    padding-right: 20px;
}
.view-more {
    margin-top: -10px;
    color: #dc8d08 !important;
}
.menugroup {
    background-color: #d1d1d1;
    font-weight: bold;
}
.pdl20 {
    padding-left: 20px !important;
}
.pdl0 {
    padding-left: 0px !important;
}
.pdr0 {
    padding-right: 0px !important;
}

/* .dropdown-menu .pdl20 {
    padding-left: 0 !important;
} */

div.news_column ul.related a h3 {
    display: inline;
}
.afooter {
    display: flex;
    align-items: center;
    padding-left: 80px;
    padding-right: 80px;
}
.back-to-top {
    display: inline-block;
    background-color: $gray-dark;
    color: $gray-light;
    width: 38px;
    height: 38px;
    line-height: 38px;
    text-align: center;
    border-radius: $border-radius-base;
    position: relative;
    opacity: 1;
    transition: $transition;
    &:hover,
    &:focus,
    &:active {
        color: $component-active-color;
    }

    &.fixed {
        opacity: 0.4;
        position: fixed;
        bottom: 31px;
        right: 80px;
        width: 40px;
        height: 40px;
        line-height: 40px;
        font-size: $font-size-large;
        z-index: $zindex-navbar-fixed;
        display: none;
    }
}

/*BLock Filter*/
.filters {
    padding: 0;
    margin: 0;
    li {
        padding: 0;
        margin: 0;
        position: relative;
        display: block;
        border-bottom: 1px dotted $btn-default-border;

        > a {
            position: relative;
            display: inline-block;
            padding: $padding-base-vertical 20px $padding-base-vertical 0px;
            background-color: $body-bg;

            &:hover em {
                color: $brand-danger;
            }
        }
        > .i_edit {
            position: absolute;
            top: 5px;
            right: 2px;
        }
    }
}

/*
 * Container style
 * https://github.com/mdbootstrap/perfect-scrollbar/blob/master/css/perfect-scrollbar.css
 */
.ps {
    overflow: hidden !important;
    overflow-anchor: none;
    -ms-overflow-style: none;
    touch-action: auto;
    -ms-touch-action: auto;

    @supports (-ms-overflow-style: none) {
        overflow: auto !important;
    }

    @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
        overflow: auto !important;
    }
}

/*
 * Scrollbar rail styles
 */
.ps__rail-x {
    display: none;
    opacity: 0;
    transition: background-color 0.2s linear, opacity 0.2s linear;
    -webkit-transition: background-color 0.2s linear, opacity 0.2s linear;
    height: 15px;
    /* there must be 'bottom' or 'top' for ps__rail-x */
    bottom: 0px;
    /* please don't change 'position' */
    position: absolute;
}

.ps__rail-y {
    display: none;
    opacity: 0;
    transition: background-color 0.2s linear, opacity 0.2s linear;
    -webkit-transition: background-color 0.2s linear, opacity 0.2s linear;
    width: 15px;
    /* there must be 'right' or 'left' for ps__rail-y */
    right: 0;
    /* please don't change 'position' */
    position: absolute;
}

.ps--active-x > .ps__rail-x,
.ps--active-y > .ps__rail-y {
    display: block;
    background-color: transparent;
}

.ps:hover > .ps__rail-x,
.ps:hover > .ps__rail-y,
.ps--focus > .ps__rail-x,
.ps--focus > .ps__rail-y,
.ps--scrolling-x > .ps__rail-x,
.ps--scrolling-y > .ps__rail-y {
    opacity: 0.6;
}

.ps .ps__rail-x:hover,
.ps .ps__rail-y:hover,
.ps .ps__rail-x:focus,
.ps .ps__rail-y:focus,
.ps .ps__rail-x.ps--clicking,
.ps .ps__rail-y.ps--clicking {
    background-color: $gray-lighter;
    opacity: 0.9;
}

/*
 * Scrollbar thumb styles
 */
.ps__thumb-x {
    background-color: $gray-light;
    border-radius: 6px;
    transition: background-color 0.2s linear, height 0.2s ease-in-out;
    -webkit-transition: background-color 0.2s linear, height 0.2s ease-in-out;
    height: 6px;
    /* there must be 'bottom' for ps__thumb-x */
    bottom: 2px;
    /* please don't change 'position' */
    position: absolute;
}

.ps__thumb-y {
    background-color: lighten($gray-base, 63%);
    border-radius: 6px;
    transition: background-color 0.2s linear, width 0.2s ease-in-out;
    -webkit-transition: background-color 0.2s linear, width 0.2s ease-in-out;
    width: 6px;
    /* there must be 'right' for ps__thumb-y */
    right: 2px;
    /* please don't change 'position' */
    position: absolute;
}

.ps__rail-x:hover > .ps__thumb-x,
.ps__rail-x:focus > .ps__thumb-x,
.ps__rail-x.ps--clicking .ps__thumb-x {
    background-color: $gray-light;
    height: 11px;
}

.ps__rail-y:hover > .ps__thumb-y,
.ps__rail-y:focus > .ps__thumb-y,
.ps__rail-y.ps--clicking .ps__thumb-y {
    background-color: $gray-light;
    width: 11px;
}

/*bootbox*/
.bootbox {
    &.in .modal-dialog {
        -webkit-transform: translate(0, calc(50vh - 50%));
        -ms-transform: translate(0, 50vh) translate(0, -50%);
        -o-transform: translate(0, calc(50vh - 50%));
        transform: translate(0, 50vh) translate(0, -50%);
    }
}

/* Block user button */
.userbutton-box-show {
    padding: 15px;
    background-color: #dcdcdc;
    min-width: 334px;
}
.text-mute {
    font-size:12px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}
.text-justify {
    text-align: justify;
}

.text-break {
    word-wrap: break-word;
    word-break: break-word;
}

@if $enable-responsive {
@media (max-width: 370px) {
    .userbutton-box-show {
        min-width: inherit;
        width: calc(100vw - 40px);
        overflow-x: hidden;
    }
}
}

/*Style logo #391*/
.dauthau-navbar{
    .navbar-net{
        .logo{
            width: 270px;
            height: 80px;
        }
        &.navbar-dt_net{
            .logo{
                background-image: $svg-logo-dauthaunet;
            }
            .logo-en {
                background-image: $svg-logo-dauthaunet-en;
            }
        }
        &.navbar-dg_net{
            .logo{
                 background-image: $svg-logo-daugia;
            }
            .logo-en {
                background-image: $svg-logo-daugia-en;
            }
        }
        &.navbar-dt_info{
            .logo{
                 background-image: $svg-logo;
            }
            .logo-en {
                background-image: $svg-logo-en;
           }
        }
    }

}

.header__row {
    display: flex;
    .header__row_left {
        width: 100%;
        position: relative;
        .overlay-text {

          position: absolute;
            bottom: 0px;
            left: 13%;
            width: 100%;
            height: 40px;
            background: #fff;
        }

        .overlay-text::after {
            content: attr(data-content);
            position: absolute;
            color: #0095da;
            font-family: 'Open Sans';
            font-weight: 600;
            text-transform: uppercase;
            font-size: 18px;
            background: #fff;
            width: 100%;
        }
    }

    .header__row_btn {
        width: 100%;
    }

    .logo_site_id{
        background-image: $svg-logo-site-id;
        display: block;
        width: 100%;
        overflow: hidden;
        background-repeat: no-repeat;
        background-size: contain;
        background-position: 0 center;
        height: $navbar-header-height;
    }

    .logo_site_support{
        background-image: $svg-logo-site-support;
        height: 60px;
    }

    .logo_site_id_en{
        background-image: $svg-logo-site-id-en;
        display: block;
        width: 100%;
        overflow: hidden;
        background-repeat: no-repeat;
        background-size: contain;
        background-position: 0 center;
        height: $navbar-header-height;
    }

    .logo_site_support_en{
        background-image: $svg-logo-site-support-en;
        height: 60px;
    }

    .header__row_btn {
        display: flex;
        align-items: center;
        .box__btn {
            display: flex;
            width: 100%;
            min-height: 20px;
            max-height: 34px;
            margin-top: 7px;
        }

        .btn__link_ex {
            background-size: contain;
            width: 33%;
            display: block;
        }

        .btn__link_ex ~ .btn__link_ex {
            margin-left: 2px;
        }

        .btn__info {
            background: $svg-logo;
            background-repeat: no-repeat;
            background-size: contain;
        }

        .btn__daugia {
            background: $svg-logo-daugia;
            background-repeat: no-repeat;
            background-size: contain;
        }

        .btn__dauthaunet {
            background: $svg-logo-dauthaunet;
            background-repeat: no-repeat;
            background-size: contain;
        }
    }
}



.list-logos{
    &.open{
        display: block;
        background-color: #fff;
        position: absolute;
        top: 53px;
        border: 1px solid #ddd;
        border-top: 0;
        border-radius: 0;
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;
        min-width: auto !important;
        left: calc(50% - 95px);
        box-shadow: none;
        width: 100% !important;
        & > a{
            float: none;
            &:first-child{
                padding-top: 20px;
            }
            & > span:before{
                display: none !important;
            }
        }
    }
}
.navbar-brands{
    flex-grow: 1;
    flex-shrink: 1;
    position: relative;
    .navbar-brands-inner{
        display: flex;
        align-items: center;
        .navbar-brand{
            width: calc(100% / 3);
            .logo{
                width: 100%;
            }
            &.navbar-dt_net{
                &.on-other_site{
                    .logo{
                        background-position-x: 10px;
                    }
                }
            }
            &.navbar-dg_net{
                &.on-dau_gia{
                    .logo{
                        background-position-x: 15px;
                    }
                }
            }
        }
    }
}
.switch-logo-icon{
    background-image: url(../images/login-24px.svg);
    background-repeat: no-repeat;
    background-size: 100%;
    display: none;
    width: 24px;
    height: 24px;
    position: absolute;
    top: calc(50% - 12px);
    right: -22px;
    z-index: 9;
    &:hover{
        cursor: pointer;
    }
}
@if $enable-responsive {
    @media (min-width: 1085px){
        .list-logos{
            &.open{
                display: none;
            }
        }
    }

    @media (max-width: 1084px){
        .navbar-brands{
            .navbar-brands-inner{
                .navbar-brand{
                    width: 190px;
                    height: 54px;
                    .logo{
                        height: 100%;
                    }
                }
            }
        }
    }

    @media (max-width: 1071px){
        .navbar-header-right-wrap{
            flex-grow: 1;
        }
        .switch-logo__desktop{
            display: inline-block;
            right: calc(50% - 20px);
            width: 20px;
            height: 20px;
            &.on-other_site{
                top: calc(50% - 10px);
                right: 0;
            }
            &.on-dau_gia{
                right: 0;
            }
        }
        .navbar-brands{
            flex: inherit;
            .navbar-brands-inner{
                .navbar-brand{
                    &.navbar-dg_net{
                        &.on-other_site{
                            display: none;
                        }
                    }
                    &.navbar-dt_info{
                        &.on-dau_gia{
                            display: none;
                        }
                    }
                    &.navbar-dg_net{
                        &.on-dau_gia{
                            .logo{
                                background-position-x: -5px;
                            }
                        }
                    }
                    &.navbar-dt_net{
                        &.on-other_site{
                            .logo{
                                background-position-x: 6px;
                            }
                        }
                    }
                }
            }
        }
        #list-logos__desktop{
            left: auto;
            right: calc(50% - 19px);
            border: none;
            &.on-dau_gia,
            &.on-other_site{
                right: 10px;
                .logo{
                    width: 190px;
                    height: 54px;
                }
            }
            &.open{
                & > a:first-child{
                    padding-top: 0;
                }
            }
            &.on-other_site{
                .navbar-dt_net{
                    display: none;
                }
                .navbar-dg_net{
                    display: block;
                    .logo{
                        background-position-x: 2px;
                    }
                }
            }
            &.on-dau_gia{
                .navbar-dt_net{
                    display: none;
                }
                .navbar-dt_info{
                    display: block;
                    .logo{
                        background-position-x: 12px;
                    }
                }
            }
        }
    }


    @media screen and (min-width: 767px) and (max-width: 1025px){
        .header__row .header__row_left .overlay-text {
            left: 17%;
        }
    }

    @media screen and (min-width: 1025px) and (max-width: 1025px){
        .header__row .header__row_left .overlay-text {
            left: 15%;
        }
    }

    @media screen and (min-width: 720px) and (max-width: 768px){
        .header__row .header__row_left .overlay-text::after {
            left: -3.5% !important;
        }

        .header__row .header__row_left .overlay-text {
            left: 10%;
        }
    }

    @media (max-width: 881px){
        .switch-logo__desktop{
            &.on-dau_gia{
                right: 20px;
            }
        }
        .navbar-brands{
            .navbar-brands-inner{
                .navbar-brand{
                    &.navbar-dt_net{
                        &.on-other_site{
                            display: none;
                        }
                        &.on-dau_gia{
                            display: none;
                        }
                    }
                }
            }
        }
        #list-logos__desktop{
            &.on-other_site{
                .navbar-dt_net{
                    display: block;
                    .logo{
                        background-position-x: 10px;
                    }
                }
            }
            &.on-dau_gia{
                .navbar-dt_net{
                    display: block;
                    .logo{
                        background-position-x: 12px;
                    }
                }
            }
        }


        .header__row_btn {
            display: none !important;
        }
    }


    @media (max-width: $screen-xs-max){
        .header__row_left .logo_site_id {
            left: -15px;
        }

        .header__row .header__row_left .overlay-text {
            bottom: 6px;
            left: 11%;
            height: 20px;
        }

        .header__row .header__row_left .overlay-text::after {
            font-size: 12px;
            background: #fff;
            left: -2%;
            padding-left: 2px;
        }

        .header__row .logo_site_id {
            width: 60vw;
            position: relative;
            top: 0px;
            height: 55px;
        }

        .header__row .logo_site_id.logo_site_support {
            height: 45px;
            left: 5px;
        }

        .header__row .logo_site_id_en.logo_site_support_en {
            height: 45px;
            left: 5px;
        }

        .dauthau-navbar{
            .logo{
                width: 190px;
            }
            .navbar-brands{
                flex-grow: inherit;
                flex-shrink: inherit;
                .navbar-brands-inner{
                    .navbar-net{
                        .logo{
                            width: 190px;
                            height: 54px;
                        }
                    }
                    .navbar-brand{
                        width: auto;
                    }
                }
            }
        }
        .switch-logo-icon{
            display: inline-block;
            &.on-dau_gia{
                right: 7px;
            }

        }
        .switch-logo__desktop{
            display: none;
        }
        .list-logos.open{
            .logo{
                &.logo-dt_info{
                    background-image: $svg-logo;
                    &.logo-en {
                        background-image: $svg-logo-en;
                    }
                }
                &.logo-dt_net{
                    background-image: $svg-logo-dauthaunet;
                    &.logo-en {
                        background-image: $svg-logo-dauthaunet-en;
                    }
                }
                &.logo-dg_net{
                    background-image: $svg-logo-daugia;
                    &.logo-en {
                        background-image: $svg-logo-daugia-en;
                    }
                }
            }
        }
    }

    @media screen and (min-width: 490px) and (max-width: 530px){
        .header__row .header__row_left .overlay-text::after {
            left: -1.5% !important;
        }
    }

    @media screen and (min-width: 530px) and (max-width: 700px){
        .header__row .header__row_left .overlay-text::after {
            left: -2% !important;
        }

        .header__row .header__row_left .overlay-text {
            left: 10%;
        }
    }

    @media screen and (min-width: 630px) and (max-width: 720px){
        .header__row .header__row_left .overlay-text::after {
            left: -3% !important;
        }

        .header__row .header__row_left .overlay-text {
            left: 10%;
        }
    }

    @media (max-width: 320px){
        .switch-logo-icon{
            width: 18px;
            height: 18px;
            top: calc(50% - 9px);
            right: -14px;
        }
        .navbar-brand {
            & > .logo{
                width: 176px;
            }
        }
        .list-logos{
            &.open{
                left: calc(50% - 88px);
            }
        }
    }

    @media (max-width: 280px){
        .navbar-brand>.logo {
            width: 145px;
        }
        .list-logos{
            &.open{
                left: calc(50% - (145px / 2));
            }
        }

        .header__row .header__row_left .overlay-text::after {
            font-size: 10px;
            background: #fff;
            left: -2.5%;
        }
    }
}
.bidding-detail{
     .bidding-detail-item{
        .c-val{
            > a{
                &.disabled-link{
                    display: block;
                    text-decoration: line-through;
                    color: #555;
                    &::after{
                        content: "\f26b";
                        font-family: FontAwesome;
                        margin-left: 2px;
                    }
                }
            }
        }
    }
}

.other-news{
    .related{
        &.list-inline{
            li{
                display: block;
            }
        }
    }
}

.News_Time_Post {
    float: left;
    color: #808284;
    margin: 6px 0px 10px 0px;
}

.modal {
    color: #333;
    .modal-body>a {
        color: #0685d6 !important;
    }
}

#confirm {
    padding-top: 15%!important;
}

.msgshow {
    background: #F5F5F5;
    padding: 20px;
    border: 4px solid #C7C7C7;
    position: fixed;
    top: 45%;
    left: 40%;
    display: none;
    z-index: 10020 !important;
}

.invalid-feedback {
    width: 100%;
    margin-top: $margin-sm;
    font-size: $font-size-small;
    line-height: 1.3;
    color: $brand-danger;
    display: none;
}

.has-error .invalid-feedback {
    display: block;
}

.signout-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.1);
    z-index: $zindex-modal;
}

.signout-wrapper .element {
    width: 350px;
    background-color: lighten($brand-primary, 55%);
    border: 2px solid lighten($brand-primary, 53%);
    padding: 15px;
}
.language {
    margin-right: 5px;
}

.language a {
    height: 27px;
    width: 35px;
    display: inline-block;
}

.language a.active{
    background-color: transparent !important;
    border-color: transparent
}

.userbutton__login {
    padding-left: 5px;
}

.header__social {
    .social_icons_block {
        margin-right: 4px;
    }

    .select__language {
        height: 32px;
        padding: 4px 10px;
        font-size: 15px;
        line-height: 1.5;
    }

    .socialList li~li {
        margin-left: 4px;
    }
}

.header__social .language_icons_block, .header__social .social_icons_block {
    padding: 0px !important;
}

.afooter {
    padding: 0 10px;
}

.text-uppercase {
    text-transform: uppercase;
}

#footer .site-info-wraper h2 {
    padding-bottom: 8px;
}

#footer .text-center {
    padding: 6px;
}

.btn-default__custom {
    background: #f0f0f0;
}

.box__lang {
    display: flex;

    .box__lang_link_language {
        width: 33px;
        height: 28px;
        display: block;
        background: #f0f0f0;
        border-radius: 4px;
        box-shadow: 0px 1px #ccc;
        margin-right: 5px;
        position: relative;
        overflow: hidden;
        img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }
}

.box {
    display: flex;
    padding: 2px 10px 6px;
    justify-content: center;
    border-bottom: 1px dotted #ccc;
    .no-pd {
        padding: 0 !important;
    }

    .box__social_custom .socialList li~li {
        margin-left: 4px;
    }
}
