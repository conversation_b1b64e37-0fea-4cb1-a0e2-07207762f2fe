<!-- BEGIN: main -->
<!-- BEGIN: search -->
<div class="well">
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" /> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" /> <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
        <div class="row">
            <div class="form-group">
                <input class="form-control" type="text" value="{Q}" name="q" maxlength="255" placeholder="{LANG.search_sdt}" />
            </div>
            <div class="form-group">
                <input class="btn btn-primary" type="submit" value="{LANG.search_submit}" />
            </div>
        </div>
    </form>
</div>
<!-- END: search -->
<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th class="w50">{LANG.number}</th>
                    <th class="w100">
                        <div class="inlineblock">
                            {LANG.so_dien_thoai}
                        </div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">
                            {LANG.gap_nv}
                        </div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">
                            {LANG.thoi_gian_goi_dau}
                        </div>
                    </th>
                </tr>
            </thead>
            <!-- BEGIN: generate_page -->
            <tfoot>
                <tr>
                    <td class="text-center" colspan="10">{NV_GENERATE_PAGE}</td>
                </tr>
            </tfoot>
            <!-- END: generate_page -->
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="text-center">{VIEW.number}</td>
                    <td class="text-center"><a href="{VIEW.link_detail}">{VIEW.sdt}</a></td>
                    <td>
                        <!-- BEGIN: chuyen_vien -->
                        {CHUYEN_VIEN},
                        <!-- END: chuyen_vien -->
                    </td>
                    <td class="text-center">{VIEW.thoi_gian_bat_dau}</td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
</form>
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<script>
    function show_advance_search() {
        if ($("#advance_search").hasClass("hidden")) {
            $("#advance_search").removeClass("hidden");
        } else {
            $("#advance_search").addClass("hidden");
        }
    }
    $(document).ready(function() {
        $('.uidatepicker').datepicker({ showOn : "both", dateFormat : "dd/mm/yy", changeMonth : true, changeYear : true, showOtherMonths : true, buttonImage : nv_base_siteurl + "assets/images/calendar.gif", buttonImageOnly : true });

    });
</script>
<!-- END: main -->
