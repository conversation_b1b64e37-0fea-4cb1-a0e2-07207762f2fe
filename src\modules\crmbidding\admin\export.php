<?php

/**
 * @Project NUKEVIET 3.0
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2010 VINADES.,JSC. All rights reserved
 * @Createdate Thu, 15 Sep 2011 03:06:40 GMT
 */
if (!defined('NV_IS_FILE_ADMIN'))
    die('Stop!!!');

$page_title = $nv_Lang->getModule('export');
$error = [];
$file_folder = 'export_' . $module_data;
$file_folder_path = NV_ROOTDIR . '/' . NV_TEMP_DIR . '/' . $file_folder;

$_sql = 'SELECT tb1.userid, tb1.first_name, tb1.last_name, tb1.username, tb1.email FROM ' . NV_USERS_GLOBALTABLE . ' tb1 INNER JOIN ' . $db_config['prefix'] . '_authors tb2 ON tb1.userid = tb2.admin_id WHERE tb1.userid IN (SELECT `admin_id` FROM ' . NV_AUTHORS_GLOBALTABLE . ' ORDER BY lev ASC)';
$array_user = $nv_Cache->db($_sql, 'userid', 'users');

if ($nv_Request->isset_request('submit', 'post')) {
    if (file_exists($file_folder_path)) {
        $check = nv_deletefile($file_folder_path, true);
        if ($check[0] != 1) {
            $error = $check[1];
        }
    }
    if (empty($error)) {
        $check = nv_mkdir(NV_ROOTDIR . '/' . NV_TEMP_DIR, $file_folder);
        if ($check[0] != 1) {
            $error = $check[1];
        }
    }
    // Kiểm tra tồn tại của file tạm
    if (!file_exists($file_folder_path)) {
        $error = $nv_Lang->getModule('export_error_fileexists');
    }

    if (empty($error)) {

        if ($sys_info['ini_set_support']) {
            set_time_limit(0);
            ini_set('memory_limit', '1028M');
        }

        $objPHPExcel = \PhpOffice\PhpSpreadsheet\IOFactory::load(NV_ROOTDIR . '/modules/' . $module_file . '/template_export.xlsx');

        // Setting a spreadsheet’s metadata
        $objPHPExcel->getProperties()->setCreator('NukeViet CMS');
        $objPHPExcel->getProperties()->setLastModifiedBy('NukeViet CMS');
        $objPHPExcel->getProperties()->setTitle($page_title . time());
        $objPHPExcel->getProperties()->setSubject($page_title . time());
        $objPHPExcel->getProperties()->setDescription($page_title);
        $objPHPExcel->getProperties()->setKeywords($page_title);
        $objPHPExcel->getProperties()->setCategory($module_name);

        $objWorksheet = $objPHPExcel->getActiveSheet();

        // Set page orientation and size
        $objWorksheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);
        $objWorksheet->getPageSetup()->setPaperSize(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::PAPERSIZE_A4);
        $objWorksheet->getPageSetup()->setHorizontalCentered(true);
        $objWorksheet->getPageSetup()->setRowsToRepeatAtTopByStartAndEnd(1, 3);

        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_opportunities ORDER BY id DESC";

        $result = $db->query($sql);
        $array_data = array();
        while ($row = $result->fetch()) {
            $_tmp_phone = $row['phone'];
            if (preg_match('/(\d{9})$/', $row['phone'], $m)) {
                $_tmp_phone = $m[0];
            }
            $row['name_sale'] = nv_show_name_user($array_user[$row['caregiver_id']]['first_name'], $array_user[$row['caregiver_id']]['last_name'], $array_user[$row['caregiver_id']]['username']);

            if (isset($array_data[$_tmp_phone])) {
                if ($row['updatetime '] > $array_data[$_tmp_phone]['updatetime ']) {
                    $array_data[$_tmp_phone] = $row;
                }
            } else {
                $array_data[$_tmp_phone] = $row;
            }
        }

        if (!empty($array_data)) {
            $i = 1;
            $stt = 0;
            foreach ($array_data as $data) {
                $i++;
                $stt++;

                $objWorksheet->setCellValue('A' . $i, $stt);
                $objWorksheet->setCellValue('B' . $i, $data['name']);
                $objWorksheet->setCellValue('C' . $i, $data['phone']);
                $objWorksheet->setCellValue('D' . $i, $data['name_sale']);
            }

            $excel_ext = 'xlsx';
            $file_name = change_alias('Danh-sach-opportunities');
            $file_path = $file_folder_path . '/' . $file_name . '.' . $excel_ext;

            $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, ucfirst($excel_ext));
            $objWriter->save($file_path);

            $tmp_file = $file_folder_path . '/' . $file_name . '.' . $excel_ext;
            $download = new NukeViet\Files\Download($tmp_file, $file_folder_path, $file_name . '.' . $excel_ext);
            $download->download_file();
            exit();
        }
    }
}

$xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $global_config['module_theme'] . "/modules/" . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('DATA', $post);

if (!empty($error)) {
    $xtpl->assign('ERROR', $error);
    $xtpl->parse('main.error');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include (NV_ROOTDIR . "/includes/header.php");
echo nv_admin_theme($contents);
include (NV_ROOTDIR . "/includes/footer.php");
