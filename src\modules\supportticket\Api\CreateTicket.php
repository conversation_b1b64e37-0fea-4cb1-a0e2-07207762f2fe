<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\supportticket\Api;

use LogKey;
use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
use TicketStatus;
use TicketStatusClient;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CreateTicket implements IApi
{

    private $result;
    private $id = 0;
    private $title = null;
    private $content = null;
    private $cat = 0;
    private $customer = 0;
    private $vip = 0;
    private $order = 0;
    private $labels = [];
    private $assignee = [];
    private $file_attach = null;
    private $status = 0;
    private $status_client = 0;
    private $payment_status = 0;
    private $point_price = 0;
    private $add_userid = 0;
    private $add_time = 0;
    private $edit_time = 0;
    private $prefix_lang = 0;
    private $is_paid = 0;
    private $ask_expert = 0;
    private $min_point_ai = 0;
    private $min_point_expert = 0;
    private $offer_point_expert = 0;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'ticket';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $nv_Request, $nv_Cache, $db_config, $array_ticket_cats, $array_active_cats, $array_active_labels, $array_admin_users, $nv_Lang, $admin_info;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';

        $row = [];
        $error = '';
        $error_code = '0000';

        $row['id'] = $nv_Request->get_int('id', 'post', 0);
        $row['title'] = nv_substr($nv_Request->get_title('title', 'post', ''), 0, 249);
        $row['content'] = $nv_Request->get_textarea('content', '', NV_ALLOWED_HTML_TAGS);
        $row['cat_id'] = $nv_Request->get_int('cat_id', 'post', 0);
        $row['customer_id'] = $nv_Request->get_int('customer_id', 'post', 0);
        $row['vip_id'] = $nv_Request->get_int('vip_id', 'post', 0);
        $row['order_id'] = $nv_Request->get_int('order_id', 'post', 0);
        $row['label_ids'] = $nv_Request->get_typed_array('label_ids', 'post', 'int', []);
        $row['assignee_to'] = $nv_Request->get_typed_array('assignee_to', 'post', 'int', []);
        $row['status'] = $nv_Request->get_int('status', 'post', 0);
        $row['add_userid'] = $nv_Request->get_int('add_userid', 'post', 0);
        $row['file_attach'] = $nv_Request->get_array('file_attach', 'post', []);
        $row['prefix_lang'] = $nv_Request->get_int('prefix_lang', 'post', 0);
        $row['send_notify'] = $nv_Request->get_int('send_notify', 'post', 0);
        $row['add_time'] = NV_CURRENTTIME;
        $row['edit_time'] = NV_CURRENTTIME;

        $list_vips = get_user_vips($row['customer_id']);
        $list_orders = get_user_orders($row['customer_id']);
        $customer_info = get_user_info($row['customer_id']);
        $lead_cagiver = get_lead_cagiver($row['customer_id']);

        if (empty($row['title'])) {
            $error_code = '1001';
            $error = $nv_Lang->getModule('error_required_title');
        } elseif (empty($row['content'])) {
            $error_code = '1002';
            $error = $nv_Lang->getModule('error_required_content');
        } elseif (empty($row['cat_id'])) {
            $error_code = '1003';
            $error = $nv_Lang->getModule('error_required_cat_id');
        } elseif (empty($row['label_ids'])) {
            $error_code = '1004';
            $error = $nv_Lang->getModule('error_required_label_ids');
        } elseif (empty($row['customer_id']) || empty($customer_info)) {
            $error_code = '1005';
            $error = $nv_Lang->getModule('error_required_customer_id');
        } elseif (!isset($array_active_cats[$row['cat_id']])) {
            //Kiểm tra bộ phận có khả dụng ko
            $error_code = '2008';
            $error = $nv_Lang->getModule('error_cant_save') . ' 002';
        } elseif ($row['vip_id'] > 0 && !isset($list_vips[$row['vip_id']])) {
            //Kiểm tra vip có thuộc KH hiện tại ko
            $error_code = '2009';
            $error = $nv_Lang->getModule('error_cant_save') . ' 003';
        } elseif ($row['order_id'] > 0 && !isset($list_orders[$row['order_id']])) {
            //Kiểm tra đơn hàng có thuộc KH hiện tại ko
            $error_code = '2010';
            $error = $nv_Lang->getModule('error_cant_save') . ' 004';
        } elseif (!in_array($row['send_notify'], [0, 1])) {
            $error_code = '2013';
            $error = $nv_Lang->getModule('error_cant_save') . ' 007';
        } else {
            //Kiểm tra nhãn có hợp lệ ko
            foreach ($row['label_ids'] as $label_id) {
                if (!isset($array_active_labels[$label_id])) {
                    $error_code = '2011';
                    $error = $nv_Lang->getModule('error_cant_save') . ' 005';
                }
            }

            //Kiểm tra có assignee đúng người ko
            if (!empty($row['assignee_to'])) {
                foreach ($row['assignee_to'] as $admin_id) {
                    if (!isset($array_admin_users[$admin_id])) {
                        $error_code = '2012';
                        $error = $nv_Lang->getModule('error_cant_save') . ' 006';
                    }
                }
            }
        }

        if (!empty($row['file_attach'])) {
            $count_files = count($row['file_attach']['name']);
            if ($count_files > NV_ATTACH_LIMITED) {
                $error_code = '1005';
                $error = sprintf($nv_Lang->getModule('error_upload_limited'), NV_ATTACH_LIMITED);
            } else {
                $attach_files = [];
                for ($i = 0; $i < $count_files; $i++) {
                    $file['name'] = $row['file_attach']['name'][$i];
                    $file['type'] = $row['file_attach']['type'][$i];
                    $file['size'] = $row['file_attach']['size'][$i];
                    $file['tmp_name'] = $row['file_attach']['tmp_name'][$i];
                    $file['error'] = $row['file_attach']['error'][$i];
                    $attach_files[] = $file;
                }
            }
        }

        if (!empty($attach_files)) {
            $uploaded = multi_upload($attach_files);
            if ($uploaded['status'] == 'error') {
                $error = $uploaded['message'];
            }
        }

        if (empty($error)) {
            if ($row['id'] != 0) {
                $this->id = $row['id'];
            }
            $this->add_userid = $row['add_userid'];
            $this->add_time = $row['add_time'];
            $this->edit_time = $row['edit_time'];
            $this->title = $row['title'];
            $this->content = $row['content'];
            $this->cat = $row['cat_id'];
            $this->customer = $row['customer_id'];
            $this->vip = $row['vip_id'];
            $this->order = $row['order_id'];
            $this->labels = implode(',', $row['label_ids']);
            $this->prefix_lang = $row['prefix_lang'];

            $assignee_ids = [];
            if ($row['vip_id'] > 0) {
                if (isset($list_vips[$row['vip_id']]) && $list_vips[$row['vip_id']]['caregiver'] > 0) {
                    $assignee_ids[] = $list_vips[$row['vip_id']]['caregiver'];
                }
            }
            if ($row['order_id'] > 0) {
                if (isset($list_orders[$row['order_id']]) && $list_orders[$row['order_id']]['caregiver'] > 0) {
                    $assignee_ids[] = $list_orders[$row['order_id']]['caregiver'];
                }
            }
            if ($lead_cagiver > 0) {
                $assignee_ids[] = $lead_cagiver;
            }
            if (!empty($row['assignee_to'])) {
                $assignee_ids = array_merge($assignee_ids, $row['assignee_to']);
            }
            $assignee_ids[] = $admin_info['userid']; // Bổ sung assign cho admin khởi tạo ticket
            $assignee_ids = array_unique($assignee_ids);
            $this->assignee = implode(',', $assignee_ids);

            $this->status = $this->isPoint($row['cat_id']) ? 4 : $row['status']; //Dịch vụ tính điểm thì status là chờ xét duyệt
            $this->payment_status = $this->isPoint($row['cat_id']) ? 3 : 0; //Dịch vụ tính điểm thì payment_status là chờ định giá
            $row['point_price'] = $this->isPoint($row['cat_id']) ? $array_ticket_cats[$row['cat_id']]['point_price'] : 0; //Dịch vụ tính điểm thì lấy giá mặc định
            $this->point_price = $row['point_price'];
            $this->status_client = TicketStatusClient::Process->value; //Ticket được admin tạo thì status ngoài site sẽ hiển thị là "Chờ phản hồi"
            if (isset($uploaded) and $uploaded['status'] == 'success') {
                $this->file_attach = json_encode($uploaded['data']);
            }

                // Nếu như là `Yêu cầu tính phí` thì khi lưu xong sẽ ở trạng thái lưu nháp
            if (isset($array_active_cats[$row['cat_id']]) && $array_active_cats[$row['cat_id']]['is_point'] === 1) {
                $this->status = TicketStatus::Process->value;
                $this->status_client = TicketStatusClient::Process->value;
                $this->is_paid = 1;
                $this->ask_expert = 1;
                $this->min_point_expert = $array_active_cats[$row['cat_id']]['point_price'];
                $this->offer_point_expert = $array_active_cats[$row['cat_id']]['point_price'];
            }

            if ($this->save()) {
                $this->result->setSuccess();
                $this->result->setMessage($nv_Lang->getModule('success_ticket_add'));
                $this->result->set('ticket_id', $this->id);
                $this->result->set('is_paid', $this->is_paid);
                $row['id'] = $this->id;
                $row['assignee_to'] = $this->assignee;
                $row['is_paid'] = $this->is_paid;
                ticket_notification_to_user($row, true);
                foreach ($assignee_ids as $caregiver_id) {
                    ticket_notification_to_caregiver($row, $caregiver_id);
                }
                $log_data = [
                    LogKey::AdminInsert->getLabel(),
                ];
                add_ticket_logs($this->add_userid, LogKey::AdminInsert->value, $log_data, $this->id, 1);
            } else {
                if (isset($uploaded) and $uploaded['status'] == 'success') {
                    multi_upload_unlink($uploaded['dir']);
                }
                $this->result->setError()->setCode('1007')->setMessage($nv_Lang->getModule('error_cant_save'));
            }
        } else {
            if (isset($uploaded) and $uploaded['status'] == 'success') {
                multi_upload_unlink($uploaded['dir']);
            }
            $this->result->setError()->setCode($error_code)->setMessage($error);
        }
        return $this->result->getResult();
    }

    private function save()
    {
        global $db, $nv_Cache, $db_config;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];

        if ($this->id == 0) {
            $stmt = $db->prepare('INSERT INTO ' . TB_TICKET_ROW . ' (title, content, cat_id, customer_id, vip_id, order_id, label_ids, assignee_to, file_attach, status, status_client, payment_status, point_price, add_userid, add_time, activity_time, prefix_lang, is_paid, ask_expert, min_point_expert, min_point_ai, offer_point_expert) VALUES
             (:title, :content, :cat_id, :customer_id, :vip_id, :order_id, :label_ids, :assignee_to, :file_attach, :status, :status_client, :payment_status, :point_price, :add_userid, :add_time, :activity_time, :prefix_lang, :is_paid, :ask_expert, :min_point_expert, :min_point_ai, :offer_point_expert)');

            $stmt->bindParam(':add_userid', $this->add_userid, PDO::PARAM_INT);
            $stmt->bindParam(':add_time', $this->add_time, PDO::PARAM_INT);
            $stmt->bindParam(':min_point_ai', $this->min_point_ai, PDO::PARAM_INT);
        } else {
            $stmt = $db->prepare('UPDATE ' . TB_TICKET_ROW . ' SET title = :title, content = :content, cat_id = :cat_id, customer_id = :customer_id, vip_id = :vip_id, order_id = :order_id, label_ids = :label_ids, assignee_to = :assignee_to, file_attach = :file_attach, status = :status, status_client = :status_client,
                payment_status = :payment_status, point_price = :point_price, edit_time = :edit_time, activity_time = :activity_time, prefix_lang = :prefix_lang, is_paid=:is_paid, ask_expert=:ask_expert min_point_expert=:min_point_expert, offer_point_expert=:offer_point_expert WHERE id=' . $this->id);

            $stmt->bindParam(':edit_time', $this->edit_time, PDO::PARAM_INT);
        }
        $stmt->bindParam(':title', $this->title, PDO::PARAM_STR);
        $stmt->bindParam(':content', $this->content, PDO::PARAM_STR, strlen($this->content));
        $stmt->bindParam(':cat_id', $this->cat, PDO::PARAM_INT);
        $stmt->bindParam(':customer_id', $this->customer, PDO::PARAM_INT);
        $stmt->bindParam(':vip_id', $this->vip, PDO::PARAM_INT);
        $stmt->bindParam(':order_id', $this->order, PDO::PARAM_INT);
        $stmt->bindParam(':label_ids', $this->labels, PDO::PARAM_STR);
        $stmt->bindParam(':assignee_to', $this->assignee, PDO::PARAM_STR);
        $stmt->bindParam(':status', $this->status, PDO::PARAM_INT);
        $stmt->bindParam(':status_client', $this->status_client, PDO::PARAM_INT);
        $stmt->bindParam(':payment_status', $this->payment_status, PDO::PARAM_INT);
        $stmt->bindParam(':point_price', $this->point_price, PDO::PARAM_INT);
        $stmt->bindParam(':file_attach', $this->file_attach, PDO::PARAM_STR);
        $stmt->bindParam(':activity_time', $this->add_time, PDO::PARAM_INT);
        $stmt->bindParam(':prefix_lang', $this->prefix_lang, PDO::PARAM_INT);
        $stmt->bindParam(':is_paid', $this->is_paid, PDO::PARAM_INT);
        $stmt->bindParam(':ask_expert', $this->ask_expert, PDO::PARAM_INT);
        $stmt->bindParam(':min_point_expert', $this->min_point_expert, PDO::PARAM_INT);
        $stmt->bindParam(':offer_point_expert', $this->offer_point_expert, PDO::PARAM_INT);

        $exc = $stmt->execute();
        if ($exc) {
            $nv_Cache->delMod($module_name);
            if (empty($this->id)) {
                $this->id = $db->lastInsertId();
                nv_insert_logs(NV_LANG_DATA, $module_name, 'Add Ticket', ' ', $this->add_userid);
            } else {
                nv_insert_logs(NV_LANG_DATA, $module_name, 'Edit Ticket', 'ID: ' . $this->id, $this->add_userid);
            }
            return true;
        }

        return false;
    }

    private function isPoint($cat_id)
    {
        global $array_ticket_cats;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        require_once NV_ROOTDIR . '/modules/' . $module_file . '/global.functions.php';
        if (isset($array_ticket_cats[$cat_id]) && $array_ticket_cats[$cat_id]['is_point'] == 1) {
            return true;
        }

        return false;
    }
}
