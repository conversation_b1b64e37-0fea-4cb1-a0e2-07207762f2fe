<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">
<!-- BEGIN: static_all -->
<div class="row">
    <div class="col-md-24">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover text-center">
                <thead>
                    <tr>
                        <th class="text-center"></th>
                        <th class="text-center" colspan="2"><a href="{ARR_STATIC.all.link}">Tổng doanh thu</a></th>
                        <th class="text-center" colspan="2"><a href="{ARR_STATIC.year.link}">Năm nay</a></th>
                        <th class="text-center" colspan="2"><a href="{ARR_STATIC.quy.link}">Quý hiện tại</a></th>
                        <th class="text-center" colspan="2"><a href="{ARR_STATIC.lastmonth.link}">Tháng trước</a></th>
                        <th class="text-center" colspan="2"><a href="{ARR_STATIC.month.link}">Tháng hiện tại</a></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <th rowspan="2">{LANG.name_bill}</th>
                        <th class="text-center">{LANG.doanh_so}</th>
                        <th class="text-center">{LANG.thuc_nhan}</th>
                        <th class="text-center">{LANG.doanh_so}</th>
                        <th class="text-center">{LANG.thuc_nhan}</th>
                        <th class="text-center">{LANG.doanh_so}</th>
                        <th class="text-center">{LANG.thuc_nhan}</th>
                        <th class="text-center">{LANG.doanh_so}</th>
                        <th class="text-center">{LANG.thuc_nhan}</th>
                        <th class="text-center">{LANG.doanh_so}</th>
                        <th class="text-center">{LANG.thuc_nhan}</th>
                    </tr>
                    <tr>
                        <td>{ARR_STATIC.all.total_money}</td>
                        <td>{ARR_STATIC.all.total_end}</td>
                        <td>{ARR_STATIC.year.total_money}</td>
                        <td>{ARR_STATIC.year.total_end}</td>
                        <td>{ARR_STATIC.quy.total_money}</td>
                        <td>{ARR_STATIC.quy.total_end}</td>
                        <td>{ARR_STATIC.lastmonth.total_money}</td>
                        <td>{ARR_STATIC.lastmonth.total_end}</td>
                        <td>{ARR_STATIC.month.total_money}</td>
                        <td>{ARR_STATIC.month.total_end}</td>
                    </tr>
                    <tr>
                        <th rowspan="2">{LANG.point}</th>
                        <th class="text-center">{LANG.wallet}</th>
                        <th class="text-center">{LANG.point_trade}</th>
                        <th class="text-center">{LANG.wallet}</th>
                        <th class="text-center">{LANG.point_trade}</th>
                        <th class="text-center">{LANG.wallet}</th>
                        <th class="text-center">{LANG.point_trade}</th>
                        <th class="text-center">{LANG.wallet}</th>
                        <th class="text-center">{LANG.point_trade}</th>
                        <th class="text-center">{LANG.wallet}</th>
                        <th class="text-center">{LANG.point_trade}</th>
                    </tr>
                    <tr>
                        <td>{ARR_STATIC.all.wallet}</td>
                        <td>{ARR_STATIC.all.point}</td>
                        <td>{ARR_STATIC.year.wallet}</td>
                        <td>{ARR_STATIC.year.point}</td>
                        <td>{ARR_STATIC.quy.wallet}</td>
                        <td>{ARR_STATIC.quy.point}</td>
                        <td>{ARR_STATIC.lastmonth.wallet}</td>
                        <td>{ARR_STATIC.lastmonth.point}</td>
                        <td>{ARR_STATIC.month.wallet}</td>
                        <td>{ARR_STATIC.month.point}</td>
                    </tr>
                    <tr>
                        <th>{LANG.total}</th>
                        <td>{ARR_STATIC.all.sum_totalmoney}</td>
                        <td>{ARR_STATIC.all.sum_totalend}</td>
                        <td>{ARR_STATIC.year.sum_totalmoney}</td>
                        <td>{ARR_STATIC.year.sum_totalend}</td>
                        <td>{ARR_STATIC.quy.sum_totalmoney}</td>
                        <td>{ARR_STATIC.quy.sum_totalend}</td>
                        <td>{ARR_STATIC.lastmonth.sum_totalmoney}</td>
                        <td>{ARR_STATIC.lastmonth.sum_totalend}</td>
                        <td>{ARR_STATIC.month.sum_totalmoney}</td>
                        <td>{ARR_STATIC.month.sum_totalend}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
<!-- END: static_all -->
<div class="panel">
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}"> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}"> <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}">
        <div class="form-group">
            <label>{LANG.time_from}:</label> <input class="form-control w100 uidatepicker" type="text" value="{ARRAY_SEARCH.time_from}" name="time_from" maxlength="10" autocomplete="off">
        </div>
        <div class="form-group">
            <label>{LANG.to}:</label> <input class="form-control w100 uidatepicker" type="text" value="{ARRAY_SEARCH.time_to}" name="time_to" maxlength="10" autocomplete="off">
        </div>
        <!-- BEGIN: view_static -->
        <div class="form-group">
            <span>
                {LANG.display_sale_confirm_orders}
                <input type="checkbox" value="1" class="form-control" name="display_confirm_the_order" {DISPLAY_CONFIRM_ORDER} />
            </span>
        </div>
        <div class="form-group">
            <select class="form-control" id="element_group_id" name="group_id" style="width: 200px;">
                <option value="-1">{LANG.group_id_all}</option>
                <!-- BEGIN: loop_admin_group -->
                <option value="{OPTION_GROUP.key}"{OPTION_GROUP.selected}>{OPTION_GROUP.title}</option>
                <!-- END: loop_admin_group -->
            </select>
        </div>
        <!-- END: view_static -->
        <div class="form-group">
            <select class="form-control" name="admin_id" style="width: 250px;">
            </select>
        </div>
        <div class="form-group">
            <input class="btn btn-primary" type="submit" value="{LANG.search_submit}">
        </div>
    </form>
</div>
<div class="row">
    <div class="col-md-24">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover text-center">
                <caption>
                    {LANG.theo_nv}
                    <!-- BEGIN: update -->
                    <a href="{LINK_UPDATE}">({LANG.reupdate})</a>
                    <!-- END: update -->
                    <!--<a href="#" class="view_leads" data-href="{LINK_CHART}"><i class="fa fa-line-chart"></i></a>-->
                </caption>
                <thead>
                    <tr>
                        <th class="w50 text-center">{LANG.number}</th>
                        <th class="w100 text-center">{LANG.nhan_vien}</th>
                        <th class="w100 text-center">{LANG.leads_title}</th>
                        <th class="w100 text-center">{LANG.oppotunities}</th>
                        <th class="w100 text-center">{LANG.number_order}</th>
                        <th class="w100 text-center">{LANG.number_vip}</th>
                        <th class="w100 text-center">{LANG.doanh_so}</th>
                        <th class="w100 text-center">{LANG.discount}</th>
                        <th class="w100 text-center">{LANG.doanh_so_sau_giam}</th>
                        <th class="w100 text-center">{LANG.chiet_khau}</th>
                        <th class="w100 text-center">{LANG.doanh_so_thuc}</th>
                        <th class="w100 text-center">{LANG.discount_excess}</th>
                        <th class="w100 text-center">% giới thiệu</th>
                        <th class="w100 text-center">% chốt đơn</th>
                        <th class="w100 text-center">% chăm sóc</th>
                        <th class="w100 text-center">Tổng %</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop -->
                    <tr>
                        <td>{DATA_USERS.stt}</td>
                        <td>{DATA_USERS.full_name}</td>
                        <!-- BEGIN: has_link_without_lead -->
                        <td>{DATA_USERS.num_leads}</td>
                        <td>{DATA_USERS.num_opportunities}</td>
                        <td><a href="#" class="view_leads" data-href="{DATA_USERS.link_order}"> {DATA_USERS.num_order}</a></td>
                        <td><a href="#" class="view_leads" data-href="{DATA_USERS.link_vip}"> {DATA_USERS.num_vip}</a></td>
                        <!-- END: has_link_without_lead -->
                        <!-- BEGIN: has_link -->
                        <td><a href="#" class="view_leads" data-href="{DATA_USERS.link_leads}"> {DATA_USERS.num_leads}</a></td>
                        <td><a href="#" class="view_leads" data-href="{DATA_USERS.link_opportunities}"> {DATA_USERS.num_opportunities}</a></td>
                        <td><a href="#" class="view_leads" data-href="{DATA_USERS.link_order}"> {DATA_USERS.num_order}</a></td>
                        <td><a href="#" class="view_leads" data-href="{DATA_USERS.link_vip}"> {DATA_USERS.num_vip}</a></td>
                        <!-- END: has_link -->
                        <!-- BEGIN: havent_link -->
                        <td>{DATA_USERS.num_leads}</td>
                        <td>{DATA_USERS.num_opportunities}</td>
                        <td>{DATA_USERS.num_order}</td>
                        <td>{DATA_USERS.num_vip}</td>
                        <!-- END: havent_link -->
                        <td>{DATA_USERS.money}</td>
                        <td>{DATA_USERS.discount}</td>
                        <td>{DATA_USERS.total}</td>
                        <td>{DATA_USERS.price_reduce}</td>
                        <td>{DATA_USERS.total_end}</td>
                        <td>{DATA_USERS.discount_excess}</td>
                        <td>{DATA_USERS.introduce_value}</td>
                        <td>{DATA_USERS.successful_value}</td>
                        <td>{DATA_USERS.caregiver_value}</td>
                        <td>{DATA_USERS.total_percent}</td>
                    </tr>
                    <!-- END: loop -->
                </tbody>
                <tfoot>
                    <tr class="red">
                        <th colspan=2 class="text-center">Tổng:</th>
                        <th class="text-center">{STATIC_TOTAL.num_leads}</th>
                        <th class="text-center">{STATIC_TOTAL.num_opportunities}</th>
                        <th class="text-center">{STATIC_TOTAL.num_order}</th>
                        <th class="text-center">{STATIC_TOTAL.num_vip}</th>
                        <th class="text-center">{STATIC_TOTAL.money}</th>
                        <th class="text-center">{STATIC_TOTAL.discount}</th>
                        <th class="text-center">{STATIC_TOTAL.total}</th>
                        <th class="text-center">{STATIC_TOTAL.price_reduce}</th>
                        <th class="text-center">{STATIC_TOTAL.total_end}</th>
                        <th class="text-center">{STATIC_TOTAL.discount_excess}</th>
                        <th class="text-center">{STATIC_TOTAL.introduce_value}</th>
                        <th class="text-center">{STATIC_TOTAL.successful_value}</th>
                        <th class="text-center">{STATIC_TOTAL.caregiver_value}</th>
                        <th class="text-center">{STATIC_TOTAL.total_percent}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <div class="col-md-24">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover text-center">
                <caption>
                    {LANG.total_service_option}
                    <!-- BEGIN: update_vips -->
                    <a href="{LINK_UPDATE_VIPS}" target="_blank">(Cập nhật lại)</a>
                    <!-- END: update_vips -->
                </caption>
                <thead>
                    <tr>
                        <th class="w50 text-center">{LANG.stt}</th>
                        <th class="w100 text-center">{LANG.vip}</th>
                        <th class="w100 text-center">{LANG.so_luong}</th>
                        <th class="w100 text-center">{LANG.doanh_so}</th>
                        <th class="w100 text-center">{LANG.discount}</th>
                        <th class="w100 text-center">{LANG.doanh_so_sau_giam}</th>
                        <th class="w100 text-center">{LANG.chiet_khau}</th>
                        <th class="w100 text-center">{LANG.doanh_so_thuc}</th>
                        <th class="w100 text-center">% CTV</th>
                        <th class="w100 text-center">% Giới thiệu</th>
                        <th class="w100 text-center">% Chốt đơn</th>
                        <th class="w100 text-center">% Chăm sóc</th>
                        <th class="w100 text-center">Tổng %</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loopvip -->
                    <tr>
                        <td>{DATA_VIP.stt}</td>
                        <td>{DATA_VIP.vip_title}</td>
                        <!-- BEGIN: has_link -->
                        <td><a href="#" class="view_leads" data-href="{DATA_VIP.link_vip}"> {DATA_VIP.num}</a></td>
                        <td><a href="#" class="view_leads" data-href="{DATA_VIP.link_order}"> {DATA_VIP.money}</a></td>
                        <!-- END: has_link -->
                        <!-- BEGIN: havent_link -->
                        <td>{DATA_VIP.num}</td>
                        <td>{DATA_VIP.money}</td>
                        <!-- END: havent_link -->
                        <td>{DATA_VIP.discount}</td>
                        <td>{DATA_VIP.total}</td>
                        <td>{DATA_VIP.price_reduce}</td>
                        <td>{DATA_VIP.total_end}</td>
                        <td>{DATA_VIP.affilicate_value}</td>
                        <td>{DATA_VIP.introduce_value}</td>
                        <td>{DATA_VIP.successful_value}</td>
                        <td>{DATA_VIP.caregiver_value}</td>
                        <td>{DATA_VIP.total_percent}</td>
                    </tr>
                    <!-- END: loopvip -->
                </tbody>
                <tfoot>
                    <tr class="red">
                        <th colspan=2 class="text-center">Tổng:</th>
                        <th class="text-center">{STATIC_VIP.num}</th>
                        <th class="text-center">{STATIC_VIP.money}</th>
                        <th class="text-center">{STATIC_VIP.discount}</th>
                        <th class="text-center">{STATIC_VIP.total}</th>
                        <th class="text-center">{STATIC_VIP.price_reduce}</th>
                        <th class="text-center">{STATIC_VIP.total_end}</th>
                        <th class="text-center">{STATIC_VIP.affilicate_value}</th>
                        <th class="text-center">{STATIC_VIP.introduce_value}</th>
                        <th class="text-center">{STATIC_VIP.successful_value}</th>
                        <th class="text-center">{STATIC_VIP.caregiver_value}</th>
                        <th class="text-center">{STATIC_VIP.total_percent}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    <div class="col-md-24">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover text-center">
                <caption>{LANG.cac_goi_moi}</caption>
                <thead>
                    <tr>
                        <th class="w50 text-center">{LANG.stt}</th>
                        <th class="w100 text-center">{LANG.vip}</th>
                        <th class="w100 text-center">{LANG.so_luong}</th>
                        <th class="w100 text-center">{LANG.doanh_so}</th>
                        <th class="w100 text-center">{LANG.discount}</th>
                        <th class="w100 text-center">{LANG.doanh_so_sau_giam}</th>
                        <th class="w100 text-center">{LANG.chiet_khau}</th>
                        <th class="w100 text-center">{LANG.doanh_so_thuc}</th>
                        <th class="w100 text-center">% CTV</th>
                        <th class="w100 text-center">% Giới thiệu</th>
                        <th class="w100 text-center">% Chốt đơn</th>
                        <th class="w100 text-center">% Chăm sóc</th>
                        <th class="w100 text-center">Tổng %</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loopvip_new -->
                    <tr>
                        <td>{DATA_VIP_NEW.stt}</td>
                        <td>{DATA_VIP_NEW.vip_title}</td>
                        <!-- BEGIN: has_link -->
                        <td><a href="#" class="view_leads" data-href="{DATA_VIP_NEW.link_vip}"> {DATA_VIP_NEW.num}</a></td>
                        <td><a href="#" class="view_leads" data-href="{DATA_VIP_NEW.link_order}"> {DATA_VIP_NEW.money}</a></td>
                        <!-- END: has_link -->
                        <!-- BEGIN: havent_link -->
                        <td>{DATA_VIP_NEW.num}</td>
                        <td>{DATA_VIP_NEW.money}</td>
                        <!-- END: havent_link -->
                        <td>{DATA_VIP_NEW.discount}</td>
                        <td>{DATA_VIP_NEW.total}</td>
                        <td>{DATA_VIP_NEW.price_reduce}</td>
                        <td>{DATA_VIP_NEW.total_end}</td>
                        <td>{DATA_VIP_NEW.affilicate_value}</td>
                        <td>{DATA_VIP_NEW.introduce_value}</td>
                        <td>{DATA_VIP_NEW.successful_value}</td>
                        <td>{DATA_VIP_NEW.caregiver_value}</td>
                        <td>{DATA_VIP_NEW.total_percent}</td>
                    </tr>
                    <!-- END: loopvip_new -->
                </tbody>
                <tfoot>
                    <tr class="red">
                        <th colspan=2 class="text-center">Tổng:</th>
                        <th class="text-center">{STATIC_VIP_NEW.num}</th>
                        <th class="text-center">{STATIC_VIP_NEW.money}</th>
                        <th class="text-center">{STATIC_VIP_NEW.discount}</th>
                        <th class="text-center">{STATIC_VIP_NEW.total}</th>
                        <th class="text-center">{STATIC_VIP_NEW.price_reduce}</th>
                        <th class="text-center">{STATIC_VIP_NEW.total_end}</th>
                        <th class="text-center">{STATIC_VIP_NEW.affilicate_value}</th>
                        <th class="text-center">{STATIC_VIP_NEW.introduce_value}</th>
                        <th class="text-center">{STATIC_VIP_NEW.successful_value}</th>
                        <th class="text-center">{STATIC_VIP_NEW.caregiver_value}</th>
                        <th class="text-center">{STATIC_VIP_NEW.total_percent}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    <div class="col-md-24">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover text-center">
                <caption>Các gói gia hạn</caption>
                <thead>
                    <tr>
                        <th class="w50 text-center">{LANG.stt}</th>
                        <th class="w100 text-center">{LANG.vip}</th>
                        <th class="w100 text-center">{LANG.so_luong}</th>
                        <th class="w100 text-center">{LANG.doanh_so}</th>
                        <th class="w100 text-center">{LANG.discount}</th>
                        <th class="w100 text-center">{LANG.doanh_so_sau_giam}</th>
                        <th class="w100 text-center">{LANG.chiet_khau}</th>
                        <th class="w100 text-center">{LANG.doanh_so_thuc}</th>
                        <th class="w100 text-center">% CTV</th>
                        <th class="w100 text-center">% Giới thiệu</th>
                        <th class="w100 text-center">% Chốt đơn</th>
                        <th class="w100 text-center">% Chăm sóc</th>
                        <th class="w100 text-center">Tổng %</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loopvip_renewal -->
                    <tr>
                        <td>{DATA_VIP_RENEWAL.stt}</td>
                        <td>{DATA_VIP_RENEWAL.vip_title}</td>
                        <!-- BEGIN: has_link -->
                        <td><a href="#" class="view_leads" data-href="{DATA_VIP_RENEWAL.link_vip}"> {DATA_VIP_RENEWAL.num}</a></td>
                        <td><a href="#" class="view_leads" data-href="{DATA_VIP_RENEWAL.link_order}"> {DATA_VIP_RENEWAL.money}</a></td>
                        <!-- END: has_link -->
                        <!-- BEGIN: havent_link -->
                        <td>{DATA_VIP_RENEWAL.num}</td>
                        <td>{DATA_VIP_RENEWAL.money}</td>
                        <!-- END: havent_link -->
                        <td>{DATA_VIP_RENEWAL.discount}</td>
                        <td>{DATA_VIP_RENEWAL.total}</td>
                        <td>{DATA_VIP_RENEWAL.price_reduce}</td>
                        <td>{DATA_VIP_RENEWAL.total_end}</td>
                        <td>{DATA_VIP_RENEWAL.affilicate_value}</td>
                        <td>{DATA_VIP_RENEWAL.introduce_value}</td>
                        <td>{DATA_VIP_RENEWAL.successful_value}</td>
                        <td>{DATA_VIP_RENEWAL.caregiver_value}</td>
                        <td>{DATA_VIP_RENEWAL.total_percent}</td>
                    </tr>
                    <!-- END: loopvip_renewal -->
                </tbody>
                <tfoot>
                    <tr class="red">
                        <th colspan=2 class="text-center">Tổng:</th>
                        <th class="text-center">{STATIC_VIP_RENEWAL.num}</th>
                        <th class="text-center">{STATIC_VIP_RENEWAL.money}</th>
                        <th class="text-center">{STATIC_VIP_RENEWAL.discount}</th>
                        <th class="text-center">{STATIC_VIP_RENEWAL.total}</th>
                        <th class="text-center">{STATIC_VIP_RENEWAL.price_reduce}</th>
                        <th class="text-center">{STATIC_VIP_RENEWAL.total_end}</th>
                        <th class="text-center">{STATIC_VIP_RENEWAL.affilicate_value}</th>
                        <th class="text-center">{STATIC_VIP_RENEWAL.introduce_value}</th>
                        <th class="text-center">{STATIC_VIP_RENEWAL.successful_value}</th>
                        <th class="text-center">{STATIC_VIP_RENEWAL.caregiver_value}</th>
                        <th class="text-center">{STATIC_VIP_RENEWAL.total_percent}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    <!-- BEGIN: source_money -->
    <div class="col-md-24">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover text-center">
                <caption>{LANG.source_money_order}</caption>
                <thead>
                    <tr>
                        <th class="w50 text-center">{LANG.stt}</th>
                        <th class="w100 text-center">{LANG.source_money}</th>
                        <th class="w100 text-center">{LANG.number_order}</th>
                        <th class="w100 text-center">{LANG.so_tien_thanh_toan}</th>
                        <th class="w100 text-center">{LANG.so_tien}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop -->
                    <tr>
                        <td>{SOURCE_MONEY.stt}</td>
                        <td>{SOURCE_MONEY.title}</td>
                        <td>{SOURCE_MONEY.countid}</td>
                        <!-- BEGIN: has_link -->
                        <td><a href="#" class="view_leads" data-href="{SOURCE_MONEY.link_order}"> {SOURCE_MONEY.total}</a></td>
                        <td><a href="#" class="view_leads" data-href="{SOURCE_MONEY.link_order}"> {SOURCE_MONEY.total_end}</a></td>
                        <!-- END: has_link -->
                        <!-- BEGIN: havent_link -->
                        <td>{SOURCE_MONEY.total}</td>
                        <td>{SOURCE_MONEY.total_end}</td>
                        <!-- END: havent_link -->
                    </tr>
                    <!-- END: loop -->
                </tbody>
                <tfoot>
                    <tr class="red">
                        <th colspan=2 class="text-center">Tổng:</th>
                        <th class="text-center">{STATIC_SOURCE_MONEY.num}</th>
                        <th class="text-center">{STATIC_SOURCE_MONEY.total}</th>
                        <th class="text-center">{STATIC_SOURCE_MONEY.total_end}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <!-- END: source_money -->

    <!-- doanh số nạp ví -->
    <div class="col-md-24">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover text-center">
                <caption>{LANG.static_wallet}</caption>
                <thead>
                    <tr>
                        <th class="w50 text-center">{LANG.number}</th>
                        <th class="w100 text-center">{LANG.nhan_vien}</th>
                        <th class="w100 text-center">{LANG.number_order}</th>
                        <th class="w100 text-center">{LANG.total_amount_deposited}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop_wallet -->
                    <tr>
                        <td>{DATA_WALLET.stt_wallet}</td>
                        <td class="text-left">{DATA_WALLET.full_name}</td>
                        <!-- BEGIN: has_link -->
                        <td><a target="_blank" class="view_wallet" href="{DATA_WALLET.link_wallet}"> {DATA_WALLET.recharge_day}</a></td>
                        <td><a target="_blank" class="view_wallet" href="{DATA_WALLET.link_wallet}">{DATA_WALLET.total_day}</a></td>
                        <!-- END: has_link -->
                        <!-- BEGIN: havent_link -->
                        <td>{DATA_WALLET.recharge_day}</td>
                        <td>{DATA_WALLET.total_day}</td>
                        <!-- END: havent_link -->
                    </tr>
                    <!-- END: loop_wallet -->
                </tbody>
                <tfoot>
                    <tr class="red">
                        <th colspan=2 class="text-center">Tổng:</th>
                        <th class="text-center">{STATIC_WALLET_TOTAL.total_recharge}</th>
                        <th class="text-center">{STATIC_WALLET_TOTAL.total_money}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    <!-- doanh số nạp điểm -->
    <div class="col-md-24">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover text-center">
                <caption>{LANG.static_wallet_point}</caption>
                <thead>
                    <tr>
                        <th class="w50 text-center">{LANG.number}</th>
                        <th class="w100 text-center">{LANG.nhan_vien}</th>
                        <th class="w100 text-center">{LANG.number_order}</th>
                        <th class="w100 text-center">{LANG.doanh_so}</th>
                        <th class="w100 text-center">{LANG.hoa_hong}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop_wallet_point -->
                    <tr>
                        <td>{DATA_WALLET_POINT.stt_wallet}</td>
                        <td class="text-left">{DATA_WALLET_POINT.full_name}</td>
                        <!-- BEGIN: has_link -->
                        <td><a target="_blank" class="view_wallet" href="{DATA_WALLET_POINT.link_wallet}"> {DATA_WALLET_POINT.money_point_num}</a></td>
                        <td><a target="_blank" class="view_wallet" href="{DATA_WALLET_POINT.link_wallet}">{DATA_WALLET_POINT.money_point}</a></td>
                        <td><a target="_blank" class="view_wallet" href="{DATA_WALLET_POINT.link_wallet}">{DATA_WALLET_POINT.money_point_bonus}</a></td>
                        <!-- END: has_link -->
                        <!-- BEGIN: havent_link -->
                        <td>{DATA_WALLET_POINT.money_point_num}</td>
                        <td>{DATA_WALLET_POINT.money_point}</td>
                        <td>{DATA_WALLET_POINT.money_point_bonus}</td>
                        <!-- END: havent_link -->
                    </tr>
                    <!-- END: loop_wallet_point -->
                </tbody>
                <tfoot>
                    <tr class="red">
                        <th colspan=2 class="text-center">Tổng:</th>
                        <th class="text-center">{STATIC_WALLET_POINT_TOTAL.money_point_num}</th>
                        <th class="text-center">{STATIC_WALLET_POINT_TOTAL.money_point}</th>
                        <th class="text-center">{STATIC_WALLET_POINT_TOTAL.money_point_bonus}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    <!-- BEGIN: source_money_wallet -->
    <div class="col-md-24">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover text-center">
                <caption>{LANG.source_money_wallet}</caption>
                <thead>
                    <tr>
                        <th class="w50 text-center">{LANG.stt}</th>
                        <th class="w100 text-center">{LANG.source_money}</th>
                        <th class="w100 text-center">{LANG.number_order}</th>
                        <th class="w100 text-center">{LANG.so_tien_thanh_toan}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop -->
                    <tr>
                        <td>{SOURCE_MONEY_WALLET.stt}</td>
                        <td>{SOURCE_MONEY_WALLET.title}</td>
                        <!-- BEGIN: has_link -->
                        <td><a target="_blank" class="view_wallet" href="{SOURCE_MONEY_WALLET.link}">{SOURCE_MONEY_WALLET.num}</a></td>
                        <td><a target="_blank" class="view_wallet" href="{SOURCE_MONEY_WALLET.link}">{SOURCE_MONEY_WALLET.money_total_format}</a></td>
                        <!-- END: has_link -->
                        <!-- BEGIN: havent_link -->
                        <td>{SOURCE_MONEY_WALLET.num}</td>
                        <td>{SOURCE_MONEY_WALLET.money_total_format}</td>
                        <!-- END: havent_link -->
                    </tr>
                    <!-- END: loop -->
                </tbody>
                <tfoot>
                    <tr class="red">
                        <th colspan=2 class="text-center">Tổng:</th>
                        <th class="text-center">{SOURCE_MONEY_WALLET_SUM.num}</th>
                        <th class="text-center">{SOURCE_MONEY_WALLET_SUM.money_total_format}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <!-- END: source_money_wallet -->
</div>
<script type="text/javascript">
    var isFirstRun = true;
    var old_groupId = -1;
    function getAdmin() {
        var groupId = $('[id="element_group_id"]').val();
        $("[name='group_id']").val(groupId);

        if (isFirstRun || (groupId > 0 && groupId != old_groupId) || groupId == -1) {
            isFirstRun = false;
            old_groupId = groupId;

            // Xử lý giá trị mặc định khi chọn nhóm
            $('select[name="admin_id"]').val('-1');
            $('select[name="admin_id"]').empty();
            $('select[name="admin_id"]').append('<option value="-1">{LANG.admin_id_all}</option>');

            $.ajax({
                type: "POST",
                url: window.location.href,
                data: "&get_admin=1&groupId=" + groupId,
                dataType: "json",
                success: function(data) {
                    $.each(data.list_admin, function(key, value) {
                        var option = $('<option></option>').val(value.id).html(value.title);
                        if (value.id == {ARRAY_SEARCH.admin_id}) {
                            option.attr("selected", "selected");
                        }
                        $('select[name="admin_id"]').append(option);
                    });
                }
            });
        }
    }
    $(document).ready(function($) {
        getAdmin();
        $("select[name='admin_id']").select2();
        $("select[name='group_id']").select2();
        $('[id="element_group_id"]').on('change', function() {
            getAdmin();
        });
    });
    $('.view_leads').click(function() {
        var href = $(this).attr('data-href');
        nv_open_browse(href, 'NVImg', 1200, 600,
                'resizable=no,scrollbars=no,toolbar=no,location=no,status=no');
        return;
    });
    $(document).ready(function() {
        $('.uidatepicker').datepicker({
            showOn : "both",
            dateFormat : "dd/mm/yy",
            changeMonth : true,
            changeYear : true,
            showOtherMonths : true,
            buttonImage : nv_base_siteurl + "assets/images/calendar.gif",
            buttonImageOnly : true
        });
    });
</script>
<!-- END: main -->

<!-- BEGIN: chart -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/chart/chart.js"></script>
<div class="row">
    <div id="canvas-holder" class="col-md-24">
        <canvas id="chart-area" width="800" height="400"></canvas>
    </div>
</div>
<script type="text/javascript">
    window.chartColors = {
        red : 'rgb(255, 99, 132)',
        orange : 'rgb(255, 159, 64)',
        yellow : 'rgb(255, 205, 86)',
        green : 'rgb(75, 192, 192)',
        blue : 'rgb(54, 162, 235)',
        purple : 'rgb(153, 102, 255)',
        grey : 'rgb(201, 203, 207)'
    };
    var lineChartData = {
        labels : [ {LABEL} ],
        datasets : [
            {
                label : 'Leads',
                borderColor : window.chartColors.yellow,
                backgroundColor : window.chartColors.yellow,
                fill : false,
                data : [ {DATA_LEADS} ]
            },
            {
                label : 'Cơ hội kinh doanh',
                borderColor : window.chartColors.blue,
                backgroundColor : window.chartColors.blue,
                fill : false,
                data : [ {DATA_OPPOTUNITIESs} ]
            },
            {
                    label : 'Đơn hàng',
                    borderColor : window.chartColors.red,
                    backgroundColor : window.chartColors.red,
                    fill : false,
                    data : [ {DATA_ORDER} ]
                },
                {
                    label : 'VIP',
                    borderColor : window.chartColors.green,
                    backgroundColor : window.chartColors.green,
                    fill : false,
                    data : [ {DATA_VIPS} ]
                }]
    };

    window.onload = function() {
        var ctx = document.getElementById('chart-area').getContext('2d');
        window.myLine = Chart.Line(ctx, {
            data : lineChartData,
            options : {
                responsive : true,
                hoverMode : 'index',
                stacked : false,
                title : {
                    display : true,
                    text : 'Biểu đồ tăng trưởng'
                }
            }
        });
    };
</script>
<!-- END: chart -->
