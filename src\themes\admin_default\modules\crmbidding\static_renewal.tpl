<!-- BEGIN: main -->
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/dauthau/plugins/daterangepicker/moment.min.js"></script>
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/dauthau/plugins/daterangepicker/daterangepicker.js"></script>
<link type="text/css" rel="stylesheet" href="{NV_BASE_SITEURL}themes/dauthau/plugins/daterangepicker/daterangepicker.css" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/select2/select2.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/select2/i18n/vi.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/select2/select2.min.css">

<div class="panel">
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline" id="ltablesearch">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}"> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}"> <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}"> <label>{LANG.select_time}:</label>
        <div class="form-group">
            <div class="col-xs-24">
                <input type="hidden" name="sfrom" value="{FROM}" data-default="{FROM_DEFAULT}" /> <input type="hidden" name="sto" value="{TO}" data-default="{TO_DEFAULT}" /> <input class="form-control search_range" type="text" value="{FROM} - {TO}">
            </div>
        </div>
        <div class="form-group">
            <select class="form-control" name="admin_id">
                <option value="0">{LANG.admin_id_all}</option>
                <!-- BEGIN: loop_admin -->
                <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                <!-- END: loop_admin -->
            </select>
        </div>
        <div class="form-group">
            <input class="form-control" type="checkbox" value="1" name="static_vieweb"{STATIC_VIEWEB}> <label>{LANG.static_vieweb}</label>
        </div>
        <div class="form-group">
            <input class="btn btn-primary" type="submit" value="{LANG.search_submit}">
        </div>
    </form>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover text-center">
                <caption>{lang_static_renewal}</caption>
                <thead>
                    <tr>
                        <th class="w50 text-center">{LANG.number}</th>
                        <th class="w100 text-center">{LANG.vip}</th>
                        <th class="w100 text-center">{LANG.number_vip}</th>
                        <th class="w100 text-center">{LANG.sodongiahantruoc}</th>
                        <th class="w100 text-center">{LANG.tongsodongiahan}</th>
                        <th class="w100 text-center">{LANG.ti_le}</th>
                        <th class="w100 text-center">{LANG.doanh_so_thuc}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop -->
                    <tr>
                        <td>{DATA.stt}</td>
                        <td>{DATA.vip}</td>
                        <td><a href="#" class="view_leads" data-href="{DATA.link_vip}"> {DATA.number_vip}</a></td>
                        <td><a href="#" class="view_leads" data-href="{DATA.link_vip_before}"> {DATA.data_before}</a></td>
                        <td><a href="#" class="view_leads" data-href="{DATA.link_vip_renewal}"> {DATA.isrenewal}</a></td>
                        <td>{DATA.ti_le}</td>
                        <td><a href="#" class="view_leads" data-href="{DATA.link_order}">{DATA.total_end}</a></td>
                    </tr>
                    <!-- END: loop -->
                </tbody>
                <tfoot>
                    <tr class="red">
                        <th colspan=2 class="text-center">Tổng:</th>
                        <th class="text-center">{STATIC_TOTAL.number_vip}</th>
                        <th class="text-center">{STATIC_TOTAL.data_before}</th>
                        <th class="text-center">{STATIC_TOTAL.isrenewal}</th>
                        <th class="text-center">{STATIC_TOTAL.ti_le}</th>
                        <th class="text-center">{STATIC_TOTAL.total_end}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    <div class="col-md-offset-1 col-md-11 ">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover text-center">
                <caption>{LANG.vip_month} {NEXT_MONTH}/{NEXT_YEAR}</caption>
                <thead>
                    <tr>
                        <th class="w50 text-center">{LANG.number}</th>
                        <th class="w100 text-center">{LANG.vip}</th>
                        <th class="w100 text-center">{LANG.number_vip}</th>
                        <th class="w100 text-center">{LANG.doanh_so}</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- BEGIN: loop_next_month -->
                    <tr>
                        <td>{DATA_NEXT.stt}</td>
                        <td>{DATA_NEXT.vip}</td>
                        <td><a href="#" class="view_leads" data-href="{DATA_NEXT.link_vip}"> {DATA_NEXT.number_vip}</a></td>
                        <td>{DATA_NEXT.total}</td>
                    </tr>
                    <!-- END: loop_next_month -->
                </tbody>
                <tfoot>
                    <tr class="red">
                        <th colspan=2 class="text-center">Tổng:</th>
                        <th class="text-center">{STATIC_TOTAL_NEXT}</th>
                        <th class="text-center">{STATIC_TOTAL_MONEY_NEXT}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>


<div class="row">
    <hr>
    <h4 class="alert alert-info">{LANG.title__static}</h4>
    <div class="col-md-24">
        

        <table class="table table-striped table-bordered table-hover text-center">
            <thead>
                <tr>
                    <th colspan="3">
                        {average_years}
                    </th>
                </tr>
                <tr>
                    <th class="w50 text-center">{LANG.number}</th>
                    <th class="w100 text-center">{LANG.title__head_vip}</th>
                    <th class="w100 text-center">{LANG.title__head_ave_year}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop_avevip -->
                 <tr>
                    <td>{AVE_VIP.stt}</td>
                    <td>{AVE_VIP.title_vip}</td>
                    <td><b>{AVE_VIP.ave}</b></td>
                </tr>
                <!-- END: loop_avevip -->
            </tbody>
        </table>
    </div>
</div>
<style>
table thead tr td, table thead tr th {
    vertical-align: middle !important;
}
</style>
<script type="text/javascript">
    $(document).ready(function($) {
        $("select[name='admin_id']").select2();
    });

    var formObject = $("[id=ltablesearch]");
    $('.view_leads').click(function() {
        var href = $(this).attr('data-href');
        console.log(href);
        nv_open_browse(href, 'NVImg', 1200, 600, 'resizable=no,scrollbars=no,toolbar=no,location=no,status=no');
        return;
    });

    function bl_setDaterangepicker(_options) {
        // Menu khoảng tìm kiếm
        var ranges = {};
        ranges['{LANG.this_month}'] = [ moment().startOf('month'), moment().endOf('month') ];
        ranges['{LANG.last_3_months}'] = [ moment().startOf('quarter'), moment().endOf('quarter') ];
        ranges['{LANG.this_year}'] = [ moment().startOf('year'), moment().endOf('year') ];
        ranges['{LANG.last_all_days}'] = [ moment('{MINDATE}', "DD/MM/YYYY"), moment() ];

        var calendar_options = { showDropdowns : true, locale : { customRangeLabel : '{LANG.custom_range}', format : 'DD/MM/YYYY', help : '' }, ranges : ranges, startDate : moment().subtract(14, 'days'), endDate : moment(), opens : 'right', drops : "auto", alwaysShowCalendars : true, };

        $.extend(calendar_options, _options);

        $(".search_range", formObject).daterangepicker(calendar_options, function(start, end, label) {
            $("[name=sfrom]", formObject).val(start.format('DD/MM/YYYY'));
            $("[name=sto]", formObject).val(end.format('DD/MM/YYYY'))
        });
    }
    $(function() {
        bl_setDaterangepicker({ startDate : $("[name=sfrom]", formObject).val(), endDate : $("[name=sto]", formObject).val() });
    });
</script>
<!-- END: main -->
