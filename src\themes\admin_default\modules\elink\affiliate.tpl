<!-- BEGIN: main -->
<div class="form-group">
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline">
        <input type="hidden" name="{NV_LANG_VARIABLE}"  value="{NV_LANG_DATA}" />
        <input type="hidden" name="{NV_NAME_VARIABLE}"  value="{MODULE_NAME}" />
        <input type="hidden" name="{NV_OP_VARIABLE}"  value="{OP}" />
        <input class="form-control w200" type="text" value="{Q}" name="q" maxlength="100" placeholder="Nhập từ khóa">
        <input class="btn btn-primary" type="submit" value="{GLANG.search}" />
        <a class="btn btn-success{HTML_FORM1}" role="button" data-toggle="collapse" href="#collapseForm" aria-expanded="{HTML_FORM2}" aria-controls="collapseForm">{LANG.affiliate_add}</a>
    </form>
</div>
<div class="collapse{HTML_FORM3}" id="collapseForm">
    <!-- BEGIN: error -->
    <div class="alert alert-danger">{ERROR}</div>
    <!-- END: error -->
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post" class="form-horizontal">
                <div class="form-group">
                    <label for="input_pre_uid" class="col-md-4 control-label">Tài khoản</label>
                    <div class="col-md-20 col-lg-10">
                        <div class="input-group">
                            <input type="text" class="form-control" id="input_pre_uid" name="pre_uid" value="{DATA.pre_uid}">
                            <div class="input-group-btn">
                                <button type="button" class="btn btn-default" id="sel_pre_uid">Chọn</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="input_pri_uid" class="col-md-4 control-label">Người giới thiệu</label>
                    <div class="col-md-20 col-lg-10">
                        <div class="input-group">
                            <input type="text" class="form-control" id="input_pri_uid" name="pri_uid" value="{DATA.pri_uid}">
                            <div class="input-group-btn">
                                <button type="button" class="btn btn-default" id="sel_pri_uid">Chọn</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-20 col-md-push-4">
                        <button type="submit" class="btn btn-primary" name="submit">{GLANG.submit}</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <script type="text/javascript">
    $('#sel_pre_uid').click(function() {
        nv_open_browse('{NV_BASE_ADMINURL}index.php?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=users&' + nv_fc_variable + "=getuserid&area=input_pre_uid&return=username&filtersql=", "NVImg", 850, 420, "resizable=no,scrollbars=no,toolbar=no,location=no,status=no");
    });
    $('#sel_pri_uid').click(function() {
        nv_open_browse('{NV_BASE_ADMINURL}index.php?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=users&' + nv_fc_variable + "=getuserid&area=input_pri_uid&return=username&filtersql=", "NVImg", 850, 420, "resizable=no,scrollbars=no,toolbar=no,location=no,status=no");
    });
    </script>
</div>
<form action="{NV_BASE_ADMINURL}index.php" method="get">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th class="mw100">Tên đăng nhập</th>
                    <th class="mw100">Họ và tên</th>
                    <th>Email</th>
                    <th class="w150">Ngày tham gia</th>
                    <th class="mw100">Người giới thiệu</th>
                    <th class="w150 text-nowrap">&nbsp;</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td>{VIEW.pre_username}</td>
                    <td>{VIEW.pre_full_name}</td>
                    <td class="text-nowrap">{VIEW.pre_email}</td>
                    <td>{VIEW.regdate}</td>
                    <td>{VIEW.pri_full_name}</td>
                    <td class="text-center">
                        <a href="#" class="btn btn-xs btn-danger" data-toggle="delaffuser" data-pri="{VIEW.pri_uid}" data-pre="{VIEW.pre_uid}"><i class="fa fa-trash"></i> {GLANG.delete}</a>
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
            <!-- BEGIN: generate_page -->
            <tfoot>
                <tr>
                    <td class="text-center" colspan="10">{NV_GENERATE_PAGE}</td>
                </tr>
            </tfoot>
            <!-- END: generate_page -->
        </table>
    </div>
</form>
<!-- END: main -->
