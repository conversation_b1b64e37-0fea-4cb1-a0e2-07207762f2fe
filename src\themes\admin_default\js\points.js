/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 16 Jan 2018 07:25:26 GMT
 */
$(".convert__point").hide();
$(".selectPoint").hide();
$(document).ready(function() {
	$('#myInput').on("keyup",function(){
        var value = $(this).val().toLowerCase();
        $(".main__boxPoint .row__box_logPoint").filter(function(){
            $(this).toggle($(this).text().toLowerCase().indexOf(value)>-1);
        });
    });   

    $('#myInputTB').on("keyup",function(){
        var value = $(this).val().toLowerCase();
        $("#tbody tr").filter(function(){
            $(this).toggle($(this).text().toLowerCase().indexOf(value)>-1);
        });
    });   

	$(".point__link").click(function(event) {
		id = $(this).attr('data-id-user');
		if (id) {
			$.ajax({
				url: window.location.href,
				type: 'POST',
				data: {
					'action': 'showLog',
					'id_user' : id
				},
			})
			.done(function(data) {
				$("#tbody").html("");
				if (data) {
					$("#tbody").html(data['content']);
				}
			})
			.fail(function() {
				console.log("error");
			})
			.always(function() {
				console.log("complete");
			});
			
		}
	});

	let configPoint = $('#config_point').val();
	let unit = $("#unit").val();
	if ($('#config_point').val()) {
		configPoint = JSON.parse(configPoint);
	}

	if ($("input[name='note_point']:checked").val() == 1) {
		$(".selectPoint").hide();	
		$("input[name='money_transaction']").show();
	} else {
		$(".selectPoint").show();
		$("input[name='money_transaction']").hide();
	}

	$("select[name='moneyConfig']").on('change', function() {
		let money = 0;
		let point = parseInt($(this).val());
		let note_point = $("input[name='note_point']:checked").val();
		if (configPoint[point] && parseInt(note_point) === -1) {
			money = configPoint[point];
			$(".convert__point").show(200);
			$("#convert__money").html(FormatNumber(money.toString()) + ' ' + unit);
		} else {
			money = 0;
			$(".convert__point").hide(200);
			$("#convert__money").html('');
		}
	});

	$("input[name='note_point']").change(function(){
		notePoint = parseInt($(this).val());
		valuePoint = parseInt($("input[name='money_transaction']").val());
		if (!$("input[name='money_transaction']").val()) {
			valuePoint = 0;
		}
		let money = 0;
		if (notePoint == 1) {
			$(".convert__point").hide(200);
			$("#convert__money").html('');
			$(".selectPoint").hide(200);
			$("input[name='money_transaction']").show(200);
		} else {
			$(".selectPoint").show(200);
			$("input[name='money_transaction']").hide(200);
			if (configPoint[valuePoint] && notePoint == -1) {
				money = configPoint[valuePoint];
				$(".convert__point").show(200);
				$("#convert__money").html(FormatNumber(money.toString()) + ' ' + unit);
			} else {
				money = 0;
				$(".convert__point").hide(200);
				$("#convert__money").html('');
			}
		}
	});

	$("input[name='account']").on('input', function() {
		console.log();
		$.ajax({
			url: window.location.href,
			type: 'POST',
			data: {
				action: 'getMoneyUser',
				userID: $(this).val()
			},
		})
		.done(function() {
			console.log("success");
		})
		.fail(function() {
			console.log("error");
		})
		.always(function() {
			console.log("complete");
		});
	});	
});

function FormatNumber(str) {
    var strTemp = GetNumber(str);
    if (strTemp.length <= 3) {
        return strTemp;
    }
    strResult = "";
    for (var i = 0; i < strTemp.length; i++) {
        strTemp = strTemp.replace(",", "");
    }
    var m = strTemp.lastIndexOf(".");
    if (m == -1) {
        for (var i = strTemp.length; i >= 0; i--) {
            if (strResult.length > 0 && (strTemp.length - i - 1) % 3 == 0) {
                strResult = "," + strResult;
            }
            strResult = strTemp.substring(i, i + 1) + strResult;
        }
    } else {
        var strphannguyen = strTemp.substring(0, strTemp.lastIndexOf("."));
        var strphanthapphan = strTemp.substring(strTemp.lastIndexOf("."), strTemp.length);
        var tam = 0;
        for (var i = strphannguyen.length; i >= 0; i--) {

            if (strResult.length > 0 && tam == 4) {
                strResult = "," + strResult;
                tam = 1;
            }

            strResult = strphannguyen.substring(i, i + 1) + strResult;
            tam = tam + 1;
        }
        strResult = strResult + strphanthapphan;
    }
    return strResult;
}

function GetNumber(str) {
    var count = 0;
    for (var i = 0; i < str.length; i++) {
        var temp = str.substring(i, i + 1);
        if (!(temp == "," || temp == "." || (temp >= 0 && temp <= 9))) {
            alert(inputnumber);
            return str.substring(0, i);
        }
        if (temp == " ") {
            return str.substring(0, i);
        }
        if (temp == ".") {
            if (count > 0) {
                return str.substring(0, i);
            }
            count++;
        }
    }
    return str;
}
