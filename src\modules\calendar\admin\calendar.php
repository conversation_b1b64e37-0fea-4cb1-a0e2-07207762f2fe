<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    exit('Stop!!!');
}
$page_title = $nv_Lang->getModule('calendar_admin');

if ($nv_Request->get_title('changeweight','post,get', '') === NV_CHECK_SESSION) {
    $order_weight_new = $nv_Request->get_int('order_weight_new', 'post, get', 0);
    $id = $nv_Request->get_int('order_weight_id', 'post, get', 0);

    // Kiểm tra tồn tại
    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . " WHERE id=" . $id;
    $array = $db->query($sql)->fetch();
    if (empty($array)) {
        $res = [
            'res' => 'error',
            'mess' => $nv_Lang->getModule('error_required_id')
        ];
        nv_jsonOutput($res);
    }

    $sql = "SELECT id FROM " . NV_PREFIXLANG . "_" . $module_data . " WHERE id!=" . $id . " ORDER BY weight ASC";
    $result = $db->query($sql);

    $weight = 0;
    while ($row = $result->fetch()) {
        ++$weight;
        if ($weight == $order_weight_new) {
            ++$weight;
        }
        $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . " SET weight=" . $weight . " WHERE id=" . $row['id'];
        $db->query($sql);
    }

    $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . " SET weight=" . $order_weight_new . " WHERE id=" . $id;
    $db->query($sql);

    nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_EDIT_CALENDAR', json_encode($array), $admin_info['admin_id']);
    $nv_Cache->delMod($module_name);
    $res = [
        'res' => 'success',
        'mess' => $nv_Lang->getModule('changeweight_success')
    ];
    nv_jsonOutput($res);
}

if ($nv_Request->get_title('changestatus', 'post', '') === NV_CHECK_SESSION) {
    $id = $nv_Request->get_absint('id', 'post', 0);

    // Kiểm tra tồn tại
    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . " WHERE id=" . $id;
    $array = $db->query($sql)->fetch();
    if (empty($array)) {
        nv_htmlOutput('NO_' . $id);
    }

    $status = empty($array['status']) ? 1 : 0;

    $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . " SET status = " . $status . " WHERE id = " . $id;
    $db->query($sql);

    nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_CHANGE_STATUS_CALENDAR', json_encode($array), $admin_info['admin_id']);
    $nv_Cache->delMod($module_name);

    nv_htmlOutput("OK");
}

if ($nv_Request->get_title('delete', 'post', '') === NV_CHECK_SESSION) {
    $id = $nv_Request->get_absint('id', 'post', 0);

    // Kiểm tra tồn tại
    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . " WHERE id=" . $id;
    $array = $db->query($sql)->fetch();
    if (empty($array)) {
        nv_htmlOutput('NO_' . $id);
    }

    // Xóa
    $sql = "DELETE FROM " . NV_PREFIXLANG . "_" . $module_data . " WHERE id=" . $id;
    $db->query($sql);

    $sql = "DELETE FROM " . NV_PREFIXLANG . "_" . $module_data . "_event WHERE calendar_id=" . $id;
    $db->query($sql);

    // Cập nhật thứ tự
    $sql = "SELECT id FROM " . NV_PREFIXLANG . "_" . $module_data . " ORDER BY weight ASC";
    $result = $db->query($sql);
    $weight = 0;

    while ($row = $result->fetch()) {
        ++$weight;
        $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . " SET weight=" . $weight . " WHERE id=" . $row['id'];
        $db->query($sql);
    }

    nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_DELETE_CALENDAR', json_encode($array), $admin_info['admin_id']);
    $nv_Cache->delMod($module_name);

    nv_htmlOutput("OK");
}

$error = $array = [];
$is_submit_form = $is_edit = false;
$id = $nv_Request->get_int('id', 'get', 0);

if (!empty($id)) {
    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . " WHERE id = " . $id;
    $result = $db->query($sql);
    $array = $result->fetch();

    if (empty($array)) {
        nv_info_die($nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_content'));
    }

    $is_edit = true;
    $caption = $nv_Lang->getModule('calendar_edit');
    $form_action = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $id;
} else {
    $array = [
        'id' => 0,
        'name_calendar' => '',
        'description' => '',
        'color' => '',
        'is_calendar' => 0,
    ];
    $caption = $nv_Lang->getModule('calendar_add');
    $form_action = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
}

if ($nv_Request->get_title('save', 'post', '') === NV_CHECK_SESSION) {
    $is_submit_form = true;
    $array['name_calendar'] = nv_substr($nv_Request->get_title('name_calendar', 'post', ''), 0, 190);
    $array['color'] = $nv_Request->get_title('color', 'post', '');
    $array['description'] = $nv_Request->get_string('description', 'post', '');
    $array['is_calendar'] = $nv_Request->get_int('is_calendar', 'post', 0);

    // Xử lý dữ liệu
    $array['description'] = nv_nl2br(nv_htmlspecialchars(strip_tags($array['description'])), '<br />');

    if (empty($array['name_calendar'])) {
        $error[] = $nv_Lang->getModule('name_calendar_error');
    }

    if ($array['is_calendar'] <= 0) {
        $error[] = $nv_Lang->getModule('is_calendar_error');
    }

    $is_exists = false;
    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . " WHERE name_calendar = :name_calendar" . ($id ? ' AND id != ' . $id : '');
    $sth = $db->prepare($sql);
    $sth->bindParam(':name_calendar', $array['name_calendar'], PDO::PARAM_STR);
    $sth->execute();
    $count_name = $sth->rowCount();

    if ($count_name) {
        $error[] = $nv_Lang->getModule('name_calendar_error_dulipcate');
    }

    if (empty($error)) {
        try {
            if ($id) {
                $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . " SET
                    name_calendar = :name_calendar,
                    color = :color,
                    description = :description,
                    is_calendar = :is_calendar,
                    user_id = " . $admin_info['userid'] . ",
                    edit_time = " . NV_CURRENTTIME . "
                WHERE id=" . $id;
            } else {
                $sql = "SELECT MAX(weight) weight FROM " . NV_PREFIXLANG . "_" . $module_data;
                $weight = intval($db->query($sql)->fetchColumn()) + 1;

                $sql = "INSERT INTO " . NV_PREFIXLANG . "_" . $module_data . " (
                    name_calendar, color, description, is_calendar, user_id, add_time, edit_time, weight
                ) VALUES (
                    :name_calendar, :color, :description, :is_calendar," . $admin_info   ['userid'] . ", " . NV_CURRENTTIME . ", " . NV_CURRENTTIME . ", " . $weight . "
                )";
            }
            $stmt = $db->prepare($sql);
            $stmt->bindParam(':name_calendar', $array['name_calendar'], PDO::PARAM_STR);
            $stmt->bindParam(':color', $array['color'], PDO::PARAM_STR);
            $stmt->bindParam(':description', $array['description'], PDO::PARAM_STR, strlen($array['description']));
            $stmt->bindParam(':is_calendar', $array['is_calendar'], PDO::PARAM_INT);
            $stmt->execute();
            if ($id) {
                nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_EDIT_CALENDAR', 'id: ' . $id, $admin_info['admin_id']);
            } else {
                nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_ADD_CALENDAR', ' ', $admin_info['admin_id']);
            }
            $nv_Cache->delMod($module_name);
            nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);
        } catch (PDOException $e) {
            trigger_error($e->getMessage());
            $error[] = $nv_Lang->getModule('error_save');
        }
    }
}

$sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . " ORDER BY weight ASC";
$result = $db->query($sql);
$list = $result->fetchAll();
$total = sizeof($list);

$xtpl = new XTemplate('calendar.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('CAPTION', $caption);
$xtpl->assign('FORM_ACTION', $form_action);
$xtpl->assign('DATA', $array);

$type_calendar = [
    1 => $nv_Lang->getModule('is_calendar_import'),
    2 => $nv_Lang->getModule('is_redday'),
];

foreach ($type_calendar as $key => $value) {
    $xtpl->assign('TYPE', [
        'key' => $key,
        'value' => $value,
        'selected' => $key == $array['is_calendar'] ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.is_calendar');
}

if (!empty($list)) {
    foreach ($list as $row) {
        $row['add_time'] = nv_date('d/m/Y', $row['add_time']);
        $row['edit_time'] = nv_date('d/m/Y', $row['edit_time']);
        $row['is_calendar'] = $row['is_calendar'] == 1 ? $nv_Lang->getModule('is_calendar_import') : $nv_Lang->getModule('is_redday');
        $row['status_render'] = empty($row['status']) ? '' : ' checked="checked"';
        $row['url_edit'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $row['id'];
        $xtpl->assign('ROW', $row);
        $xtpl->parse('main.loop');
    }
}

if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

// Hiển thị nút thêm
if (!$is_edit) {
    $xtpl->parse('main.add_btn');
}

// Cuộn đến form
if ($is_submit_form or $is_edit) {
    $xtpl->parse('main.scroll');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
