<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\users\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class SetTmpOauthData implements IApi
{
    private $result;

    /**
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_GOD;
    }

    /**
     * @return string
     */
    public static function getCat()
    {
        return 'user';
    }

    /**
     * {@inheritDoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     * {@inheritDoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $nv_Request, $db, $db_config;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        $admin_id = Api::getAdminId();

        $attribs = $nv_Request->get_string('attribs', 'post', '', false, false);
        $attribs = empty($attribs) ? [] : json_decode($attribs, true);
        if (empty($attribs)) {
            return $this->result->setCode('0001')->setMessage('No valid data')->getResult();
        }

        $uniqid = uniqid(NV_CURRENTTIME, true);
        $sql = "INSERT INTO " . NV_USERS_GLOBALTABLE . "_gsi (
            uniqid, attribs, addtime
        ) VALUES (
            :uniqid, :attribs, :addtime
        )";
        $data = [
            'uniqid' => $uniqid,
            'attribs' => json_encode($attribs),
            'addtime' => NV_CURRENTTIME
        ];
        $id = $db->insert_id($sql, 'id', $data);
        if (empty($id)) {
            return $this->result->setCode('0001')->setMessage('Error store gsi data')->getResult();
        }

        // Tạo mã hash
        ksort($attribs);
        $hashdata = '';
        $i = 0;
        foreach ($attribs as $key => $value) {
            if ($i == 1) {
                $hashdata .= '&' . $key . '=' . $value;
            } else {
                $hashdata .= $key . '=' . $value;
                $i = 1;
            }
        }
        $secureHash = hash_hmac('sha512', $hashdata, SSO_REGISTER_SECRET);

        $this->result->set('hash', $secureHash);
        $this->result->set('uniqid', $uniqid);
        return $this->result->setSuccess()->getResult();
    }
}
