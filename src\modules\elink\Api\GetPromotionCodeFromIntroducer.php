<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\elink\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class GetPromotionCodeFromIntroducer implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'promotion';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $global_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        $userid = $nv_Request->get_absint('userid', 'post', 0);
        $code = $nv_Request->get_title('code', 'post', '');

        $this->result->setSuccess();

        // Lấy của người giới thiệu thành viên đó nếu có
        $promotion = [];

        if (!empty($userid)) {
            $sql = "SELECT tb3.* FROM " . $db_config['prefix'] . "_elink_affiliate_set tb1,
            " . NV_USERS_GLOBALTABLE . " tb2, " . $db_config['prefix'] . "_elink_promotion_code tb3
            WHERE tb1.pre_uid=" . $userid . " AND tb1.pri_uid=tb2.userid AND tb1.pri_uid=tb3.userid
            AND tb3.is_affiliate=1 AND tb2.active=1";
            $row = $db->query($sql)->fetch();
            if (!empty($row)) {
                $promotion = $row;
            }
        }

        // Lấy của UID hiện hành nếu có
        if (!empty($code) and empty($promotion)) {
            $sql = "SELECT tb2.* FROM " . NV_USERS_GLOBALTABLE . " tb1, " . $db_config['prefix'] . "_elink_promotion_code tb2
            WHERE tb1.userid=tb2.userid AND tb1.username=" . $db->quote($code) . " AND tb2.is_affiliate=1 LIMIT 1";
            $row = $db->query($sql)->fetch();
            if (!empty($row)) {
                $promotion = $row;
            }
        }

        if (empty($promotion)) {
            $this->result->set('data', []);
            return $this->result->getResult();
        }

        // Lấy số người đã sử dụng của mã khuyến mãi này
        $sql = "SELECT COUNT(DISTINCT use_userid) FROM " . $db_config['prefix'] . "_elink_promotion_code_use
        WHERE promo_code=" . $db->quote($promotion['promo_code']);
        $promotion['distinct_used'] = $db->query($sql)->fetchColumn() ?: 0;

        // Lấy số lần sử dụng mã giảm giá của người này
        $promotion['my_used'] = -1;
        if (!empty($userid)) {
            $sql = "SELECT COUNT(id) FROM " . $db_config['prefix'] . "_elink_promotion_code_use
            WHERE promo_code=" . $db->quote($promotion['promo_code']) . " AND use_userid=" . $userid;
            $promotion['my_used'] = $db->query($sql)->fetchColumn() ?: 0;
        }

        $this->result->set('data', $promotion);
        return $this->result->getResult();
    }
}
