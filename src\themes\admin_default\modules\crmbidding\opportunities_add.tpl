<!-- BEGIN: main -->
<!-- BEGIN: error -->
<div class="alert alert-warning">{ERROR}</div>
<!-- END: error -->
<div class="panel panel-default">
    <div class="panel-body">
        <form class="form-horizontal" action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
            <input type="hidden" name="leadsid" value="{LEADSID}" />
            <div class="col-md-12">
                <div class="form-group">
                    <label class="col-sm-5 col-md-5 control-label"><strong>{LANG.name}</strong> <span class="red">(*)</span></label>
                    <div class="col-sm-19 col-md-19">
                        <input class="form-control" type="text" name="name" value="{ROW.name}" required="required" oninvalid="setCustomValidity(nv_required)" oninput="setCustomValidity('')" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-5 col-md-5 control-label"><strong>{LANG.email}</strong> <span class="red">(*)</span><a class="order-edit-ection" href="#" data-toggle="tooltip" title="" data-original-title="Giá trị Số điện thoại hoặc email có thể nhập 1 trong 2"><i class="fa fa-info-circle" aria-hidden="true"></i></a></label>
                    <div class="col-sm-19 col-md-19">
                        <input class="form-control" type="text" name="email" value="{ROW.email}" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-5 col-md-5 control-label"><strong>{LANG.sub_email}</strong></label>
                    <div class="col-sm-19 col-md-19">
                        <input class="form-control" type="text" value="{ROW.sub_email}" name="sub_email" />
                    </div>
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.phone}</strong> <span class="red">(*)</span><a class="order-edit-ection" href="#" data-toggle="tooltip" title="" data-original-title="Giá trị Số điện thoại hoặc email có thể nhập 1 trong 2 {LANG.info_phone}"><i class="fa fa-info-circle" aria-hidden="true"></i></a></label>
                    <div class="col-sm-19 col-md-18">
                        <input class="form-control" type="text" name="phone" value="{ROW.phone}" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.sub_phone}</strong></label>
                    <div class="col-sm-19 col-md-18">
                        <input class="form-control" type="text" name="sub_phone" value="{ROW.sub_phone}" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.address}</strong></label>
                    <div class="col-sm-19 col-md-18">
                        <input class="form-control" type="text" name="address" value="{ROW.address}" />
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.tax}</strong></label>
                <div class="col-sm-19 col-md-20">
                    <input class="form-control" type="text" name="tax" value="{ROW.tax}" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.company_name}</strong></label>
                <div class="col-sm-19 col-md-20">
                    <input class="form-control" type="text" name="company_name" value="{ROW.company_name}" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.address_company}</strong></label>
                <div class="col-sm-19 col-md-20">
                    <input class="form-control" type="text" name="address_company" value="{ROW.address_company}" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-5 col-md-4 control-label"><strong>{LANG.about}</strong></label>
                <div class="col-sm-19 col-md-20">
                    <textarea class="form-control" name="about" rows="6">{ROW.about}</textarea>
                </div>
            </div>
            <div class="form-group">
            <label class="col-sm-5 col-md-6 control-label"><strong>{LANG.siteid}</strong></label>
            <div class="col-sm-19 col-md-18">
                <select class="form-control" name="siteid">
                    <!-- BEGIN: select_siteid -->
                    <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                    <!-- END: select_siteid -->
                </select>
                <br/>
                <select class="form-control inputinfo" name="prefix_lang">
                    <!-- BEGIN: select_prefix_lang -->
                    <option value="{OPTION.key}"{OPTION.selected}>{OPTION.title}</option>
                    <!-- END: select_prefix_lang -->
                </select>
            </div>
        </div>
            <div class="form-group" style="text-align: center">
                <input class="btn btn-primary" name="submit" type="submit" value="{LANG.save}" />
            </div>
        </form>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function() {
        // Xử lý xoá kí tự không phải số và dấu + trong mục điện thoại
        $('input[name="phone"]').on('change', function() {
            $(this).val($(this).val().replace(/[^0-9\+]/gm, ''));
        });
        $('input[name="sub_phone"]').on('change', function() {
            $(this).val($(this).val().replace(/[^0-9\+\,]/gm, ''));
        });
    });
</script>
<!-- END: main -->
