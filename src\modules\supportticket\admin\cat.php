<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$error = [];

// Change status
if ($nv_Request->isset_request('change_status', 'post, get')) {
    $cat_id = $nv_Request->get_int('cat_id', 'post, get', 0);
    $content = 'NO_' . $cat_id;

    $query = 'SELECT active FROM ' . TB_TICKET_CAT . ' WHERE cat_id=' . $cat_id;
    $row = $db->query($query)->fetch();
    if (isset($row['active'])) {
        $active = ($row['active']) ? 0 : 1;
        $query = 'UPDATE ' . TB_TICKET_CAT . ' SET active=' . intval($active) . ' WHERE cat_id=' . $cat_id;
        $db->query($query);
        $content = 'OK_' . $cat_id;
    }
    $nv_Cache->delMod($module_name);
    include NV_ROOTDIR . '/includes/header.php';
    echo $content;
    include NV_ROOTDIR . '/includes/footer.php';
}

if ($nv_Request->isset_request('ajax_action', 'post')) {
    $cat_id = $nv_Request->get_int('cat_id', 'post', 0);
    $new_vid = $nv_Request->get_int('new_vid', 'post', 0);
    $content = 'NO_' . $cat_id;
    if ($new_vid > 0) {
        $sql = 'SELECT cat_id FROM ' . TB_TICKET_CAT . ' WHERE cat_id!=' . $cat_id . ' ORDER BY weight ASC';
        $result = $db->query($sql);
        $weight = 0;
        while ($row = $result->fetch()) {
            ++$weight;
            if ($weight == $new_vid) {
                ++$weight;
            }
            $sql = 'UPDATE ' . TB_TICKET_CAT . ' SET weight=' . $weight . ' WHERE cat_id=' . $row['cat_id'];
            $db->query($sql);
        }
        $sql = 'UPDATE ' . TB_TICKET_CAT . ' SET weight=' . $new_vid . ' WHERE cat_id=' . $cat_id;
        $db->query($sql);
        $content = 'OK_' . $cat_id;
    }
    $nv_Cache->delMod($module_name);
    include NV_ROOTDIR . '/includes/header.php';
    echo $content;
    include NV_ROOTDIR . '/includes/footer.php';
}

if ($nv_Request->isset_request('delete_cat_id', 'get') and $nv_Request->isset_request('delete_checkss', 'get')) {
    $cat_id = $cat_id_log = $nv_Request->get_int('delete_cat_id', 'get');
    $delete_checkss = $nv_Request->get_string('delete_checkss', 'get');

    //Không cho xóa bộ phận nếu đã có ticket liên quan
    $sth = $db->query('SELECT COUNT(id) FROM ' . TB_TICKET_ROW . ' WHERE cat_id='. $cat_id . ' LIMIT 1');
    $have_ticket = $sth->fetchColumn();
    if ($cat_id > 0 and $delete_checkss == md5($cat_id . NV_CACHE_PREFIX . $client_info['session_id']) && $have_ticket == 0) {
        $weight = 0;
        $sql = 'SELECT weight FROM ' . TB_TICKET_CAT . ' WHERE cat_id =' . $db->quote($cat_id);
        $result = $db->query($sql);
        list($weight) = $result->fetch(3);

        $db->query('DELETE FROM ' . TB_TICKET_CAT . ' WHERE cat_id = ' . $db->quote($cat_id));
        if ($weight > 0)         {
            $sql = 'SELECT cat_id, weight FROM ' . TB_TICKET_CAT . ' WHERE weight >' . $weight;
            $result = $db->query($sql);
            while (list($cat_id, $weight) = $result->fetch(3)) {
                $weight--;
                $db->query('UPDATE ' . TB_TICKET_CAT . ' SET weight=' . $weight . ' WHERE cat_id=' . intval($cat_id));
            }
        }
        $nv_Cache->delMod($module_name);
        nv_insert_logs(NV_LANG_DATA, $module_name, 'Delete Cat', 'ID: ' . $cat_id_log, $admin_info['userid']);
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    } else {
        $error[] = $nv_Lang->getModule('error_cannot_del_cat');
    }
}

$row = [];
$row['cat_id'] = $nv_Request->get_int('cat_id', 'post,get', 0);
if ($nv_Request->isset_request('submit', 'post')) {
    $row['title_vi'] = nv_substr($nv_Request->get_title('title_vi', 'post', ''), 0, 249);
    $row['title_en'] = nv_substr($nv_Request->get_title('title_en', 'post', ''), 0, 249);
    $row['is_customer'] = $nv_Request->get_int('is_customer', 'post', 0);
    $row['assignee_sale'] = $nv_Request->get_int('assignee_sale', 'post', 0);
    $row['is_point'] = $nv_Request->get_int('is_point', 'post', 0);
    $row['point_price'] = $nv_Request->get_int('point_price', 'post', 0);
    $row['point_ai'] = $nv_Request->get_int('point_ai', 'post', 0);
    $row['bonus_point'] = $nv_Request->get_int('bonus_point', 'post', 0); // Điểm thưởng khi báo cáo lỗi

    if (empty($row['title_vi'])) {
        $error[] = $nv_Lang->getModule('error_required_title_vi');
    } elseif (empty($row['title_en'])) {
        $error[] = $nv_Lang->getModule('error_required_title_en');
    }

    if (empty($error)) {
        try {
            if (empty($row['cat_id'])) {
                $stmt = $db->prepare('INSERT INTO ' . TB_TICKET_CAT . ' (title_vi, title_en, is_customer, assignee_sale, is_point, point_price, point_ai, weight, active, bonus_point)
                    VALUES (:title_vi, :title_en, :is_customer, :assignee_sale, :is_point, :point_price, :point_ai, :weight, :active, :bonus_point)');

                $weight = $db->query('SELECT max(weight) FROM ' . TB_TICKET_CAT)->fetchColumn();
                $weight = intval($weight) + 1;
                $stmt->bindParam(':weight', $weight, PDO::PARAM_INT);

                $stmt->bindValue(':active', 1, PDO::PARAM_INT);


            } else {
                $stmt = $db->prepare('UPDATE ' . TB_TICKET_CAT . ' SET title_vi = :title_vi, title_en = :title_en, is_customer = :is_customer, is_point = :is_point,
                 point_price = :point_price, point_ai = :point_ai, assignee_sale = :assignee_sale, bonus_point=:bonus_point WHERE cat_id=' . $row['cat_id']);
            }
            $stmt->bindParam(':title_vi', $row['title_vi'], PDO::PARAM_STR);
            $stmt->bindParam(':title_en', $row['title_en'], PDO::PARAM_STR);
            $stmt->bindParam(':is_customer', $row['is_customer'], PDO::PARAM_INT);
            $stmt->bindParam(':assignee_sale', $row['assignee_sale'], PDO::PARAM_INT);
            $stmt->bindParam(':is_point', $row['is_point'], PDO::PARAM_INT);
            $stmt->bindParam(':point_price', $row['point_price'], PDO::PARAM_INT);
            $stmt->bindParam(':point_ai', $row['point_ai'], PDO::PARAM_INT);
            $stmt->bindParam(':bonus_point', $row['bonus_point'], PDO::PARAM_INT);

            $exc = $stmt->execute();
            $id = $db->lastInsertId();
            if ($exc) {
                $nv_Cache->delMod($module_name);
                if (empty($row['cat_id'])) {
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Add Cat', 'ID: ' .$id, $admin_info['userid']);
                } else {
                    nv_insert_logs(NV_LANG_DATA, $module_name, 'Edit Cat', json_encode($row), $admin_info['userid']);
                }
                nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
            }
        } catch(PDOException $e) {
            trigger_error($e->getMessage());
        }
    }
} elseif ($row['cat_id'] > 0) {
    $row = $db->query('SELECT * FROM ' . TB_TICKET_CAT . ' WHERE cat_id=' . $row['cat_id'])->fetch();
    if (empty($row)) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=' . $op);
    }
} else {
    $row['cat_id'] = 0;
    $row['title_vi'] = '';
    $row['title_en'] = '';
    $row['is_point'] = 0;
    $row['is_customer'] = 0;
    $row['assignee_sale'] = 0;
    $row['point_price'] = 0;
    $row['point_ai'] = 0;
    $row['bonus_point'] = 0;
}

// Fetch Limit
$show_view = false;
if (!$nv_Request->isset_request('id', 'post,get')) {
    $show_view = true;
    $per_page = 20;
    $page = $nv_Request->get_int('page', 'post,get', 1);
    $db->sqlreset()
        ->select('COUNT(*)')
        ->from(TB_TICKET_CAT);
    $sth = $db->prepare($db->sql());
    $sth->execute();
    $num_items = $sth->fetchColumn();

    $db->select('*')
        ->order('weight ASC')
        ->limit($per_page)
        ->offset(($page - 1) * $per_page);
    $sth = $db->prepare($db->sql());
    $sth->execute();
}

$xtpl = new XTemplate('cat.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('ROW', $row);
$xtpl->assign('CAPTION', $row['cat_id'] > 0 ? $nv_Lang->getModule('edit_cat') : $nv_Lang->getModule('add_cat'));


if ($show_view) {
    $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.view.generate_page');
    }
    $number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;
    while ($view = $sth->fetch()) {
        for($i = 1; $i <= $num_items; ++$i) {
            $xtpl->assign('WEIGHT', [
                'key' => $i,
                'title' => $i,
                'selected' => ($i == $view['weight']) ? ' selected="selected"' : ''
            ]);
            $xtpl->parse('main.view.loop.weight_loop');
        }
        $xtpl->assign('CHECK', $view['active'] == 1 ? 'checked' : '');
        $view['link_user'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=cat_admins&amp;cat_id=' . $view['cat_id'];
        $view['link_edit'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;cat_id=' . $view['cat_id'];
        $view['link_delete'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;delete_cat_id=' . $view['cat_id'] . '&amp;delete_checkss=' . md5($view['cat_id'] . NV_CACHE_PREFIX . $client_info['session_id']);
        $view['is_customer'] = $view['is_customer'] == 1 ? $nv_Lang->getModule('is_point_yes') : $nv_Lang->getModule('is_point_no');
        $view['is_point'] = $view['is_point'] == 1 ? $nv_Lang->getModule('is_point_yes') : $nv_Lang->getModule('is_point_no');
        $view['assignee_sale'] = $view['assignee_sale'] == 1 ? $nv_Lang->getModule('is_point_yes') : $nv_Lang->getModule('is_point_no');
        $xtpl->assign('VIEW', $view);
        $xtpl->parse('main.view.loop');
    }
    $xtpl->parse('main.view');
}


if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

if (isset($row['is_customer'])) {
    $xtpl->assign('OPTION_CUTOMER', ['key' => 0, 'title' => $nv_Lang->getModule('is_point_no'), 'checked' => $row['is_customer'] ==0 ? ' checked' : '']);
    $xtpl->parse('main.radio_is_customer');
    $xtpl->assign('OPTION_CUTOMER', ['key' => 1, 'title' => $nv_Lang->getModule('is_point_yes'), 'checked' => $row['is_customer'] ==1 ? ' checked' : '']);
    $xtpl->parse('main.radio_is_customer');
}

if (isset($row['is_point'])) {
    $xtpl->assign('OPTION', ['key' => 0, 'title' => $nv_Lang->getModule('is_point_no'), 'checked' => $row['is_point'] ==0 ? ' checked' : '']);
    $xtpl->parse('main.radio_is_point');
    $xtpl->assign('OPTION', ['key' => 1, 'title' => $nv_Lang->getModule('is_point_yes'), 'checked' => $row['is_point'] ==1 ? ' checked' : '']);
    $xtpl->parse('main.radio_is_point');
}
if (isset($row['assignee_sale'])) {
    $xtpl->assign('OPTION_ASSIGNEE', ['key' => 0, 'title' => $nv_Lang->getModule('is_point_no'), 'checked' => $row['assignee_sale'] ==0 ? ' checked' : '']);
    $xtpl->parse('main.radio_assignee_sale');
    $xtpl->assign('OPTION_ASSIGNEE', ['key' => 1, 'title' => $nv_Lang->getModule('is_point_yes'), 'checked' => $row['assignee_sale'] ==1 ? ' checked' : '']);
    $xtpl->parse('main.radio_assignee_sale');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('cat');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
