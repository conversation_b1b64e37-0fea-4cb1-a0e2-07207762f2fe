<?php
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));

require NV_CONSOLE_DIR . '/server.php';
require NV_ROOTDIR . '/includes/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

function slack_escape($str)
{
    return str_replace([
        '&',
        '<',
        '>',
        '\\',
        '"'
    ], [
        '&amp;',
        '&lt;',
        '&gt;',
        '\\\\',
        '\"'
    ], $str);
}

$directory = NV_ROOTDIR . '/data/errorlog';
if ($dh = opendir($directory)) {
    while (($file_name = readdir($dh)) !== false) {
        if (preg_match('/([a-z0-9\.\-]+)\_(.*).log$/', $file_name, $m)) {

            $file_uniqid = $file_name . '_' . uniqid('', true);
            if (rename($directory . '/' . $file_name, $directory . '/analysis/' . $file_uniqid)) {
                $server = $m[1];
                echo $file_name . "\n";
                $file_delete = true;
                $file_content = file_get_contents($directory . '/analysis/' . $file_uniqid);
                $errors = explode($ErrorHandler::LOG_DELIMITER, $file_content);
                $errors = array_map('trim', $errors);
                $errors = array_filter($errors);

                foreach ($errors as $item) {
                    $item = json_decode($item, true);
                    /*
                        Tại /includes/core/flood_blocker.php
                            Hệ thống đang từ chối truy cập của bạn
                            Access denied
                        /includes/mainfile.php
                            Hi and Good-bye!!!
                            ERROR: You are behind a proxy server. Please disconnect and come again!
                            The software you are using to access our website is not allowed
                        /includes/vendor/vinades/nukeviet/Core/Request.php
                            Incorrect IP address specified
                            Your request is blocked
                            Incorrect Origin specified
                    */
                    if ($item['errstr'] == 'Your request is blocked') { // Bỏ qua lỗi bị blocked
                        continue;
                    } elseif (strpos($item['errstr'], 'từ chối truy cập của bạn') !== false  or strpos($item['errstr'], 'access our website is not allowed') !== false) { // Bỏ qua lỗi
                        continue;
                    }

                    $item['time'] = strtotime($item['time']);
                    $item['errno'] = intval(preg_replace('/\(.*\)/', '', $item['errno']));
                    $item['errstr'] = preg_replace('/elastic\:([a-z0-9\_\-]+)\@([a-z0-9\_]+)\:9200/i', '$2:9200', $item['errstr']);
                    $item['backtrace'] = !empty($item['backtrace']) ? json_encode($item['backtrace'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) : '';
                    if (!isset($item['line'])) {
                        $item['line'] = 0;
                    }
                    try {
                        $sql = ("INSERT INTO `nv4_errorlog` (`id`, `server`, `time`, `ip`,
                            `errno`, `errstr`, `file`, `line`,
                            `request`, `method`, `agent`, `backtrace`) VALUES
                           (NULL, " . $db->quote($server) . ", " . intval($item['time']) . ", " . $db->quote($item['ip']) . ",
                            " . $db->quote($item['errno']) . ", " . $db->quote($item['errstr']) . ", " . $db->quote($item['file']) . ", " . $db->quote($item['line']) . ",
                            " . $db->quote($item['request']) . ", " . $db->quote($item['method']) . ", " . $db->quote($item['agent']) . ", " . $db->quote($item['backtrace']) . ")");
                        $db->exec($sql);
                        $id = $db->lastInsertId();

                        if ($item['errno'] == 1 or $item['errno'] == 256 or strpos($item['errstr'], 'PDOException') !== false  or strpos($item['errstr'], 'Elasticsearch\Exception') !== false) {
                            $message = "Lỗi trang " . $server . "\n";
                            $message .= "*ID:* " . $id . "\n";
                            $message .= "*Time:* " . date('d/m/Y H:i:s P', $item['time']) . "\n";
                            $message .= "*File:* " . $item['file'] . " \t Line: " . $item['line'] . "\n";
                            if (!empty($item['method'])) {
                                $message .= "*Method:* " . $item['method'] . "\n";
                            }
                            if (!empty($item['agent'])) {
                                $message .= "*Agent:* " . $item['agent'] . "\n";
                            }
                            if (!empty($item['request'])) {
                                $message .= "*Request:* " . $item['request'] . "\n";
                            }
                            $message .= "*Error Message:* " . slack_escape($item['errstr']) . "\n";
                            if (!empty($item['backtrace'])) {
                                $message .= "```" . slack_escape($item['backtrace']) . "```";
                            }

                            $array = array(
                                "channel" => NV_CHANNEL_SLACK,
                                "text" => urlencode($message),
                                "icon_emoji" => ':ghost'
                            );

                            $data = "payload=" . json_encode($array);
                            $ch = curl_init(NV_API_SLACK);
                            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
                            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                            $slack = curl_exec($ch);
                            curl_close($ch);

                            $db->exec('UPDATE `nv4_errorlog` SET `slack` = ' . $db->quote($slack) . ' WHERE `id` = ' . $id);
                            echo "slack: " . $slack . "\n";
                        }
                    } catch (PDOException $e) {
                        $file_delete = false;
                        file_put_contents(NV_CONSOLE_DIR . '/analysis_sql.log', $sql . "\n\n", FILE_APPEND);
                        file_put_contents(NV_CONSOLE_DIR . '/analysis_errorlog.log', $file_uniqid . "\n" . print_r($e, true) . "\n\n", FILE_APPEND);

                        $data = "payload=" . json_encode(array(
                            "channel" => NV_CHANNEL_SLACK,
                            "text" => urlencode("analysis_errorlog PDOException: " . $e->getMessage()),
                            "icon_emoji" => ':ghost'
                        ));

                        $ch = curl_init(NV_API_SLACK);
                        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
                        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_exec($ch);
                        curl_close($ch);
                    }
                }
                // Xóa file nếu ko có lỗi
                if ($file_delete) {
                    unlink($directory . '/analysis/' . $file_uniqid);
                }
            }
        }
    }
    closedir($dh);
}

echo "\n<br><br>Cập nhật trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
die("Thống kê xong");
//SELECT `server`, `file`, `errno`, COUNT(*) FROM `nv4_errorlog` GROUP BY `server`, `file` , `errno` ORDER BY `nv4_errorlog`.`errno` ASC
