<!-- BEGIN: main -->
<link type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css" rel="stylesheet" />
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<form action="{NV_BASE_ADMINURL}index.php" method="get">
    <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" />
    <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" />
    <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
    <div class="row">
        <div class="col-sm-12 col-md-6">
            <div class="form-group">
                <label for="ipt_q"><strong>{LANG.keywords}:</strong></label>
                <input id="ipt_q" class="form-control" type="text" value="{SEARCH.q}" name="q" maxlength="255" placeholder="{LANG.search_title}">
            </div>
        </div>
        <div class="col-sm-12 col-md-6">
            <div class="form-group">
                <label for="ipt_job_id"><strong>{LANG.from_date}:</strong></label>
                <input class="form-control my-datepicker" type="text" name="f" value="{SEARCH.from}" autocomplete="off" placeholder="dd-mm-yyyy">
            </div>
        </div>
        <div class="col-sm-12 col-md-6">
            <div class="form-group">
                <label for="ipt_api_status"><strong>{LANG.to}:</strong></label>
                <input class="form-control my-datepicker" type="text" name="t" value="{SEARCH.to}" autocomplete="off" placeholder="dd-mm-yyyy">
            </div>
        </div>
        <div class="col-sm-12 col-md-6">
            <div class="form-group form-group-label">
                <button class="btn btn-primary" type="submit"><i class="fa fa-search"></i> {LANG.search_submit}</button>
            </div>
        </div>
    </div>
</form>
<script type="text/javascript">
$(document).ready(function() {
    $('.my-datepicker').datepicker({
        dateFormat: "dd-mm-yy",
        changeMonth: true,
        changeYear: true,
        showOtherMonths: true,
        showOn: 'focus'
    });
});
</script>
<form>
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th style="width: 5%" class="text-center"><input name="check_all[]" type="checkbox" value="yes" onclick="nv_checkAll(this.form, 'idcheck[]', 'check_all[]',this.checked);"></th>
                    <th style="width: 15%" class="text-nowrap">{LANG.ip}</th>
                    <th style="width: 15%" class="text-nowrap">{LANG.telelog_event_type}</th>
                    <th style="width: 15%" class="text-nowrap">{LANG.name_customer}</th>
                    <th style="width: 15%" class="text-nowrap">{LANG.phone}</th>
                    <th style="width: 15%" class="text-nowrap">{LANG.nhan_vien}</th>
                    <th style="width: 15%" class="text-nowrap">{LANG.thoi_gian}</th>
                    <th style="width: 10%" class="text-nowrap text-center">{LANG.telejob_function}</th>
                </tr>
            </thead>
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="text-center">
                        <input type="checkbox" onclick="nv_UncheckAll(this.form, 'idcheck[]', 'check_all[]', this.checked);" value="{ROW.id}" name="idcheck[]">
                    </td>
                    <td><a href="#" data-toggle="viewgetpost" data-id="{ROW.id}">{ROW.remote_ip}</a></td>
                    <td>{ROW.event_type}</td>
                    <td>{ROW.fullname}</td>
                    <td>{ROW.phone}</td>
                    <td>{ROW.agent_fullname}</td>
                    <td>{ROW.log_time}</td>
                    <td class="text-center text-nowrap">
                        <a href="javascript:void(0);" onclick="nv_delele_telepro_logs('{ROW.id}');" class="btn btn-xs btn-danger"><i class="fa fa-trash"></i> {GLANG.delete}</a>
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
            <!-- BEGIN: generate_page -->
            <tfoot>
                <tr>
                    <td colspan="7">
                        {GENERATE_PAGE}
                    </td>
                </tr>
            </tfoot>
            <!-- END: generate_page -->
        </table>
    </div>
    <div class="form-group form-inline">
        <div class="form-group">
            <select class="form-control" id="action-of-telepro-logs">
                <option value="delete">{GLANG.delete}</option>
            </select>
        </div>
        <button type="button" class="btn btn-primary" onclick="nv_telepro_logs_action(this.form, '{NV_CHECK_SESSION}', '{LANG.msgnocheck}')">{GLANG.submit}</button>
    </div>
</form>
<!-- START FORFOOTER -->
<div class="modal" tabindex="-1" role="dialog" id="mdRequestData">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" onclick="modalHide();" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><span class="h1">{LANG.telelog_detail}</span></h4>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>
<!-- END FORFOOTER -->
<script type="text/javascript">
$(document).ready(function() {
    var md = $('#mdRequestData');

    // Xem chi tiết post get
    $('[data-toggle="viewgetpost"]').on('click', function(e) {
        e.preventDefault();
        md.data('id', $(this).data('id'));
        md.modal('show');
    });

    md.on('show.bs.modal', function() {
        $('.modal-body', md).html('<div class="text-center"><i class="fa fa-spin fa-spinner fa-2x"></i></div>');

        $.ajax({
            type: 'POST',
            url: script_name + '?' + nv_lang_variable + '=' + nv_lang_data + '&' + nv_name_variable + '=' + nv_module_name + '&' + nv_fc_variable + '={OP}&nocache=' + new Date().getTime(),
            data: {
                viewdtail: 1,
                id: md.data('id'),
            },
            dataType: 'json',
            cache: false,
            success: function(respon) {
                $('.modal-body', md).html(respon.text);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert('Request Error!!!');
                console.log(jqXHR, textStatus, errorThrown);
            }
        });
    });
});
</script>
<!-- END: main -->
