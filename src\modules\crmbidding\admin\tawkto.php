<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
$showheader = $nv_Request->get_int('showheader', 'post,get', 1);
$error = [];

$q = $nv_Request->get_title('q', 'post,get');
$status = $nv_Request->get_title('status', 'post,get', -1);
$orderby_hoten = $nv_Request->get_int('orderby_hoten', 'post,get', 0);
$orderby_time = $nv_Request->get_int('orderby_time', 'post,get', 0);
// Fetch Limit
$per_page = 50;
$page = $nv_Request->get_int('page', 'post,get', 1);
$db->sqlreset()
    ->select('COUNT(*)')
    ->from('' . NV_PREFIXLANG . '_' . $module_data . '_tawkto tb1');
$where = [];
if (!empty($q)) {
    $where[] = '(hoten LIKE :q_hoten OR sdt LIKE :q_sdt OR email LIKE :q_email)';
}
if (!empty($status)) {
    if ($status == 1) {
        $where[] = "EXISTS (SELECT id FROM nv4_vi_crmbidding_leads tb2 WHERE (tb1.email = tb2.email AND tb2.email != '') OR (tb2.phone = tb1.sdt))";
    }
    if ($status == 2) {
        $where[] = "NOT EXISTS (SELECT id FROM nv4_vi_crmbidding_leads tb2 WHERE (tb1.email = tb2.email AND tb2.email != '') OR (tb2.phone = tb1.sdt))";
    }
}
if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}

$sth = $db->prepare($db->sql());

if (!empty($q)) {
    $sth->bindValue(':q_hoten', '%' . $q . '%');
    $sth->bindValue(':q_sdt', '%' . $q . '%');
    $sth->bindValue(':q_email', '%' . $q . '%');
}
$sth->execute();
$num_items = $sth->fetchColumn();
$order = 'thoi_gian_gui DESC';
if ($orderby_hoten > 0) {
    $order = $orderby_hoten == 1 ? 'hoten ASC' : 'hoten DESC';
}
if ($orderby_time > 0) {
    $order = $orderby_time == 1 ? 'thoi_gian_gui ASC' : 'thoi_gian_gui DESC';
}
$db->select('*')
    ->order($order)
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$sth = $db->prepare($db->sql());

if (!empty($q)) {
    $sth->bindValue(':q_hoten', '%' . $q . '%');
    $sth->bindValue(':q_sdt', '%' . $q . '%');
    $sth->bindValue(':q_email', '%' . $q . '%');
}

$sth->execute();
$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('Q', $q);
$xtpl->assign('LINK_DUPLICATE', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=duplicate&type=2');

$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&showheader=' . $showheader;
if (!empty($q)) {
    $base_url .= '&q=' . $q;
}
if (!empty($status)) {
    $base_url .= '&status=' . $status;
}
$link_orderby_hoten = $base_url . '&orderby_hoten=1';
$link_orderby_time = $base_url . '&orderby_time=1';
if ($orderby_hoten > 0) {
    if ($orderby_hoten == 1) {
        $link_orderby_hoten_desc = $base_url . '&orderby_hoten=2' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_HOTEN_DESC', $link_orderby_hoten_desc);
        $xtpl->assign('ORDER_BY_HOTEN', $link_orderby_hoten_desc);
        $xtpl->parse('main.hoten.desc');
    } else {
        $link_orderby_hoten_asc = $base_url . '&orderby_hoten=1' . '&page=' . $page;
        $xtpl->assign('ORDER_BY_HOTEN_ASC', $link_orderby_hoten_asc);
        $xtpl->assign('ORDER_BY_HOTEN', $link_orderby_hoten_asc);
        $xtpl->parse('main.hoten.asc');
    }
    $xtpl->parse('main.hoten');
    $base_url .= '&orderby_hoten=' . $orderby_hoten;
} else {
    $xtpl->assign('ORDER_BY_HOTEN', $link_orderby_hoten . '&page=' . $page);
}

if ($orderby_time == 1 or $orderby_time == 0) {
    $link_orderby_time_desc = $base_url . '&orderby_time=2' . '&page=' . $page;
    $xtpl->assign('ORDER_BY_TIME_DESC', $link_orderby_time_desc);
    $xtpl->assign('ORDER_BY_TIME', $link_orderby_time_desc);
    $xtpl->parse('main.updatetime.desc');
} else {
    $link_orderby_time_asc = $base_url . '&orderby_time=1' . '&page=' . $page;
    $xtpl->assign('ORDER_BY_TIME_ASC', $link_orderby_time_asc);
    $xtpl->assign('ORDER_BY_TIME', $link_orderby_time_asc);
    $xtpl->parse('main.updatetime.asc');
}
$xtpl->parse('main.updatetime');
$base_url .= '&orderby_time=' . $orderby_time;
$xtpl->parse('main.status');
$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
if (!empty($generate_page)) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}
$number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;
while ($view = $sth->fetch()) {
    if (!empty($view['sdt']) or !empty($view['email'])) {
        $_sql = "SELECT id,name,phone,email FROM " . NV_PREFIXLANG . "_" . $module_data . "_leads";
        $where_duplicate = [];
        $num_items_duplicate = "";
        if (!empty($view['email'])) {
            $where_duplicate[] = "email = " . $db->quote($view['email']) . "";
        }
        if (!empty($view['sdt'])) {
            $where_duplicate[] = "phone = " . $db->quote($view['sdt']);
            $_tmp_phone = $view['sdt'];
            if (preg_match('/(\d{9})$/', $_tmp_phone, $m)) {
                $_tmp_phone = $m[0];
            }
            $where_duplicate[] = "phone_search = " . $db->quote($_tmp_phone);
        }
        $_sql .= " WHERE (" . implode(' OR ', $where_duplicate) . ") ORDER BY id DESC";
        $result = $db->query($_sql);
        $items_duplicate = $result->fetchAll();
        if (sizeof($items_duplicate) > 0) {
            $xtpl->assign('STATUS_LEADS', $nv_Lang->getModule('da_tao_lead'));
            foreach ($items_duplicate as $item) {
                $xtpl->assign('LINK_LEADS', NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=' . 'leads_info&id=' . $item['id'] . '&showheader=' . $showheader);
                $xtpl->assign('NAME_LEADS', $item['name']);
                $xtpl->parse('main.loop.link_leads');
            }
        } else {
            $xtpl->assign('STATUS_LEADS', $nv_Lang->getModule('chua_tao_lead'));
        }
        $xtpl->parse('main.loop.status_leads');
    }
    $view['number'] = $number++;
    $view['thoi_gian_gui'] = nv_date("H:m d/m/y", $view['thoi_gian_gui']);
    $xtpl->assign('VIEW', $view);
    $xtpl->parse('main.loop');
}

$array_status_tawk = [
    '1' => $nv_Lang->getModule('da_tao_lead'),
    '2' => $nv_Lang->getModule('chua_tao_lead')
];
foreach ($array_status_tawk as $key => $value) {
    $xtpl->assign('STATUS', array(
        'id' => $key,
        'title' => $value,
        'selected' => $key == $status ? 'selected="selected"' : ''
    ));
    $xtpl->parse('main.search.status');
}

if ($showheader) {
    $xtpl->parse('main.search');
}

if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');
$page_title = $nv_Lang->getModule('ds_khach_chat');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';
