<!-- BEGIN: main -->
<script type="text/javascript" src="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript" src="{ASSETS_LANG_STATIC_URL}/js/language/jquery.ui.datepicker-{NV_LANG_INTERFACE}.js"></script>
<link rel="stylesheet" type="text/css" href="{ASSETS_STATIC_URL}/js/jquery-ui/jquery-ui.min.css">

<!-- BEGIN: form -->
<div class="well">
    <form action="{NV_BASE_ADMINURL}index.php" method="get">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" />
        <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" />
        <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
        <div class="row">
            <div class="col-xs-24 col-md-4">
                <div class="form-group">
                    <input class="form-control" type="text" value="{Q}" name="q" maxlength="255" placeholder="{LANG.search_title}" />
                </div>
            </div>
               <div class="form-group col-md-5">
                <div class="input-group w150">
                    <input class="form-control" type="text" placeholder="Từ ngày" id="from_time" name="from" value="{FROM_TIME}" readonly="readonly" />
                    <span class="input-group-btn">
                        <button class="btn btn-default" type="button" id="from_time_btn">
                            <em class="fa fa-calendar fa-fix">&nbsp;</em>
                        </button>
                    </span>
                </div>
            </div>
            <div class="form-group col-md-5">
                <div class="input-group w150">
                    <input class="form-control" type="text" placeholder="Đến ngày" id="to_time" name="to" value="{TO_TIME}" readonly="readonly" />
                    <span class="input-group-btn">
                        <button class="btn btn-default" type="button" id="to_time_btn">
                            <em class="fa fa-calendar fa-fix">&nbsp;</em>
                        </button>
                    </span>
                </div>
            </div>
            <div class="col-xs-12 col-md-3">
                <div class="form-group">
                    <input class="btn btn-primary" type="submit" value="{LANG.search_submit}" />
                </div>
            </div>
        </div>
    </form>
</div>
<!-- END: form -->
<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th class="w50">{LANG.phieu_ghi}</th>
                    <th class="w100">{LANG.sdt}</th>
                    <th class="w100">{LANG.chuyen_vien}</th>
                    <th class="w100">{LANG.loai_cuoc_goi}</th>
                    <th class="w150 text-center">{LANG.nhanh}</th>
                    <th class="w150">{LANG.thoi_diem_bat_dau}</th>
                    <th class="w100">{LANG.thoi_diem_ket_thuc}</th>
                    <th>{LANG.wait_time}</th>
                    <th>{LANG.thoi_gian_dam_thoai}</th>
                    <th>{LANG.trang_thai_cuoc_goi}</th>
                    <th>{LANg.trang_thai_ket_thuc}</th>
                    <th>{LANG.record}</th>
                </tr>
            </thead>
            <!-- BEGIN: generate_page -->
            <tfoot>
                <tr>
                    <td class="text-center" colspan="9">{NV_GENERATE_PAGE}</td>
                </tr>
            </tfoot>
            <!-- END: generate_page -->
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td>{VIEW.phieu_ghi}</td>
                    <td>{VIEW.sdt}</td>
                    <td>{VIEW.chuyen_vien}</td>
                    <td>{VIEW.loai_cuoc_goi}</td>
                    <td class="text-center">{VIEW.nhanh}</td>
                    <td>{VIEW.thoi_gian_bat_dau}</td>
                    <td>{VIEW.thoi_gian_ket_thuc}</td>
                    <td>{VIEW.thoi_gian_cho}</td>
                    <td>{VIEW.thoi_gian_dam_thoai}</td>
                    <td>{VIEW.trang_thai_cuoc_goi}</td>
                    <td>{VIEW.trang_thai_ket_thuc}</td>
                    <td>
                        <!-- BEGIN: recording -->
                        <audio controls>
                            <source src="{VIEW.link}" type="audio/mpeg">
                            Your browser does not support the audio element.
                        </audio>
                        <!-- END: recording -->
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
</form>
<script>
    $("#from_time,#to_time").datepicker({
        dateFormat : "dd/mm/yy",
        changeMonth : true,
        changeYear : true,
        showOtherMonths : true,
        yearRange : '2018:2030',
        showOn : 'focus'
    });

    $('#from_time_btn').click(function() {
        $("#from_time").datepicker('show');
    });

    $('#to_time_btn').click(function() {
        $("#to_time").datepicker('show');
    })
</script>
<!-- END: main -->
