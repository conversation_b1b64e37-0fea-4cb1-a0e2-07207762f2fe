<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 16 Jan 2018 07:25:26 GMT
 */
if (!defined('NV_IS_FILE_ADMIN'))
    die('Stop!!!');

$page_title = $table_caption = $nv_Lang->getModule('acountuser');

$page = $nv_Request->get_int('page', 'get', 1);
$per_page = 30;
$base_url = NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name;

$q = $nv_Request->get_title('q', 'get', '');
$array_users = [];
if (!empty($q) or defined('NV_IS_SPADMIN') or defined('NV_IS_GODADMIN')) {
    $db->sqlreset()
        ->select('COUNT(*)')
        ->from($db_config['prefix'] . "_" . $module_data . "_customs tb1")
        ->join('INNER JOIN ' . NV_USERS_GLOBALTABLE . ' tb2 ON tb1.userid=tb2.userid');

    $base_url .= '&q=' . $q;
    if (defined('NV_IS_SPADMIN') or defined('NV_IS_GODADMIN')) {
        $dbkey = $db->dblikeescape($q);
        $db->where("tb2.username LIKE '%" . $dbkey . "%' OR tb2.email LIKE '%" . $dbkey . "%'");
    } else {
        $db->where("tb2.username = " . $db->quote($q) . " OR tb2.email = " . $db->quote($q) . "");
    }
    $result = $db->query($db->sql());
    $all_page = $result->fetchColumn();

    $db->select('tb1.*, tb2.username, tb2.first_name, tb2.last_name')
        ->order('tb1.updatetime DESC')
        ->limit($per_page)
        ->offset(($page - 1) * $per_page);

    $result = $db->query($db->sql());
    while ($row = $result->fetch()) {
        $array_users[$row['userid']] = $row;
    }
}

$generate_page = nv_generate_page($base_url, $all_page, $per_page, $page);

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('TABLE_CAPTION', $table_caption);
$xtpl->assign('q', $q);

if (!empty($array_users)) {
    foreach ($array_users as $row) {
        $row['edit_url'] = NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;u=" . urlencode($row['username']) . "&amp;update=1";
        $row['view_url'] = NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=transaction&amp;userid=" . $row['userid'];
        $row['full_name'] = nv_show_name_user($row['first_name'], $row['last_name']);
        $row['updatetime'] = nv_date('d/m/Y, H:i', $row['updatetime']);
        $row['point_total'] =number_format($row['point_total']);
        $xtpl->assign('ACOUNT', $row);
        $xtpl->parse('main.loop_listacount');
    }
}
if ($generate_page) {
    $xtpl->assign('GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
