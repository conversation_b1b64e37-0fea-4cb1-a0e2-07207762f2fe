<!-- BEGIN: main -->
<form action="{FORM_ACTION}" method="post">
    <div class="panel panel-default">
        <div class="panel-heading">{LANG.config_g}</div>
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover">
                <colgroup>
                    <col style="width: 260px" />
                    <col/>
                </colgroup>
                <tbody>
                    <tr>
                        <td>{LANG.config_affiliate_code_groups}</td>
                        <td>
                            <!-- BEGIN: group_affiliate -->
                            <div><label><input type="checkbox" name="affiliate_code_groups[]" value="{GROUP_ID}"{GROUP_AFFILIATE}> {GROUP_TITLE}</label></div>
                            <!-- END: group_affiliate -->
                        </td>
                    </tr>
                    <tr>
                        <td>{LANG.config_promotion_code_length}</td>
                        <td class="form-inline">
                            <input class="form-control w200" name="promotion_code_length" value="{DATA.promotion_code_length}">
                            Số ký tự tối đa có thể chấp nhận khi nhập là 50
                        </td>
                    </tr>
                    <tr>
                        <td>{LANG.config_promotion_use_limityear}</td>
                        <td class="form-inline">
                            <input class="form-control w200" name="promotion_use_limityear" value="{DATA.promotion_use_limityear}"> {LANG.config_promotion_use_limityear_help}
                        </td>
                    </tr>
                    <tr>
                        <td>{LANG.config_group_id_collaborator}</td>
                        <td>
                            <div><label><input type="radio" name="group_id_collaborator" value="0"{NO_COLLABORATOR_CHECKED}> <strong class="text-danger">{GLANG.no}</strong></label></div>
                            <!-- BEGIN: group_id_collaborator -->
                            <div><label><input type="radio" name="group_id_collaborator" value="{GROUP_ID}"{COLLABORATOR_CHECKED}> {GROUP_TITLE}</label></div>
                            <!-- END: group_id_collaborator -->
                            <div class="text-muted"><i>{LANG.config_group_id_collaborator_note}</i></div>
                        </td>
                    </tr>
                    <tr>
                        <td>{LANG.config_discount_freelance}</td>
                        <td class="form-inline">
                            <input class="form-control" name="discount_freelance" value="{DATA.discount_freelance}">
                        </td>
                    </tr>
                    <tr>
                        <td>{LANG.config_discount_official}</td>
                        <td class="form-inline">
                            <input class="form-control" name="discount_official" value="{DATA.discount_official}">
                        </td>
                    </tr>
                    <tr>
                        <td>{LANG.config_max_freelance_promo_value}</td>
                        <td class="form-inline">
                            <input class="form-control" name="max_freelance_promo_value" value="{DATA.max_freelance_promo_value}">
                        </td>
                    </tr>
                    <tr>
                        <td>{LANG.config_max_official_promo_value}</td>
                        <td class="form-inline">
                            <input class="form-control" name="max_official_promo_value" value="{DATA.max_official_promo_value}">
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="2" class="text-center"><input type="submit" name="submit" value="{GLANG.save}" class="btn btn-primary"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <div class="panel panel-default">
        <div class="panel-heading">{LANG.config_group}</div>
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover">
                <thead>
                    <tr>
                        <th class="m200">{LANG.config_label}</th>
                        <!-- BEGIN: group -->
                        <th class="text-center">{GROUP_TITLE}</th>
                        <!-- END: group -->
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <strong><a href="cfg_promo_enable" data-toggle="chkuchk">{LANG.config_promo_enable}</a></strong>
                        </td>
                        <!-- BEGIN: group1 -->
                        <td class="text-center">
                            <input type="checkbox" name="promo_enable[{GROUP_ID}]" class="cfg_promo_enable" value="1"{PROMO_ENABLE}>
                        </td>
                        <!-- END: group1 -->
                    </tr>
                    <tr>
                        <td>{LANG.config_promo_number}</td>
                        <!-- BEGIN: group2 -->
                        <td class="text-center">
                            <input type="number" name="promo_number[{GROUP_ID}]" value="{PROMO_NUMBER}" class="form-control w100 dinlinebl">
                        </td>
                        <!-- END: group2 -->
                    </tr>
                    <tr>
                        <td>
                            <strong><a href="cfg_promo_coupon" data-toggle="chkuchk">{LANG.config_promo_coupon}</a></strong>
                        </td>
                        <!-- BEGIN: group3 -->
                        <td class="text-center">
                            <input type="checkbox" name="promo_coupon[{GROUP_ID}]" class="cfg_promo_coupon" value="1"{PROMO_COUPON}>
                        </td>
                        <!-- END: group3 -->
                    </tr>
                    <tr>
                        <td>
                            <strong><a href="cfg_promo_voucher" data-toggle="chkuchk">{LANG.config_promo_voucher}</a></strong>
                        </td>
                        <!-- BEGIN: group4 -->
                        <td class="text-center">
                            <input type="checkbox" name="promo_voucher[{GROUP_ID}]" class="cfg_promo_voucher" value="1"{PROMO_VOUCHER}>
                        </td>
                        <!-- END: group4 -->
                    </tr>
                    <tr>
                        <td>{LANG.config_promo_couponmax}</td>
                        <!-- BEGIN: group5 -->
                        <td class="text-center">
                            <input type="number" name="promo_couponmax[{GROUP_ID}]" value="{PROMO_COUPONMAX}" class="form-control w100 dinlinebl">
                        </td>
                        <!-- END: group5 -->
                    </tr>
                    <tr>
                        <td>{LANG.config_promo_vouchermax}</td>
                        <!-- BEGIN: group6 -->
                        <td class="text-center">
                            <input type="number" name="promo_vouchermax[{GROUP_ID}]" value="{PROMO_VOUCHERMAX}" class="form-control w100 dinlinebl">
                        </td>
                        <!-- END: group6 -->
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="{COLSPAN}" class="text-center"><input type="submit" name="submit" value="{GLANG.save}" class="btn btn-primary"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</form>
<script>
$(function() {
    $('[data-toggle="chkuchk"]').on('click', function(e) {
        e.preventDefault();
        var $ipt = $('.' + $(this).attr('href'));
        var $iptChecked = $('.' + $(this).attr('href') + ':checked');
        if ($iptChecked.length >= $ipt.length) {
            $ipt.prop('checked', false);
        } else {
            $ipt.prop('checked', true);
        }
    });
});
</script>
<!-- END: main -->
