<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\wallet\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CheckInfoPayment implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'order';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        require_once NV_ROOTDIR . '/modules/' . $module_file . '/init.php';
        require_once NV_ROOTDIR . '/modules/wallet/wallet.class.php';
        $wallet = new \nukeviet_wallet();

        $data = [
            'site_id' => $nv_Request->get_absint('site_id', 'post', 0),
            'modname' => nv_substr($nv_Request->get_title('modname', 'post', ''), 0, 100), // Module thanh toán
            'id' => nv_substr($nv_Request->get_title('id', 'post', ''), 0, 100), // ID đơn hàng
        ];

        $payment_info = $wallet->checkInfoPayment($data);
        if ($payment_info['status'] !== 'SUCCESS') {
            return $this->result->setCode('1001')->setMessage($payment_info['message'])->getResult();
        }

        $this->result->setSuccess();
        $this->result->set('data', $payment_info['data']);

        return $this->result->getResult();
    }
}
