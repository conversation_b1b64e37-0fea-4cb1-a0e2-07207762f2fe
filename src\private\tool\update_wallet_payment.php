<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 * Thống kê số gói VT được tạo, số site có khởi tạo mới > 1 đề thi mới
 * Thống kê site có phát sinh giao dịch trả phí
 
 */
define('NV_MAINFILE', true);
// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/..')));
// Thời gian chạy tối đa (giây)

$db_config = array();
$countries = array(
    'ZZ' => array('RES', 'Reserved', '', '')
);
$client_info['country'] = 'ZZ';
// Ket noi voi cac file constants, config
require NV_ROOTDIR . '/' . 'vendor/autoload.php';
require NV_ROOTDIR . '/includes/constants.php';
require realpath(NV_ROOTDIR . '/' . NV_CONFIG_FILENAME);
require NV_ROOTDIR . '/' . NV_DATADIR . '/config_global.php';
require NV_ROOTDIR . '/includes/timezone.php';
require NV_ROOTDIR . '/includes/functions.php';
require NV_ROOTDIR . '/includes/core/filesystem_functions.php';
// Kết nối CSDL
$db = $db_slave = new NukeViet\Core\Database($db_config);
if (empty($db->connect)) {
    trigger_error('Sorry! Could not connect to data server', 256);
}
unset($db_config['dbpass']);
// Các cổng thanh toán trong CSDL
$module_data = 'wallet';
$module_file = 'wallet';

$array_setting_payment = [];
$sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_payment ORDER BY weight ASC";
$result = $db->query($sql);
$all_page = $result->rowCount();
while ($row = $result->fetch()) {
    $row['config'] = unserialize(nv_base64_decode($row['config']));
    $array_setting_payment[$row['payment']] = $row;
}

// Các cổng thanh toán trên máy chủ
$check_config_payment = "/^([a-zA-Z0-9\-\_]+)\.config\.ini$/";
$payment_funcs = nv_scandir(NV_ROOTDIR . '/modules/' . $module_file . '/payment', $check_config_payment);

if (!empty($payment_funcs)) {
    $payment_funcs = preg_replace($check_config_payment, "\\1", $payment_funcs);
}

$array_payment_other = [];

foreach ($payment_funcs as $payment) {
    $xml = simplexml_load_file(NV_ROOTDIR . '/modules/' . $module_file . '/payment/' . $payment . '.config.ini');

    if ($xml !== false) {
        $xmlconfig = $xml->xpath('config');

        $config = $xmlconfig[0];
        $array_config = [];

        foreach ($config as $key => $value) {
            $config_attr = $value->attributes();
            if (isset($config_attr['lang']) && !empty($config_attr['lang'])) {
                $array_config[$key] = !empty($array_config[$key]) ? $array_config[$key] : [];
                $array_config[$key][(string)$config_attr['lang']] = trim($value);
            } else {
                $array_config[$key] = trim($value);
            }
        }

        $array_payment_other[$payment] = [
            'config' => $array_config,
        ];

        unset($config, $xmlconfig, $xml);
    }
}


foreach ($array_setting_payment as $payment => $row) {
    $setting_config = $row['config'];
    $other_config = $array_payment_other[$payment]['config'];
    if (empty($setting_config)) {
        continue;
    }
    foreach ($setting_config as $key => $value) {
        if (is_array($other_config[$key])) {
            $value_vi = $setting_config[$key];
            $setting_config[$key] = $other_config[$key];
            $setting_config[$key]['vi'] = $value_vi;
        }
    }
    
    nv_base64_encode(serialize($setting_config));
    $db->query('UPDATE nv4_wallet_payment SET config="' . nv_base64_encode(serialize($setting_config)) . '" WHERE payment="' . $payment . '"');
}

echo "HET\n";
