<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2020 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 24/4/2020, 20:6
 */

namespace NukeViet\Dauthau;

class LangMulti
{
    /**
     * @var array
     */
    public static $lang = [
        'vi' => [
            'admin_active_account' => 'Tài khoản của bạn đã được quản trị viên kích hoạt',
            'user_change_password' => 'Hoạt động thay đổi mật khẩu vừa diễn ra trên tài khoản của bạn, nhấp vào nút bên dưới để kiểm tra lại. Nếu bạn là người tao tác, chỉ cần bỏ qua thông báo này.',
            'user_lostpass_success' => 'Tà<PERSON> khoản của bạn vừa lấy lại mật khẩu thành công.',
            'point_interaction' => 'Tài khoản của bạn vừa %s%s điểm',
            'give_point' => 'Tài khoản của bạn vừa được %s tặng %s điểm',
            'give_money' => 'Tài khoản của bạn vừa %s%s %s',
            'reply_to_customer' => '<strong>%s</strong> đã trả lời bạn trong yêu cầu <strong>%s</strong>',
            'notice_to_customer' => 'Quý khách vừa tạo 1 yêu cầu hỗ trợ trên support.dauthau.net với chủ đề là: <strong>%s</strong>. Hỗ trợ viên sẽ xác nhận và phản hồi quý khách trong thời gian sớm nhất.',
            'notice_to_customer_admin' => 'Quản trị viên vừa tạo 1 yêu cầu hỗ trợ cho quý khách trên support.dauthau.net với chủ đề là: <strong>%s</strong>. Hỗ trợ viên sẽ xác nhận và phản hồi quý khách trong thời gian sớm nhất.',
            'notice_assignee_to_customer' => '<strong>%s</strong> đã tiếp nhận xử lý yêu cầu hỗ trợ của bạn.',
            'notice_payment_to_customer' => 'Quý khách có một yêu cầu hỗ trợ thuộc danh mục trả phí, quý khách vui lòng thanh toán %s điểm để được chuyên gia của chúng tôi tư vấn',
            'notice_payment_success_to_customer' => 'Quý khách đã thanh toán thành công %s điểm cho Yêu cầu %s. Hệ thống đã gửi yêu cầu phản hồi đến chuyên gia hỗ trợ, Quý khách sẽ nhận được câu trả lời trong thời gian sớm nhất!',
            'notice_close_to_customer' => 'Quản trị viên đã xử lý xong 1 yêu cầu hỗ trợ cho quý khách trên support.dauthau.net với chủ đề là: %s',
            'notice_refund_comment_ticket_to_customer' => 'Yêu cầu hoàn điểm của bạn đã được Admin HSTDT xử lý',
            'create_ai_to_customer' => 'Quý khách đã khởi tạo thành công yêu cầu hỗ trợ - %s.',
            'create_expert_to_customer' => 'Quý khách đã chọn gửi yêu cầu hỗ trợ đến bộ phận Chuyên gia - %s.',
            'create_both_to_customer' => 'Quý khách đã chọn gửi yêu cầu hỗ trợ đến Tất cả (gửi cho AI Support và Chuyên gia) - %s.',
            'refund_open_to_customer' => 'Yêu cầu hoàn điểm đã được hệ thống tiếp nhận - %s. Thường xuyên theo dõi thông báo để cập nhật thông tin!',
            'refund_accept_to_customer' => 'Yêu cầu hoàn điểm cho yêu cầu - %s đã được xử lý, hệ thống đã HOÀN LẠI ĐIỂM cho Quý khách',
            'refund_refuse_to_customer' => 'Quý khách KHÔNG ĐƯỢC HOÀN ĐIỂM cho yêu cầu - %s vì lý do không hợp lý, click vào đây để xem chi tiết thông tin',
            'reply_to_customer_by_paid' => 'Yêu cầu hỗ trợ - %s của Quý khách đã được Chuyên gia phản hồi. Click vào đây để thanh toán và xem chi tiết phản hồi %s',
            'notice_bonus_report_to_customer' => 'Quản trị viên đã xử lý một yêu cầu <strong>Báo Lỗi</strong> của Quý khách với tiêu đề là: <strong>%s</strong> - mã #%s',
            'notice_bonus_ides_to_customer' => 'Quản trị viên đã xử lý một yêu cầu <strong>Đóng Góp Ý Kiến</strong> của Quý khách với tiêu đề là: <strong>%s</strong> - mã #%s',
        ],
        'en' => [
            'admin_active_account' => 'Your account has been activated by an administrator',
            'user_change_password' => 'Password change activity has just happened on your account, click the button below to check again. If you are the author, just ignore this message.',
            'user_lostpass_success' => 'Your account has just successfully recovered its password.',
            'point_interaction' => 'Your account was %s%s points',
            'give_point' => 'Your account has been received %s points from %s',
            'give_money' => 'Your account was %s%s %s',
            'reply_to_customer' => '<strong>%s</strong> replied in your ticket <strong>%s</strong>',
            'notice_to_customer' => 'You have just created a ticket on support.dauthau.net with the subject: <strong>%s</strong>. Our support team will confirm and respond to you as soon as possible.',
            'notice_to_customer_admin' => 'An administrator has created a ticket for you on support.dauthau.net with the subject: <strong>%s</strong>. Our support team will confirm and respond to you as soon as possible.',
            'notice_assignee_to_customer' => '<strong>%s</strong> has accepted your ticket for processing.',
            'notice_payment_to_customer' => 'You have a ticket in the paid category, please make a payment of %s points to receive consultation from our experts.',
            'notice_payment_success_to_customer' => 'You have successfully paid %s points for Request %s. The system has sent a feedback request to the support expert, you will receive an answer as soon as possible!',
            'notice_close_to_customer' => 'The administrator has successfully processed one support request for you on support.dauthau.net with the subject: %s.',
            'notice_refund_comment_ticket_to_customer' => 'Your score refund request has been processed by Admin HSTDT',
            'create_ai_to_customer' => 'You have successfully created a support request - %s.',
            'create_expert_to_customer' => 'You have chosen to send the support request to the Expert department - %s.',
            'create_both_to_customer' => 'You have chosen to send the support request to All (sent to both AI Support and Expert) - %s.',
            'refund_open_to_customer' => 'Your point refund request has been received by the system - %s. Please check notifications regularly for updates!',
            'refund_accept_to_customer' => 'The point refund request for - %s has been processed, and the system has REFUNDED POINTS to you.',
            'refund_refuse_to_customer' => 'You have NOT BEEN REFUNDED POINTS for the request - %s due to an unreasonable reason, click here to see detailed information.',
            'reply_to_customer_by_paid' => 'Your support request - %s has been responded to by an Expert. Click here to pay and see detailed feedback %s',
            'notice_bonus_report_to_customer' => 'The administrator has processed your <strong>Error Report</strong> request with the title: <strong>%s</strong> - code #%s',
            'notice_bonus_ides_to_customer' => 'The administrator has processed your <strong>Suggestion</strong> request with the subject: <strong>%s</strong> - code #%s',
        ],
    ];

    /**
     * @param array $lang
     * @param array $key
     * @return mixed
     */
    public static function get($lang, $key)
    {
        if (isset(self::$lang[$lang]) and isset(self::$lang[$lang][$key])) {
            return self::$lang[$lang][$key];
        }
        if (isset(self::$lang['en']) and isset(self::$lang['en'][$key])) {
            return self::$lang['en'][$key];
        }
        return $key;
    }
}
