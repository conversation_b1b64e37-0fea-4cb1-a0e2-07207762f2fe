<!-- BEGIN: main -->
<!-- BEGIN: search -->
<div class="well">
    <form action="{NV_BASE_ADMINURL}index.php" method="get" class="form-inline">
        <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}" /> <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}" /> <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}" />
        <div class="row">
            <div class="form-group">
                <select class="form-control" name="chapter">
                    <option value="-1">{LANG.chapter}</option>
                    <!-- BEGIN: chapter -->
                    <option value="{chapter.id}"{chapter.selected}>{chapter.title}</option>
                    <!-- END: chapter -->
                </select>
            </div>
            <div class="form-group">
                <input class="form-control" type="text" value="{full_name}" name="full_name" maxlength="255" placeholder="{LANG.full_name_bni}" />
            </div>
            <div class="form-group">
                <input class="btn btn-primary" type="submit" value="{LANG.search_submit}" />
            </div>
        </div>
    </form>
</div>
<!-- END: search -->

<!-- BEGIN: view -->
<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post">
    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th class="w50">{LANG.number}</th>
                    <th>
                        <div class="inlineblock">{LANG.chapter}</div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">{LANG.full_name}</div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">{LANG.company_name}</div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">{LANG.imaplist_info_basic}</div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">{LANG.get_time}</div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">{LANG.update_time}</div>
                    </th>
                    <th class="text-center">
                        <div class="inlineblock">{LANG.leads}</div>
                    </th>
                </tr>
            </thead>
            <!-- BEGIN: generate_page -->
            <tfoot>
                <tr>
                    <td class="text-center" colspan="10">{NV_GENERATE_PAGE}</td>
                </tr>
            </tfoot>
            <!-- END: generate_page -->
            <tbody>
                <!-- BEGIN: loop -->
                <tr>
                    <td class="text-center">{VIEW.number}</td>
                    <td class="text-center">{VIEW.chapter}</td>
                    <td>
                       {VIEW.full_name}
                    </td>
                    <td class="text-center">
                        {VIEW.company}
                    </td>
                    <td>
                        <!-- BEGIN: profession -->
                        <div> <strong>{LANG.profession}</strong> : {VIEW.profession} </div>
                        <!-- END: profession -->
                        <!-- BEGIN: field_pro -->
                        <div> <strong>{LANG.field_pro}</strong> : {VIEW.field} </div>
                        <!-- END: field_pro -->
                        <!-- BEGIN: phone -->
                        <div><strong>{LANG.phone}</strong> : <a href="tel:{VIEW.phone}">{VIEW.phone}</a></div>
                        <!-- END: phone -->
                        <!-- BEGIN: address -->
                        <div><strong>{LANG.address}</strong> : {VIEW.address}</div>
                        <!-- END: address -->
                        
                        <ul class="mxh">
                            <!-- BEGIN: website -->
                            <li><a href="{VIEW.website}" target="_blank"><i class="fa fa-globe"></i></a></li>
                            <!-- END: website -->
                            <!-- BEGIN: facebook -->
                            <li><a href="{VIEW.link_facebook}" target="_blank"><i class="fa fa-facebook-square" aria-hidden="true"></i></a></li>
                            <!-- END: facebook -->
                            <!-- BEGIN: link_mail -->
                            <li><a href="{VIEW.link_mail}" target="_blank"><i class="fa fa-envelope" aria-hidden="true"></i></a></li>
                            <!-- END: link_mail -->
                        </ul>

                    </td>
                    <td class="text-center">{VIEW.get_time}</td>
                    <td class="text-center">{VIEW.update_time}</td>
                    <td class="text-center">
                    <!-- BEGIN: link_leads -->
                        <a href="{LINK_LEADS}" target="_blank">{VIEW.full_name}</a>
                    <!-- END: link_leads -->
                    </td>
                </tr>
                <!-- END: loop -->
            </tbody>
        </table>
    </div>
</form>
<!-- END: view -->

<!-- END: main -->
