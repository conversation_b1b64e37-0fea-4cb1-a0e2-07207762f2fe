<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2022 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\inform\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    exit('Stop!!!');
}

/**
 * NukeViet\Module\inform\Api\InformSetStatus
 * API dùng để cập nhật trạng thái một thông báo
 *
 * @package NukeViet\Module\inform\Api
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2022 VINADES.,JSC. All rights reserved
 * @version 4.6.00
 * @access public
 */
class InformSetStatus implements IApi
{
    private $result;

    /**
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     * @return string
     */
    public static function getCat()
    {
        return 'Action';
    }

    /**
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $global_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        $admin_id = Api::getAdminId();
        $admin_lev = Api::getAdminLev();

        $postdata = [];
        $postdata['userid'] = $nv_Request->get_absint('userid', 'post', 0);
        $postdata['id'] = $nv_Request->get_absint('id', 'post', 0);
        $postdata['status'] = $nv_Request->get_title('status', 'post', '');

        if (!in_array($postdata['status'], ['viewed', 'unviewed', 'favorite', 'unfavorite', 'hidden', 'unhidden'], true)) {
            return $this->result->setError()
            ->setCode('0001')
            ->setMessage('Invalid status!!!')
            ->getResult();
        }
        if (empty($postdata['id'])) {
            return $this->result->setError()
            ->setCode('0001')
            ->setMessage('No ID!!!')
            ->getResult();
        }

        if (empty($postdata['userid'])) {
            return $this->result->setError()
            ->setCode('5016')
            ->setMessage($nv_Lang->getModule('please_enter_user'))
            ->getResult();
        }

        // Lấy thành viên
        $sql = "SELECT group_id, in_groups FROM " . NV_USERS_GLOBALTABLE . " WHERE userid=" . $postdata['userid'] . " AND active=1";
        $user = $db->query($sql)->fetch();
        if (empty($user)) {
            return $this->result->setError()
            ->setCode('5017')
            ->setMessage($nv_Lang->getModule('user_not_exist'))
            ->getResult();
        }

        // Xác định nhóm của thành viên
        $array_groups = explode(',', $user['in_groups']);
        $array_groups[] = $user['group_id'];
        $array_groups = array_values(array_unique(array_filter(array_map(function ($gr) {
            return $gr >= 10 ? (int) $gr : 0;
        }, $array_groups))));

        switch ($postdata['status']) {
            case 'viewed':
                $field_name = 'viewed_time';
                $field_value = NV_CURRENTTIME;
                break;
            case 'unviewed':
                $field_name = 'viewed_time';
                $field_value = 0;
                break;
            case 'favorite':
                $field_name = 'favorite_time';
                $field_value = NV_CURRENTTIME;
                break;
            case 'unfavorite':
                $field_name = 'favorite_time';
                $field_value = 0;
                break;
            case 'hidden':
                $field_name = 'hidden_time';
                $field_value = NV_CURRENTTIME;
                break;
            case 'unhidden':
                $field_name = 'hidden_time';
                $field_value = 0;
                break;
        }

        $where = [];
        $where[] = "(mtb.receiver_grs = '' AND mtb.receiver_ids = '')";
        if (!empty($array_groups)) {
            $wh = [];
            foreach ($array_groups as $gr) {
                $wh[] = 'FIND_IN_SET(' . $gr . ', mtb.receiver_grs)';
            }
            $wh = implode(' OR ', $wh);
            $where[] = "(mtb.receiver_grs != '' AND (" . $wh . '))';
        }
        $where[] = "(mtb.receiver_ids != '' AND FIND_IN_SET(" . $postdata['userid'] . ', mtb.receiver_ids))';
        $where = '(' . implode(' OR ', $where) . ') AND (mtb.add_time <= ' . NV_CURRENTTIME . ') AND (mtb.exp_time = 0 OR mtb.exp_time > ' . NV_CURRENTTIME . ')';
        if (!empty($array_groups)) {
            $where .= " AND (mtb.sender_role != 'group' OR (mtb.sender_role = 'group' AND mtb.sender_group IN (" . implode(',', $array_groups) . ')))';
        } else {
            $where .= " AND (mtb.sender_role != 'group')";
        }

        $db->select('mtb.id, IFNULL(jtb.shown_time, 0) AS shown_time, IFNULL(jtb.viewed_time, 0) AS viewed_time, IFNULL(jtb.favorite_time, 0) AS favorite_time, IFNULL(jtb.hidden_time, 0) AS hidden_time')
        ->from(NV_INFORM_GLOBALTABLE . ' AS mtb')
        ->join('LEFT JOIN ' . NV_INFORM_STATUS_GLOBALTABLE . ' AS jtb ON (jtb.pid = mtb.id AND jtb.userid = ' . $postdata['userid'] . ')')
        ->where($where . ' AND mtb.id=' . $postdata['id']);
        $result = $db->query($db->sql());
        $row = $result->fetch();

        if (!empty($row['id'])) {
            if (empty($row['shown_time']) and empty($row['viewed_time']) and empty($row['favorite_time']) and empty($row['hidden_time'])) {
                $db->query('INSERT IGNORE INTO ' . NV_INFORM_STATUS_GLOBALTABLE . ' (pid, userid, ' . $field_name . ') VALUES (' . $postdata['id'] . ', ' . $postdata['userid'] . ', ' . $field_value . ')');
            } else {
                $db->query('UPDATE ' . NV_INFORM_STATUS_GLOBALTABLE . ' SET ' . $field_name . ' = ' . $field_value . ' WHERE pid=' . $postdata['id'] . ' AND userid=' . $postdata['userid']);
            }
        }

        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
