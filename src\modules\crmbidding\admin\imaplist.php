<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:39:33 GMT
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Module\crmbidding\User;
use NukeViet\Module\crmbidding\ImapMail;

if (defined('NV_IS_SPADMIN')) {
    // Chiếm quyền xử lý email
    if ($nv_Request->get_title('emailtakeover', 'post', '') === NV_CHECK_SESSION) {
        if (!defined('NV_IS_AJAX')) {
            die('Wrong URL!!!');
        }

        $id = $nv_Request->get_int('id', 'post', 0);

        $respon = [
            'title' => $nv_Lang->getModule('error'),
            'text' => '',
            'classcolor' => 'danger',
            'success' => false
        ];

        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_emails WHERE status=" . ImapMail::STATUS_PROCESSING . " AND
        assign_to!=" . $admin_info['userid'] . " AND email_id=" . $id;
        $result = $db->query($sql);
        $row = $result->fetch();
        if (empty($row)) {
            $respon['text'] = 'Email not exists!!!';
            nv_jsonOutput($respon);
        }

        $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_emails SET
            assign_to=" . $admin_info['userid'] . ",
            status_time=" . NV_CURRENTTIME . "
        WHERE email_id=" . $row['email_id'];
        $db->query($sql);

        $respon['title'] = $nv_Lang->getModule('success');
        $respon['success'] = true;
        $respon['classcolor'] = 'success';
        $respon['text'] = $nv_Lang->getModule('success');

        nv_insert_logs(NV_LANG_DATA, $module_name, 'EMAIL_TAKE_OVER', json_encode($row), $admin_info['userid']);
        nv_jsonOutput($respon);
    }

    // Cho vào thùng rác (xóa)
    if ($nv_Request->get_title('emaildelete', 'post', '') === NV_CHECK_SESSION) {
        if (!defined('NV_IS_AJAX')) {
            die('Wrong URL!!!');
        }

        $id = $nv_Request->get_int('id', 'post', 0);

        $respon = [
            'title' => $nv_Lang->getModule('error'),
            'text' => '',
            'classcolor' => 'danger',
            'success' => false
        ];

        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_emails WHERE status!=" . ImapMail::STATUS_DELETED . "
        AND email_id=" . $id;
        $result = $db->query($sql);
        $row = $result->fetch();
        if (empty($row)) {
            $respon['text'] = 'Email not exists!!!';
            nv_jsonOutput($respon);
        }

        // Email đang xử lý không thể xóa
        if ($row['status'] == ImapMail::STATUS_PROCESSING) {
            $respon['text'] = $nv_Lang->getModule('imaplist_delete_error1');
            nv_jsonOutput($respon);
        }

        $sql = "UPDATE " . NV_PREFIXLANG . "_" . $module_data . "_emails SET
            status=" . ImapMail::STATUS_DELETED . ",
            status_time=" . NV_CURRENTTIME . "
        WHERE email_id=" . $row['email_id'];
        $db->query($sql);

        $respon['title'] = $nv_Lang->getModule('success');
        $respon['success'] = true;
        $respon['classcolor'] = 'success';
        $respon['text'] = $nv_Lang->getModule('success');

        nv_insert_logs(NV_LANG_DATA, $module_name, 'IMAPEMAIL_DELETE', json_encode($row), $admin_info['userid']);
        nv_jsonOutput($respon);
    }
}

$page_title = $nv_Lang->getModule('imaplist_process');
$viewlist = $nv_Request->get_int('viewlist', 'get', 0);

$xtpl = new XTemplate('imaplist.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('TOKEND', NV_CHECK_SESSION);
$xtpl->assign('LINK_WORK', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);

if (!$viewlist) {
    // Kiểm tra xem mình có đang xử lý email nào không
    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_emails WHERE assign_to=" . $admin_info['admin_id'] . "
    AND status=" . ImapMail::STATUS_PROCESSING . " LIMIT 1";
    $email = $db->query($sql)->fetch();

    if (!empty($email)) {
        $subject = empty($email['subject']) ? 'No subject' : $email['subject'];
        $url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=imapdetail&amp;id=' . $email['email_id'];
        $message_content = sprintf($nv_Lang->getModule('imaplist_info1'), $subject, nv_date('H:i:s d/m/Y', $email['status_time']), $url);

        $contents = nv_theme_alert($nv_Lang->getModule('info'), $message_content, 'info');
        include NV_ROOTDIR . '/includes/header.php';
        echo nv_admin_theme($contents);
        include NV_ROOTDIR . '/includes/footer.php';
    }

    // Lấy 1 email mới nhất, hiển thị để xử lý
    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_emails WHERE status=" . ImapMail::STATUS_NEW . " AND (
        assign_to=0 OR assign_to=" . $admin_info['admin_id'] . "
    ) ORDER BY email_id DESC LIMIT 1";
    $email = $db->query($sql)->fetch();

    // Không còn email nào xử lý nữa thì chuyển sang trang xem danh sách
    if (empty($email)) {
        $url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;viewlist=1';
        $contents = nv_theme_alert($nv_Lang->getModule('info'), $nv_Lang->getModule('imaplist_info2'), 'info', $url);
        include NV_ROOTDIR . '/includes/header.php';
        echo nv_admin_theme($contents);
        include NV_ROOTDIR . '/includes/footer.php';
    }

    $page_title = $nv_Lang->getModule('imaplist_queue_title');
    $subject = empty($email['subject']) ? 'No subject' : $email['subject'];
    $url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=imapdetail&amp;id=' . $email['email_id'];
    $message_content = sprintf($nv_Lang->getModule('imaplist_info3'), $subject, nv_date('H:i:s d/m/Y', $email['status_time']), $url);

    $contents = nv_theme_alert($nv_Lang->getModule('info'), $message_content, 'info');
    include NV_ROOTDIR . '/includes/header.php';
    echo nv_admin_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

$page_title = $nv_Lang->getModule('imaplist');

$array_search = [];
$array_search['q'] = $nv_Request->get_title('q', 'get', '');
$array_search['imap_account'] = $nv_Request->get_absint('a', 'get', 0);
$page = $nv_Request->get_int('page', 'get', 1);
if ($page < 1 or $page > ********) {
    $page = 1;
}
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;viewlist=1';
$per_page = 20;
$db->sqlreset()->from(NV_PREFIXLANG . "_" . $module_data . "_emails");

$where = [];
if (!empty($array_search['q'])) {
    $base_url .= '&amp;q=' . urlencode($array_search['q']);
    $dblikekey = $db->dblikeescape($array_search['q']);
    $where[] = "(
        subject LIKE '%" . $dblikekey . "%' OR
        sort_content LIKE '%" . $dblikekey . "%' OR
        from_host LIKE '%" . $dblikekey . "%' OR
        from_name LIKE '%" . $dblikekey . "%' OR
        from_address LIKE '%" . $dblikekey . "%' OR
        send_to LIKE '%" . $dblikekey . "%' OR
        send_cc LIKE '%" . $dblikekey . "%' OR
        send_bcc LIKE '%" . $dblikekey . "%'
    )";
}
if ($array_search['imap_account'] > 0) {
    $base_url .= '&amp;a=' . $array_search['imap_account'];
    $where[] = "imap_account=" . $db->quote($array_search['imap_account'] == 1 ? '<EMAIL>' : '<EMAIL>');
}

if (!defined('NV_IS_GODADMIN')) {
    // Tối cao được tất cả các email (bao gồm cả email bị xóa)
    if (defined('NV_IS_SPADMIN')) {
        // Quyền xem tất cả trừ các mail đã xóa
        $where[] = '(status!=4)';
    } else {
        /*
         * Quyền của sale:
         * - Email mình đang xử lý
         * - Email  2 đã loại bỏ, 3 đã chuyển thành cơ hội
         */
        $where[] = '(status=2 OR (
            status=1 AND assign_to=' . $admin_info['admin_id'] . '
        ) OR status=3)';
    }
}

if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}

$db->select('COUNT(id)');
$num_items = $db->query($db->sql())->fetchColumn();

$db->select('*')->order('id DESC')->limit($per_page)->offset(($page - 1) * $per_page);
$result = $db->query($db->sql());

$array = $array_userids = [];
while ($row = $result->fetch()) {
    $array[$row['email_id']] = $row;

    if (!empty($row['assign_to'])) {
        $array_userids[$row['assign_to']] = $row['assign_to'];
    }
}

$xtpl->assign('DATA_SEARCH', $array_search);

$array_users = User::getUsers($array_userids);
foreach ($array as $row) {
    $row['url_continue'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=imapdetail&amp;id=' . $row['email_id'];
    $row['mail_date'] = $row['mail_date'] ? nv_date('H:i d/m/Y', $row['mail_date']) : '--';
    $row['status_text'] = $nv_Lang->getModule('imaplist_status' . $row['status']);
    $row['send_to'] = str_replace(',', ', ', $row['send_to']);
    $row['send_cc'] = str_replace(',', ', ', $row['send_cc']);
    $row['send_bcc'] = str_replace(',', ', ', $row['send_bcc']);
    $row['status_time'] = empty($row['status_time']) ? 'N/A' : nv_date('H:i d/m/Y', $row['status_time']);

    $xtpl->assign('ROW', $row);

    if (empty($row['from_name'])) {
        $xtpl->parse('main.loop.sender_apart');
    } else {
        $xtpl->parse('main.loop.sender_full');
    }
    if (!empty($row['send_cc'])) {
        $xtpl->parse('main.loop.send_cc');
    }
    if (!empty($row['send_bcc'])) {
        $xtpl->parse('main.loop.send_bcc');
    }

    $menus = 0;
    $menu_desktop = 0;

    // Xóa: Điều hành chung trở lên. Email đang không xử lý
    if (defined('NV_IS_SPADMIN') and $row['status'] != ImapMail::STATUS_PROCESSING and $row['status'] != ImapMail::STATUS_DELETED) {
        $xtpl->parse('main.loop.delete');
        $xtpl->parse('main.loop.delete1');
        $menus++;
    }

    if ($row['status'] == ImapMail::STATUS_PROCESSING) {
        // Email đang xử lý
        $xtpl->assign('VIEW_USER', isset($array_users[$row['assign_to']]) ? $array_users[$row['assign_to']]['show_name'] : ('#' . $row['assign_to']));
        $xtpl->parse('main.loop.status_viewing');

        // Điều hành chung trở lên chiếm quyền xử lý
        if (defined('NV_IS_SPADMIN')) {
            // Chiếm của người khác
            if ($row['assign_to'] != $admin_info['admin_id']) {
                $xtpl->parse('main.loop.take_over');
                $menus++;
                $menu_desktop++;
            }
        }

        // Tiếp tục xử lý email
        if ($row['assign_to'] == $admin_info['admin_id']) {
            $xtpl->parse('main.loop.continue');
            $xtpl->parse('main.loop.continue1');
            $menus++;
        }
    } else {
        // Text trạng thái khác
        $xtpl->parse('main.loop.status_text');
    }

    // Điều hành chung trở lên thì trực tiếp vào xử lý một email nào đó
    if ($row['status'] == ImapMail::STATUS_NEW and defined('NV_IS_SPADMIN')) {
        $xtpl->parse('main.loop.direct_access');
        $menus++;
        $menu_desktop++;
    }

    $xtpl->assign('SHOW_DESKTOP_TOOLS', $menu_desktop ? '' : ' hide-desktop');

    if ($menus) {
        $xtpl->parse('main.loop.menu');
        $xtpl->parse('main.loop.menu1');
    }

    // Xem email này giao cho ai
    if (defined('NV_IS_SPADMIN') and $row['status'] == ImapMail::STATUS_NEW and !empty($row['assign_to'])) {
        $xtpl->assign('VIEW_ASSIGN', isset($array_users[$row['assign_to']]) ? $array_users[$row['assign_to']]['show_name'] : ('#' . $row['assign_to']));
        $xtpl->parse('main.loop.view_assign');
    }

    $xtpl->parse('main.loop');
}

$imap_accounts = [
    1 => '<EMAIL>',
    2 => '<EMAIL>',
];
foreach ($imap_accounts as $acc_id => $acc_mail) {
    $xtpl->assign('ACCOUNT', [
        'key' => $acc_id,
        'selected' => $acc_id == $array_search['imap_account'] ? ' selected="selected"' : '',
        'title' => $acc_mail,
    ]);
    $xtpl->parse('main.account');
}

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
if (!empty($generate_page)) {
    $xtpl->assign('GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}

// Hiển thị link thống kê đối với điều hành chung trở lên
if (defined('NV_IS_SPADMIN')) {
    $xtpl->assign('LINK_STAT', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=imapstat');
    $xtpl->parse('main.link_stat');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
