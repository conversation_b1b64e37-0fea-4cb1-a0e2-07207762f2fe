<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2023 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 27 Feb 2023 09:02:35 GMT
 */

if (!defined('NV_IS_MOD_SUPPORTTICKET')) {
    die('Stop!!!');
}

if (!defined('NV_IS_USER')) {
    $redirect = nv_url_rewrite(NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=" . $op, true);
    Header("Location: " . NV_BASE_SITEURL . "index.php?" . NV_NAME_VARIABLE . "=users&" . NV_OP_VARIABLE . "=login&nv_redirect=" . nv_redirect_encrypt($redirect));
    die();
}

$ticket_id = $nv_Request->get_int('id', 'get,post', 0);
$row = $db->query('SELECT * FROM ' . $db_config['prefix'] . '_' . $module_data . '_row WHERE id=' . $ticket_id . ' AND delete_time=0')->fetch();
if (empty($row) || $row['customer_id'] != $user_info['userid']) {
    nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
}

$page_title = $nv_Lang->getModule('spending_history') . '-' . $row['title'] . ' - ' . $module_config[$module_name]['sitename'];
$key_words = $module_info['keywords'];

$array_mod_title[] = [
    'title' => $row['title'],
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['detail_paid'] . '&amp;id=' . $ticket_id, true),
];
$array_mod_title[] = [
    'title' => $nv_Lang->getModule('spending_history'),
    'link' => nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['point_log'] . '&amp;id=' . $ticket_id, true),
];

$per_page = 10;
$page = $nv_Request->get_int('page', 'post,get', 1);
//Tính tổng lượt cộng/trừ điểm
$db->sqlreset()
    ->select('COUNT(id)')
    ->from(TB_TICKET_POINT_LOG)
    ->where('ticket_id=' . $row['id'] . ' AND userid=' . $user_info['userid']);
$sth = $db->prepare($db->sql());
$sth->execute();
$total = $sth->fetchColumn();

//Tính tổng điểm đã chi tiêu
$db->select('SUM(CASE WHEN status = 1 THEN point ELSE -point END) AS total')
    ->group('ticket_id, userid');
$sth = $db->prepare($db->sql());
$sth->execute();
$total_points = $sth->fetch();
$ticket_points = empty($total_points) ? 0 : $total_points['total'];

//Lấy danh sách chi tiêu
$db->select('*')
    ->order('add_time DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page)
    ->group();
$sth = $db->prepare($db->sql());
$sth->execute();
$i = ($page - 1) * $per_page;

$array_data = [];
while ($view = $sth->fetch()) {
    $view['stt'] = $i + 1;
    // Lấy đa ngôn ngữ
    $_aray_mess = json_decode($view['message'], true);
    if (is_array($_aray_mess)) {
        if (isset($_aray_mess[NV_LANG_DATA])) {
            $view['message'] = $_aray_mess[NV_LANG_DATA];
        } else {
            $view['message'] = array_shift(array_values($_aray_mess));
        }
    } else {
        $view['message'] = $view['message'];
    }
    $array_data[$view['id']] = $view;
    ++$i;
}

if ($page > 1 and empty($array_data)) {
    if (empty($array_data)) {
        nv_redirect_location(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name);
    }
    $page_title .= NV_TITLEBAR_DEFIS . $nv_Lang->getGlobal('page') . ' ' . $page;
}

//Thông tin ví
if (!class_exists('nukeviet_wallet')) {
    require_once NV_ROOTDIR . '/modules/wallet/wallet.class.php';
}
$wallet = new nukeviet_wallet();
$user_money = $wallet->my_money($user_info['userid']);

//Thông tin điểm
if (!class_exists('nukeviet_points')) {
    require_once NV_ROOTDIR . '/modules/points/points.class.php';
}
$nv_points = new nukeviet_points();
$customs_points = $nv_points->my_point($user_info['userid']);

$customer_info = get_user_info($row['customer_id']);
$base_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $module_info['alias']['point_log'] . '&amp;id=' . $ticket_id;
$generate_page = nv_generate_page($base_url, $total, $per_page, $page);

// lấy page_url
$page_url = $base_url;
if ($page > 1) {
    $page_url .= '&amp;page=' . $page;
}
$canonicalUrl = getCanonicalUrl($page_url);
$urlappend = '&amp;page=';
// Kiểm tra đánh số trang
betweenURLs($page, ceil($total / $per_page), $base_url, $urlappend, $prevPage, $nextPage);

$contents = nv_theme_supportticket_point_log($row, $array_data, $generate_page, $customer_info, $user_money, $customs_points, $ticket_points);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
