<?php
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));

require NV_CONSOLE_DIR . '/server.php';
require NV_ROOTDIR . '/includes/mainfile.php';

use NukeViet\Api\DoApi;

if ($sys_info['ini_set_support']) {
    set_time_limit(0);

    if ((int) ini_get('memory_limit') < 512) {
        ini_set('memory_limit', '512M');
    }
}

$static_chart_log = NV_ROOTDIR . '/data/logs/static_chart_log.txt';

// chạy tool quét lại bắt đầu từ ngày xxx
if (file_exists(NV_CONSOLE_DIR . '/static_chart.txt')) {
    $date_from = file_get_contents(NV_CONSOLE_DIR . '/static_chart.txt'); // // lấy mốc $date_from = 1514739600; // 01/01/2018
    $date = intval($date_from);
    $todate = mktime(23, 59, 59, date('m', $date), date('d', $date), date('Y', $date));
} else {
    // chạy hàng ngày
    $date = mktime(0, 0, 0, date('m'), date('d'), date('Y'));
    $todate = mktime(23, 59, 59, date('m', $date), date('d', $date), date('Y', $date));
}

// lấy ngày trước đó để cộng dồn các thống kê, phục vụ cho thống kê tăng trưởng
$date_previous = $date - 86400;
$row = [];
$row_previous = $db->query('SELECT * FROM `nv4_vi_crmbidding_daily_statistic` WHERE date = ' . $date_previous)->fetch();
if (!empty($row_previous)) {
    $row['total_user'] = $row_previous['total_user'];
    $row['total_vip'] = $row_previous['total_vip'];
    $row['total_vieweb'] = $row_previous['total_vieweb'];
    $row['total_leads'] = $row_previous['total_leads'];
    $row['total_opportunities'] = $row_previous['total_opportunities'];
    $row['total_sales'] = $row_previous['total_sales'];
} else {
    // lấy mốc $date_from = 1514739600; // 01/01/2018
    if ($date == 1514739600) {
        $row['total_leads'] = 0;
        $row['total_vip'] = 0;
        $row['total_vieweb'] = 0;
        $row['total_user'] = 0;
        $row['total_opportunities'] = 0;
        $row['total_sales'] = 0;
    } else {
        // ghi lại log lỗi
        $message = "Có lỗi xảy ra! \n";
        $message .= 'Thời gian: ' . nv_date('d-m-Y H:i:s', $date) . "\n";
        $message .= "File: id.dauthau.net/private/static_chart \n";
        $message .= 'Lỗi k xác định previous date: ' . nv_date('d-m-Y H:i:s', $date_previous) . ', ' . $date_previous . '';
        trigger_error($message, 256);
        file_put_contents($static_chart_log, "\n\n ---------- [" . nv_date('d-m-Y H:i:s', $date) . "] Lỗi previous: \n" . $date_previous . "\n", FILE_APPEND);
        die();
    }
}

$array_admin_except = [];
$result_admin = $db->query('SELECT admin_id FROM nv4_authors');
while ($_admin_info = $result_admin->fetch()) {
    $array_admin_except[$_admin_info['admin_id']] = $_admin_info['admin_id'];
}

// các tài khoản test mà k muốn thống kê đưa vào đây, lấy theo nhóm test https://id.dauthau.net/qtdtvnds/index.php?language=vi&nv=users&op=groups&userlist=19
$sql = 'SELECT userid FROM ' . NV_GROUPS_GLOBALTABLE . '_users WHERE group_id = 19';
$result = $db->query($sql);
while ($_gr_user_info = $result->fetch()) {
    $array_admin_except[$_gr_user_info['userid']] = $_gr_user_info['userid'];
}

$row['num_leads'] = 0;
$row['num_vips'] = 0;
$row['num_vieweb'] = 0;
$row['num_users'] = 0;
$row['num_opportunities'] = 0;
$row['num_sales'] = 0;

$where_leads = [];
$where_leads['AND'][] = [
    '>=' => [
        'timecreate' => $date
    ]
];
$where_leads['AND'][] = [
    '<=' => [
        'timecreate' => $todate
    ]
];
$params_leads = [
    'where' => $where_leads
];

$api_leads = new DoApi(API_CRM_URL, API_CRM_KEY, API_CRM_SECRET);
$api_leads->setModule('crmbidding')
    ->setLang('vi')
    ->setAction('ListAllLeads')
    ->setData($params_leads);

$total_leads = $api_leads->execute();
if (!empty($total_leads['total'])) {
    $row['num_leads'] = intval($total_leads['total']);
}

$where_opportunities = [];
$where_opportunities['AND'][] = [
    '>=' => [
        'timecreate' => $date
    ]
];
$where_opportunities['AND'][] = [
    '<=' => [
        'timecreate' => $todate
    ]
];

$params_opportunities = [
    'where' => $where_opportunities
];

$api_opportunities = new DoApi(API_CRM_URL, API_CRM_KEY, API_CRM_SECRET);
$api_opportunities->setModule('crmbidding')
    ->setLang('vi')
    ->setAction('ListAllOpportunities')
    ->setData($params_opportunities);

$total_opportunities = $api_opportunities->execute();
if (!empty($total_opportunities['total'])) {
    $row['num_opportunities'] = intval($total_opportunities['total']);
}

// lây ra các giao dịch nằm trong ngày đó và có người giao dịch tương ứng
$row['num_users'] = $db->query('SELECT count(*) FROM `nv4_users` WHERE regdate >=' . $date . ' AND regdate <=' . $todate)->fetchColumn();

$where_vips = $params_vips = $params_vip99 = [];
$where_vips['AND'][] = [
    '>=' => [
        'from_time' => $date
    ]
];
$where_vips['AND'][] = [
    '<=' => [
        'from_time' => $todate
    ]
];

$where_vip99 = $where_vips;
$where_vips['AND'][] = [
    '!=' => [
        'vip' => 99
    ]
];
$where_vip99['AND'][] = [
    '=' => [
        'vip' => 99
    ]
];

$params_vips = [
    'where' => $where_vips
];
$params_vip99 = [
    'where' => $where_vip99
];
$api_vips = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
$api_vips->setModule('bidding')
    ->setLang('vi')
    ->setAction('ListBiddingCustoms')
    ->setData($params_vips);
$total_vips = $api_vips->execute();

$api_vip99 = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
$api_vip99->setModule('bidding')
    ->setLang('vi')
    ->setAction('ListBiddingCustoms')
    ->setData($params_vip99);

$total_vip99 = $api_vip99->execute();

if (!empty($total_vips['total'])) {
    $row['num_vips'] = intval($total_vips['total']);
}
if (!empty($total_vip99['total'])) {
    $row['num_vieweb'] = intval($total_vip99['total']);
}

// Lấy doanh số theo ngày
$sql = 'SELECT SUM(total_end) as total_end FROM ' . NV_PREFIXLANG . '_bidding_orders_general WHERE static_time >= ' . $date . ' AND static_time <=' . $todate;
$stmt1 = $db->query($sql);
while ($row1 = $stmt1->fetch()) {
    $row['num_sales'] += $row1['total_end'];
}
$stmt1->closeCursor();

// tổng nạp ví theo ngày tính toàn bộ k tính theo sale
$sql = 'SELECT SUM(CASE WHEN status = -1 AND transaction_type = 1 THEN -money_total WHEN status = 1 THEN money_total END) as s_money_total FROM nv4_wallet_transaction WHERE transaction_status = 4 AND transaction_time >= ' . $date . ' AND transaction_time <=' . $todate . ' AND userid NOT IN (' . implode(',', array_keys($array_admin_except)) . ')';
$result_trans_wl = $db->query($sql);
while ($trans_wl = $result_trans_wl->fetch()) {
    $row['num_sales'] += $trans_wl['s_money_total'];
}
$row['num_sales'] = round($row['num_sales']);

$row['total_leads'] += $row['num_leads'];
$row['total_vip'] += $row['num_vips'];
$row['total_vieweb'] += $row['num_vieweb'];
$row['total_user'] += $row['num_users'];
$row['total_opportunities'] += $row['num_opportunities'];
$row['total_sales'] += $row['num_sales'];

// lưu bảng thống kê
echo "- Ngày: " . nv_date('d/m/Y', $date) . "\n";
echo "- Leads: " . $row['num_leads'] . "\n";
echo "- Opportunities: " . $row['num_opportunities'] . "\n";
echo "- Users: " . $row['num_users'] . "\n";
echo "- Vips: " . $row['num_vips'] . "\n";
echo "- Vieweb: " . $row['num_vieweb'] . "\n";
echo "- Sales: " . $row['num_sales'] . "\n";
echo "- Total leads: " . $row['total_leads'] . "\n";
echo "- Total opportunities: " . $row['total_opportunities'] . "\n";
echo "- Total users: " . $row['total_user'] . "\n";
echo "- Total vips: " . $row['total_vip'] . "\n";
echo "- Total vieweb: " . $row['total_vieweb'] . "\n";
echo "- Total sales: " . $row['total_sales'] . "\n";
try {

    $check = $db->query('SELECT * FROM nv4_vi_crmbidding_daily_statistic WHERE date = ' . $date)->fetch();
    if (empty($check)) {
        $stmt = $db->prepare('INSERT INTO nv4_vi_crmbidding_daily_statistic(date, num_leads, num_opportunities, num_users, num_vips, num_vieweb, total_leads, total_opportunities, total_user, total_vip, total_vieweb, num_sales, total_sales) VALUES (:date, :num_leads, :num_opportunities, :num_users, :num_vips, :num_vieweb, :total_leads, :total_opportunities, :total_user, :total_vip, :total_vieweb, :num_sales, :total_sales)');
    } else {
        $stmt = $db->prepare('UPDATE nv4_vi_crmbidding_daily_statistic SET num_leads=:num_leads, num_opportunities=:num_opportunities, num_users=:num_users, num_vips=:num_vips, num_vieweb=:num_vieweb, total_leads=:total_leads, total_opportunities=:total_opportunities, total_user=:total_user, total_vip=:total_vip, total_vieweb=:total_vieweb, num_sales=:num_sales, total_sales=:total_sales WHERE date=:date');
    }
    $stmt->bindParam(':date', $date, PDO::PARAM_INT);
    $stmt->bindParam(':num_leads', $row['num_leads'], PDO::PARAM_INT);
    $stmt->bindParam(':num_opportunities', $row['num_opportunities'], PDO::PARAM_INT);
    $stmt->bindParam(':num_users', $row['num_users'], PDO::PARAM_INT);
    $stmt->bindParam(':num_vips', $row['num_vips'], PDO::PARAM_INT);
    $stmt->bindParam(':num_vieweb', $row['num_vieweb'], PDO::PARAM_INT);
    $stmt->bindParam(':num_sales', $row['num_sales'], PDO::PARAM_INT);
    $stmt->bindParam(':total_leads', $row['total_leads'], PDO::PARAM_INT);
    $stmt->bindParam(':total_user', $row['total_user'], PDO::PARAM_INT);
    $stmt->bindParam(':total_opportunities', $row['total_opportunities'], PDO::PARAM_INT);
    $stmt->bindParam(':total_vip', $row['total_vip'], PDO::PARAM_INT);
    $stmt->bindParam(':total_vieweb', $row['total_vieweb'], PDO::PARAM_INT);
    $stmt->bindParam(':total_sales', $row['total_sales'], PDO::PARAM_INT);
    $exc = $stmt->execute();
    if ($exc) {
        echo "\n<br><update thành công ngày: " . nv_date('d-m-Y H:i:s', $date) . "\n";
        if ($date == mktime(0, 0, 0, date('m', NV_CURRENTTIME), date('d', NV_CURRENTTIME), date('Y', NV_CURRENTTIME)) and file_exists(NV_CONSOLE_DIR . '/static_chart.txt')) {
            // chạy hết ngày hiện tại thì xóa file thống kê để chạy hàng ngày
            unlink(NV_CONSOLE_DIR . '/static_chart.txt');
            echo "Đã chạy hết!!";
            exit(1);
        } else {
            file_put_contents(NV_CONSOLE_DIR . '/static_chart.txt', ($date + 86400));
        }
    }
} catch (PDOException $e) {
    trigger_error($e, 256);
    file_put_contents($static_chart_log, "\n\n ---------- [" . date('d-m-Y H:i:s', $date) . "] Lỗi API ListGroupsUsers: \n" . print_r($e, true) . "\n", FILE_APPEND);
    die();
}
echo "\n<br><br>Cập nhật trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
die("Thống kê xong");
