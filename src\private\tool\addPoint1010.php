<?php
define('NV_SYSTEM', true);

// define('NV_ROOTDIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME) . '/../public_html')));
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

require NV_ROOTDIR . '/includes/mainfile.php';

use NukeViet\Point\Point;

$time_run = 1696917599;  //<PERSON>hi chạy thì thay thế số này bằng thời gian chạy

$last_userid = 0;
if (file_exists(NV_ROOTDIR . '/tool/addPoint1010.txt')) {
    $last_userid = intval(file_get_contents(NV_ROOTDIR . '/tool/addPoint1010.txt'));
}
try {
    $config_brokers = [
        'Dauthauinfo' => 1,
        'Dauthaunet' => 2,
        'Iddauthaunet' => 1
    ];

    $sql = "SELECT DISTINCT(userid) FROM `nv4_users_login_log` WHERE userid > " . $last_userid . " AND logtime > 1696870800 AND logtime < " . $time_run . " ORDER BY userid ASC LIMIT 100";
    $query1 = $db->query($sql);
    if ($query1->rowCount() == 0) {
        echo "Đã chạy hết!!";
        exit(1);
    }
    $tmp_arr_userid = $query1->fetchAll(PDO::FETCH_COLUMN);
    $new_userid = $tmp_arr_userid[count($tmp_arr_userid) - 1];

    $arr_is_give = $db->query("SELECT userid FROM nv4_points_users_tmp WHERE userid IN(" . implode(',', $tmp_arr_userid) . ") AND type = 104")->fetchAll(PDO::FETCH_COLUMN);
    $arr_userid = array_diff($tmp_arr_userid, $arr_is_give);
    if (!empty($arr_userid)) {
        //Lấy broke_id của các userid
        $query = $db->query("SELECT userid, broker_id FROM nv4_users_login_log WHERE userid IN(" . implode(',', $arr_userid) . ") GROUP BY userid");
        $arr_sql = [];
        while ($user = $query->fetch()) {
            $userid = $user['userid'];
            $site_id = $config_brokers[$user['broker_id']];
            if (isset($site_id)) {
                echo "#U " . $userid ."\n";
                $new_userid = $userid;
                $arr_sql[] = "(" . $site_id . ", " . $userid . ", 0, 104, 0, " . NV_CURRENTTIME . ", 110, 'Tặng điểm ngày Chuyển đổi số quốc gia 10/10/2023', 'Tặng điểm ngày Chuyển đổi số quốc gia 10/10/2023', 1698771599, '')";
            }
        }
        if (!empty($arr_sql)) {
            $sql = "INSERT INTO nv4_points_users_tmp (site_id, userid, pre_uid, type, status, addtime, givepoints, givemessage, givelog, giveexpired, key_crawl) VALUES " . implode(',', $arr_sql);
            $exec = $db->exec($sql);
        }
    }
} catch (Exception $e) {
    // Dừng ngay nếu lỗi
    print_r($e);
    die();
}
if ($new_userid > $last_userid) {
    file_put_contents(NV_ROOTDIR . '/tool/addPoint1010.txt', $new_userid);
}

