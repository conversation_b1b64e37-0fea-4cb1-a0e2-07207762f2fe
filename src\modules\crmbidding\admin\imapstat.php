<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:39:33 GMT
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$set_active_op = 'imaplist';
$page_title = $nv_Lang->getModule('imapstat_title');

$xtpl = new XTemplate('imapstat.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('NV_LANG_INTERFACE', NV_LANG_INTERFACE);
$xtpl->assign('TEMPLATE', $global_config['module_theme']);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_FILE', $module_file);
$xtpl->assign('OP', $op);
$xtpl->assign('TOKEND', NV_CHECK_SESSION);

$search = [];
$search['from'] = $nv_Request->get_title('f', 'get', '');
$search['to'] = $nv_Request->get_title('t', 'get', '');
$search['imap_account'] = $nv_Request->get_absint('a', 'get', 0);

if (preg_match('/^([0-9]{1,2})\-([0-9]{1,2})\-([0-9]{4})$/', $search['from'], $m)) {
    $search['from'] = mktime(0, 0, 0, intval($m[2]), intval($m[1]), intval($m[3]));
} else {
    $search['from'] = mktime(0, 0, 0, date('n'), 1, date('Y'));
}
if (preg_match('/^([0-9]{1,2})\-([0-9]{1,2})\-([0-9]{4})$/', $search['to'], $m)) {
    $search['to'] = mktime(23, 59, 59, intval($m[2]), intval($m[1]), intval($m[3]));
} else {
    $search['to'] = mktime(23, 59, 59, date('n'), cal_days_in_month(CAL_GREGORIAN, date('n'), date('Y')), date('Y'));
}

$where = [];
$where[] = "mail_date>=" . $search['from'];
$where[] = "mail_date<=" . $search['to'];

$where_or = [];
if ($search['imap_account'] > 0) {
    if ($search['imap_account'] == 1) {
        $where_or[] = "FIND_IN_SET('<EMAIL>', send_cc)";
    } else {
        $where_or[] = "FIND_IN_SET('<EMAIL>', send_cc)";
    }
} else {
    $where_or[] = "FIND_IN_SET('<EMAIL>', send_cc)";
    $where_or[] = "FIND_IN_SET('<EMAIL>', send_cc)";
}
$where[] = "(" . implode(' OR ', $where_or) . ")";

$sql = "SELECT COUNT(email_id) nummail, from_address FROM " . NV_PREFIXLANG . "_" . $module_data . "_emails
WHERE " . implode(' AND ', $where) . " GROUP BY from_address";
$data_rows = $db->query($sql)->fetchAll();

$array_sale_mails = [];
foreach ($array_user_id_users as $_row) {
    $array_sale_mails[$_row['email']] = nv_show_name_user($_row['first_name'], $_row['last_name'], $_row['username']);
}

$series_data = $categories = [];

$array = [];
foreach ($data_rows as $row) {
    if (isset($array_sale_mails[$row['from_address']])) {
        $series_data[] = $row['nummail'];
        $categories[] = $array_sale_mails[$row['from_address']];
    } else {
        $array[] = $row;
    }
}

$search['from'] = date('d-m-Y', $search['from']);
$search['to'] = date('d-m-Y', $search['to']);

$xtpl->assign('SERIES_DATA', json_encode($series_data));
$xtpl->assign('CATEGORIES', json_encode($categories));
$xtpl->assign('SEARCH', $search);

if (empty($series_data)) {
    $xtpl->parse('main.nodata');
} else {
    $xtpl->parse('main.chart');
}

$imap_accounts = [
    1 => '<EMAIL>',
    2 => '<EMAIL>',
];
foreach ($imap_accounts as $acc_id => $acc_mail) {
    $xtpl->assign('ACCOUNT', [
        'key' => $acc_id,
        'selected' => $acc_id == $search['imap_account'] ? ' selected="selected"' : '',
        'title' => $acc_mail,
    ]);
    $xtpl->parse('main.account');
}

if (!empty($array)) {
    foreach ($array as $row) {
        $xtpl->assign('ROW', $row);
        $xtpl->parse('main.others.loop');
    }

    $xtpl->parse('main.others');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
