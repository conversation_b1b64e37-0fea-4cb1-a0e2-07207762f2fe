<?php

// Trạng thái của ticket ngoài site
enum TicketStatusClient: int
{
    case Open = 1;
    case Done = 2;
    case Process = 3;
    case Close = 4;
    case Unread = 5;
    case Draft = 6;

    public function getIcon()
    {
        return match ($this) {
            self::Open => 'icon_email_open',
            self::Done => 'icon_email_done',
            self::Close => 'icon_email_close',
            self::Process => 'icon_email_forward_to_inbox',
            self::Unread => 'icon_email_viewed_feedback',
            self::Draft => 'icon_email_forward_to_inbox',
        };
    }

    public function getMessage()
    {
        global $nv_Lang;
        $nv_Lang->loadModule('supportticket', false, true);
        $label = match ($this) {
            self::Open => $nv_Lang->getModule('ticket_tooltip_open'),
            self::Done => $nv_Lang->getModule('ticket_tooltip_all_read'),
            self::Close => $nv_Lang->getModule('ticket_tooltip_lock'),
            self::Process => $nv_Lang->getModule('ticket_tooltip_forward'),
            self::Unread => $nv_Lang->getModule('ticket_tooltip_no_read'),
            self::Draft => $nv_Lang->getModule('ticket_tooltip_draft'),
        };
        $nv_Lang->changeLang(NV_LANG_DATA);
        return $label;
    }

    public function getLabel()
    {
        global $nv_Lang;
        return match ($this) {
            self::Open => $nv_Lang->getModule('ticket_status_open'),
            self::Done => $nv_Lang->getModule('ticket_status_done'),
            self::Close => $nv_Lang->getModule('ticket_status_close'),
            self::Process => $nv_Lang->getModule('ticket_status_process'),
            self::Unread => $nv_Lang->getModule('ticket_status_unread'),
            self::Draft => $nv_Lang->getModule('ticket_status_draft'),
        };
    }
}

// Trạng thái của ticket trong admin
enum TicketStatus: int
{
    case Open = 1;
    case Done = 2;
    case Process = 3;
    case Close = 4;
    case Unread = 5;
    case Draft = 6;
    case RefuseToAddPoint = 10;

    public function getLabel()
    {
        global $nv_Lang;
        $nv_Lang->loadModule('supportticket', false, true);
        $label = match ($this) {
            self::Open => $nv_Lang->getModule('ticket_status_open'),
            self::Done => $nv_Lang->getModule('ticket_status_done'),
            self::Close => $nv_Lang->getModule('ticket_status_close'),
            self::Process => $nv_Lang->getModule('ticket_status_process'),
            self::Unread => $nv_Lang->getModule('ticket_status_unread'),
            self::Draft => $nv_Lang->getModule('ticket_status_draft'),
            self::RefuseToAddPoint => $nv_Lang->getModule('ticket_status_refuse_add_point')
        };
        $nv_Lang->changeLang(NV_LANG_DATA);
        return $label;
    }
}

// Vai trò người trả lời comment
enum CommentType: int
{
    case System = 1;
    case Expert = 2;
    case AI = 3;
    case Staff = 4;
    case Customer = 5;
    case ExpertAdditional = 6;

    case ExpertAdditionalForAI = 7;

    public function getLabel()
    {
        global $nv_Lang;
        return match ($this) {
            self::System => $nv_Lang->getModule('role_system'),
            self::Expert => $nv_Lang->getModule('role_expert'),
            self::AI => $nv_Lang->getModule('role_ai'),
            self::Staff => $nv_Lang->getModule('role_staff'),
            self::Customer => $nv_Lang->getModule('role_customer'),
            self::ExpertAdditional => $nv_Lang->getModule('role_expert_additional'),
            self::ExpertAdditionalForAI => $nv_Lang->getModule('role_expert_additional_for_ai'),
        };
    }
}

// Trạng thái thanh toán
enum PaymentStatus: int
{
    case Done = 1;
    case Process = 2;
    case Cancel = 3;

    public function getLabel()
    {
        global $nv_Lang;
        return match ($this) {
            self::Done => $nv_Lang->getModule('payment_status1'),
            self::Process => $nv_Lang->getModule('payment_status2'),
            self::Cancel => $nv_Lang->getModule('payment_status4'),
        };
    }
}

// Trạng thái trả lời
enum CommentStatus: int
{
    case Open = 1;
    case Process = 2;
    case Done = 3;
    case Refund = 4;
    case Invalid = 5;

    public function getLabel()
    {
        global $nv_Lang;
        return match ($this) {
            self::Open => $nv_Lang->getModule('comment_status_open'),
            self::Done => $nv_Lang->getModule('comment_status_done'),
            self::Process => $nv_Lang->getModule('comment_status_process'),
            self::Refund => $nv_Lang->getModule('comment_status_refund'),
            self::Invalid => $nv_Lang->getModule('comment_status_invalid'),
        };
    }
}

// Trạng thái hoàn tiền
enum RefundStatus: int
{
    case Open = 1;
    case Process = 2;
    case Accept = 3;
    case Refuse = 4;

    public function getLabel()
    {
        global $nv_Lang;
        $nv_Lang->loadModule('supportticket', false, true);
        $label = match ($this) {
            self::Open => $nv_Lang->getModule('refund_open'),
            self::Process => $nv_Lang->getModule('refund_process'),
            self::Accept => $nv_Lang->getModule('refund_accept'),
            self::Refuse => $nv_Lang->getModule('refund_refuse'),
        };
        $nv_Lang->changeLang(NV_LANG_DATA);
        return $label;
    }

    public function getClass()
    {
        return match ($this) {
            self::Open => 'success',
            self::Process => 'warning',
            self::Accept => 'primary',
            self::Refuse => 'danger',
        };
    }
}

enum LogKey: string
{
    case AdminInsert = 'ADMIN_INSERT_TICKET';
    case AdminUpdate = 'ADMIN_UPDATE_TICKET';
    case AdminReply = 'ADMIN_REPLY_COMMENT';
    case AdminReplyReview = 'ADMIN_REPLY_REVIEW';
    case AiReply = 'AI_REPLY_COMMENT';
    case ExpertReply = 'EXPERT_REPLY_COMMENT';
    case ExpertAddtional = 'EXPERT_ADDITIONAL_COMMENT';
    case CustomerInsert = 'CUSTOMER_INSERT_TICKET';
    case CustomerUpdate = 'CUSTOMER_UPDATE_TICKET';
    case CustomerSendAi = 'CUSTOMER_SEND_AI_REQUEST';
    case CustomerSendExpert = 'CUSTOMER_SEND_EXPERT_REQUEST';
    case CustomerSendBoth = 'CUSTOMER_SEND_BOTH_REQUEST';
    case CustomerAdditionalAi = 'CUSTOMER_SEND_ADDITIONAL_AI';
    case CustomerAdditionalExpert = 'CUSTOMER_SEND_ADDITIONAL_EXPERT';
    case CustomerReview = 'CUSTOMER_REVIEW_COMMENT';
    case CustomerPaymentAi = 'CUSTOMER_PAYMENT_COMMENT_AI';
    case CustomerPaymentExpert = 'CUSTOMER_PAYMENT_COMMENT_EXPERT';
    case CustomerClosedTicket = 'CUSTOMER_CLOSED_TICKET';
    case CustomerReply = 'CUSTOMER_REPLY';
    case AdminHandleError = 'ADMIN_HANDLE_ERROR';
    case MarkInvalid = 'ADMIN_MARK_INVALID';
    case ExpertAddtionalForAI = 'EXPERT_ADDITIONAL_COMMENT_FOR_AI';

    public function getLabel()
    {
        global $nv_Lang;
        return match ($this) {
            self::AdminInsert => $nv_Lang->getModule('log_admin_insert_ticket'),
            self::AdminUpdate => $nv_Lang->getModule('log_admin_update_ticket'),
            self::AdminReply => $nv_Lang->getModule('log_admin_reply_comment'),
            self::AdminReplyReview => $nv_Lang->getModule('log_admin_reply_review'),
            self::AiReply => $nv_Lang->getModule('log_ai_reply_comment'),
            self::ExpertReply => $nv_Lang->getModule('log_expert_reply_comment'),
            self::ExpertAddtional => $nv_Lang->getModule('log_expert_additional_comment'),
            self::CustomerInsert => $nv_Lang->getModule('log_customer_insert_ticket'),
            self::CustomerUpdate => $nv_Lang->getModule('log_customer_update_ticket'),
            self::CustomerSendAi => $nv_Lang->getModule('log_customer_send_ai_request'),
            self::CustomerSendExpert => $nv_Lang->getModule('log_customer_send_expert_request'),
            self::CustomerSendBoth => $nv_Lang->getModule('log_customer_send_both_request'),
            self::CustomerAdditionalAi => $nv_Lang->getModule('log_customer_send_additional_ai'),
            self::CustomerAdditionalExpert => $nv_Lang->getModule('log_customer_send_additional_expert'),
            self::CustomerReview => $nv_Lang->getModule('log_customer_review_comment'),
            self::CustomerPaymentAi => $nv_Lang->getModule('log_customer_payment_comment_ai'),
            self::CustomerPaymentExpert => $nv_Lang->getModule('log_customer_payment_comment_expert'),
            self::CustomerClosedTicket => $nv_Lang->getModule('log_customer_closed_ticket'),
            self::CustomerReply => $nv_Lang->getModule('log_customer_reply'),
            self::AdminHandleError => $nv_Lang->getModule('admin_handle_error'),
            self::MarkInvalid => $nv_Lang->getModule('admin_mark_invalid'),
            self::ExpertAddtionalForAI => $nv_Lang->getModule('log_expert_additional_comment_for_ai'),
        };
    }
}

// Trạng thái hoàn tiền
enum PlusPointStatus: int
{
    case Deny = 0;
    case Plus = 1;

    public function getLabel()
    {
        $label = match ($this) {
            self::Deny => 'deny',
            self::Plus => 'plus',
        };
        return $label;
    }
}
