<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

use NukeViet\Files\Download;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Border;

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_url = $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
$array_search = [];
$where = $where_ticket = [];

$array_search['type'] = $nv_Request->get_int('type', 'get', 0);
$array_search['status'] = $nv_Request->get_int('status', 'get', 0);
$array_search['refund_status'] = $nv_Request->get_int('refund_status', 'get', 0);
$array_search['rating_status'] = $nv_Request->get_int('rating_status', 'get', 0);
$array_search['expert'] = $nv_Request->get_int('expert', 'get', 0);
$curent_from = date('d-m-Y', strtotime('first day of this month'));
$curent_to = date('d-m-Y', strtotime('now'));
$array_search['time_from'] = $nv_Request->get_title('time_from', 'get', $curent_from);
$array_search['time_to'] = $nv_Request->get_title('time_to', 'get', $curent_to);
$array_search['u_time_from'] = nv_d2u_get($array_search['time_from']);
$array_search['u_time_to'] = nv_d2u_get($array_search['time_to'], 23, 59, 59);
$array_search['ticket_id'] = $nv_Request->get_int('ticket_id', 'get', 0);

$where['AND'][] = [
    '=' => [
        'delete_time' => 0,
    ]
];
$where['AND'][] = [
    '=' => [
        'is_paid' => 1,
    ]
];

if ($array_search['type'] == 1) {
    $where['AND'][] = [
        '=' => [
            'comment_type' => CommentType::AI->value,
        ]
    ];
} elseif ($array_search['type'] == 2) {
    $where['AND'][] = [
        '=' => [
            'comment_type' => CommentType::Expert->value,
        ]
    ];
} else {
    $where['AND'][] = [
        'IN' => [
            'comment_type' => '(' . CommentType::AI->value .',' . CommentType::Expert->value . ')',
        ]
    ];
}

if ($array_search['u_time_from'] > 0) {
    $base_url .= '&amp;time_from=' . urlencode($array_search['time_from']);
    $where['AND'][] = [
        '>=' => [
            'ticket_add_time' => $array_search['u_time_from'],
        ]
    ];
}

if ($array_search['u_time_to'] > 0) {
    $base_url .= '&amp;time_to=' . urlencode($array_search['time_to']);
    $where['AND'][] = [
        '<=' => [
            'ticket_add_time' => $array_search['u_time_to'],
        ]
    ];
}

if ($array_search['type'] > 0) {
    $base_url .= '&amp;type=' . $array_search['type'];
}

if ($array_search['status'] > 0) {
    $base_url .= '&amp;status=' . $array_search['status'];
    $where['AND'][] = [
        '=' => [
            'status' => $array_search['status'],
        ]
    ];
}

if ($array_search['expert'] > 0) {
    $base_url .= '&amp;expert=' . $array_search['expert'];
    $where['AND'][] = [
        '=' => [
            'reply_userid' => $array_search['expert'],
        ]
    ];
}

if ($array_search['ticket_id'] > 0) {
    $base_url .= '&amp;ticket_id=' . $array_search['ticket_id'];
    $where['AND'][] = [
        '=' => [
            'ticket_id' => $array_search['ticket_id'],
        ]
    ];
} else {
    $array_search['ticket_id'] = '';
}

if ($array_search['refund_status'] > 0) {
    $base_url .= '&amp;refund_status=' . $array_search['refund_status'];
    $where['AND'][] = [
        '=' => [
            'refund_status' => $array_search['refund_status'],
        ]
    ];
}

if ($array_search['rating_status'] == 1) {
    $where['AND'][] = [
        '>' => [
            'rating_number' => 0,
        ]
    ];
} elseif ($array_search['rating_status'] == 2) {
    $where['AND'][] = [
        '=' => [
            'rating_number' => 0,
        ]
    ];
}
if ($array_search['rating_status'] > 0) {
    $base_url .= '&amp;rating_status=' . $array_search['rating_status'];
}

// Fetch Limit
if ($nv_Request->get_title('token', 'get', '') === NV_CHECK_SESSION && $nv_Request->get_int('export_excel', 'get', 0) == 1 && defined('NV_IS_ADMIN')) {
    $per_page = -1;
    $page = 1;
} else {
    $per_page = 20;
    $page = $nv_Request->get_int('page', 'post,get', 1);
}

$params = [
    'page' => $page,
    'perpage' => $per_page
];

// Nếu có điều kiện where thì gán
if (!empty($where)) {
    $params['where'] = $where;
}

// GỌI API
$config_commission = isset($module_config[$module_name]['commission']) ? $module_config[$module_name]['commission'] * 0.01 : 0;
$ListComment = nv_local_api('ListAllComment', $params, $admin_info['username'], $module_name);
$ListAllComment = json_decode($ListComment, true);
$data = [];
$ticket_ids = [];
$generate_page = '';
if ($ListAllComment['status'] == 'success') {
    $stt = 0;
    foreach ($ListAllComment['data'] as $view) {
        ++$stt;
        $view['stt'] = $page == 1 ? $stt : $stt + ($per_page * ($page - 1));
        $view['link_detail'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail_paid&amp;id=' . $view['ticket_id'];
        $view['status'] = CommentStatus::tryFrom(intval($view['status']))?->getLabel();
        $view['payment_status'] = PaymentStatus::tryFrom(intval($view['payment_status']))?->getLabel();
        $view['rating'] = $view['rating_number'] > 0 ? $view['rating_number'] : '';
        $view['revenue'] = ($view['point_refund'] > 0 && $view['refund_status'] == RefundStatus::Accept->value) ? 0 : nv_number_format($view['point_final'] * 1000);
        $view['commission'] = ($view['revenue'] != 0 && $view['comment_type'] == CommentType::Expert->value) ? nv_number_format($view['point_final'] * $config_commission * 1000) : '';
        $view['reply_user'] = $view['reply_userid'] == 0 ? $nv_Lang->getModule('role_ai') : get_user_info($view['reply_userid'])['fullname'];
        $view['reply_time'] = ($view['edit_time'] > 0) ? nv_datetime_format($view['edit_time']) : nv_datetime_format($view['add_time']);
        $ticket_ids[] = $view['ticket_id'];
        $data[] = $view;
    }
    $generate_page = nv_generate_page($base_url, $ListAllComment['total'], $ListAllComment['perpage'], $ListAllComment['page']);
}

// GỌI API TICKET ĐỂ LẤY TIÊU ĐỀ
$where_ticket['AND'][] = [
    'IN' => [
        'id' => '(' . implode(',', $ticket_ids) . ')'
    ]
];
$params = [
    'perpage' => -1,
    'where' => $where_ticket
];
$ListTicket = nv_local_api('ListAllTicket', $params, $admin_info['username'], $module_name);
$ListAllTicket = json_decode($ListTicket, true);

if ($ListAllTicket['status'] == 'success') {
    $ticket_titles = [];
    foreach ($ListAllTicket['data'] as $ticket) {
        $ticket_titles[$ticket['id']] = $ticket['title'];
    }

    foreach ($data as &$comment) {
        if (isset($ticket_titles[$comment['ticket_id']])) {
            $comment['ticket_title'] = $ticket_titles[$comment['ticket_id']];
        }
    }
    unset($comment);
}

//Xuất dữ liệu thông kê ra file excel
if ($nv_Request->get_title('token', 'get', '') === NV_CHECK_SESSION && $nv_Request->get_int('export_excel', 'get', 0) == 1 && defined('NV_IS_ADMIN')) {
    // Kiểm tra thư viện tồn tại
    if (!is_dir(NV_ROOTDIR . '/vendor/phpoffice/phpspreadsheet')) {
        trigger_error('No phpspreadsheet lib. Run command &quot;composer require phpoffice/phpspreadsheet&quot; to install phpspreadsheet', 256);
    }

    // Tăng giới hạn bộ nhớ lên để có chỗ xử lý dữ liệu
    if ($sys_info['allowed_set_time_limit']) {
        set_time_limit(0);
    }
    if ($sys_info['ini_set_support']) {
        $memoryLimitMB = (integer)ini_get('memory_limit');
        if ($memoryLimitMB < 4000) {
            ini_set("memory_limit", "4000M");
        }
    }

    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();

    // Kẻ đường viền các ô
    $styleArray = [
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN,
                'color' => ['argb' => 'FF000000'],
            ],
        ],
    ];

    // Style cho tiêu đề (in đậm)
    $headerStyleArray = [
        'font' => [
            'bold' => true,
        ],
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN,
                'color' => ['argb' => 'FF000000'],
            ],
        ],
    ];

    // Style canh giữa
    $centerAlignmentStyle = [
        'alignment' => [
            'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
        ],
    ];
    
    // Ghi tiêu đề cột
    $columns = ['stt', 'time', 'ticket_id', 'ticket_title', 'comment_reply_user', 'paid', 'refund', 'revenue', 'commission', 'status', 'payment_status', 'rating', 'ticket_add_time'];
    $columnWidths = [5, 20, 12, 40, 25, 15, 15, 15, 15, 15, 15, 15, 20]; // Đặt chiều rộng cho từng cột
    $columnIndex = 'A';
    foreach ($columns as $key => $column) {
        $sheet->setCellValue($columnIndex . '1', $nv_Lang->getModule($column));
        $sheet->getColumnDimension($columnIndex)->setWidth($columnWidths[$key]);
        $columnIndex++;
    }

    // Áp dụng style cho tiêu đề
    $sheet->getStyle('A1:' . chr(ord('A') + count($columns) - 1) . '1')->applyFromArray($headerStyleArray);

    // Ghi dữ liệu
    $rowIndex = 2;
    foreach ($data as $row) {
        $sheet->setCellValue('A' . $rowIndex, $row['stt']);
        $sheet->setCellValue('B' . $rowIndex, \PhpOffice\PhpSpreadsheet\Shared\Date::PHPToExcel($row['add_time']));
        $sheet->getStyle('B' . $rowIndex)->getNumberFormat()->setFormatCode('yyyy-mm-dd hh:mm:ss');
        $sheet->setCellValue('C' . $rowIndex, $row['ticket_id']);
        $sheet->getCell('C' . $rowIndex)->getHyperlink()->setUrl(SITE_ID_DOMAIN . $row['link_detail']);
        $sheet->setCellValue('D' . $rowIndex, $row['ticket_title']);
        $sheet->getCell('D' . $rowIndex)->getHyperlink()->setUrl(SITE_ID_DOMAIN . $row['link_detail']);
        $sheet->setCellValue('E' . $rowIndex, $row['reply_user']);
        $sheet->setCellValue('F' . $rowIndex, $row['point_final']);
        $sheet->setCellValue('G' . $rowIndex, $row['point_refund']);
        $revenue = ($row['point_refund'] > 0 && $row['refund_status'] == RefundStatus::Accept->value) ? 0 : $row['point_final'] * 1000;
        $commission = ($revenue != 0 && $row['comment_type'] == CommentType::Expert->value) ? $row['point_final'] * $config_commission * 1000 : '';
        $sheet->setCellValue('H' . $rowIndex, $revenue);
        $sheet->setCellValue('I' . $rowIndex, $commission);
        $sheet->setCellValue('J' . $rowIndex, $row['status']);
        $sheet->setCellValue('K' . $rowIndex, $row['payment_status']);
        $sheet->setCellValue('L' . $rowIndex, $row['rating']);
        $sheet->setCellValue('M' . $rowIndex, \PhpOffice\PhpSpreadsheet\Shared\Date::PHPToExcel($row['ticket_add_time']));
        $sheet->getStyle('M' . $rowIndex)->getNumberFormat()->setFormatCode('yyyy-mm-dd hh:mm:ss');
        
        // Canh giữa cột
        $sheet->getStyle('A' . $rowIndex)->applyFromArray($centerAlignmentStyle);
        $sheet->getStyle('C' . $rowIndex)->applyFromArray($centerAlignmentStyle);
        $sheet->getStyle('L' . $rowIndex)->applyFromArray($centerAlignmentStyle);

        $rowIndex++;
    }

    // Áp dụng style cho toàn bộ bảng (bao gồm dữ liệu)
    $sheet->getStyle('A1:' . chr(ord('A') + count($columns) - 1) . ($rowIndex - 1))->applyFromArray($styleArray);

    // Lưu file và tải xuống
    $file = NV_ROOTDIR . '/' . NV_TEMP_DIR . '/export-answers-' . NV_CHECK_SESSION . '.xlsx';
    $writer = new Xlsx($spreadsheet);
    if (is_file($file)) {
        nv_deletefile($file);
    }
    $writer->save($file);

    $file_name = 'export-answers-' . NV_CURRENTTIME;
    $download = new Download($file, NV_ROOTDIR . '/' . NV_TEMP_DIR, change_alias($file_name) . '.xlsx');
    $download->download_file();
    exit();
}

$template = get_tpl_dir([$global_config['module_theme'], $global_config['admin_theme']], 'admin_default', '/modules/' . $module_file . '/answers.tpl');
$tpl = new \NukeViet\Template\NVSmarty();
$tpl->registerPlugin('modifier', 'nformat', 'nv_number_format');
$tpl->registerPlugin('modifier', 'dformat', 'nv_datetime_format');
$tpl->setTemplateDir(NV_ROOTDIR . '/themes/' . $template . '/modules/' . $module_file);
$tpl->assign('LANG', $nv_Lang);
$tpl->assign('MODULE_NAME', $module_name);
$tpl->assign('OP', $op);
$tpl->assign('SEARCH', $array_search);
$tpl->assign('COMMENTS', $data);
$tpl->assign('PAGINATION', $generate_page);
$tpl->assign('TOKEN', NV_CHECK_SESSION);
$tpl->assign('URL', $base_url);

$tpl->assign('TYPES', [
    0 => [
            'selected' => $array_search['type'] == 0 ? 'selected' : '',
            'label' => $nv_Lang->getModule('all'),
        ],
    1 => [
            'selected' => $array_search['type'] == 1 ? 'selected' : '',
            'label' => $nv_Lang->getModule('role_ai'),
        ],
    2 => [
            'selected' => $array_search['type'] == 2 ? 'selected' : '',
            'label' => $nv_Lang->getModule('role_expert'),
        ]
]);

$tpl->assign('STATUSES', array_merge([
    [
        'value' => 0,
        'label' => $nv_Lang->getModule('all'),
        'selected' => $array_search['status'] == 0 ? 'selected' : ''
    ]
], array_map(function ($status) use ($array_search) {
    return [
        'value' => $status->value,
        'label' => $status->getLabel(),
        'selected' => $array_search['status'] == $status->value ? 'selected' : ''
    ];
}, CommentStatus::cases())));

$tpl->assign('REFUND_STATUSES', array_merge([
    [
        'value' => 0,
        'label' => $nv_Lang->getModule('all'),
        'selected' => $array_search['refund_status'] == 0 ? 'selected' : ''
    ]
], array_map(function ($status) use ($array_search) {
    return [
        'value' => $status->value,
        'label' => $status->getLabel(),
        'selected' => $array_search['refund_status'] == $status->value ? 'selected' : ''
    ];
}, RefundStatus::cases())));

$tpl->assign('RATING', [
    0 => [
            'selected' => $array_search['rating_status'] == 0 ? 'selected' : '',
            'label' => $nv_Lang->getModule('all'),
    ],
    1 => [
            'selected' => $array_search['rating_status'] == 1 ? 'selected' : '',
            'label' => $nv_Lang->getModule('rating_status_yes'),
        ],
    2 => [
            'selected' => $array_search['rating_status'] == 2 ? 'selected' : '',
            'label' => $nv_Lang->getModule('rating_status_no'),
        ]
]);

$paid_cat_ids = array_column($array_paid_cats, 'cat_id');
$query_cat = $db->query('SELECT userid FROM ' . $db_config['prefix'] . '_' . $module_data . '_cat_admins WHERE cat_id IN (' . implode(',', $paid_cat_ids) . ')');
$experts = [];
$admin_ids = [];
while ($cat = $query_cat->fetch()) {
    $admin_ids[] = $cat['userid'];
}
$admin_ids = array_unique($admin_ids);
foreach ($admin_ids as $id) {
    if(isset($array_admin_listall[$id])) {
        $array_admin_listall[$id]['selected'] = $array_search['expert'] == $id ? 'selected' : '';
        $array_admin_listall[$id]['fullname'] = nv_show_name_user($array_admin_listall[$id]['first_name'], $array_admin_listall[$id]['last_name'], $array_admin_listall[$id]['username']);
        $experts[] = $array_admin_listall[$id];
    }
}

$tpl->assign('EXPERTS', $experts);

$page_title = $nv_Lang->getModule('answers');
$contents = $tpl->fetch('answers.tpl');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
