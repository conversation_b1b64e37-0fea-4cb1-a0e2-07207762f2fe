<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2023 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_url = $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
$array_search = [];
$where = [];

$array_search['type'] = $nv_Request->get_title('type', 'get', 'tool');
$curent_from = date('d-m-Y', strtotime('first day of this month'));
$curent_to = date('d-m-Y', strtotime('now'));
$array_search['time_from'] = $nv_Request->get_title('time_from', 'get', $curent_from);
$array_search['time_to'] = $nv_Request->get_title('time_to', 'get', $curent_to);
$array_search['u_time_from'] = nv_d2u_get($array_search['time_from']);
$array_search['u_time_to'] = nv_d2u_get($array_search['time_to'], 23, 59, 59);

$base_url .= '&amp;type=' . $array_search['type'];

$where['AND'][] = [
    'IN' => [
        'comment_type' => '(' . CommentType::AI->value .',' . CommentType::Expert->value . ')'
    ]
];
$where['AND'][] = [
    '=' => [
        'delete_time' => 0
    ]
];

if ($array_search['type'] == 'tool') {
    $where['AND'][] = [
        '=' => [
            'status' => CommentStatus::Invalid->value
        ]
    ];
} else {
    $where['AND'][] = [
        '=' => [
            'invalid' => 1
        ]
    ];
}

if ($array_search['u_time_from'] > 0) {
    $base_url .= '&amp;time_from=' . urlencode($array_search['time_from']);
    $where['AND'][] = [
        '>=' => [
            'add_time' => $array_search['u_time_from']
        ]
    ];
}

if ($array_search['u_time_to'] > 0) {
    $base_url .= '&amp;time_to=' . urlencode($array_search['time_to']);
    $where['AND'][] = [
        '<=' => [
            'add_time' => $array_search['u_time_to']
        ]
    ];
}

// Fetch Limit
$per_page = 20;
$page = $nv_Request->get_int('page', 'post,get', 1);

$params = [
    'page' => $page,
    'perpage' => $per_page
];

// Nếu có điều kiện where thì gán
if (!empty($where)) {
    $params['where'] = $where;
}

// GỌI API
$List = nv_local_api('ListAllComment', $params, $admin_info['username'], $module_name);
$ListAllComment = json_decode($List, true);
$data = [];
$generate_page = '';
if ($ListAllComment['status'] == 'success') {
    foreach ($ListAllComment['data'] as $view) {
        $view['link_detail'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=ticket_detail_paid&amp;id=' . $view['ticket_id'];
        $view['status'] = CommentStatus::tryFrom(intval($view['status']))?->getLabel();
        $view['reply_user'] = $view['reply_userid'] == 0 ? $nv_Lang->getModule('role_ai') : get_user_info($view['reply_userid'])['fullname'];
        $view['add_time'] = ($view['edit_time'] > 0) ? nv_datetime_format($view['edit_time']) : nv_datetime_format($view['add_time']);
        $data[] = $view;
    }
    $generate_page = nv_generate_page($base_url, $ListAllComment['total'], $ListAllComment['perpage'], $ListAllComment['page']);
}

$template = get_tpl_dir([$global_config['module_theme'], $global_config['admin_theme']], 'admin_default', '/modules/' . $module_file . '/invalid_answers.tpl');
$tpl = new \NukeViet\Template\NVSmarty();
$tpl->registerPlugin('modifier', 'nformat', 'nv_number_format');
$tpl->registerPlugin('modifier', 'dformat', 'nv_datetime_format');
$tpl->setTemplateDir(NV_ROOTDIR . '/themes/' . $template . '/modules/' . $module_file);
$tpl->assign('LANG', $nv_Lang);
$tpl->assign('MODULE_NAME', $module_name);
$tpl->assign('OP', $op);
$tpl->assign('SEARCH', $array_search);
$tpl->assign('COMMENTS', $data);
$tpl->assign('PAGINATION', $generate_page);
$tpl->assign('TAB_TOOL', [
    'url' => $page_url . '&amp;type=tool',
    'active' => $array_search['type'] == 'tool' ? 'active' : '',
]);
$tpl->assign('TAB_ADMIN', [
    'url' => $page_url . '&amp;type=admin',
    'active' => $array_search['type'] == 'admin' ? 'active' : '',
]);
$tpl->assign('URL', $base_url);

$page_title = $nv_Lang->getModule('invalid_answers');
$contents = $tpl->fetch('invalid_answers.tpl');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
