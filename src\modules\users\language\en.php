<?php

/**
 * NukeViet Content Management System
 * @version 5.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2025 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_MAINFILE')) {
    exit('Stop!!!');
}

$lang_translator['author'] = 'VINADES.,JSC <<EMAIL>>';
$lang_translator['createdate'] = '20/07/2023, 07:15';
$lang_translator['copyright'] = '@Copyright (C) 2010 VINADES.,JSC. All rights reserved';
$lang_translator['info'] = '';
$lang_translator['langtype'] = 'lang_module';

$lang_module['notallowuserlogin'] = 'Sorry, login temporary not available';
$lang_module['register'] = 'Register';
$lang_module['userlogout'] = 'Log out of a user\'s account';
$lang_module['userloginviaopt'] = 'Login by port';
$lang_module['login_title'] = 'If you had an account';
$lang_module['lostpass_title'] = 'Are you forgot your password';
$lang_module['memberlist'] = 'Member list';
$lang_module['gohome'] = 'Home page';
$lang_module['info'] = 'To register, you must complete this form';
$lang_module['name'] = 'Name';
$lang_module['first_name'] = 'First Name';
$lang_module['last_name'] = 'Last Name';
$lang_module['gender'] = 'Gender';
$lang_module['male'] = 'Male';
$lang_module['female'] = 'Female';
$lang_module['na'] = 'No Answer';
$lang_module['account'] = 'Account';
$lang_module['sig'] = 'Signature';
$lang_module['email'] = 'Email';
$lang_module['check'] = 'Check';
$lang_module['page'] = 'Page %d';
$lang_module['creat'] = 'Create new';
$lang_module['password'] = 'Password';
$lang_module['re_password'] = 'Repeat password';
$lang_module['question'] = 'Security Question';
$lang_module['select_question'] = 'Please select question';
$lang_module['your_question'] = 'Or your question';
$lang_module['answer_question'] = 'Your answer';
$lang_module['answer'] = 'Answer';
$lang_module['captcha'] = 'Security code';
$lang_module['code'] = 'Code';
$lang_module['retype_captcha'] = 'Enter the Security code';
$lang_module['usage_terms'] = 'Terms to register';
$lang_module['accept'] = 'I agree!';
$lang_module['accept2'] = 'I agree with';
$lang_module['avatar'] = 'Avatar';
$lang_module['avatar_pagetitle'] = 'Upload avatar';
$lang_module['avatar_bigfile'] = 'File capacity allowed: <= %1$s';
$lang_module['avatar_bigsize'] = 'Max image size allowed (px): %1$s x %2$s';
$lang_module['avatar_smallsize'] = 'Minimum image size allowed (px): %1$s x %2$s';
$lang_module['avatar_filetype'] = 'File format allowed: *.jpg, *.png';
$lang_module['avatar_filesize'] = 'Capacity';
$lang_module['avatar_ftype'] = 'Type';
$lang_module['avatar_filedimension'] = 'Size';
$lang_module['avatar_displaydimension'] = 'Display dimension';
$lang_module['avatar_guide'] = 'Please follow the steps below to change your avatar';
$lang_module['avatar_crop'] = 'Crop image';
$lang_module['avatar_chosen'] = 'Select the image by click the box at right';
$lang_module['avatar_chosen_other'] = 'Select image other';
$lang_module['avatar_upload'] = 'Select area and click "Crop image"';
$lang_module['avatar_delete'] = 'Avatar delete';
$lang_module['avatar_clear'] = 'Avatar clear';
$lang_module['avatar_select_img'] = 'Click here to select file...';
$lang_module['avatar_error_data'] = 'Error: incomplete data, please follow the';
$lang_module['avatar_error_save'] = 'Error: The system does not perform the request, please try again';
$lang_module['avatar_old_not_exists'] = 'Old avatar does not exist';
$lang_module['avatar_news_not_exists'] = 'New avatar does not exist';
$lang_module['avatar_news_copy_error'] = 'Unable to save avatar';
$lang_module['birthday'] = 'Birthday';
$lang_module['showmail'] = 'Display email';
$lang_module['login'] = 'Login';
$lang_module['login_with'] = 'Sign in with';
$lang_module['login_submit'] = 'Login';
$lang_module['user_info'] = 'Member information';
$lang_module['regdate'] = 'Registration date';
$lang_module['yes'] = 'Yes';
$lang_module['prev_login'] = 'Previous login';
$lang_module['last_login'] = 'Last login';
$lang_module['register_ok'] = 'Register successfully';
$lang_module['login_ok'] = 'Login successfully';
$lang_module['logout_ok'] = 'Logout successfully';
$lang_module['login_no_active'] = 'Error: Your account currenly locked';
$lang_module['redirect_to_back'] = 'Click here to go back previous page';
$lang_module['redirect_to_login'] = 'Click here to go to login page';
$lang_module['redirect_to_home'] = 'Click here to go to home page';
$lang_module['lostpass'] = 'Forgot password?';
$lang_module['lostpass_info'] = 'Are you realy forgot your password?';
$lang_module['lostpass_info1'] = 'Please complete this form to reset your password.';
$lang_module['lostpass_info2'] = 'If you doesn\'t remember those requirements, please contact administrator.';
$lang_module['lostpass_submit'] = 'Submit';
$lang_module['no_allowuserreg'] = 'Sorry, register temporary not available.';
$lang_module['err_no_save_account'] = 'Can\'t update your account information, please contact administrator.';
$lang_module['full_name_empty'] = 'Empty name';
$lang_module['your_question_empty'] = 'Empty question';
$lang_module['answer_empty'] = 'Empty answer';
$lang_module['agreecheck_empty'] = 'You must agree with these term to complete register';
$lang_module['account_registered_name'] = 'Sorry, account %s registered by another member. If you forgot your password, using Lost password function';
$lang_module['account_deny_name'] = 'Sorry, Account %s banned.';
$lang_module['email_deny_name'] = 'Sorry, email %s banned';
$lang_module['email_registered_name'] = 'Email %s was registered. Using lost password function to reset password';
$lang_module['account_register_to_admin'] = 'Your account was created. We\'ll check your register information and notify you soon.';
$lang_module['account_active_mess'] = 'Your account was created. We sent information to your e-mail,<br /><br /> please follow instructions to activate account';
$lang_module['account_active_mess_error_mail'] = 'Error: Your account was created, but system can not send email to activate your account, please contact administrator.';
$lang_module['account_active_ok'] = 'Account activated';
$lang_module['account_active_error'] = 'Error: Fail to activate your account. Please check activate link or activate link is expired time';
$lang_module['editinfo_pagetitle'] = 'Account Settings';
$lang_module['editinfo'] = 'Edit info';
$lang_module['editinfo_confirm'] = 'Confirm';
$lang_module['editinfo_password_notice'] = 'Leave 2 password fields blank if you don\'t want to change password';
$lang_module['lostactive_pagetitle'] = 'Activate link';
$lang_module['lostactive_noactive'] = 'You had non activate link';
$lang_module['lostactive_info1'] = 'Complete this form to get brand new activate link.';
$lang_module['lostactive_info2'] = 'If you doesn\'t remember those requirements, please contact administrator.';
$lang_module['lostactive_noinfo'] = 'Your infor doesn\'t match';
$lang_module['lostactive_error_link'] = 'Invalid link';
$lang_module['change_pass'] = 'Change password';
$lang_module['change_info'] = 'Complete this form to change password';
$lang_module['pass_old'] = 'Old password';
$lang_module['pass_new'] = 'New password';
$lang_module['pass_new_re'] = 'Repeat password';
$lang_module['change_pass_ok'] = 'Password changed';
$lang_module['lostpass_content_mess'] = 'An email is sent to %1$s, please follow istruction to activate';
$lang_module['lostpass_active_error'] = 'The Verification code does not match!';
$lang_module['lostpass_newpass_mess'] = 'Let declare a new password in this box';
$lang_module['lostpass_sendmail_error'] = 'Currently the system can not send email so you can temporarily not get your password, please contact the site administrator for assistance. Honestly apologize for this inconvenience!';
$lang_module['logout_title'] = 'Logout';
$lang_module['edit_info_title'] = 'Edit';
$lang_module['img_size_title'] = 'Your avatar';
$lang_module['change_avatar'] = 'Change avatar';
$lang_module['email_active_mes'] = 'A verification code has been sent to the new email address that you provided. Please copy this code here';
$lang_module['email_active_error_mail'] = 'Your account is updated, but system cannot send email to confirm your new email. Please contact to website administrator for helps.';
$lang_module['st_login'] = 'Regular login';
$lang_module['st_login2'] = 'Regular login';
$lang_module['admin_login'] = 'Login by admin account';
$lang_module['mode_login_1'] = 'Regular login';
$lang_module['mode_login_2'] = 'Login by OpenID';
$lang_module['mode_login_3'] = 'Login by Oauth';
$lang_module['mode_login_4'] = 'Login by CAS Server';
$lang_module['mode_login_6'] = 'Login by passkey';
$lang_module['openid_login2'] = 'Login using other OpenID';
$lang_module['openid_note'] = 'OpenID is not registered. Please choose one of the following options';
$lang_module['openid_without_email_note'] = 'The information provided by the third-party does not include an email address, so we are unable to automatically create an account for you.';
$lang_module['openid_connect_note'] = 'To login by this OpenID, you must link it with your existed account. Please declare your login information (required one time only)';
$lang_module['openid_create_note'] = 'Please create a new account to assign this OpenID to it.';
$lang_module['openid_processing_connect'] = 'I want to connect this third-party account to an existing account';
$lang_module['openid_processing_create'] = 'I want to manually create a new account to connect it to this third party account';
$lang_module['openid_processing_auto'] = 'Please create me a new account to connect it to this third-party account';
$lang_module['openid_administrator'] = 'Third party accounts';
$lang_module['no'] = 'No';
$lang_module['canceled_authentication'] = 'You cancelled access by OpenID';
$lang_module['not_logged_in'] = 'You still not login by OpenID';
$lang_module['logged_in_failed'] = 'OpenID server doesn\'t provide some required information to login to our website';
$lang_module['logged_no_email'] = 'Unfortunately, this app does not offer e-mail, please check that you have declared for the application email then try again';
$lang_module['openid_confirm_failed'] = 'You provided wrong infomation';
$lang_module['openid_confirm_info'] = 'The email address just provided by %1$s (<strong>%2$s</strong>) has been used for a certain account. If this is your account, please confirm by declaring the password. We will connect it to your %1$s account after you confirm it successfully.';
$lang_module['openid_lostpass_info'] = 'Because your account is only allowed to log in through a third-party account, we can\'t provide you with a new account password. Log in to your account through your registered third-party account, then go to the account settings area to add a password.';
$lang_module['lostpass_no_info1'] = 'Empty user name or email';
$lang_module['lostpass_no_info2'] = 'Can\'t find account matched your provided information';
$lang_module['lostpass_no_info3'] = 'Empty new password';
$lang_module['lostpass_no_info4'] = 'Password does not match';
$lang_module['lostpass_page_title'] = 'Get password';
$lang_module['lostpass_question_empty'] = 'You doesn\'t specify security question or answer. We can\'t provide new password for you. Please contact site admin for more information';
$lang_module['answer_failed'] = 'Your answer is not correct';
$lang_module['lostpass_question'] = 'Question';
$lang_module['step1'] = 'Step 1';
$lang_module['step2'] = 'Step 2';
$lang_module['lostactivelink_question_empty'] = 'You doesn\'t specify security question or answer. We can\'t provide new activate link for you. Please contact site admin for more information';
$lang_module['lostactivelink_no_info1'] = 'Empty user name or email';
$lang_module['lostactivelink_no_info2'] = 'Can\'t find account match your provided information';
$lang_module['lostactivelink_send'] = 'Activate link sent to email %s';
$lang_module['lostactivelink_submit'] = 'Submit';
$lang_module['resend_activelink'] = 'Not receive activate link after registered';
$lang_module['change_question_ok'] = 'Reserve question was changed';
$lang_module['editinfo_error'] = '. Notice %s';
$lang_module['editinfo_ok'] = 'Updated';
$lang_module['editinfo_okcensor'] = 'Your information has been saved and will take effect after being moderated. While waiting for approval, you can continue to edit the information';
$lang_module['question2'] = 'Reserve question';
$lang_module['current_mode'] = 'Current login';
$lang_module['current_login'] = 'Current login at';
$lang_module['or_activate_account'] = 'or activate your exists account';
$lang_module['account2'] = 'Account';
$lang_module['ip'] = 'By IP';
$lang_module['pass_empty_note'] = 'Warnning: You can\'t using regular login because you doesn\'t specific password. Please click <a href="%s">here</a> to create password';
$lang_module['question_empty_note'] = 'Warnning: You can\'t using lost password feature because you doesn\'t specific security question. Please click <a href="%s">here</a> to specific';
$lang_module['change_name_info'] = 'Warnning: You can change your username only one. This feature will disable on next login. Please click <a href="%s">here</a> to change';
$lang_module['openid_del'] = 'Disconnect';
$lang_module['openid_is_exists'] = 'Unfortunately this third party account is already in use. Try choosing another provider';
$lang_module['openid_is_wrongdata'] = 'The data is incorrect, please try again';
$lang_module['openid_add_new'] = 'Connect a third-party account to yours';
$lang_module['email_is_exists'] = 'Unfortunately, this third-party account\'s email is already in use. Please choose another supplier';
$lang_module['error_update_users_info'] = 'Error: System cannot update your user infomation, please contact site admin.';
$lang_module['account_active_log'] = 'Activate account';
$lang_module['openid_add'] = 'Third-party account connection';
$lang_module['no_act'] = 'This function is currently not active';
$lang_module['account_change_mail_ok'] = 'Email adress has been confirmed sucessfully';
$lang_module['account_change_mail_error'] = 'There\'s an error in process of confirming email adress, please contact website administrator for more instruction.';
$lang_module['listusers'] = 'Members List';
$lang_module['listusers_sort_by'] = 'sort by %s, %s';
$lang_module['listusers_sort_by_username'] = 'Username';
$lang_module['listusers_sort_by_gender'] = 'Gender';
$lang_module['listusers_sort_by_regdate'] = 'Join date';
$lang_module['listusers_order_DESC'] = 'Descending';
$lang_module['listusers_order_ASC'] = 'Ascending';
$lang_module['notuser'] = 'Members do not exist under this option!';
$lang_module['field_match_type_error'] = '%s doesn\'t match the rule';
$lang_module['field_min_max_value'] = 'You must fill in the %1$s field from %2$s to %3$s characters';
$lang_module['field_min_max_value_date'] = 'You must fill in the %1$s field from %2$s to %3$s';
$lang_module['field_min_max_error'] = 'You must fill in the %1$s field from %2$s to %3$s characters';
$lang_module['field_match_type_required'] = '%s is required to be declared';
$lang_module['errorMessage'] = 'Notifications from website';
$lang_module['login_info'] = 'Please complete this form';
$lang_module['old_min_user_error'] = 'Your age is under %s so it is not old enough to register';
$lang_module['field_uname_error'] = 'Please leave it blank or enter a value without special characters';
$lang_module['field_req_uname_error'] = 'Please enter a value without special characters';
$lang_module['edit_basic'] = 'Basic';
$lang_module['edit_others'] = 'Other';
$lang_module['edit_avatar'] = 'Avatar';
$lang_module['edit_login'] = 'Username';
$lang_module['edit_email'] = 'Email';
$lang_module['edit_question'] = 'Security question';
$lang_module['edit_password'] = 'Password';
$lang_module['edit_passkey'] = 'Passkey';
$lang_module['edit_seckey'] = 'Security key';
$lang_module['edit_login_warning'] = 'To change username , you need delare your password.';
$lang_module['edit_email_warning'] = 'To change the email , you should perform the following sequential steps : <br />1.Declaring password <br />2.Declaring new email address <br />3.Click on the button Send Verification Code<br />4.Check the mail notification is sent to the verification code address that you just declare, then enter this code in the box Verification code <br /> 5.Click Accept button.';
$lang_module['edit_question_warning'] = 'Please complete the fields below to create a new security question and answer (replacing the old security question and answer).';
$lang_module['edit_password_warning'] = 'to change password, you need declare your password.';
$lang_module['newlogin'] = 'New username';
$lang_module['newemail'] = 'New email';
$lang_module['newquestion'] = 'New question security';
$lang_module['currentlogin'] = 'Current username';
$lang_module['currentemail'] = 'Current email';
$lang_module['verifykey_send'] = 'Sent verification code';
$lang_module['verifykey'] = 'The verification code';
$lang_module['verifykey_error'] = 'the verification code does not match';
$lang_module['verifykey_empty'] = 'The verification code is undeclared';
$lang_module['verifykey_issend'] = 'A verifykey have been sent a moment ago. After %1$d  minutes you can request for other verifykey .';
$lang_module['email_not_change'] = 'Email nothing new other than email is being used !';
$lang_module['required'] = 'This field is required';
$lang_module['safe_mode'] = 'Safe mode';
$lang_module['safe_activate'] = 'Turn on safe mode';
$lang_module['safe_deactivate'] = 'Turn off safe mode';
$lang_module['safe_key'] = 'Verification code';
$lang_module['safe_resendkey'] = 'Resend verification code';
$lang_module['safe_send_ok'] = 'Verifykey safety regime has been sent to your email. This operation can be performed again %1$d minute.';
$lang_module['safe_activate_info'] = '<p><strong>Safe mode is off at current!</strong></p><p>- Unless you need to edit your account information, you should turn this mode on. It will help you avoid unwanted changes.</p><p>- When you activate the safe mode, the system will send a verifykey to your email box. Use that verifykey turn off safe mode later. The verifykey is valid between on and off only. After you turn off safe mode, this verifykey will be worthless.</p><p>- To enable safe mode, you follow these steps:</p><p>1.Declare your login password<br />2.Click on the button Send verifykey<br />3.Check mail notification verifykey and scripting key to verify that the box below<br />4.Click on the Accept button.</p>';
$lang_module['safe_deactivate_info'] = 'To turn off safe mode, you need to declare password and verification code. The verification code has been sent to your email when you turn the safe mode on. If you can not find the verification code, please click submit button to get other verification code.';
$lang_module['safe_active_info'] = 'Your account is in safe mode, so all functions for editing your account information are blocked. To disable this mode, you need to know the verification code sent to you earlier.';
$lang_module['safe_deactivate_ok'] = 'Safe mode is turned off.';
$lang_module['safe_activate_ok'] = 'Safe mode is turned on.';
$lang_module['safe_deactivate_openidlogin'] = 'Temporarily does not support logging in with a third-party account. Please login by declaring username and password.';
$lang_module['safe_deactivate_openidreg'] = 'Email provided by a third-party has been associated with an account on this website. But unfortunately that account currently doesn\'t support signing in with a third-party account. If this is your account, please log in using your alias and password.';
$lang_module['safe_deactive_notvalid'] = '. Account has no login password and can not secure mode enabled.Create your login password and then return to this  page';
$lang_module['changelogin_notvalid'] = 'Account has no password, so you can not change your username. Create your login password and then return to this page';
$lang_module['changequestion_notvalid'] = 'Account  has no password so you can not change your security question . Create your login password after do return to this page';
$lang_module['changeemail_notvalid'] = 'Account has no password so you can not change email. Create your login password then return to this  page.';
$lang_module['add_pass'] = 'Create password';
$lang_module['openid_server'] = 'Provider';
$lang_module['openid_email_or_id'] = 'E-mail or ID';
$lang_module['openid_deleted'] = 'Disconnected from selected third-party accounts';
$lang_module['openid_choose'] = 'Please select at least one third-party account';
$lang_module['openid_default'] = 'This third-party account cannot be disconnected because it is being used by you to log into your account';
$lang_module['openid_added'] = 'The third-party account has been successfully connected to your account. Please wait a few seconds...';
$lang_module['openid_not_found'] = 'Third-party account not connected';
$lang_module['group'] = 'Group';
$lang_module['no_in_group'] = 'You have not registered in any group';
$lang_module['group_name'] = 'Name of group';
$lang_module['group_description'] = 'Description';
$lang_module['group_userr'] = 'Member';
$lang_module['group_reg'] = 'Join';
$lang_module['group_manage'] = 'Group manage';
$lang_module['return_group_manage'] = 'Return to %s Group Management';
$lang_module['in_group_ok'] = 'Setting';
$lang_module['lostpass_key'] = 'The Verification code';
$lang_module['group_status'] = 'Status';
$lang_module['group_status_0'] = 'Not joined';
$lang_module['group_status_1'] = 'Joined';
$lang_module['group_status_2'] = 'Pending';
$lang_module['group_type'] = 'Group type';
$lang_module['group_type_0'] = 'Fixed group';
$lang_module['group_type_1'] = 'Closed group';
$lang_module['group_type_2'] = 'Open group';
$lang_module['group_type_1_note'] = 'can only be joined by request';
$lang_module['group_type_2_note'] = 'anyone can join';
$lang_module['group_manage_count'] = 'Group manage number';
$lang_module['group_manage_list'] = 'List groups manage';
$lang_module['group_exp_time'] = 'Valid until';
$lang_module['group_exp_unlimited'] = 'Unlimited';
$lang_module['group_edit'] = 'Edit group';
$lang_module['group_title'] = 'Group name';
$lang_module['group_desc'] = 'Description';
$lang_module['group_content'] = 'Content';
$lang_module['group_title_empty'] = 'The group name cannot be left blank';
$lang_module['group_edit_saved'] = 'Changes have been saved';
$lang_module['lastname_firstname'] = 'Lastname and Firstname';
$lang_module['members_in_group_caption'] = 'List of members';
$lang_module['leaders_in_group_caption'] = 'List of leaders';
$lang_module['pending_in_group_caption'] = 'List of waiting accounts';
$lang_module['error_group_not_found'] = 'Error: Can\'t find this group';
$lang_module['error_users_not_found'] = 'Group has not member';
$lang_module['error_group_in_site'] = 'Error: You can only add or remove members who are users of your site';
$lang_module['error_not_groups'] = 'No groups have been created yet. Click <a href="%s">here</a> to start creating a group';
$lang_module['search_not_result'] = 'Not found any results';
$lang_module['add_users'] = 'Add account';
$lang_module['exclude_user2'] = 'Remove from group';
$lang_module['approved'] = 'Approved';
$lang_module['denied'] = 'Denied';
$lang_module['search_id'] = 'Member\'s ID';
$lang_module['addMemberToGroup'] = 'Add user to group';
$lang_module['delConfirm'] = 'Are you sure to delete';
$lang_module['excludeUserConfirm'] = 'Are you sure you want to remove this member from the group';
$lang_module['choiceUserID'] = 'Please declare account name';
$lang_module['no_premission'] = 'No premission';
$lang_module['no_premission_leader'] = 'Only leader have premission this area. Please back!';
$lang_module['title'] = 'Group name';
$lang_module['add_time'] = 'Start time';
$lang_module['exp_time'] = 'End time';
$lang_module['users'] = 'User';
$lang_module['addusers'] = 'Add new user';
$lang_module['del_user_err'] = 'Delete user failed';
$lang_module['user_waiting'] = 'User waiting';
$lang_module['firstname_lastname'] = 'Firstname and Lastname';
$lang_module['search'] = 'Search user';
$lang_module['reset'] = 'Reset';
$lang_module['username'] = 'Username';
$lang_module['user_id'] = 'ID';
$lang_module['active'] = 'Active';
$lang_module['active_users'] = 'Activate user';
$lang_module['actived_users'] = 'The user has been successfully activated!';
$lang_module['not_active'] = 'Can\'t active user, please check again!';
$lang_module['noresult'] = 'There were no results matching your request';
$lang_module['STT'] = 'Order';
$lang_module['min_search'] = 'Please enter at least %s characters';
$lang_module['no_premission_pass'] = 'You are not allowed to change the member password!';
$lang_module['info_user'] = 'Complete the form below to add new user';
$lang_module['note_remove_leader'] = 'You can not remove yourself from the group!';
$lang_module['not_exclude_leader'] = 'You cannot exclude leader from the group';
$lang_module['not_del_leader'] = 'You cannot delete a group leader account';
$lang_module['not_del_user'] = 'You can\'t delete this user because user is member of other group. You can only remove this user from your group';
$lang_module['UserNotInGroup'] = 'This user not in your group';
$lang_module['for_admin'] = 'For admin';
$lang_module['2step_require_title'] = 'Requires enabled two-step authentication';
$lang_module['2step_require_content'] = 'You must enable two-factor authentication can login. The system will automatically switch to activate this page momentarily';
$lang_module['2step_require_directgo'] = 'Click here if the system did not redirect';
$lang_module['2step_error_opt_backup'] = 'Please enter the code provided by the authenticator app or the saved backup code';
$lang_module['2step_status'] = 'Two-step authentication';
$lang_module['2step_link'] = 'Settings';
$lang_module['pass_reset1_info'] = 'For security reasons, please change your password before using the site\'s facilities.';
$lang_module['pass_reset2_info'] = 'For security reasons, we recommend that you change your password before using the site\'s facilities. Please <a href="%s">click here</a> to change your password.';
$lang_module['pass_reset3_info'] = 'It\'s been more than %d days and you haven\'t changed your user account password. For security reasons, please change your password before using the site\'s facilities.';
$lang_module['email_reset1_info'] = 'For security reasons, you are required to change your account email here before using the site\'s facilities.';
$lang_module['email_reset2_info'] = 'For security reasons, we recommend that you change your email before using the site\'s facilities. Please click <a href="%s">here</a> to change your email.';
$lang_module['password_was_used'] = 'The password is not available because it was used in one of the last %d password changes.';
$lang_module['another_option'] = 'Another option';
$lang_module['verify_mail_error'] = 'Error: The e-mail verification code has not been entered or is entered incorrectly.';
$lang_module['verify_email_sent'] = 'Verification code has been sent to the address you provided above (If you don\'t see it in inbox folder, check SPAM folder). Copy and paste it into the Verification Code box below';
$lang_module['verify_email_sent_error'] = 'There was a technical error while sending the verification code to your email. Please contact the site administrator about this.';
$lang_module['forcedrelogin'] = 'Forced re-login everywhere';
$lang_module['forcedrelogin_note'] = 'You are signed out of your account. Please login again';
$lang_module['welcome_new_account'] = 'Welcome to %s! You have successfully registered. Let\'s start with setting up your account... We hope you\'ll have a pleasant and rewarding time on our website.';
$lang_module['welcome_new_member'] = 'Welcome to the %1$s group! You have now become its official member. We wish you many enjoyable experiences with %1$s group.';
$lang_module['info_when_leaving_group'] = 'Unfortunately, you left the %s group. We hope that we will have the opportunity to serve you again.';
$lang_module['login_name'] = 'Login name is valid as';
$lang_module['username_or_email'] = 'Username or Email';
$lang_module['bydatalang'] = 'By data language';
$lang_module['addfile'] = 'Add file';
$lang_module['addfile_size_error'] = 'The file size you are about to upload';
$lang_module['addfile_size_error2'] = 'is larger than allowed';
$lang_module['addfile_ext_error'] = 'The file type that you are preparing to upload is not accepted. Please only upload files with the extension:';
$lang_module['file_empty'] = 'File not uploaded yet';
$lang_module['file_extension_does_not_match'] = 'The file\'s extension does not match its mimetype';
$lang_module['file_extension_not_accepted'] = 'File extension not accepted';
$lang_module['file_image_size_error'] = 'The size of the uploaded image does not match';
$lang_module['file_image_width'] = 'Width of the image';
$lang_module['file_image_height'] = 'Height of the image';
$lang_module['equal'] = 'should be equal to';
$lang_module['greater'] = 'must be greater than or equal to';
$lang_module['less'] = 'must be less than or equal to';
$lang_module['accepted_extensions'] = 'Accepted extensions';
$lang_module['field_file_max_size'] = 'Maximum size of uploaded file';
$lang_module['file_not_allowed'] = 'You are not allowed to view/download this file or the file does not exist';
$lang_module['delete'] = 'Delete';
$lang_module['g_id_confirm'] = 'Google-Identity is ready to submit your personal information. We will use your personal information including name, email address for account verification purposes on this website.';
$lang_module['g_id_confirm2'] = 'Please click here to confirm';
$lang_module['avatar_size'] = 'Avatar size';
$lang_module['dir_forum'] = 'Dir forum';
$lang_module['modforum'] = 'Members management by forum %1$s.';
$lang_module['list_module_title'] = 'Members list';
$lang_module['member_add'] = 'Add member';
$lang_module['member_wating'] = 'Member wating';
$lang_module['list_question'] = 'Question list';
$lang_module['search_type'] = 'Search by';
$lang_module['search_account'] = 'Member\'s account';
$lang_module['search_name'] = 'Member\'s name';
$lang_module['search_mail'] = 'Member\'s email';
$lang_module['search_oauth'] = 'Oauth account';
$lang_module['search_key'] = 'Keyword search';
$lang_module['search_note'] = 'Keyword maximum length is 64 characters, html isn\'t allowed';
$lang_module['submit'] = 'Search';
$lang_module['members_list'] = 'Member list';
$lang_module['main_title'] = 'Manage member';
$lang_module['userid'] = 'ID';
$lang_module['register_date'] = 'Register date';
$lang_module['status'] = 'Status';
$lang_module['funcs'] = 'Features';
$lang_module['user_add'] = 'Add member';
$lang_module['repassword'] = 'Repeat password';
$lang_module['name_show'] = 'Type display name';
$lang_module['error_language'] = 'Error: You do not select the type of display Name';
$lang_module['date'] = 'Day';
$lang_module['month'] = 'Month';
$lang_module['year'] = 'Year';
$lang_module['show_email'] = 'Display email';
$lang_module['in_group'] = 'Member of group';
$lang_module['in_group_default'] = 'Group default';
$lang_module['clear_group_default'] = 'Clear group default';
$lang_module['is_official'] = 'Is an official member';
$lang_module['is_official_note'] = 'If not selected, this account will be included in the group of new member registration';
$lang_module['set_official_note'] = 'To make an official member';
$lang_module['msgnocheck'] = 'You need to select at least 1 account to perform';
$lang_module['addquestion'] = 'Add Security Question';
$lang_module['savequestion'] = 'Save Security Question';
$lang_module['deletequestion'] = 'Delete security question';
$lang_module['errornotitle'] = 'Error: Empty Security Question';
$lang_module['errorsave'] = 'Error: Can\'t update data, please check topic title';
$lang_module['weight'] = 'position';
$lang_module['save'] = 'Save';
$lang_module['siteterms'] = 'Rules register';
$lang_module['error_content'] = 'Error: Empty site rules';
$lang_module['saveok'] = 'Update successful';
$lang_module['config'] = 'Configuration';
$lang_module['allow_reg'] = 'Allow register';
$lang_module['allow_login'] = 'Allow login';
$lang_module['allow_change_email'] = 'Allow change email';
$lang_module['type_reg'] = 'Register type';
$lang_module['active_not_allow'] = 'Not grant to register';
$lang_module['allow_public'] = 'Allow members register in public groups';
$lang_module['allow_question'] = 'Answer Security Question for Lost Password';
$lang_module['active_admin_check'] = 'Admin active';
$lang_module['active_all'] = 'No need to active';
$lang_module['active_email'] = 'Email activation';
$lang_module['deny_email'] = 'Deny words on member\'s email';
$lang_module['deny_name'] = 'Deny words on member\'s account';
$lang_module['password_simple'] = 'Simple passwords';
$lang_module['memberlist_active'] = 'Active';
$lang_module['memberlist_unactive'] = 'Active';
$lang_module['memberlist_error_method'] = 'Please select search method!';
$lang_module['memberlist_error_value'] = 'Search value must be at least 1 and doesn\'t exceed 64 characters!';
$lang_module['memberlist_nousers'] = 'No result match!';
$lang_module['memberlist_selectusers'] = 'Select at least 1 user to delete!';
$lang_module['checkall'] = 'Select all';
$lang_module['uncheckall'] = 'Unselect all';
$lang_module['delete_success'] = 'Success!';
$lang_module['delete_group_system'] = 'Error: not delete the account because this account is the administrator of the site';
$lang_module['active_success'] = 'Success!';
$lang_module['memberlist_edit'] = 'Edit';
$lang_module['memberlist_deleteconfirm'] = 'Do you realy want to delete?';
$lang_module['edit_title'] = 'Edit';
$lang_module['edit_password_note'] = 'Change password (leave blank if you don\'t want to change password)';
$lang_module['edit_avatar_note'] = 'Leave blank if you don\'t want to change avatar mới';
$lang_module['edit_save'] = 'Accept';
$lang_module['edit_error_username_exist'] = 'User name used by another member. Please choose another name';
$lang_module['edit_error_photo'] = 'File type doesn\'t allowed';
$lang_module['edit_error_email'] = 'Incorrect email';
$lang_module['edit_error_email_exist'] = 'Your email used in another account. Please choose another account.';
$lang_module['edit_error_permission'] = 'You can not change the account information.';
$lang_module['edit_error_password'] = 'Password doesn\'t match';
$lang_module['edit_error_nopassword'] = 'Empty password';
$lang_module['edit_add_error'] = 'Can\'t update member information!';
$lang_module['edit_error_question'] = 'Empty Security Question';
$lang_module['edit_error_answer'] = 'Empty answer';
$lang_module['edit_error_group'] = 'Please select group for member';
$lang_module['awaiting_active'] = 'Activate';
$lang_module['delconfirm_message'] = 'Do you realy want to delete selected member?';
$lang_module['delconfirm_email'] = 'Send notification email:';
$lang_module['delconfirm_email_yes'] = 'Yes';
$lang_module['delconfirm_ok'] = 'Ok!';
$lang_module['adduser_email'] = 'Send notification email:';
$lang_module['adduser_email1'] = 'Send email notification';
$lang_module['adduser_email1_note'] = 'If selected, after creating a successful account the member who created the account will receive an email notification that the account has been created, including login details and password.';
$lang_module['adduser_email1_note1'] = 'If selected, after successfully updating the account members will receive an email notification of account changes, including login information and password (if the password is replaced).';
$lang_module['adduser_email_yes'] = 'Yes';
$lang_module['pass_reset_request'] = 'Change password request';
$lang_module['pass_reset_request0'] = 'No need';
$lang_module['pass_reset_request1'] = 'Required';
$lang_module['pass_reset_request2'] = 'Recommended';
$lang_module['email_reset_request'] = 'Email change request';
$lang_module['email_reset_request0'] = 'No';
$lang_module['email_reset_request1'] = 'Mandatory';
$lang_module['email_reset_request2'] = 'Recommended';
$lang_module['currentpass_created_time'] = 'Current password created time';
$lang_module['currentpass_request_status'] = 'Current request status';
$lang_module['pass_reset_request1_send'] = 'Send request for a mandatory password change';
$lang_module['pass_reset_request2_send'] = 'Send a recommendation to change password';
$lang_module['pass_reset_request_sent'] = 'The request has been sent';
$lang_module['currentemail_created_time'] = 'Current email creation time';
$lang_module['currentemail_request_status'] = 'Current email change request status';
$lang_module['email_reset_request1_send'] = 'Send request for mandatory email change';
$lang_module['email_reset_request2_send'] = 'Send recommendation to change email';
$lang_module['email_reset_request_sent'] = 'The email change request has been sent!';
$lang_module['openid_servers'] = 'Oauth accepted list';
$lang_module['openid_processing'] = 'Actions after receiving the new Oauth';
$lang_module['allow_change_login'] = 'Allow change login name';
$lang_module['is_user_forum'] = 'Use forum\'s users';
$lang_module['search_page_title'] = 'Result';
$lang_module['click_to_view'] = 'Click to view';
$lang_module['level0'] = 'User';
$lang_module['admin_add'] = 'Then go to Create an admin account for this user page';
$lang_module['nv_admin_add'] = 'Add group';
$lang_module['nv_admin_edit'] = 'Edit group';
$lang_module['title_empty'] = 'You do not declare group title';
$lang_module['alias_empty'] = 'You do not declare group alias';
$lang_module['nv_admin_add_caption'] = 'To create a new group, you need to declare fully in the box below';
$lang_module['nv_admin_edit_caption'] = 'To change content of the group &ldquo;%s&rdquo;, you need to declare fully in the box below';
$lang_module['alias'] = 'Alias';
$lang_module['content'] = 'Content';
$lang_module['group_color'] = 'Colour group';
$lang_module['group_avatar'] = 'Avatar group';
$lang_module['group_is_default'] = 'Default groups when members register';
$lang_module['group_del_inactive'] = 'Delete inactive groups';
$lang_module['siteus'] = 'Allow subsite add members to the group';
$lang_module['promote'] = 'Promote';
$lang_module['demote'] = 'Demote';
$lang_module['error_alias_exists'] = 'Group alias "%s" already exist';
$lang_module['form_search_label0'] = 'Search members by';
$lang_module['form_search_label1'] = 'Keyword (Null = all member)';
$lang_module['form_search_select0'] = 'Name';
$lang_module['form_search_select1'] = 'Email';
$lang_module['form_search_select2'] = 'ID';
$lang_module['search_result_caption'] = 'Result';
$lang_module['group_pgtitle'] = 'Detail';
$lang_module['group_info'] = 'Group information &ldquo;%s&rdquo;';
$lang_module['group_user_peding'] = 'Some members would like to join the group "%s"';
$lang_module['add_user'] = 'Member %1$s group %2$s';
$lang_module['exclude_user'] = 'Delete member %1$s in group %2$s';
$lang_module['siteinfo_user'] = 'Members';
$lang_module['siteinfo_waiting'] = 'Unactive members';
$lang_module['siteinfo_editcensor'] = 'Users waiting for moderation of personal information';
$lang_module['pagetitle'] = 'Get Member ID';
$lang_module['pagetitle1'] = 'Search Member ID';
$lang_module['waiting'] = 'Enter the information and then press the search member button to perform';
$lang_module['from'] = 'From';
$lang_module['to'] = 'to';
$lang_module['select'] = 'Select';
$lang_module['enter_key'] = 'Please enter information to find a member';
$lang_module['full_name'] = 'Full Name';
$lang_module['last_idlogin'] = 'IP of the last login';
$lang_module['select_gender'] = 'Select your gender';
$lang_module['select_gender_male'] = 'Male';
$lang_module['select_gender_female'] = 'Female';
$lang_module['changeGroupWeight'] = 'Change the weight of groups';
$lang_module['ChangeGroupAct'] = 'Change status of groups';
$lang_module['delGroup'] = 'Delete group';
$lang_module['ChangeGroupPublic'] = 'Change the type of group';
$lang_module['emptyIsUnlimited'] = 'Empty same with the infinite';
$lang_module['dateError'] = 'Error! Your date is error';
$lang_module['errorChangeWeight'] = 'Error! Can not change the position';
$lang_module['UserInGroup'] = 'This user has on the list of group users';
$lang_module['detail'] = 'Detail';
$lang_module['ChangeConfigModule'] = 'Change module configuration';
$lang_module['unactive_users'] = 'Inactivates members';
$lang_module['whoviewlistuser'] = 'Who can view a list of members';
$lang_module['random_password'] = 'Random Password';
$lang_module['show_password'] = 'Show password';
$lang_module['usactive'] = 'Account status';
$lang_module['usactive_0'] = 'Account locked';
$lang_module['usactive_1'] = 'Account active';
$lang_module['usactive_2'] = 'Account admin blocked';
$lang_module['usactive_3'] = 'Account admin active';
$lang_module['access_register'] = 'Configure register';
$lang_module['nv_unick'] = 'Number characters account';
$lang_module['nv_unick_type'] = 'Characters allow when create account';
$lang_module['nv_upass'] = 'Number characters of password';
$lang_module['unick_type_0'] = 'Use any character';
$lang_module['nv_upass_type'] = 'Complexity of the password';
$lang_module['upass_type_0'] = 'Unlimit';
$lang_module['access_other'] = 'Other configure';
$lang_module['access_caption'] = 'Configure using module';
$lang_module['access_admin'] = 'Group manage';
$lang_module['access_viewlist'] = 'View member list';
$lang_module['access_addus'] = 'Create member';
$lang_module['access_waiting'] = 'Active member';
$lang_module['access_editus'] = 'Edit member';
$lang_module['access_delus'] = 'Delete member';
$lang_module['access_passus'] = 'Change password';
$lang_module['access_groups'] = 'Group manage';
$lang_module['access_caption_leader'] = 'User configuration module powers of the leader';
$lang_module['access_groups_add'] = 'Add members to the group';
$lang_module['access_groups_del'] = 'Remove member from group';
$lang_module['viewlist_error_permission'] = 'You do not have permission to view the account list';
$lang_module['fields'] = 'Custom User Fields';
$lang_module['captionform_add'] = 'Add User Fields';
$lang_module['captionform_edit'] = 'Edit User Fields';
$lang_module['field_edit'] = 'Edit';
$lang_module['field_choices_empty'] = 'Empty Choice Fields';
$lang_module['field_id'] = 'Field ID';
$lang_module['field_id_note'] = 'This is the unique identifier for this field. It cannot be changed once set ';
$lang_module['field_title'] = 'Title';
$lang_module['field_description'] = 'Description';
$lang_module['field_required'] = 'Field is required';
$lang_module['field_required_note'] = 'Required fields will always be shown during register';
$lang_module['field_show_register'] = 'Show during register';
$lang_module['field_user_editable'] = 'User editable';
$lang_module['field_show_profile'] = 'Show on profile pages';
$lang_module['field_type'] = 'Field Type';
$lang_module['field_type_number'] = 'Number';
$lang_module['field_type_date'] = 'Date';
$lang_module['field_type_textbox'] = 'Single-line text box';
$lang_module['field_type_textarea'] = 'Multi-line text box';
$lang_module['field_type_editor'] = 'Editor';
$lang_module['field_type_select'] = 'Drop down selection';
$lang_module['field_type_radio'] = 'Radio Buttons';
$lang_module['field_type_checkbox'] = 'Check Boxes';
$lang_module['field_type_multiselect'] = 'Multiple-choice drop down selection';
$lang_module['field_type_file'] = 'File';
$lang_module['field_type_note'] = 'It cannot be changed once set ';
$lang_module['field_class'] = 'Html class form';
$lang_module['field_size'] = 'Size textarea';
$lang_module['field_options_text'] = 'Options for Text Fields';
$lang_module['field_match_type'] = 'Value Match Requirements:<br>Empty values are always allowed.';
$lang_module['field_match_type_none'] = 'none';
$lang_module['field_match_type_alphanumeric'] = 'A-Z, 0-9, and _ only';
$lang_module['field_match_type_unicodename'] = 'Human name (Unicode letters, dash, apostrophe and space)';
$lang_module['field_match_type_date'] = 'Enter the date in the format';
$lang_module['field_match_type_url'] = 'Url';
$lang_module['field_match_type_regex'] = 'Regular expression';
$lang_module['field_match_type_callback'] = 'Func callback';
$lang_module['field_default_value'] = 'Default Value';
$lang_module['field_min_length'] = 'Min Length';
$lang_module['field_max_length'] = 'Max Length';
$lang_module['field_min_value'] = 'Min value';
$lang_module['field_max_value'] = 'Max value';
$lang_module['field_options_number'] = 'Options for Number Fields';
$lang_module['field_number_type'] = 'Number Type';
$lang_module['field_integer'] = 'Integer';
$lang_module['field_real'] = 'Real';
$lang_module['field_options_date'] = 'Options for Date Fields';
$lang_module['field_current_date'] = 'Use the current date';
$lang_module['field_default_date'] = 'Default Date';
$lang_module['field_min_date'] = 'Min Date';
$lang_module['field_max_date'] = 'Max Date';
$lang_module['field_options_choice'] = 'Options for Choice Fields';
$lang_module['field_number'] = 'STT';
$lang_module['field_value'] = 'Value';
$lang_module['field_text'] = 'Text';
$lang_module['field_add_choice'] = 'More choices';
$lang_module['field_date_error'] = 'Min date must smaller max day';
$lang_module['field_number_error'] = 'Min value must smaller max value';
$lang_module['field_error_empty'] = 'Field do not empty';
$lang_module['field_error_not_allow'] = 'Field do not allow';
$lang_module['field_error'] = 'Field already exists';
$lang_module['field_choicetypes_title'] = 'Choose the type of data';
$lang_module['field_choicetypes_sql'] = 'Retrieve data from database';
$lang_module['field_choicetypes_text'] = 'Get data from input';
$lang_module['field_options_choicesql'] = 'Module selection, data tables and data fields';
$lang_module['field_options_choicesql_module'] = 'Select the module';
$lang_module['field_options_choicesql_table'] = 'Select the data table';
$lang_module['field_options_choicesql_column'] = 'Select the column data';
$lang_module['field_options_choicesql_key'] = 'Select columns as key';
$lang_module['field_options_choicesql_val'] = 'Select column as value';
$lang_module['field_options_choicesql_order'] = 'Select the sort column';
$lang_module['field_options_choicesql_sort'] = 'Select sorting style';
$lang_module['field_options_choicesql_sort_asc'] = 'Ascending';
$lang_module['field_options_choicesql_sort_desc'] = 'Descending';
$lang_module['field_sql_choices_empty'] = 'Error: Selection retrieve data from the database is not full';
$lang_module['field_options_file'] = 'File options';
$lang_module['field_file_exts'] = 'File types allowed to upload';
$lang_module['field_file_exts_error'] = 'Please select at least one file type that is allowed to be uploaded';
$lang_module['field_file_maxnum'] = 'Maximum number of files allowed to upload';
$lang_module['field_photo_max_size'] = 'Upload photo size limits';
$lang_module['field_photo_max_size_note'] = 'Leaving the above boxes blank is interpreted as unlimited image size<br />= size needs to be absolutely correct<br />&#8805; size greater than or equal to<br />&#8804; size less than or equal to';
$lang_module['field_photo_width'] = 'Width';
$lang_module['field_photo_height'] = 'Height';
$lang_module['oauth_config'] = 'Configure login, register width %s';
$lang_module['oauth_client_id'] = 'App ID/API Key/Client ID';
$lang_module['oauth_client_secret'] = 'Secret code';
$lang_module['import'] = 'Import from excel';
$lang_module['import_note'] = 'To import data from Excel files, you need <a title="Download sample file" href="%1$s" /> download sample file , then fill full data file not exceeding 2,000 per account and upload to folder <b />%2$s';
$lang_module['export'] = 'Export data to excel';
$lang_module['export_example'] = 'Sample file data module users';
$lang_module['required_phpexcel'] = 'To use this function you need to install the PHPExcel library, you can download in  <a title="Download PHPExcel" href="https://nukeviet.vn/vi/store/other/phpexcel/">NukeViet Store</a>';
$lang_module['export_comment_userid'] = 'This data should be blank, if you enter this value system will replace the account with corresponding userid';
$lang_module['export_comment_password'] = 'If you do not enter data system will set the default password is the current date';
$lang_module['export_comment_gender'] = 'Accep values​​: M = Male, F = Female';
$lang_module['export_comment_date'] = 'Enter by: Month / Day / Year or empty';
$lang_module['export_complete'] = 'Export data successfully, you just download and unzip the file to retrieve data';
$lang_module['export_note'] = 'The process of data can occur within a few minutes, please wait until the completion notice';
$lang_module['read_note'] = 'To continue the process of reading the data file, select the file you need then click on the execute button, the data read process can take place within a few minutes, please wait until further notice';
$lang_module['read_submit'] = 'Perform';
$lang_module['read_filename'] = 'File name';
$lang_module['read_filesite'] = 'Size';
$lang_module['read_complete'] = 'Reading data successfully, you want to move to site members list';
$lang_module['read_error'] = 'Error reading file %1$s, the system does not update account: %2$s Name: %3$s. So the system is stopped!';
$lang_module['read_error_field'] = 'Error reading file %1$s, you need to check column: %2$s column should be: %3$s. So the system is stopped!';
$lang_module['read_error_memory_limit'] = 'Error: The system can not read the data, please check your data files only to about 2,000 lines per file or you must configure the php.ini file memory_limit value (128MB read about 2,000 lines)';
$lang_module['read_ignore'] = 'Read the incorrect data standards';
$lang_module['update_field'] = 'Upgrade site';
$lang_module['update_field_oncreate'] = 'Once created';
$lang_module['update_field_onlogin'] = 'On login';
$lang_module['cas_config'] = 'Set the CAS server';
$lang_module['cas_config_hostname'] = 'Hostname';
$lang_module['cas_config_hostname_info'] = 'Hostname of the CAS server	<br />eg: cas.nukeviet.vn';
$lang_module['cas_config_baseUri'] = 'Base URI';
$lang_module['cas_config_baseUri_info'] = 'URI of the server (nothing if no baseUri)<br />For example, if the CAS server responds to cas.nukeviet.vn/cas/ then<br />cas_baseuri = cas/';
$lang_module['cas_config_port'] = 'Port';
$lang_module['cas_config_port_info'] = 'Port of the CAS server';
$lang_module['cas_config_version'] = 'CAS protocol version';
$lang_module['cas_config_version_info'] = 'CAS protocol version to use';
$lang_module['cas_config_language'] = 'Language';
$lang_module['cas_config_language_info'] = 'Select language for authentication pages';
$lang_module['cas_config_proxyMode'] = 'Proxy mode';
$lang_module['cas_config_proxyMode_info'] = 'Select \'yes\' if you use CAS in proxy-mode';
$lang_module['cas_config_multiAuthentication'] = 'Multi-authentication';
$lang_module['cas_config_multiAuthentication_info'] = 'Select \'yes\' if you want to have multi-authentication (CAS + other authentication)';
$lang_module['cas_config_serverValidation'] = 'Server validation';
$lang_module['cas_config_serverValidation_info'] = 'Select \'yes\' if you want to validate the server certificate';
$lang_module['cas_config_certificatePath'] = 'Certificate path';
$lang_module['cas_config_certificatePath_info'] = 'Path of the CA chain file (PEM Format) to validate the server certificate';
$lang_module['ldap_config'] = 'The LDAP server settings';
$lang_module['ldap_config_hostURL'] = 'Host URL';
$lang_module['ldap_config_hostURL_info'] = 'Only the LDAP server in the form URL like \'ldap: //ldap.nukeviet.vn/\' or \'ldaps: //ldap.nukeviet.vn/\'.';
$lang_module['ldap_config_version'] = 'Version';
$lang_module['ldap_config_version_info'] = 'The version of the LDAP protocol your server is being used.';
$lang_module['ldap_config_useTLS'] = 'Use TLS';
$lang_module['ldap_config_useTLS_info'] = 'Use regular LDAP service (port 389) with TLS encryption';
$lang_module['ldap_config_LDAPencoding'] = 'LDAP encoding';
$lang_module['ldap_config_LDAPencoding_info'] = 'Specify encoding used by LDAP server. Most probably utf-8, MS AD v2 uses default platform encoding such as cp1252, cp1250, etc.';
$lang_module['ldap_config_PageSize'] = 'Page Size';
$lang_module['ldap_config_PageSize_info'] = 'Make sure this value is smaller than your LDAP server result set size limit (the maximum number of entries that can be returned in a single query)';
$lang_module['rb_config'] = 'The set binding';
$lang_module['rb_config_dn'] = 'Distinguished name';
$lang_module['rb_config_dn_info'] = 'If you want to use binding the user to identify the user, indicating it here. Sometimes it\'s like \'cn = Manager, dc = NukeViet, dc = VN\'';
$lang_module['rb_config_pw'] = 'Password';
$lang_module['rb_config_pw_info'] = 'Password for the user constraints.';
$lang_module['user_config'] = 'The setup user lookup';
$lang_module['user_config_userType'] = 'User type';
$lang_module['user_config_userType_info'] = 'Choose how users are stored in LDAP. These settings also indicate disable login how to create a user and the activity log will look like.';
$lang_module['user_config_context'] = 'Contexts';
$lang_module['user_config_context_info'] = 'List of the context in which the user is identified. Separating the different contexts by a \';\'. Example : \'ou=people,dc=nukeviet,dc=vn\'';
$lang_module['user_config_searchSubcontexts'] = 'Search subcontexts';
$lang_module['user_config_searchSubcontexts_info'] = 'Put another value 0. If you want to search from context secondary users.';
$lang_module['user_config_dereferenceAliases'] = 'Dereference aliases';
$lang_module['user_config_dereferenceAliases_info'] = 'Decide how many aliases used in the search process. Choose one of the following values: \'No\' (LDAP_DEREF_NEVER) or \'Yes\' (LDAP_DEREF_ALWAYS)';
$lang_module['user_config_userAttribute'] = 'User attribute';
$lang_module['user_config_userAttribute_info'] = 'Options: Override attributes used to indicate / search users. Normally \'cn\'.';
$lang_module['user_config_memberAttribute'] = 'Member attribute';
$lang_module['user_config_memberAttribute_info'] = 'Optional: Override attributes about the user, when the user is related to a group. Usually the \'members\'';
$lang_module['user_config_memberAttributeUsesDn'] = 'Member attribute uses dn';
$lang_module['user_config_memberAttributeUsesDn_info'] = 'Optional: Overrides handling of member attribute values, either 0 or 1';
$lang_module['user_config_objectClass'] = 'Object class';
$lang_module['user_config_objectClass_info'] = 'Optional: Override object class used to specify / search users on the type of user ldap_user_type. Normally you do not need to change this.';
$lang_module['user_config_admin_email'] = 'Notify users of account updates';
$lang_module['user_config_admin_email1'] = 'Email users when admins make changes to their accounts';
$lang_module['update_LDAP_config'] = 'Updated data from LDAP to website';
$lang_module['update_LDAP_config_name'] = 'First Name';
$lang_module['update_LDAP_config_lname'] = 'Last Name';
$lang_module['default'] = 'Default';
$lang_module['allowuserloginmulti'] = 'Allow user login multi';
$lang_module['user_check_pass_time'] = 'Password recheck time, if user is not using browser';
$lang_module['user_check_pass_time0'] = 'Check still online';
$lang_module['user_check_pass_time_note'] = 'Enter 0 if you do not want to check (not recommended)';
$lang_module['auto_login_after_reg'] = 'Automatically login after successful registration';
$lang_module['active_group_newusers'] = 'Enable new member';
$lang_module['active_group_newusers_note'] = 'If this function is activated, the new member registration will be automatically classified as "activated" until it has been removed from the team manager, at which new members are considered "Official members"';
$lang_module['active_editinfo_censor'] = 'Turn on edit censor';
$lang_module['active_editinfo_censor_note'] = 'If this function is enabled, all accounts that edit the information themselves will be moderated before being displayed, not applicable to editing the username, email, avatar, team leader, administrator edit. member information. When this feature is turned off, the administrative moderation menu will be hidden to save space, but you can still censor';
$lang_module['active_editinfo_censor_note1'] = '<a href="%s">here</a>';
$lang_module['active_user_logs'] = 'Log the log, log out of the member';
$lang_module['user_openid_mamager'] = 'Third-party account management';
$lang_module['user_2step_mamager'] = 'Manage two-step authentication';
$lang_module['user_2step_of'] = 'Manage two-step member authentication:';
$lang_module['user_2step_off'] = 'Two-step authentication is off';
$lang_module['user_2step_turnoff'] = 'Turn off two-step authentication';
$lang_module['user_2step_turnoff_info'] = 'Two-step authentication is required for some regions. If you turn off, the member will have to force it back on';
$lang_module['user_2step_codes'] = 'Backup code';
$lang_module['user_2step_newcodes'] = 'New backup code';
$lang_module['user_2step_codes_timecreat'] = 'Creation time';
$lang_module['user_2step_codes_timeuse'] = 'Time to use';
$lang_module['user_2step_codes_s0'] = 'Not used yet';
$lang_module['user_2step_codes_s1'] = 'Used';
$lang_module['user_2step_reset'] = 'Generate backup codes';
$lang_module['user_2step_sendmail'] = 'Email a new backup code for members';
$lang_module['user_oauthmanager'] = 'User\'s third-party account management';
$lang_module['user_oauthmanager_list'] = 'List of third party accounts connected to the user account';
$lang_module['user_oauthmanager_empty'] = 'No third party accounts are connected to this user account yet';
$lang_module['user_oauthmanager_gate'] = 'Port name';
$lang_module['user_oauthmanager_email'] = 'Email or ID';
$lang_module['user_oauthmanager_deleteall'] = 'Delete all';
$lang_module['min_old_user'] = 'Minimum age to join members';
$lang_module['error_system'] = '%s can not be empty';
$lang_module['two_step_verification_require_admin'] = 'Mandatory two-step authentication of the admin area';
$lang_module['two_step_verification_require_site'] = 'Two-step authentication is required outside the site';
$lang_module['two_step_verification_require_admindefault'] = 'Security settings are required to be enabled in the admin area, though not enabled here. Administrators still have to enable two-step authentication to log in.';
$lang_module['two_step_verification_require_sitedefault'] = 'The security setting is asking to be enabled outside the site, even if not enabled. The team member must still enable two-step authentication to log in.';
$lang_module['notification_new_acount'] = 'Acount %s is wating for active';
$lang_module['notification_sendactive_fail'] = 'Account %s is awaiting activation because the system could not send email';
$lang_module['editcensor'] = 'Moderate information';
$lang_module['editcensor_lastedit'] = 'Last modified';
$lang_module['editcensor_confirm_approval'] = 'Are you sure to confirm approved? After browsing this information will override current member information';
$lang_module['editcensor_confirm_denied'] = 'Are you sure to refuse? After denial, this information will be deleted and cannot be restored';
$lang_module['editcensor_field'] = 'Data fields';
$lang_module['editcensor_current'] = 'Current value';
$lang_module['editcensor_new'] = 'New value';
$lang_module['editcensor_info_basic'] = 'Basic info';
$lang_module['editcensor_info_custom'] = 'Other info';
$lang_module['emailverify_sys1'] = 'Account by system';
$lang_module['emailverify_sys2'] = 'Account activated by admin';
$lang_module['emailverify_sys3'] = 'Account no activation required';
$lang_module['emailverify_sys4'] = 'Email has not been verified';
$lang_module['emailverify_sys5'] = 'Verify email at %s';
$lang_module['active_obj_1'] = 'Activate by system';
$lang_module['active_obj_2'] = 'Activate via email';
$lang_module['active_obj_3'] = 'Activate via Oauth %s';
$lang_module['active_obj_4'] = 'Admin <strong title="%s">%s</strong> activated';
$lang_module['userwait_note'] = 'Users that are pending activation will be automatically deleted within %s hours of registration if not activated';
$lang_module['userwait_resend_email'] = 'Resend the activation email';
$lang_module['userwait_resend_per_email'] = 'Number of emails once sent';
$lang_module['userwait_resend_pause_time'] = 'Waiting time between two emails';
$lang_module['userwait_resend_counter'] = 'The next time the email is sent is';
$lang_module['userwait_resend_run'] = 'Emailing';
$lang_module['userwait_resend_note'] = 'Please do not turn off the browser';
$lang_module['userwait_resend_complete'] = 'The process is complete. Details see below';
$lang_module['userwait_resend_start'] = 'Start at';
$lang_module['userwait_resend_end'] = 'Ended at';
$lang_module['userwait_resend_delete'] = 'Email already exists (has been activated in some way), deleting information waiting for activation';
$lang_module['userwait_resend_ok'] = 'Resend the activation email';
$lang_module['userwait_resend_error'] = 'Unable to send email, please check your mail configuration';
$lang_module['is_email_verified'] = 'E-mail without verification';
$lang_module['is_email_verified1'] = 'If checked here, the email status of the account is verified. Otherwise, the status of the account email is unverified';
$lang_module['auto_assign_oauthuser'] = 'Automatically handle when duplicate Oauth email';
$lang_module['auto_assign_oauthuser_note'] = 'When you enable this option, if you log in via Oauth and the email is already in use, the system will automatically assign Oauth to that account without entering a password to confirm';
$lang_module['pass_timeout'] = 'User must change password every';
$lang_module['days'] = 'days (0 = Not applicable)';
$lang_module['oldpass_num'] = 'Number of old passwords stored in the database';
$lang_module['send_password'] = 'Send password information to user email';
$lang_module['email_dot_equivalent'] = 'E-mail with a dot before the @ are the version of an email without a dot';
$lang_module['email_plus_equivalent'] = 'The email alias (with the + sign and after the + sign before @) is a version of the main email (before the + sign)';
$lang_module['login_name_type'] = 'Login name type';
$lang_module['censorship'] = 'Censorship';
$lang_module['leave_blank_note'] = 'Leave blank if using the value entered by the registrant. If a new password is declared - the password will be sent to the registrant';
$lang_module['register_active_time'] = 'Time for the account to wait for activation';
$lang_module['register_active_time_note'] = 'If the value is greater than 0, the account pending activation will be deleted after this period. If it is 0, the account waiting for activation will not be deleted automatically';
$lang_module['hours'] = 'hours';
$lang_module['value_empty_note'] = 'If Value is not declared in current locale, it will take Key as value';
$lang_module['reg_time_from'] = 'Registered from';
$lang_module['reg_time_to'] = 'Registered to';
$lang_module['display_mode'] = 'Display mode';
$lang_module['display_mode0'] = 'Login form';
$lang_module['display_mode1'] = 'button form';
$lang_module['popup_register'] = 'Registration popup';
$lang_module['popup_register0'] = 'No';
$lang_module['popup_register1'] = 'Yes';
$lang_module['admin_openid_processing_connect'] = 'Guest connects a third-party account to an existing account';
$lang_module['admin_openid_processing_create'] = 'Guest creates a new account to connect it to a third party account';
$lang_module['admin_openid_processing_auto'] = 'The system automatically creates a new account to connect it to a third-party account';
$lang_module['admin_members_in_group_caption'] = 'Member list in group "%s" (%d group)';
$lang_module['admin_leaders_in_group_caption'] = 'Managing the list of group "%s" (%d accounts)';
$lang_module['admin_pending_in_group_caption'] = 'Acceding list of group "%s" (%d accounts)';
$lang_module['admin_choiceUserID'] = 'Please enter the user ID';
$lang_module['admin_UserNotInGroup'] = 'User of your choice is not in the list of group members';
$lang_module['ldap_info'] = 'Updated data from LDAP to the website is optional. You can choose to pre-fill some NukeViet user information with information from the <b /> LDAP fields  is shown here. <p> <br /> If you leave these fields blank, then no What have been converted from LDAP and the default value of NukeViet will be used to replace  <p> <br /> In other cases, users will be able to prepare all This after school starts.  <p> <b /> Update site:  If enabled, the entry will be updated (from external authentication) each time the user logged in or have synced users. </p> <hr /> <p> <br /> <b /> Note:  to update the data you require an external LDAP settings and bindpw binddn to bind a user has the right to edit all User log. Currently it does not preserve multi-valued attributes, and will remove the added value when updating. </p>';
$lang_module['admin_forcedrelogin_note'] = 'User has logged out';
$lang_module['remove_2step_allow'] = 'Allows users to request the deactivation of two-factor authentication from the app\'s code if they lose their phone (Sends a Verification Code to email when the user is able to log in with a password or third-party account and answers the correct security question)';
$lang_module['remove_2step_method'] = 'Automatically disable two-factor authentication from the app\'s code when the request is successfully submitted';
$lang_module['remove_2step_method_title'] = 'Turn off 2-step verification';
$lang_module['remove_2step_info'] = 'To turn off 2-step verification, please complete the form below';
$lang_module['remove_2step_email'] = 'Account email';
$lang_module['remove_2step_email_error'] = 'Sorry, you entered your email incorrectly';
$lang_module['remove_2step_error5'] = 'Sorry, you have declared fields incorrectly 5 times in a row. Please log in again';
$lang_module['verifykey_info'] = 'The verification code has just been sent to your email. Please copy this Verification Code into the blank box below';
$lang_module['remove_2step_success'] = 'Successfully turned off 2-step verification. Please log in again';
$lang_module['remove_2step_send'] = 'Your request to turn off 2-step verification has been sent to the site administrator. We will respond to your request as soon as possible.';
$lang_module['remove_2step_request'] = 'Account %s sent a request to turn off 2-step verification';
$lang_module['active2step_status'] = '2-step verification status';
$lang_module['active2step_status0'] = '2-step verification is not enabled';
$lang_module['active2step_status1'] = '2-step verification enabled';
$lang_module['active2step_status2'] = 'Request to disable 2-step verification';
$lang_module['config_deny'] = 'Config deny words';
$lang_module['pass_confirm'] = 'Confirm password to continue';
$lang_module['pass_confirm_info'] = 'To perform this feature, you need to confirm your password again. Please enter your password in the box below and click Confirm';
$lang_module['error_no_password'] = 'You do not have a password, please create a password first';
$lang_module['passkey_error_exist'] = 'This passkey already exists, please create another key';
$lang_module['passkey_error_userhandle'] = 'This credential does not provide user information and cannot be used as a passkey';
$lang_module['passkey_created'] = 'Passkey created';
$lang_module['passkey_created_body'] = 'You can now use fingerprint, PIN, face, screen lock, or hardware security key to log in to your account without entering a username or password';
$lang_module['passkey_login_create'] = 'Passwordless login with passkey';
$lang_module['passkey_login_create_body'] = 'The passkey is a secure and modern method that allows you to log in to your account using only fingerprint, face, screen lock, PIN, or hardware security key. You can also use the passkey as a two-step authentication method after logging in with a password';
$lang_module['passkey_add'] = 'Add passkey';
$lang_module['seckey_add'] = 'Add security key';
$lang_module['passkey_not_supported'] = 'This browser/device does not support WebAuthn, so the passkey cannot be created. Please use another browser/device or try again later';
$lang_module['passkey_list'] = 'Your passkeys';
$lang_module['passkey_seenthis'] = 'Created from this browser';
$lang_module['passkey_created_at'] = 'Created at';
$lang_module['passkey_last_used_at'] = 'Last used';
$lang_module['passkey_nickname'] = 'Nickname';
$lang_module['passkey_nickname_edit'] = 'Edit nickname';
$lang_module['action_time'] = 'Action time';
$lang_module['tstep_link'] = 'Two-step authentication management link';
$lang_module['pass_link'] = 'Password change link';
$lang_module['code_link'] = 'Backup code download link';
$lang_module['passkey_link'] = 'Passkey management link';

$lang_module['merge_field_active_deadline'] = 'Expired time for active';
$lang_module['merge_field_link'] = 'Link';
$lang_module['mf_send_newvalue'] = 'Send new value via email';
$lang_module['mf_label'] = 'Label';
$lang_module['mf_deadline'] = 'Expiration time';
$lang_module['title_des_reg_line1'] = 'Only one account is needed to access all websites within the DauThau Ecosystem';
$lang_module['title_des_reg_line2'] = 'Please use your own personal information to register for an account and then proceed to get the business management rights and authorize or revoke business management rights of other user accounts! For purposes of security, the system will prevent multiple users from using the same account!';
