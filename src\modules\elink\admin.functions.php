<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 12/31/2009 2:29
 */

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE') or !defined('NV_IS_MODADMIN')) {
    die('Stop!!!');
}

$allow_func = ['main'];

if (defined('NV_IS_SPADMIN')) {
    $allow_func[] = 'config';
    $allow_func[] = 'affiliate';
    //$allow_func[] = 'sync'; @deprecated Chức năng cũ, ai cần phải viết lại
}

define('NV_IS_FILE_ADMIN', true);

// Thiết lập module
$sql = 'SELECT config_name, config_value FROM ' . $db_config['prefix'] . '_' . $module_data . '_config';
$list = $nv_Cache->db($sql, '', $module_name);
$global_arrray_config = [];
foreach ($list as $values) {
    $global_arrray_config[$values['config_name']] = $values['config_value'];
}
