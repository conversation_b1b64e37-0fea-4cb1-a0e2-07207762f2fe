<!-- BEGIN: main -->
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/dauthau/plugins/daterangepicker/moment.min.js"></script>
<script type="text/javascript" src="{NV_BASE_SITEURL}themes/dauthau/plugins/daterangepicker/daterangepicker.js"></script>
<link type="text/css" rel="stylesheet" href="{NV_BASE_SITEURL}themes/dauthau/plugins/daterangepicker/daterangepicker.css" />
<!-- BEGIN: detail -->
<script type="text/javascript">
    var formObject = $("[id=ltablesearch]");
    function bl_setDaterangepicker(_options) {
            // Menu khoảng tìm kiếm
        var ranges = {};
        ranges['{LANG.this_month}'] = [ moment().startOf('month'), moment().endOf('month') ];
        ranges['{LANG.last_3_months}'] = [ moment().startOf('quarter'), moment().endOf('quarter') ];
        ranges['{LANG.this_year}'] = [ moment().startOf('year'), moment().endOf('year') ];
        ranges['{LANG.last_all_days}'] = [ moment('{MINDATE}', "DD/MM/YYYY"), moment() ];

        var calendar_options = { showDropdowns : true, locale : { customRangeLabel : '{LANG.custom_range}', format : 'DD/MM/YYYY', help : '' }, ranges : ranges, startDate : moment().subtract(14, 'days'), endDate : moment(), opens : 'right', drops : "auto", alwaysShowCalendars : true, };

        $.extend(calendar_options, _options);

        $(".search_range", formObject).daterangepicker(calendar_options, function(start, end, label) {
            $("[name=sfrom]", formObject).val(start.format('DD/MM/YYYY'));
            $("[name=sto]", formObject).val(end.format('DD/MM/YYYY'))
        });
    }
    $(function() {
        bl_setDaterangepicker({ startDate : $("[name=sfrom]", formObject).val(), endDate : $("[name=sto]", formObject).val() });
    });
</script>

<div class="row">
    <div class="col-sm-18 col-md-19">
        <form method="get" class="form-inline" id="ltablesearch">
            <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}">
            <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
            <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}">
            <input type="hidden" name="userid" value="{USERID}">
            <input type="hidden" name="admin_id" value="{ADMIN_ID}">
            <label>{LANG.select_time}:</label>
            <div class="form-group">
                <div class="col-xs-24">
                    <input type="hidden" name="sfrom" value="{FROM}" data-default="{FROM_DEFAULT}" />
                    <input type="hidden" name="sto" value="{TO}" data-default="{TO_DEFAULT}" />
                    <input class="form-control search_range" type="text" value="{FROM} - {TO}">
                </div>
            </div>

            <div class="form-group">
                <button class="btn btn-primary" type="submit" name="searchLog" value="{LANG.search_submit}"><i class="fa fa-search" aria-hidden="true"></i> {LANG.search_submit}</button>
            </div>
        </form>
        <br>
    </div>
</div>
<div class="row">
    <div class="col-md-24">
        <div class="panel panel-info">
            <div class="panel-heading">
                {LANG.title_gd_td}
            </div>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered table-hover" id="table1">
                        <thead>
                            <tr>
                                <th class="text-center">{LANG.stt}</th>
                                <th class="text-center">{LANG.account_give_point}</th>
                                <th class="text-center">{LANG.give_point_admin}</th>
                                <th class="text-center">{LANG.receive_give_point}</th>
                                <th class="text-center">{LANG.sodiemnhan}</th>
                                <th class="text-center">{LANG.updattime_new}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- BEGIN: loop_detail -->
                            <tr>
                                <td class="text-center">{ACOUNT.stt}</td>
                                <td>
                                    <a href="javascript:void(0)"><b>{ACOUNT.full_name_admin}</b></a>
                                    <p>{LANG.message}: <span>{ACOUNT.message_sender}</span></p>
                                </td>
                                <td class="text-center">
                                    <label class="label label-primary txt_total">{ACOUNT.point_total} {LANG.diem}</label>
                                </td>
                                <td>
                                    <a href="javascript:void(0)"><b>{ACOUNT.full_name}</b></a>
                                    <p>{LANG.message}: <span>{ACOUNT.message_give}</span></p>
                                </td>
                                <td class="text-center">
                                    <label class="label label-success txt_total">{ACOUNT.point_receive} {LANG.diem}</label>
                                </td>
                                <td class="text-center">
                                    {ACOUNT.addtime}
                                </td>
                            </tr>
                            <!-- END: loop_detail -->
                            <!-- BEGIN: nodata1 -->
                            <tr>
                                <td colspan="6"><p class="alert alert-warning">{NODATA}</p></td>
                            </tr>
                            <!-- END: nodata1 -->
                        </tbody>
                        <tfoot class="text-center">
                            <tr class="bg-info">
                                <td></td>
                                <td colspan="1" class="text-center"><strong>{LANG.tongdiemtang}</strong></td>
                                <td><strong class="label label-success txt_total">{TONG_TANG} {LANG.diem}</strong></td>
                                <td colspan="1" class="text-center"><strong>{LANG.tongdiemnhan}</strong></td>
                                <td><strong class="label label-success txt_total">{TONG_NHAN}  {LANG.diem}</strong></td>
                                <td colspan="3"></td>
                            </tr>
                        </tfoot>
                    </table>
                    <div class="text-center">
                        <!-- BEGIN: generate_page -->
                        <tfoot class="text-center">
                            <tr>
                                <td colspan="7">{GENERATE_PAGE}</td>
                            </tr>
                        </tfoot>
                        <!-- END: generate_page -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- END: detail -->

<!-- BEGIN: showlist -->
<div class="row">
    <div class="col-sm-18 col-md-19">
        <form action="{NV_BASE_ADMINURL}index.php" method="get" id="ltablesearch">
            <input type="hidden" name="{NV_LANG_VARIABLE}" value="{NV_LANG_DATA}">
            <input type="hidden" name="{NV_NAME_VARIABLE}" value="{MODULE_NAME}">
            <input type="hidden" name="{NV_OP_VARIABLE}" value="{OP}">
            <div class="row">
                <div class="col-sm-6">
                    <label>{LANG.select_time}:</label>
                    <div class="form-group">
                        <div class="col-xs-24">
                            <input type="hidden" name="sfrom" value="{FROM}" data-default="{FROM_DEFAULT}" />
                            <input type="hidden" name="sto" value="{TO}" data-default="{TO_DEFAULT}" />
                            <input class="form-control search_range" type="text" value="{FROM} - {TO}">
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label>{LANG.account_give_point}:</label>
                        <input type="text" name="tk_tang" value="{TK_TANG}" class="form-control" placeholder="{LANG.account_give_point}" />
                    </select>
                    </div>
                </div>

                <div class="col-sm-6">
                    <div class="form-group">
                        <label>{LANG.receive_give_point}:</label>
                        <input type="text" name="tk_nhan" value="{TK_NHAN}" class="form-control" placeholder="{LANG.receive_give_point}" />
                    </select>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <button type="submit" value="{LANG.search_submit}" class="btn btn-primary mt-22" name="search_list">{LANG.search_submit}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<table class="table table-striped table-bordered table-hover" id="table3">
    <thead>
        <tr>
            <th class="text-center">{LANG.stt}</th>
            <th class="text-center">{LANG.account_give_point}</th>
            <th class="text-center">{LANG.give_point_admin}</th>
            <th class="text-center">{LANG.receive_give_point}</th>
            <th class="text-center">{LANG.sodiemnhan}</th>
            <th class="text-center">{LANG.updattime_new}</th>
        </tr>
    </thead>
    <tbody>
        <!-- BEGIN: loop_listacount -->
        <tr>
            <td class="text-center">{ACOUNT.stt}</td>
            <td>
                <a href="{ACOUNT.view_url}"><b>{ACOUNT.full_name_admin}</b></a>
            </td>
            <td class="text-center">
                <label class="label label-primary txt_total">{ACOUNT.point_total} {LANG.diem}</label>
            </td>
            <td>
                {ACOUNT.full_name}
            </td>
            <td class="text-center">
                <label class="label label-success txt_total">{ACOUNT.point_receive} {LANG.diem}</label>
            </td>
            <td class="text-center">
                {ACOUNT.created_time}
            </td>
        </tr>
        <!-- END: loop_listacount -->
        <!-- BEGIN: nodata3 -->
        <tr>
            <td colspan="5"><p class="alert alert-warning">{NODATA}</p></td>
        </tr>
        <!-- END: nodata3 -->
    </tbody>
    <!-- BEGIN: generate_page -->
    <tfoot class="text-center">
        <tr>
            <td colspan="7">{GENERATE_PAGE}</td>
        </tr>
    </tfoot>
    <!-- END: generate_page -->
</table>
<script type="text/javascript">
    var formObject = $("[id=ltablesearch]");
    function bl_setDaterangepicker(_options) {
            // Menu khoảng tìm kiếm
        var ranges = {};
        ranges['{LANG.this_month}'] = [ moment().startOf('month'), moment().endOf('month') ];
        ranges['{LANG.last_3_months}'] = [ moment().startOf('quarter'), moment().endOf('quarter') ];
        ranges['{LANG.this_year}'] = [ moment().startOf('year'), moment().endOf('year') ];
        ranges['{LANG.last_all_days}'] = [ moment('{MINDATE}', "DD/MM/YYYY"), moment() ];

        var calendar_options = { showDropdowns : true, locale : { customRangeLabel : '{LANG.custom_range}', format : 'DD/MM/YYYY', help : '' }, ranges : ranges, startDate : moment().subtract(14, 'days'), endDate : moment(), opens : 'right', drops : "auto", alwaysShowCalendars : true, };

        $.extend(calendar_options, _options);

        $(".search_range", formObject).daterangepicker(calendar_options, function(start, end, label) {
            $("[name=sfrom]", formObject).val(start.format('DD/MM/YYYY'));
            $("[name=sto]", formObject).val(end.format('DD/MM/YYYY'))
        });
    }
    $(function() {
        bl_setDaterangepicker({ startDate : $("[name=sfrom]", formObject).val(), endDate : $("[name=sto]", formObject).val() });
    });
</script>
<!-- END: showlist -->

<!-- END: main -->
