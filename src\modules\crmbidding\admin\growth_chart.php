<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
use NukeViet\Api\DoApi;

$page_title = $nv_Lang->getModule('growth_chart');

$array_search = [];
$array_search['type'] = $nv_Request->get_int('type', 'post,get', 0);
$array_search['type_chart'] = $nv_Request->get_int('type_chart', 'post,get', 1);
$curent_from = nv_date('01/Y', NV_CURRENTTIME);
$curent_to = nv_date('12/Y', NV_CURRENTTIME);
$curent_single = nv_date('m/Y', NV_CURRENTTIME);
$array_search['time_from'] = $nv_Request->get_title('time_from', 'post,get', $curent_from);
$array_search['time_to'] = $nv_Request->get_title('time_to', 'post,get', $curent_to);
$array_search['single_month'] = $nv_Request->get_title('single_month', 'post,get', $curent_single);
if (preg_match("/^([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_from'], $m)) {
    $sfrom = mktime(0, 0, 0, $m[1], 01, $m[2]);
} else {
    $sfrom = mktime(0, 0, 0, nv_date('m', NV_CURRENTTIME), 01, nv_date('Y', NV_CURRENTTIME));
}

if (preg_match("/^([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_to'], $m)) {
    $sto = mktime(0, 0, 0, $m[1], get_last_day($m[1], $m[2]), $m[2]);
} else {
    $sto = mktime(23, 59, 59, nv_date('m', NV_CURRENTTIME), get_last_day(nv_date('m', NV_CURRENTTIME), nv_date('Y', NV_CURRENTTIME)), nv_date('Y', NV_CURRENTTIME));
}

if (preg_match("/^([0-9]{1,2})\/([0-9]{4})$/", $array_search['single_month'], $m)) {
    $smonth = mktime(0, 0, 0, $m[1], get_last_day($m[1], $m[2]), $m[2]);
} else {
    $smonth = mktime(23, 59, 59, nv_date('m', NV_CURRENTTIME), get_last_day(nv_date('m', NV_CURRENTTIME), nv_date('Y', NV_CURRENTTIME)), nv_date('Y', NV_CURRENTTIME));
}

if ($array_search['type_chart'] == 2) {
    $sfrom = nv_date('m', $smonth);
    $sto = nv_date('Y', $smonth);
}

$params = [
    'type_chart' => $array_search['type_chart'],
    'from_date' => $sfrom,
    'to_date' => $sto,
];

$api = new DoApi(API_CRM_URL, API_CRM_KEY, API_CRM_SECRET);
$api->setModule('crmbidding')
    ->setLang('vi')
    ->setAction('GetGrowthChart')
    ->setData($params);

$data = $api->execute();
$error = $api->getError();
$series_line = $channel = $total_month_column = $list_month = $data_series_month = [];

if (!empty($data['data'])) {
    $channel = $data['data']['channel'];
    if ($array_search['type_chart'] == 1) {
        $series_line = $data['data']['series_line'];
        $list_month = $data['data']['list_month'];
    } else {
        $total_month_column = $data['data']['total_month_column'];
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('NV_LANG_INTERFACE', NV_LANG_INTERFACE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_FILE', $module_file);
$xtpl->assign('TEMPLATE', $global_config['module_theme']);
$xtpl->assign('OP', $op);
//users
$xtpl->assign('CATEGORIES', json_encode($channel));
$xtpl->assign('ARRAY_SEARCH', $array_search);
if ($array_search['type_chart'] == 1) {
    $xtpl->assign('DATA_LINE', json_encode($series_line));
    $xtpl->assign('LIST_MONTH', json_encode($list_month));
    $xtpl->parse('main.chartline');
} else {
    $xtpl->assign('DATA_COLUMN', json_encode($total_month_column));
    $xtpl->parse('main.chartcolumn');
}
$xtpl->assign('TYPE_CHART' . $array_search['type_chart'], ' selected="selected"');
$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';