<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */
if (!defined('NV_IS_MOD_WALLET')) {
    die('Stop!!!');
}

use NukeViet\Http\Http;

$page_title = $module_info['site_title'];
$key_words = $module_info['keywords'];

$url_checkout = [];

// Nạp đúng số tiền nào đó
$pay_amount = $nv_Request->get_title('amount', 'get', '');
$pay_info = nv_substr($nv_Request->get_title('info', 'get', ''), 0, 250);
$pay_money = '';
if (preg_match('/^([0-9\.]+)\-([A-Z]{3})$/', $pay_amount, $m)) {
    if (!isset($global_array_money_sys[$m[2]])) {
        $pay_amount = '';
    } else {
        $pay_money = $m[2];
    }
} else {
    $pay_amount = '';
}

$db->sqlreset()
    ->select('*')
    ->from($db_config['prefix'] . '_' . $module_name . "_bank_info")
    ->where('status = 1');
$bank_info = $db->query($db->sql())
    ->fetchAll();
foreach ($bank_info as $k => $row) {
    $bank_info[$k]['title'] = $row['title_' . NV_LANG_DATA];
    $bank_info[$k]['account_holder'] = $row['account_holder_' . NV_LANG_DATA];
    $bank_info[$k]['bank_shortname'] = $row['bank_shortname_' . NV_LANG_DATA];
    $bank_info[$k]['bank_name'] = $row['bank_name_' . NV_LANG_DATA];
    $bank_info[$k]['description_content'] = $row['description_content_' . NV_LANG_DATA];
    $bank_info[$k]['note'] = $row['note_' . NV_LANG_DATA];
}

foreach ($global_array_payments as $row) {
    $row['currency_support'] = explode(',', $row['currency_support']);
    if (file_exists(NV_ROOTDIR . "/modules/" . $module_file . "/payment/" . $row['payment'] . ".checkout_url.php") and (empty($pay_amount) or !empty($row['allowedoptionalmoney'])) and (empty($pay_money) or in_array($pay_money, $row['currency_support']))) {
        $payment_config = unserialize(nv_base64_decode($row['config'])) ?: [];
        $payment_config['paymentname'] = $row['paymentname'];
        $payment_config['domain'] = $row['domain'];

        $images_button = $row['images_button'];
        $url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=recharge/" . $row['payment'];
        if (!empty($pay_amount)) {
            $url .= '&amp;amount=' . $pay_amount;
        }
        if (!empty($pay_info)) {
            $url .= '&amp;info=' . urlencode($pay_info);
        }

        if (!empty($images_button) and file_exists(NV_UPLOADS_REAL_DIR . "/" . $module_name . "/" . $images_button)) {
            $images_button = NV_BASE_SITEURL . NV_UPLOADS_DIR . "/" . $module_name . "/" . $images_button;
        }

        $url_checkout[] = [
            'payment' => $row['payment'],
            'name' => $row['paymentname'],
            'url' => $url,
            'images_button' => $images_button,
            'guide' => $row['bodytext']
        ];
    }
}

// Trang chờ thanh toán ATM
if (isset($array_op[1]) and $array_op[0] == 'atm' and preg_match('/^(GD|WP|BDH)([0-9]{10})$/', $array_op[1], $m) and defined('NV_IS_USER') and isset($global_array_payments['ATM'])) {
    $nv_BotManager->setPrivate();

    $pay_dauthau = false;
    if ($m[1] == 'BDH') {
        $pay_dauthau= true;
        $_transaction_info = $array_op[1];
    }
    $transaction_type = ($m[1] == 'WP' or $pay_dauthau) ? 'pay' : 'recharge';
    $transaction_id = intval($m[2]);
    $payment = $global_array_payments['ATM'];
    $payment_config = unserialize(nv_base64_decode($payment['config'])) ?: [];

    $payment_config['account_no'] = empty($payment_config['account_no']) ? [] : explode(',', $payment_config['account_no']);
    $payment_config['account_name'] = empty($payment_config['account_no']) ? [] : explode(',', $payment_config['account_name']);
    $payment_config['acq_id'] = empty($payment_config['account_no']) ? [] : explode(',', $payment_config['acq_id']);

    $json_respon = [
        'continue' => false,
        'success' => false,
        'message' => '',
        'redirect' => ''
    ];
    $is_ajax = ($nv_Request->get_title('getStatus', 'post', '') === NV_CHECK_SESSION);

    // Lấy và chỉ chấp nhận giao dịch cùng người, có status=0
    if ($pay_dauthau) {
        $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_transaction WHERE
    transaction_info = " . $db->quote($_transaction_info) . " AND customer_id=" . $user_info['userid'] . " AND transaction_type=-1 ORDER BY id DESC LIMIT 1";
    } else {
        $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_transaction WHERE
    id=" . $transaction_id . " AND customer_id=" . $user_info['userid'] . " AND transaction_type=-1";
    }

    $transaction = $db->query($sql)->fetch();

    if (empty($transaction)) {
        if ($is_ajax) {
            $json_respon['message'] = $nv_Lang->getModule('transition_no_exists');
            nv_jsonOutput($json_respon);
        }
        nv_info_die($nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_content'), 404);
    }
    if ($pay_dauthau) {
        $transaction['transaction_code'] = $transaction['transaction_info'];
    } else {
        $transaction['transaction_code'] = !empty($transaction['order_id']) ? sprintf('WP%010s', $transaction['id']) : sprintf('GD%010s', $transaction['id']);
    }

    // Kiểm tra đơn hàng
    if (!empty($transaction['order_id'])) {
        $sql = "SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_orders WHERE id=" . $transaction['order_id'];
        $order_info = $db->query($sql)->fetch();
        if (empty($order_info)) {
            if ($is_ajax) {
                $json_respon['message'] = $nv_Lang->getModule('paygate_error_order');
                nv_jsonOutput($json_respon);
            }
            nv_info_die($nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_content'), 404);
        }
    }
    if ($is_ajax) {
        if ($transaction['transaction_status'] == 0) {
            // Tiếp tục đợi
            $json_respon['continue'] = true;
            nv_jsonOutput($json_respon);
        }
        if ($transaction['transaction_status'] == 4) {
            // Giao dịch hoàn tất
            $json_respon['success'] = true;

            if (!empty($transaction['order_id'])) {
                require_once NV_ROOTDIR . '/modules/wallet/wallet.class.php';
                $wallet = new nukeviet_wallet();

                $order_info['payurl'] = $wallet->getOrderPayUrl($order_info, false);

                // Url chuyển hướng thanh toán đơn hàng
                $checksum_str = $transaction['transaction_code'] . $transaction['money_net'] . $transaction['money_unit'] . $transaction['transaction_info'] . $transaction['tokenkey'];
                $checksum = hash('sha256', $checksum_str);

                // Tạo URL để chuyển ngay về phần complete
                $transaction['ReturnURL'] = urlRewriteWithDomain($order_info['payurl'] . '&payment=ATM&wpayportres=true', NV_MY_DOMAIN);
                $url = $transaction['ReturnURL'];
                $url .= '&code=' . $transaction['transaction_code'] . '&money=' . $transaction['money_net'] . '&unit=' . $transaction['money_unit'] . '&info=' . urlencode($transaction['transaction_info']) . '&checksum=' . $checksum;

                $json_respon['redirect'] = nv_url_rewrite($url, true);
            } else {
                // Url chuyển hướng nạp tiền
                $json_respon['redirect'] = nv_url_rewrite(NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=money', true);
            }

            nv_jsonOutput($json_respon);
        }
        // Giao dịch này không còn chờ thanh toán nữa
        $json_respon['message'] = sprintf($nv_Lang->getModule('atm_blockstatus'), $nv_Lang->getModule('transaction_status' . $transaction['transaction_status']));
        nv_jsonOutput($json_respon);
    }
    if ($transaction['transaction_status'] != 0) {
        nv_info_die($nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_content'), 404);
    }

    $page_url = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=atm/' . $transaction['transaction_code'];
    $transaction_data = unserialize($transaction['transaction_data']) ?: [];

    // Thông tin thanh toán không hợp lệ
    if (empty($transaction_data['atm_toacc'])) {
        nv_info_die($nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_content'), 404);
    }

    // Xác định thông tin ngân hàng nhận
    $array_bank = [];
    foreach ($bank_info as $bank) {
        if ($bank['account_number'] == $transaction_data['atm_toacc']) {
            $array_bank = $bank;
            break;
        }
    }
    if (empty($array_bank)) {
        nv_info_die($nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_content'), 404);
    }

    // Xem thử có hỗ trợ tạo mã QR code không
    if ($transaction['money_net'] > 0 and strlen(strval($transaction['money_net'])) <= 13) {
        $transaction['qr_supported'] = false;
        foreach ($payment_config['account_no'] as $key => $account_no) {
            if ($account_no == $array_bank['account_number']) {
                $transaction['qr_supported'] = true;
                $transaction['acq_id'] = $payment_config['acq_id'][$key];
                $transaction['account_no'] = $payment_config['account_no'][$key];
                $transaction['account_name'] = $payment_config['account_name'][$key];
                break;
            }
        }
    }

    // Xử lý tạo mã QR code
    $api_banks = [];
    if ($transaction['qr_supported']) {
        // Lấy một số thông tin ngân hàng khi nạp API
        $cacheFile = NV_LANG_DATA . '_vietqr_banks_' . NV_CACHE_PREFIX . '.cache';
        $cacheTTL = 86400;
        if (($cache = $nv_Cache->getItem($module_name, $cacheFile, $cacheTTL)) != false) {
            $api_banks = json_decode($cache, true);
        } else {
            $banks = file_get_contents('https://api.vietqr.io/v1/banks');
            $banks = json_decode($banks, true);

            if (is_array($banks) and !empty($banks['data'])) {
                foreach ($banks['data'] as $bank) {
                    if ($bank['vietqr'] > 1) {
                        // Ngân hàng quét mã được mới hiển thị
                        $api_banks[$bank['bin']] = $bank;
                    }
                }
            }
            $nv_Cache->setItem($module_name, $cacheFile, json_encode($api_banks), $cacheTTL);
        }
        if (!isset($api_banks[$transaction['acq_id']])) {
            $transaction['qr_supported'] = false;
        }
    }

    if ($transaction['qr_supported']) {
        $qr_session = $nv_Request->get_string('wallet_qrcode', 'session', '');
        $qr_session = empty($qr_session) ? [] : (json_decode($qr_session, true) ?: []);
        if (empty($qr_session) or $qr_session['id'] != $transaction['id']) {
            $body = [
                'accountNo' => $transaction['account_no'],
                'accountName' => $transaction['account_name'],
                'acqId' => $transaction['acq_id'],
                'amount' => $transaction['money_net'],
                'addInfo' => $transaction['transaction_code'],
                // 'format' => 'vietqr_net',
                'template' => 'print'
            ];
            $args = [
                'headers' => [
                    'Referer' => urlRewriteWithDomain($page_url, NV_MY_DOMAIN),
                    'x-client-id' => NV_MY_DOMAIN,
                    'x-api-key' => '511352cc-a577-4a96-bf3d-cd5b06401c36',
                    'Content-Type' => 'application/json'
                ],
                'body' => json_encode($body),
                'timeout' => 10,
                'decompress' => false,
                'sslverify' => false
            ];
            $http = new Http($global_config, NV_TEMP_DIR);
            $responsive = $http->post('https://api.vietqr.io/v2/generate', $args);

            if (!empty(Http::$error)) {
                trigger_error(Http::$error['message']);
            } elseif (!is_array($responsive)) {
                trigger_error($nv_Lang->getModule('atm_vietqr_error_api'));
            } elseif (empty($responsive['body'])) {
                trigger_error($nv_Lang->getModule('atm_vietqr_error_api'));
            } else {
                $api_body = json_decode($responsive['body'], true);
                if (!is_array($api_body) or empty($api_body['data']['qrDataURL'])) {
                    trigger_error($nv_Lang->getModule('atm_vietqr_error_api'));
                } else {
                    $qr_session = [
                        'id' => $transaction['id'],
                        'data' => $api_body['data']['qrDataURL']
                    ];
                    $transaction['qr_image'] = $qr_session['data'];
                    $nv_Request->set_Session('wallet_qrcode', json_encode($qr_session));
                }
            }
        } else {
            $transaction['qr_image'] = $qr_session['data'];
        }
    }
    if ($pay_dauthau) {
        $transaction['type_show'] = sprintf($nv_Lang->getModule('paygate_tranmess'), $transaction['transaction_code']);
    } else {
        $transaction['type_show'] = empty($transaction['order_id']) ? $nv_Lang->getModule('note_pay1') : sprintf($nv_Lang->getModule('paygate_tranmess'), sprintf('DH%010s', $transaction['order_id']));
    }
    $transaction['display_money'] = display_money($transaction['money_net']);
    $transaction['show_status'] = $nv_Lang->getModule('transaction_status' . $transaction['transaction_status']);
    $transaction['created_time'] = nv_date('H:i d/m/Y', $transaction['created_time']);
    $transaction['ajax_url'] = str_replace('&amp;', '&', $page_url);

    $canonicalUrl = getCanonicalUrl($page_url);
    $contents = nv_theme_wallet_waitatm($transaction, $api_banks, $array_bank);

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_site_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

// Chuyển đến trang nạp nếu chỉ có một cổng thanh toán
if (sizeof($url_checkout) == 1) {
    $url = current($url_checkout);
    nv_redirect_location(str_replace('&amp;', '&', $url['url']));
}

$array_replace = array(
    'SITE_NAME' => $global_config['site_name'],
    'SITE_DES' => $global_config['site_description'],
    'SITE_EMAIL' => $global_config['site_email'],
    'SITE_PHONE' => $global_config['site_phone'],
    'USER_NAME' => $user_info['username'],
    'USER_EMAIL' => $user_info['email'],
    'USER_FULLNAME' => $user_info['full_name']
);

$payport_content = nv_unhtmlspecialchars($module_config[$module_name]['payport_content']);
foreach ($array_replace as $index => $value) {
    $payport_content = str_replace('[' . $index . ']', $value, $payport_content);
}

$page_url = $base_url = NV_BASE_SITEURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name;
$canonicalUrl = getCanonicalUrl($page_url);

$array_mod_title[] = [
    'catid' => 0,
    'title' => $nv_Lang->getModule('recharge_to_wallet'),
    'link' => NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op,
];

$contents = nv_theme_wallet_main($url_checkout, $payport_content, $bank_info);

include NV_ROOTDIR . '/includes/header.php';
echo nv_site_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
