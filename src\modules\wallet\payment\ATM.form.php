<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */

if (!defined('NV_IS_MOD_WALLET')) {
    die('Stop!!!');
}

$post['atm_toacc'] = nv_substr($nv_Request->get_title('atm_toacc', 'post', ''), 0, 250);
$post['transaction_data'] = '';

unset($fcode);
if ($module_captcha == 'recaptcha') {
    // Xác định giá trị của captcha nhập vào nếu sử dụng reCaptcha
    $fcode = $nv_Request->get_title('g-recaptcha-response', 'post', '');
} elseif ($module_captcha == 'captcha') {
    // <PERSON><PERSON>c định giá trị của captcha nhập vào nếu sử dụng captcha hình
    $fcode = $nv_Request->get_title('capchar', 'post', '');
}

if (empty($post['atm_toacc'])) {
    $atm_error = $nv_Lang->getModule('atm_error_toacc');
} elseif (isset($fcode) and !nv_capcha_txt($fcode, $module_captcha)) {
    $atm_error = ($module_captcha == 'recaptcha') ? $nv_Lang->getGlobal('securitycodeincorrect1') : $nv_Lang->getGlobal('securitycodeincorrect');
}
