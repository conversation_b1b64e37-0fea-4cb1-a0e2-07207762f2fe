## Hướng dẫn cài đặt

- Cài NodeJS https://nodejs.org/
- Cài Sass: `npm install -g sass` (mở cmd lên gõ lệnh như vậy là được, khi cập nhật thì gõ lại lệnh đó tiếp)
- Chạy file `dev/buildcss.dauthau.bat` để build ra css trong quá trình code.
- Nếu không chạy trên windows thì vào thư mục dev chạy lệnh `sass --watch theme.dauthau/scss/dauthau.responsive.scss:../themes/dauthau/css/dauthau.responsive.css theme.dauthau/scss/dauthau.non-responsive.scss:../themes/dauthau/css/dauthau.non-responsive.css --style compressed`

## Chú ý: 

- Do build CSS ra dạng compress nên chắc chắn bị conflic (do có 1 dòng) nên trên kho code không lưu file css.
- Khi thây file scss thay đổi thì devloper tự build ra
- <PERSON>hi đưa lên hosting nếu thấy scss cũng build ra và đưa lên hosting

Có thể cài program dành cho window: https://scout-app.io/ để code các file Sass