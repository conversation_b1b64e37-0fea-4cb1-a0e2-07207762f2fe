<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('telelog');
$set_active_op = 'import';

// Xóa bỏ 1 hoặc nhiều
if ($nv_Request->isset_request('delete', 'post')) {
    $id = $nv_Request->get_int('id', 'post', 0);
    $listid = $nv_Request->get_title('listid', 'post', '');
    $listid = $listid . ',' . $id;
    $listid = array_filter(array_unique(array_map('intval', explode(',', $listid))));

    foreach ($listid as $id) {
        // <PERSON><PERSON>m tra tồn tại
        $sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_logs WHERE id=' . $id;
        $array = $db->query($sql)->fetch();
        if (!empty($array)) {
            // Xóa
            $sql = 'DELETE FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_logs WHERE id=' . $id;
            $db->query($sql);

            nv_insert_logs(NV_LANG_DATA, $module_name, 'LOG_DELETE_TELEPRO_LOG', json_encode($array), $admin_info['admin_id']);
        }
    }

    $nv_Cache->delMod($module_name);
    nv_htmlOutput("OK");
}

// Xem chi tiết
if ($nv_Request->isset_request('viewdtail', 'post')) {
    if (!defined('NV_IS_AJAX')) {
        die('Wrong URL!!!');
    }

    $id = $nv_Request->get_int('id', 'post', 0);

    $respon = [
        'text' => '',
    ];

    $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_telepro_logs WHERE id=" . $id;
    $result = $db->query($sql);
    if (!$result->rowCount()) {
        $respon['text'] = 'Log not exists!!!';
        nv_jsonOutput($respon);
    }
    $row = $result->fetch();

    $row['post_data'] = nv_htmlspecialchars(print_r($row['post_data'] ? json_decode($row['post_data'], true) : [], true));
    $row['get_data'] = nv_htmlspecialchars(print_r($row['get_data'] ? json_decode($row['get_data'], true) : [], true));

    $respon['text'] .= '<h1>GET</h1>';
    $respon['text'] .= '<pre><code>' . $row['get_data'] . '</code></pre>';
    $respon['text'] .= '<h1>POST</h1>';
    $respon['text'] .= '<pre><code>' . $row['post_data'] . '</code></pre>';

    nv_jsonOutput($respon);
}

$per_page = 30;
$page = $nv_Request->get_int('page', 'get', 1);
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;

$array_search = [];
$array_search['q'] = $nv_Request->get_title('q', 'get', '');
$array_search['from'] = $nv_Request->get_string('f', 'get', '');
$array_search['to'] = $nv_Request->get_string('t', 'get', '');

if (preg_match('/^([0-9]{1,2})\-([0-9]{1,2})\-([0-9]{4})$/', $array_search['from'], $m)) {
    $array_search['from'] = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
} else {
    $array_search['from'] = 0;
}
if (preg_match('/^([0-9]{1,2})\-([0-9]{1,2})\-([0-9]{4})$/', $array_search['to'], $m)) {
    $array_search['to'] = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
} else {
    $array_search['to'] = 0;
}

$db->sqlreset()->select('COUNT(*)')->from(NV_PREFIXLANG . '_' . $module_data . '_telepro_logs');

$where = [];

if (!empty($array_search['q'])) {
    $base_url .= '&amp;q=' . urlencode($array_search['q']);
    $dblikekey = $db->dblikeescape($array_search['q']);
    $where[] = "(remote_ip LIKE '%" . $dblikekey . "%' OR event_type LIKE '%" . $dblikekey . "%' OR phone LIKE '%" . $dblikekey . "%' OR fullname LIKE '%" . $dblikekey . "%' OR agent_fullname LIKE '%" . $dblikekey . "%')";
}
if (!empty($array_search['from'])) {
    $base_url .= '&amp;f=' . nv_date('d-m-Y', $array_search['from']);
    $where[] = 'log_time>=' . $array_search['from'];
}
if (!empty($array_search['to'])) {
    $base_url .= '&amp;t=' . nv_date('d-m-Y', $array_search['to']);
    $where[] = 'log_time<=' . $array_search['to'];
}
$array_search['from'] = $array_search['from'] ? nv_date('d-m-Y', $array_search['from']) : '';
$array_search['to'] = $array_search['to'] ? nv_date('d-m-Y', $array_search['to']) : '';

if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}

$num_items = $db->query($db->sql())->fetchColumn();

$db->select('*')->order('id DESC')->limit($per_page)->offset(($page - 1) * $per_page);
$result = $db->query($db->sql());

$xtpl = new XTemplate('telepro-logs.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('SEARCH', $array_search);
$xtpl->assign('NV_CHECK_SESSION', NV_CHECK_SESSION);

while ($row = $result->fetch()) {
    $row['log_time'] = nv_date('H:i d/m/Y', $row['log_time']);

    $xtpl->assign('ROW', $row);
    $xtpl->parse('main.loop');
}

$generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);

if (!empty($generate_page)) {
    $xtpl->assign('GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
