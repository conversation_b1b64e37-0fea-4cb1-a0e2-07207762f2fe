<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */
if (!defined('NV_IS_FILE_ADMIN'))
    die('Stop!!!');

// Xem theo thành viên
$view_userid = $nv_Request->get_int('userid', 'get', 0);
$showheader = $nv_Request->get_int('showheader', 'get', 1);
$id = $nv_Request->get_int('id', 'get', 0);
if ($view_userid and !$id) {
    $sql = "SELECT userid, username FROM " . NV_USERS_GLOBALTABLE . " WHERE userid=" . $view_userid;
    $view_user_info = $db->query($sql)->fetch();
    if (empty($view_user_info)) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name);
    }
}

$array_search = [];
$array_search['time_from'] = $nv_Request->get_title('time_from', 'post,get');
$array_search['time_to'] = $nv_Request->get_title('time_to', 'post,get');

if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_from'], $m)) {
    $sfrom = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
} else {
    $sfrom = 0;
}
if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $array_search['time_to'], $m)) {
    $sto = mktime(23, 59, 59, $m[2], $m[1], $m[3]);
} else {
    $sto = 0;
}
$status = $nv_Request->get_int('status', 'get', 0);
$type_transaction = $nv_Request->get_int('type_transaction', 'get', -1);
$show_last = $nv_Request->get_int('show_last', 'get', 0);

$page = $nv_Request->get_int('page', 'get', 1);
$per_page = 20;
$base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;userid=' . $view_userid . '&amp;showheader=' . $showheader;

$where = [];
if ($view_userid) {
    $where[] = 'tb1.userid =' . $view_userid;
}
if ($sfrom > 0 and $sto > 0) {
    $where[] = 'tb1.created_time >=' . $sfrom . ' AND tb1.created_time <=' . $sto;
    $base_url .= '&amp;time_from=' . $array_search['time_from'] . '&amp;time_to=' . $array_search['time_to'];
}
if ($status != 0) {
    $where[] = 'tb1.status =' . $status;
    $base_url .= '&amp;status=' . $status;
}
if ($type_transaction != -1) {
    $where[] = 'tb1.type_transaction =' . $type_transaction;
    $base_url .= '&amp;type_transaction=' . $type_transaction;
}
if ($id) {
    $where[] = 'tb1.id =' . $id;
    $base_url .= '&amp;id=' . $id;
}

if ($show_last) {
    $all_page = 1;
    $db->select('*')
        ->from($db_config['prefix'] . '_' . $module_data . '_log tb1')
        ->where(implode(' AND ', $where))
        ->order('id DESC')
        ->limit(1);
} else {
    $db->sqlreset()
        ->select('COUNT(*)')
        ->from($db_config['prefix'] . '_' . $module_data . '_log tb1')
        ->where(implode(' AND ', $where));
    $all_page = $db->query($db->sql())
        ->fetchColumn();

    $db->select('*')
        ->order('created_time DESC')
        ->limit($per_page)
        ->offset(($page - 1) * $per_page);
}

$result = $db->query($db->sql());

$arr_list_transaction = $array_userid = array();
while ($_row = $result->fetch()) {
    $arr_list_transaction[$_row['id']] = $_row;
    $array_userid[$_row['admin_id']] = $_row['admin_id'];
    $array_userid[$_row['userid']] = $_row['userid'];
}

if (!empty($array_userid)) {
    $sql = "SELECT userid, username FROM " . NV_USERS_GLOBALTABLE . " WHERE userid IN (" . implode(',', $array_userid) . ")";
    $user_info = $db->query($sql);
    while ($_row = $user_info->fetch()) {
        $array_userid[$_row['userid']] = $_row;
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);

$i = 1;
foreach ($arr_list_transaction as $row) {
    $xtpl->assign('stt', $i);
    $row['created_time'] = nv_date('d/m/Y, H:i', $row['created_time']);
    $row['date_expired'] = (!empty($row['expired']) and $row['status'] == 1) ? nv_date('d/m/Y, H:i', $row['expired']) : '';
    $row['point_total'] = ($row['status'] == 1 ? '+ ' : '- ') . number_format($row['point_total']);
    $_aray_mess = json_decode($row['message'], true);
    if (is_array($_aray_mess)) {
        if (isset($_aray_mess[NV_LANG_DATA])) {
            $row['message'] = $_aray_mess[NV_LANG_DATA];
        } else {
            $row['message'] = array_shift(array_values($_aray_mess));
        }
    } else {
        $row['message'] = $row['message'];
    }
    $row['username'] = !empty($array_userid[$row['userid']]) ? $array_userid[$row['userid']]['username'] : '';
    $row['admin_id'] = ($row['admin_id'] == 0 ? $row['username'] : $array_userid[$row['admin_id']]['username']);

    $xtpl->assign('CONTENT', $row);
    $xtpl->parse('main.loop');
    $i++;
}

$generate_page = nv_generate_page($base_url, $all_page, $per_page, $page);
if ($generate_page) {
    $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
    $xtpl->parse('main.generate_page');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('transaction');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';
