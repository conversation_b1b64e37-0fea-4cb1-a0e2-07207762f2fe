<?php

/**
 * @Project WALLET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Friday, March 9, 2018 6:24:54 AM
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$id = $nv_Request->get_int('id', 'get', 0);
$set_active_op = 'transaction';

$is_update_add = $nv_Request->isset_request('update_add', 'get');
if ($is_update_add) {
    $address = $nv_Request->get_string('address', 'post', '');
    $contents = "NO_" . $id;
    if ($id > 0 and !empty($address)) {
        $query = "UPDATE " . $db_config['prefix'] . "_" . $module_data . "_transaction SET `customer_address` = " . $db->quote($address) . " WHERE `id` = " . $id;
        $db->query($query);
        $contents = "OK_" . $id;
    }
    include NV_ROOTDIR . '/includes/header.php';
    echo $contents;
    include NV_ROOTDIR . '/includes/footer.php';
}

if ($nv_Request->get_title('save_edit_location', 'post', '') === NV_CHECK_SESSION) {
    $province = $nv_Request->get_int('province', 'post', 0);
    $district = $nv_Request->get_int('district', 'post', 0);
    $ward = $nv_Request->get_int('ward', 'post', 0);
    $address = $nv_Request->get_title('customer_address', 'post', '');

    if (empty($address)) {
        $res = [
            'res' => 'error',
            'mess' => $nv_Lang->getModule('error_empty_address')
        ];
        nv_jsonOutput($res);
    }
    if ($province == 0) {
        $res = [
            'res' => 'error',
            'mess' => $nv_Lang->getModule('error_empty_province')
        ];
        nv_jsonOutput($res);
    }
    if ($district == 0) {
        $res = [
            'res' => 'error',
            'mess' => $nv_Lang->getModule('error_empty_district')
        ];
        nv_jsonOutput($res);
    }
    if ($ward == 0) {
        $res = [
            'res' => 'error',
            'mess' => $nv_Lang->getModule('error_empty_ward')
        ];
        nv_jsonOutput($res);
    }
    $query = "UPDATE " . $db_config['prefix'] . "_" . $module_data . "_transaction SET `province_id` = " . $province . ", `district_id` = " . $district . ", `ward_id` = " . $ward . ", `customer_address` = " . $db->quote($address) . " WHERE `id` = " . $id;
    $db->query($query);
    $res = [
        'res' => 'success',
        'mess' => $nv_Lang->getModule('update_add_success')
    ];
    nv_jsonOutput($res);
}

$sql = "SELECT tb1.*, tb2.username admin_transaction, tb3.username accounttran, tb4.username customer_transaction
FROM " . $db_config['prefix'] . "_" . $module_data . "_transaction tb1
LEFT JOIN " . NV_USERS_GLOBALTABLE . " tb2 ON tb1.adminid=tb2.userid
LEFT JOIN " . NV_USERS_GLOBALTABLE . " tb3 ON tb1.userid=tb3.userid
LEFT JOIN " . NV_USERS_GLOBALTABLE . " tb4 ON tb1.customer_id=tb4.userid
WHERE tb1.id = " . $id;
$result = $db->query($sql);
if ($result->rowCount() != 1) {
    nv_info_die($nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_title'), $nv_Lang->getGlobal('error_404_content'));
}
$row = $result->fetch();

$caregiver_id_leads = array();
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_crmbidding_groups_users WHERE userid = ' . $admin_info['userid'];
$result = $db->query($sql);
while ($row_groups_users = $result->fetch()) {
    $admin_config = json_decode($row_groups_users['config'], true);
    if ($row_groups_users['is_leader'] == 1) {
        $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_crmbidding_groups_users WHERE group_id = ' . $row_groups_users['group_id'] . ' AND userid != ' . $admin_info['userid'];
        $_result = $db->query($_sql);
        while ($_row_groups_users = $_result->fetch()) {
            $caregiver_id_leads[$_row_groups_users['userid']] = $_row_groups_users['userid'];
        }
    }
}
$caregiver_id_leads[$admin_info['userid']] = $admin_info['userid'];
// check quyền hạn xem giao dịch
if (!defined('NV_IS_SPADMIN') and $row['caregiver_id'] != 0 and !in_array($row['caregiver_id'], $caregiver_id_leads) and empty($PERMISSION_ADMIN['is_mtransaction'])) {
    nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=transaction');
}

$address_full = [];
if (!empty($row['customer_address'])) {
    $address_full = explode(', ', $row['customer_address']);
}
$row['province'] = $row['district'] = $row['ward'] = '';

// Lấy thông tin thành phố , quận , phường
if (!empty($row['ward_id'])) {
    $sql = "SELECT title FROM " . NV_PREFIXLANG . "_location_ward WHERE id=" . $row['ward_id'];
    $row['ward'] = $db->query($sql)->fetchColumn();
    $address_full[] = $row['ward'];
}

if (!empty($row['district_id'])) {
    $sql = "SELECT title FROM " . NV_PREFIXLANG . "_location_district WHERE id=" . $row['district_id'];
    $row['district'] = $db->query($sql)->fetchColumn();
    $address_full[] = $row['district'];
}

if (!empty($row['province_id'])) {
    $sql = "SELECT title FROM " . NV_PREFIXLANG . "_location_province WHERE id=" . $row['province_id'];
    $row['province'] = $db->query($sql)->fetchColumn();
    $address_full[] = $row['province'];
}

$xtpl = new XTemplate($op . ".tpl", NV_ROOTDIR . "/themes/" . $global_config['module_theme'] . "/modules/" . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);
$xtpl->assign('LINK_UPDATE_ADD', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . "=" . $op . "&update_add=1&id=" . $id);

foreach ($province_list as $key => $value) {
    $xtpl->assign('PROVINCE', array(
        'key' => $key,
        'title' => $value['title'],
        'selected' => $row['province_id'] == $key ? ' selected="selected"' : ''
    ));
    $xtpl->parse('main.province');
}

if (empty($row['order_id'])) {
    $row['code'] = sprintf('GD%010s', $row['id']);
} else {
    $row['code'] = sprintf('WP%010s', $row['id']);
}
$row['created_time'] = date("H:i d/m/Y", $row['created_time']);
$row['transaction_time'] = date("H:i d/m/Y", $row['transaction_time']);
$row['status_title'] = ($row['status'] == 1) ? $nv_Lang->getModule('transaction1') : $nv_Lang->getModule('transaction2');
$row['money_total'] = get_display_money($row['money_total']);
$row['money_net'] = get_display_money($row['money_net']);
$row['money_discount'] = get_display_money($row['money_discount']);
$row['money_revenue'] = get_display_money($row['money_revenue']);
$row['transaction_status'] = isset($global_array_transaction_status[$row['transaction_status']]) ? $global_array_transaction_status[$row['transaction_status']] : 'N/A';
$row['transaction_type'] = isset($global_array_transaction_type[$row['transaction_type']]) ? $global_array_transaction_type[$row['transaction_type']] : 'N/A';
$row['accounttran'] = empty($row['accounttran']) ? 'N/A' : $row['accounttran'];
$row['transaction_uname'] = ($row['admin_transaction'] ? '<strong>' . $row['admin_transaction'] . '</strong>' : ($row['customer_transaction'] ? $row['customer_transaction'] : $row['customer_name']));
$row['payment'] = isset($global_array_payments[$row['payment']]) ? $global_array_payments[$row['payment']]['payment'] : $nv_Lang->getModule('transaction_payment_no');
$row['paymentname'] = isset($global_array_payments[$row['payment']]) ? $global_array_payments[$row['payment']]['paymentname'] : $nv_Lang->getModule('transaction_payment_no');

$row['transaction_id'] = $row['transaction_id'] ? $row['transaction_id'] : '--';
$row['customer_name'] = $row['customer_name'] ? $row['customer_name'] : '--';
$row['customer_email'] = $row['customer_email'] ? $row['customer_email'] : '--';
$row['customer_phone'] = $row['customer_phone'] ? $row['customer_phone'] : '--';
$row['customer_address'] = $row['customer_address'] ? $row['customer_address'] : '';
$row['address_full'] = $address_full ? implode(', ', $address_full) : '';
$row['customer_info'] = $row['customer_info'] ? $row['customer_info'] : '--';

$row['link_opportunities'] = $row['opportunities_id'] > 0 ? NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&" . NV_NAME_VARIABLE . "=crmbidding&" . NV_OP_VARIABLE . "=opportunities_info&id=" . $row['opportunities_id'] : '';

$_aray_mess = json_decode($row['transaction_info'], true);
if (is_array($_aray_mess)) {
    if (isset($_aray_mess[NV_LANG_DATA])) {
        $row['transaction_info'] = $_aray_mess[NV_LANG_DATA];
    } else {
        $row['transaction_info'] = array_shift(array_values($_aray_mess));
    }
} else {
    $row['transaction_info'] = $row['transaction_info'];
}
$row['source_money_title'] = $arr_source_money[$row['source_money']];

$xtpl->assign('CONTENT', $row);

if ($row['opportunities_id'] > 0) {
    $xtpl->parse('main.link_opportunities');
}

if ($row['status'] == 1) {
    $xtpl->parse('main.source_money');
}

$array_files = [];
$array_files_key = 0;

if (!empty($row['transaction_data'])) {
    $transaction_data = unserialize($row['transaction_data']);
    $transaction_data_size = 0;
    foreach ($transaction_data as $key => $value) {
        if (!empty($value)) {
            $transaction_data_size++;
            $xtpl->assign('OTHER_KEY', $nv_Lang->getModule($key));

            if ($key == 'atm_filedepute' or $key == 'atm_filebill' or $key == 'vietqr_screenshots') {
                $files = explode('|', $value);
                if (isset($files[1])) {
                    $array_files_key++;
                    $array_files[$array_files_key] = [
                        'filename' => $files[0],
                        'filepath' => $files[1]
                    ];
                    $xtpl->assign('OTHER_VAL', $files[0]);
                    $xtpl->assign('OTHER_LINK', NV_BASE_ADMINURL . "index.php?" . NV_LANG_VARIABLE . "=" . NV_LANG_DATA . "&amp;" . NV_NAME_VARIABLE . "=" . $module_name . "&amp;" . NV_OP_VARIABLE . "=" . $op . "&amp;id=" . $id . '&amp;file=' . $array_files_key);
                    $xtpl->parse('main.transaction_data.loop.link');
                }
            } else {
                $xtpl->assign('OTHER_VAL', $value);
                $xtpl->parse('main.transaction_data.loop.text');
            }

            $xtpl->parse('main.transaction_data.loop');
        }
    }
    if ($transaction_data_size > 0) {
        $xtpl->parse('main.transaction_data');
    }
}

// Tải file về: Đường dẫn file này bí mật
$file_key = $nv_Request->get_int('file', 'get', '');
if (isset($array_files[$file_key])) {
    $file_src = NV_UPLOADS_REAL_DIR . '/' . $module_upload . '/' . $array_files[$file_key]['filepath'];
    $download = new NukeViet\Files\Download($file_src, NV_UPLOADS_REAL_DIR . '/' . $module_upload, $array_files[$file_key]['filename'], true);
    $download->download_file();
    die();
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('detailtransaction');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
