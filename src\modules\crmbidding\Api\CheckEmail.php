<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */

namespace NukeViet\Module\crmbidding\Api;

use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CheckEmail implements IApi
{
    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'email';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $email = $nv_Request->get_title('email', 'post', '');

        if (!empty($email)) {
            try {
                $where = [
                    'email="'. $email .'"'
                ];
                $db->sqlreset()->where(implode(' AND ', $where));

                $db->select('COUNT(*)')->from('nv4_users');                    
                $sth = $db->query($db->sql());
                if ($sth->fetchColumn()) {
                    return $this->result->setSuccess()->getResult();
                }

                $db->select('COUNT(*)')->from(NV_PREFIXLANG . '_' . $module_data . '_leads ');
                $sth = $db->query($db->sql());
                if ($sth->fetchColumn()) {
                    return $this->result->setSuccess()->getResult();
                }

                $db->select('COUNT(*)')->from(NV_PREFIXLANG . '_' . $module_data . '_opportunities ');
                $sth = $db->query($db->sql());
                if ($sth->fetchColumn()) {
                    return $this->result->setSuccess()->getResult();
                }
            } catch (PDOException $e) {
                return $this->result->setError()
                    ->setCode('2000')
                    ->setMessage(print_r($e, true))
                    ->getResult();
            }
        }
        return $this->result->getResult();
    }
}
