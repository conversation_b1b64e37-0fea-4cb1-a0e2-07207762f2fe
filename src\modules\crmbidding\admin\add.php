<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:39:33 GMT
 */
use NukeViet\Api\DoApi;
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

// List
$error = "";
$success = "";
$input = "";

$row = [];
// $row['id'] = 0;
$row['source_leads'] = 0;
$row['user_id'] = 0;
$row['businessid'] = 0;
$row['teleproid'] = 0;
$row['name'] = '';
$row['phone'] = '';
$row['sub_phone'] = '';
$row['email'] = '';
$row['sub_email'] = '';
$row['address'] = '';
$row['tax'] = '';
$row['company_name'] = '';
$row['address_company'] = '';
$row['affilacate_id'] = $admin_info['userid'];
$row['caregiver_id'] = $admin_info['userid'];
$row['convert_contact'] = 0;
$row['convert_organization'] = 0;
$row['siteid'] = 0;
$row['prefix_lang'] = 0;

// Thêm mới leads
if ($nv_Request->isset_request('submit', 'post')) {
    managerLeads();
}

// Lấy thông tin user điền vào form khi một tài khoản được chọn
if ($nv_Request->isset_request('getuser', 'post')) {
    $userid = $nv_Request->get_int('userid', 'post', 0);
    $respon = [
        'name' => '',
        'phone' => '',
        'email' => '',
        'tax' => ''
    ];

    if (!empty($userid)) {
        // Lấy thành viên
        $sql = "SELECT last_name, first_name, email, regdate FROM " . NV_USERS_GLOBALTABLE . " WHERE userid=" . $userid;
        $check_user = $db->query($sql)->fetch();
        if (!empty($check_user)) {
            $respon['name'] = $check_user['last_name'] . ' ' . $check_user['first_name'];
            $respon['email'] = $check_user['email'];

            $sql = "SELECT phone, mst FROM " . NV_USERS_GLOBALTABLE . "_info WHERE userid=" . $userid;
            $user_info = $db->query($sql)->fetch();
            $respon['phone'] = $user_info['phone'];
            $respon['tax'] = $user_info['mst'];
        }
    }
    nv_jsonOutput($respon);
}
$is_convert_contact = $nv_Request->get_int('id_contact', 'get', 0);
$is_convert_org = $nv_Request->get_int('id_org', 'get', 0);

if ($is_convert_contact > 0) {
    $arr_info_contact = [];
    $params['contactid'] = $is_convert_contact;
    $arr_info_contact = nv_local_api('GetDetailContact', $params, $admin_info['username'], $module_name);
    $arr_info_contact = json_decode($arr_info_contact, true);
    if (isset($arr_info_contact['data'])) {
        $row['name'] = $arr_info_contact['data']['contactname'];
        $row['phone'] = $arr_info_contact['data']['primaryphone'];
        $row['sub_phone'] = $arr_info_contact['data']['secondaryphone'];
        $row['email'] = $arr_info_contact['data']['primaryemail'];
        $row['sub_email'] = $arr_info_contact['data']['secondaryemail'];
        $row['address'] = $arr_info_contact['data']['address'];
        $row['caregiver_id'] = 0;
        $row['affilacate_id'] = 0;
        $row['convert_contact'] = $arr_info_contact['data']['id'];
    }
}

if ($is_convert_org > 0) {
    $arr_info_org = [];
    $params['organizationsid'] = $is_convert_org;
    $arr_info_org = nv_local_api('GetDetailOrganizations', $params, $admin_info['username'], $module_name);
    $params = [];
    $arr_info_org = json_decode($arr_info_org, true);
    if (isset($arr_info_org['data'])) {
        $row['name'] = $arr_info_org['data']['organizationname'];
        $row['phone'] = $arr_info_org['data']['primaryphone'];
        $row['sub_phone'] = $arr_info_org['data']['secondaryphone'];
        $row['email'] = $arr_info_org['data']['primaryemail'];
        $row['sub_email'] = $arr_info_org['data']['secondaryemail'];
        $row['address'] = $arr_info_org['data']['trading_address'];
        $row['caregiver_id'] = 0;
        $row['affilacate_id'] = 0;
        $row['convert_organization'] = $arr_info_org['data']['id'];
    }
}

if ($nv_Request->isset_request('get_bussiness', 'post, get')) {
    $q_select = $nv_Request->get_title('q_select', 'post, get', '');
    $type = $nv_Request->get_int('type', 'post, get', 0);
    $array_business_select = $array_business_return = [];
    if (!empty($q_select)) {
        if ($type == 1) { // Nhà thầu
            $where['OR'][] = [
                'LIKE' => [
                    'content_full' => "%" . $q_select . "%"
                ]
            ];
            $params_business = [
                'userid' => $admin_info['userid'],
                'page' => 1,
                'perpage' => 50,
                'where' => $where
            ];

            $api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
            $api->setModule('businesslistings')
                ->setLang('vi')
                ->setAction('ListAllBusinessListings')
                ->setData($params_business);
        } elseif ($type == 2) { // Bên mời thầu
            $where['OR'][] = [
                'LIKE' => [
                    'content_full' => "%" . $q_select . "%"
                ]
            ];
            $params_solicitor = [
                'userid' => $admin_info['userid'],
                'page' => 1,
                'perpage' => 50,
                'where' => $where
            ];

            $api = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
            $api->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListAllSolicitor')
                ->setData($params_solicitor);
        }
        isset($api) && $array_business_select = $api->execute();
        if (!empty($array_business_select) and $array_business_select['code'] == "0000") {
            foreach ($array_business_select['data'] as $key => $value) {
                $array_business_return[] = [
                    'id' => $value['id'],
                    'title' => $type == 1 ? $value['companyname'] : $value['title']
                ];
            }
        }
    }
    nv_jsonOutput($array_business_return);
}

$row['sub_email'] = nv_htmlspecialchars(nv_br2nl($row['sub_email']));

$xtpl = new XTemplate('add.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('ROW', $row);

foreach ($array_groups_leads as $value) {
    $xtpl->assign('OPTION', [
        'key' => $value['id'],
        'title' => $value['title'],
        'selected' => ($value['id'] == $row['source_leads']) ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.select_source_leads');
}

foreach ($array_user_id_users as $value) {
    $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
    $xtpl->assign('OPTION', [
        'key' => $value['userid'],
        'title' => $value['username'] . ' (' . $fullname . ')',
        'selected' => ($value['userid'] == $row['affilacate_id']) ? ' selected="selected"' : '',
        'selectedcaregiver_id' => ($value['userid'] == $row['caregiver_id']) ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.select_affilacate_id');
    $xtpl->parse('main.select_caregiver_id');
}

foreach ($array_site as $key => $value) {
    $xtpl->assign('OPTION', [
        'key' => $key,
        'title' => $value,
        'selected' => ($key == $row['siteid']) ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.select_siteid');
}

foreach ($array_lang as $key => $value) {
    $xtpl->assign('OPTION', [
        'key' => $key,
        'title' => $value,
        'selected' => ($key == $row['prefix_lang']) ? ' selected="selected"' : ''
    ]);
    $xtpl->parse('main.select_prefix_lang');
}
if (!empty($error)) {
    $xtpl->assign('ERROR', $error);
    $xtpl->parse('main.error');
}

if (!empty($hot_leads['error'])) {
    if ($hot_leads['error'] == 'phone') {
        $xtpl->parse('main.error_phone');
    } elseif ($hot_leads['error'] == 'email') {
        $xtpl->parse('main.error_email');
    } elseif ($hot_leads['error'] == 'sub_phone') {
        $xtpl->parse('main.error_sub_phone');
    } elseif ($hot_leads['error'] == 'sub_email') {
        $xtpl->parse('main.error_sub_email');
    }
}

if (!empty($success)) {
    $xtpl->assign('SUCCESS', $success);
    $xtpl->parse('main.success');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('leads');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';

function managerLeads()
{
    global $error, $success, $nv_Request, $admin_info, $module_name, $nv_Cache, $module_name, $row, $op, $admin_info, $db, $hot_leads, $array_status, $array_status_opportunities, $array_groups_leads, $nv_Lang;
    $row['user_id'] = $nv_Request->get_int('userid', 'post', 0);
    $row['sub_phone'] = $nv_Request->get_title('sub_phone', 'post', '');
    $row['sub_email'] = $nv_Request->get_textarea('sub_email', '', NV_ALLOWED_HTML_TAGS);
    $row['address'] = $nv_Request->get_title('address', 'post', '');
    $row['tax'] = $nv_Request->get_title('tax', 'post', '');
    $row['company_name'] = $nv_Request->get_title('company_name', 'post', '');
    $row['address_company'] = $nv_Request->get_title('address_company', 'post', '');
    $row['affilacate_id'] = $nv_Request->get_int('affilacate_id', 'post', 0);
    $row['caregiver_id'] = $nv_Request->get_int('caregiver_id', 'post', $admin_info['userid']);
    $row['about'] = $nv_Request->get_textarea('about', '', NV_ALLOWED_HTML_TAGS);
    $row['siteid'] = $nv_Request->get_int('siteid', 'post', 0);
    $row['prefix_lang'] = $nv_Request->get_int('prefix_lang', 'post', 0);

    $source_list = $nv_Request->get_int('source_leads', 'post', 0);
    if ($source_list == 15) {
        $row['businessid'] = $nv_Request->get_int('solicitor_id', 'post', 0);
    } else {
        $row['businessid'] = $nv_Request->get_int('business_id', 'post', 0);
    }

    $data = [];
    foreach ($row as $key => $value) {
        if ($value != "") {
            $data[$key] = $value;
        }
    }

    $row['source_leads'] = $source_list;
    $row['name'] = $nv_Request->get_title('name', 'post', '');
    $row['phone'] = $nv_Request->get_title('phone', 'post', '');
    $row['email'] = $nv_Request->get_title('email', 'post', '');
    $row['convert_contact'] = $nv_Request->get_int('contactid', 'post', 0);
    $row['convert_organization'] = $nv_Request->get_int('organizationid', 'post', 0);
    if ($row['user_id'] > 0) {
        $check_user = $db->query('SELECT * FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $row['user_id'])->fetch();
        if (empty($check_user)) {
            $error = $nv_Lang->getModule('not_user');
        }

        $where['AND'][] = [
            '=' => [
                'user_id' => $row['user_id']
            ]
        ];

        $params_leads = [
            'userid' => $admin_info['userid'],
            'use_elastic' => 1,
            'where' => $where
        ];

        $data_leads = nv_local_api('ListAllLeads', $params_leads, $admin_info['username'], $module_name);
        $check_leads = json_decode($data_leads, true);

        if (isset($check_leads['data'])) {
            $error = $nv_Lang->getModule('error_required_users');
        }
    }

    if (empty($row['source_leads'])) {
        $error = $nv_Lang->getModule('error_required_source_leads');
    }

    if ($row['source_leads'] == 2 and empty($row['businessid'])) {
        $error = $nv_Lang->getModule('error_required_business');
    }

    if ($row['source_leads'] == 15 and empty($row['businessid'])) {
        $error = $nv_Lang->getModule('error_required_solicitor');
    }

    if ($row['source_leads'] != 2 and $row['source_leads'] != 15 and $row['source_leads'] != 7 and $row['source_leads'] != 4 and $row['source_leads'] != 11) {
        $row['affilacate_id'] = 0;
    }

    if ($row['source_leads'] != 1) {
        $row['user_id'] = 0;
    }

    if ($row['name'] == '') {
        $error = $nv_Lang->getModule('error_required_name');
    }

    if ($row['phone'] == '' and $row['email'] == '') {
        $error = $nv_Lang->getModule('error_required_phone');
    } elseif ($row['phone'] != '' && !phonecheck($row['phone'], $row['prefix_lang'])) {
        $error = $nv_Lang->getModule('error_phone_number');
    } elseif ($row['email'] != '' && nv_check_valid_email($row['email']) != '') {
        $error = $nv_Lang->getModule('error_email');
    }

    $hot_leads = check_hot_leads($row['phone'], $row['email'], $row['sub_phone'], $row['sub_email']);
    if (isset($hot_leads) && $hot_leads['status'] == 1) {
        $error = hot_lead_error_message($hot_leads['data']);
    }

    if (empty($error)) {
        // Trường hợp thêm
        $params = [
            'source_leads' => $row['source_leads'],
            'name' => $row['name'],
            'phone' => $row['phone'],
            'email' => $row['email'],
            'admin_id' => $admin_info['admin_id'],
            'siteid' => $row['siteid'],
            'prefix_lang' => $row['prefix_lang']
        ];
        // xóa các biến của otherdata do data đang lấy toàn bộ row
        unset($data['source_leads']);
        unset($data['name']);
        unset($data['phone']);
        unset($data['email']);
        unset($data['siteid']);
        unset($data['prefix_lang']);

        if (!empty($row['convert_contact'])) {
            $data['convert_contact'] = $row['convert_contact'];
        }

        if (!empty($row['convert_organization'])) {
            $data['convert_organization'] = $row['convert_organization'];
        }

        if (!empty($data)) {
            $params['otherdata'] = $data;
        }

        $result_add = nv_local_api('CreateLeads', $params, $admin_info['username'], $module_name);
        $result_add = json_decode($result_add, true);
        if ($result_add['code'] == "0000") {
            $link = NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=leads_info&id=' . $result_add['leadsid'];
            $success = sprintf($nv_Lang->getModule('success__'), 'lead', $link, $result_add['leadsid']);
            if (!empty($data['convert_contact'])) {
                $data = [
                    'convert_leads' => $result_add['leadsid']
                ];

                $_params_update = [
                    'contactid' => $row['convert_contact'],
                    'admin_id' => $admin_info['userid'],
                    'data' => $data
                ];
                $status_update = nv_local_api('UpdateContact', $_params_update, $admin_info['username'], $module_name);
                $status_update = json_decode($status_update, true);
            }
            if (!empty($data['convert_organization'])) {
                $data = [
                    'convert_leads' => $result_add['leadsid']
                ];

                $_params_update = [
                    'organizationsid' => $row['convert_organization'],
                    'admin_id' => $admin_info['userid'],
                    'data' => $data
                ];
                $status_update = nv_local_api('UpdateOrganizations', $_params_update, $admin_info['username'], $module_name);
                $status_update = json_decode($status_update, true);
            }
        } else {
            $error = $result_add['message'];
        }
    }
}
