<!-- BEGIN: main -->
<form action="{NV_BASE_ADMINURL}index.php?{NV_LANG_VARIABLE}={NV_LANG_DATA}&amp;{NV_NAME_VARIABLE}={MODULE_NAME}&amp;{NV_OP_VARIABLE}={OP}" method="post" class="form-inline">
  <div class="table-responsive">
    <table class="table table-striped table-bordered table-hover">
      <caption>{LANG.module_config}</caption>
      <tbody>
        <tr>
          <th>{LANG.set_leads_to_sales}</th>
          <td><input type="checkbox" value="1" class="form-control" {DATA.set_leads_to_sales} name="set_leads_to_sales" /></td>
        </tr>
        <tr>
          <th>{LANG.set_order_to_sales}</th>
          <td><input type="checkbox" value="1" class="form-control" {DATA.set_order_to_sales} name="set_order_to_sales" /></td>
        </tr>
        <tr>
          <th>{LANG.sales_not_set_status}</th>
          <td><input type="checkbox" value="1" class="form-control" {DATA.sales_not_set_status} name="sales_not_set_status" /></td>
        </tr>
        <tr>
          <th>{LANG.time_revoke_leads}</th>
          <td><input type="text" value="{DATA.time_revoke_leads}" class="form-control" name="time_revoke_leads" /> {LANG.minutes}</td>
        </tr>
        <tr>
          <th>{LANG.allow_viewall_order}</th>
          <td><input type="checkbox" value="1" class="form-control" name="allow_viewall_order" {DATA.allow_viewall_order}/></td>
        </tr>
        <tr>
          <th>{LANG.allow_delete_order}</th>
          <td><input type="checkbox" value="1" class="form-control" name="allow_delete_order" {DATA.allow_delete_order}/></td>
        </tr>
        <tr>
          <th>{LANG.allow_delete_vips}</th>
          <td><input type="checkbox" value="1" class="form-control" name="allow_delete_vips" {DATA.allow_delete_vips} /></td>
        </tr>
        <tr>
          <th>{LANG.view_static}</th>
          <td><label><input type="radio" value="1" class="form-control" name="view_static" {DATA.view_static1} />{LANG.view_static_all}</label> <label><input type="radio" value="2" class="form-control" name="view_static" {DATA.view_static2} />{LANG.view_static_sale}</label></td>
        </tr>
        <tr>
          <th>{LANG.config_view_leads}</th>
          <td>
            <label><input type="radio" value="1" class="form-control" name="view_leads" {DATA.view_leads1} />{LANG.view_leads_all}</label>
            <label><input type="radio" value="2" class="form-control" name="view_leads" {DATA.view_leads2} />{LANG.view_leads_config}</label>
          </td>
        </tr>
        <tr>
          <th>{LANG.num_lead_leader_view}</th>
          <td><input type="text" value="{DATA.num_lead_leader_view}" class="form-control" name="num_lead_leader_view" /></td>
        </tr>
        <tr>
          <th>{LANG.num_lead_sale_view}</th>
          <td><input type="text" value="{DATA.num_lead_sale_views}" class="form-control" name="num_lead_sale_views" /></td>
        </tr>
      </tbody>
    </table>
  </div>
  <script type="text/javascript">
      var num = {CONFIG_WEIGHT_COUNT}
      function nv_add_items() {
          num += 1;
          var html = '';
          html += '<tr id="weight_' + num + '">';
      html += '<td class="text-center"><input type="text" class="form-control" value="" name="agent_id_' + num + '" id="agent_id_' + num + '"></td>';
      html += '<td class="text-center"><input type="text" class="form-control" value="" name="agent_id_name_' + num + '" id="agent_id_name_' + num + '"></td>';
          html += '<td class="text-center"><input type="text" class="form-control" value="" name="agent_id_phone_' + num + '" id="agent_id_phone_' + num + '"></td>';
          html += '<td class="text-center"><em class="fa fa-trash-o fa-lg">&nbsp;</em><a href="javascript:void(0);" onclick="nv_remove_item(' + num + ');">{LANG.delete}</a> <input type="hidden" name="ids[]" value="' + num + '" /></td>';
          html += '</tr>';

          $('#id-area').append(html);
      }
      function nv_remove_item(num) {
          $('#weight_' + num).remove();
      }
  </script>

<div class="table-responsive">
    <table class="table table-striped table-bordered table-hover">
        <caption>
            <em class="fa fa-file-text-o">&nbsp;</em>{LANG.config_econtract}
        </caption>
        <tbody>
            <tr>
              <th>{LANG.max_file_econtract_file}</th>
              <td><input type="number" value="{DATA.max_file_econtract_file}" class="form-control w-100" name="max_file_econtract_file" /> MB</td>
            </tr>
            <tr>
                <th>{LANG.econtract_leader_emails}</th>
                <td><input type="text" value="{DATA.econtract_leader_emails}" class="form-control w-100" name="econtract_leader_emails" /></td>
            </tr>
            <tr>
              <th>{LANG.email_hardcopy_contract}</th>
              <td><input type="text" value="{DATA.email_hardcopy_contract}" class="form-control w-100" name="email_hardcopy_contract" /></td>
          </tr>
        </tbody>
    </table>
</div>

  <div class="table-responsive">
    <table class="table table-striped table-bordered table-hover">
        <caption>
            <em class="fa fa-file-text-o">&nbsp;</em>{LANG.fbtitle}
        </caption>
        <tbody>
           <tr>
          <th>{LANG.accesstokenfb}</th>
          <td><input type="text" value="{DATA.accesstokenfb}" class="form-control" name="accesstokenfb" /></td>
        </tr>
        <tr>
          <th>{LANG.blockphone}</th>
          <td><textarea rows="4" cols="50" class="form-control" name="blockphone">{DATA.blockphone}</textarea></td>
        </tr>
        <tr>
          <th>{LANG.blockemail}</th>
          <td><textarea rows="4" cols="50" class="form-control" name="blockemail">{DATA.blockemail}</textarea></td>
        </tr>
        </tbody>
    </table>
  </div>

  <div class="table-responsive">
    <table class="table table-striped table-bordered table-hover">
        <caption>
            <em class="fa fa-file-text-o">&nbsp;</em>{LANG.zalotitle}
        </caption>
        <tbody>
          <tr>
            <th class="w-200">{LANG.zaloOfficialAccountID}</th>
            <td><input type="text" value="{DATA.zaloOfficialAccountID}" class="form-control" name="zaloOfficialAccountID" /></td>
          </tr>
          <tr>
            <th>{LANG.zaloAppID}</th>
            <td><input type="text" value="{DATA.zaloAppID}" class="form-control" name="zaloAppID" /></td>
          </tr>
          <tr>
            <th>{LANG.zaloAppSecretKey}</th>
            <td><input type="text" value="{DATA.zaloAppSecretKey}" class="form-control" name="zaloAppSecretKey" /></td>
          </tr>
          <tr>
            <th>{LANG.zaloOASecretKey}</th>
            <td><input type="text" value="{DATA.zaloOASecretKey}" class="form-control" name="zaloOASecretKey" /></td>
          </tr>
          <tr>
            <th>{LANG.zaloOAAccessToken}</th>
            <td><input type="text" value="{DATA.zaloOAAccessToken}" class="form-control" name="zaloOAAccessToken" /></td>
          </tr>
          <tr>
            <th>{LANG.zaloOARefreshToken}</th>
            <td><input type="text" value="{DATA.zaloOARefreshToken}" class="form-control" name="zaloOARefreshToken" /></td>
          </tr>
          <tr>
            <th>{LANG.note_zalo}</th>
            <td>{LANG.oa_create_note}{LANG.app_note}</td>
          </tr>
        </tbody>
    </table>
  </div>
  <div class="table-responsive">
    <table class="table table-striped table-bordered table-hover">
        <caption>
            <em class="fa fa-file-text-o">&nbsp;</em>{LANG.setting_seach}
        </caption>
        <tbody>
            <tr>
                <td colspan="2">{LANG.use_setup_elasticseach}: <a href="http://wiki.nukeviet.vn/web_server:install-and-configure-elasticsearch-on-centos-7" target="_blank">http://wiki.nukeviet.vn/web_server:install-and-configure-elasticsearch-on-centos-7</a><br /> {LANG.use_dev_elasticseach}: <a href="http://wiki.nukeviet.vn/web_server:use-elasticsearch-in-nukeviet" target="_blank">http://wiki.nukeviet.vn/web_server:use-elasticsearch-in-nukeviet</a><br />
                </td>
            </tr>
            <tr>
                <th>{LANG.setting_elas_use}</th>
                <td><input type="checkbox" value="1" name="elas_use" {ELAS_USE}/></td>
            </tr>
            <tr>
                <th>{LANG.setting_elas_host}</th>
                <td><input class="form-control" type="text" value="{DATA.elas_host}" name="elas_host" /></td>
            </tr>
            <tr>
                <th>{LANG.setting_elas_port}</th>
                <td><input class="form-control" type="text" value="{DATA.elas_port}" name="elas_port" /></td>
            </tr>
            <tr>
                <th>{LANG.setting_elas_user}</th>
                <td><input class="form-control" type="text" value="{DATA.elas_user}" name="elas_user" /></td>
            </tr>
            <tr>
                <th>{LANG.setting_elas_pass}</th>
                <td><input class="form-control" type="password" value="{DATA.elas_pass}" name="elas_pass" /></td>
            </tr>
        </tbody>
    </table>
  </div>

  <div class="table-responsive">
    <div class="col-md-12">
      <table class="table table-striped table-bordered table-hover table-responsive">
        <caption>
          <em class="fa fa-file-text-o">&nbsp;</em>{LANG.setting_mobifone}
        </caption>
        <thead>
          <tr class="text-center">
            <th class="text-center">{LANG.agent_id}</th>
            <th class="text-center">{LANG.agent_id_phone}</th>
            <th class="text-center">{LANG.agent}</th>
          </tr>
        </thead>
        <tbody>
          <!-- BEGIN: loop -->
          <tr>
            <td class="text-center"><input type="text" class="form-control" value="{CALLED.agent}" name="agent_{KEY}"></td>
            <td class="text-center"><input type="text" class="form-control" value="{CALLED.called}" name="called_{KEY}"></td>
            <td class="text-center"><input type="text" class="form-control" value="{CALLED.called_name}" name="called_name_{KEY}"></td>
          </tr>
          <!-- END: loop -->
        </tbody>
      </table>
    </div>
    <div class="col-md-12">
      <table class="table table-striped table-bordered table-hover">
        <caption>
          <em class="fa fa-file-text-o">&nbsp;</em>{LANG.setting_mobifone_agent}
        </caption>
        <thead>
          <tr class="text-center">
            <th class="text-center">{LANG.agent_id}</th>
            <th class="text-center">{LANG.agent_id_name}</th>
            <th class="text-center">{LANG.agent_id_phone}</th>
          </tr>
        </thead>
        <tbody id="id-area">
          <!-- BEGIN: loop_agent -->
          <tr id="weight_{KEY}">
            <td class="text-center"><input type="text" class="form-control" value="{VALUE.agent_id}" name="agent_id_{KEY}" id="agent_id_{KEY}"></td>
            <td class="text-center"><input type="text" class="form-control" value="{VALUE.agent_id_name}" name="agent_id_name_{KEY}" id="agent_id_name_{KEY}"></td>
            <td class="text-center"><input type="text" class="form-control" value="{VALUE.agent_id_phone}" name="agent_id_phone_{KEY}" id="agent_id_phone_{KEY}"></td>
            <td class="text-center"><em class="fa fa-trash-o fa-lg">&nbsp;</em><a href="javascript:void(0);" onclick="nv_remove_item({KEY});">{LANG.delete}</a> <input type="hidden" name="ids[]" value="{KEY}" /></td>
          </tr>
          <!-- END: loop_agent -->
        </tbody>
        <tfoot>
          <tr>
            <td colspan="1"><a href="javascript:void(0)" class="btn btn-success" onclick="nv_add_items()">Thêm</a></td>
          </tr>
        </tfoot>
      </table>
    </div>
  </div>
  <div class="text-center">
    <input class="btn btn-primary" name="submit" type="submit" value="{LANG.save}" />
  </div>
</form>
<!-- END: main -->
