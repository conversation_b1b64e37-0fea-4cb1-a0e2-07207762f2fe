<?php
define('NV_SYSTEM', true);

// define('NV_ROOTDIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME) . '/../public_html')));
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

$_SERVER['HTTP_HOST'] = 'id.dauthau.net.my';
$_SERVER['HTTPS'] = 'off';

$_SERVER['SERVER_NAME'] = 'localhost';
$_SERVER['SERVER_PROTOCOL'] = 'http';
$_SERVER['REQUEST_URI'] = '';
$_SERVER['SERVER_PORT'] = '80';
$_SERVER['PHP_SELF'] = '';
$_SERVER['HTTP_CLIENT_IP'] = '127.0.0.1';
$_GET['language'] = 'vi';

require NV_ROOTDIR . '/includes/mainfile.php';

if (file_exists(NV_ROOTDIR . '/tool/static_point_download.txt')) {
    $array = file_get_contents(NV_ROOTDIR . '/tool/static_point_download.txt');
    $array = json_decode($array, true);
} else {
    $array = [];
    $array['id'] = 0;

    // R là Regular (Thông thường), V - Tải theo các gói VIP, F - fast (Mua tải nhanh)
    // $op là func xử lý
    $array['data']['view']['F'] = 0;
    $array['data']['view']['R'] = 0;
    $array['data']['view']['V'] = 0;

    $array['data']['plans']['F'] = 0;
    $array['data']['plans']['R'] = 0;
    $array['data']['plans']['V'] = 0;

    $array['data']['projects']['F'] = 0;
    $array['data']['projects']['R'] = 0;
    $array['data']['projects']['V'] = 0;

    $array['data']['result']['F'] = 0;
    $array['data']['result']['R'] = 0;
    $array['data']['result']['V'] = 0;
}

$query = $db->query('SELECT * FROM `nv4_vi_bidding_customs_rowfiles` WHERE id > ' . $array['id'] . ' ORDER BY id ASC LIMIT 10000');
$a = 0;
while ($_row = $query->fetch()) {
    $array['id'] = $_row['id'];

    // xác định điểm
    if ($_row['type'] == 'F') {
        $mess = 'Tải nhanh hồ sơ gói thầu';
    } else {
        $mess = 'Phục hồi Tải nhanh gói thầu';
    }
    $sql_point = "SELECT point_total FROM nv4_points_log WHERE userid = " . $_row['userid'] . " AND status = -1 AND message LIKE " . $db->quote($mess . '%') . " AND message LIKE '%" . $_row['opid'] . "'";
    $point_total = $db->query($sql_point)->fetchColumn();
    if ($point_total > 0) {
        $array['data'][$_row['op']][$_row['type']] += $point_total;
    }
    $a++;
}

echo "\n id:" . $array['id'];
file_put_contents(NV_ROOTDIR . '/tool/static_point_download.txt', json_encode($array));
echo "\n<br>Cập nhật trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";

if ($a > 0) {
    die('Còn chạy tiếp!!!');
}
die('Đã chạy xong!!!');
