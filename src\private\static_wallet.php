<?php
/*
 * Thống kê ví tiền
 * Chạy 1 tiếng 1 lần
 */
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);

define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __FILE__), PATHINFO_DIRNAME))));
require NV_CONSOLE_DIR . '/server.php';
require NV_ROOTDIR . '/includes/mainfile.php';

if (ob_get_level()) {
    ob_end_clean();
}

define('NV_ADMIN', true);
if ($sys_info['ini_set_support']) {
    set_time_limit(0);

    if ((int) ini_get('memory_limit') < 512) {
        ini_set('memory_limit', '512M');
    }
}

$last_id = $db->query("SELECT config_value FROM " . NV_CONFIG_GLOBALTABLE . " WHERE lang = 'vi' AND module = 'wallet' AND config_name = 'last_static_transaction'")->fetchColumn();
$last_id = $last_id ? $last_id : 0;
try {
    // lấy các giao dịch nạp tiền
    $last_id_max = $last_id + 100;
    $sql = "SELECT * FROM nv4_wallet_transaction WHERE id > " . $last_id . " AND id <= " . $last_id_max . " AND transaction_status = 4 ORDER BY id ASC LIMIT 100";
    $result = $db->query($sql);
    $last_id_tmp = $last_id;
    while ($row = $result->fetch()) {
        if ($row['transaction_type'] == -1 and $row['status'] == 1) {
            // đánh dấu đã nạp tiền
            $db->query("UPDATE " . NV_USERS_GLOBALTABLE . "_info SET charge_wallet = charge_wallet + " . $row['money_total'] . " WHERE userid = " . $row['userid']);
        }

        if ($row['status'] == 1) {
            $db->query("UPDATE " . NV_USERS_GLOBALTABLE . "_info SET money_wallet = money_wallet + " . $row['money_total'] . " WHERE userid = " . $row['userid']);
        } else {
            $db->query("UPDATE " . NV_USERS_GLOBALTABLE . "_info SET money_wallet = money_wallet - " . $row['money_total'] . " WHERE userid = " . $row['userid']);
        }

        $last_id_tmp = $row['id'];
    }
    if ($last_id_tmp == $last_id) {
        $max_id = $db->query("SELECT MAX(id) FROM nv4_wallet_transaction")->fetchColumn();
        if ($last_id_max < $max_id) {
            $last_id = $last_id_max;
        } else {
            echo "Đã chạy hết!!";
            exit(1);
        }
    } else {
        $last_id = $last_id_tmp;
    }

    $db->query("UPDATE " . NV_CONFIG_GLOBALTABLE . " SET config_value = " . $last_id . " WHERE lang = 'vi' AND module = 'wallet' AND config_name = 'last_static_transaction'");
    echo "Chạy đến " . $last_id . "\n";
} catch (PDOException $e) {
    print_r($e);
    die($e->getMessage());
}

echo "\n<br><br>Cập nhật trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
die("Thống kê xong\n");
