<?php
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __FILE__), PATHINFO_DIRNAME) . '/..')));

require NV_CONSOLE_DIR . '/server.php';
require NV_ROOTDIR . '/includes/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

set_time_limit(10000);

$i = 0;
try {
    $_sql = "SELECT id, phone, sub_phone FROM `nv4_vi_crmbidding_leads` WHERE phone_search ='0' and phone != '' ORDER BY id DESC LIMIT 2000";
    $_result = $db->query($_sql);
    while ($tmp = $_result->fetch()) {
        // chuẩn hóa số điện thoại

        $_tmp_phone = $tmp['phone'];
        if (!phonecheck($_tmp_phone)) {
            if (preg_match_all('/(\d{10,11})/i', $_tmp_phone, $m)) {
                $_tmp_phone = $m[0][0];
            }
        }

        $phone = $_tmp_phone;
        $_tmp_phone = str_replace('-', '', change_alias($_tmp_phone));
        if (preg_match('/(\d{9})$/', $_tmp_phone, $m)) {
            $_tmp_phone = intval($m[0]);
        } else {
            $_tmp_phone = 0;
        }

        $db->query("UPDATE `nv4_vi_crmbidding_leads` SET phone=" . $db->quote($phone) . ", phone_search=" . $db->quote($_tmp_phone) . ", elasticsearch = 9 WHERE id='" . $tmp['id'] . "'");
        echo "\n phone: " . $phone . " - phone_search: " . $_tmp_phone;
        $i++;
    }
} catch (PDOException $e) {
    print_r($e);
    die($e->getMessage());
}

try {
    $_sql = "SELECT id, phone, sub_phone FROM `nv4_vi_crmbidding_opportunities` WHERE phone_search ='0' and phone != '' ORDER BY id DESC LIMIT 2000";
    $_result = $db->query($_sql);
    while ($tmp = $_result->fetch()) {
        // chuẩn hóa số điện thoại
        $_tmp_phone = $tmp['phone'];
        if (!phonecheck($_tmp_phone)) {
            if (preg_match_all('/(\d{10,11})/i', $_tmp_phone, $m)) {
                $_tmp_phone = $m[0][0];
            }
        }

        $phone = $_tmp_phone;
        $_tmp_phone = str_replace('-', '', change_alias($_tmp_phone));
        if (preg_match('/(\d{9})$/', $_tmp_phone, $m)) {
            $_tmp_phone = intval($m[0]);
        }

        $db->query("UPDATE `nv4_vi_crmbidding_opportunities` SET phone=" . $db->quote($phone) . ",phone_search=" . $db->quote($_tmp_phone) . ", elasticsearch = 9 WHERE id='" . $tmp['id'] . "'");
        echo "\n phone: " . $phone . " - phone_search: " . $_tmp_phone;
        $i++;
    }
} catch (PDOException $e) {
    print_r($e);
    die($e->getMessage());
}

echo "\n <br><br>Tìm kiếm trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
if ($i == 0) {
    exit(1);
}
die('ok');

