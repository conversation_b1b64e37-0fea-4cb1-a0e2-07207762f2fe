<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2021 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Tue, 14 Dec 2021 01:32:38 GMT
 */
use NukeViet\Api\Api;

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('organizations');
global $array_user_id_users;
// List
$where = [];
$keys_check = [];
$perpage = 20;
$keys_check = [
    'tax',
    'organizationname',
    'shortname',
    'website',
    'primaryphone',
    'secondaryphone',
    'fax',
    'primaryemail',
    'secondaryemail',
    'trading_address'
];
$page = $nv_Request->get_int('page', 'get', 1);
$error = "";
$success = "";
$input = "";
$row = [];
$filter = $nv_Request->get_title('filter', 'get', 'organizationname');
$order = $nv_Request->get_int('order', 'get', -1);
$id_convert = $nv_Request->get_int('id_convert', 'post,get', 0);

// Tìm kiếm
if ($nv_Request->isset_request('search', 'get')) { 
    $input = $nv_Request->get_title('q', 'get', '');
    if (!empty($input)) {
        foreach ($keys_check as $key => $value) {
            $where['OR'][] = [
                'like' => [
                    $keys_check[$key] => '%' . $input . '%',
                ],
            ];
        }
    }
}

if ($nv_Request->isset_request('order', 'get')) { 
    if ($order == 1) {
        $order_by[$filter] = 'ASC';     
    }

    if ($order == 0) {
        $order_by[$filter] = 'DESC';     
    } else {
        $order_by['id'] = 'DESC';
    }
   
} else {
    $order_by['id'] = 'DESC';
}

$order_sort = $order == 1 ? 0 : 1;

$params = [
    'page' => $page,
    'perpage' => $perpage,
    'order' => $order_by
];

// Nếu có điều kiện where thì gán
if (!empty($where)) {
    $params['where'] = $where;
} else {
    $where['OR'][] = [
        '=' => [
            'active' => 1
        ],
    ];

    $where['OR'][] = [
        '=' => [
            'active' => 0,
        ],
    ];
    $params['where'] = $where;
}

// GỌI API
$List = nv_local_api('ListAllOrganizations', $params, $admin_info['username'], $module_name);
$ListAllOrganizations = json_decode($List, true);

$organizationsid = $nv_Request->get_int('id', 'get', 0);

if ($nv_Request->isset_request('deleteOrganzation', 'post')) {
    $id = $nv_Request->get_int('deleteOrganzation', 'post', 0);
    DeleteOgainzation($id);
}

// Thay đổi trạng thái
if ($nv_Request->isset_request('change_status', 'post, get')) {
    $id = $nv_Request->get_int('id', 'post', 0);
    $params['organizationsid'] = $id;
    $row = nv_local_api('GetDetailOrganizations', $params, $admin_info['username'], $module_name);
    $row = json_decode($row, true);
    $row = $row['data'];
    $content = 'NO_' . $id;
    if (isset($row['active'])) {
        $active = ($row['active']) ? 0 : 1;
        $content = 'OK_' . $id;
    }

    $data = [
        'active' => intval($active),
        'organizationname' => $row['organizationname'],
        'primaryphone' => $row['primaryphone'],
        'primaryemail' => $row['primaryemail'],
    ];

    $_params_update = [
        'organizationsid' => $id,
        'admin_id' => $admin_info['userid'],
        'data' => $data,
    ];

    $status_update = nv_local_api('UpdateOrganizations', $_params_update, $admin_info['username'], $module_name);
    $status_update = json_decode($status_update, true);

    include NV_ROOTDIR . '/includes/header.php';
    echo $content;
    include NV_ROOTDIR . '/includes/footer.php';
}

// Xem chi tiết
if ($nv_Request->isset_request('view', 'get')) {
    $id = $nv_Request->get_int('view', 'get', 0);
    $detailOrganizations = [];
    if ($id > 0) {
        $params['organizationsid'] = $id;
        // GỌI API
        $detailOrganizations = nv_local_api('GetDetailOrganizations', $params, $admin_info['username'], $module_name);
        $detailOrganizations = json_decode($detailOrganizations, true);
        if (empty($detailOrganizations['data'])) {
            nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);
        }
    } else {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);
    }

    // Lấy thông tin liên hệ
    $where_contact['AND'][] = [
        '=' => [
            'organizationsid' => $id,
        ],
    ];

    $fields = ['id', 'contactname'];
    $params_contact = [
        'userid' => $admin_info['userid'],
        'page' => $nv_Request->get_int('page', 'post,get', 1),
        'perpage' => 50,
        'where' => $where_contact,
        'fields' => $fields,
    ];

    $data_contact = nv_local_api('ListAllContact', $params_contact, $admin_info['username'], $module_name);
    $data_contact = json_decode($data_contact, true);
    
    $xtpl = new XTemplate('detail_organization.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('MODULE_UPLOAD', $module_upload);
    $xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
    $xtpl->assign('OP', $op);

    foreach ($array_user_id_users as $value) {
        $fullname = nv_show_name_user($value['first_name'], $value['last_name'], $value['userid']);
        $xtpl->assign('OPTION', [
            'key' => $value['userid'],
            'title' => $value['username'] . ' (' . $fullname . ')',
        ]);
        $xtpl->parse('main.select_affilacate_id');
        $xtpl->parse('main.select_caregiver_id');
    }

    $xtpl->parse('main.type_org');

    foreach ($array_groups_leads as $value) {
        $xtpl->assign('OPTION', [
            'key' => $value['id'],
            'title' => $value['title'],
        ]);
        $xtpl->parse('main.select_source_leads');
    }

    if ($detailOrganizations['data']['active'] == 1) {
        $detailOrganizations['data']['textActive'] = $nv_Lang->getModule('danghoatdong');
        $detailOrganizations['data']['class'] = 'label-info';
    } else {
        $detailOrganizations['data']['textActive'] = $nv_Lang->getModule('dinhchinh');
        $detailOrganizations['data']['class'] = 'label-warning';
    }

    foreach ($detailOrganizations['data'] as $key => $value) {
        if ($value == "") {
            $detailOrganizations['data'][$key] = "N/A";
        }
    }

    if (isset($data_contact['data'])) {
        foreach ($data_contact['data'] as $key => $value) {
            $value['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=contact&amp;id_contact=' . $value['id'];
            $xtpl->assign('ROW', $value);
            $xtpl->parse('main.show_contact');
        }
    }

    if ($detailOrganizations['data']['convert_leads'] > 0) {
        $xtpl->assign('LINK_LEADS', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $detailOrganizations['data']['convert_leads']);
        $xtpl->parse('main.link_view_leads');
    } else {
        $xtpl->assign('LINK_CONVERT_LEADS', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=add&id_org=' . $id);
        $xtpl->parse('main.link_convert');
    }

    $xtpl->assign('ROW', $detailOrganizations['data']);
    $xtpl->assign('URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);
    $xtpl->assign('URL_EDIT', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;status=edit&amp;id=' . $id);
    $xtpl->parse('main');
    $contents = $xtpl->text('main');


    $page_title = $nv_Lang->getModule('detail_organizations');

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_admin_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

$status = $nv_Request->get_title('status', 'get', '');

// Thêm tổ chức
if ($nv_Request->isset_request('status', 'get') && ($status == 'add' || $status == 'edit') || $nv_Request->isset_request('id_convert', 'post, get')) {
    // Thêm và Cập nhật Orgainzation
    if ($nv_Request->isset_request('add', 'post')) { 
        ManagerOgainzation();
        $page_title = $nv_Lang->getModule('add_orag');
    }
    if ($status == 'add') {
        $page_title = $nv_Lang->getModule('add_orag');
    } else {
        $page_title = $nv_Lang->getModule('edit_orag');
    }

    // Thực hiện lấy ID khi bấm vào nút sửa
    $detailOrganizations = [];
    if ($nv_Request->isset_request('id', 'get') && $organizationsid != 0) { 
        $params['organizationsid'] = $organizationsid;
        // GỌI API
        $detailOrganizations = nv_local_api('GetDetailOrganizations', $params, $admin_info['username'], $module_name);
        $detailOrganizations = json_decode($detailOrganizations, true);
    } 

    $xtpl = new XTemplate('manage_organization.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
    $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
    $xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
    $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
    $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
    $xtpl->assign('MODULE_NAME', $module_name);
    $xtpl->assign('MODULE_UPLOAD', $module_upload);
    $xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
    $xtpl->assign('OP', $op);
    $xtpl->assign('URL', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);
    $xtpl->assign('URL_ADD', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;status=add');
    // Nếu có dữ liệu thì xuất
    
    // Lấy thông tin Leads
    if (!empty($id_convert)) {
        $arr_info_leads = [];
        $params['leadid'] = $id_convert;
        $arr_info_leads = nv_local_api('GetDetailLeads', $params, $admin_info['username'], $module_name);
        $params = [];
        $arr_info_leads = json_decode($arr_info_leads, true);
        if (isset($arr_info_leads['data'])) {
            $row['organizationname'] = $arr_info_leads['data']['name'];
            $row['primaryphone'] = $arr_info_leads['data']['phone'];
            $row['secondaryphone'] = $arr_info_leads['data']['sub_phone'];
            $row['primaryemail'] = $arr_info_leads['data']['email'];
            $row['secondaryemail'] = $arr_info_leads['data']['sub_email'];
            $row['address'] = $arr_info_leads['data']['address'];
            $row['convert_leads'] = $id_convert;
        }
    }
    if (!empty($detailOrganizations['data'])) {
        $xtpl->assign('ROW', $detailOrganizations['data']);
    } else {
        $xtpl->assign('ROW', $row);
    }

    if (!empty($error)) {
        $xtpl->assign('ERROR', $error);
        $xtpl->parse('main.error');
    }

    if (!empty($success)) {
        $xtpl->assign('SUCCESS', $success);
        $xtpl->parse('main.success');
    }

      // Kiểm tra trạng thái hoạt động của tổ chức
    if (!empty($detailOrganizations['data'])) {
        $array_active[1] = $nv_Lang->getModule('selete_active') ;
        $array_active[0] = $nv_Lang->getModule('selete_noactive');
        // Xuất trạng thái hoạt động
        foreach ($array_active as $key => $title) {
            $xtpl->assign('S_SELECT', [
                'key' => $key,
                'title' => $title,
                'selected' => ($key == $detailOrganizations['data']['active']) ? ' selected="selected"' : ''
            ]);
            $xtpl->parse('main.select.loop');
        }

        $xtpl->parse('main.select');
        // Lấy class col-md-18 do có thêm trạng thái hoạt động!
        $xtpl->parse('main.hide_class');
    } else {
        // Lấy class col-md-24
        $xtpl->parse('main.show_class');
    }

    $xtpl->parse('main');
    $contents = $xtpl->text('main');

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_admin_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

$xtpl = new XTemplate('organizations.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('Q', $input);
$url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
$xtpl->assign('URL', $url);
$xtpl->assign('URL_ADD', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;status=add');

if (!empty($detailOrganizations['data'])) {
   $xtpl->parse('main.show_js_edit');
}

if (!empty($error)) {
    $xtpl->assign('ERROR', $error);
    $xtpl->parse('main.error');
}

if (!empty($success)) {
    $xtpl->assign('SUCCESS', $success);
    $xtpl->parse('main.success');
}

show_list();

$xtpl->parse('main');
$contents = $xtpl->text('main');


include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';

function DeleteOgainzation($id) {
    global $error, $success, $nv_Request, $admin_info, $module_name, $admin_info, $nv_Lang;
    if ($id > 0) {
        $params = [
            'organizationsid' => $id,
            'admin_id' => $admin_info['admin_id']
        ];
        $result_update = nv_local_api('DeleteOrganizations', $params, $admin_info['username'], $module_name);
        $result_update = json_decode($result_update, true);
        if ($result_update['code'] == "0000") {
            $success  = $nv_Lang->getModule('success_delete_organizationname');
        } else {
            $error = $result_update['message'];
        }
    } else {
        $error = $nv_Lang->getModule('error_delete_org');
    }
}

function ManagerOgainzation() {
    global $error, $success, $nv_Request, $admin_info, $module_name, $nv_Cache, $module_name, $row, $op, $admin_info, $nv_Lang;
    $row['tax'] = $nv_Request->get_title('tax', 'post', '');
    $row['shortname'] = $nv_Request->get_title('shortname', 'post', '');
    $row['website'] = $nv_Request->get_title('website', 'post', '');
    $row['secondaryphone'] = $nv_Request->get_title('secondaryphone', 'post', '');
    $row['fax'] = $nv_Request->get_title('fax', 'post', '');
    $row['secondaryemail'] = $nv_Request->get_title('secondaryemail', 'post', '');
    $row['employees'] = $nv_Request->get_int('employees', 'post', 0);
    $row['address'] = $nv_Request->get_title('address', 'post', '');
    $row['trading_address'] = $nv_Request->get_title('trading_address', 'post', '');
    $row['represent_name'] = $nv_Request->get_title('represent_name', 'post', '');
    $row['represent_position'] = $nv_Request->get_title('represent_position', 'post', '');
    $row['represent_address'] = $nv_Request->get_title('represent_address', 'post', '');
    $row['description'] = $nv_Request->get_title('description', 'post', '');
    $row['convert_leads'] = $nv_Request->get_int('convert_leads', 'post', 0);
    $data = [];
    foreach ($row as $key => $value) {
        if ($value != "") {
            $data[$key] = $value;
        }
    }

    $row['primaryemail'] = $nv_Request->get_title('primaryemail', 'post', '');
    $row['primaryphone'] = $nv_Request->get_title('primaryphone', 'post', '');
    $row['organizationname'] = $nv_Request->get_title('organizationname', 'post', '');

    $organizationsid = $nv_Request->get_int('id', 'get', '');

    // Trường hợp update
    if ($organizationsid > 0) {
        $data['active'] = $nv_Request->get_int('active', 'post', 1);
        foreach ($row as $key => $value) {
            if ($value != "") {
                $data[$key] = $value;
            }
        }

        $params = [
            'organizationsid' => $organizationsid,
            'admin_id' => $admin_info['admin_id'],
            'data' => $data,
        ];
      
        $result_update = nv_local_api('UpdateOrganizations', $params, $admin_info['username'], $module_name);
        $result_update = json_decode($result_update, true);
        if ($result_update['code'] == "0000") {
            $success  = sprintf($nv_Lang->getModule('success_update_organizationname'), NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;view=' . $result_update['organizationsid'], $result_update['organizationsid']);
        } else {
            $error = $result_update['message'];
        }
    } else {
        // Trường hớp thêm
        $params = [
            'organizationname' => $row['organizationname'],
            'primaryphone' => $row['primaryphone'],
            'primaryemail' => $row['primaryemail'],
            'admin_id' => $admin_info['admin_id'],
        ];

        if (!empty($data)) {
            $params['otherdata'] = $data;
        }

        $result_add = nv_local_api('CreateOrganizations', $params, $admin_info['username'], $module_name);
        $result_add = json_decode($result_add, true);
        if ($result_add['code'] == "0000") {
            if (!empty($row['convert_leads'])) {
                $params = [];
                $params = [
                    'leadsid' => $row['convert_leads'],
                    'admin_id' => $admin_info['admin_id'],
                    'data' => [
                        'convert_organization' => $result_add['OrganizationsID']
                    ]
                ];
                $result = nv_local_api('UpdateLeads', $params, $admin_info['username'], $module_name);
                
                $params_leads = [
                    'leadid' => $row['convert_leads']
                ];
                $data_leads = nv_local_api('GetDetailLeads', $params_leads, $admin_info['username'], $module_name);
                $data_leads = json_decode($data_leads, true);
                $row_detail_leads = $data_leads['data'];
                $params_opportunities = [
                    'opportunitiesid' => $row_detail_leads['opportunities_id'],
                    'admin_id' => $admin_info['admin_id'],
                    'data' => [
                        'convert_organization' => $result_add['OrganizationsID']
                    ]
                ];
                $result_opportunities = nv_local_api('UpdateOpportunities', $params_opportunities, $admin_info['username'], $module_name);
            }
            $success  = sprintf($nv_Lang->getModule('success_add_organizationname'), NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;view=' . $result_add['OrganizationsID'], $result_add['OrganizationsID']);
        } else {
            $error = $result_add['message'];
        }
    }
    $nv_Cache->delMod($module_name);
}

function show_list() {
    global $xtpl, $module_name, $op, $ListAllOrganizations, $input, $nv_Request, $order_sort, $filter, $order, $nv_Lang;
    $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
    $base_url_old = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;

    // Set url sort
    $url_sort = array(
        'URL_NAME' => $base_url . '&amp;filter=organizationname&amp;order=' . $order_sort,
        'URL_PHONE' => $base_url . '&amp;filter=primaryphone&amp;order=' . $order_sort,
        'URL_EMAIL' => $base_url . '&amp;filter=primaryemail&amp;order=' . $order_sort,
        'URL_STATUS' => $base_url . '&amp;filter=active&amp;order=' . $order_sort,
    );

    $input = $nv_Request->get_title('q', 'get', '');
    if (!empty($input)) {
        // Thêm urlencode('Tìm+kiếm') bởi lý do khi bấm tìm kiếm sau đó bấm sửa sẽ mất get tìm kiếm
        $base_url .= '&amp;q=' .  urlencode($input) . '&amp;search=' . urlencode('Tìm+kiếm');
    }


    if (isset($ListAllOrganizations['data'])) {
        $base_url .= '&amp;filter=' . $filter . '&amp;order=' . $order;
        $generate_page = nv_generate_page($base_url, $ListAllOrganizations['total'], $ListAllOrganizations['perpage'], $ListAllOrganizations['page']);
        if ($ListAllOrganizations['page'] > 1) {
            $base_url .= "&amp;page=" . $ListAllOrganizations['page'];
            $url_sort = array(
                'URL_NAME' => $base_url_old . '&amp;filter=organizationname&amp;order=' . $order_sort . "&amp;page=" . $ListAllOrganizations['page'],
                'URL_PHONE' => $base_url_old . '&amp;filter=primaryphone&amp;order=' . $order_sort . "&amp;page=" . $ListAllOrganizations['page'],
                'URL_EMAIL' => $base_url_old . '&amp;filter=primaryemail&amp;order=' . $order_sort . "&amp;page=" . $ListAllOrganizations['page'],
                'URL_STATUS' => $base_url_old . '&amp;filter=active&amp;order=' . $order_sort . "&amp;page=" . $ListAllOrganizations['page'],
            );
        }

        if (!empty($generate_page)) {
            $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.view_orgainzation.generate_page');
        }
        $number = $ListAllOrganizations['page'] > 1 ? ($ListAllOrganizations['perpage'] * ($ListAllOrganizations['page'] - 1)) + 1 : 1;
        foreach ($ListAllOrganizations['data'] as $key => $value) {
            $value['stt'] = $number++;
            if ($value['active'] == 1) {
                $value['textActive'] = 'Đang hoạt động';
                $value['class'] = 'label-info';
                $value['active'] = 'checked';
            } else {
                $value['textActive'] = 'Đình chỉ';
                $value['class'] = 'label-warning';
                $value['active'] = '';
            }
            $value['link'] = $base_url . '&amp;status=edit&amp;id=' . $value['id'];
            $value['link_view'] = $base_url . '&amp;view=' . $value['id'];
            $xtpl->assign('VIEW', $value);
            $xtpl->parse('main.view_orgainzation.loop');
        }
        $xtpl->assign('NUMBER_RESULT', sprintf($nv_Lang->getModule('result__data'), $ListAllOrganizations['total']));
    } else if ($nv_Request->get_int('id', 'get', 0) == 0){
        $xtpl->assign('NODATA', $nv_Lang->getModule('nodata'));
        $xtpl->parse('main.view_orgainzation.nodata');
    } 

    $xtpl->assign('URL_ORDER', $url_sort);
    $xtpl->assign('FILTER', $filter);
    $xtpl->assign('ORDER_SORT', $order_sort);
    $xtpl->parse('main.view_orgainzation');
}
