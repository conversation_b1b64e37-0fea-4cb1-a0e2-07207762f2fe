<?php
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
use NukeViet\Api\DoApi;
// chỉ tuyenhv sử dụng chức năng này để đỡ các quản trị khác bấm nhầm
if ($admin_info['admin_id'] != 8223) {
    die('Stop!!!');
}

$a = 0;
$caregiver_id = $nv_Request->get_int('caregiver_id', 'post,get', 0);
$sale = $nv_Request->get_textarea('sale', 'post,get', '');
if (!empty($caregiver_id) and !empty($sale)) {
    $sale = str_replace(' ', '', $sale);
    $sale = explode(',', $sale);
    // lấy userid của sale

    global $userid_sale, $sale_tmp, $sale_tmp_vip3, $arr_userid;

    $userid_sale = [];
    foreach ($sale as $value) {
        $value = trim($value);
        $_sql = 'SELECT userid FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (SELECT `admin_id` FROM ' . NV_AUTHORS_GLOBALTABLE . ' ORDER BY lev ASC) AND email =' . $db->quote($value) . '';
        $result = $db->query($_sql);
        while ($_row = $result->fetch()) {
            $userid_sale[$_row['userid']] = $_row['userid'];
        }
    }

    $sale_tmp = $userid_sale;
    $sale_tmp_vip3 = $arr_userid = [];
    if ($caregiver_id > 0 and $sale != '') {
        // lấy toàn bộ đơn hàng thành công của sale bị thu hồi
        // các đơn hàng vip trước tiên
        $a = recovery_admin(1, $caregiver_id);
        // các đơn hàng vieweb
        $a = recovery_admin(0, $caregiver_id);
    }

}

function recovery_admin($vip, $caregiver_id)
{
    global $db, $module_name, $userid_sale, $sale_tmp, $sale_tmp_vip3, $array_user_id_users, $arr_userid;

    $recovery_admin_vip_log = NV_ROOTDIR . '/data/logs/recovery_admin_vip_' . date('Ymd') . '.txt';

    $orderid_loaitru = '20751,22805';

    try {

        $a = 0;
        // Chia theo khách hàng, k chia theo đơn hay gói vip
        if ($vip) {
            // API 1: SELECT order_id FROM ' . NV_PREFIXLANG . '_bidding_customs_log WHERE vip != 99
            $array_order_id = $where_order_id = $params_order_id = [];
            $where_order_id['AND'][] = [
                '!=' => [
                    'vip' => 99
                ]
            ];
            $params_order_id = [
                'where' => $where_order_id,
                'array_select' => ['order_id']
            ];
            $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
            $api_dtinfo->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListBiddingCustomsLog')
                ->setData($params_order_id);
            $result = $api_dtinfo->execute();
            $error = $api_dtinfo->getError();
            if (empty($error) and !empty($result['data'])) {
                foreach ($result['data'] as $key => $_order) {
                    $array_order_id[] = $_order['order_id'];
                }
            }
            // API 2: 'SELECT userid FROM ' . NV_PREFIXLANG . '_bidding_orders WHERE caregiver_id = ' . $caregiver_id . ' AND status=4 AND total > 0 AND id IN (SELECT order_id FROM ' . NV_PREFIXLANG . '_bidding_customs_log WHERE vip != 99)
            //AND id NOT IN (' . $orderid_loaitru . ') GROUP BY userid ORDER BY userid ASC';
            $array_userid = $where_order = $params_order = [];
            $where_order['AND'][] = [
                '=' => [
                    'caregiver_id' => $caregiver_id
                ]
            ];
            $where_order['AND'][] = [
                '=' => [
                    'status' => 4
                ]
            ];
            $where_order['AND'][] = [
                '>' => [
                    'total' => 0
                ]
            ];
            $where_order['AND'][] = [
                'IN' => [
                    'id' => '(' . implode(',', $array_order_id) . ')'
                ]
            ];
            $where_order['AND'][] = [
                'NOT IN' => [
                    'id' => '(' . $orderid_loaitru . ')'
                ]
            ];
            $order = [
                'userid' => 'ASC'
            ];
            $params_order_id = [
                'where' => $where_order,
                'array_select' => ['userid'],
                'group' => 'userid',
                'order' => $order
            ];
            $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
            $api_dtinfo->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListBiddingOrder')
                ->setData($params_order_id);
            $result = $api_dtinfo->execute();
            $error = $api_dtinfo->getError();
            if (empty($error) and !empty($result['data'])) {
                foreach ($result['data'] as $key => $_order) {
                    $array_userid[$_order['userid']] = $_order['userid'];
                }
            }
            // $_sql = 'SELECT userid FROM ' . NV_PREFIXLANG . '_bidding_orders WHERE caregiver_id = ' . $caregiver_id . ' AND status=4 AND total > 0 AND id IN (SELECT order_id FROM ' . NV_PREFIXLANG . '_bidding_customs_log WHERE vip != 99) AND id NOT IN (' . $orderid_loaitru . ') GROUP BY userid ORDER BY userid ASC';
            file_put_contents($recovery_admin_vip_log, "\n\n ---------- \n Theo gói VIP", FILE_APPEND);
        } else {
            // API 1:SELECT order_id FROM ' . NV_PREFIXLANG . '_bidding_customs_log WHERE vip = 99
            $array_order_id = $where_order_id = $params_order_id = [];
            $where_order_id['AND'][] = [
                '=' => [
                    'vip' => 99
                ]
            ];
            $params_order_id = [
                'where' => $where_order_id,
                'array_select' => ['order_id']
            ];
            $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
            $api_dtinfo->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListBiddingCustomsLog')
                ->setData($params_order_id);
            $result = $api_dtinfo->execute();
            $error = $api_dtinfo->getError();
            if (empty($error) and !empty($result['data'])) {
                foreach ($result['data'] as $key => $_order) {
                    $array_order_id[] = $_order['order_id'];
                }
            }
            // API 2:SELECT userid FROM ' . NV_PREFIXLANG . '_bidding_orders WHERE caregiver_id = ' . $caregiver_id . ' AND
            // status=4 AND total > 0
            //AND id IN (SELECT order_id FROM ' . NV_PREFIXLANG . '_bidding_customs_log WHERE vip = 99)
            // AND id NOT IN (' . $orderid_loaitru . ') AND userid NOT IN (' . implode(',', $arr_userid) . ')
            // GROUP BY userid ORDER BY userid ASC';
            $array_userid = $where_order = $params_order = [];
            $where_order['AND'][] = [
                '=' => [
                    'caregiver_id' => $caregiver_id
                ]
            ];
            $where_order['AND'][] = [
                '=' => [
                    'status' => 4
                ]
            ];
            $where_order['AND'][] = [
                '>' => [
                    'total' => 0
                ]
            ];
            $where_order['AND'][] = [
                'IN' => [
                    'id' => '(' . implode(',', $array_order_id) . ')'
                ]
            ];
            $where_order['AND'][] = [
                'NOT IN' => [
                    'id' => '(' . $orderid_loaitru . ')'
                ]
            ];
            $order = [
                'userid' => 'ASC'
            ];
            $params_order_id = [
                'where' => $where_order,
                'array_select' => ['userid'],
                'group' => 'userid',
                'order' => $order
            ];
            $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
            $api_dtinfo->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListBiddingOrder')
                ->setData($params_order_id);
            $result = $api_dtinfo->execute();
            $error = $api_dtinfo->getError();
            if (empty($error) and !empty($result['data'])) {
                foreach ($result['data'] as $key => $_order) {
                    $array_userid[$_order['userid']] = $_order['userid'];
                }
            }
            file_put_contents($recovery_admin_vip_log, "\n\n ---------- \n Theo gói VIEWEB", FILE_APPEND);
        }
        $st = 0;
        // while ($_userid = $result_user->fetch()) {
        foreach ($array_userid as $key => $_userid) {
            $arr_userid[$_userid['userid']] = $_userid['userid'];
            file_put_contents($recovery_admin_vip_log, "\n - Khách hàng:" . $_userid['userid'] . "  ", FILE_APPEND);

            // lấy sale dc chia trong vòng này
            $sales_id = array_key_first($sale_tmp);
            file_put_contents($recovery_admin_vip_log, "\n + Chia cho sale :" . $sales_id . "(" . $array_user_id_users[$sales_id]['username'] . ")  ", FILE_APPEND);
            $log_data = [
                [
                    'Thu hồi khách của nhân viên nghỉ việc',
                    $array_user_id_users[$caregiver_id]['username']
                ],
                [
                    'Chia tự động cho sale',
                    $array_user_id_users[$sales_id]['username']
                ]
            ];

            // lấy các đơn hàng của khách hàng
            // $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_bidding_orders
            // WHERE
            // userid = ' . $_userid['userid'] . ' AND
            // status=4 AND
            // total > 0 AND
            // id NOT IN (' . $orderid_loaitru . ') AND
            // caregiver_id = ' . $caregiver_id . '
            // ORDER BY id ASC';
            // $result = $db->query($_sql);
            $where_order_custom['AND'][] = [
                '=' => [
                    'userid' => $_userid['userid']
                ]
            ];
            $where_order_custom['AND'][] = [
                '=' => [
                    'caregiver_id' => $caregiver_id
                ]
            ];
            $where_order_custom['AND'][] = [
                '=' => [
                    'status' => 4
                ]
            ];
            $where_order_custom['AND'][] = [
                '>' => [
                    'total' => 0
                ]
            ];
            $where_order_custom['AND'][] = [
                'NOT IN' => [
                    'id' => '(' . $orderid_loaitru . ')'
                ]
            ];
            $order = [
                'id' => 'ASC'
            ];
            $params_order_id = [
                'where' => $where_order_custom,
                'array_select' => ['userid'],
                'group' => 'userid',
                'order' => $order
            ];
            $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
            $api_dtinfo->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListBiddingOrder')
                ->setData($params_order_id);
            $result = $api_dtinfo->execute();
            $error = $api_dtinfo->getError();

            $i = 0;
            if (empty($error) and !empty($result['data'])) {
                foreach ($result['data'] as $key => $_row) {
                // while ($_row = $result->fetch()) {
                    file_put_contents($recovery_admin_vip_log, "\n + Đơn hàng:" . $_row['id'] . "  ", FILE_APPEND);
                    // kiểm tra các gói vip trong đơn hàng đó
                    // $_customs_log = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_customs_log
                    // WHERE order_id = ' . $_row['id'] . ' ORDER BY vip ASC');
                    $where_order_id = $params_order_id = $order = $result_log = [];
                    $where_order_id['AND'][] = [
                        '=' => [
                            'order_id' => $_row['id']
                        ]
                    ];
                    $order['vip'] = 'ASC';
                    $params_order_id = [
                        'where' => $where_order_id,
                        'order' => $order,
                    ];
                    $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
                    $api_dtinfo->setModule('bidding')
                        ->setLang('vi')
                        ->setAction('ListBiddingCustomsLog')
                        ->setData($params_order_id);
                    $result_log = $api_dtinfo->execute();
                    $error = $api_dtinfo->getError();
                    if (empty($error) and !empty($result_log['data'])) {
                        foreach ($result_log['data'] as $key => $customs_log) {
                        // while ($customs_log = $_customs_log->fetch()) {
                            // kiểm tra gói vip tồn tại hay k
                            // $customs = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_customs WHERE user_id = ' . $customs_log['user_id'] . ' AND vip = ' . $customs_log['vip'] . '')->fetch();
                            // }
                            $where_order = $params_order = [];
                            $where_order['AND'][] = [
                                '=' => [
                                    'vip' => $customs_log['vip']
                                ]
                            ];
                            $where_order['AND'][] = [
                                '=' => [
                                    'user_id' => $customs_log['user_id']
                                ]
                            ];
                            $params_order = [
                                'page' => 1,
                                'per_page' => 1,
                                'where' => $where_order
                            ];

                            $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
                            $api_dtinfo->setModule('bidding')
                                ->setLang('vi')
                                ->setAction('ListBiddingCustoms')
                                ->setData($params_order);
                            $result_customs = $api_dtinfo->execute();
                            $customs = [];
                            if (!empty($result_customs['data'])) {
                                $customs = $result_customs['data'][0];
                                // $customs = array_values($result_customs['data'])[0];
                            }
                            if (!empty($customs)) {
                                // đổi người chăm sóc ở khách vip
                                // $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_bidding_customs SET admin_id = ' . $sales_id . ', updatetime = ' . NV_CURRENTTIME . ' WHERE id = ' . $customs['id']);
                                // $exc = $stmt->execute();
                                $params_update = [];
                                $data_update = [
                                    'admin_id' => $sales_id,
                                    'updatetime' => NV_CURRENTTIME
                                ];
                                $params_update = [
                                    'biddingcustom_id' => $customs['id'],
                                    'where' => $where_order,
                                    'data' => $data_update
                                ];

                                $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                                $api_dtinfo->setModule('bidding')
                                    ->setLang('vi')
                                    ->setAction('UpdateBiddingCustom')
                                    ->setData($params_update);
                                $exc = $api_dtinfo->execute();
                                if ($exc) {
                                    $params_log = [];
                                    $params_log = [
                                        'userid' => 0,
                                        'log_area' => 1,
                                        'log_key' => 'LOG_ADMIN_UPDATE_VIP_INFO',
                                        'log_time' => NV_CURRENTTIME,
                                        'log_data' => $log_data,
                                        'uvip_id' => $customs['user_id'],
                                        'vip_id' => $customs['vip'],
                                        'type_export' => $customs['type_export']
                                    ];
                                    $api_dtinfo = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
                                    $api_dtinfo->setModule('bidding')
                                        ->setLang('vi')
                                        ->setAction('UpdateBiddingCustom')
                                        ->setData($params_log);
                                    $api_dtinfo->execute();

                                    nv_insert_logs(NV_LANG_DATA, 'bidding', 'Đổi người phụ trách khách vip', 'ID khách vip: ' . $customs['id'] . ', Admin old ' . $array_user_id_users[$caregiver_id]['username'] . ', Admin new: ' . $array_user_id_users[$sales_id]['username'], 0);
                                }
                            }
                            ++$i;
                        }
                    }

                    // kiểm tra cơ hội và đổi người chăm sóc ở cơ hội
                    $_opportunities = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_crmbidding_opportunities WHERE FIND_IN_SET( ' . $db->quote($_row['id']) . ', orderid )');
                    while ($opportunities = $_opportunities->fetch()) {
                        $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_crmbidding_opportunities SET caregiver_id = ' . $sales_id . ', updatetime = ' . NV_CURRENTTIME . ' WHERE id = ' . $opportunities['id']);
                        $exc = $stmt->execute();
                        if ($exc) {
                            // ghi log
                            $sql = "INSERT INTO " . NV_PREFIXLANG . "_crmbidding_alllogs (userid, log_area, log_key, log_time, log_data, oppotunities_id) VALUES (0, 1, 'LOG_SYS_UPDATE_OPPOTUNITIES_INFO', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $opportunities['id'] . ")";
                            $db->query($sql);
                            nv_insert_logs(NV_LANG_DATA, $module_name, 'Edit Oppotunities Info', 'ID: ' . $opportunities['id'], 0);

                            $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_crmbidding_leads SET caregiver_id = ' . $sales_id . ', updatetime = ' . NV_CURRENTTIME . ' WHERE id = ' . $opportunities['leadsid']);
                            $exc = $stmt->execute();
                            if ($exc) {
                                $sql = "INSERT INTO " . NV_PREFIXLANG . "_crmbidding_alllogs (userid, log_area, log_key, log_time, log_data, leads_id) VALUES (0, 1, 'LOG_SYS_UPDATE_LEADS_INFO', " . NV_CURRENTTIME . ", " . $db->quote(json_encode($log_data)) . ", " . $opportunities['leadsid'] . ")";
                                $db->query($sql);
                                nv_insert_logs(NV_LANG_DATA, $module_name, 'Edit leads Info', 'ID: ' . $opportunities['leadsid'], 0);
                            }
                        }
                    }
                }
            }
            // nếu dc chia 3 gói của 1 khách hàng thì
            if ($i >= 3) {
                file_put_contents($recovery_admin_vip_log, "\n + Sale dc chia 3 gói", FILE_APPEND);
                $sale_tmp_vip3[$sales_id] = $sales_id;
            }

            // sau khi chia xong, thì xóa $sales_id khỏi ds chia để chạy tiếp
            unset($sale_tmp[$sales_id]);
            file_put_contents($recovery_admin_vip_log, "\n + Chia xong 1 đơn\n", FILE_APPEND);

            // nếu chạy hết, thì bắt đầu vòng mới
            if (empty($sale_tmp)) {
                file_put_contents($recovery_admin_vip_log, "\n - kết thúc vòng chia -------------------\n\n", FILE_APPEND);
                $sale_tmp = $userid_sale;
                // nếu bắt đầu vòng mới thì bỏ các sale đã dc chia khách hàng có 3 gói vip
                if (!empty($sale_tmp_vip3)) {
                    foreach ($sale_tmp_vip3 as $value) {
                        unset($sale_tmp[$value]);
                    }
                    $sale_tmp_vip3 = [];
                }
            }
            ++$a;
        }
    } catch (PDOException $e) {
        trigger_error($e);
        die($nv_Lang->getModule('error_recovery'));
    }
    return $a;
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);

foreach ($array_user_id_users as $value) {
    // $value['selected'] = $value['userid'] == $caregiver_id ? 'selected="selected"' : '';
    $value['title'] = nv_show_name_user($value['first_name'], $value['last_name'], $value['username']);
    $xtpl->assign('CAREGIVER_ID', $value);
    $xtpl->parse('main.caregiver_id');
}

if ($a > 0) {
    $xtpl->parse('main.sussess');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('recovery_admin_vip');
include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';


