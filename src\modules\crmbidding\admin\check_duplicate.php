<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

use NukeViet\Api\DoApi;

$user_extfield_keys = [
    'marketing_types',
    'custom_types'
];

$error = array();
$id = $nv_Request->get_int('id', 'post,get', 0);
$type = $nv_Request->get_int('type', 'post,get', 1);

session_write_close(); // Đến đây Session vẫn có sẵn để đọc, nhưng không thể ghi thêm được
$file_log = NV_ROOTDIR . '/data/logs/unsubscribe/' . $admin_info['username'] . '.log';
file_put_contents($file_log, date('H:i:s') . "\t" . $op . "\t" . $id . "\t" . $type . "\n", FILE_APPEND);

if ($type == 1) {
    $params_leads = [
        'leadid' => $id
    ];
    $data_leads = nv_local_api('GetDetailLeads', $params_leads, $admin_info['username'], $module_name);

    $data = json_decode($data_leads, true);
    $row = $data['data'];
    if (empty($row)) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=leads&showheader=' . $showheader);
    }
} elseif ($type == 2) {
    $params = [
        'opportunitiesid' => $id
    ];
    $ListAllOpportunities = nv_local_api('GetDetailOpportunities', $params, $admin_info['username'], $module_name);
    $ListAllOpportunities = json_decode($ListAllOpportunities, true);
    $row = $ListAllOpportunities['data'];
    if (empty($row)) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=opportunities&showheader=' . $showheader);
    }
}

// Lấy thông tin mở rộng của Lead nếu là thành viên
$row['user_fields'] = [];
$row['user'] = [];
if (!empty($row['user_id'])) {
    $ext_uinfo = $db->query('SELECT * FROM ' . NV_USERS_GLOBALTABLE . '_info WHERE userid = ' . $row['user_id'])->fetch();
    $row['user'] = $db->query('SELECT userid, username FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid = ' . $row['user_id'])->fetch();
} else {
    $ext_uinfo = [];
}
foreach ($user_extfield_keys as $key) {
    $row['user_fields'][$key] = !empty($ext_uinfo[$key]) ? array_map('intval', explode(',', $ext_uinfo[$key])) : [];
    $row[$key] = $row['user_fields'][$key];
}
if (empty($row['user'])) {
    $row['user'] = [
        'userid' => 0,
        'username' => ''
    ];
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('FORM_ACTION', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&amp;id=' . $id . '&type=' . $type);

// chuẩn hóa số điện thoại
$_tmp_phone = $row['phone'];
if (preg_match('/(\d{9})$/', $row['phone'], $m)) {
    $_tmp_phone = $m[0];
}
$_tmp_phone = preg_replace('/[^0-9]/', '', $_tmp_phone);
$_tmp_phone = (int) $_tmp_phone;

$_tmp_sub_phone = $row['sub_phone'];
if (!empty($_tmp_sub_phone)) {
    $_tmp_sub_phone = explode(',', $_tmp_sub_phone);
    foreach ($_tmp_sub_phone as $key => $value) {
        if (preg_match('/(\d{9})$/', $value, $m)) {
            $_tmp_sub_phone[$key] = $m[0];
        }
        $_tmp_sub_phone[$key] = preg_replace('/[^0-9]/', '', $_tmp_sub_phone[$key]);
        $_tmp_sub_phone[$key] = (int) $_tmp_sub_phone[$key];
    }
}
$array_telepro = $array_business = $array_user_phone = $array_lead_phone = $array_oppotunities = $array_customs_log = $array_bank = $array_bni = $mobiphone_record = $tawkto_record = $messager_record = $to_chuc_record = $laboratory_record = $canhan_record = $teacher_record = $array_dkkd = $company_related = $voicecloud = [];

// xử lý lần lượt dữ liệu
$next = $nv_Request->get_int('next', 'post,get', 0);

// trả về null
$respon = [
    'next' => $next,
    'data' => []
];

// các user mà sale làm trưởng nhóm
$caregiver_id_leads = array();
$sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE userid = ' . $admin_info['userid'];
$result = $db->query($sql);
$admin_config = [];
$is_leader = 0;
while ($row_groups_users = $result->fetch()) {
    $admin_config = json_decode($row_groups_users['config'], true);
    if ($row_groups_users['is_leader'] == 1) {
        $is_leader = 1;
        $_sql = 'SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_groups_users WHERE group_id = ' . $row_groups_users['group_id'] . ' AND userid != ' . $admin_info['userid'];
        $_result = $db->query($_sql);
        while ($_row_groups_users = $_result->fetch()) {
            $caregiver_id_leads[$_row_groups_users['userid']] = $_row_groups_users['userid'];
        }
    }
}
$caregiver_id_leads[$admin_info['userid']] = $admin_info['userid'];

if (!empty($row['phone']) or !empty($row['email'])) {
    if ($next == 1) {
        $next = 2;
        // các dữ liệu có sẵn trên db id.dauthau.net

        // telepro job active
        $_array_telepro_jobs_active = [];
        $_telepro_jobs_active = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_' . $module_data . '_telepro_jobs WHERE active = 1');
        while ($temp = $_telepro_jobs_active->fetch()) {
            $_array_telepro_jobs_active[] = $temp['id'];
        }

        // telepro
        $sql = "SELECT id,name,phone,email,timecall,job_id FROM " . NV_PREFIXLANG . "_" . $module_data . "_telepro";
        $where = [];
        if (!empty($row['email'])) {
            $where[] = "email = " . $db->quote($row['email']) . "";
        }
        if (!empty($row['sub_email'])) {
            $where[] = "email IN (" . $db->quote($row['sub_email']) . ") ";
        }
        if (!empty($row['phone'])) {
            $where[] = "phone LIKE " . $db->quote('%' . $_tmp_phone . '');
        }
        if (!empty($row['sub_phone'])) {
            foreach ($_tmp_sub_phone as $key => $value) {
                $where[] = "phone LIKE (" . $db->quote('%' . $value . '') . ") ";
            }
        }

        if ($type == 1) {
            $where_id = "";
            if (!empty($row['teleproid'])) {
                $where_id = " AND id != " . $row['teleproid'];
            }

            $sql .= " WHERE " . implode(' OR ', $where) . $where_id;
        } else {
            $sql .= " WHERE " . implode(' OR ', $where);
        }
        // Tìm các job đang active
        if (!empty($_array_telepro_jobs_active)) {
            $sql .= " AND job_id IN ('" . implode("','", $_array_telepro_jobs_active) . "') ORDER BY id DESC";
            $result = $db->query($sql);
            while ($_row = $result->fetch()) {
                $array_telepro[$_row['id']] = $_row;
            }
        }
        if (sizeof($array_telepro) > 0) {
            $xtpl->assign('NUM_TELEPRO', sprintf($nv_Lang->getModule('duplicate_telepro'), sizeof($array_telepro)));
            foreach ($array_telepro as $telepro) {
                $telepro['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=crmbidding&amp;' . NV_OP_VARIABLE . '=telepro&amp;showheader=0&amp;id=' . $telepro['id'];
                if (!empty($row['phone']) and !empty($telepro['phone']) and (substr($telepro['phone'], -9) == substr($row['phone'], -9))) {
                    $telepro['phone'] = '<span class="red">' . $telepro['phone'] . '</span>';
                }
                if (!empty($row['sub_phone']) and !empty($telepro['phone']) and strpos($row['sub_phone'], $telepro['phone']) !== false) {
                    $telepro['phone'] = '<span class="red">' . $telepro['phone'] . '</span>';
                }
                if (!empty($row['email']) and !empty($telepro['email']) and $telepro['email'] == $row['email']) {
                    $telepro['email'] = '<span class="red">' . $telepro['email'] . '</span>';
                }
                if (!empty($row['sub_email']) and !empty($telepro['email']) and strpos($row['sub_email'], $telepro['email']) !== false) {
                    $telepro['email'] = '<span class="red">' . $telepro['email'] . '</span>';
                }
                $telepro['timecall'] = nv_date('H:i d/m/Y', $telepro['timecall']);

                $telepro['job_title'] = '';
                if (!empty($telepro['job_id']) && in_array($telepro['job_id'], array_keys($_array_telepro_jobs))) {
                    $telepro['job_title'] = $_array_telepro_jobs[$telepro['job_id']]['title'];
                }

                $xtpl->assign('TELEPRO', $telepro);
                $xtpl->parse('main.telepro.loop');
            }

            $xtpl->parse('main.telepro');
        }

        // thành viên
        $sql = "SELECT userid,email,regdate,username,first_name,last_name,md5username FROM " . NV_USERS_GLOBALTABLE . " WHERE userid!=" . $row['user_id'];
        $where = [];
        $where_id = '';
        if (!empty($row['email'])) {
            $where[] = "email = " . $db->quote($row['email']) . "";
        }
        if (!empty($row['sub_email'])) {
            $where[] = "email IN (" . $db->quote($row['sub_email']) . ")";
        }

        if (!empty($row['user_id'])) {
            $where_id = " AND userid !=" . $row['user_id'];
        }

        $sql_info = "SELECT userid, phone, mst FROM " . NV_USERS_GLOBALTABLE . "_info ";

        $_where = [];
        if (!empty($row['tax'])) {
            $_where[] = "mst LIKE " . $db->quote('%' . $row['tax'] . '%') . "";
        }
        if (!empty($row['phone'])) {
            $_where[] = "phone LIKE " . $db->quote('%' . $_tmp_phone . '');
        }
        if (!empty($row['sub_phone'])) {
            foreach ($_tmp_sub_phone as $key => $value) {
                $_where[] = "phone LIKE (" . $db->quote('%' . $value . '') . ")";
            }
        }
        if (!empty($_where)) {
            $_arr_user_info = [];
            $sql_info .= " WHERE (" . implode(' OR ', $_where) . ")" . $where_id . " ORDER BY userid DESC";
            $result_info = $db->query($sql_info);
            while ($_row = $result_info->fetch()) {
                $_arr_user_info[$_row['userid']] = $_row['userid'];
            }

            if (!empty($_arr_user_info)) {
                $where[] = "userid IN (" . implode(',', $_arr_user_info) . ")";
            }
        }
        if (!empty($where)) {
            $sql .= " AND (" . implode(' OR ', $where) . ")";
            $sql .= "  ORDER BY userid DESC";
            $result = $db->query($sql);
            $array_user_not_info = [];
            while ($_row = $result->fetch()) {
                $_row['order'] = [];
                $array_user_phone[$_row['userid']] = $_row;
            }

            if (!empty($array_user_phone)) {
                $query_user_not_info = $db->query("SELECT userid, phone, mst FROM " . NV_USERS_GLOBALTABLE . "_info WHERE userid IN (" . implode(',', array_keys($array_user_phone)) . ")");
                while ($_row = $query_user_not_info->fetch()) {
                    if (isset($array_user_phone[$_row['userid']])) {
                        $array_user_phone[$_row['userid']]['phone'] = $_row['phone'];
                        $array_user_phone[$_row['userid']]['mst'] = $_row['mst'];
                    }
                }
            }
        }

        if (!empty($array_user_phone)) {
            $where = [];
            $where['AND'][] = [
                'IN' => [
                    'user_id' => '(' . implode(',', array_keys($array_user_phone)) . ')'
                ]
            ];
            $params = [
                'where' => $where
            ];
            $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
            $api_dtinfo->setModule('bidding')
                ->setLang('vi')
                ->setAction('ListBiddingCustomsLog')
                ->setData($params);
            $result = $api_dtinfo->execute();
            $error = $api_dtinfo->getError();
            if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
                foreach ($result['data'] as $_order) {
                    if (!empty($array_user_phone[$_order['user_id']])) {
                        $array_user_phone[$_order['user_id']]['order'][$_order['id']] = $_order;
                    }
                }
            }
        }

        // thành viên
        if (sizeof($array_user_phone) > 0) {
            $xtpl->assign('NUM_USERS_PHONE', sprintf($nv_Lang->getModule('duplicate_users'), sizeof($array_user_phone)));
            foreach ($array_user_phone as $user_email) {
                $user_email['title'] = nv_show_name_user($user_email['first_name'], $user_email['last_name'], $user_email['username']);
                $user_email['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=users&amp;' . NV_OP_VARIABLE . '=memberlist/' . change_alias($user_email['username']) . '-' . $user_email['md5username'];
                $user_email['check_vip'] = '';
                if (isset($user_email['order']) and !empty($user_email['order'])) {
                    foreach ($user_email['order'] as $order) {
                        $user_email['check_vip'] = '; Đã tạo đơn hàng VIP ' . $order['vip'] . ', Chờ thanh toán.';
                        if ($order['status'] == 1) {
                            $user_email['check_vip'] = '; Đã đăng ký VIP ' . $order['vip'];
                        }
                    }
                }
                if (!empty($row['phone']) and !empty($user_email['phone']) and (substr($user_email['phone'], -9) == substr($row['phone'], -9))) {
                    $user_email['phone'] = '<span class="red">' . $user_email['phone'] . '</span>';
                }
                if (!empty($row['sub_phone']) and !empty($user_email['phone']) and strpos($row['sub_phone'], $user_email['phone']) !== false) {
                    $user_email['phone'] = '<span class="red">' . $user_email['phone'] . '</span>';
                }
                if (!empty($row['email']) and !empty($user_email['email']) and $user_email['email'] == $row['email']) {
                    $user_email['email'] = '<span class="red">' . $user_email['email'] . '</span>';
                }
                if (!empty($row['sub_email']) and !empty($user_email['email']) and strpos($row['sub_email'], $user_email['email']) !== false) {
                    $user_email['email'] = '<span class="red">' . $user_email['email'] . '</span>';
                }
                if (!empty($row['tax']) and !empty($user_email['mst']) and $user_email['mst'] == $row['tax']) {
                    $user_email['mst'] = '<span class="red">' . $user_email['mst'] . '</span>';
                }
                $user_email['regdate'] = nv_date('H:i d/m/Y', $user_email['regdate']);
                $xtpl->assign('USERS_PHONE', $user_email);
                $xtpl->parse('main.user_phone.loop');
            }
            $xtpl->parse('main.user_phone');
        }

        // leads
        $where = [];
        if ($type == 1) {
            $where['AND'][] = [
                '!=' => [
                    'id' => $row['id']
                ]
            ];
        } else {
            $where['AND'][] = [
                '!=' => [
                    'id' => $row['leadsid']
                ]
            ];
        }

        $where['AND'][] = [
            '=' => [
                'active' => 1
            ]
        ];

        if (!empty($row['email'])) {
            $where['OR'][] = [
                '=' => [
                    'email' => $row['email']
                ]
            ];

            $where['OR'][] = [
                'FIND_IN_SET' => [
                    'sub_email' => $row['email']
                ]
            ];
        }
        if (!empty($row['sub_email'])) {
            $_sub_email = explode(',', $row['sub_email']);
            foreach ($_sub_email as $key => $value) {
                $where['OR'][] = [
                    '=' => [
                        'email' => $value
                    ]
                ];

                $where['OR'][] = [
                    'FIND_IN_SET' => [
                        'sub_email' => $value
                    ]
                ];
            }
        }

        if (!empty($row['phone'])) {
            $where['OR'][] = [
                '=' => [
                    'phone_search' => $_tmp_phone
                ]
            ];

            $where['OR'][] = [
                'FIND_IN_SET' => [
                    'sub_phone_search' => $_tmp_phone
                ]
            ];
        }
        if (!empty($row['sub_phone'])) {
            foreach ($_tmp_sub_phone as $key => $value) {
                $where['OR'][] = [
                    '=' => [
                        'phone_search' => $value
                    ]
                ];

                $where['OR'][] = [
                    'FIND_IN_SET' => [
                        'sub_phone_search' => $value
                    ]
                ];
            }
        }

        if (!empty($row['tax'])) {
            $where['OR'][] = [
                '=' => [
                    'tax' => $row['tax']
                ]
            ];
        }

        $params_leads = [];
        $params_leads = [
            'userid' => $admin_info['userid'],
            'where' => $where
        ];

        // GỌI API
        $List = nv_local_api('ListAllLeads', $params_leads, $admin_info['username'], $module_name);
        $ListAllLeads = json_decode($List, true);
        if (isset($ListAllLeads['data'])) {
            foreach ($ListAllLeads['data'] as $key => $value) {
                $array_lead_phone[$value['id']] = $value;
            }
        }

        if (!empty($array_lead_phone)) {
            $params = [];
            $where['AND'][] = [
                'IN' => [
                    'leadsid' => implode(',', array_keys($array_lead_phone))
                ]
            ];

            $params = [
                'where' => $where
            ];

            $opportunities = nv_local_api('ListAllOpportunities', $params, $admin_info['username'], $module_name);
            $check_opportunities = json_decode($opportunities, true);
            if (isset($check_opportunities['data'])) {
                foreach ($check_opportunities['data'] as $key => $value) {
                    $array_lead_phone[$value['leadsid']]['opportunities'] = $value;
                    if ($value['orderid'] != '') {

                        $where = [];
                        $where['AND'][] = [
                            'IN' => [
                                'order_id' => '(' . $value['orderid'] . ')'
                            ]
                        ];
                        $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
                        $api_dtinfo->setModule('bidding')
                            ->setLang('vi')
                            ->setAction('ListBiddingCustomsLog')
                            ->setData([
                            'where' => $where
                        ]);
                        $result = $api_dtinfo->execute();
                        $error = $api_dtinfo->getError();
                        if (empty($error) and $result['status'] == 'success') {
                            foreach ($result['data'] as $_order) {
                                $array_lead_phone[$value['leadsid']]['order'][$_order['id']] = $_order;
                            }
                        }
                    }
                }
            }
        }

        // leads
        if (sizeof($array_lead_phone) > 0) {
            $xtpl->assign('NUM_LEADS_PHONE', sprintf($nv_Lang->getModule('duplicate_leads_phone'), sizeof($array_lead_phone)));
            foreach ($array_lead_phone as $lead_phone) {
                $lead_phone['caregiverid'] = $lead_phone['caregiver_id'];
                $lead_phone['caregiver_id'] = isset($array_user_id_users[$lead_phone['caregiver_id']]['username']) ? nv_show_name_user($array_user_id_users[$lead_phone['caregiver_id']]['first_name'], $array_user_id_users[$lead_phone['caregiver_id']]['last_name'], $array_user_id_users[$lead_phone['caregiver_id']]['username']) : $nv_Lang->getModule('not_caregiver');
                $lead_phone['source_leads_title'] = isset($array_groups_leads[$lead_phone['source_leads']]['title']) ? $array_groups_leads[$lead_phone['source_leads']]['title'] : '';
                $lead_phone['status_id'] = $lead_phone['status'];
                $lead_phone['status'] = $array_status[$lead_phone['status']];

                $lead_phone['check_vip'] = '';
                if (isset($lead_phone['order']) and !empty($lead_phone['order'])) {
                    foreach ($lead_phone['order'] as $order) {
                        $lead_phone['check_vip'] = '; Đã tạo đơn hàng VIP ' . $order['vip'] . ', Chờ thanh toán.';
                        if ($order['status'] == 1) {
                            $lead_phone['check_vip'] = '; Đã đăng ký VIP ' . $order['vip'];
                        }
                    }
                }
                $lead_phone['duplicate'] = [];
                $is_open_lead = 0;
                if (!empty($row['phone']) and !empty($lead_phone['phone']) and (substr($lead_phone['phone'], -9) == substr($row['phone'], -9))) {
                    $lead_phone['duplicate'][] = '<span class="red">' . $lead_phone['phone'] . '</span>';
                    $is_open_lead = 1;
                }
                if (!empty($row['sub_phone']) and !empty($lead_phone['phone']) and strpos($row['sub_phone'], $lead_phone['phone']) !== false) {
                    $lead_phone['duplicate'][] = '<span class="red">' . $lead_phone['phone'] . '</span>';
                    $is_open_lead = 1;
                }
                if (!empty($row['sub_phone']) and !empty($lead_phone['sub_phone'])) {
                    $i = array_intersect(explode(',', $row['sub_phone']), explode(',', $lead_phone['sub_phone']));
                    foreach ($i as $p) {
                        $lead_phone['duplicate'][] = '<span class="red">' . $p . '</span>';
                        $is_open_lead = 1;
                    }
                }

                if (!empty($row['phone']) and !empty($lead_phone['sub_phone']) and strpos($lead_phone['sub_phone'], $row['phone']) !== false) {
                    $lead_phone['duplicate'][] = '<span class="red">' . $row['phone'] . '</span>';
                    $is_open_lead = 1;
                }

                if (!empty($row['sub_email']) and !empty($lead_phone['sub_email'])) {
                    $i = array_intersect(explode(',', $row['sub_email']), explode(',', $lead_phone['sub_email']));
                    foreach ($i as $p) {
                        $lead_phone['duplicate'][] = '<span class="red">' . $p . '</span>';
                        $is_open_lead = 1;
                    }
                }

                if (!empty($row['email']) and !empty($lead_phone['email']) and $lead_phone['email'] == $row['email']) {
                    $lead_phone['duplicate'][] = '<span class="red">' . $lead_phone['email'] . '</span>';
                    $is_open_lead = 1;
                }
                if (!empty($row['sub_email']) and !empty($lead_phone['email']) and strpos($row['sub_email'], $lead_phone['email']) !== false) {
                    $lead_phone['duplicate'][] = '<span class="red">' . $lead_phone['email'] . '</span>';
                    $is_open_lead = 1;
                }
                if (!empty($row['email']) and !empty($lead_phone['sub_email']) and strpos($lead_phone['sub_email'], $row['email']) !== false) {
                    $lead_phone['duplicate'][] = '<span class="red">' . $row['email'] . '</span>';
                    $is_open_lead = 1;
                }
                if (!empty($row['tax']) and !empty($lead_phone['tax']) and $lead_phone['tax'] == $row['tax']) {
                    $lead_phone['duplicate'][] = '<span class="red">' . $lead_phone['tax'] . '</span>';
                }
                $lead_phone['duplicate'] = array_unique($lead_phone['duplicate']);
                $lead_phone['duplicate'] = implode(', ', $lead_phone['duplicate']);

                /*
                 * trường hợp mở leads:
                 * - trùng 1 trong các thông tin: SDt, email, k tính mã số thuế
                 * - Thời gian chăm sóc gần nhất lớn hơn 3 ngày :
                 * - Quá lịch hẹn, nếu có
                 */
                $check_current_lead = check_current_lead($lead_phone);
                $open_check = $check_current_lead['hotting'] == true ? 0 : 1;

                if (($is_open_lead == 1 and $open_check == 1 and $lead_phone['status_id'] != 2 and $lead_phone['opportunities_id'] == 0) or defined('NV_IS_SPADMIN') or in_array($lead_phone['caregiverid'], $caregiver_id_leads) or $lead_phone['status_id'] == 4 or $lead_phone['status_id'] == 0) {
                    $link_lead = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $lead_phone['id'] . '&is_open=' . $is_open_lead . '&id_open_pre=' . $row['id'] . '&type_open_pre=' . $type;
                    $lead_phone['name'] = '<a href="' . $link_lead . '">' . $lead_phone['name'] . '</a>';
                    $lead_phone['notify_click_lead'] = $nv_Lang->getModule('notify_click_lead');
                }

                //Hiển thị thời gian sẽ nguội nếu lead đang nóng
                $lead_phone['hot_remaining_to'] = $check_current_lead['hotting'] == false ? '' : $nv_Lang->getModule('hot_remaining', $check_current_lead['remaining']);

                $lead_phone['updatetime'] = nv_date('H:i d/m/Y', $lead_phone['updatetime']);
                $xtpl->assign('LEADS_PHONE', $lead_phone);
                $xtpl->parse('main.lead_phone.loop');
            }
            $xtpl->parse('main.lead_phone');
        }

        // _opportunities
        $where = [];
        $where['AND'][] = [
            '=' => [
                'active' => 1
            ]
        ];

        if ($type == 1) {
            $where['AND'][] = [
                '!=' => [
                    'leadsid' => $row['id']
                ]
            ];
        } else {
            $where['AND'][] = [
                '!=' => [
                    'id' => $row['id']
                ]
            ];
        }

        if (!empty($row['email'])) {
            $where['OR'][] = [
                '=' => [
                    'email' => $row['email']
                ]
            ];

            $where['OR'][] = [
                'FIND_IN_SET' => [
                    'sub_email' => $row['email']
                ]
            ];
        }
        if (!empty($row['sub_email'])) {
            $_sub_email = explode(',', $row['sub_email']);
            foreach ($_sub_email as $key => $value) {
                $where['OR'][] = [
                    '=' => [
                        'email' => $value
                    ]
                ];

                $where['OR'][] = [
                    'FIND_IN_SET' => [
                        'sub_email' => $value
                    ]
                ];
            }
        }

        if (!empty($row['phone'])) {
            $where['OR'][] = [
                '=' => [
                    'phone_search' => $_tmp_phone
                ]
            ];

            $where['OR'][] = [
                'FIND_IN_SET' => [
                    'sub_phone_search' => $_tmp_phone
                ]
            ];
        }
        if (!empty($row['sub_phone'])) {
            foreach ($_tmp_sub_phone as $key => $value) {
                $where['OR'][] = [
                    '=' => [
                        'phone_search' => $value
                    ]
                ];

                $where['OR'][] = [
                    'FIND_IN_SET' => [
                        'sub_phone_search' => $value
                    ]
                ];
            }
        }
        if (!empty($row['tax'])) {
            $where['OR'][] = [
                '=' => [
                    'tax' => $row['tax']
                ]
            ];
        }

        $params = [];
        $params = [
            'userid' => $admin_info['userid'],
            'where' => $where
        ];

        // GỌI API
        $ListAllOpportunities = nv_local_api('ListAllOpportunities', $params, $admin_info['username'], $module_name);
        $ListAllOpportunities = json_decode($ListAllOpportunities, true);

        if (isset($ListAllOpportunities['data'])) {
            foreach ($ListAllOpportunities['data'] as $key => $value) {
                $array_oppotunities[$value['id']] = $value;
                if ($value['orderid'] != '') {
                    $where = [];
                    $where['AND'][] = [
                        'IN' => [
                            'order_id' => '(' . $value['orderid'] . ')'
                        ]
                    ];
                    $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
                    $api_dtinfo->setModule('bidding')
                        ->setLang('vi')
                        ->setAction('ListBiddingCustomsLog')
                        ->setData([
                        'where' => $where
                    ]);
                    $result = $api_dtinfo->execute();
                    $error = $api_dtinfo->getError();
                    if (empty($error) and $result['status'] == 'success') {
                        foreach ($result['data'] as $_order) {
                            $array_oppotunities[$value['id']]['order'][$_order['id']] = $_order;
                        }
                    }
                }
            }
        }

        // oppotunities
        if (sizeof($array_oppotunities) > 0) {
            $xtpl->assign('NUM_OPPOTUNTIES', sprintf($nv_Lang->getModule('duplicate_oppotunities'), sizeof($array_oppotunities)));
            foreach ($array_oppotunities as $oppotunities) {
                $oppotunities['caregiverid'] = $oppotunities['caregiver_id'];
                $oppotunities['caregiver_id'] = isset($array_user_id_users[$oppotunities['caregiver_id']]['username']) ? nv_show_name_user($array_user_id_users[$oppotunities['caregiver_id']]['first_name'], $array_user_id_users[$oppotunities['caregiver_id']]['last_name'], $array_user_id_users[$oppotunities['caregiver_id']]['username']) : $nv_Lang->getModule('not_caregiver');
                $oppotunities['status_id'] = $oppotunities['status'];
                $oppotunities['status'] = $array_status_opportunities[$oppotunities['status']];

                $oppotunities['check_vip'] = '';
                if (isset($oppotunities['order']) and !empty($oppotunities['order'])) {
                    foreach ($oppotunities['order'] as $order) {
                        $oppotunities['check_vip'] = '; Đã tạo đơn hàng VIP ' . $order['vip'] . ', Chờ thanh toán.';
                        if ($order['status'] == 1) {
                            $oppotunities['check_vip'] = '; Đã đăng ký VIP ' . $order['vip'];
                        }
                    }
                }
                $oppotunities['duplicate'] = [];
                $is_open_lead = 0;
                if (!empty($row['phone']) and !empty($oppotunities['phone']) and (substr($oppotunities['phone'], -9) == substr($row['phone'], -9))) {
                    $oppotunities['duplicate'][] = '<span class="red">' . $oppotunities['phone'] . '</span>';
                    $is_open_lead = 1;
                }
                if (!empty($row['phone']) and !empty($oppotunities['sub_phone']) and (strpos($oppotunities['sub_phone'], substr($row['phone'], -9))) !== false) {
                    $oppotunities['duplicate'][] = '<span class="red">' . $row['phone'] . '</span>';
                    $is_open_lead = 1;
                }
                if (!empty($row['sub_phone']) and !empty($oppotunities['phone']) and strpos($row['sub_phone'], $oppotunities['phone']) !== false) {
                    $oppotunities['duplicate'][] = '<span class="red">' . $oppotunities['phone'] . '</span>';
                    $is_open_lead = 1;
                }
                if (!empty($row['sub_phone']) and !empty($oppotunities['sub_phone'])) {
                    $i = array_intersect(explode(',', $row['sub_phone']), explode(',', $oppotunities['sub_phone']));
                    foreach ($i as $p) {
                        $oppotunities['duplicate'][] = '<span class="red">' . $p . '</span>';
                        $is_open_lead = 1;
                    }
                }

                if (!empty($row['sub_email']) and !empty($oppotunities['sub_email'])) {
                    $i = array_intersect(explode(',', $row['sub_email']), explode(',', $oppotunities['sub_email']));
                    foreach ($i as $p) {
                        $oppotunities['duplicate'][] = '<span class="red">' . $p . '</span>';
                        $is_open_lead = 1;
                    }
                }

                if (!empty($row['email']) and !empty($oppotunities['email']) and $oppotunities['email'] == $row['email']) {
                    $oppotunities['duplicate'][] = '<span class="red">' . $oppotunities['email'] . '</span>';
                    $is_open_lead = 1;
                }
                if (!empty($row['sub_email']) and !empty($oppotunities['email']) and strpos($row['sub_email'], $oppotunities['email']) !== false) {
                    $oppotunities['duplicate'][] = '<span class="red">' . $oppotunities['email'] . '</span>';
                    $is_open_lead = 1;
                }
                if (!empty($row['email']) and !empty($oppotunities['sub_email']) and strpos($oppotunities['sub_email'], $row['email']) !== false) {
                    $oppotunities['duplicate'][] = '<span class="red">' . $row['email'] . '</span>';
                    $is_open_lead = 1;
                }
                if (!empty($row['tax']) and !empty($oppotunities['tax']) and $oppotunities['tax'] == $row['tax']) {
                    $oppotunities['duplicate'][] = '<span class="red">' . $oppotunities['tax'] . '</span>';
                }
                $oppotunities['duplicate'] = array_unique($oppotunities['duplicate']);
                $oppotunities['duplicate'] = implode(', ', $oppotunities['duplicate']);

                /*
                 * trường hợp mở leads:
                 * - trùng 1 trong các thông tin: SDt, email, k tính mã số thuế
                 * - Thời gian chăm sóc gần nhất lớn hơn 3 ngày :
                 * - Quá lịch hẹn, nếu có
                 */
                $check_current_oppotunities = check_current_oppotunities($oppotunities);
                $open_check = $check_current_oppotunities['hotting'] == true ? 0 : 1;

                if (($is_open_lead == 1 and $open_check == 1 and $oppotunities['status_id'] != 2) or defined('NV_IS_SPADMIN') or in_array($oppotunities['caregiverid'], $caregiver_id_leads) or $oppotunities['status_id'] == 4 or $oppotunities['status_id'] == 0) {
                    $link_op = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=opportunities_info&amp;id=' . $oppotunities['id'] . '&is_open=' . $is_open_lead . '&id_open_pre=' . $row['id'] . '&type_open_pre=' . $type;
                    $oppotunities['name'] = '<a href="' . $link_op . '">' . $oppotunities['name'] . '</a>';
                    $oppotunities['notify_click_opportunities'] = $nv_Lang->getModule('notify_click_opportunities');
                }

                //Hiển thị thời gian sẽ nguội nếu lead đang nóng
                $oppotunities['hot_remaining_to'] = $check_current_oppotunities['hotting'] == false ? '' : $nv_Lang->getModule('hot_remaining', $check_current_oppotunities['remaining']);

                $oppotunities['updatetime'] = nv_date('H:i d/m/Y', $oppotunities['updatetime']);

                $xtpl->assign('OPPOTUNITIES', $oppotunities);
                $xtpl->parse('main.oppotunities.loop');
            }
            $xtpl->parse('main.oppotunities');
        }

        // 13/2/2023 by Đan. Key mã hóa từ dauthau.info vì bảng databank này được mã hóa từ bên đó
        $crypt_info = new NukeViet\Core\Encryption('f1f999357a163bfa056863b63f434a19');
        // bank
        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_" . $module_data . "_databank ";
        $where = [];
        if (!empty($row['email'])) {
            $where[] = "email = " . $db->quote($crypt_info->encrypt(strtolower($row['email']))) . "";
        }
        if (!empty($row['sub_email'])) {
            $where[] = "email IN (" . $db->quote($crypt_info->encrypt(strtolower($row['sub_email']))) . ") ";
        }
        if (!empty($row['phone'])) {
            // Xử lý số điện thoại
            $where_phone[] = $db->quote($crypt_info->encrypt($row['phone']));
            if (!empty(preg_match('/^\+84/', $row['phone']))) {
                $where_phone[] = $db->quote($crypt_info->encrypt(preg_replace('/^\+84/', '0', $row['phone'])));
            } elseif (!empty(preg_match('/^0/', $row['phone']))) {
                $where_phone[] = $db->quote($crypt_info->encrypt(preg_replace('/^0/', '+84', $row['phone'])));
            }
            $where[] = "phone IN (" . implode(',', $where_phone) . ")";
        }
        if (!empty($row['sub_phone'])) {
            $tmp_sub_phone = explode(',', $row['sub_phone']);
            foreach ($tmp_sub_phone as $key => $value) {
                $where[] = "phone = " . $db->quote($crypt_info->encrypt($value)) . "";
            }
        }

        $sql .= " WHERE " . implode(' OR ', $where) . " ORDER BY id DESC";
        $result = $db->query($sql);
        while ($_row = $result->fetch()) {
            $array_bank[$_row['id']] = $_row;
        }

        $where_canhan = [];
        if (sizeof($array_bank) > 0) {
            $xtpl->assign('NUM_BANK', sprintf($nv_Lang->getModule('duplicate_bank'), sizeof($array_bank)));
            foreach ($array_bank as $bank) {
                $bank['phone'] = $crypt_info->decrypt($bank['phone']);
                $bank['email'] = $crypt_info->decrypt($bank['email']);
                $bank['name'] = $crypt_info->decrypt($bank['name']);
                $bank['sex'] = $crypt_info->decrypt($bank['sex']);
                $bank['sex'] = $bank['sex'] == 'F' ? 'Nữ' : 'Nam';
                $bank['birthday'] = $crypt_info->decrypt($bank['birthday']);
                $bank['birthday'] = nv_date('d/m/Y', $bank['birthday']);
                $bank['job'] = $crypt_info->decrypt($bank['job']);
                $bank['address'] = $crypt_info->decrypt($bank['address']);
                $bank['idnumber'] = $crypt_info->decrypt($bank['idnumber']);
                if (!empty($bank['idnumber']) and is_numeric($bank['idnumber'])) {
                    $where_canhan[] = $bank['idnumber'];
                }
                if (!empty($row['phone']) and !empty($bank['phone']) and (substr($bank['phone'], -9) == substr($row['phone'], -9))) {
                    $bank['phone'] = '<span class="red">' . $bank['phone'] . '</span>';
                }
                if (!empty($row['sub_phone']) and !empty($bank['phone']) and strpos($row['sub_phone'], $bank['phone']) !== false) {
                    $bank['phone'] = '<span class="red">' . $bank['phone'] . '</span>';
                }
                if (!empty($row['email']) and !empty($bank['email']) and $bank['email'] == $row['email']) {
                    $bank['email'] = '<span class="red">' . $bank['email'] . '</span>';
                }
                if (!empty($row['sub_email']) and !empty($bank['email']) and strpos($row['sub_email'], $bank['email']) !== false) {
                    $bank['email'] = '<span class="red">' . $bank['email'] . '</span>';
                }

                $xtpl->assign('BANK', $bank);
                $xtpl->parse('main.bank.loop');
            }
            $xtpl->parse('main.bank');
        }
        $canhan_record = [];
        // Lấy thông tin cá nhân
        $params = [
            'canhan' => $where_canhan
        ];
        $api_canhan = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
        $api_canhan->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListCanhan')
            ->setData($params);
        $ListCanhan = $api_canhan->execute();
        if (empty($error) and $ListCanhan['status'] == 'success' and !empty($ListCanhan['data'])) {
            $canhan_record = $ListCanhan['data'];
        }

        if (!empty($canhan_record) and sizeof($canhan_record) > 0) {
            $xtpl->assign('NUM_CANHAN', sprintf($nv_Lang->getModule('count_ca_nhan'), sizeof($canhan_record)));
            foreach ($canhan_record as $canhan) {
                if (!empty($canhan['cmnd'])) {
                    $canhan['cccd'] = '<span class="red">' . $canhan['cmnd'] . '</span>';
                } else {
                    $canhan['cccd'] = '<span class="red">' . $canhan['cccd'] . '</span>';
                }
                $canhan['thoi_gian_boc'] = nv_date('H:i d/m/Y', $canhan['thoi_gian_boc']);
                $canhan['ngay_sinh'] = nv_date('d/m/Y', $canhan['ngay_sinh']);
                $xtpl->assign('CANHAN', $canhan);
                $xtpl->parse('main.canhan.loop');
            }
            $xtpl->parse('main.canhan');
            $canhan_chungchi_record = [];
            $chungchi = [
                'ca_nhan_id' => array_keys($canhan_record)
            ];
            $api_chungchi = new DoApi(API_DAUTHAUINFO_URL, API_DAUTHAUINFO_KEY, API_DAUTHAUINFO_SECRET);
            $api_chungchi->setModule('bidding')
                ->setLang('vi')
                ->setAction('GetChungChi')
                ->setData($chungchi);
            $GetChungChi = $api_chungchi->execute();
            if (empty($error) and $GetChungChi['status'] == 'success') {
                $canhan_chungchi_record = $GetChungChi['data'];
            }
            // Thông tin chứng chỉ xây dựng
            if (!empty($canhan_chungchi_record)) {
                $xtpl->assign('NUM_CHUNGCHI', sprintf($nv_Lang->getModule('count_chung_chi'), sizeof($canhan_chungchi_record)));
                foreach ($canhan_chungchi_record as $canhan_chungchi) {
                    $canhan_chungchi['ngay_het_han'] = date('d/m/Y', $canhan_chungchi['ngay_het_han']);
                    $xtpl->assign('CHUNGCHI', $canhan_chungchi);
                    $xtpl->parse('main.chungchi_xaydung.loop');
                }
            }
            $xtpl->parse('main.chungchi_xaydung');
        }

        // BNI
        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_bidding_bni_members";
        $where = [];
        $where[] = 'type > 0';
        if (!empty($row['phone'])) {
            $phoneConditions[] = "phone LIKE " . $db->quote('%' . $_tmp_phone . '');
        }

        if (!empty($row['sub_phone'])) {
            foreach ($tmp_sub_phone as $key => $value) {
                $phoneConditions[] = "phone LIKE " . $db->quote('%' . $value . '');
            }
        }

        if (!empty($phoneConditions)) {
            $where[] = '(' . implode(' OR ', $phoneConditions) . ')';
        }
        if (!empty($row['phone']) || !empty($row['sub_phone'])) {
            $sql .= " WHERE " . implode(' AND ', $where);
            $sql .= " ORDER BY id DESC";
            $result = $db->query($sql);
            while ($_row = $result->fetch()) {
                $chapter = $db->query("SELECT * FROM " . NV_PREFIXLANG . "_bidding_bni_chapters_url WHERE id=" . $_row['chapter']);
                while ($_chapter = $chapter->fetch()) {
                    $_row['chapter'] = $_chapter;
                }
                $array_bni[$_row['id']] = $_row;
            }
        }

        if (sizeof($array_bni) > 0) {
            $xtpl->assign('NUM_BNI', sprintf($nv_Lang->getModule('duplicate_bni'), sizeof($array_bni)));
            foreach ($array_bni as $bni) {
                if (!empty($row['phone']) and !empty($bni['phone']) and (substr($bni['phone'], -9) == substr($row['phone'], -9))) {
                    $bni['phone'] = '<span class="red">' . $bni['phone'] . '</span>';
                }
                if (!empty($row['sub_phone']) and !empty($bni['phone']) and strpos($row['sub_phone'], $bni['phone']) !== false) {
                    $bni['phone'] = '<span class="red">' . $bni['phone'] . '</span>';
                }
                if ($bni['status'] == 1) {
                    $bni['isuser'] = 'Là thành viên BNI';
                } else if ($bni['status'] == 0) {
                    $bni['isuser'] = 'Không còn là thành viên BNI từ tháng: <b>' . ($bni['chapter']['updatetime'] != 0 ? nv_date('m/Y', $bni['chapter']['updatetime']) : nv_date('m/Y', $bni['chapter']['gettime'])) . '</b>';
                }
                $xtpl->assign('BNI', $bni);
                $xtpl->parse('main.bni.loop');
            }
            $xtpl->parse('main.bni');
        }

        // Mobiphone records
        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_crmbidding_mobiphone";
        $where = [];
        if (!empty($row['phone'])) {
            $where[] = "sdt LIKE " . $db->quote('%' . $_tmp_phone . '');
        }
        if (!empty($row['sub_phone'])) {
            foreach ($tmp_sub_phone as $key => $value) {
                $where[] = "sdt LIKE (" . $db->quote('%' . $value . '') . ") ";
            }
        }
        if (!empty($where)) {
            $sql .= " WHERE " . implode(' OR ', $where) . "";
            $result = $db->query($sql);
            while ($_row = $result->fetch()) {
                $mobiphone_record[$_row['call_id']] = $_row;
            }
        }

        if (sizeof($mobiphone_record) > 0) {
            $xtpl->assign('MOBIPHONE_DUPLICATE', sprintf($nv_Lang->getModule('count_mobiphone'), sizeof($mobiphone_record)));
            $link = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=mobiphone&q=' . $row['phone'];

            foreach ($mobiphone_record as $mobiphone) {
                $mobiphone['link_mobilephone'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=crmbidding&amp;' . NV_OP_VARIABLE . '=mobiphone&amp;showheader=0&amp;id=' . $mobiphone['call_id'];
                if (!empty($row['phone']) and !empty($mobiphone['sdt']) and (substr($mobiphone['sdt'], -9) == substr($row['phone'], -9))) {
                    $mobiphone['sdt'] = '<span class="red">' . $mobiphone['sdt'] . '</span>';
                }
                if (!empty($row['sub_phone']) and !empty($mobiphone['sdt']) and strpos($row['sub_phone'], $mobiphone['sdt']) !== false) {
                    $mobiphone['sdt'] = '<span class="red">' . $mobiphone['sdt'] . '</span>';
                }
                $mobiphone['thoi_gian_bat_dau'] = nv_date('H:i d/m/Y', $mobiphone['thoi_gian_bat_dau']);
                $mobiphone['thoi_gian_ket_thuc'] = nv_date('H:i d/m/Y', $mobiphone['thoi_gian_ket_thuc']);
                $mobiphone['loai_cuoc_goi'] = $mobiphone['loai_cuoc_goi'] == 1 ? 'Gọi đến' : 'Gọi đi';
                $mobiphone['trang_thai_cuoc_goi'] = $mobiphone['trang_thai_cuoc_goi'] == 1 ? 'Cuộc gọi gặp' : 'Cuộc gọi nhỡ';
                $xtpl->assign('MOBIPHONE', $mobiphone);
                $xtpl->parse('main.mobiphone.loop');
            }

            $xtpl->parse('main.mobiphone');
        }

        // Voice Cloud
        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_crmbidding_voicecloud";
        $where = [];
        if (!empty($row['phone'])) {
            $where[] = "phone LIKE " . $db->quote('%' . $_tmp_phone . '');
        }

        if (!empty($row['sub_phone'])) {
            foreach ($tmp_sub_phone as $key => $value) {
                $where[] = "phone LIKE (" . $db->quote('%' . $value . '') . ") ";
            }
        }
        if (!empty($where)) {
            $sql .= " WHERE " . implode(' OR ', $where) . "";
            $result = $db->query($sql);
            while ($_row = $result->fetch()) {
                $voicecloud[] = $_row;
            }
        }

        if (sizeof($voicecloud) > 0) {
            $xtpl->assign('VOICECLOUD_DUPLICATE', sprintf($nv_Lang->getModule('count_voicecloud'), sizeof($voicecloud)));
            $link = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=voice_cloud&phone=' . $row['phone'];

            foreach ($voicecloud as $_voicecloud) {
                $_voicecloud['link'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=crmbidding&amp;' . NV_OP_VARIABLE . '=voice_cloud&amp;showheader=0&amp;type_view=2&amp;callid=' . $_voicecloud['callid'];
                if (!empty($row['phone']) and !empty($_voicecloud['phone']) and (substr($_voicecloud['phone'], -9) == substr($row['phone'], -9))) {
                    $_voicecloud['phone'] = '<span class="red">' . $_voicecloud['phone'] . '</span>';
                }
                if (!empty($row['sub_phone']) and !empty($_voicecloud['phone']) and strpos($row['sub_phone'], $_voicecloud['phone']) !== false) {
                    $_voicecloud['phone'] = '<span class="red">' . $_voicecloud['phone'] . '</span>';
                }
                $_voicecloud['calldatetimestart'] = nv_date('H:i d/m/Y', $_voicecloud['calldatetimestart']);
                $_voicecloud['calldatetimeend'] = nv_date('H:i d/m/Y', $_voicecloud['calldatetimeend']);
                $_voicecloud['direction'] = ($_voicecloud['direction'] == 'Outgoing' or $_voicecloud['direction'] == 'Outbound') ? $nv_Lang->getModule('goi_ra') : $nv_Lang->getModule('goi_vao');
                $_voicecloud['trang_thai_cuoc_goi'] = $_voicecloud['status'];
                $xtpl->assign('VOICECLOUD', $_voicecloud);
                $xtpl->parse('main.voicecloud.loop');
            }

            $xtpl->parse('main.voicecloud');
        }

        // Tawk to records
        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_crmbidding_tawkto";
        $where = [];
        if (!empty($row['email'])) {
            $where[] = "email = " . $db->quote($row['email']) . "";
        }
        if (!empty($row['sub_email'])) {
            $where[] = "email IN (" . $db->quote($row['sub_email']) . ") ";
        }
        if (!empty($row['phone'])) {
            $where[] = "sdt LIKE " . $db->quote('%' . $_tmp_phone . '');
        }
        if (!empty($row['sub_phone'])) {
            foreach ($tmp_sub_phone as $key => $value) {
                $where[] = "sdt LIKE (" . $db->quote('%' . $value . '') . ") ";
            }
        }
        if (!empty($where)) {
            $sql .= " WHERE " . implode(' OR ', $where) . "";
            $result = $db->query($sql);
            while ($_row = $result->fetch()) {
                $tawkto_record[$_row['id']] = $_row;
            }
        }

        if (sizeof($tawkto_record) > 0) {
            $xtpl->assign('NUM_TAWK', sprintf($nv_Lang->getModule('count_tawkto'), sizeof($tawkto_record)));

            foreach ($tawkto_record as $tawk) {
                if (!empty($row['phone']) and !empty($tawk['sdt']) and (substr($tawk['sdt'], -9) == substr($row['phone'], -9))) {
                    $tawk['sdt'] = '<span class="red">' . $tawk['sdt'] . '</span>';
                }
                if (!empty($row['sub_phone']) and !empty($tawk['sdt']) and strpos($row['sub_phone'], $tawk['sdt']) !== false) {
                    $tawk['sdt'] = '<span class="red">' . $tawk['sdt'] . '</span>';
                }
                if (!empty($row['email']) and !empty($tawk['email']) and $tawk['email'] == $row['email']) {
                    $tawk['email'] = '<span class="red">' . $tawk['email'] . '</span>';
                }
                if (!empty($row['sub_email']) and !empty($tawk['email']) and strpos($row['sub_email'], $tawk['email']) !== false) {
                    $tawk['email'] = '<span class="red">' . $tawk['email'] . '</span>';
                }
                $tawk['thoi_gian_gui'] = nv_date('H:i d/m/Y', $tawk['thoi_gian_gui']);
                $xtpl->assign('TAWK', $tawk);
                $xtpl->parse('main.tawk.loop');
            }
            $xtpl->parse('main.tawk');
        }        

        // Messager Record
        $sql = "SELECT * FROM " . NV_PREFIXLANG . "_crmbidding_messenger";
        $where = [];
        if (!empty($row['email'])) {
            $where[] = "email = " . $db->quote($row['email']) . "";
        }
        if (!empty($row['sub_email'])) {
            $where[] = "email IN (" . $db->quote($row['sub_email']) . ") ";
        }
        if (!empty($row['phone'])) {
            $where[] = "sdt LIKE " . $db->quote('%' . $_tmp_phone . '');
        }
        if (!empty($row['sub_phone'])) {
            foreach ($tmp_sub_phone as $key => $value) {
                $where[] = "sdt LIKE (" . $db->quote('%' . $value . '') . ") ";
            }
        }
        if (!empty($where)) {
            $sql .= " WHERE " . implode(' OR ', $where) . "";
            $result = $db->query($sql);
            while ($_row = $result->fetch()) {
                $messager_record[$_row['id']] = $_row;
            }
        }

        if (sizeof($messager_record) > 0) {
            $xtpl->assign('NUM_MESS', sprintf($nv_Lang->getModule('count_mess'), sizeof($messager_record)));

            foreach ($messager_record as $mess) {
                if (!empty($row['phone']) and !empty($mess['sdt']) and (substr($mess['sdt'], -9) == substr($row['phone'], -9))) {
                    $mess['sdt'] = '<span class="red">' . $mess['sdt'] . '</span>';
                }
                if (!empty($row['sub_phone']) and !empty($mess['sdt']) and strpos($row['sub_phone'], $mess['sdt']) !== false) {
                    $mess['sdt'] = '<span class="red">' . $mess['sdt'] . '</span>';
                }
                if (!empty($row['email']) and !empty($mess['email']) and $mess['email'] == $row['email']) {
                    $mess['email'] = '<span class="red">' . $mess['email'] . '</span>';
                }
                if (!empty($row['sub_email']) and !empty($mess['email']) and strpos($row['sub_email'], $mess['email']) !== false) {
                    $mess['email'] = '<span class="red">' . $mess['email'] . '</span>';
                }
                $mess['thoi_gian_gui'] = nv_date('H:i d/m/Y', $mess['thoi_gian_gui']);
                $xtpl->assign('MESS', $mess);
                $xtpl->parse('main.mess.loop');
            }
            $xtpl->parse('main.mess');
        }

        // Teacher Record
        $sql = "SELECT * FROM nv4_vi_bidding_teachers";
        $where = [];
        if (!empty($row['email'])) {
            $where[] = "email = " . $db->quote($row['email']) . "";
        }
        if (!empty($row['sub_email'])) {
            $where[] = "email IN (" . $db->quote($row['sub_email']) . ") ";
        }
        if (!empty($row['phone'])) {
            $where[] = "phone LIKE " . $db->quote('%' . $_tmp_phone . '');
        }
        if (!empty($row['sub_phone'])) {
            foreach ($tmp_sub_phone as $key => $value) {
                $where[] = "sub_phone LIKE (" . $db->quote('%' . $value . '') . ") ";
            }
        }
        if (!empty($where)) {
            $sql .= " WHERE " . implode(' OR ', $where) . "";
            $result = $db->query($sql);
            while ($_row = $result->fetch()) {
                $teacher_record[$_row['id']] = $_row;
            }
        }

        if (sizeof($teacher_record) > 0) {
            $xtpl->assign('NUM_TEACHER', sprintf($nv_Lang->getModule('count_teacher'), sizeof($teacher_record)));

            foreach ($teacher_record as $teacher) {
                if (!empty($row['phone']) and !empty($teacher['sdt']) and (substr($teacher['sdt'], -9) == substr($row['phone'], -9))) {
                    $teacher['sdt'] = '<span class="red">' . $teacher['sdt'] . '</span>';
                }
                if (!empty($row['sub_phone']) and !empty($teacher['sdt']) and strpos($row['sub_phone'], $teacher['sdt']) !== false) {
                    $teacher['sdt'] = '<span class="red">' . $teacher['sdt'] . '</span>';
                }
                if (!empty($row['email']) and !empty($teacher['email']) and $teacher['email'] == $row['email']) {
                    $teacher['email'] = '<span class="red">' . $teacher['email'] . '</span>';
                }
                if (!empty($row['sub_email']) and !empty($teacher['email']) and strpos($row['sub_email'], $teacher['email']) !== false) {
                    $teacher['email'] = '<span class="red">' . $teacher['email'] . '</span>';
                }
                $teacher['thoi_gian_gui'] = nv_date('H:i d/m/Y', $teacher['thoi_gian_gui']);
                $xtpl->assign('TEACHER', $teacher);
                $xtpl->parse('main.teacher.loop');
            }
            $xtpl->parse('main.teacher');
        }
    } else if ($next == 2) {
        // các dữ liệu dauthau.info
        $next = 3;
        // csdl khách vip
        $where = [];
        if (!empty($row['email'])) {
            $where['OR'][] = [
                '=' => [
                    'email' => $row['email']
                ]
            ];
        }
        if (!empty($row['sub_email'])) {
            $where['OR'][] = [
                '=' => [
                    'email' => $row['sub_email']
                ]
            ];
        }
        if (!empty($row['phone'])) {
            $where['OR'][] = [
                'LIKE' => [
                    'phone' => '%' . $_tmp_phone
                ]
            ];
        }
        if (!empty($row['sub_phone'])) {
            foreach ($_tmp_sub_phone as $key => $value) {
                $where['OR'][] = [
                    'LIKE' => [
                        'phone' => '%' . $value
                    ]
                ];
            }
        }

        if (!empty($row['tax'])) {
            $where['OR'][] = [
                '=' => [
                    'tax' => $row['tax']
                ]
            ];
        }
        $params = [
            'where' => $where
        ];
        $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
        $api_dtinfo->setModule('bidding')
            ->setLang('vi')
            ->setAction('ListBiddingCustomsLog')
            ->setData($params);
        $result = $api_dtinfo->execute();
        $error = $api_dtinfo->getError();
        if (empty($error) and $result['status'] == 'success' and !empty($result['data'])) {
            foreach ($result['data'] as $_row) {
                $_row['isset'] = 0;
                if (isset($_row['userdelete']) and $_row['userdelete'] == 0) {
                    $api_dtinfo = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
                    $api_dtinfo->setModule('bidding')
                        ->setLang('vi')
                        ->setAction('GetBiddingOrder')
                        ->setData([
                        'id' => $_row['order_id']
                    ]);
                    $check_order = $api_dtinfo->execute();
                    if ($check_order['status'] == 'success' and !empty($check_order['data'])) {
                        $_row['isset'] = 1;
                        $_row['order'] = $check_order['data'];
                    }
                }
                $array_customs_log[$_row['id']] = $_row;
            }
        }

        if (sizeof($array_customs_log) > 0) {
            $xtpl->assign('NUM_CUSTOMS_LOG', sprintf($nv_Lang->getModule('duplicate_customs'), sizeof($array_customs_log)));
            foreach ($array_customs_log as $customs_log) {
                $customs_log['duplicate'] = [];
                if (!empty($row['phone']) and !empty($customs_log['phone']) and (substr($customs_log['phone'], -9) == substr($row['phone'], -9))) {
                    $customs_log['link'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;q=' . $customs_log['phone'];
                    $customs_log['duplicate'][] = '<span class="red">' . $customs_log['phone'] . '</span>';
                }
                if (!empty($row['sub_phone']) and !empty($customs_log['phone']) and strpos($row['sub_phone'], $customs_log['phone']) !== false) {
                    $customs_log['link'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;q=' . $customs_log['phone'];
                    $customs_log['duplicate'][] = '<span class="red">' . $customs_log['phone'] . '</span>';
                }
                if (!empty($row['email']) and !empty($customs_log['email']) and $customs_log['email'] == $row['email']) {
                    $customs_log['link'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;q=' . $customs_log['email'];
                    $customs_log['duplicate'][] = '<span class="red">' . $customs_log['email'] . '</span>';
                }
                if (!empty($row['sub_email']) and !empty($customs_log['email']) and strpos($row['sub_email'], $customs_log['email']) !== false) {
                    $customs_log['link'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;q=' . $customs_log['sub_email'];
                    $customs_log['duplicate'][] = '<span class="red">' . $customs_log['email'] . '</span>';
                }
                if (!empty($row['tax']) and !empty($customs_log['tax']) and $customs_log['tax'] == $row['tax']) {
                    $customs_log['link'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;q=' . $customs_log['tax'];
                    $customs_log['duplicate'][] = '<span class="red">' . $customs_log['tax'] . '</span>';
                }

                $customs_log['duplicate'] = implode(',', $customs_log['duplicate']);
                if (!empty($customs_log['link']) and $customs_log['isset'] != 0) {
                    $customs_log['name'] = '<a href="' . $customs_log['link'] . '">' . $customs_log['name'] . '</a>';
                }

                $customs_log['check_vip'] = '; Đơn hàng';
                if ($customs_log['is_renewal'] == 1) {
                    $customs_log['check_vip'] .= ' gia hạn ';
                } else {
                    $customs_log['check_vip'] .= ' mới ';
                }
                $customs_log['check_vip'] .= $nv_Lang->getModule('vip' . $customs_log['vip']);
                if ($customs_log['status'] == 1) {
                    $customs_log['check_vip'] .= ' đã thanh toán.';
                } else {
                    $customs_log['check_vip'] .= ' chưa thanh toán.';
                }
                if ($customs_log['isset'] == 0) {
                    $customs_log['userdelete'] = isset($customs_log['userdelete']) ? $customs_log['userdelete'] : 0;
                    $userdelete = isset($array_user_id_users[$customs_log['userdelete']]['username']) ? nv_show_name_user($array_user_id_users[$customs_log['userdelete']]['first_name'], $array_user_id_users[$customs_log['userdelete']]['last_name'], $array_user_id_users[$customs_log['userdelete']]['username']) : 'N/A';
                    $customs_log['check_vip'] .= '(Đơn hàng đã bị xóa bởi: ' . $userdelete . ')';
                }

                $customs_log_caregiver_id = !empty($customs_log['order']) ? $customs_log['order']['caregiver_id'] : 0;
                if ($customs_log_caregiver_id > 0) {
                    $customs_log['caregiver_id'] = isset($array_user_id_users[$customs_log_caregiver_id]['username']) ? nv_show_name_user($array_user_id_users[$customs_log_caregiver_id]['first_name'], $array_user_id_users[$customs_log_caregiver_id]['last_name'], $array_user_id_users[$customs_log_caregiver_id]['username']) : $nv_Lang->getModule('not_caregiver');
                }

                $customs_log['addtime'] = nv_date('H:i d/m/Y', $customs_log['addtime']);

                $xtpl->assign('CUSTOMS_LOG', $customs_log);
                $xtpl->parse('main.customs_log.loop');
            }
            $xtpl->parse('main.customs_log');
        }

        $infoAPI = [];
        $arr_where = [];
        // nhà thầu
        $where = [];
        /*
         * if (!empty($row['email'])) {
         * $where[] = "email = " . $db->quote($row['email']) . "";
         * }
         * if (!empty($row['sub_email'])) {
         * $where[] = "email IN (" . $db->quote($row['sub_email']) . ") ";
         * }
         */
        if (!empty($row['phone'])) {
            $arr_where['OR'][] = [
                '=' => [
                    'phone_search' => $_tmp_phone
                ]
            ];
        }
        if (!empty($row['sub_phone'])) {
            foreach ($_tmp_sub_phone as $key => $value) {
                $arr_where['OR'][] = [
                    '=' => [
                        'phone_search' => $value
                    ]
                ];
            }
        }
        if (!empty($row['tax'])) {
            $arr_where['OR'][] = [
                '=' => [
                    'code' => $db->quote($row['tax'])
                ]
            ];
        }

        if (!empty($arr_where)) {
            $infoAPI = [
                'where' => $arr_where
            ];
            $businesslistings_info = CallAPI($infoAPI, 'ListAllBusinessListings', 'businesslistings', API_API_URL, API_API_KEY, API_API_SECRET);

            if (isset($businesslistings_info['data'])) {
                foreach ($businesslistings_info['data'] as $k => $_row) {
                    $array_business[$_row['id']] = $_row;
                }
            }
        }

        // nhà thầu
        if (sizeof($array_business) > 0) {
            $dauthau_asia_url = preg_replace('/(\/api\.php)$/', '/index.php?', API_DAUTHAUINFO_URL);
            $xtpl->assign('NUM_BUSINESS', sprintf($nv_Lang->getModule('duplicate_business'), sizeof($array_business)));
            foreach ($array_business as $business) {
                $business['link'] = $dauthau_asia_url . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=businesslistings&amp;' . NV_OP_VARIABLE . '=detail/' . change_alias($business['companyname']) . "-" . $business['id'];
                if (!empty($row['phone']) and !empty($business['phone']) and (substr($business['phone'], -9) == substr($row['phone'], -9))) {
                    $business['phone'] = '<span class="red">' . $business['phone'] . '</span>';
                }
                if (!empty($row['sub_phone']) and !empty($business['phone']) and strpos($row['sub_phone'], $business['phone']) !== false) {
                    $business['phone'] = '<span class="red">' . $business['phone'] . '</span>';
                }
                if (!empty($row['email']) and !empty($business['email']) and $business['email'] == $row['email']) {
                    $business['email'] = '<span class="red">' . $business['email'] . '</span>';
                }
                if (!empty($row['sub_email']) and !empty($business['email']) and strpos($row['sub_email'], $business['email']) !== false) {
                    $business['email'] = '<span class="red">' . $business['email'] . '</span>';
                }
                if (!empty($row['tax']) and !empty($business['code']) and $business['code'] == $row['tax']) {
                    $business['code'] = '<span class="red">' . $business['code'] . '</span>';
                }
                $business['update_time'] = nv_date('H:i d/m/Y', $business['update_time']);
                $business['ngay_phe_duyet'] = $business['ngay_phe_duyet'] != '' ? nv_date('H:i d/m/Y', $business['ngay_phe_duyet']) : '';
                $xtpl->assign('BUSINESS', $business);
                $xtpl->parse('main.business.loop');
            }
            $xtpl->parse('main.business');
        }

        // Tổ chức
        $infoAPI = [];
        $arr_where = [];
        if (!empty($row['tax'])) {
            $arr_where['AND'][] = [
                '=' => [
                    'ma_so_thue' => $row['tax']
                ]
            ];
        }

        if (!empty($arr_where)) {
            $infoAPI = [
                'where' => $arr_where
            ];
            $org_info = CallAPI($infoAPI, 'ListAllOrganization', 'to-chuc', API_API_URL, API_API_KEY, API_API_SECRET);

            if (isset($org_info['data'])) {
                foreach ($org_info['data'] as $k => $_row) {
                    $to_chuc_record[$_row['id']] = $_row;
                }
            }
        }

        if (sizeof($to_chuc_record) > 0) {
            $xtpl->assign('NUM_TOCHUC', sprintf($nv_Lang->getModule('count_to_chuc'), sizeof($to_chuc_record)));
            foreach ($to_chuc_record as $tochuc) {
                $tochuc['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=to-chuc&amp;' . NV_OP_VARIABLE . '=' . strtolower(change_alias($tochuc['ten_to_chuc'])) . '-' . $tochuc['id'] . $global_config['rewrite_exturl'];
                if (!empty($row['tax']) and !empty($tochuc['ma_so_thue']) and $tochuc['ma_so_thue'] == $row['tax']) {
                    $tochuc['ma_so_thue'] = '<span class="red">' . $tochuc['ma_so_thue'] . '</span>';
                }
                $tochuc['thoi_gian_boc'] = !empty($tochuc['thoi_gian_boc']) ? nv_date('H:i d/m/Y', $tochuc['thoi_gian_boc']) : '';
                $xtpl->assign('TOCHUC', $tochuc);
                $xtpl->parse('main.tochuc.loop');
            }

            $xtpl->parse('main.tochuc');
        }

        // Phòng thí nghiệm
        $infoAPI = [];
        $arr_where = [];
        if (!empty($row['tax'])) {
            $arr_where['AND'][] = [
                '=' => [
                    'ma_so_thue' => $row['tax']
                ]
            ];
        }

        if (!empty($arr_where)) {
            $infoAPI = [
                'where' => $arr_where
            ];
            $lab_info = CallAPI($infoAPI, 'ListAllLaboratory', 'las', API_API_URL, API_API_KEY, API_API_SECRET);

            if (isset($lab_info['data'])) {
                foreach ($lab_info['data'] as $k => $_row) {
                    $laboratory_record[$_row['id']] = $_row;
                }
            }
        }
        if (sizeof($laboratory_record) > 0) {
            $xtpl->assign('NUM_LABORATORY', sprintf($nv_Lang->getModule('count_phongtn'), sizeof($laboratory_record)));
            foreach ($laboratory_record as $laboratory) {
                $laboratory['link'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=laboratory&amp;' . NV_OP_VARIABLE . '=' . strtolower(change_alias($laboratory['tieu_de'])) . '-' . $laboratory['id'] . $global_config['rewrite_exturl'];
                if (!empty($row['tax']) and !empty($laboratory['ma_so_thue']) and $laboratory['ma_so_thue'] == $row['tax']) {
                    $laboratory['ma_so_thue'] = '<span class="red">' . $laboratory['ma_so_thue'] . '</span>';
                }
                $laboratory['thoi_gian_boc'] = nv_date('H:i d/m/Y', $laboratory['thoi_gian_boc']);
                $xtpl->assign('LABORATORY', $laboratory);
                $xtpl->parse('main.laboratory.loop');
            }

            $xtpl->parse('main.laboratory');
        }
    } else if ($next == 3) {
        // các dữ liệu dauthau.net
        $next = 4;
        // cơ sở dữ liệu doanh nghiệp
        if (!empty($row['tax'])) {
            $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
            $api->setModule('')
                ->setLang('vi')
                ->setAction('GetBidsProfile')
                ->setData([
                'prof_code' => $row['tax']
            ]);
            $result = $api->execute();
            $error = $api->getError();
            if (empty($error) and $result['status'] == 'success') {
                if ($result['profile_info']['profile']['sys_add'] == 2) {
                    $array_dkkd[] = $result['profile_info']['profile'];

                    if (!empty($result['profile_info']['profile']['business_related'])) {
                        $company_related = $result['profile_info']['profile']['business_related'];
                    }
                }
            }
        }

        if (sizeof($array_dkkd) > 0) {
            $xtpl->assign('NUM_DKKD', sprintf($nv_Lang->getModule('duplicate_dkkd'), sizeof($array_dkkd)));
            foreach ($array_dkkd as $business) {
                $business['link'] = 'https://dauthau.net/vi/dn/' . $business['prof_alias'];
                if (!empty($row['tax']) and !empty($business['prof_code']) and $business['prof_code'] == $row['tax']) {
                    $business['prof_code'] = '<span class="red">' . $business['prof_code'] . '</span>';
                }
                $xtpl->assign('BUSINESS', $business);

                if (!empty($company_related)) {
                    foreach ($company_related as $company) {
                        $company['link_view'] = 'https://dauthau.net/vi/dn/' . $company['company_alias'];
                        $xtpl->assign('COMPANY_RELATED', $company);
                        $xtpl->parse('main.dkkd.loop.company_related.loop_company');
                    }
                    $xtpl->parse('main.dkkd.loop.company_related');
                }
                $xtpl->parse('main.dkkd.loop');
            }
            $xtpl->parse('main.dkkd');
        }

        // ajax gọi API check trùng dữ liệu hồ sơ đã tạo trên dauthau.net (GetDuplicateProfile)
        $array_profile_dtnet = [];
        $error_api_getduplicate_profile = '';
        $array_payment_dtnet = [];
        $error_api_listduplicate_payment = '';

        if (defined('API_DAUTHAUNET_URL')) {
            $check_duplicate['phone'] = $row['phone'];
            $check_duplicate['sub_phone'] = $row['sub_phone'];
            $check_duplicate['email'] = $row['email'];
            $check_duplicate['sub_email'] = $row['sub_email'];
            $check_duplicate['tax'] = $row['tax'];

            // check trùng csdl dauthau.net
            if (!empty($check_duplicate['tax']) or !empty($check_duplicate['phone']) or !empty($check_duplicate['sub_phone']) or !empty($check_duplicate['email']) or !empty($check_duplicate['sub_email'])) {
                // check trùng profile cơ sở dữ liệu dauthau.net: GetDuplicateProfile
                $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
                $api->setModule('')
                    ->setLang('vi')
                    ->setAction('GetDuplicateProfile')
                    ->setData([
                    'prof_code' => $check_duplicate['tax'],
                    'phone' => $_tmp_phone,
                    'sub_phone' => $_tmp_sub_phone,
                    'email' => $check_duplicate['email'],
                    'sub_email' => $check_duplicate['sub_email']
                ]);
                $result = $api->execute();
                $error = $api->getError();
                if (empty($error) and $result['status'] == 'success') {
                    $array_profile_dtnet = isset($result['profile_duplicate']) ? $result['profile_duplicate'] : [];
                } else {
                    if (!empty($error)) {
                        $error_api_getduplicate_profile = $error;
                    } elseif ($result['status'] == 'error') {
                        $error_api_getduplicate_profile = $result;
                    }
                }

                // check trùng đơn hàng cơ sở dữ liệu dauthau.net
                $api = new DoApi(API_DAUTHAUNET_URL, API_DAUTHAUNET_KEY, API_DAUTHAUNET_SECRET);
                $api->setModule('')
                    ->setLang('vi')
                    ->setAction('ListDuplicatePayment')
                    ->setData([
                    'tax' => $check_duplicate['tax'],
                    'phone' => $_tmp_phone,
                    'sub_phone' => $_tmp_sub_phone,
                    'email' => $check_duplicate['email'],
                    'sub_email' => $check_duplicate['sub_email']
                ]);
                $result = $api->execute();
                $error = $api->getError();
                if (empty($error) and $result['status'] == 'success') {
                    if ($result['code'] != 4000) {
                        $array_payment_dtnet = $result['payment_duplicate'];
                    }
                } else {
                    if (!empty($error)) {
                        $error_api_listduplicate_payment = $error;
                    } elseif ($result['status'] == 'error') {
                        $error_api_listduplicate_payment = $result;
                    }
                }
            }
        }

        if (empty($error_api_getduplicate_profile)) {
            if (!empty($array_profile_dtnet)) {
                $xtpl->assign('NUMBER_PROFILE_TITLE', sprintf($nv_Lang->getModule('duplicate_profile_dtnet'), count($array_profile_dtnet)));
                foreach ($array_profile_dtnet as $key => $value) {
                    $value['link'] = 'https://dauthau.net/vi/dn/' . $value['prof_alias'];
                    $value['prof_name_title'] = $value['status'] == 1 ? '<a href="' . $value['link'] . '" target="_blank"><strong>' . $value['prof_name'] . '</strong></a>' : '<strong>' . $value['prof_name'] . '</strong>';

                    if (!empty($row['phone']) and !empty($value['info_phone']) and (substr($value['info_phone'], -9) == substr($row['phone'], -9))) {
                        $value['info_phone'] = '<span class="red">' . $value['info_phone'] . '</span>';
                    }
                    if (!empty($row['sub_phone']) and !empty($value['info_phone']) and strpos($row['sub_phone'], $value['info_phone']) !== false) {
                        $value['info_phone'] = '<span class="red">' . $value['info_phone'] . '</span>';
                    }
                    if (!empty($row['email']) and !empty($value['info_email']) and $value['info_email'] == $row['email']) {
                        $value['info_email'] = '<span class="red">' . $value['info_email'] . '</span>';
                    }
                    if (!empty($row['sub_email']) and !empty($value['info_email']) and strpos($row['sub_email'], $value['info_email']) !== false) {
                        $value['info_email'] = '<span class="red">' . $value['info_email'] . '</span>';
                    }
                    if (!empty($row['tax']) and !empty($value['prof_code']) and $value['prof_code'] == $row['tax']) {
                        $value['prof_code'] = '<span class="red">' . $value['prof_code'] . '</span>';
                    }
                    $xtpl->assign('PROFILE', $value);

                    $xtpl->parse('main.duplicate_profile.loop');
                }
                $xtpl->parse('main.duplicate_profile');
            }
        } else {
            $xtpl->parse('main.error_duplicate_profile');
        }

        if (empty($error_api_listduplicate_payment)) {
            if (!empty($array_payment_dtnet)) {
                $xtpl->assign('NUM_DUPLICATE_PAYMENT', sprintf($nv_Lang->getModule('duplicate_payment_dtnet'), sizeof($array_payment_dtnet)));
                foreach ($array_payment_dtnet as $duplicate_payment) {
                    $duplicate_payment['duplicate'] = [];
                    if (!empty($row['phone']) and !empty($duplicate_payment['contact_phone']) and (substr($duplicate_payment['contact_phone'], -9) == substr($row['phone'], -9))) {
                        $duplicate_payment['link'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;q=' . $duplicate_payment['contact_phone'];
                        $duplicate_payment['duplicate'][] = '<span class="red">' . $duplicate_payment['contact_phone'] . '</span>';
                    }
                    if (!empty($row['sub_phone']) and !empty($duplicate_payment['contact_phone']) and strpos($row['sub_phone'], $duplicate_payment['contact_phone']) !== false) {
                        $duplicate_payment['link'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;q=' . $duplicate_payment['contact_phone'];
                        $duplicate_payment['duplicate'][] = '<span class="red">' . $duplicate_payment['contact_phone'] . '</span>';
                    }
                    if (!empty($row['email']) and !empty($duplicate_payment['address_bill']) and $duplicate_payment['address_bill'] == $row['email']) {
                        $duplicate_payment['link'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;q=' . $duplicate_payment['address_bill'];
                        $duplicate_payment['duplicate'][] = '<span class="red">' . $duplicate_payment['address_bill'] . '</span>';
                    }
                    if (!empty($row['sub_email']) and !empty($duplicate_payment['address_bill']) and strpos($row['sub_email'], $duplicate_payment['address_bill']) !== false) {
                        $duplicate_payment['link'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;q=' . $duplicate_payment['sub_email'];
                        $duplicate_payment['duplicate'][] = '<span class="red">' . $duplicate_payment['address_bill'] . '</span>';
                    }
                    if (!empty($row['tax']) and !empty($duplicate_payment['tax']) and $duplicate_payment['tax'] == $row['tax']) {
                        $duplicate_payment['link'] = URL_DTINFO_ADMIN . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=bidding&amp;' . NV_OP_VARIABLE . '=payment&amp;q=' . $duplicate_payment['tax'];
                        $duplicate_payment['duplicate'][] = '<span class="red">' . $duplicate_payment['tax'] . '</span>';
                    }

                    $duplicate_payment['duplicate'] = implode(',', $duplicate_payment['duplicate']);
                    if (!empty($duplicate_payment['link']) and $duplicate_payment['isset'] != 0) {
                        $duplicate_payment['name'] = '<a href="' . $duplicate_payment['link'] . '">' . $duplicate_payment['name'] . '</a>';
                    }

                    $duplicate_payment['check_vip'] = ' ' . $nv_Lang->getModule('payment');
                    if (!empty($duplicate_payment['vip'])) {
                        if ($duplicate_payment['is_renewal'] == 1) {
                            $duplicate_payment['check_vip'] .= ' ' . $nv_Lang->getModule('check_vip_renewal1') . ' ';
                        } elseif ($duplicate_payment['is_renewal'] == 2) {
                            $duplicate_payment['check_vip'] .= ' ' . $nv_Lang->getModule('check_vip_renewal2') . ' ';
                        } else {
                            $duplicate_payment['check_vip'] .= ' ' . $nv_Lang->getModule('check_vip_renewal0') . ' ';
                        }
                    } else {
                        $duplicate_payment['check_vip'] .= ' ' . $nv_Lang->getModule('check_vip_authen') . ' ';
                    }

                    $duplicate_payment['check_vip'] .= $nv_Lang->getModule($duplicate_payment['vip']);
                    if ($duplicate_payment['status'] == 1) {
                        $duplicate_payment['check_vip'] .= ' ' . $nv_Lang->getModule('check_vip_already_payment') . '.';
                    } else {
                        $duplicate_payment['check_vip'] .= ' ' . $nv_Lang->getModule('check_vip_not_payment') . '.';
                    }

                    $duplicate_payment_caregiver_id = $duplicate_payment['caregiver_id'];
                    if ($duplicate_payment_caregiver_id > 0) {
                        $duplicate_payment['caregiver_id'] = isset($array_user_id_users[$duplicate_payment_caregiver_id]['username']) ? nv_show_name_user($array_user_id_users[$duplicate_payment_caregiver_id]['first_name'], $array_user_id_users[$duplicate_payment_caregiver_id]['last_name'], $array_user_id_users[$duplicate_payment_caregiver_id]['username']) : $nv_Lang->getModule('not_caregiver');
                    } else {
                        $duplicate_payment['caregiver_id'] = $nv_Lang->getModule('not_caregiver');
                    }

                    $duplicate_payment['addtime'] = nv_date('H:i d/m/Y', $duplicate_payment['addtime']);

                    $xtpl->assign('DUPLICATE_PAYMENT', $duplicate_payment);
                    $xtpl->parse('main.duplicate_payment.loop');
                }
                $xtpl->parse('main.duplicate_payment');
            }
        } else {
            $xtpl->parse('main.error_duplicate_payment');
        }
    } else if ($next == 4) {
        // các dữ liệu marketing
        $next = 5;
        $send_mail = array();
        if ($row['sub_email'] != '') {
            $send_mail = explode(',', $row['sub_email']);
        }
        array_push($send_mail, $row['email']);

        if (defined('MARKETING_API_CRM_KEY')) {
            $status_send = array(
                'successfully_sent' => 'Gửi thành công',
                'pending' => 'Chờ gửi',
                'failure' => 'Thất bại',
                'wait_for_resend' => 'Gửi thất bại, chờ thử lại',
                'cancel' => 'Admin hủy gửi'
            );

            $num = 0;
            foreach ($send_mail as $email) {
                $num++;
                if ($email != '') {
                    $page = 1;
                    $list_campaign = [];
                    $list_userid = [];
                    $email_marketing = [];

                    // tuyenhv: sửa lại chỉ lấy 1 page và total
                    // Lấy data email được gửi tới KH
                    $request = [
                        'apikey' => MARKETING_API_CRM_KEY,
                        'apisecret' => MARKETING_API_CRM_SECRET,
                        'action' => 'GetEmailSentOfEmail',
                        'module' => 'marketing',
                        'page' => $page,
                        'per_page' => 100,
                        'email' => $email,
                        'language' => 'vi'
                    ];
                    $NV_Http = new NukeViet\Http\Http($global_config, NV_TEMP_DIR);
                    $NV_Http->reset();
                    $args = [
                        'headers' => [
                            'Referer' => NV_MY_DOMAIN
                        ],
                        'body' => $request,
                        'timeout' => 10,
                        'sslverify' => false,
                        'decompress' => false
                    ];
                    $responsive = $NV_Http->post(MARKETING_API_URL, $args);
                    if (is_array($responsive) and empty(NukeViet\Http\Http::$error)) {
                        $email_marketing = !empty($responsive['body']) ? json_decode($responsive['body'], true) : [];
                        if (!empty($email_marketing)) {
                            if ($email_marketing['status'] == 'success') {
                                foreach ($email_marketing['rows'] as $item) {
                                    $list_campaign[$item['campaign']]['data'][] = $item;
                                    $list_userid[$item['userid']] = $item['userid'];
                                }
                            }
                        }
                    } else {
                        trigger_error(print_r($NV_Http, true));
                    }

                    if (!empty($email_marketing) and $email_marketing['status'] == 'success' && !empty($list_campaign)) {
                        // Lấy ds username theo user ID
                        $list_userid = implode(',', $list_userid);
                        $list_username = array();
                        $sql = "SELECT userid, username  FROM " . NV_USERS_GLOBALTABLE . " WHERE userid IN (" . $db->quote($list_userid) . ")";
                        $result = $db->query($sql);
                        while ($_row = $result->fetch()) {
                            $list_username[$_row['userid']] = $_row['username'];
                        }

                        $xtpl->assign('MAIL', $email);
                        $xtpl->assign('NUM', $num);
                        foreach ($list_campaign as $key => $campaign) {
                            $xtpl->assign('NUM_CAM', sizeof($campaign['data']));
                            $xtpl->assign('NAME_CAM', $key);
                            foreach ($campaign['data'] as $item) {
                                $item['userid'] = $list_username[$item['userid']];
                                $item['status'] = !empty($status_send[$item['status']]) ? $status_send[$item['status']] : '';
                                if ($item['is_read'] == 1) {
                                    $item['read'] = 'Đã đọc ' . $item['open_count'] . ' lần, lần đầu: ' . nv_date('H:i d/m/Y', $item['date_read']) . ', lần cuối: ' . nv_date('H:i d/m/Y', $item['last_opened']);
                                } else {
                                    $item['read'] = 'Chưa đọc';
                                }

                                if (!empty($item['tracking_urls'])) {
                                    $item['urls'] = '[';
                                    foreach ($item['tracking_urls'] as $link) {
                                        $item['urls'] .= ' <a href="' . $link['url'] . '">' . $link['url'] . '</a> - ' . $link['hits'] . ' hits </br>';
                                    }
                                    $item['urls'] .= ']';
                                } else {
                                    $item['urls'] = 'None';
                                }

                                $xtpl->assign('ITEM', $item);
                                $xtpl->parse('main.email.marketing_email.campaign.loop');
                            }
                            $xtpl->parse('main.email.marketing_email.campaign');
                        }

                        $xtpl->assign('NUM_MAIL', $email_marketing['records']);
                        $xtpl->parse('main.email.marketing_email');
                    } else if ($email_marketing['status'] == 'error') {
                        $xtpl->assign('ERROR_CODE', $email_marketing['code']);
                        $xtpl->assign('ERROR_MESS', $email_marketing['message']);
                        $xtpl->parse('main.email.marketing_api_error');
                    }
                }

                $xtpl->parse('main.email');
            }
        }

        // thống kê mail được gửi từ hệ thống: từ module contact, dauthau (tạo tk VIP, gia hạn VIP)
        $mail_alllogs = array();
        /*
         * if($row['user_id'] > 0){
         * //tìm vip_id
         * $vip_id = $db->query("SELECT id FROM " . NV_PREFIXLANG . "_bidding_customs WHERE user_id=" . $row['user_id'])->fetchColumn();
         * //$vip_id=7305;
         * if(!empty($vip_id)){
         * $result = $db->query("SELECT * FROM " . NV_PREFIXLANG . "_bidding_alllogs WHERE uvip_id=" . $vip_id . " AND (log_key=" . $db->quote('LOG_VIP_ACCOUNT_RENEWAL') . " OR log_key=" . $db->quote('LOG_VIP_ACCOUNT_CREAT') . ")");
         * while($item = $result->fetch()){
         * $item['log_data'] = json_decode($item['log_data']);
         * $item['log_time'] = nv_date('H:i d/m/Y', $item['log_time']);
         * $mail_alllogs['bidding_alllogs'][] = $item;
         * }
         * }
         * }
         */

        // $send_mail = array('<EMAIL>', '<EMAIL>');
        $where = array();
        /*
         * Tạm thời tắt đi theo issues https://vinades.org/dauthau/dauthau.info/-/issues/1091
         * foreach ($send_mail as $item) {
         * if ($item != '') {
         * $where[] = "log_data LIKE '%" . $item . "%'";
         * }
         * }
         */

        if (!empty($where)) {
            $where = implode(' OR ', $where);
            $result = $db->query("SELECT * FROM " . NV_PREFIXLANG . "_bidding_alllogs WHERE " . $where . " AND (log_key=" . $db->quote('LOG_VIP_ACCOUNT_RENEWAL') . " OR log_key=" . $db->quote('LOG_VIP_ACCOUNT_CREAT') . ")");
            while ($item = $result->fetch()) {
                $item['log_data'] = json_decode($item['log_data']);
                $item['log_time'] = nv_date('H:i d/m/Y', $item['log_time']);
                $mail_alllogs['bidding_alllogs'][] = $item;
            }
        }

        // Tìm email từ module contact
        $sql = "SELECT a.title, a.sender_email,b.* FROM " . NV_PREFIXLANG . "_contact_send as a RIGHT JOIN " . NV_PREFIXLANG . "_contact_reply as b ON a.id= b.id WHERE a.sender_email IN (" . $db->quote(implode(',', $send_mail)) . ")";
        $result = $db->query($sql);
        while ($_row = $result->fetch()) {
            $_row['reply_time'] = nv_date('H:i d/m/Y', $_row['reply_time']);
            $mail_alllogs['contact'][] = $_row;
        }
        if (!empty($mail_alllogs)) {
            foreach ($mail_alllogs as $key => $arr_log) {
                if ($key == 'bidding_alllogs') {
                    $xtpl->assign('NUM_LOG', sizeof($arr_log));
                    foreach ($arr_log as $log) {
                        $type_log = $log['log_key'] == 'LOG_VIP_ACCOUNT_CREAT' ? 'Mail tạo tài khoản VIP' : 'Mail gia hạn tài khoản VIP';
                        $xtpl->assign('TYPE_LOG', $type_log);
                        $xtpl->assign('LOG', $log);
                        foreach ($log['log_data'] as $log_detail) {
                            $xtpl->assign('KEY_LOG', $log_detail[0]);
                            $xtpl->assign('LOG_VALUE', $log_detail[1]);
                            $xtpl->parse('main.email_sys.bidding.logs.loop');
                        }
                        $xtpl->parse('main.email_sys.bidding.logs');
                    }
                    $xtpl->parse('main.email_sys.bidding');
                }

                if ($key == 'contact') {
                    $xtpl->assign('NUM_LOG', sizeof($arr_log));
                    foreach ($arr_log as $contact) {
                        $xtpl->assign('CONTACT', $contact);
                        $xtpl->parse('main.email_sys.contact.loop');
                    }
                    $xtpl->parse('main.email_sys.contact');
                }
            }
            $xtpl->parse('main.email_sys');
        }
    }
}
$xtpl->parse('main');
$contents = $xtpl->text('main');
$respon = [
    'next' => $next,
    'data' => $contents
];
nv_jsonOutput($respon);

function CallAPI($infoAPI, $API, $module_name = 'businesslistings', $api_url = API_DAUTHAUINFO_URL, $api_key = API_DAUTHAUINFO_KEY, $api_secret = API_DAUTHAUINFO_SECRET)
{
    if (isset($infoAPI['page'])) {
        $infoAPI['page'] = intval($infoAPI['page']);
    }

    if (isset($infoAPI['perpage'])) {
        $infoAPI['perpage'] = intval($infoAPI['perpage']);
    }

    if (!empty($infoAPI)) {
        foreach ($infoAPI as $key => $value) {
            $params_customs[$key] = $value;
        }
    }

    $api = new DoApi($api_url, $api_key, $api_secret);

    if (!empty($params_customs)) {
        $api->setModule($module_name)
            ->setLang('vi')
            ->setAction($API)
            ->setData($params_customs);
    } else {
        $api->setModule($module_name)
            ->setLang('vi')
            ->setAction($API);
    }

    $result = $api->execute();
    $error = $api->getError();
    $data = [];
    if (empty($error) and $result['status'] == 'success') {
        $data = $result;
    }
    return $data;
}

function check_affilacate_info($lead) {
    global $array_groups_leads;
    $affilacate = [];
    $affilacate['status'] = false;
    if ($lead['affilacate_id'] > 0 && in_array($lead['source_leads'], [2, 4, 7, 11, 15])) {
        $affilacate['status'] = true;
        $affilacate['group'] = $array_groups_leads[$lead['source_leads']]['title'];
        $affilacate_user = get_user($lead['affilacate_id']);
        $affilacate['fullname'] = nv_show_name_user($affilacate_user['first_name'], $affilacate_user['last_name'], $affilacate_user['username']);
    }

    return $affilacate;
}
