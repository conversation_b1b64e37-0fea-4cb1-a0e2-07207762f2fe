<?php

/*
 * <PERSON><PERSON><PERSON> log về xử lý tập trung, code lưu tại: https://vinades.org/dauthau/id.dauthau.net/-/blob/master/private/push_errorlog.php
 */
$site_timezone = 'Asia/Ho_Chi_Minh';
date_default_timezone_set($site_timezone);

define('NV_ROOTDIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME) . '/../public_html')));
define('NV_CONSOLE_DIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME))));

$directory = NV_ROOTDIR . '/data/logs/error_logs';
if (NV_CONSOLE_DIR == '/home/<USER>/private') {
    $server_name = 'dauthau.asia';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private') {
    $server_name = 'api.dauthau.asia';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private') {
    $server_name = 'craws.dauthau.asia';
    $directory = NV_CONSOLE_DIR . '/data/logs/error_logs';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private/dau-gia') {
    $server_name = 'craws.daugia.net';
    $directory = NV_CONSOLE_DIR . '/data/logs/error_logs';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private') {
    $server_name = 'sendmailaws.dauthau.asia';
    $directory = NV_CONSOLE_DIR . '/data/logs/error_logs';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private') {
    $server_name = 'crontabdb.dauthau.asia';
    $directory = NV_CONSOLE_DIR . '/data/logs/error_logs';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private') {
    $server_name = 'dtimap.dauthau.asia';
    $directory = NV_CONSOLE_DIR . '/data/logs/error_logs';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private') {
    $server_name = 'dauthau.net';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private') {
    $server_name = 'id.dauthau.net';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private') {
    $server_name = 'sso.dauthau.net';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private') {
    $server_name = 'crmprivate.dauthau.net';
    $directory = NV_CONSOLE_DIR . '/data/logs/error_logs';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private') {
    $server_name = 'crdtnet.dauthau.net';
    $directory = NV_CONSOLE_DIR . '/data/logs/error_logs';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private') {
    $server_name = 'private.dauthau.net';
    $directory = NV_CONSOLE_DIR . '/data/logs/error_logs';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private') {
    $server_name = 'marketing.dauthau.asia';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private') {
    $server_name = 'mkttools.marketing';
    $directory = NV_CONSOLE_DIR . '/data/logs/error_logs';
} else {
    die("Chưa xác định server_name; NV_CONSOLE_DIR: " . NV_CONSOLE_DIR);
}

if ($dh = opendir($directory)) {
    if (ob_get_level()) {
        ob_end_clean();
    }

    while (($file_name = readdir($dh)) !== false) {
        if (preg_match('/_log.log$/', $file_name) or $file_name == 'sendmail.log') {
            $file_uniqid = $file_name . '_' . uniqid('', true);
            if (rename($directory . '/' . $file_name, $directory . '/' . $file_uniqid)) {
                echo $file_name . "\n";
                $responsive = nv_push_log($directory . '/' . $file_uniqid, $file_name);
                if ($responsive['status'] == 'success') {
                    unlink($directory . '/' . $file_uniqid);
                } else {
                    file_put_contents(NV_CONSOLE_DIR . '/errorlog.log', print_r($responsive, true), FILE_APPEND);
                }
            }
        }
    }
    closedir($dh);
}
die("Thực hiện xong push_errorlog\n");

function nv_push_log($file_path, $file_name)
{
    global $server_name;

    $agent = 'NukeViet Remote API Lib';

    $apikey = 'p10Tzou0c44tEZU53ANJ2CyJ1SI3l2gb';
    $apisecret = 'y45A4r7n933H6g7nN8nm6o5451DAc4fo';
    $api_remote_url = 'https://errorlog.dauthau.net/api.php';

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_remote_url);
    curl_setopt($ch, CURLOPT_HEADER, 0);

    $safe_mode = (ini_get('safe_mode') == '1' || strtolower(ini_get('safe_mode')) == 'on') ? 1 : 0;
    $open_basedir = ini_get('open_basedir') ? true : false;
    if (!$safe_mode and !$open_basedir) {
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
    }

    $timestamp = time();
    $request = [
        'apikey' => $apikey,
        'timestamp' => $timestamp,
        'hashsecret' => password_hash($apisecret . '_' . $timestamp, PASSWORD_DEFAULT),
        'module' => '',
        'action' => 'ErrorLog',
        'language' => 'vi',
        'server_name' => $server_name,
        'file_name' => $file_name,
        'file_content' => gzcompress(file_get_contents($file_path), 9)
    ];

    curl_setopt($ch, CURLOPT_TIMEOUT, 20);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_POST, sizeof($request));
    curl_setopt($ch, CURLOPT_POSTFIELDS, $request);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    $res = curl_exec($ch);
    print_r($res);
    curl_close($ch);

    return json_decode($res, true);
}