<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\elink\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CheckAffiliateCode implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'affiliate';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $global_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        $_affiliate_code = $nv_Request->get_title('code', 'post', '');
        $_old_affiliate_code = $nv_Request->get_title('old_code', 'post', '');
        if (empty($_affiliate_code) or nv_check_valid_login($_affiliate_code, $global_config['nv_unickmax'], $global_config['nv_unickmin'])) {
            return $this->result->setCode('1001')->setMessage($nv_Lang->getModule('apierror_uidcode'))->getResult();
        }

        // Kiểm tra thành viên
        $sql = "SELECT tb1.userid, tb2.admin_id FROM " . NV_USERS_GLOBALTABLE . " tb1
        LEFT JOIN " . NV_AUTHORS_GLOBALTABLE . " tb2 ON tb1.userid=tb2.admin_id
        WHERE tb1.username=" . $db->quote($_affiliate_code);
        $check = $db->query($sql)->fetch();
        if (!empty($check)) {
            /*
             * Trường hợp trước đó có mã. Chỉ đè khi có 1 trong các điều kiện này
             * - Mã này của CTV tức empty
             * - Mã trước đó của sale
             */
            $set = false;
            if (!empty($_old_affiliate_code)) {
                $sql = "SELECT tb1.userid, tb2.admin_id FROM " . NV_USERS_GLOBALTABLE . " tb1
                LEFT JOIN " . NV_AUTHORS_GLOBALTABLE . " tb2 ON tb1.userid=tb2.admin_id
                WHERE tb1.username=" . $db->quote($_old_affiliate_code);
                $check_old = $db->query($sql)->fetch();
                if (empty($check_old) or empty($check['admin_id']) or !empty($check_old['admin_id'])) {
                    $set = true;
                }
            } else {
                $set = true;
            }
            if ($set) {
                $this->result->setSuccess();
                return $this->result->getResult();
            }
        }

        return $this->result->setCode('1001')->setMessage('No error but not set')->getResult();
    }
}
