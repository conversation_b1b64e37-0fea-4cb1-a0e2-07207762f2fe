<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UpdateMessenger implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'messenger';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];

        $row = [];
        $error = '';
        $error_code = '0000';
        $id = $nv_Request->get_int('id', 'post', '0');

        if ($id > 0) {
            $row_old = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_messenger WHERE id=' . $id)->fetch();
            if (empty($row_old)) {
                return $this->result->setError()
                    ->setCode('2004')
                    ->setMessage('Row not exit')
                    ->getResult();
            }

            $data = $nv_Request->get_array('data', 'post');
            if (!is_array($data)) {
                return $this->result->setError()
                    ->setCode('2000')
                    ->setMessage('Param data invalid to json')
                    ->getResult();
            }
            $row = $data;

            $keys_check = [
                'id',
                'hoten',
                'sdt',
                'sdt_text',
                'email',
                'thoi_gian_gui',
                'senderid',
                'updatetime'
            ];
            if (!empty($data)) {
                // check $field
                $array_sql = [];
                foreach ($data as $key => $value) {
                    if (!in_array($key, $keys_check)) {
                        return $this->result->setError()
                            ->setCode('2002')
                            ->setMessage('Missing field ' . $key . ' in otherdata')
                            ->getResult();
                    }
                    $array_sql[$key] = $key . '=:' . $key;
                }

                if ($row['hoten'] == '') {
                    $error_code = '2003';
                    $error = $nv_Lang->getModule('error_required_name');
                } else if ($row['sdt_text'] == '' and $row['email'] == '') {
                    $error_code = '2004';
                    $error = $nv_Lang->getModule('error_required_phone');
                }
                if (!phonecheck($row['sdt'])) {
                    $row['sdt'] = '';
                }

                $email = nv_check_valid_email($row['email'], true);
                if (empty($email[0])) {
                    $row['email'] = $email[1];
                } else {
                    $row['email'] = '';
                }

                if (empty($error)) {
                    try {
                        $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . $module_data . '_messenger SET ' . implode(',', $array_sql) . ', updatetime=' . NV_CURRENTTIME . '  WHERE id=' . $id);

                        foreach ($row as $key => $value) {
                            $stmt->bindValue(':' . $key, $value, PDO::PARAM_STR);
                        }
                        $exc = $stmt->execute();
                        if ($exc) {
                            // ghi log
                            $this->result->setSuccess();
                            $this->result->set('ID', $id);
                        }
                    } catch (PDOException $e) {
                        $this->result->setError()
                            ->setCode('3000')
                            ->setMessage(print_r($e, true));
                    }
                } else {
                    return $this->result->setError()
                        ->setCode($error_code)
                        ->setMessage($error)
                        ->getResult();
                }
            } else {
                return $this->result->setError()
                    ->setCode('2001')
                    ->setMessage('Param data empty')
                    ->getResult();
            }
        } else {
            return $this->result->setError()
                ->setCode('2003')
                ->setMessage('ID is integer, more than 0' . $id)
                ->getResult();
        }

        return $this->result->getResult();
    }
}
