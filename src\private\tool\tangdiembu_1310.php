<?php
define('NV_SYSTEM', true);

// define('NV_ROOTDIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME) . '/../public_html')));
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

require NV_ROOTDIR . '/includes/mainfile.php';

$last_userid = 0;
$filelog = NV_ROOTDIR . '/tool/tangdiembu_1310.txt';
if (file_exists($filelog)) {
    $last_userid = intval(file_get_contents($filelog));
}
// 1697130000; //0h ngày 13/10/2023
// 1698080399; //23h59'59 ngày 23/10/2023

//L<PERSON>y tất cả userid đã đăng ký vip trong thời gian cần check (dữ liệu chỉ khoảng 50 dòng)
$sql = "SELECT userid FROM `nv4_vi_bidding_orders_general` WHERE userid > " . $last_userid . " AND siteid = 1 AND vip != 19 AND vip != 99 AND add_time > 1697130000 AND add_time <= 1698080399 ORDER BY userid ASC LIMIT 200";
$arr_userid = $db->query($sql)->fetchAll(PDO::FETCH_COLUMN);
$new_userid = end($arr_userid);
if (empty($arr_userid)) {
    echo "Đã chạy hết!!";
    exit(1);
} else {
    //Lấy những userid đã tặng điểm, và số điểm được tặng
    $query_users_tmp = $db->query("SELECT userid, givepoints FROM nv4_points_users_tmp WHERE (type = 105 OR type = 108) AND userid IN(" . implode(', ', array_unique($arr_userid)) . ")");
    $arr_count_userid = [];
    while ($user_tmp = $query_users_tmp->fetch()) {
        $arr_count_userid[$user_tmp['userid']] = !isset($arr_count_userid[$user_tmp['userid']]) ? $user_tmp['givepoints'] : $arr_count_userid[$user_tmp['userid']] + $user_tmp['givepoints'];
    }

    $arr_tmp = [];
    foreach ($arr_userid as $userid) {
        //Mỗi lần đăng ký 1 gói vip thì cộng 500đ
        $arr_tmp[$userid] += 500;
    }
    if (!empty($arr_tmp)) {
        $arr_sql = [];
        foreach ($arr_tmp as $userid => $point) {
            $checkpoint = $point;
            if (!empty($arr_count_userid[$userid]) && $arr_count_userid[$userid] > 0) {
                //TH khách đăng ký 2 gói vip, 1 gói được tặng còn 1 gói chưa được tặng thì sẽ vào đây
                //Kiểm tra xem đã tặng điểm chưa> nếu có thì trừ bỏ số điểm đã tặng
                $checkpoint = $point - $arr_count_userid[$userid];
                $checkpoint = $checkpoint > 0 ? $checkpoint : 0;
            }
            if ($checkpoint > 0) {
                //Nếu còn cần cộng bù điểm thì vào đây
                echo "#U " . $userid . "\n";
                $new_userid = $userid;
                $arr_sql[] = "(1, " . $userid . ", 0, 108, 0, " . NV_CURRENTTIME . ", " . $checkpoint . ", 'Tặng bù điểm ngày Doanh nhân Việt Nam 13/10/2023', 'Tặng bù điểm ngày Doanh nhân Việt Nam 13/10/2023', 1701363599, '')";
            }
        }
        if (!empty($arr_sql)) {
            $sql = "INSERT INTO nv4_points_users_tmp (site_id, userid, pre_uid, type, status, addtime, givepoints, givemessage, givelog, giveexpired, key_crawl) VALUES " . implode(',', $arr_sql);
            $exec = $db->exec($sql);
        }
    }
}
if ($new_userid > $last_userid) {
    file_put_contents($filelog, $new_userid);
}