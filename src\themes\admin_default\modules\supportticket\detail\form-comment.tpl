<!-- BEGIN: main -->
<div class="">
    <form id="commentForm_{COMMENT_PARENT_ID}" class="form-horizontal" action="" method="post" enctype="multipart/form-data">
        <input hidden name="comment_token" value="{TOKEN}">
        <input hidden name="comment_parent_id" value="{COMMENT_PARENT_ID}">
        <input hidden name="comment_type" value="{COMMENT_TYPE}">

        <div class="form-group">
            <label class="col-md-24"><strong>{TITLE_REPLY} <span class="text-danger">(*)</span></strong></label>
            <div class="col-md-24">
                <textarea class="form-control" id="comment_reply_{COMMENT_PARENT_ID}" rows="7" name="comment_reply" data-editor="true" style="width: 100%"></textarea>
            </div>
        </div>
        <div class="form-group comment-form-input">
            <!-- BEGIN: expert_offer_point -->
            <div class="notify">
                <p>{LANG.notify_read_expert_comment} <span class="number_expert">
                    <input type="number" name="point_quote" min="0" value="{MIN_POINT_EXPERT}" id="point_quote_{COMMENT_PARENT_ID}"></span> {LANG.point}
                </p>
            </div>
            <!-- END: expert_offer_point -->
            <div class="form-group">
                <button type="submit" class="btn btn-primary" id="submitComment">
                    <i class="fa fa-paper-plane" aria-hidden="true"></i> {LANG.send}
                </button>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    // Kiểm tra xem CKEditor đã được tải chưa
    if (typeof CKEDITOR !== 'undefined') {
        let tagID = 'comment_reply_{COMMENT_PARENT_ID}';
        CKEDITOR.replace(tagID, {
            width: '100%',
            height: 200,
            toolbar: [
                { name: 'insert', items: ['Table'] },
                { name: 'tools', items: ['Maximize', 'ShowBlocks'] }
            ],
            toolbarLocation: 'top',
            removePlugins: 'elementspath,resize,clipboard', // Xóa clipboard mặc định
            extraPlugins: 'table,tabletools,pastefromword', // Gộp các plugin bổ sung
            allowedContent: true, // Cho phép tất cả thẻ HTML
            extraAllowedContent: 'table tr td th tbody thead;*{*}[*]', // Thêm các thẻ và thuộc tính tùy chỉnh
            on: {
                instanceReady: function(e) {
                    CKEDITOR.instances[tagID].setData(content);
                }
            }
        });
        // Đánh dấu rằng editor đã được khởi tạo
        $('#' + tagID).data('editor', true);
    } else {
        // CKEditor 5
        (async () => {
            await ClassicEditor
            .create(document.getElementById("comment_reply_{COMMENT_PARENT_ID}"), {
                language: '{NV_LANG_INTERFACE}',
                removePlugins: ["NVBox"],
                image: {insert: {integrations: ["url"]}},
                nvmedia: {insert: {integrations: ["url"]}},
                toolbar: {
                    items: [
                        'undo','redo','selectAll','|',
                        'link','imageInsert','nvmediaInsert','insertTable','code','codeBlock','horizontalLine','specialCharacters','pageBreak','|',
                        'findAndReplace','showBlocks','|',
                        'bulletedList','numberedList','outdent','indent','blockQuote','heading','fontSize','fontFamily',
                        'fontColor','fontBackgroundColor','highlight','alignment','|',
                        'bold','italic','underline','strikethrough','subscript','superscript','|',
                        'sourceEditing','restrictedEditingException','removeFormat'
                    ],
                    shouldNotGroupWhenFull: false
                }
            })
            .then(editor => {
                window.nveditor = window.nveditor || [];
                window.nveditor["comment_reply_{COMMENT_PARENT_ID}"] = editor;
                if (editor.sourceElement && editor.sourceElement instanceof HTMLTextAreaElement && editor.sourceElement.form) {
                    editor.sourceElement.dataset.editorname = "comment_reply_{COMMENT_PARENT_ID}";
                    editor.sourceElement.form.addEventListener("submit", event => {
                        // Xử lý khi submit form thông thường
                        editor.sourceElement.value = editor.getData();
                    });
                }
            })
            .catch(error => {
                console.error(error);
            });
        })();
    }
    $(document).ready(function() {
        $('#commentForm_{COMMENT_PARENT_ID}').on('submit', function(e) {
            e.preventDefault();

            var submitComment = $('#submitComment');
            var commentTextarea = $('#comment_reply_{COMMENT_PARENT_ID}');
            var tagID = 'comment_reply_{COMMENT_PARENT_ID}';
            if (typeof CKEDITOR !== 'undefined' && CKEDITOR.instances[tagID]) {
                // Lấy nội dung từ CKEditor 4
                commentTextarea.val(CKEDITOR.instances[tagID].getData());
            } else if (window.nveditor && window.nveditor[tagID]) {
                // Lấy nội dung từ CKEditor 5
                commentTextarea.val(window.nveditor[tagID].getData());
            }

            var commentReply = commentTextarea.val().trim();
            if (!commentReply) {
                alert('{LANG.notify_empty_content}');
                return;
            }
            var pointQuote = $('#point_quote_{COMMENT_PARENT_ID}').val();
            if (pointQuote < 0) {
                alert('{LANG.error_point_quote}');
                return;
            }
            // disable button submit đi tránh trường hợp bấm nhầm spam nhiều câu trả lời
            submitComment.prop('disabled', true);

            var formData = $(this).serialize();
            var actionUrl = $('#detail_paid_form_action').attr('action');
            var ratingToken = $('#token_selection').val();
            $.ajax({
                url: actionUrl,
                method: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message);
                        submitComment.prop('disabled', false);
                    }
                },
                error: function(xhr, status, error) {
                    alert(response.message);
                    submitComment.prop('disabled', false);
                }
            });
        });
    });
</script>
<style>
    .ck-editor__editable_inline {
        min-height: 200px;
    }
</style>
<!-- END: main -->
