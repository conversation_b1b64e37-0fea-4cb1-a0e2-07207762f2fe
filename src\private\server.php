<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (!defined('NV_IS_CONSOLE')) {
    die('Stop!!!');
}

$_SERVER['HTTPS'] = 'on';
$_SERVER['HTTP_HOST'] = 'id.dauthau.net';
$_SERVER['SERVER_NAME'] = 'id.dauthau.net';
$_SERVER['SERVER_PROTOCOL'] = 'http';
$_SERVER['SERVER_PORT'] = '443';

if (NV_CONSOLE_DIR == '/home/<USER>/private') {
    define('NV_ROOTDIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME) . '/../public_html')));
    $_SERVER['HTTP_HOST'] = 'errorlog.dauthau.net';
    $_SERVER['SERVER_NAME'] = 'errorlog.dauthau.net';
} elseif (NV_CONSOLE_DIR == '/home/<USER>/private') {
    define('NV_ROOTDIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME) . '/../public_html')));
} else {
    define('NV_ROOTDIR', str_replace(DIRECTORY_SEPARATOR, '/', realpath(pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME) . '/..')));
}

$_SERVER['HTTP_CLIENT_IP'] = '127.0.0.1';
$_SERVER['REQUEST_URI'] = '';
$_SERVER['PHP_SELF'] = '';
$_SERVER['HTTP_USER_AGENT'] = 'Local shell';

$_GET['language'] = 'vi';

// Sitemap cho sitemap index
$sitemapIndexHeader = '<?xml version="1.0" encoding="UTF-8"?><?xml-stylesheet type="text/xsl" href="https://dauthau.net/assets/css/sitemapindex.xsl"?><sitemapindex xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/siteindex.xsd" xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"></sitemapindex>';
// Sitemap cho sitemap con
$sitemapHeader = '<?xml version="1.0" encoding="UTF-8"?><?xml-stylesheet type="text/xsl" href="https://dauthau.net/assets/css/sitemap.xsl"?><urlset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd" xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"></urlset>';

/**
 *
 * @param double $start
 * @param double $end
 * @return string
 */
function getConsoleExecuteTime($start, $end)
{
    $times = ($end - $start) * 1000;
    if ($times < 1000) {
        return (intval($times) . 'ms');
    }
    $times = intval($times / 1000);
    return nv_convertfromSec($times);
}
