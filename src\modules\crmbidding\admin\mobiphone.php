<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$row = [];
$error = [];

$q = $nv_Request->get_title('q', 'post,get');
$showheader = $nv_Request->get_int('showheader', 'post,get', 1);
$id = $nv_Request->get_int('id', 'post,get', 0);
$time_from = nv_substr($nv_Request->get_title('from', 'get, post', ''), 0, 10);
$time_to = nv_substr($nv_Request->get_title('to', 'get, post', ''), 0, 10);
// Fetch Limit
$show_view = false;
$show_view = true;
$per_page = 20;
$page = $nv_Request->get_int('page', 'post,get', 1);
$db->sqlreset()
    ->select('COUNT(*)')
    ->from('' . NV_PREFIXLANG . '_' . $module_data . '_mobiphone');
$where = array();
if (!empty($q)) {
    $where[] = 'phieu_ghi like :q_phieu_ghi OR sdt LIKE :q_sdt OR nhanh LIKE :q_nhanh OR chuyen_vien LIKE :q_chuyen_vien';
}

if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $time_from, $m)) {
    $time_from = mktime(0, 0, 0, $m[2], $m[1], $m[3]);
}

if (preg_match("/^([0-9]{1,2})\/([0-9]{1,2})\/([0-9]{4})$/", $time_to, $m)) {
    $time_to = mktime(24, 0, 0, $m[2], $m[1], $m[3]);
}

if ($id > 0) {
    $where[] = 'call_id= ' . $id;
} else if (!empty($time_from)) {
    $where[] = "thoi_gian_bat_dau >= " . $time_from;
} else if (!empty($time_to)) {
    $where[] = "thoi_gian_bat_dau <= " . $time_to;
}

if (!empty($where)) {
    $db->where(implode(' AND ', $where));
}
$sth = $db->prepare($db->sql());

if (!empty($q)) {
    $sth->bindValue(':q_phieu_ghi', '%' . $q . '%');
    $sth->bindValue(':q_sdt', '%' . $q . '%');
    $sth->bindValue(':q_nhanh', '%' . $q . '%');
    $sth->bindValue(':q_chuyen_vien', '%' . $q . '%');
}
$sth->execute();
$num_items = $sth->fetchColumn();

$db->select('*')
    ->order('thoi_gian_bat_dau DESC')
    ->limit($per_page)
    ->offset(($page - 1) * $per_page);
$sth = $db->prepare($db->sql());

if (!empty($q)) {
    $sth->bindValue(':q_phieu_ghi', '%' . $q . '%');
    $sth->bindValue(':q_sdt', '%' . $q . '%');
    $sth->bindValue(':q_nhanh', '%' . $q . '%');
    $sth->bindValue(':q_chuyen_vien', '%' . $q . '%');
}
$sth->execute();

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('MODULE_UPLOAD', $module_upload);
$xtpl->assign('NV_ASSETS_DIR', NV_ASSETS_DIR);
$xtpl->assign('OP', $op);
$xtpl->assign('ROW', $row);
$xtpl->assign('Q', $q);
if (!empty($time_from)) {
    $xtpl->assign('FROM_TIME', nv_date('d/m/Y', $time_from));
}

if (!empty($time_to)) {
    $xtpl->assign('TO_TIME', nv_date('d/m/Y', $time_to));
}

if ($show_view) {
    $base_url = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op;
    if (!empty($q)) {
        $base_url .= '&q=' . $q;
    }

    if (!empty($time_from)) {
        $base_url .= '&from=' . nv_date('d/m/Y', $time_from);
    }

    if (!empty($time_to)) {
        $base_url .= '&to=' . nv_date('d/m/Y', $time_to);
    }

    $generate_page = nv_generate_page($base_url, $num_items, $per_page, $page);
    if (!empty($generate_page)) {
        $xtpl->assign('NV_GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
    }
    $number = $page > 1 ? ($per_page * ($page - 1)) + 1 : 1;
    while ($view = $sth->fetch()) {
        $view['number'] = $number++;
        // $view['link_view'] = NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=leads_info&amp;id=' . $view['id'];
        $view['thoi_gian_bat_dau'] = nv_date('H:i:s d/m/Y', $view['thoi_gian_bat_dau']);
        $view['thoi_gian_ket_thuc'] = nv_date('H:i:s d/m/Y', $view['thoi_gian_ket_thuc']);
        $view['loai_cuoc_goi'] = $view['loai_cuoc_goi'] == 0 ? "Gọi ra" : "Gọi vào";
        $view['trang_thai_cuoc_goi'] = $view['trang_thai_cuoc_goi'] == 0 ? "Gọi nhỡ" : "Gọi gặp";
        $view['trang_thai_ket_thuc'] = $view['trang_thai_ket_thuc'] == 0 ? "System" : "Khách hàng";

        if ($view['link_s3'] != '' and preg_match('/^20/', $view['link_s3'], $m)) {
            $view['link'] = 'https://s3.ap-southeast-1.amazonaws.com/dauthau.asia/uploads/mobiphone/' . $view['link_s3'];
        }
        $xtpl->assign('VIEW', $view);

        if ($view['link'] != '') {
            $xtpl->parse('main.loop.recording');
        }
        $xtpl->parse('main.loop');
    }
}

if ($showheader) {
    $xtpl->parse('main.form');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

$page_title = $nv_Lang->getModule('mobiphone');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents, $showheader);
include NV_ROOTDIR . '/includes/footer.php';
