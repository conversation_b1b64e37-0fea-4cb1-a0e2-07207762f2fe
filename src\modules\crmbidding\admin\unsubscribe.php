<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Wed, 25 Sep 2019 08:46:46 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}
use NukeViet\Api\DoApi;

$error = array();
$id = $nv_Request->get_int('id', 'post,get', 0);
$type = $nv_Request->get_int('type', 'post,get', 1);

session_write_close();// Đến đây Session vẫn có sẵn để đọc, nhưng không thể ghi thêm được
$file_log = NV_ROOTDIR . '/data/logs/unsubscribe/' . $admin_info['username'] . '.log';
file_put_contents($file_log, date('H:i:s') . "\t" . $op . "\t" . $id . "\t" . $type . "\n", FILE_APPEND);

if ($type == 1) {
    $params_leads = [
        'leadid' => $id
    ];
    $data_leads = nv_local_api('GetDetailLeads', $params_leads, $admin_info['username'], $module_name);

    $data = json_decode($data_leads, true);
    $row = $data['data'];
    if (empty($row)) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=leads&showheader=' . $showheader);
    }
} elseif ($type == 2) {
    $params = [
        'opportunitiesid' => $id
    ];
    $ListAllOpportunities = nv_local_api('GetDetailOpportunities', $params, $admin_info['username'], $module_name);
    $ListAllOpportunities = json_decode($ListAllOpportunities, true);
    $row = $ListAllOpportunities['data'];
    if (empty($row)) {
        nv_redirect_location(NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&' . NV_NAME_VARIABLE . '=' . $module_name . '&' . NV_OP_VARIABLE . '=opportunities&showheader=' . $showheader);
    }
}

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);

$send_mail = array();
if ($row['sub_email'] != '') {
    $send_mail = explode(',', $row['sub_email']);
}
array_push($send_mail, $row['email']);
$send_mail = array_unique($send_mail);
$num = 0;

// AI tư vấn đấu thầu
// chuẩn hóa số điện thoại
$_tmp_phone = $row['phone'];
if (preg_match('/(\d{9})$/', $row['phone'], $m)) {
    $_tmp_phone = $m[0];
}
$_tmp_phone = preg_replace('/[^0-9]/', '', $_tmp_phone);
$_tmp_phone = (int) $_tmp_phone;
$ai_record = $zalo_record = $chatgpt_record = [];
$sql_ai = "SELECT tb1.*, tb2.first_name, tb2.last_name, tb2.username, tb2.userid, tb2.email, tb3.phone
        FROM " . $db_config['prefix'] . "_supportticket_row tb1
        LEFT JOIN " . NV_USERS_GLOBALTABLE . " tb2 ON tb2.userid = tb1.customer_id
        LEFT JOIN " . NV_USERS_GLOBALTABLE . "_info tb3 ON tb2.userid = tb3.userid";
$where = [];
if (!empty($row['user_id'])) {
    $where[] = "tb1.customer_id = " . $row['user_id'] . "";
}
if (!empty($row['email'])) {
    $where[] = "email = " . $db->quote($row['email']) . "";
}
if (!empty($row['sub_email'])) {
    $subEmail = array_map('trim', explode(',', $row['sub_email']));
    foreach ($subEmail as $value) {
        $where[] = "email = " . $db->quote($value) . "";
    }
}
if (!empty($row['phone'])) {
    $where[] = "phone LIKE " . $db->quote('%' . $_tmp_phone . '');
}
if (!empty($row['sub_phone'])) {
    foreach ($tmp_sub_phone as $key => $value) {
        $where[] = "phone LIKE (" . $db->quote('%' . $value . '') . ") ";
    }
}
if (!empty($where)) {
    $sql_ai .= " WHERE (" . implode(' OR ', $where) . ") AND tb1.cat_id = 4";
    $result = $db->query($sql_ai);
    while ($_row = $result->fetch()) {
        $ai_record[$_row['id']] = $_row;
    }
}
if (count($ai_record) > 0) {
    $xtpl->assign('NUM_AI', sprintf($nv_Lang->getModule('count_ai'), sizeof($ai_record)));
    foreach ($ai_record as $_ai) {
        if (!empty($row['phone']) and !empty($_ai['phone']) and (substr($_ai['phone'], -9) == substr($row['phone'], -9))) {
            $_ai['sdt'] = '<span class="red">' . $_ai['phone'] . '</span>';
        }
        if (!empty($row['sub_phone']) and !empty($_ai['phone']) and strpos($row['sub_phone'], $_ai['phone']) !== false) {
            $_ai['sdt'] = '<span class="red">' . $_ai['phone'] . '</span>';
        }
        if (!empty($row['email']) and !empty($_ai['email']) and $_ai['email'] == $row['email']) {
            $_ai['email'] = '<span class="red">' . $_ai['email'] . '</span>';
        }
        if (!empty($row['sub_email']) and !empty($_ai['email']) and strpos($row['sub_email'], $_ai['email']) !== false) {
            $_ai['email'] = '<span class="red">' . $_ai['email'] . '</span>';
        }
        $_ai['hoten'] = nv_show_name_user($_ai['first_name'], $_ai['last_name'], $_ai['username']);
        $_ai['thoi_gian'] = nv_date('H:i d/m/Y', $_ai['activity_time']);
        $_ai['link_detail'] = NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=supportticket&" . NV_OP_VARIABLE . '=' . 'ticket_detail&id=' . $_ai['id'];
        $xtpl->assign('AI_RECORD', $_ai);
        $xtpl->parse('main.ai_record.loop');
    }
    $xtpl->parse('main.ai_record');
}

// ChatGPT to records
$sql_gpt = "SELECT tb1.*, tb2.first_name, tb2.last_name, tb2.username, tb2.userid, tb2.email, tb3.phone
        FROM " . NV_PREFIXLANG . "_crmbidding_chatgpt_users tb1
        LEFT JOIN " . NV_USERS_GLOBALTABLE . " tb2 ON tb2.userid = tb1.userid
        LEFT JOIN " . NV_USERS_GLOBALTABLE . "_info tb3 ON tb2.userid = tb3.userid";
$where = [];
if (!empty($row['user_id'])) {
    $where[] = "tb1.userid = " . $row['user_id'] . "";
}
if (!empty($row['email'])) {
    $where[] = "email = " . $db->quote($row['email']) . "";
}
if (!empty($row['sub_email'])) {
    $subEmail = array_map('trim', explode(',', $row['sub_email']));
    foreach ($subEmail as $value) {
        $where[] = "email = " . $db->quote($value) . "";
    }
}
if (!empty($row['phone'])) {
    $where[] = "phone LIKE " . $db->quote('%' . $_tmp_phone . '');
}
if (!empty($row['sub_phone'])) {
    foreach ($tmp_sub_phone as $key => $value) {
        $where[] = "phone LIKE (" . $db->quote('%' . $value . '') . ") ";
    }
}
if (!empty($where)) {
    $sql_gpt .= " WHERE " . implode(' OR ', $where) . "";
    $result = $db->query($sql_gpt);
    while ($_row = $result->fetch()) {
        $chatgpt_record[$_row['id']] = $_row;
    }
}
if (sizeof($chatgpt_record) > 0) {
    $xtpl->assign('NUM_CHATGPT', sprintf($nv_Lang->getModule('count_chatgpt'), sizeof($chatgpt_record)));
    foreach ($chatgpt_record as $chat) {
        if (!empty($row['phone']) and !empty($chat['phone']) and (substr($chat['phone'], -9) == substr($row['phone'], -9))) {
            $chat['sdt'] = '<span class="red">' . $chat['phone'] . '</span>';
        }
        if (!empty($row['sub_phone']) and !empty($chat['phone']) and strpos($row['sub_phone'], $chat['phone']) !== false) {
            $chat['sdt'] = '<span class="red">' . $chat['phone'] . '</span>';
        }
        if (!empty($row['email']) and !empty($chat['email']) and $chat['email'] == $row['email']) {
            $chat['email'] = '<span class="red">' . $chat['email'] . '</span>';
        }
        if (!empty($row['sub_email']) and !empty($chat['email']) and strpos($row['sub_email'], $chat['email']) !== false) {
            $chat['email'] = '<span class="red">' . $chat['email'] . '</span>';
        }
        $chat['hoten'] = nv_show_name_user($chat['first_name'], $chat['last_name'], $chat['username']);
        $chat['thoi_gian_gui'] = nv_date('H:i d/m/Y', $chat['last_activity']);
        $chat['link_detail'] = NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=' . 'chatgpt_detail&id=' . $chat['id'];
        $xtpl->assign('CHATGPT', $chat);
        $xtpl->parse('main.chatgpt.loop');
    }
    $xtpl->parse('main.chatgpt');
}

// Zalo Record
$sql = "SELECT * FROM " . NV_PREFIXLANG . "_crmbidding_zalo";
$where = [];
if (!empty($row['email'])) {
    $where[] = "email = " . $db->quote($row['email']) . "";
}
if (!empty($row['sub_email'])) {
    $where[] = "email IN (" . $db->quote($row['sub_email']) . ") ";
}
if (!empty($row['phone'])) {
    $where[] = "sdt LIKE " . $db->quote('%' . $_tmp_phone . '');
}
if (!empty($row['sub_phone'])) {
    foreach ($tmp_sub_phone as $key => $value) {
        $where[] = "sdt LIKE (" . $db->quote('%' . $value . '') . ") ";
    }
}
if (!empty($where)) {
    $sql .= " WHERE " . implode(' OR ', $where) . "";
    $result = $db->query($sql);
    while ($_row = $result->fetch()) {
        $zalo_record[$_row['id']] = $_row;
    }
}

if (count($zalo_record) > 0) {
    $xtpl->assign('NUM_ZALO', sprintf($nv_Lang->getModule('count_zalo'), sizeof($zalo_record)));

    foreach ($zalo_record as $zalo) {
        if (!empty($row['phone']) and !empty($zalo['sdt']) and (substr($zalo['sdt'], -9) == substr($row['phone'], -9))) {
            $zalo['sdt'] = '<span class="red">' . $zalo['sdt'] . '</span>';
        }
        if (!empty($row['sub_phone']) and !empty($zalo['sdt']) and strpos($row['sub_phone'], $zalo['sdt']) !== false) {
            $zalo['sdt'] = '<span class="red">' . $zalo['sdt'] . '</span>';
        }
        if (!empty($row['email']) and !empty($zalo['email']) and $zalo['email'] == $row['email']) {
            $zalo['email'] = '<span class="red">' . $zalo['email'] . '</span>';
        }
        if (!empty($row['sub_email']) and !empty($zalo['email']) and strpos($row['sub_email'], $zalo['email']) !== false) {
            $zalo['email'] = '<span class="red">' . $zalo['email'] . '</span>';
        }
        $zalo['thoi_gian_gui'] = nv_date('H:i d/m/Y', $zalo['thoi_gian_gui']);
        $xtpl->assign('ZALO', $zalo);
        $xtpl->parse('main.zalo.loop');
    }
    $xtpl->parse('main.zalo');
}

// Hiển thị thông tin click banner
$arr_where['AND'][] = [
    '=' => [
        'c.userid' => $row['user_id']
    ]
];
$params = [
    'where' => $arr_where
];

$api = new DoApi(API_API_URL, API_API_KEY, API_API_SECRET);
$api->setModule('bannersdt')
    ->setLang('vi')
    ->setAction('ListUsersClickBanners')
    ->setData($params);
$result = $api->execute();

if ($result['status'] == 'success') {
    if (!empty($result['data'][$row['user_id']])) {

        // Sắp xếp theo timestamp mới nhất trước
        $arr_time_banner = $result['data'][$row['user_id']]['list_banners'];

        uasort($arr_time_banner, function ($a, $b) {
            if ($a['last_click_timestamp'] == $b['last_click_timestamp']) return 0;
            return ($a['last_click_timestamp'] > $b['last_click_timestamp']) ? -1 : 1;
        });
        foreach ($arr_time_banner as $banner) {
            $xtpl->assign('BANNER', $banner);
            $xtpl->parse('main.banner_click.loop');
        }
        $xtpl->parse('main.banner_click');
    }
}

if ($row['chatgpt_userid']) {
    $_chatgpt_userids = array_map([$db, 'quote'], explode(",", $row['chatgpt_userid']));
    $_str_ids = implode(",", $_chatgpt_userids);
    $_ids = $db->query("SELECT GROUP_CONCAT(id) FROM nv4_vi_crmbidding_chatgpt_users WHERE uniqueid IN (". $_str_ids .")")->fetchColumn();
    
    $query_url = $db->query("SELECT message_user, addtime, id FROM `nv4_vi_crmbidding_chatgpt` WHERE crmbidding_chatgpt_id IN (". $_ids .")");
    while ($rowchat = $query_url->fetch()) {
        $rowchat['addtime'] = date('H:i:s d/m/Y', $view['addtime']);
        $rowchat['detail_link'] = NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=' . 'chatgpt_detail&id=' . $rowchat['id'];
        $xtpl->assign('ROWCHAT', $rowchat);
        $xtpl->parse('main.chatgpt_activity.item');
    }
    $xtpl->parse('main.chatgpt_activity');
}

// Hiển thị các hoạt động khác của khách hàng (click xem điểm, ví tiền, nạp tiền, xem bảng giá, theo dõi tbmt, khlcnt, ...)
// Hiển thị các hoạt động khác của khách hàng giao dịch điểm
if (!empty($row['user_id'])) {
    $db->sqlreset()
            ->select('COUNT(*) as num')
            ->from($db_config['prefix'] . '_points_customs_static')
            ->where('userid =' . $row['user_id']);
    $_total_log_point = $db->query($db->sql())->fetchColumn();

    $db->select('date')->order('date DESC');
    $_date = $db->query($db->sql())->fetchColumn();
    if (!empty($_total_log_point)) {
        $points_dt = array (
            'point_transaction' => sprintf($nv_Lang->getModule('point_transaction'), $_total_log_point),
            'time_click' => date('d/m/Y', $_date),
            'link' => NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=points&amp;' . NV_OP_VARIABLE . '=transaction&amp;userid=' . $row['user_id']
        );
        $xtpl->assign('POINTS', $points_dt);
        $xtpl->parse('main.point_activity');
    }

    // Hiển thị các hoạt động khác của khách hàng giao dịch ví tiền
    $db->sqlreset()
            ->select('COUNT(*) as num')
            ->from($db_config['prefix'] . '_wallet_transaction')
            ->where('userid =' . $row['user_id']);
    $_total_log_wallet = $db->query($db->sql())->fetchColumn();

    $db->select('created_time')->order('created_time DESC');
    $_created_time = $db->query($db->sql())->fetchColumn();
    if (!empty($_total_log_wallet)) {
        $wallet_dt = array (
            'wallet_transaction' => sprintf($nv_Lang->getModule('wallet_transaction'), $_total_log_wallet, date('H:i:s d/m/Y', $_created_time)),
            'link' => NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=wallet&amp;' . NV_OP_VARIABLE . '=transaction&amp;userid=' . $row['user_id']
        );
        $xtpl->assign('WALLET', $wallet_dt);
        $xtpl->parse('main.wallet_activity');
    }
}
// Hiển thị các hoạt động khác của khách hàng (click xem các link bảng giá, các link về gói VIP)
$sql = "SELECT tb1.*, tb2.first_name, tb2.last_name, tb2.username, tb2.userid, tb2.email, tb3.phone
        FROM " . $db_config['prefix'] . "_history_activity_custom tb1
        LEFT JOIN " . NV_USERS_GLOBALTABLE . " tb2 ON tb2.userid = tb1.userid
        LEFT JOIN " . NV_USERS_GLOBALTABLE . "_info tb3 ON tb2.userid = tb3.userid";
$where = [];
if (!empty($row['user_id'])) {
    $where[] = "tb1.userid = " . $row['user_id'] . "";
}
if (!empty($row['email'])) {
    $where[] = "tb2.email = " . $db->quote($row['email']) . "";
}
if (!empty($row['sub_email'])) {
    $subEmail = array_map('trim', explode(',', $row['sub_email']));
    foreach ($subEmail as $value) {
        $where[] = "tb2.email = " . $db->quote($value) . "";
    }
}
if (!empty($row['phone'])) {
    $where[] = "tb3.phone LIKE " . $db->quote('%' . $_tmp_phone . '');
}
if (!empty($row['sub_phone'])) {
    foreach ($tmp_sub_phone as $key => $value) {
        $where[] = "tb3.phone LIKE (" . $db->quote('%' . $value . '') . ") ";
    }
}
if (!empty($where)) {
    $sql .= " WHERE (" . implode(' OR ', $where) . ") AND (tb1.type = 7 OR tb1.type = 9) AND tb1.status = 1";  
    $result = $db->query($sql);
    $_list_act = [];
    while ($_row = $result->fetch()) {
        $_list_act[$_row['userid']][$_row['type']]['total']++;
        $_list_act[$_row['userid']][$_row['type']]['url'][$_row['url']]['click']++;
        if ( !isset($_list_act[$_row['userid']][$_row['type']]['time_click_tmp']) || $_row['time_click'] > $_list_act[$_row['userid']][$_row['type']]['time_click_tmp'] ) {
            $_list_act[$_row['userid']][$_row['type']]['time_click_tmp'] = $_row['time_click'];
            $_list_act[$_row['userid']][$_row['type']]['time_click'] = $_row['time_click'];
            $_list_act[$_row['userid']][$_row['type']]['type'] = $_row['type'];            
        }
        if ( !isset($_list_act[$_row['userid']][$_row['type']]['url'][$_row['url']]['time_click_tmp']) || $_row['time_click'] > $_list_act[$_row['userid']][$_row['type']]['url'][$_row['url']]['time_click_tmp'] ) {
            $_list_act[$_row['userid']][$_row['type']]['url'][$_row['url']]['time_click_tmp'] = $_row['time_click'];
        }
        $history_activity = $_list_act[$_row['userid']];
    }
    if (!empty($history_activity)) {
        uasort($history_activity, function ($a, $b) {
            if ($a['time_click'] == $b['time_click']) return 0;
            return ($a['time_click'] > $b['time_click']) ? -1 : 1;
        });
        foreach ($history_activity as $_act) {
            if (!empty($_act['url'])) {            
                $_act['urls'] = '<ul class="logotherlists">';
                foreach ($_act['url'] as $link => $val) {
                    $_act['urls'] .= '<li><a href="' . $link . '" target="_blank">' . $link . '</a> - ' . $val['click'] . ' ' . $nv_Lang->getModule('times') . ' (' . $nv_Lang->getModule('time_nearest') . ' ' . nv_date('H:i:s d/m/Y', $val['time_click_tmp']) . ')</li>';
                }
                $_act['urls'] .= '</ul>';
            }
            $_act['time_click'] = nv_date('H:i:s d/m/Y', $_act['time_click']);            
            $_act['type_txt'] = sprintf($nv_Lang->getModule('type_view_' . $_act['type']), $_act['total'], $_act['time_click']);
            $xtpl->assign('ACTIVITY', $_act);
            $xtpl->parse('main.history_activity.loop');
        }
        $xtpl->parse('main.history_activity');
    }
}

if (defined('MARKETING_API_CRM_KEY')) {
    $NV_Http_sub = new NukeViet\Http\Http($global_config, NV_TEMP_DIR);
    $list_item_sub = [];
    $args_sub = [
        'headers' => [
            'Referer' => NV_MY_DOMAIN
        ],
        'body' => '',
        'timeout' => 10,
        'sslverify' => false,
        'decompress' => false
    ];
    $request_sub = [
        'apikey' => MARKETING_API_CRM_KEY,
        'apisecret' => MARKETING_API_CRM_SECRET,
        'action' => '',
        'module' => 'marketing',
        'email' => '',
        'language' => 'vi'
    ];
    
    //  Lấy thông tin hủy nhận tin của email
    $NV_Http_sub->reset();
    $request_sub['action'] = 'DetailUnsubscribe';
    foreach ($send_mail as $email) {
        $email = trim($email);
        if ($email == '') {
            continue;
        }
        $request_sub['email'] = $email;
        $args_sub['body'] = $request_sub;

        $responsive_sub = $NV_Http_sub->post(MARKETING_API_URL, $args_sub);
        if (is_array($responsive_sub) and empty(NukeViet\Http\Http::$error)) {
            $email_marketing_sub = !empty($responsive_sub['body']) ? json_decode($responsive_sub['body'], true) : [];
            if (!empty($email_marketing_sub) && ($email_marketing_sub['status'] == 'success') && !empty($email_marketing_sub['data'])) {
                $email_marketing_sub['data']['email'] = $email;
                $list_item_sub[] = $email_marketing_sub['data'];
            } else if ($email_marketing_sub['status'] == 'error') {
                $list_item_sub[] = [
                    'error' => [
                        'email' => $email,
                        'code' => $email_marketing_sub['code'],
                        'message' => $email_marketing_sub['message']
                    ]
                ];
            }
        }
    }

    //  Lấy thông tin báo cáo spam
    $NV_Http_sub->reset();
    $request_sub['action'] = 'GetComplaintEmail';
    foreach ($send_mail as $email) {
        $email = trim($email);
        if ($email == '') {
            continue;
        }
        $request_sub['email'] = $email;
        $args_sub['body'] = $request_sub;

        $responsive_sub = $NV_Http_sub->post(MARKETING_API_URL, $args_sub);
        if (is_array($responsive_sub) and empty(NukeViet\Http\Http::$error)) {
            $email_marketing_sub = !empty($responsive_sub['body']) ? json_decode($responsive_sub['body'], true) : [];
            if (!empty($email_marketing_sub) && ($email_marketing_sub['status'] == 'success')  && !empty($email_marketing_sub['data'])) {
                $email_marketing_sub['data']['is_complaint'] = 1;
                $email_marketing_sub['data']['email'] = $email;
                $list_item_sub[] = $email_marketing_sub['data'];
            } else if ($email_marketing_sub['status'] == 'error') {
                $list_item_sub[] = [
                    'error' => [
                        'email' => $email,
                        'code' => $email_marketing_sub['code'],
                        'message' => $email_marketing_sub['message']
                    ]
                ];
            }
        }
    }

    // Lấy data email được gửi tới KH mà khách 
    $NV_Http_sub->reset();
    $request_sub['action'] = 'GetEmailSentOfEmail';
    foreach ($send_mail as $email) {
        $num_email = 0;     
        $list_campaign = [];
        $list_userid = [];
        $email_marketing = [];
        $email = trim($email);
        if ($email == '') {
            continue;
        }
        $request_sub['email'] = $email;
        $request_sub['page'] = 1;
        $request_sub['per_page'] = 100;
        $args_sub['body'] = $request_sub;

        $responsive_sub = $NV_Http_sub->post(MARKETING_API_URL, $args_sub);             
        if (is_array($responsive_sub) and empty(NukeViet\Http\Http::$error)) {
            $email_marketing = !empty($responsive_sub['body']) ? json_decode($responsive_sub['body'], true) : [];
            if (!empty($email_marketing) and $email_marketing['status'] == 'success') {  
                foreach ($email_marketing['rows'] as $item) {
                    if ($item['is_read'] == 1) {
                        $num_email++;
                        $list_campaign[$item['campaign']]['data'][] = $item;
                        $list_userid[$item['userid']] = $item['userid'];                        
                    }
                }
            } else if ($email_marketing['status'] == 'error') {
                $list_item_sub[] = [
                    'error' => [
                        'email' => $email,
                        'code' => $email_marketing['code'],
                        'message' => $email_marketing['message']
                    ]
                ];
            }
        }

        if (!empty($email_marketing) and $email_marketing['status'] == 'success' && !empty($list_campaign)) {
            // Lấy ds username theo user ID
            $list_userid = implode(',', $list_userid);
            $list_username = array();
            $sql = "SELECT userid, username  FROM " . NV_USERS_GLOBALTABLE . " WHERE userid IN (" . $db->quote($list_userid) . ")";
            $result = $db->query($sql);
            while ($_row = $result->fetch()) {
                $list_username[$_row['userid']] = $_row['username'];
            }
            $xtpl->assign('EMAIL', $email);
            $xtpl->assign('TOTAL', $num_email);
            foreach ($list_campaign as $key => $campaign) {
                $xtpl->assign('NUM_CAM', sizeof($campaign['data']));
                $xtpl->assign('NAME_CAM', $key);
                foreach ($campaign['data'] as $item) {
                    $item['userid'] = $list_username[$item['userid']];
                    $item['status'] = !empty($status_send[$item['status']]) ? $status_send[$item['status']] : '';
                    $item['read'] = 'Đã đọc ' . $item['open_count'] . ' lần, lần đầu: ' . nv_date('H:i d/m/Y', $item['date_read']) . ', lần cuối: ' . nv_date('H:i d/m/Y', $item['last_opened']);
                        
                    if (!empty($item['tracking_urls'])) {
                        $item['urls'] = '[';
                        foreach ($item['tracking_urls'] as $link) {
                            $item['urls'] .= ' <a href="' . $link['url'] . '" target="_blank">' . $link['url'] . '</a> - ' . $link['hits'] . ' hits </br>';
                        }
                        $item['urls'] .= ']';
                    } else {
                        $item['urls'] = 'None';
                    }
    
                    $xtpl->assign('ITEM', $item);
                    $xtpl->parse('main.marketing_email_read.campaign.loop');
                }
                $xtpl->assign('CAMP_TITLE', sprintf($nv_Lang->getModule('camp_title'), sizeof($campaign['data'])));
                $xtpl->parse('main.marketing_email_read.campaign');
            }
    
            $xtpl->assign('NUM_MAIL', $num_email);
            $xtpl->assign('TOTAL_MAIL', sprintf($nv_Lang->getModule('total_email'), $num_email, $email));
            $xtpl->parse('main.marketing_email_read');
        } else if ($email_marketing['status'] == 'error') {
            $xtpl->assign('ERROR_CODE', $email_marketing['code']);
            $xtpl->assign('ERROR_MESS', $email_marketing['message']);
            $xtpl->parse('main.marketing_read_api_error');
        }
    }

    $title_complaint = [
        'unsub_title' => '',
        'unsub_system_name' => $nv_Lang->getModule('from_system_name'),
        'unsub_date_added' => $nv_Lang->getModule('spam_date_add'),
        'unsub_reason' => '',
        'unsub_campaign' => $nv_Lang->getModule('from_spam_campaign'),
        'unsub_template' => $nv_Lang->getModule('from_spam_template'),
        'unsub_show_name' => $nv_Lang->getModule(''),
        'unsub_comments' => '',
    ];

    $title_destroy = [
        'unsub_title' => $nv_Lang->getModule('unsub_title'),
        'unsub_system_name' => $nv_Lang->getModule('unsub_system_name'),
        'unsub_date_added' => $nv_Lang->getModule('unsub_date_added'),
        'unsub_reason' => $nv_Lang->getModule('unsub_reason'),
        'unsub_campaign' => $nv_Lang->getModule('unsub_campaign'),
        'unsub_template' => $nv_Lang->getModule('unsub_template'),
        'unsub_show_name' => $nv_Lang->getModule('unsub_show_name'),
        'unsub_comments' => $nv_Lang->getModule('unsub_comments'),
    ];
    foreach ($list_item_sub as $item_sub) {
        $num++;
        $email = $email_marketing_sub['data']['email'];
        if (empty($item_sub['error']) || empty($item_sub['error']['code'])) {
            $all_system_destroy = false;
            $title_unsub = $item_sub['is_complaint'] ? $title_complaint : $title_destroy;
            if (empty($item_sub['system_name'])) {
                /**
                 * Có trường hợp admin vào add hủy nhận tin thủ công (vì khách phàn nàn trực tiếp đến sale)
                 * => Lúc đó system_id = 0 được hiểu là hủy tất
                 */
                $item_sub['system_name'] = $nv_Lang->getModule('all_system');
                $title_unsub['unsub_title'] = $nv_Lang->getModule('admin_destroy_email');
                $all_system_destroy = true;
            }
            $title_unsub['unsub_title'] .= ' ' . $email;
            if ($item_sub['is_complaint']) {
                $title_unsub['unsub_title'] = sprintf($nv_Lang->getModule('report_email_spam'), '<EMAIL>');
            }

            $xtpl->assign('TITLE', $title_unsub);
            $xtpl->assign('MAIL', $email);
            $xtpl->assign('NUM', $num);
            $item_sub['date_added'] = nv_date('H:i d/m/Y', $item_sub['date_added']);
            $xtpl->assign('USER', $item_sub['direct_user']);
            if (!empty($item_sub['direct_user'])  && !$item_sub['is_complaint']) {
                $xtpl->parse('main.email.marketing_email_sub.direct_user');
            }
            if (strlen(strstr($item_sub['campaign'], $nv_Lang->getModule('dauthau.info'))) > 0) {
                $item_sub['campaign'] = str_replace($nv_Lang->getModule('dauthau.info'), '<a href="' . URL_DTINFO . '" target="_blank">' . $nv_Lang->getModule('dauthau.info') . '</a>', $item_sub['campaign']);
            }
            if (strlen(strstr($item_sub['template'], $nv_Lang->getModule('dauthau.info'))) > 0) {
                $item_sub['template'] = str_replace($nv_Lang->getModule('dauthau.info'), '<a href="' . URL_DTINFO . '" target="_blank">' . $nv_Lang->getModule('dauthau.info') . '</a>', $item_sub['template']);
            }
            if (strlen(strstr($item_sub['campaign'], $nv_Lang->getModule('dauthau.net'))) > 0) {
                $item_sub['campaign'] = str_replace($nv_Lang->getModule('dauthau.net'), '<a href="' . URL_DTNET . '" target="_blank">' . $nv_Lang->getModule('dauthau.net') . '</a>', $item_sub['campaign']);
            }
            if (strlen(strstr($item_sub['template'], $nv_Lang->getModule('dauthau.net'))) > 0) {
                $item_sub['template'] = str_replace($nv_Lang->getModule('dauthau.net'), '<a href="' . URL_DTNET . '" target="_blank">' . $nv_Lang->getModule('dauthau.net') . '</a>', $item_sub['template']);
            }
            $xtpl->assign('ITEM', $item_sub);
            if ($item_sub['is_complaint']) {
                $xtpl->parse('main.email.marketing_email_sub.from_email');
            }
            if (!empty($item_sub['reason']) && !$item_sub['is_complaint']) {
                $xtpl->parse('main.email.marketing_email_sub.reason');
            }
            if (!empty($item_sub['campaign']) && !$all_system_destroy) {
                $xtpl->parse('main.email.marketing_email_sub.campaign');
            }
            if (!empty($item_sub['template'])  && !$all_system_destroy) {
                $xtpl->parse('main.email.marketing_email_sub.template');
            }
            if (!empty($item_sub['comments'])  && !$item_sub['is_complaint'] && !$all_system_destroy) {
                $xtpl->parse('main.email.marketing_email_sub.comments');
            }
            $xtpl->parse('main.email.marketing_email_sub');
        } else if (!empty($item_sub['error']) && !empty($item_sub['error']['code'])) {
            $xtpl->assign('ERROR_EMAIL', $item_sub['error']['email']);
            $xtpl->assign('ERROR_CODE', $item_sub['error']['code']);
            $xtpl->assign('ERROR_MESS', $item_sub['error']['message']);
            $xtpl->parse('main.email.marketing_api_error');
        }
        $xtpl->parse('main.email');
    }
}

// Lấy thông tin spam tại elastic search
// Kiểm tra xem có ở ElasticSearh không
$list_elastic_search = [];
foreach ($send_mail as $email) {
    $res_elastic = '';
    $search_elastic = array();
    $search_elastic['must_not'] = [
        "term" => [
            "complaint.keyword" => ""
        ]
    ];
    $search_elastic['must'] = [
        "term" => [
            "main_mail.keyword" => $email
        ]
    ];

    $array_query_elastic = array();
    if (!empty($search_elastic)) {
        $array_query_elastic['query']['bool'] = $search_elastic;
    }

    $array_query_elastic['track_total_hits'] = 'false';
    $array_query_elastic['size'] = 1;
    $array_query_elastic['sort'] = [
        [
            "addtime" => [
                "order" => "desc"
            ]
        ]
    ];
    $array_query_elastic['_source'] = array(
        'id',
        'complaint',
        'addtime'
    );
    // kết nối tới ElasticSearh
    $nukeVietElasticSearh = new NukeViet\ElasticSearch\Functions($module_config[$module_name]['elas_host'], $module_config[$module_name]['elas_port'], 'dauthau_mail', $module_config[$module_name]['elas_user'], $module_config[$module_name]['elas_pass']);
    $response = $nukeVietElasticSearh->search_data(NV_PREFIXLANG . '_' . $module_data . '_mail', $array_query_elastic);

    $value = (!empty($response['hits']['hits'])) ? $response['hits']['hits'][0] : [];
    $view = (!empty($value) && !empty($value['_source'])) ? $value['_source'] : [];
    $complaint = (array) json_decode($view['complaint'], true);
    if (!empty($complaint)) {
        $_complaint_i = isset($complaint['complainedRecipients']) && !empty($complaint['complainedRecipients']) ? $complaint['complainedRecipients'][0] : [];
        $timestamp = !empty($_complaint_i['timestamp']) ? $_complaint_i['timestamp'] : '';
        $list_elastic_search[] = [
            'email' => $email,
            'timestamp' => $timestamp,
            'addtime' => $view['addtime']
        ];
    }
}
$title_unsub = [];
$title_unsub['unsub_title'] = sprintf($nv_Lang->getModule('report_email_spam'), '<EMAIL>');
$title_unsub['unsub_system_name'] = $nv_Lang->getModule('from_system_name');
$title_unsub['unsub_date_added'] = $nv_Lang->getModule('spam_date_add');

foreach ($list_elastic_search as $val) {
    $num++;
    $email = $val['email'];
    $xtpl->assign('TITLE', $title_unsub);
    $xtpl->assign('MAIL', $email);
    $xtpl->assign('NUM', $num);
    $item = [];
    $item['system_name'] = 'ES mail';
    $item['date_added'] = date('H:i d/m/Y', empty($val['timestamp']) ? $val['addtime'] : strtotime($val['timestamp']));
    $xtpl->assign('ITEM', $item);
    $xtpl->parse('main.email.elastic_search_email_sub');
    $xtpl->parse('main.email');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo $contents;
include NV_ROOTDIR . '/includes/footer.php';
