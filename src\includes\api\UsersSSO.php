<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class UsersSSO implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'webtools';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request;

        $email = $nv_Request->get_title('email', 'post', '');
        $username = $nv_Request->get_title('username', 'post', '');

        $sql = '';
        if (!empty($email)) {
            $sql = 'email = ' . $db->quote(nv_strtolower($email));
        } elseif (!empty($username)) {
            $sql = 'md5username = ' . $db->quote(nv_md5safe($username));
        }
        $data = [];
        if ($sql != '') {
            $data['users'] = $db->query("SELECT * FROM nv4_users WHERE " . $sql . " LIMIT 1")->fetch();
        }
        if (isset($data['users']['userid'])) {
            $data['users_info'] = $db->query("SELECT * FROM nv4_users_info WHERE userid = " . $data['users']['userid'])->fetch();
            $this->result->setMessage(json_encode($data));
            $this->result->setSuccess();
        } else {
            $this->result->setError();
            $this->result->setMessage('No data');
        }
        return $this->result->getResult();
    }
}