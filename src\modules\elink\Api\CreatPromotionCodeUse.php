<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\elink\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CreatPromotionCodeUse implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'promotion';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Cache, $db_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];

        // Các site thanh toán
        $sql = "SELECT * FROM " . $db_config['prefix'] . "_wallet_sites ORDER BY weight ASC";
        $global_array_sites = $nv_Cache->db($sql, 'id', 'wallet');

        $array = [];
        $array['site_id'] = $nv_Request->get_absint('site_id', 'post', 0);
        $array['code'] = $nv_Request->get_title('code', 'post', '');
        $array['order_id'] = $nv_Request->get_absint('order_id', 'post', 0);
        $array['userid'] = $nv_Request->get_absint('userid', 'post', 0);
        $array['profile_id'] = $nv_Request->get_absint('profile_id', 'post', 0);
        $array['data'] = $nv_Request->get_string('data', 'post', 0, false, false);
        $array['time'] = $nv_Request->get_absint('time', 'post', 0) ?: NV_CURRENTTIME;
        $array['update_use'] = $nv_Request->get_absint('update_use', 'post', 0);
        $array['remove_old'] = $nv_Request->get_absint('remove_old', 'post', 0);

        // Check JSON data
        $array['data'] = json_decode($array['data'], true);
        if (!is_array($array['data'])) {
            $array['data'] = '';
        } else {
            $array['data'] = json_encode($array['data']);
        }

        if (empty($array['code']) or !preg_match('/^([a-zA-Z0-9\-\_]+)$/', $array['code'])) {
            return $this->result->setCode('1001')->setMessage($nv_Lang->getModule('apierror_procode'))->getResult();
        }
        if (!empty($array['site_id']) and !isset($global_array_sites[$array['site_id']])) {
            return $this->result->setCode('1002')->setMessage($nv_Lang->getModule('apierror_site_exists'))->getResult();
        }

        // Lấy mã khuyến mãi
        $sql = "SELECT * FROM " . $db_config['prefix'] . "_elink_promotion_code WHERE promo_code=" . $db->quote($array['code']);
        $promo = $db->query($sql)->fetch();
        if (empty($promo)) {
            return $this->result->setCode('1003')->setMessage($nv_Lang->getModule('apierror_procode_exists'))->getResult();
        }

        // Kiểm tra số lượt sử dụng
        if (!empty($promo['use_number']) and $promo['use_count'] >= $promo['use_number']) {
            return $this->result->setCode('1005')->setMessage($nv_Lang->getModule('apierror_prouse_count'))->getResult();
        }


        $db->beginTransaction();
        try {
            // Ghi vào số lần sử dụng
            $sql = "INSERT INTO " . $db_config['prefix'] . "_elink_promotion_code_use (
                promo_id, order_id, promo_code, promo_data, use_userid, use_profileid, use_time
            ) VALUES (
                " . $promo['promo_id'] . ", " . $array['order_id'] . ", " . $db->quote($promo['promo_code']) . ",
                " . $db->quote($array['data']) . ", " . $array['userid'] . ", " . $array['profile_id'] . ", " . $array['time'] . "
            )";
            $db->query($sql);

            // Cập nhật số lần sử dụng tăng lên
            if ($array['update_use']) {
                $sql = "UPDATE " . $db_config['prefix'] . "_elink_promotion_code SET
                    use_count=use_count+1
                WHERE promo_id=" . $promo['promo_id'];
                $db->query($sql);
            }

            if (!empty($array['remove_old'])) {
                try {
                    // Xóa lượt sử dụng mã giảm giá cũ nếu có
                    $sql = "DELETE FROM " . $db_config['prefix'] . "_elink_promotion_code_use
                    WHERE promo_id= " . $array['remove_old'] . " AND order_id =" . $array['order_id'] . " AND use_userid = " . $array['userid'];
                    $db->query($sql);

                    $sql = "UPDATE " . $db_config['prefix'] . "_elink_promotion_code SET
                        use_count=use_count-1
                    WHERE promo_id=" . $array['remove_old'];
                    $db->query($sql);
                } catch (\Exception $e) {
                    trigger_error(print_r($e, true));
                }
            }

            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            return $this->result->setCode('1004')->setMessage($e->getMessage())->getResult();
        }

        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
