.wraper {
    @media (max-width: $screen-xs-max) {
      width: 100%;
    }

    @media (min-width: $screen-sm-min) {
      width: $container-sm;
    }

    @media (min-width: $screen-md-min) {
      width: $container-md;
    }

    @media (min-width: $screen-lg-min) {
        width: $container-lg;
    }

    @media (min-width: $screen-xl-min) {
        width: $container-xl;
    }
}

@media (min-width: $screen-sm-min) {

}

@media screen and (min-width: $screen-sm-min) {
  .modal.auto-height:before {
    display: inline-block;
    vertical-align: middle;
    content: " ";
    height: 100%;
  }
}

@media (min-width :$screen-md-min) {
    nav > .container > .row {
        margin-left: 0;
        margin-right: 0;
    }

    .ltablesearch {
        .panel {
            position: relative;
        }
        .advance-search {
            left: -1px;
            right: -1px;
            z-index: $zindex-dropdown;
            background-color: $body-bg;
            border: 1px $panel-default-border solid;
            border-bottom-left-radius: $border-radius-base;
            border-bottom-right-radius: $border-radius-base;
            box-shadow: $box-shadow-sm;
        }
    }
}

@media (min-width: $screen-lg-min) {
    nav > .container > .row {
        margin-left: -5px;
        margin-right: -5px;
    }
}

@media (max-width: $grid-float-breakpoint-max) {
    .float-hidden {
        display: none !important;
    }
    .dauthau-navbar {
        background: $body-bg;

        .navbar-header {
            justify-content: space-between;
            background-color: $body-bg;
            background-image: linear-gradient(to top, darken($body-bg, 5%) 0px, darken($body-bg, 5%) 1px, $body-bg 1px, $body-bg 100%);
            height: $navbar-height;
            box-shadow: 0 2px 5px rgba(0,0,0,.1);

            .navbar-header-right-wrap {
                flex-grow: 0;
            }

            .navbar-brands {
                .navbar-brand-other {
                    display: none;
                }
            }
        }

        .navbar {
            position:fixed;
            left:0;
            top:0;
            border-width:0 0 1px;
            padding-right:0;
            padding-left:0;
            z-index: $zindex-navbar-fixed;
            width: 100%;
            max-height: $navbar-collapse-max-height;
        }

        .logo {
          height: $navbar-height;
          width: $logo-width-sm;
          background-size: 90%;
          background-position: center center;
          background-image: $svg-logo;
        }
    }

    .dauthau-navbar + * {
        margin-top: $navbar-height;
    }

    #menusite-wrap {
        .navbar-toggle {
          position: relative;
          margin-right: $navbar-padding-horizontal;
          float: none;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: $button-user-width;
          height: $button-user-width;
        }

        .switch-logos {
            display: block;
            position: fixed;
            width: 52px;
            height: calc(100% - #{$navbar-height});
            background-color: #c3c3c3;
            overflow-y: hidden;
            left: 0;
        }

        .navbar-nav {
           margin-top: 0;
           position: fixed;
           width: $menusite-mobile-width;
           height: calc(100% - #{$navbar-height});
           background-color: $menusite-mobile-color;
           overflow-y: auto;
           border-right: 1px darken($menusite-mobile-color, 5%) solid;
           box-shadow: $box-shadow-sm;
           left: 62px;

           > *:last-child {
            margin-bottom: $margin-lg;
           }

           > li ~ li {
                margin-left: 0;
           }

           & > li > a {
             border-bottom-color: darken($component-active-bg, 10%);
             color: $dropdown-link-active-color;
             background-color: $component-active-bg;
           }

           & > li > a > span > span {
             text-transform: none;
           }

           & > .dropdown:not(.active).open > a > span > span > .caret:not(.caret-right){
                border-bottom-color: $component-active-color;
            }

          .headerSearch {
            margin-top: 0;

            input[type=text] {
                border-bottom-left-radius: 0;
            }
            button {
                border-bottom-right-radius: 0;
            }
          }
        }

        .active.dropdown > a {
            border-left: 5px $brand-danger solid;
        }

        .active:not(.dropdown) > a > *:first-child:before {
            content: " \f178 ";
            font-family:'FontAwesome';
            display: inline-block;
            margin-right: 3px;
        }

        .dropdown-menu {

            & > a,
            & > a:focus,
            & > a:hover {
                color:inherit !important;
                background-color:inherit !important;
            }
        }

        .dropdown-menu > li:last-child > a:hover,
        .dropdown-menu > li:last-child > a:focus,
        .dropdown-menu > li:last-child.active > a,
        .dropdown-menu > li:last-child.active > a:hover,
        .dropdown-menu > li:last-child.active > a:focus,
        .navbar .navbar-nav .open .dropdown-menu > li:last-child > a:hover,
        .navbar .navbar-nav .open .dropdown-menu > li:last-child > a:focus,
        .navbar .navbar-nav .open .dropdown-menu > li:last-child.active > a,
        .navbar .navbar-nav .open .dropdown-menu > li:last-child.active > a:hover,
        .navbar .navbar-nav .open .dropdown-menu > li:last-child.active > a:focus {
            /*background-color: $dropdown-link-hover-bg;*/
            border-radius: 0;
        }

        .dropdown-submenu {
            & > .dropdown-menu {
                margin-top: 0;
                margin-left: 0;

                a {
                    background-color: $dropdown-bg;
                }
            }
        }

        .navbar-default .navbar-nav > .dropdown.open > .dropdown-menu > .dropdown-submenu > a {
            color: $navbar-default-link-hover-color;
            background-color: $navbar-default-link-hover-bg;
        }

        .navbar-inverse .navbar-nav > .dropdown.open > .dropdown-menu > .dropdown-submenu > a {
            color: $navbar-inverse-link-hover-color;
            background-color: $navbar-inverse-link-hover-bg;
        }

        .get_social_icons_block, .get_head_right_block {
            padding: $padding-base-vertical $padding-base-horizontal;
            display: flex;
            flex-direction: column;

            .bl-title {
                margin-bottom: $margin-sm;
                text-align: center;
            }

            .socialList, .contactList {
                display: flex;
                justify-content: center;
            }
        }

        .navbar-collapse.collapse {
            overflow: visible;
        }
    }

    .center-search-bl {

        h2 {
            font-size: ceil(($font-size-base * 1.4));
            margin: 0;
        }
    }

    .ltablesearch-cont {
        padding: $padding-large-vertical $padding-base-horizontal;
    }

    .afooter {
      padding-left: 88px;
      padding-right: 88px;
      margin-bottom: 20px;
    }

    .affix {
        top: 54px;
        width: auto;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
    }
}

@media (min-width: $grid-float-breakpoint) {
    @import "nav.desktop";
}

@media (max-width: $screen-md-max) {
    .footer-nav .content {
        font-size: $font-size-subtitle;
    }

    .data-pc{
        display: none;
    }
    .data-mobile{
        display: block;
    }

    .tab-content.mobile-all-display {
        & > .tab-pane {
            display: block !important;

            &.fade {
                opacity:1 !important;
            }
        }

        & > .tab-pane + .tab-pane {
            margin-top: $margin-xl;
        }
    }

    table.bidding-table {
        width: 100%;
        display: block;

        thead, tbody, th {
            display: block;
        }

        tr {
           display: table;
           width: 100%;
           border-collapse: collapse;
           border: 1px solid $table-border-color;
           border-bottom: 5px solid $component-active-bg;
        }

        thead tr {
            position: absolute;
            top: -9999px;
            left: -9999px;
        }

        tr:nth-of-type(odd) {
          background: inherit;
        }

        tr + tr {
           margin-top: $margin-lg;
        }

        td {
            display: table-row;
            position: relative;
            text-align: left;

            & > div {
                display: table-cell;
                padding: $padding-large-vertical $padding-base-horizontal;
            }
        }

        td:before {
            display: table-cell;
            width: 35%;
            padding: $padding-large-vertical $padding-base-horizontal;
            content: attr(data-column);
            background-color: shade($body-bg, 3%);
            background-clip: padding-box;
            border-right: 1px solid $table-border-color;
        }

        .txt-center {
            text-align: inherit;
        }

        .order-header {
            display:table-header-group;
        }

        .order-footer {
            display:table-footer-group;
        }
    }

    .bidding-list {
        .bidding-list-header {
            display: none;
        }

        .bidding-list-body .item {
            border: 1px $table-border-color solid;
            /* border-radius: $border-radius-base;
            margin-bottom: $margin-lg;
            flex-direction: column;
            padding-bottom: $padding-base; */
            background-color: darken($body-bg, 1%);

            & > div {
                border-right: 0;
                padding-bottom: 0;
                flex-basis: 0;

                &:first-child {
                    border-left: 0;
                }

                &.c-name h3 {
                    font-weight: $headings-font-weight;
                    font-size: $font-size-h2;
                }
            }

            .label-name {
                display: inline-block;
                margin-right: $margin-sm;
            }
        }


        .bidding-list-body .item > div,
        &.bidding-list-detail .bidding-list-body .item > div,
        &:not(.bidding-list-detail) .bidding-list-body .item > div {
            &.c-number, &.c-number.c-number-lg, &.c-name, &.c-author, &.c-close, &.c-pub, &.c-pub.c-pub-lg, &.c-adm {
                flex-basis: inherit;
                width: 100%;
                max-width: 100%;
                text-align: left;
            }
        }

        .bidding-list-body .item > div,
        &.bidding-list-detail .bidding-list-body .item > div {
            &.c-stt, &.c-stt-lg, &.c-open, &.c-author1, &.c-author2, &.c-author3, &.c-res, &.c-gia, &.c-gia1, &.c-tyle {
                flex-basis: inherit;
                width: 100%;
                max-width: 100%;
                text-align: left;
            }
        }

        &.bidding-list-detail .bidding-list-header > div.c-stt,
        &.bidding-list-detail .bidding-list-body .item > div.c-stt {
            display:none;
        }
        &.bidding-list-detail .bidding-list-header > div.c-stt-lg,
        &.bidding-list-detail .bidding-list-body .item > div.c-stt-lg {
            display:none;
        }

        &.bidding-list-detail .bidding-list-body .item > div.c-number {
            order: 2;
        }

        &.bidding-list-detail .bidding-list-body .item > div.c-name {
            order: 1;
        }
    }

    #menu-site-default .navbar-right {
        display: none;
    }
    .column-margin-left {
        margin-left: 0 !important
    }

    .site-name {
        margin-top: 10px;
    }
    .collapse-button {
        left: 0px !important;
        top: -65% !important;
    }

    ul.slimmenu.collapsed {
        z-index: 999999999;
    }
    .collapse-button {
        width: 100% !important;
        margin-top: 10px;
        height: 35px;
        background-color: #0685d6 !important;
    }
    .collapse-button .icon-bar {
        margin-top: 4px !important;
        margin-left: 5px;
    }

    /*.footer-menu {
        .main-menu {
            justify-content: space-around;
        }
    }*/

    .footer-menu {
        .main-menu, .sub {
            flex-direction: column;
        }
        .sub {
            margin-bottom: $margin-base;
        }
        .main-item + .main-item, .sub-menu + .sub-menu {
            margin-left: 0;
        }
        .main-item + .main-item {
            border-top: 1px outset lighten($footer-bg, 6.5%);
        }
        .main-title {
            cursor: pointer;
        }
        .hd-xs {
            display: none;
        }
    }
}

@media (max-width: $screen-sm-max) {
    #footer {
        .site-info {
            flex-direction: column;
            align-items: center;
            margin-bottom: $margin-lg;

            > div:last-child {
                margin-left: 0;
                margin-bottom: $margin-base;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }

            h3, p {
                text-align: center;
            }
        }

        .site-info-outer {
            flex-wrap: wrap;

            .network-trust {
                flex: 0 0 100%;
                padding-left: 0;
                max-width: 100%;
                text-align: center;

                img {
                    margin-top: 0;
                    margin-bottom: 20px;
                }
            }
        }

        .flex-align-items-end {
            flex-direction: column;

            & > div + div {
                margin-top: $margin-top-lg;
            }
        }

        .cpr, .cpr div, .cpr p {
            text-align: center;
        }
    }

    .ltablesearch {
        .block {
            flex-direction: column;
        }
    }

    .home-news {
        flex-direction: column;

        .panel-left {
            border-right: 0;
            border-bottom: 1px $table-border-color solid;
            padding-bottom: $padding-base-vertical * 2;
        }

        .cont {
            position: relative;
            padding-right: 0;
            padding-left: 0;
            margin-top: $margin-lg;
        }
    }
    .msgshow {
        left: 25%;
    }
}

@media (max-width: $screen-xs-max) {
    .ltablesearch {
        .flex-align-items-end {
            flex-direction: column;
        }

        .help {
            flex-direction: column;
            align-items: flex-start;
            a + a {
              margin-left: 0;
            }
        }
    }

    .box-shadow {
        box-shadow: none;
        -webkit-box-shadow: none;
    }
    .contactDefault {
        position: relative
    }
    .social-icons, .contactDefault {
        float: right;
        margin-right: 10px;
        margin-left: 0;
    }
    .social-icons .content, .contactDefault .content {
        display: none;
    }
    .contactList li {
        display: block;
    }
    .contactList li .fa, .contactList li [class^="icon-"], .contactList li [class*=" icon-"] {
        width: 20px !important
    }
    .hidden-ss-block, .hidden-ss-inline, .hidden-ss-inline-block {
        display: none !important;
    }
    /*  .second-nav {
        position: absolute;
        left: -10px;
        top: 0;
    }
    .second-nav .bg {
        -webkit-border-top-left-radius: 0;
        -webkit-border-top-right-radius: 0;
        -webkit-border-bottom-right-radius: 5px;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        border-bottom-right-radius: 10px;
    } */
    .third-nav .bg {
        padding: 10px 5px;
    }
    .third-nav .current-time {
        display: none
    }



    .social-icons {
        display: none;
    }

    .nv-infodie {
        width: 100%;
    }

    .visible-ss-block {
        display: block !important;
    }
    .visible-ss-inline {
        display: inline !important;
    }
    .visible-ss-inline-block {
        display: inline-block !important;
    }
    .hidden-ss-block, .hidden-ss-inline, .hidden-ss-inline-block {
        display: none !important;
    }
    #tip, #footer-tip {
        max-width: 100%;
    }
    .guestBlock {
        width: 100%
    }
    .site-name {
        margin-left: 0px;
    }

    .pull-left {
        max-width: 100%;
    }
    .nv-block-banners {
        text-align: center !important;
    }
    .footer-span {
        margin-top: 5px;
    }
    .back-to-top.fixed {
        bottom: 135px;
        right: 20px;
    }
    .msgshow {
        left: 1%;
    }
    #popup_not_dismiss, #backdrop-check-user-data {
        top: 40%;
        left: 2%;
    }
}

@media (min-width : $screen-sm-min) {
    .hidden-ss-block {
        display: block !important;
    }
    .hidden-ss-inline {
        display: inline !important;
    }
    .hidden-ss-inline-block {
        display: inline-block !important;
    }
}

@media (min-width: $screen-xs-min) and (max-width: $screen-lg) {

}

@media ( min-width: $screen-xs-min) {
    .nv-infodie {
        width: $screen-xs-min;
        left: 50%;
        margin-left: -250px;
    }
}

@media ( min-height: $screen-xs-min) {
    .nv-infodie {
        height: $screen-xs-min;
        top: 50%;
        margin-top: -250px;
    }
}



@media (max-width: 676.98px), (min-width: $screen-sm) and (max-width: $screen-md-min) {


    .bidding-detail .bidding-detail-item.col-four {
        flex-wrap: wrap;
    }

    .bidding-detail .bidding-detail-item.col-four > div {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .bidding-detail .bidding-detail-item.col-four > div:last-child > div:first-child {
        border-left: 0;
    }

    .bidding-detail .bidding-detail-item .c-tit,
    .bidding-detail .bidding-detail-item .c-tit2 {
        flex: 0 0 120px;
        max-width: 120px;
    }
}

@media (max-height :$navbar-collapse-max-height + $navbar-height) {
    #tip {
        max-height: $navbar-collapse-max-height;
        overflow-y: auto;
    }
}

/*
 * Tùy chỉnh kích thước Logo
 */
@media (min-width: 768px) and (max-width: 1099.98px) {
    .dauthau-navbar {
        .logo {
            width: 230px;
            height: 98.78px;
        }

        .navbar-brand-other {
            .logo {
                width: 220px;
                height: 98.78px;
            }
        }
    }
}

@media (min-width: 768px) and (max-width: 949.98px) {
    .dauthau-navbar {
        .logo {
            width: 190px;
            height: 80px;
        }

        .navbar-brand-other {
            .logo {
                width: 180px;
                height: 80px;
            }
        }
    }
}

@media (min-width: 768px) and (max-width: 879.98px) {
    .dauthau-navbar {
        .logo {
            width: 120px;
            height: 53.33px;
        }

        .navbar-brand-other {
            .logo {
                width: 120px;
                height: 53.33px;
            }
        }
    }
}

@media (max-width: $screen-xs-max) {
    .signout-wrapper .element {
        width: 280px;
    }
}
