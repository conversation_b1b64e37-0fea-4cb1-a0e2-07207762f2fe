<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jun 20, 2010 8:59:32 PM
 */
namespace NukeViet\Module\crmbidding\Api;

use PDO;
use PDOException;
use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;
if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    die('Stop!!!');
}

class CreateMessenger implements IApi
{

    private $result;

    /**
     *
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     *
     * @return string
     */
    public static function getCat()
    {
        return 'messenger';
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     *
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];

        $row = [];
        $error = '';
        $error_code = '0000';

        $row['hoten'] = $nv_Request->get_title('hoten', 'post', '');
        $row['sdt_text'] = $nv_Request->get_title('sdt_text', 'post', '');
        $row['sdt'] = $nv_Request->get_title('sdt', 'post', '');
        $row['email'] = $nv_Request->get_title('email', 'post', '');
        $row['senderid'] = $nv_Request->get_title('senderid', 'post', 0);

        if ($row['hoten'] == '') {
            $error_code = '2003';
            $error = $nv_Lang->getModule('error_required_name');
        } else if ($row['sdt_text'] == '' and $row['email'] == '') {
            $error_code = '2004';
            $error = $nv_Lang->getModule('error_required_phone');
        }
        if (!phonecheck($row['sdt'])) {
            $row['sdt'] = '';
        }

        $email = nv_check_valid_email($row['email'], true);
        if (empty($email[0])) {
            $row['email'] = $email[1];
        } else {
            $row['email'] = '';
        }

        $_row = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . $module_data . '_messenger WHERE hoten=' . $db->quote($row['name']) . ' AND sdt = ' . $db->quote($row['sdt']) . ' AND email =' . $db->quote($row['email']))
            ->fetch();
        if (!empty($_row)) {
            $error_code = '4002';
            $error = $nv_Lang->getModule('error_duplicate');
        }

        if (empty($error)) {
            try {
                $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . $module_data . '_messenger (hoten, sdt, sdt_text, email, thoi_gian_gui, senderid) VALUES (:hoten, :sdt, :sdt_text, :email, :thoi_gian_gui, :senderid)');

                $stmt->bindParam(':hoten', $row['hoten'], PDO::PARAM_STR);
                $stmt->bindParam(':sdt', $row['sdt'], PDO::PARAM_STR);
                $stmt->bindParam(':sdt_text', $row['sdt_text'], PDO::PARAM_STR);
                $stmt->bindParam(':email', $row['email'], PDO::PARAM_STR);
                $stmt->bindValue(':thoi_gian_gui', NV_CURRENTTIME, PDO::PARAM_INT);
                $stmt->bindParam(':senderid', $row['senderid'], PDO::PARAM_INT);
                $exc = $stmt->execute();
                $id = $db->lastInsertId();
                if ($exc and $id > 0) {
                    $this->result->setSuccess();
                    $this->result->set('ID', $id);
                } else {
                    $this->result->setError()
                        ->setCode('4001')
                        ->setMessage('Not response');
                }
            } catch (PDOException $e) {
                $this->result->setError()
                    ->setCode('3000')
                    ->setMessage(print_r($e, true));
            }
        } else {
            return $this->result->setError()
                ->setCode($error_code)
                ->setMessage($error)
                ->getResult();
        }
        return $this->result->getResult();
    }
}
