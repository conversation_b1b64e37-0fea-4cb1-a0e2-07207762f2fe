<?php

/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2022 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

namespace NukeViet\Module\inform\Api;

use NukeViet\Api\Api;
use NukeViet\Api\ApiResult;
use NukeViet\Api\IApi;

if (!defined('NV_ADMIN') or !defined('NV_MAINFILE')) {
    exit('Stop!!!');
}

/**
 * NukeViet\Module\inform\Api\InformGetUserList
 * API dùng để lấy danh sách thông báo của một thành viên cụ thể được chỉ định
 *
 * @package NukeViet\Module\inform\Api
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2022 VINADES.,JSC. All rights reserved
 * @version 4.6.00
 * @access public
 */
class InformGetUserList implements IApi
{
    private $result;

    /**
     * @return number
     */
    public static function getAdminLev()
    {
        return Api::ADMIN_LEV_MOD;
    }

    /**
     * @return string
     */
    public static function getCat()
    {
        return 'Get';
    }

    /**
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::setResultHander()
     */
    public function setResultHander(ApiResult $result)
    {
        $this->result = $result;
    }

    /**
     * {@inheritdoc}
     * @see \NukeViet\Api\IApi::execute()
     */
    public function execute()
    {
        global $db, $nv_Request, $global_config, $nv_Lang;

        $module_name = Api::getModuleName();
        $module_info = Api::getModuleInfo();
        $module_data = $module_info['module_data'];
        $module_file = $module_info['module_file'];
        $admin_id = Api::getAdminId();
        $admin_lev = Api::getAdminLev();

        $postdata = [];
        $postdata['userid'] = $nv_Request->get_absint('userid', 'post', 0);
        $postdata['page'] = $nv_Request->get_absint('page', 'post', 1);
        $postdata['per_page'] = $nv_Request->get_absint('per_page', 'post', 20);
        $postdata['filter'] = $nv_Request->get_title('filter', 'post', '');

        if (!in_array($postdata['filter'], ['unviewed', 'favorite', 'hidden'], true)) {
            $postdata['filter'] = '';
        }

        if (empty($postdata['userid'])) {
            return $this->result->setError()
            ->setCode('5016')
            ->setMessage($nv_Lang->getModule('please_enter_user'))
            ->getResult();
        }

        // Lấy thành viên
        $sql = "SELECT group_id, in_groups FROM " . NV_USERS_GLOBALTABLE . " WHERE userid=" . $postdata['userid'] . " AND active=1";
        $user = $db->query($sql)->fetch();
        if (empty($user)) {
            return $this->result->setError()
            ->setCode('5017')
            ->setMessage($nv_Lang->getModule('user_not_exist'))
            ->getResult();
        }

        // Xác định nhóm của thành viên
        $array_groups = explode(',', $user['in_groups']);
        $array_groups[] = $user['group_id'];
        $array_groups = array_values(array_unique(array_filter(array_map(function ($gr) {
            return $gr >= 10 ? (int) $gr : 0;
        }, $array_groups))));

        $where = [];
        $where[] = "(mtb.receiver_grs = '' AND mtb.receiver_ids = '')";
        if (!empty($array_groups)) {
            $wh = [];
            foreach ($array_groups as $gr) {
                $wh[] = 'FIND_IN_SET(' . $gr . ', mtb.receiver_grs)';
            }
            $wh = implode(' OR ', $wh);
            $where[] = "(mtb.receiver_grs != '' AND (" . $wh . '))';
        }
        $where[] = "(mtb.receiver_ids != '' AND FIND_IN_SET(" . $postdata['userid'] . ', mtb.receiver_ids))';
        $where = '(' . implode(' OR ', $where) . ') AND (mtb.add_time <= ' . NV_CURRENTTIME . ') AND (mtb.exp_time = 0 OR mtb.exp_time > ' . NV_CURRENTTIME . ')';
        if (!empty($array_groups)) {
            $where .= " AND (mtb.sender_role != 'group' OR (mtb.sender_role = 'group' AND mtb.sender_group IN (" . implode(',', $array_groups) . ')))';
        } else {
            $where .= " AND (mtb.sender_role != 'group')";
        }

        if ($postdata['filter'] == 'unviewed') {
            $where .= ' AND NOT EXISTS (SELECT 1 FROM ' . NV_INFORM_STATUS_GLOBALTABLE . ' AS exc WHERE exc.pid = mtb.id AND exc.userid = ' . $postdata['userid'] . ' AND (exc.viewed_time != 0 OR exc.hidden_time != 0))';
        } elseif ($postdata['filter'] == 'favorite') {
            $where .= ' AND EXISTS (SELECT 1 FROM ' . NV_INFORM_STATUS_GLOBALTABLE . ' AS exc WHERE exc.pid = mtb.id AND exc.userid = ' . $postdata['userid'] . ' AND (exc.favorite_time != 0 AND exc.hidden_time = 0))';
        } elseif ($postdata['filter'] == 'hidden') {
            $where .= ' AND EXISTS (SELECT 1 FROM ' . NV_INFORM_STATUS_GLOBALTABLE . ' AS exc WHERE exc.pid = mtb.id AND exc.userid = ' . $postdata['userid'] . ' AND exc.hidden_time != 0)';
        } else {
            $where .= ' AND NOT EXISTS (SELECT 1 FROM ' . NV_INFORM_STATUS_GLOBALTABLE . ' AS exc WHERE exc.pid = mtb.id AND exc.userid = ' . $postdata['userid'] . ' AND exc.hidden_time != 0)';
        }

        $db->sqlreset()
        ->select('COUNT(*)')
        ->from(NV_INFORM_GLOBALTABLE . ' AS mtb')
        ->where($where);

        $num_items = $db->query($db->sql())->fetchColumn();
        $pages = ceil($num_items / $postdata['per_page']);
        if ($postdata['page'] > $pages) {
            return $this->result->setError()->setCode('0001')->setMessage('Wrong page!!!')->getResult();
        }

        $db->select('mtb.id, mtb.meta_id, mtb.sender_role, mtb.sender_group, mtb.sender_admin, mtb.message, mtb.link, mtb.add_time, IFNULL(jtb.shown_time, 0) AS shown_time, IFNULL(jtb.viewed_time, 0) AS viewed_time, IFNULL(jtb.favorite_time, 0) AS favorite_time')
        ->join('LEFT JOIN ' . NV_INFORM_STATUS_GLOBALTABLE . ' AS jtb ON (jtb.pid = mtb.id AND jtb.userid = ' . $postdata['userid'] . ')')
        ->order('mtb.add_time DESC')
        ->limit($postdata['per_page'])
        ->offset(($postdata['page'] - 1) * $postdata['per_page']);
        $result = $db->query($db->sql());

        $items = [];
        $notshown = [];
        $admin_ids = $group_ids = [];

        while ($row = $result->fetch()) {
            // Xử lý nội dung
            if (!empty($row['message'])) {
                $messages = json_decode($row['message'], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    if (!empty($messages['contents'][NV_LANG_DATA])) {
                        $row['message'] = $messages['contents'][NV_LANG_DATA];
                    } else {
                        $row['message'] = $messages['contents'][$messages['isdef']];
                    }
                }

                if (!empty($row['message'])) {
                    $row['message'] = preg_replace('/\<\/?br\s*\/?\>/', '<br/>', $row['message']);
                    $row['message'] = text_split($row['message'], 120);
                } else {
                    $row['message'] = [];
                }
            } else {
                $row['message'] = [];
            }

            // Xử lý liên kết
            if (!empty($row['link'])) {
                $links = json_decode($row['link'], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    if (!empty($links['contents'][NV_LANG_DATA])) {
                        $row['link'] = $links['contents'][NV_LANG_DATA];
                    } else {
                        $row['link'] = $links['contents'][$links['isdef']];
                    }
                }
            }

            if (!empty($row['sender_admin'])) {
                $admin_ids[$row['sender_admin']] = $row['sender_admin'];
            }
            if (!empty($row['sender_group'])) {
                $group_ids[$row['sender_group']] = $row['sender_group'];
            }

            $items[$row['id']] = $row;
        }
        $result->closeCursor();

        // Lấy admin
        $adminlist = [];
        if (!empty($admin_ids)) {
            $sql = 'SELECT t1.admin_id, t2.first_name, t2.last_name FROM ' . NV_AUTHORS_GLOBALTABLE . ' t1
            INNER JOIN ' . NV_USERS_GLOBALTABLE . ' t2 ON t1.admin_id = t2.userid
            WHERE t1.admin_id IN(' . implode(',', $admin_ids) . ')';
            $result = $db->query($sql);

            while ($row = $result->fetch()) {
                $full_name = $global_config['name_show'] ? [$row['first_name'], $row['last_name']] : [$row['last_name'], $row['first_name']];
                $full_name = array_filter($full_name);
                $adminlist[$row['admin_id']] = implode(' ', array_map('trim', $full_name));
            }
            $result->closeCursor();
        }

        // Lấy nhóm
        $grouplist = [];
        if (!empty($group_ids)) {
            $sql = 'SELECT g.group_id, d.title, g.group_type, g.exp_time FROM ' . NV_USERS_GLOBALTABLE . '_groups AS g
            LEFT JOIN ' . NV_USERS_GLOBALTABLE . "_groups_detail d ON ( g.group_id = d.group_id AND d.lang='" . NV_LANG_DATA . "' )
            WHERE g.act=1 AND (g.idsite = " . $global_config['idsite'] . ' OR (g.idsite =0 AND g.siteus = 1))
            AND g.group_id IN(' . implode(',', $group_ids) . ') ORDER BY g.idsite, g.weight';
            $result = $db->query($sql);
            while ($row = $result->fetch()) {
                if (!($row['exp_time'] != 0 and $row['exp_time'] <= NV_CURRENTTIME) and $row['group_type']) {
                    $grouplist[$row['group_id']] = $row['title'];
                }
            }
            $result->closeCursor();
        }

        // Duyệt lại items để xác định đối tượng gửi và các thông báo đã đọc
        $event_logs = [];
        foreach ($items as $row) {
            if (!($row['sender_role'] == 'admin' and !empty($row['sender_admin'])) and !($row['sender_role'] == 'group' and !empty($row['sender_group']) and !empty($grouplist[$row['sender_group']]))) {
                $row['sender_role'] = 'system';
            }
            if (empty($row['shown_time'])) {
                $notshown[] = $row['id'];
            }

            if ($row['sender_role'] == 'group') {
                $title = sprintf($nv_Lang->getModule('from_group'), $grouplist[$row['sender_group']]);
                $row['sender_avatar'] = 'group';
            } elseif ($row['sender_role'] == 'admin' and !empty($adminlist[$row['sender_admin']])) {
                $title = sprintf($nv_Lang->getModule('from_admin'), $adminlist[$row['sender_admin']]);
                $row['sender_avatar'] = 'admin';
            } else {
                $title = sprintf($nv_Lang->getModule('from_system'), nv_strtolower($nv_Lang->getModule('admin_from_system')));
                $row['sender_avatar'] = 'system';
            }
            $row['title'] = $title;

            $items[$row['id']] = $row;

            if (!empty($row['meta_id'])) {
                $event_logs[] = $row['meta_id'];
            }
        }

        // Ghi vào bảng event để đẩy lên marketing sau
        if (!empty($event_logs)) {
            $sqls = [];
            foreach ($event_logs as $meta_id) {
                $sqls[] = "(" . $db->quote($meta_id) . ", 'open', " . NV_CURRENTTIME . ")";
            }
            $db->query("INSERT INTO " . NV_INFORM_GLOBALTABLE . "_events (meta_id, event_name, event_time) VALUES " . implode(',', $sqls));
        }

        if (!empty($notshown)) {
            foreach ($notshown as $id) {
                if (empty($items[$id]['viewed_time']) and empty($items[$id]['favorite_time']) and empty($items[$id]['hidden_time'])) {
                    $db->query('INSERT IGNORE INTO ' . NV_INFORM_STATUS_GLOBALTABLE . ' (pid, userid, shown_time) VALUES (' . $id . ', ' . $postdata['userid'] . ', ' . NV_CURRENTTIME . ')');
                } else {
                    $db->query('UPDATE ' . NV_INFORM_STATUS_GLOBALTABLE . ' SET shown_time = ' . NV_CURRENTTIME . ' WHERE pid=' . $id . ' AND userid=' . $postdata['userid']);
                }
            }
        }

        $this->result->set('items', $items);
        $this->result->set('count', count($notshown));
        $this->result->set('num_items', $num_items);

        $this->result->setSuccess();
        return $this->result->getResult();
    }
}
