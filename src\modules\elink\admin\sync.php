<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 2-9-2010 14:43
 */

if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

/**
 * @deprecated <PERSON>ức n<PERSON>ng cũ, ai cần phải viết lại
 */

$page_title = $nv_Lang->getModule('sync');
$data_res = [];

if ($nv_Request->isset_request('submit', 'post')) {
    // Xóa bảng _affiliate_set
    $sql = "DELETE FROM " . $db_config['prefix'] . "_" . $module_data . "_affiliate_set WHERE NOT EXISTS(
        SELECT userid FROM " . NV_USERS_GLOBALTABLE . " WHERE userid=" . $db_config['prefix'] . "_" . $module_data . "_affiliate_set.pri_uid
    )";
    $data_res['affiliate_pri'] = $db->exec($sql);

    $sql = "DELETE FROM " . $db_config['prefix'] . "_" . $module_data . "_affiliate_set WHERE NOT EXISTS(
        SELECT userid FROM " . NV_USERS_GLOBALTABLE . " WHERE userid=" . $db_config['prefix'] . "_" . $module_data . "_affiliate_set.pre_uid
    )";
    $data_res['affiliate_pre'] = $db->exec($sql);
    // Đếm lại số lượng
    $db->exec("UPDATE " . NV_USERS_GLOBALTABLE . " tb1 SET tb1.num_aff_user = (SELECT COUNT(*) FROM " . $db_config['prefix'] . "_" . $module_data . "_affiliate_set tb2 WHERE tb2.pri_uid = tb1.userid)");

    // Xóa mã giảm giá của thành viên bị xóa
    $sql = "DELETE FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code WHERE NOT EXISTS(
        SELECT userid FROM " . NV_USERS_GLOBALTABLE . " WHERE userid=" . $db_config['prefix'] . "_" . $module_data . "_promotion_code.userid
    )";
    $data_res['promocode'] = $db->exec($sql);

    /*
     * Xóa số lần sử dụng mã giảm giá của thành viên
     * Khi đơn hàng không tồn tại và mã giảm giá không tồn tại
     */
    $sql = "DELETE FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code_use WHERE NOT EXISTS(
        SELECT * FROM " . $db_config['prefix'] . "_" . $module_data . "_promotion_code  WHERE promo_id=" . $db_config['prefix'] . "_" . $module_data . "_promotion_code_use.promo_id
    ) AND NOT EXISTS(
        SELECT * FROM " . $db_config['prefix'] . "_vi_bidding_orders WHERE id=" . $db_config['prefix'] . "_" . $module_data . "_promotion_code_use.order_id
    )";
    $data_res['promocodeuse'] = $db->exec($sql);

    $nv_Cache->delMod($module_name);
}

$xtpl = new XTemplate('sync.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('FORM_ACTION', NV_BASE_ADMINURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

if (!empty($data_res)) {
    $xtpl->assign('RESULT', $data_res);
    $xtpl->parse('main.result');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
