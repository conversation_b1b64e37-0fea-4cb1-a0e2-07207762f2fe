<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2019 Hoàng Tuyên. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Mon, 23 Sep 2019 07:59:36 GMT
 */
if (!defined('NV_IS_FILE_ADMIN')) {
    die('Stop!!!');
}

$page_title = $nv_Lang->getModule('config');
if (!defined('NV_IS_GODADMIN')) {
    $contents = nv_theme_alert($nv_Lang->getModule('info'), $nv_Lang->getModule('info_notallowed'));

    include NV_ROOTDIR . '/includes/header.php';
    echo nv_admin_theme($contents);
    include NV_ROOTDIR . '/includes/footer.php';
}

$data = $data_old = $module_config[$module_name];
$error = array();
if ($data['setting_mobifone'] == '' or $data['setting_mobifone'] == 'null') {
    $data['setting_mobifone'] = [];
    $data['setting_mobifone'][1]['called'] = '**********';
    $data['setting_mobifone'][1]['called_name'] = 'Web';
    $data['setting_mobifone'][1]['agent'] = '1';

    $data['setting_mobifone'][2]['called'] = '**********';
    $data['setting_mobifone'][2]['called_name'] = 'Đấu thầu';
    $data['setting_mobifone'][2]['agent'] = '2';
    $data['setting_mobifone'] = $data_old['setting_mobifone'] = json_encode($data['setting_mobifone']);
}

if ($nv_Request->isset_request('submit', 'post, get')) {
    $data = array();
    $data['set_leads_to_sales'] = $nv_Request->get_int('set_leads_to_sales', 'post', 0);
    $data['set_order_to_sales'] = $nv_Request->get_int('set_order_to_sales', 'post', 0);
    $data['time_revoke_leads'] = $nv_Request->get_int('time_revoke_leads', 'post', 0);
    $data['allow_delete_order'] = $nv_Request->get_int('allow_delete_order', 'post', 0);
    $data['allow_delete_vips'] = $nv_Request->get_int('allow_delete_vips', 'post', 0);
    $data['view_static'] = $nv_Request->get_int('view_static', 'post', 1);
    $data['allow_viewall_order'] = $nv_Request->get_int('allow_viewall_order', 'post', 0);
    $data['sales_not_set_status'] = $nv_Request->get_int('sales_not_set_status', 'post', 0);

    $data['view_leads'] = $nv_Request->get_int('view_leads', 'post', 1);
    $data['num_lead_leader_view'] = $nv_Request->get_int('num_lead_leader_view', 'post', 0);
    $data['num_lead_sale_views'] = $nv_Request->get_int('num_lead_sale_views', 'post', 0);
    $data['accesstokenfb'] = $nv_Request->get_title('accesstokenfb', 'post', "");
    $data['blockphone'] = $nv_Request->get_title('blockphone', 'post', "");
    $data['blockemail'] = $nv_Request->get_title('blockemail', 'post', "");
    $data['zaloAppSecretKey'] = $nv_Request->get_title('zaloAppSecretKey', 'post', "");
    $data['zaloAppID'] = $nv_Request->get_title('zaloAppID', 'post', "");
    $data['zaloOfficialAccountID'] = $nv_Request->get_title('zaloOfficialAccountID', 'post', "");
    $data['zaloOASecretKey'] = $nv_Request->get_title('zaloOASecretKey', 'post', "");
    $data['zaloOAAccessToken'] = $nv_Request->get_title('zaloOAAccessToken', 'post', "");
    $data['zaloOARefreshToken'] = $nv_Request->get_title('zaloOARefreshToken', 'post', "");

    $data['max_file_econtract_file'] = $nv_Request->get_int('max_file_econtract_file', 'post', 0);
    $data['econtract_leader_emails'] = $nv_Request->get_title('econtract_leader_emails', 'post', "");
    $data['email_hardcopy_contract'] = $nv_Request->get_title('email_hardcopy_contract', 'post', "");

    $data['elas_use'] = $nv_Request->get_int('elas_use', 'post', '0');
    $data['elas_host'] = $nv_Request->get_title('elas_host', 'post', '');
    $data['elas_port'] = $nv_Request->get_title('elas_port', 'post', '');
    $data['elas_user'] = $nv_Request->get_title('elas_user', 'post', '');
    $data['elas_pass'] = $nv_Request->get_title('elas_pass', 'post', '');

    $data['setting_mobifone'] = json_decode($data_old['setting_mobifone'], true);
    foreach ($data['setting_mobifone'] as $key => $value) {
        $data['setting_mobifone'][$key]['agent'] = $nv_Request->get_title('agent_' . $key, 'post', '');
        $data['setting_mobifone'][$key]['called'] = $nv_Request->get_title('called_' . $key, 'post', '');
        $data['setting_mobifone'][$key]['called_name'] = $nv_Request->get_title('called_name_' . $key, 'post', '');
    }
    $data['setting_mobifone'] = json_encode($data['setting_mobifone']);

    $ids = $nv_Request->get_array('ids', 'post', array());
    $_data = [];
    foreach ($ids as $key) {
        $_data[$key]['agent_id'] = $nv_Request->get_title('agent_id_' . $key, 'post', '');
        $_data[$key]['agent_id_name'] = $nv_Request->get_title('agent_id_name_' . $key, 'post', '');
        $_data[$key]['agent_id_phone'] = $nv_Request->get_title('agent_id_phone_' . $key, 'post', '');
    }

    $data['setting_mobifone_agent'] = json_encode($_data);
    if (empty($error)) {
        $sth = $db->prepare("UPDATE " . NV_CONFIG_GLOBALTABLE . " SET config_value = :config_value WHERE lang = '" . NV_LANG_DATA . "' AND module = :module_name AND config_name = :config_name");

        $sth->bindParam(':module_name', $module_name, PDO::PARAM_STR);
        foreach ($data as $config_name => $config_value) {
            $sth->bindParam(':config_name', $config_name, PDO::PARAM_STR);
            $sth->bindParam(':config_value', $config_value, PDO::PARAM_STR);
            $sth->execute();
        }

        nv_insert_logs(NV_LANG_DATA, $module_name, NV_LANG_DATA, "Setting", $admin_info['userid']);
        $nv_Cache->delMod('settings');
        $nv_Cache->delMod($module_name);

        Header("Location: " . NV_BASE_ADMINURL . "index.php?" . NV_NAME_VARIABLE . "=" . $module_name . "&" . NV_OP_VARIABLE . '=config');
        die();
    }
}

$nv_Lang->setModule('oa_create_note', sprintf($nv_Lang->getModule('oa_create_note'), 'https://oa.zalo.me/manage/oa?option=create', 'https://oa.zalo.me/manage/oa'));
$nv_Lang->setModule('app_note', sprintf($nv_Lang->getModule('app_note'), 'https://developers.zalo.me/createapp', 'https://developers.zalo.me/apps', NV_MY_DOMAIN . NV_BASE_ADMINURL . 'index.php', NV_MY_DOMAIN, NV_MY_DOMAIN . NV_BASE_SITEURL . 'index.php', NV_MY_DOMAIN . NV_BASE_ADMINURL . 'index.php'));

$xtpl = new XTemplate($op . '.tpl', NV_ROOTDIR . '/themes/' . $global_config['module_theme'] . '/modules/' . $module_file);
$xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
$xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
$xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
$xtpl->assign('NV_BASE_ADMINURL', NV_BASE_ADMINURL);
$xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
$xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
$xtpl->assign('MODULE_NAME', $module_name);
$xtpl->assign('OP', $op);

$data['allow_delete_order'] = $data['allow_delete_order'] == 1 ? 'checked="checked"' : '';
$data['allow_delete_vips'] = $data['allow_delete_vips'] == 1 ? 'checked="checked"' : '';
$data['allow_viewall_order'] = $data['allow_viewall_order'] == 1 ? 'checked="checked"' : '';
$data['set_leads_to_sales'] = $data['set_leads_to_sales'] == 1 ? 'checked="checked"' : '';
$data['set_order_to_sales'] = $data['set_order_to_sales'] == 1 ? 'checked="checked"' : '';
$data['sales_not_set_status'] = $data['sales_not_set_status'] == 1 ? 'checked="checked"' : '';

$data['view_static' . $data['view_static']] = 'checked="checked"';
$data['view_leads' . $data['view_leads']] = 'checked="checked"';

$xtpl->assign('DATA', $data);
$xtpl->assign('ELAS_USE', $data['elas_use'] ? ' checked="checked"' : '');


$data['setting_mobifone'] = json_decode($data['setting_mobifone'], true);
foreach ($data['setting_mobifone'] as $key => $value) {
    $xtpl->assign('CALLED', $value);
    $xtpl->assign('KEY', $key);
    $xtpl->parse('main.loop');
}

$data['setting_mobifone_agent'] = json_decode($data['setting_mobifone_agent'], true);
if (!empty($data['setting_mobifone_agent'])) {
    $a = 0;
    foreach ($data['setting_mobifone_agent'] as $key => $value) {
        $xtpl->assign('VALUE', $value);
        $xtpl->assign('KEY', $key);
        $a = $key;
        $xtpl->parse('main.loop_agent');
    }
    $xtpl->assign('CONFIG_WEIGHT_COUNT', $a + 1);
} else {
    $xtpl->assign('KEY', 1);
    $xtpl->assign('CONFIG_WEIGHT_COUNT', 1);
    $xtpl->parse('main.loop_agent');
}

if (!empty($error)) {
    $xtpl->assign('ERROR', implode('<br />', $error));
    $xtpl->parse('main.error');
}

$xtpl->parse('main');
$contents = $xtpl->text('main');

include NV_ROOTDIR . '/includes/header.php';
echo nv_admin_theme($contents);
include NV_ROOTDIR . '/includes/footer.php';
