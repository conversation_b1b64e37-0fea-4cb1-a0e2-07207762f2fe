<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES ., JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate Jul 11, 2010 8:43:46 PM
 */

if (!defined('NV_IS_MOD_ELINK')) {
    die('Stop!!!');
}

/**
 * @param array $array
 * @return string
 */
function nv_elink_theme_overview_orders($array)
{
    global $module_info, $nv_Lang;

    $xtpl = new XTemplate('main.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    $array['promotion_total_price_orders'] = number_format($array['promotion_total_price_orders'], 0, ',', '.');
    $array['promotion_total_paid_orders'] = number_format($array['promotion_total_paid_orders'], 0, ',', '.');
    $array['promotion_total_receive_orders'] = number_format($array['promotion_total_receive_orders'], 0, ',', '.');
    $array['promotion_total_waiting_orders'] = number_format($array['promotion_total_waiting_orders'], 0, ',', '.');

    $xtpl->assign('DATA', $array);

    $xtpl->parse('stat');
    return $xtpl->text('stat');
}

/**
 * @param array $array
 * @return string
 */
function nv_elink_theme_main($array)
{
    global $module_info, $global_array_config, $nv_Lang;

    $xtpl = new XTemplate('main.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    $array['affiliate_visitors'] = number_format($array['affiliate_visitors'], 0, ',', '.');
    $array['affiliate_users'] = number_format($array['affiliate_users'], 0, ',', '.');
    $array['promotion_number'] = number_format($array['promotion_number'], 0, ',', '.');
    $array['promotion_visitors'] = number_format($array['promotion_visitors'], 0, ',', '.');
    $array['promotion_used'] = number_format($array['promotion_used'], 0, ',', '.');

    $xtpl->assign('DATA', $array);

    $view_statistics = false;

    if ($array['affiliate_enabled']) {
        $view_statistics = true;
        $xtpl->parse('main.affiliate_enabled');
    } else {
        $xtpl->parse('main.affiliate_disabled');
    }

    if ($array['promotion_enabled']) {
        $view_statistics = true;
        $xtpl->parse('main.promotion_enabled');
    } else {
        $xtpl->parse('main.promotion_disabled');
    }

    if ($view_statistics) {
        if (empty($array['statistics'])) {
            $xtpl->parse('main.view_statistics.no_statistics');
        } else {
            $stt = 0;
            foreach ($array['statistics'] as $link) {
                $link['stt'] = ++$stt;
                $link['last_update'] = nv_date('H:i d/m/Y', $link['last_update']);
                $link['scount'] = number_format($link['scount'], 0, ',', '.');
                $xtpl->assign('LINK', $link);
                $xtpl->parse('main.view_statistics.statistics.loop');
            }
            $xtpl->parse('main.view_statistics.statistics');
        }

        if (empty($array['statistics_ref'])) {
            $xtpl->parse('main.view_statistics.no_statistics_ref');
        } else {
            $stt = 0;
            foreach ($array['statistics_ref'] as $link) {
                $link['stt'] = ++$stt;
                $link['last_update'] = nv_date('H:i d/m/Y', $link['last_update']);
                $link['scount'] = number_format($link['scount'], 0, ',', '.');
                $xtpl->assign('LINK', $link);
                $xtpl->parse('main.view_statistics.statistics_ref.loop');
            }
            $xtpl->parse('main.view_statistics.statistics_ref');
        }

        $xtpl->parse('main.view_statistics');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * @param array $array
 * @param array $array_user_phone
 * @param array $array_user_vip
 * @return string
 */
function nv_elink_theme_affiliate($array, $array_user_phone, $array_user_vip)
{
    global $module_info, $global_array_config, $module_name, $global_array_sites, $nv_Lang;

    $xtpl = new XTemplate('affiliate.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('DATA', $array);
    $xtpl->assign('NV_SERVER_PROTOCOL', NV_SERVER_PROTOCOL);

    $site_list = [];
    foreach ($global_array_sites as $site) {
        $site_list[] = '<a href="' . NV_SERVER_PROTOCOL . '://' . $site['sitedomain'] . '" title="' . $site['title'] . '" target="_blank" rel="dofollow">' . $site['sitedomain'] . '</a>';

        $xtpl->assign('SITE', $site);
        $xtpl->parse('main.loopsite');
    }
    $site_list = implode(', ', $site_list);
    $xtpl->assign('SITE_LIST', $site_list);

    if ($array['promotion_enabled']) {
        // Thông báo chưa có mã khuyến mãi nào
        if ($array['promotion_numbers'] < 1) {
            $xtpl->assign('LINK_CREAT_PROMOTION', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=promotion/creat');
            $xtpl->parse('main.no_promotion');
        } elseif (empty($array['promotion_default'])) {
            // Trường hợp đã có mã khuyến mãi nhưng chưa có mặc định
            $xtpl->assign('LINK_DEFAULT_PROMOTION', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=promotion');
            $xtpl->assign('PROMOTION_NUMBERS', number_format($array['promotion_numbers'], 0, ',', '.'));
            $xtpl->parse('main.no_promotion_default');
        } else {
            // Thông báo mã mặc định và link sửa
            $xtpl->assign('PROMOTION_DEFAULT', $array['promotion_default']);
            $xtpl->assign('LINK_EDITDEF_PROMOTION', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=promotion/edit/' . $array['promotion_default']);
            $xtpl->parse('main.promotion_default');
        }
    }

    if (empty($array['users'])) {
        $xtpl->parse('main.no_users');
    } else {
        foreach ($array['users'] as $user) {
            $user['regdate'] = nv_date('d/m/Y H:i', $user['regdate']);
            if (isset($array_user_phone[$user['userid']])) {
                $user['phone'] = '***' . substr($array_user_phone[$user['userid']], -4, 4);
            } else {
                $user['phone'] = '';
            }
            $user['email'] = preg_replace('/^(\w{4}).*?\@(.*?)$/', '\\1***@\\2', $user['email']);
            $user['full_name'] = nv_show_name_user($user['first_name'], $user['last_name']);

            $xtpl->assign('USER', $user);

            if (isset($array_user_vip[$user['userid']])) {
                foreach ($array_user_vip[$user['userid']] as $vip) {
                    $xtpl->assign('VIP', $vip);
                    if ($vip['status'] == 1) {
                        $xtpl->parse('main.users.loop.vip.active');
                    } else {
                        $xtpl->parse('main.users.loop.vip.exp');
                    }
                    $xtpl->parse('main.users.loop.vip');
                }
            }

            $xtpl->parse('main.users.loop');
        }

        $xtpl->parse('main.users');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * @param array $array
 * @param string $generate_page
 * @param integer $num_items
 * @param array $array_search
 * @param string $sql_order_by
 * @param string $sql_order_val
 * @param string $sql_order_base
 * @return string
 */
function nv_elink_theme_affiliate_links($array, $generate_page, $num_items, $array_search, $sql_order_by, $sql_order_val, $sql_order_base)
{
    global $module_info, $global_array_config, $array_promotion_code, $link_dissmiss_promotion, $global_array_sites, $nv_Lang;

    $xtpl = new XTemplate('affiliate-links.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('DATA', $array);
    $xtpl->assign('NUM_ITEMS', number_format($num_items, 0, ',', '.'));

    $array_search_cats = [
        0 => $nv_Lang->getModule('affl_c0'),
        1 => $nv_Lang->getModule('affl_c1'),
        2 => $nv_Lang->getModule('affl_c2')
    ];
    $array_search_types = [
        0 => $nv_Lang->getModule('affl_t0'),
        1 => $nv_Lang->getModule('affl_t1'),
        2 => $nv_Lang->getModule('affl_t2')
    ];

    foreach ($global_array_sites as $site) {
        $site['selected'] = $array_search['site_id'] == $site['id'] ? ' selected="selected"' : '';
        $xtpl->assign('SITE', $site);
        $xtpl->parse('main.site');
    }

    foreach ($array_search_cats as $key => $title) {
        $xtpl->assign('CAT', [
            'key' => $key,
            'title' => $title,
            'selected' => $key == $array_search['cat'] ? ' selected="selected"' : '',
        ]);
        $xtpl->parse('main.cat');
    }

    foreach ($array_search_types as $key => $title) {
        $xtpl->assign('TYPE', [
            'key' => $key,
            'title' => $title,
            'selected' => $key == $array_search['type'] ? ' selected="selected"' : '',
        ]);
        $xtpl->parse('main.type');
    }

    foreach ($array['data'] as $link) {
        $link['scount'] = number_format($link['scount'], 0, ',', '.');
        $link['stt'] = number_format($link['stt'], 0, ',', '.');
        $link['last_update'] = nv_date('H:i d/m/Y', $link['last_update']);
        $xtpl->assign('LINK', $link);
        $xtpl->parse('main.loop');
    }

    // Sắp xếp
    $link_order_time = $sql_order_base;
    if ($sql_order_by != 'lastupdate') {
        $xtpl->assign('CLASS_ORDER_TIME', 'fa-sort');
        $link_order_time .= '&amp;ob=lastupdate&amp;ov=DESC';
    } elseif ($sql_order_val == 'ASC') {
        $xtpl->assign('CLASS_ORDER_TIME', 'fa-sort-asc');
        $link_order_time .= '&amp;ob=lastupdate&amp;ov=DESC';
    } else {
        $xtpl->assign('CLASS_ORDER_TIME', 'fa-sort-desc');
        $link_order_time .= '&amp;ob=lastupdate&amp;ov=ASC';
    }
    $xtpl->assign('LINK_ORDER_TIME', $link_order_time);

    $link_order_hit = $sql_order_base;
    if ($sql_order_by != 'hits') {
        $xtpl->assign('CLASS_ORDER_HIT', 'fa-sort');
        $link_order_hit .= '&amp;ob=hits&amp;ov=DESC';
    } elseif ($sql_order_val == 'ASC') {
        $xtpl->assign('CLASS_ORDER_HIT', 'fa-sort-asc');
        $link_order_hit .= '&amp;ob=hits&amp;ov=DESC';
    } else {
        $xtpl->assign('CLASS_ORDER_HIT', 'fa-sort-desc');
        $link_order_hit .= '&amp;ob=hits&amp;ov=ASC';
    }
    $xtpl->assign('LINK_ORDER_HIT', $link_order_hit);

    // Thống kê theo mã
    if (!empty($array_promotion_code)) {
        $xtpl->assign('LINK_DISSMISS_PROMOTION', $link_dissmiss_promotion);
        $xtpl->assign('DATA_PROMOTION', $array_promotion_code);
        $xtpl->parse('main.stat_by_promotion');

        $xtpl->assign('DISABLED_CAT', ' disabled="disabled"');
    } else {
        $xtpl->assign('DISABLED_CAT', '');
    }

    $link_selob = $sql_order_base;
    if (!empty($sql_order_val)) {
        $link_selob .= '&amp;ov=' . strtolower($sql_order_val);
    }
    $xtpl->assign('SELOB_0', $link_selob);
    $xtpl->assign('SELOB_1', $link_selob . '&amp;ob=lastupdate');
    $xtpl->assign('SELOB_2', $link_selob . '&amp;ob=hits');
    if (empty($sql_order_by)) {
        $xtpl->assign('SELOB_0_SELECTED', ' selected="selected"');
    } elseif ($sql_order_by == 'lastupdate') {
        $xtpl->assign('SELOB_1_SELECTED', ' selected="selected"');
    } else {
        $xtpl->assign('SELOB_2_SELECTED', ' selected="selected"');
    }

    $link_selov = $sql_order_base;
    if (!empty($sql_order_by)) {
        $link_selov .= '&amp;ob=' . $sql_order_by;
    }
    $xtpl->assign('SELOV_0', $link_selov);
    $xtpl->assign('SELOV_1', $link_selov . '&amp;ov=asc');
    $xtpl->assign('SELOV_2', $link_selov . '&amp;ov=desc');
    if (empty($sql_order_val)) {
        $xtpl->assign('SELOV_0_SELECTED', ' selected="selected"');
    } elseif ($sql_order_val == 'ASC') {
        $xtpl->assign('SELOV_1_SELECTED', ' selected="selected"');
    } else {
        $xtpl->assign('SELOV_2_SELECTED', ' selected="selected"');
    }


    if (!empty($generate_page)) {
        $xtpl->assign('GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * @param array $array
 * @param string $generate_page
 * @param integer $num_items
 * @param array $array_user_phone
 * @param array $array_user_vip
 * @return string
 */
function nv_elink_theme_affiliate_users($array, $generate_page, $num_items, $array_user_phone, $array_user_vip)
{
    global $module_info, $global_array_config, $nv_Lang;

    $xtpl = new XTemplate('affiliate-users.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('DATA', $array);
    $xtpl->assign('NUM_ITEMS', number_format($num_items, 0, ',', '.'));

    foreach ($array['data'] as $user) {
        $user['regdate'] = nv_date('d/m/Y H:i', $user['regdate']);
        if (isset($array_user_phone[$user['userid']])) {
            $user['phone'] = '***' . substr($array_user_phone[$user['userid']], -4, 4);
        } else {
            $user['phone'] = '';
        }
        $user['email'] = preg_replace('/^(\w{4}).*?\@(.*?)$/', '\\1***@\\2', $user['email']);
        $user['full_name'] = nv_show_name_user($user['first_name'], $user['last_name']);

        $xtpl->assign('USER', $user);

        if (isset($array_user_vip[$user['userid']])) {
            foreach ($array_user_vip[$user['userid']] as $vip) {
                $xtpl->assign('VIP', $vip);
                if ($vip['status'] == 1) {
                    $xtpl->parse('main.loop.vip.active');
                } else {
                    $xtpl->parse('main.loop.vip.exp');
                }
                $xtpl->parse('main.loop.vip');
            }
        }

        $xtpl->parse('main.loop');
    }

    if (!empty($generate_page)) {
        $xtpl->assign('GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * @param array $array
 * @param array $array_statistics
 * @param array $array_lastest_users
 * @param array $array_user_phone
 * @param array $array_user_vip
 * @return string
 */
function nv_elink_theme_promotion($array, $array_statistics, $array_lastest_users, $array_user_phone, $array_user_vip)
{
    global $module_info, $global_array_config, $global_array_sites, $module_name, $op, $nv_Request, $language_array, $nv_Lang;

    $xtpl = new XTemplate('promotion.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('DATA', $array);
    $xtpl->assign('NUM_PROMO_MESSAGE', sprintf($nv_Lang->getModule('promotion_by_acctount'), number_format($array['promotion_numallowed'], 0, ',', '.')));

    $have_affiliate = false;
    if (empty($array['data'])) {
        $xtpl->parse('main.empty');
    } else {
        if (sizeof($array['data']) < $array['promotion_numallowed']) {
            $xtpl->parse('main.data.creat');
        }

        foreach ($array['data'] as $row) {
            $row['start_time'] = empty($row['start_time']) ? '' : nv_date('d/m/Y', $row['start_time']);
            $row['end_time'] = empty($row['end_time']) ? '' : nv_date('d/m/Y', $row['end_time']);
            if (!empty($row['promo_type'])) {
                $row['promo_value'] = number_format($row['promo_value'], 0, ',', '.') . '&nbsp;VNĐ';
            } else {
                $row['promo_value'] .= '%';
            }

            // Cho xóa mã đã dùng
            //$row['allow_delete'] = $row['use_count'] > 0 ? false : true;
            $row['allow_delete'] = true;

            $row['promo_type'] = $nv_Lang->getModule('promotion_typev' . $row['promo_type']);
            $row['use_number'] = $row['use_number'] ? number_format($row['use_number'], 0, ',', '.') : $nv_Lang->getModule('nolimit');
            $row['use_user'] = $row['use_user'] ? number_format($row['use_user'], 0, ',', '.') : $nv_Lang->getModule('nolimit');
            $row['use_peruser'] = $row['use_peruser'] ? number_format($row['use_peruser'], 0, ',', '.') : $nv_Lang->getModule('nolimit');
            $row['use_count'] = $row['use_count'] ? number_format($row['use_count'], 0, ',', '.') : $nv_Lang->getModule('noyetuse');

            $use_limityear = [];
            $use_type = [];
            $vip_apply = [];
            $vip_include = [];

            if (!empty($row['use_limityear'])) {
                $row['use_limityear'] = explode(',', $row['use_limityear']);
                foreach ($row['use_limityear'] as $val) {
                    $use_limityear[] = $val . ' ' . $nv_Lang->getModule('year');
                }
            }
            if (empty($use_limityear)) {
                $row['use_limityear'] = $nv_Lang->getModule('all');
            } else {
                $row['use_limityear'] = implode(', ', $use_limityear);
            }
            if (!empty($row['use_type'])) {
                $row['use_type'] = explode(',', $row['use_type']);
                foreach ($row['use_type'] as $val) {
                    $use_type[] = $nv_Lang->getModule('promotion_use_type' . $val);
                }
            }
            if (empty($use_type)) {
                $row['use_type'] = $nv_Lang->getModule('all');
            } else {
                $row['use_type'] = implode('<br/>', $use_type);
            }
            if (!empty($row['vip_apply'])) {
                $row['vip_apply'] = explode(',', $row['vip_apply']);
                foreach ($row['vip_apply'] as $val) {
                    $vip_apply[] = $nv_Lang->getModule('vip' . $val);
                }
            }
            if (empty($vip_apply)) {
                $row['vip_apply'] = $nv_Lang->getModule('all');
            } else {
                $row['vip_apply'] = implode(', ', $vip_apply);
            }
            if (!empty($row['vip_include'])) {
                $row['vip_include'] = explode(',', $row['vip_include']);
                foreach ($row['vip_include'] as $val) {
                    $vip_include[] = $nv_Lang->getModule('vip' . $val);
                }
            }
            if (empty($vip_include)) {
                $row['vip_include'] = $nv_Lang->getModule('all');
            } else {
                $row['vip_include'] = implode(', ', $vip_include);
            }
            $row['is_affiliate_text'] = $row['is_affiliate'] ? 'Có' : 'Không';

            if (isset($array_statistics[$row['promo_code']])) {
                $row['visitors'] = number_format($array_statistics[$row['promo_code']], 0, ',', '.');
            } else {
                $row['visitors'] = 0;
            }

            $row['apply_lang'] = empty($row['apply_lang']) ? $nv_Lang->getModule('all') : $language_array[$row['apply_lang']][NV_LANG_INTERFACE == 'vi' ? 'name' : 'language'];

            $xtpl->assign('ROW', $row);

            if ($row['allow_delete']) {
                $xtpl->parse('main.data.loop.delete');
            }
            if ($row['is_affiliate']) {
                $have_affiliate = true;
                $xtpl->parse('main.data.loop.is_affiliate');
            }

            $xtpl->parse('main.data.loop');
        }

        if ($have_affiliate) {
            $xtpl->parse('main.data.is_affiliate');
        }

        $site_list = [];
        foreach ($global_array_sites as $site) {
            $site_list[] = '<a href="' . NV_SERVER_PROTOCOL . '://' . $site['sitedomain'] . '" title="' . $site['title'] . '" target="_blank" rel="dofollow">' . $site['sitedomain'] . '</a>';

            $xtpl->assign('SITE', $site);
            $xtpl->parse('main.data.loopsite');
        }
        $site_list = implode(', ', $site_list);
        $xtpl->assign('NV_SERVER_PROTOCOL', NV_SERVER_PROTOCOL);
        $xtpl->assign('SITE_LIST', $site_list);

        $xtpl->parse('main.data');
    }

    // 5 người dùng mới nhất
    if (empty($array_lastest_users)) {
        $xtpl->parse('main.no_topusers');
    } else {
        foreach ($array_lastest_users as $user) {
            $user['regdate'] = nv_date('d/m/Y H:i', $user['regdate']);
            if (isset($array_user_phone[$user['userid']])) {
                $user['phone'] = '***' . substr($array_user_phone[$user['userid']], -4, 4);
            } else {
                $user['phone'] = '';
            }
            $user['email'] = preg_replace('/^(\w{4}).*?\@(.*?)$/', '\\1***@\\2', $user['email']);
            $user['full_name'] = nv_show_name_user($user['first_name'], $user['last_name']);

            $xtpl->assign('USER', $user);

            if (isset($array_user_vip[$user['userid']])) {
                foreach ($array_user_vip[$user['userid']] as $vip) {
                    $xtpl->assign('VIP', $vip);
                    if ($vip['status'] == 1) {
                        $xtpl->parse('main.topusers.loop.vip.active');
                    } else {
                        $xtpl->parse('main.topusers.loop.vip.exp');
                    }
                    $xtpl->parse('main.topusers.loop.vip');
                }
            }

            $xtpl->parse('main.topusers.loop');
        }
        $xtpl->parse('main.topusers');
    }

    // Xuất các site
    $site_id = $nv_Request->get_absint('s', 'get', 1);
    if (!isset($global_array_sites[$site_id])) {
        $site_id = 1;
    }
    $xtpl->assign('SITE_ID', $site_id);
    foreach ($global_array_sites as $site) {
        $xtpl->assign('SITE_NAME', $site['title']);
        $xtpl->assign('SITE_ACTIVE', $site['id'] == $site_id ? ' class="active"' : '');
        $xtpl->assign('SITE_LINK', $site['id'] != $site_id ? (NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '&s=' . $site['id']) : '#');
        $xtpl->parse('main.site');
    }
    if ($nv_Request->isset_request('s', 'get')) {
        $xtpl->parse('main.scroll');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * @param array $array_lastest_orders
 * @param array $array_vips
 * @return string
 */
function nv_elink_theme_promotion_top_orders($array_lastest_orders, $array_vips)
{
    global $module_info, $global_array_config, $global_array_sites, $module_name, $op, $nv_Request, $nv_Lang;

    $xtpl = new XTemplate('promotion.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    // 5 đơn hàng mới nhất
    if (empty($array_lastest_orders)) {
        $xtpl->parse('orders.no_toporders');
    } else {
        foreach ($array_lastest_orders as $order) {
            $order['link_view'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/o-' . $order['promo_code'];
            $order['add_time'] = nv_date('d/m/Y H:i', $order['add_time']);
            $order['status'] = $nv_Lang->getModule('bidding_order_status' . $order['status']);
            $order['total'] = number_format($order['total'], 0, ',', '.');
            $order['money'] = number_format($order['money'], 0, ',', '.');
            $order['discount'] = number_format($order['discount'], 0, ',', '.');

            $xtpl->assign('ORDER', $order);

            $order_name = 'N/A';
            $order_phone = 'N/A';
            $order_email = 'N/A';
            if (isset($array_vips[$order['id']])) {
                foreach ($array_vips[$order['id']] as $vip) {
                    $xtpl->assign('VIP', $vip);
                    $order_name = empty($vip['name']) ? 'N/A' : $vip['name'];
                    $order_phone = empty($vip['phone']) ? 'N/A' : $vip['phone'];
                    $order_email = empty($vip['email']) ? 'N/A' : $vip['email'];
                    $xtpl->parse('orders.toporders.loop.vip');
                }
            }

            $xtpl->assign('ORDER_NAME', $order_name);
            $xtpl->assign('ORDER_PHONE', $order_phone);
            $xtpl->assign('ORDER_EMAIL', $order_email);

            if ($order_email != 'N/A') {
                $xtpl->parse('orders.toporders.loop.email');
            }

            $xtpl->parse('orders.toporders.loop');
        }
        $xtpl->parse('orders.toporders');
    }

    $xtpl->parse('orders');
    return $xtpl->text('orders');
}

/**
 * @param array $array
 * @param array $data
 * @param string $error
 * @param boolean $isEdit
 * @return string
 */
function nv_elink_theme_promotion_content($array, $data, $error, $isEdit)
{
    global $module_info, $global_array_config, $array_vip_plans, $array_vip2_plans, $language_array, $global_config, $array_vip_en, $nv_Lang;

    $xtpl = new XTemplate('promotion-content.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    $array['promotion_vouchermax'] = number_format($array['promotion_vouchermax'], 0, ',', '.');

    $data['promo_value'] = empty($data['promo_value']) ? '' : $data['promo_value'];
    $data['use_number'] = empty($data['use_number']) ? '' : $data['use_number'];
    $data['use_user'] = empty($data['use_user']) ? '' : $data['use_user'];
    $data['use_peruser'] = empty($data['use_peruser']) ? '' : $data['use_peruser'];
    $data['start_time'] = empty($data['start_time']) ? '' : nv_date('d/m/Y', $data['start_time']);
    $data['end_time'] = empty($data['end_time']) ? '' : nv_date('d/m/Y', $data['end_time']);

    $xtpl->assign('DATA', $array);
    $xtpl->assign('POST', $data);

    if (!empty($error)) {
        $xtpl->assign('ERROR', $error);
        $xtpl->parse('main.error');
    }

    // Kiểu khuyến mãi
    foreach ($array['promotion_typeallowed'] as $_promo_type) {
        $promo_type = [
            'key' => $_promo_type,
            'title' => sprintf($nv_Lang->getModule('promotion_type' . $_promo_type), ($_promo_type == 0) ? $array['promotion_couponmax'] : $array['promotion_vouchermax']),
            'checked' => $_promo_type == $data['promo_type'] ? ' checked="checked"' : ''
        ];
        $xtpl->assign('PROMO_TYPE', $promo_type);
        $xtpl->parse('main.promo_type');
    }

    // Dùng làm mặc đinh
    for ($i = 1; $i >= 0; $i--) {
        $is_affiliate = [
            'key' => $i,
            'title' => $nv_Lang->getModule('promotion_is_affiliate' . $i),
            'checked' => $i == $data['is_affiliate'] ? ' checked="checked"' : ''
        ];
        $xtpl->assign('IS_AFFILIATE', $is_affiliate);
        $xtpl->parse('main.is_affiliate');
    }

    // Số năm thanh toán
    $cfg_use_limityear = explode(',', $global_array_config['promotion_use_limityear']);
    foreach ($cfg_use_limityear as $_use_limityear) {
        $use_limityear = [
            'key' => $_use_limityear,
            'title' => $_use_limityear,
            'checked' => in_array($_use_limityear, $data['use_limityear']) ? ' checked="checked"' : '',
            'unit' => NV_LANG_DATA == 'vi' ? 'Năm' : ('Year' . ($_use_limityear > 1 ? 's' : ''))
        ];
        $xtpl->assign('USE_LIMITYEAR', $use_limityear);
        $xtpl->parse('main.use_limityear');
    }

    // Kiểu áp dụng
    $xtpl->assign('PROMOTION_USE_TYPE0', in_array(0, $data['use_type']) ? ' checked="checked"' : '');
    $xtpl->assign('PROMOTION_USE_TYPE1', in_array(1, $data['use_type']) ? ' checked="checked"' : '');
    $xtpl->assign('PROMOTION_USE_TYPE2', in_array(2, $data['use_type']) ? ' checked="checked"' : '');

    // Gói dịch vụ dauthau.info
    foreach ($array_vip_plans as $vipkey) {
        $vip_item = [
            'key' => $vipkey,
            'title' => $nv_Lang->getModule('vip' . $vipkey),
            'checked_apply' => in_array($vipkey, $data['vip_apply']) ? ' checked="checked"' : '',
            'checked_include' => in_array($vipkey, $data['vip_include']) ? ' checked="checked"' : '',
            'show' => ($data['apply_lang'] == 'en' and !in_array($vipkey, $array_vip_en)) ? ' style="display: none;"' : ''
        ];

        $xtpl->assign('VIP_ITEM', $vip_item);
        $xtpl->parse('main.vip_apply');
        $xtpl->parse('main.vip_include');
    }

    // Gói dịch vụ dauthau.net
    foreach ($array_vip2_plans as $vipkey) {
        $vip_item = [
            'key' => $vipkey,
            'title' => $nv_Lang->getModule($vipkey),
            'checked_apply' => in_array($vipkey, $data['vip_apply']) ? ' checked="checked"' : '',
            'checked_include' => in_array($vipkey, $data['vip_include']) ? ' checked="checked"' : '',
        ];

        $xtpl->assign('VIP_ITEM', $vip_item);
        $xtpl->parse('main.vip2_apply');
        $xtpl->parse('main.vip2_include');
    }

    // Tự động tạo mã mặc định nếu không sửa
    if (!$isEdit and empty($data['promo_code'])) {
        $xtpl->parse('main.auto_creat_code');
    }

    // Áp dụng cho ngôn ngữ
    foreach ($global_config['allow_sitelangs'] as $lang) {
        $xtpl->assign('APPLY_LANG', [
            'key' => $lang,
            'title' => $language_array[$lang][NV_LANG_INTERFACE == 'vi' ? 'name' : 'language'],
            'selected' => $lang == $data['apply_lang'] ? ' selected="selected"' : '',
        ]);
        $xtpl->parse('main.apply_lang');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * @param array $array
 * @param array $row
 * @param array $array_users
 * @param array $array_user_phone
 * @param array $array_user_vip
 * @param string $generate_page
 * @return string
 */
function nv_elink_theme_promotion_statusers($array, $promo, $array_users, $array_user_phone, $array_user_vip, $generate_page)
{
    global $module_info, $global_array_config, $nv_Lang;

    $xtpl = new XTemplate('promotion-start-users.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('ARRAY', $array);
    $xtpl->assign('DATA', $promo);

    if (empty($array_users)) {
        $xtpl->parse('main.empty');
    } else {
        foreach ($array_users as $user) {
            $user['regdate'] = nv_date('d/m/Y H:i', $user['regdate']);
            if (isset($array_user_phone[$user['userid']])) {
                $user['phone'] = '***' . substr($array_user_phone[$user['userid']], -4, 4);
            } else {
                $user['phone'] = '';
            }
            $user['email'] = preg_replace('/^(\w{4}).*?\@(.*?)$/', '\\1***@\\2', $user['email']);
            $user['full_name'] = nv_show_name_user($user['first_name'], $user['last_name']);

            $xtpl->assign('USER', $user);

            if (isset($array_user_vip[$user['userid']])) {
                foreach ($array_user_vip[$user['userid']] as $vip) {
                    $xtpl->assign('VIP', $vip);
                    if ($vip['status'] == 1) {
                        $xtpl->parse('main.data.loop.vip.active');
                    } else {
                        $xtpl->parse('main.data.loop.vip.exp');
                    }
                    $xtpl->parse('main.data.loop.vip');
                }
            }

            $xtpl->parse('main.data.loop');
        }

        if (!empty($generate_page)) {
            $xtpl->assign('GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.data.generate_page');
        }

        $xtpl->parse('main.data');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * @param array $array_users
 * @param array $array_user_phone
 * @param array $array_user_vip
 * @param string $generate_page
 * @return string
 */
function nv_elink_theme_promotion_statusersall($array_users, $array_user_phone, $array_user_vip, $generate_page)
{
    global $module_info, $global_array_config, $nv_Lang;

    $xtpl = new XTemplate('promotion-start-users-all.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    if (empty($array_users)) {
        $xtpl->parse('main.empty');
    } else {
        foreach ($array_users as $user) {
            $user['regdate'] = nv_date('d/m/Y H:i', $user['regdate']);
            if (isset($array_user_phone[$user['userid']])) {
                $user['phone'] = '***' . substr($array_user_phone[$user['userid']], -4, 4);
            } else {
                $user['phone'] = '';
            }
            $user['email'] = preg_replace('/^(\w{4}).*?\@(.*?)$/', '\\1***@\\2', $user['email']);
            $user['full_name'] = nv_show_name_user($user['first_name'], $user['last_name']);

            $xtpl->assign('USER', $user);

            if (isset($array_user_vip[$user['userid']])) {
                foreach ($array_user_vip[$user['userid']] as $vip) {
                    $xtpl->assign('VIP', $vip);
                    if ($vip['status'] == 1) {
                        $xtpl->parse('main.data.loop.vip.active');
                    } else {
                        $xtpl->parse('main.data.loop.vip.exp');
                    }
                    $xtpl->parse('main.data.loop.vip');
                }
            }

            $xtpl->parse('main.data.loop');
        }

        if (!empty($generate_page)) {
            $xtpl->assign('GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.data.generate_page');
        }

        $xtpl->parse('main.data');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * @param array $array
 * @param array $promo
 * @param array $array_orders
 * @param array $array_vips
 * @param string $generate_page
 * @param string $error_message
 * @return string
 */
function nv_elink_theme_promotion_statorders($array, $promo, $array_orders, $array_vips, $generate_page, $error_message = '')
{
    global $module_info, $global_array_config, $global_array_sites, $nv_Request, $module_name, $op, $nv_Lang;

    $xtpl = new XTemplate('promotion-start-orders.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);
    $xtpl->assign('ARRAY', $array);
    $xtpl->assign('DATA', $promo);

    if (!empty($error_message)) {
        $xtpl->assign('ERROR_MESSAGE', $error_message);
        $xtpl->parse('main.error_message');
    }

    if (empty($array_orders)) {
        $xtpl->parse('main.empty');
    } else {
        foreach ($array_orders as $order) {
            $order['add_time'] = nv_date('d/m/Y H:i', $order['add_time']);
            $order['status'] = $nv_Lang->getModule('bidding_order_status' . $order['status']);
            $order['total'] = number_format($order['total'], 0, ',', '.');
            $order['money'] = number_format($order['money'], 0, ',', '.');
            $order['discount'] = number_format($order['discount'], 0, ',', '.');

            $xtpl->assign('ORDER', $order);

            $order_name = 'N/A';
            $order_phone = 'N/A';
            $order_email = 'N/A';
            if (isset($array_vips[$order['id']])) {
                foreach ($array_vips[$order['id']] as $vip) {
                    $xtpl->assign('VIP', $vip);
                    $order_name = empty($vip['name']) ? 'N/A' : $vip['name'];
                    $order_phone = empty($vip['phone']) ? 'N/A' : $vip['phone'];
                    $order_email = empty($vip['email']) ? 'N/A' : $vip['email'];
                    $xtpl->parse('main.data.loop.vip');
                }
            }

            $xtpl->assign('ORDER_NAME', $order_name);
            $xtpl->assign('ORDER_PHONE', $order_phone != 'N/A' ? '***' . substr($order_phone, -4, 4) : $order_phone);
            $xtpl->assign('ORDER_EMAIL', $order_email != 'N/A' ? preg_replace('/^(\w{4}).*?\@(.*?)$/', '\\1***@\\2', $order_email) : $order_email);

            if ($order_email != 'N/A') {
                $xtpl->parse('main.data.loop.email');
            }

            $xtpl->parse('main.data.loop');
        }

        if (!empty($generate_page)) {
            $xtpl->assign('GENERATE_PAGE', $generate_page);
            $xtpl->parse('main.data.generate_page');
        }

        $xtpl->parse('main.data');
    }

    // Xuất các site
    $site_id = $nv_Request->get_absint('s', 'get', 1);
    if (!isset($global_array_sites[$site_id])) {
        $site_id = 1;
    }
    $xtpl->assign('SITE_ID', $site_id);
    foreach ($global_array_sites as $site) {
        $xtpl->assign('SITE_NAME', $site['title']);
        $xtpl->assign('SITE_ACTIVE', $site['id'] == $site_id ? ' class="active"' : '');
        $xtpl->assign('SITE_LINK', $site['id'] != $site_id ? (NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op . '/o-' . $promo['promo_code'] . '&s=' . $site['id']) : '#');
        $xtpl->parse('main.site');
    }

    $xtpl->parse('main');
    return $xtpl->text('main');
}

/**
 * @param array $array_orders
 * @param array $array_vips
 * @param string $generate_page
 * @param array $array_search
 * @param string $error_message
 * @return string
 */
function nv_elink_theme_statordersall($array_orders, $array_vips, $generate_page, $array_search, $error_message = '')
{
    global $module_info, $global_array_config, $global_config, $module_name, $op, $global_array_sites, $nv_Lang;

    $xtpl = new XTemplate('start-orders-all.tpl', NV_ROOTDIR . '/themes/' . $module_info['template'] . '/modules/' . $module_info['module_theme']);
    $xtpl->assign('LANG', \NukeViet\Core\Language::$lang_module);
    $xtpl->assign('GLANG', \NukeViet\Core\Language::$lang_global);

    $xtpl->assign('SEARCH', $array_search);

    foreach ($array_orders as $order) {
        $order['link_view'] = NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=promotion/o-' . $order['promo_code'];
        $order['add_time'] = nv_date('d/m/Y H:i', $order['add_time']);
        $order['status'] = $nv_Lang->getModule('bidding_order_status' . $order['status']);

        // Số tiền được hưởng
        $order['amount_received'] = number_format(EL_GetAmountReceivedFromOrder($order), 0, ',', '.');
        // Tổng tiền thanh toán
        $order['total'] = number_format($order['total'], 0, ',', '.');
        // Tổng tiền thực sau khi giảm giá
        $order['total_end'] = number_format($order['total_end'], 0, ',', '.');
        // Tổng tiền hóa đơn
        $order['money'] = number_format($order['money'], 0, ',', '.');
        // Tiền giảm giá: Mã giảm giá
        $order['discount'] = number_format($order['discount'], 0, ',', '.');

        $xtpl->assign('ORDER', $order);

        $order_name = 'N/A';
        $order_phone = 'N/A';
        $order_email = 'N/A';
        if (isset($array_vips[$order['id']])) {
            foreach ($array_vips[$order['id']] as $vip) {
                $xtpl->assign('VIP', $vip);
                $order_name = empty($vip['name']) ? 'N/A' : $vip['name'];
                $order_phone = empty($vip['phone']) ? 'N/A' : $vip['phone'];
                $order_email = empty($vip['email']) ? 'N/A' : $vip['email'];
                $xtpl->parse('main.loop.vip');
            }
        }

        $xtpl->assign('ORDER_NAME', $order_name);
        $xtpl->assign('ORDER_PHONE', $order_phone != 'N/A' ? '***' . substr($order_phone, -4, 4) : $order_phone);
        $xtpl->assign('ORDER_EMAIL', $order_email != 'N/A' ? preg_replace('/^(\w{4}).*?\@(.*?)$/', '\\1***@\\2', $order_email) : $order_email);

        $xtpl->parse('main.loop');
    }

    if (!empty($error_message)) {
        $xtpl->assign('ERROR_MESSAGE', $error_message);
        $xtpl->parse('main.error_message');
    }

    if (!empty($generate_page)) {
        $xtpl->assign('GENERATE_PAGE', $generate_page);
        $xtpl->parse('main.generate_page');
    }

    if (!$global_config['rewrite_enable']) {
        $xtpl->assign('NV_LANG_VARIABLE', NV_LANG_VARIABLE);
        $xtpl->assign('NV_LANG_DATA', NV_LANG_DATA);
        $xtpl->assign('NV_NAME_VARIABLE', NV_NAME_VARIABLE);
        $xtpl->assign('NV_OP_VARIABLE', NV_OP_VARIABLE);
        $xtpl->assign('MODULE_NAME', $module_name);
        $xtpl->assign('OP', $op);
        $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . 'index.php');
        $xtpl->parse('main.no_rewrite');
    } else {
        $xtpl->assign('FORM_ACTION', NV_BASE_SITEURL . 'index.php?' . NV_LANG_VARIABLE . '=' . NV_LANG_DATA . '&amp;' . NV_NAME_VARIABLE . '=' . $module_name . '&amp;' . NV_OP_VARIABLE . '=' . $op);
    }

    // Xuất các site
    foreach ($global_array_sites as $site) {
        $xtpl->assign('SITE_ID', $site['id']);
        $xtpl->assign('SITE_NAME', $site['title']);
        $xtpl->assign('SITE_ACTIVE', $site['id'] == $array_search['site_id'] ? ' class="active"' : '');
        $xtpl->parse('main.site');
    }

    $xtpl->assign('SELECTED_TYPE_1', $array_search['type'] == 'a' ? ' selected="selected"' : '');
    $xtpl->assign('SELECTED_TYPE_2', $array_search['type'] == 'p' ? ' selected="selected"' : '');

    $xtpl->parse('main');
    return $xtpl->text('main');
}
